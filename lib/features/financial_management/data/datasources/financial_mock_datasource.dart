import 'package:injectable/injectable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../analytics/domain/repositories/analytics_repository.dart' hide ReportFilterCriteria, GenerateReportRequest;
import '../../domain/entities/financial_entities.dart';
import '../../domain/entities/financial_report_entities.dart' hide CostTrend;
import '../../domain/repositories/financial_repository.dart';
import 'financial_datasource.dart';
import '../../../analytics/domain/entities/analytics_entities.dart' show AnalyticsReportType;

/// Maps AnalyticsReportType used by analytics requests to FinancialReportType
FinancialReportType _mapToFinancialType(AnalyticsReportType type) {
  switch (type) {
    case AnalyticsReportType.financial:
      // Default to a common financial statement
      return FinancialReportType.profitLoss;
    case AnalyticsReportType.performance:
      return FinancialReportType.departmentPerformance;
    case AnalyticsReportType.production:
      return FinancialReportType.costAnalysis;
    case AnalyticsReportType.sales:
      return FinancialReportType.productProfitability;
    // Other analytics types don't have a direct financial equivalent
    // Fallback to custom
    default:
      return FinancialReportType.custom;
  }
}

/// Mock financial data source implementation
@LazySingleton(as: FinancialDataSource)
class FinancialMockDataSource implements FinancialDataSource {
  // Mock data
  final List<CostCenter> _costCenters = _generateMockCostCenters();
  final List<FinancialTransaction> _transactions = _generateMockTransactions();
  final List<Budget> _budgets = _generateMockBudgets();
  final List<FinancialReport> _reports = _generateMockReports();

  @override
  Future<ApiListResponse<CostCenter>> getCostCenters({
    CostCenterFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    // Since CostCenterFilterCriteria currently has no fields, just return all cost centers
    var filteredCostCenters = _costCenters.toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final endIndex = startIndex + (pagination?.perPage ?? 20);
    final paginatedCostCenters = filteredCostCenters.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedCostCenters,
      pagination: Pagination(
        currentPage: (pagination?.page ?? 0) + 1,
        perPage: pagination?.perPage ?? 20,
        total: filteredCostCenters.length,
        totalPages: (filteredCostCenters.length / (pagination?.perPage ?? 20)).ceil(),
        from: startIndex + 1,
        to: endIndex,
        hasNextPage: endIndex < filteredCostCenters.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
        nextPageUrl: null,
        previousPageUrl: null,
      ), success: true,
    );
  }

  @override
  Future<ApiResponse<CostCenter>> getCostCenterById(String costCenterId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final costCenter = _costCenters.firstWhere(
      (cc) => cc.id == costCenterId,
      orElse: () => throw Exception('Cost center not found'),
    );

    return ApiResponse(data: costCenter, success: true);
  }

  @override
  Future<ApiResponse<CostCenter>> createCostCenter(CreateCostCenterRequest request) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final newCostCenter = CostCenter(
      id: 'cc_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      costCenterCode: request.costCenterCode,
      costCenterName: request.costCenterName,
      description: request.description ?? '',
      type: CostCenterType.production,
      status: CommonStatus.active,
      parentCostCenterId: request.parentCostCenterId,
      departmentId: 'dept_default',
      departmentName: 'Department Name', // Mock
      managerId: null,
      managerName: null,
      budget: CostCenterBudget(
        allocatedAmount: 0,
        spentAmount: 0,
        committedAmount: 0,
        availableAmount: 0,
        currency: 'USD',
        period: BudgetPeriod.annual,
        periodStart: DateTime(DateTime.now().year, 1, 1),
        periodEnd: DateTime(DateTime.now().year, 12, 31),
        categoryAllocations: const {},
        categorySpent: const {},
      ),
      metrics: CostCenterMetrics(
        totalCosts: 0,
        directCosts: 0,
        indirectCosts: 0,
        laborCosts: 0,
        materialCosts: 0,
        overheadCosts: 0,
        costPerUnit: 0,
        costPerHour: 0,
        efficiency: 100,
        productivity: 100,
        transactionCount: 0,
        lastUpdated: DateTime.now(),
      ),
    );

    _costCenters.add(newCostCenter);
    return ApiResponse(data: newCostCenter, success: true);
  }

  @override
  Future<ApiResponse<CostCenter>> updateCostCenter(UpdateCostCenterRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final index = _costCenters.indexWhere((cc) => cc.id == request.id);
    if (index == -1) throw Exception('Cost center not found');

    final existingCostCenter = _costCenters[index];
    final updatedCostCenter = CostCenter(
      id: existingCostCenter.id,
      createdAt: existingCostCenter.createdAt,
      updatedAt: DateTime.now(),
      costCenterCode: existingCostCenter.costCenterCode,
      costCenterName: request.name ?? existingCostCenter.costCenterName,
      description: existingCostCenter.description,
      type: existingCostCenter.type,
      status: existingCostCenter.status,
      parentCostCenterId: existingCostCenter.parentCostCenterId,
      departmentId: existingCostCenter.departmentId,
      departmentName: existingCostCenter.departmentName,
      managerId: existingCostCenter.managerId,
      managerName: existingCostCenter.managerName,
      budget: existingCostCenter.budget,
      metrics: existingCostCenter.metrics,
    );

    _costCenters[index] = updatedCostCenter;
    return ApiResponse(data: updatedCostCenter, success: true);
  }

  @override
  Future<ApiVoidResponse> deleteCostCenter(String costCenterId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    _costCenters.removeWhere((cc) => cc.id == costCenterId);
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiListResponse<FinancialTransaction>> getTransactions({
    TransactionFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredTransactions = _transactions.where((transaction) {
      if (filter?.type != null && transaction.type != filter!.type) return false;
      if (filter?.category != null && transaction.category != filter!.category) return false;
      if (filter?.status != null && transaction.status != filter!.status) return false;
      if (filter?.costCenterId != null && transaction.costCenterId != filter!.costCenterId) return false;
      if (filter?.startDate != null && transaction.transactionDate.isBefore(filter!.startDate!)) return false;
      if (filter?.endDate != null && transaction.transactionDate.isAfter(filter!.endDate!)) return false;
      if (filter?.minAmount != null && transaction.amount < filter!.minAmount!) return false;
      if (filter?.maxAmount != null && transaction.amount > filter!.maxAmount!) return false;
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedTransactions = filteredTransactions.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedTransactions,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        totalPages: (filteredTransactions.length / (pagination?.perPage ?? 20)).ceil(),
        total: filteredTransactions.length,
        perPage: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredTransactions.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<ApiResponse<FinancialTransaction>> getTransactionById(String transactionId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final transaction = _transactions.firstWhere(
      (t) => t.id == transactionId,
      orElse: () => throw Exception('Transaction not found'),
    );

    return ApiResponse(data: transaction, success: true);
  }

  @override
  Future<ApiResponse<FinancialTransaction>> createTransaction(CreateTransactionRequest request) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final newTransaction = FinancialTransaction(
      id: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      transactionNumber: 'TXN-${DateTime.now().millisecondsSinceEpoch}',
      type: request.type,
      category: request.category,
      amount: request.amount,
      currency: 'USD', // Default currency for mock data
      transactionDate: request.transactionDate,
      description: request.description,
      reference: null,
      costCenterId: request.costCenterId,
      costCenterName: 'Cost Center Name', // Mock
      orderId: null,
      productionOrderId: null,
      supplierId: null,
      customerId: null,
      employeeId: null,
      status: TransactionStatus.pending,
      // lineItems omitted; defaults to empty list in entity
    );

    _transactions.add(newTransaction);
    return ApiResponse(data: newTransaction, success: true);
  }

  @override
  Future<ApiResponse<FinancialTransaction>> updateTransaction(UpdateTransactionRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final index = _transactions.indexWhere((t) => t.id == request.id);
    if (index == -1) throw Exception('Transaction not found');

    final existingTransaction = _transactions[index];
    final updatedTransaction = FinancialTransaction(
      id: existingTransaction.id,
      createdAt: existingTransaction.createdAt,
      updatedAt: DateTime.now(),
      transactionNumber: existingTransaction.transactionNumber,
      type: request.type ?? existingTransaction.type,
      category: request.category ?? existingTransaction.category,
      amount: request.amount ?? existingTransaction.amount,
      currency: existingTransaction.currency,
      transactionDate: request.transactionDate ?? existingTransaction.transactionDate,
      description: request.description ?? existingTransaction.description,
      reference: existingTransaction.reference,
      costCenterId: request.costCenterId ?? existingTransaction.costCenterId,
      costCenterName: existingTransaction.costCenterName,
      status: existingTransaction.status,
      lineItems: existingTransaction.lineItems,
    );

    _transactions[index] = updatedTransaction;
    return ApiResponse(data: updatedTransaction, success: true);
  }

  @override
  Future<ApiVoidResponse> deleteTransaction(String transactionId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    _transactions.removeWhere((t) => t.id == transactionId);
    return  ApiVoidResponse();
  }

  @override
  Future<ApiResponse<FinancialTransaction>> approveTransaction(String transactionId, String approverId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _transactions.indexWhere((t) => t.id == transactionId);
    if (index == -1) throw Exception('Transaction not found');

    final existingTransaction = _transactions[index];
    final approvedTransaction = FinancialTransaction(
      id: existingTransaction.id,
      createdAt: existingTransaction.createdAt,
      updatedAt: DateTime.now(),
      transactionNumber: existingTransaction.transactionNumber,
      type: existingTransaction.type,
      category: existingTransaction.category,
      amount: existingTransaction.amount,
      currency: existingTransaction.currency,
      transactionDate: existingTransaction.transactionDate,
      description: existingTransaction.description,
      reference: existingTransaction.reference,
      costCenterId: existingTransaction.costCenterId,
      costCenterName: existingTransaction.costCenterName,
      status: TransactionStatus.approved,
      approvedBy: approverId,
      approvedAt: DateTime.now(),
      lineItems: existingTransaction.lineItems,
    );

    _transactions[index] = approvedTransaction;
    return ApiResponse(data: approvedTransaction, success: true);
  }

  @override
  Future<ApiResponse<FinancialTransaction>> rejectTransaction(String transactionId, String rejectedBy, String reason) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _transactions.indexWhere((t) => t.id == transactionId);
    if (index == -1) throw Exception('Transaction not found');

    final existingTransaction = _transactions[index];
    final rejectedTransaction = FinancialTransaction(
      id: existingTransaction.id,
      createdAt: existingTransaction.createdAt,
      updatedAt: DateTime.now(),
      transactionNumber: existingTransaction.transactionNumber,
      type: existingTransaction.type,
      category: existingTransaction.category,
      amount: existingTransaction.amount,
      currency: existingTransaction.currency,
      transactionDate: existingTransaction.transactionDate,
      description: existingTransaction.description,
      reference: existingTransaction.reference,
      costCenterId: existingTransaction.costCenterId,
      costCenterName: existingTransaction.costCenterName,
      status: TransactionStatus.rejected,
      rejectedBy: rejectedBy,
      rejectedAt: DateTime.now(),
      rejectionReason: reason,
      lineItems: existingTransaction.lineItems,
    );

    _transactions[index] = rejectedTransaction;
    return ApiResponse(data: rejectedTransaction,success: true);
  }

  @override
  Future<ApiListResponse<Budget>> getBudgets({
    BudgetFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredBudgets = _budgets.where((budget) {
      if (filter?.approvalStatus != null && budget.approvalStatus != filter!.approvalStatus) return false;
      if (filter?.departmentId != null && budget.departmentId != filter!.departmentId) return false;
      return true;
    }).toList();

    final startIndex = ((pagination?.page ?? 0) * (pagination?.perPage ?? 20)).toInt();
    final paginatedBudgets = filteredBudgets.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    final currentPage = (pagination?.page ?? 0).toInt();
    final perPage = (pagination?.perPage ?? 20).toInt();
    final total = filteredBudgets.length;
    final totalPages = (total / perPage).ceil();
    final from = (startIndex + 1).toInt();
    final to = (startIndex + paginatedBudgets.length).toInt();
    
    return ApiListResponse(
      data: paginatedBudgets,
      pagination: Pagination(
        currentPage: (currentPage + 1).toInt(), // Convert to 1-based index
        perPage: perPage,
        total: total,
        totalPages: totalPages,
        from: from,
        to: to,
        hasNextPage: (startIndex + perPage) < total,
        hasPreviousPage: currentPage > 0,
        nextPageUrl: null,
        previousPageUrl: null,
      ),
      success: true,
    );
  }

  @override
  Future<ApiResponse<Budget>> getBudgetById(String budgetId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final budget = _budgets.firstWhere(
      (b) => b.id == budgetId,
      orElse: () => throw Exception('Budget not found'),
    );

    return ApiResponse(data: budget, success: true);
  }

  @override
  Future<ApiResponse<Budget>> createBudget(CreateBudgetRequest request) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final newBudget = Budget(
      id: 'budget_$timestamp',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      budgetCode: 'BUD-$timestamp', // Generate a code since request doesn't provide one
      budgetName: request.budgetName,
      description: '', // No description in CreateBudgetRequest
      type: request.type,
      period: request.period,
      periodStart: request.periodStart,
      periodEnd: request.periodEnd,
      status: CommonStatus.active,
      totalAmount: request.totalAmount,
      currency: 'USD', // Default currency
      costCenterId: request.costCenterId,
      departmentId: request.departmentId,
      projectId: null, // Not provided in CreateBudgetRequest
      ownerId: 'owner_default', // Not provided in CreateBudgetRequest
      ownerName: 'Owner Name', // Mock
      lineItems: const [], // Not provided in CreateBudgetRequest
      metrics: BudgetMetrics(
        spentAmount: 0,
        committedAmount: 0,
        remainingAmount: 0,
        utilizationPercentage: 0,
        varianceAmount: 0,
        variancePercentage: 0,
        isExceeded: false,
        lastUpdated: DateTime.now(),
      ),
      approverIds: const [], // Not provided in CreateBudgetRequest
      approvalStatus: BudgetApprovalStatus.draft,
    );

    _budgets.add(newBudget);
    return ApiResponse(data: newBudget, success: true);
  }

  @override
  Future<ApiResponse<Budget>> updateBudget(UpdateBudgetRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final index = _budgets.indexWhere((b) => b.id == request.id);
    if (index == -1) throw Exception('Budget not found');

    final existingBudget = _budgets[index];
    final updatedBudget = Budget(
      id: existingBudget.id,
      createdAt: existingBudget.createdAt,
      updatedAt: DateTime.now(),
      budgetCode: existingBudget.budgetCode,
      budgetName: request.budgetName ?? existingBudget.budgetName,
      description: existingBudget.description,
      type: existingBudget.type,
      period: existingBudget.period,
      periodStart: request.periodStart ?? existingBudget.periodStart,
      periodEnd: request.periodEnd ?? existingBudget.periodEnd,
      status: existingBudget.status,
      totalAmount: request.totalAmount ?? existingBudget.totalAmount,
      currency: existingBudget.currency,
      costCenterId: existingBudget.costCenterId,
      departmentId: existingBudget.departmentId,
      projectId: existingBudget.projectId,
      ownerId: existingBudget.ownerId,
      ownerName: existingBudget.ownerName,
      lineItems: existingBudget.lineItems,
      metrics: existingBudget.metrics,
      approverIds: existingBudget.approverIds,
      approvalStatus: existingBudget.approvalStatus,
    );

    _budgets[index] = updatedBudget;
    return ApiResponse(data: updatedBudget, success: true);
  }

  @override
  Future<ApiVoidResponse> deleteBudget(String budgetId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    _budgets.removeWhere((b) => b.id == budgetId);
    return const ApiVoidResponse();
  }

  @override
  Future<ApiResponse<Budget>> approveBudget(String budgetId, String approverId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _budgets.indexWhere((b) => b.id == budgetId);
    if (index == -1) throw Exception('Budget not found');

    final existingBudget = _budgets[index];
    final approvedBudget = Budget(
      id: existingBudget.id,
      createdAt: existingBudget.createdAt,
      updatedAt: DateTime.now(),
      budgetCode: existingBudget.budgetCode,
      budgetName: existingBudget.budgetName,
      description: existingBudget.description,
      type: existingBudget.type,
      period: existingBudget.period,
      periodStart: existingBudget.periodStart,
      periodEnd: existingBudget.periodEnd,
      status: existingBudget.status,
      totalAmount: existingBudget.totalAmount,
      currency: existingBudget.currency,
      costCenterId: existingBudget.costCenterId,
      departmentId: existingBudget.departmentId,
      projectId: existingBudget.projectId,
      ownerId: existingBudget.ownerId,
      ownerName: existingBudget.ownerName,
      lineItems: existingBudget.lineItems,
      metrics: existingBudget.metrics,
      approverIds: existingBudget.approverIds,
      approvalStatus: BudgetApprovalStatus.approved,
      approvedAt: DateTime.now(),
      approvedBy: approverId,
    );

    _budgets[index] = approvedBudget;
    return ApiResponse(data: approvedBudget, success: true);
  }

  @override
  Future<ApiResponse<Budget>> rejectBudget(String budgetId, String rejectedBy, String reason) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _budgets.indexWhere((b) => b.id == budgetId);
    if (index == -1) throw Exception('Budget not found');

    final existingBudget = _budgets[index];
    final rejectedBudget = Budget(
      id: existingBudget.id,
      createdAt: existingBudget.createdAt,
      updatedAt: DateTime.now(),
      budgetCode: existingBudget.budgetCode,
      budgetName: existingBudget.budgetName,
      description: existingBudget.description,
      type: existingBudget.type,
      period: existingBudget.period,
      periodStart: existingBudget.periodStart,
      periodEnd: existingBudget.periodEnd,
      status: existingBudget.status,
      totalAmount: existingBudget.totalAmount,
      currency: existingBudget.currency,
      costCenterId: existingBudget.costCenterId,
      departmentId: existingBudget.departmentId,
      projectId: existingBudget.projectId,
      ownerId: existingBudget.ownerId,
      ownerName: existingBudget.ownerName,
      lineItems: existingBudget.lineItems,
      metrics: existingBudget.metrics,
      approverIds: existingBudget.approverIds,
      approvalStatus: BudgetApprovalStatus.rejected,
    );

    _budgets[index] = rejectedBudget;
    return ApiResponse(data: rejectedBudget, success: true);
  }

  // Continue with remaining methods...
  @override
  Future<ApiListResponse<FinancialReport>> getReports({
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredReports = _reports.where((report) {
      if (filter?.type != null && report.type != filter!.type) return false;
      if (filter?.period != null && report.period != filter!.period) return false;
      if (filter?.status != null && report.status != filter!.status) return false;
      if (filter?.generatedBy != null && report.generatedBy != filter!.generatedBy) return false;
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedReports = filteredReports.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    final endIndex = startIndex + (pagination?.perPage ?? 20);
    return ApiListResponse(
      data: paginatedReports,
      pagination: Pagination(
        currentPage: (pagination?.page ?? 0) + 1,
        perPage: pagination?.perPage ?? 20,
        total: filteredReports.length,
        totalPages: (filteredReports.length / (pagination?.perPage ?? 20)).ceil(),
        from: startIndex + 1,
        to: endIndex <= filteredReports.length ? endIndex : filteredReports.length,
        hasNextPage: endIndex < filteredReports.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
        nextPageUrl: null,
        previousPageUrl: null,
      ),
      success: true,
    );
  }

  @override
  Future<ApiResponse<FinancialReport>> getReportById(String reportId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final report = _reports.firstWhere(
      (r) => r.id == reportId,
      orElse: () => throw Exception('Report not found'),
    );

    return ApiResponse(data: report, success: true);
  }

  @override
  Future<ApiResponse<FinancialReport>> generateReport(GenerateReportRequest request) async {
    await Future.delayed(const Duration(milliseconds: 2000)); // Simulate report generation

    final newReport = FinancialReport(
      id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      reportCode: 'RPT-${DateTime.now().millisecondsSinceEpoch}',
      reportName: request.reportName,
      description: request.description,
      type: _mapToFinancialType(request.type as AnalyticsReportType),
      period: request.period,
      periodStart: request.periodStart,
      periodEnd: request.periodEnd,
      currency: request.currency,
      status: ReportStatus.completed,
      generatedBy: 'current_user', // Mock
      generatedAt: DateTime.now(),
      parameters: request.parameters,
      data: const FinancialReportData(), // Mock empty data
      tags: request.tags ?? const [],
    );

    _reports.add(newReport);
    return ApiResponse(data: newReport, success: true);
  }

  @override
  Future<ProfitLossStatement> getProfitLossStatement({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1000));

    // Mock profit and loss statement
    return ProfitLossStatement(
      periodStart: startDate,
      periodEnd: endDate,
      currency: 'USD',
      totalRevenue: 1000000,
      totalCostOfGoodsSold: 600000,
      grossProfit: 400000,
      totalOperatingExpenses: 250000,
      operatingIncome: 150000,
      totalOtherIncome: 10000,
      totalOtherExpenses: 5000,
      netIncome: 155000,
      grossProfitMargin: 40.0,
      operatingMargin: 15.0,
      netProfitMargin: 15.5,
      revenueBreakdown: const {
        'Product Sales': 800000,
        'Service Revenue': 200000,
      },
      expenseBreakdown: const {
        'Materials': 400000,
        'Labor': 200000,
        'Overhead': 150000,
        'Administration': 100000,
      },
    );
  }

  @override
  Future<CostAnalysisReport> getCostAnalysisReport({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1000));

    // Mock cost analysis report
    return CostAnalysisReport(
      periodStart: startDate,
      periodEnd: endDate,
      currency: 'USD',
      totalCosts: 850000,
      directCosts: 600000,
      indirectCosts: 250000,
      fixedCosts: 300000,
      variableCosts: 550000,
      costsByCategory: const {
        CostCategory.directMaterial: 400000,
        CostCategory.directLabor: 200000,
        CostCategory.manufacturingOverhead: 150000,
        CostCategory.administrativeExpense: 100000,
      },
      costsByCostCenter: const {
        'Production': 600000,
        'Administration': 100000,
        'Sales': 75000,
        'Quality': 75000,
      },
      costsByDepartment: const {
        'Cutting': 200000,
        'Sewing': 250000,
        'Finishing': 150000,
        'Packaging': 100000,
        'Quality Control': 75000,
        'Administration': 75000,
      },
      costsByProduct: const {
        'T-Shirts': 300000,
        'Jeans': 250000,
        'Dresses': 200000,
        'Accessories': 100000,
      },
    );
  }

  @override
  Future<BudgetVarianceReport> getBudgetVarianceReport({
    required DateTime startDate,
    required DateTime endDate,
    String? budgetId,
    String? costCenterId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1000));

    // Mock budget variance report
    return  BudgetVarianceReport(
      periodStart: startDate,
      periodEnd: endDate,
      currency: 'USD',
      totalBudget: 1000000,
      totalActual: 950000,
      totalVariance: -50000,
      variancePercentage: -5.0,
      items: const [
        BudgetVarianceItem(
          description: 'Materials',
          budgetAmount: 400000,
          actualAmount: 380000,
          variance: -20000,
          variancePercentage: -5.0,
          category: CostCategory.directMaterial,
        ),
        BudgetVarianceItem(
          description: 'Labor',
          budgetAmount: 300000,
          actualAmount: 320000,
          variance: 20000,
          variancePercentage: 6.67,
          category: CostCategory.directLabor,
        ),
      ],
    );
  }

  @override
  Future<Map<String, double>> getFinancialSummary({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return const {
      'totalRevenue': 1000000,
      'totalExpenses': 850000,
      'netIncome': 150000,
      'grossMargin': 40.0,
      'operatingMargin': 15.0,
      'netMargin': 15.0,
    };
  }

  @override
  Future<List<CostTrend>> getCostTrends({
    required DateTime startDate,
    required DateTime endDate,
    CostCategory? category,
    String? costCenterId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    // Mock cost trends
    final trends = <CostTrend>[];
    final days = endDate.difference(startDate).inDays;
    
    for (int i = 0; i <= days; i += 7) {
      final date = startDate.add(Duration(days: i));
      trends.add(CostTrend(
        date: date,
        amount: 50000 + (i * 1000),
        category: category ?? CostCategory.directMaterial,
        costCenterId: costCenterId,
      ));
    }

    return trends;
  }

  @override
  Future<Map<String, double>> getRevenueBreakdown({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return const {
      'Product Sales': 800000,
      'Service Revenue': 150000,
      'Other Revenue': 50000,
    };
  }

  @override
  Future<Map<String, double>> getExpenseBreakdown({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return const {
      'Materials': 400000,
      'Labor': 200000,
      'Overhead': 150000,
      'Administration': 100000,
    };
  }

  @override
  Future<ApiListResponse<CostCenter>> searchCostCenters({
    required String query,
    CostCenterFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredCostCenters = _costCenters.where((costCenter) {
      final matchesQuery = costCenter.costCenterName.toLowerCase().contains(query.toLowerCase()) ||
          costCenter.costCenterCode.toLowerCase().contains(query.toLowerCase()) ||
          costCenter.description.toLowerCase().contains(query.toLowerCase());
      
      if (!matchesQuery) return false;
      
      if (filter?.type != null && costCenter.type != filter!.type) return false;
      if (filter?.departmentId != null && costCenter.departmentId != filter!.departmentId) return false;
      if (filter?.status != null && costCenter.status != filter!.status) return false;
      
      return true;
    }).toList(); 

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedCostCenters = filteredCostCenters.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    final endIndex = startIndex + (pagination?.perPage ?? 20);
    return ApiListResponse(
      data: paginatedCostCenters,
      pagination: Pagination(
        currentPage: (pagination?.page ?? 0) + 1,
        perPage: pagination?.perPage ?? 20,
        total: filteredCostCenters.length,
        totalPages: (filteredCostCenters.length / (pagination?.perPage ?? 20)).ceil(),
        from: startIndex + 1,
        to: endIndex <= filteredCostCenters.length ? endIndex : filteredCostCenters.length,
        hasNextPage: endIndex < filteredCostCenters.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
        nextPageUrl: null,
        previousPageUrl: null,
      ), success: true,
    );
  }

  @override
  Future<ApiListResponse<FinancialTransaction>> searchTransactions({
    required String query,
    TransactionFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredTransactions = _transactions.where((transaction) {
      final matchesQuery = transaction.description.toLowerCase().contains(query.toLowerCase()) ||
          transaction.transactionNumber.toLowerCase().contains(query.toLowerCase()) ||
          (transaction.reference?.toLowerCase().contains(query.toLowerCase()) ?? false);
      
      if (!matchesQuery) return false;
      
      if (filter?.type != null && transaction.type != filter!.type) return false;
      if (filter?.category != null && transaction.category != filter!.category) return false;
      if (filter?.status != null && transaction.status != filter!.status) return false;
      
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedTransactions = filteredTransactions.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedTransactions,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        totalPages: (filteredTransactions.length / (pagination?.perPage ?? 20)).ceil(),
        total: filteredTransactions.length,
        perPage: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredTransactions.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<ApiListResponse<Budget>> searchBudgets({
    required String query,
    BudgetFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredBudgets = _budgets.where((budget) {
      final matchesQuery = budget.budgetName.toLowerCase().contains(query.toLowerCase()) ||
          budget.budgetCode.toLowerCase().contains(query.toLowerCase()) ||
          budget.description.toLowerCase().contains(query.toLowerCase());
      
      if (!matchesQuery) return false;
      
      if (filter?.approvalStatus != null && budget.approvalStatus != filter!.approvalStatus) return false;
      if (filter?.departmentId != null && budget.departmentId != filter!.departmentId) return false;
      
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedBudgets = filteredBudgets.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    final currentPage = pagination?.page ?? 0;
    final perPage = pagination?.perPage ?? 20;
    final total = filteredBudgets.length;
    final totalPages = (total / perPage).ceil();
    final from = startIndex + 1;
    final to = startIndex + paginatedBudgets.length;
  
    return ApiListResponse(
      data: paginatedBudgets,
      pagination: Pagination(
        currentPage: currentPage + 1, // Convert to 1-based index
        perPage: perPage,
        total: total,
        totalPages: totalPages,
        from: from,
        to: to,
        hasNextPage: startIndex + perPage < total,
        hasPreviousPage: currentPage > 0,
        nextPageUrl: null,
        previousPageUrl: null,
      ),
      success: true,
    );  
  }
}

// Mock data generators
List<CostCenter> _generateMockCostCenters() {
  return [
    CostCenter(
      id: 'cc_001',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      costCenterCode: 'CC-PROD-001',
      costCenterName: 'Production Line 1',
      description: 'Main production line for T-shirts and casual wear',
      type: CostCenterType.production,
      status: CommonStatus.active,
      departmentId: 'dept_001',
      departmentName: 'Production Department',
      managerId: 'mgr_001',
      managerName: 'John Smith',
      budget: CostCenterBudget(
        allocatedAmount: 500000,
        spentAmount: 350000,
        committedAmount: 50000,
        availableAmount: 100000,
        currency: 'USD',
        period: BudgetPeriod.annual,
        periodStart: DateTime.now(),
        periodEnd: DateTime.now(),
      ),
      metrics:  CostCenterMetrics(
        totalCosts: 350000,
        directCosts: 280000,
        indirectCosts: 70000,
        laborCosts: 150000,
        materialCosts: 130000,
        overheadCosts: 70000,
        costPerUnit: 12.50,
        costPerHour: 85.00,
        efficiency: 92.5,
        productivity: 88.0,
        transactionCount: 245,
        lastUpdated: DateTime.now(),
      ),
    ),
    CostCenter(
      id: 'cc_002',
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      costCenterCode: 'CC-QC-001',
      costCenterName: 'Quality Control',
      description: 'Quality inspection and testing department',
      type: CostCenterType.service,
      status: CommonStatus.active,
      departmentId: 'dept_002',
      departmentName: 'Quality Department',
      managerId: 'mgr_002',
      managerName: 'Sarah Johnson',
      budget: CostCenterBudget(
        allocatedAmount: 150000,
        spentAmount: 95000,
        committedAmount: 15000,
        availableAmount: 40000,
        currency: 'USD',
        period: BudgetPeriod.annual,
        periodStart: DateTime.now().subtract(const Duration(days: 365)),
        periodEnd:  DateTime.now(),
      ),
      metrics: CostCenterMetrics(
        totalCosts: 95000,
        directCosts: 75000,
        indirectCosts: 20000,
        laborCosts: 60000,
        materialCosts: 15000,
        overheadCosts: 20000,
        costPerUnit: 3.50,
        costPerHour: 45.00,
        efficiency: 95.0,
        productivity: 90.0,
        transactionCount: 89,
        lastUpdated: DateTime.now(),
      ),
    ),
  ];
}

List<FinancialTransaction> _generateMockTransactions() {
  return [
    FinancialTransaction(
      id: 'txn_001',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      transactionNumber: 'TXN-2024-001',
      type: TransactionType.expense,
      category: TransactionCategory.materialPurchase,
      amount: 25000,
      currency: 'USD',
      transactionDate: DateTime.now().subtract(const Duration(days: 5)),
      description: 'Cotton fabric purchase for Q1 production',
      reference: 'PO-2024-001',
      costCenterId: 'cc_001',
      costCenterName: 'Production Line 1',
      orderId: 'order_001',
      supplierId: 'supplier_001',
      supplierName: 'ABC Textiles',
      status: TransactionStatus.approved,
      approvedBy: 'mgr_001',
      approvedAt: DateTime.now().subtract(const Duration(days: 4)),
      lineItems: const [
        TransactionLineItem(
          description: 'Cotton fabric - White',
          quantity: 1000,
          unit: 'yards',
          unitPrice: 15.00,
          totalAmount: 15000,
          materialId: 'mat_001',
          materialName: 'Cotton Fabric White',
          costCategory: CostCategory.directMaterial,
        ),
        TransactionLineItem(
          description: 'Cotton fabric - Blue',
          quantity: 500,
          unit: 'yards',
          unitPrice: 20.00,
          totalAmount: 10000,
          materialId: 'mat_002',
          materialName: 'Cotton Fabric Blue',
          costCategory: CostCategory.directMaterial,
        ),
      ],
    ),
    FinancialTransaction(
      id: 'txn_002',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      transactionNumber: 'TXN-2024-002',
      type: TransactionType.revenue,
      category: TransactionCategory.sales,
      amount: 45000,
      currency: 'USD',
      transactionDate: DateTime.now().subtract(const Duration(days: 3)),
      description: 'Sales revenue from retail orders',
      reference: 'INV-2024-001',
      costCenterId: 'cc_001',
      costCenterName: 'Production Line 1',
      orderId: 'order_002',
      customerId: 'customer_001',
      customerName: 'Fashion Retail Co.',
      status: TransactionStatus.posted,
      lineItems: const [
        TransactionLineItem(
          description: 'T-Shirt Sales',
          quantity: 1000,
          unit: 'pieces',
          unitPrice: 25.00,
          totalAmount: 25000,
          productId: 'prod_001',
          productName: 'Basic T-Shirt',
          costCategory: CostCategory.other,
        ),
        TransactionLineItem(
          description: 'Polo Shirt Sales',
          quantity: 500,
          unit: 'pieces',
          unitPrice: 40.00,
          totalAmount: 20000,
          productId: 'prod_002',
          productName: 'Polo Shirt',
          costCategory: CostCategory.other,
        ),
      ],
    ),
  ];
}

List<Budget> _generateMockBudgets() {
  return [
    Budget(
      id: 'budget_001',
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      budgetCode: 'BUD-2024-001',
      budgetName: 'Q1 2024 Production Budget',
      description: 'Quarterly budget for production operations',
      type: BudgetType.operational,
      period: BudgetPeriod.quarterly,
      periodStart: DateTime(2024, 1, 1),
      periodEnd: DateTime(2024, 3, 31),
      status: CommonStatus.active,
      totalAmount: 750000,
      currency: 'USD',
      costCenterId: 'cc_001',
      costCenterName: 'Production Line 1',
      departmentId: 'dept_001',
      departmentName: 'Production Department',
      ownerId: 'mgr_001',
      ownerName: 'John Smith',
      lineItems: const [
        BudgetLineItem(
          description: 'Direct Materials',
          category: CostCategory.directMaterial,
          allocatedAmount: 400000,
          spentAmount: 280000,
          committedAmount: 50000,
          accountCode: '5001',
          accountName: 'Raw Materials',
        ),
        BudgetLineItem(
          description: 'Direct Labor',
          category: CostCategory.directLabor,
          allocatedAmount: 250000,
          spentAmount: 180000,
          committedAmount: 30000,
          accountCode: '5002',
          accountName: 'Direct Labor',
        ),
        BudgetLineItem(
          description: 'Manufacturing Overhead',
          category: CostCategory.manufacturingOverhead,
          allocatedAmount: 100000,
          spentAmount: 65000,
          committedAmount: 15000,
          accountCode: '5003',
          accountName: 'Manufacturing Overhead',
        ),
      ],
      metrics: BudgetMetrics(
        spentAmount: 525000,
        committedAmount: 95000,
        remainingAmount: 130000,
        utilizationPercentage: 70.0,
        varianceAmount: 130000,
        variancePercentage: 17.3,
        isExceeded: false,
        lastUpdated: DateTime.now(),
      ),
      approverIds: const ['mgr_001', 'mgr_002'],
      approvalStatus: BudgetApprovalStatus.approved,
      approvedAt: DateTime.now().subtract(const Duration(days: 55)),
      approvedBy: 'mgr_001',
    ),
  ];
}

List<FinancialReport> _generateMockReports() {
  return [
    FinancialReport(
      id: 'report_001',
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      reportCode: 'RPT-2024-001',
      reportName: 'Q1 2024 Profit & Loss Statement',
      description: 'Quarterly profit and loss analysis',
      type: FinancialReportType.profitLoss,
      period: ReportPeriod.quarterly,
      periodStart: DateTime(2024, 1, 1),
      periodEnd: DateTime(2024, 3, 31),
      currency: 'USD',
      status: ReportStatus.completed,
      generatedBy: 'mgr_001',
      generatedAt: DateTime.now().subtract(const Duration(days: 7)),
      data: const FinancialReportData(
        summary: {
          'totalRevenue': 1000000,
          'totalExpenses': 850000,
          'netIncome': 150000,
          'grossMargin': 40.0,
        },
      ),
      tags: const ['quarterly', 'profit-loss', '2024'],
    ),
  ];
}
