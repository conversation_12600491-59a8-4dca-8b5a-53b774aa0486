import 'package:equatable/equatable.dart';

class UpdateQualityInspectionRequest extends Equatable {
  final String inspectionId;
  final String? productId;
  final String? batchNumber;
  final int? quantity;
  final String? inspectorId;
  final DateTime? scheduledDate;
  final String? status;
  final String? notes;
  final Map<String, dynamic>? customFields;

  const UpdateQualityInspectionRequest({
    required this.inspectionId,
    this.productId,
    this.batchNumber,
    this.quantity,
    this.inspectorId,
    this.scheduledDate,
    this.status,
    this.notes,
    this.customFields,
  });

  Map<String, dynamic> toJson() => {
        'inspectionId': inspectionId,
        if (productId != null) 'productId': productId,
        if (batchNumber != null) 'batchNumber': batchNumber,
        if (quantity != null) 'quantity': quantity,
        if (inspectorId != null) 'inspectorId': inspectorId,
        if (scheduledDate != null) 'scheduledDate': scheduledDate?.toIso8601String(),
        if (status != null) 'status': status,
        if (notes != null) 'notes': notes,
        if (customFields != null) ...customFields!,
      };

  @override
  List<Object?> get props => [
        inspectionId,
        productId,
        batchNumber,
        quantity,
        inspectorId,
        scheduledDate,
        status,
        notes,
        customFields,
      ];
}
