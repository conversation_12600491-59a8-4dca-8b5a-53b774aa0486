import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Merchandiser Dashboard Page - Order management and customer relations
class MerchandiserDashboardPage extends StatelessWidget {
  const MerchandiserDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Merchandiser Dashboard'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildMerchandiserDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildMerchandiserDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildOrderOverview(),
          const SizedBox(height: 24),
          _buildMerchandiserActions(context),
          const SizedBox(height: 24),
          _buildCustomerInsights(),
          const SizedBox(height: 24),
          _buildProductionStatus(),
          const SizedBox(height: 24),
          _buildRecentOrders(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue,
            Colors.blue.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Merchandiser'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Order management and customer relations',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.shopping_cart,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Order Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Total Orders',
              value: '89',
              icon: Icons.shopping_bag,
              color: Colors.blue,
              trend: '+15%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Pending Orders',
              value: '23',
              icon: Icons.pending,
              color: Colors.orange,
              trend: '-5%',
              isPositiveTrend: false,
            ),
            DashboardMetricCard(
              title: 'In Production',
              value: '45',
              icon: Icons.factory,
              color: Colors.purple,
              trend: '+8%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Completed',
              value: '21',
              icon: Icons.check_circle,
              color: Colors.green,
              trend: '+12%',
              isPositiveTrend: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMerchandiserActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Create Order',
              subtitle: 'Add new customer order',
              icon: Icons.add_shopping_cart,
              color: Colors.blue,
              onTap: () {
                // Navigate to create order
              },
            ),
            DashboardCard(
              title: 'Customer Management',
              subtitle: 'Manage customer relationships',
              icon: Icons.people,
              color: Colors.green,
              onTap: () {
                // Navigate to customer management
              },
            ),
            DashboardCard(
              title: 'Production Planning',
              subtitle: 'Plan production schedules',
              icon: Icons.calendar_today,
              color: Colors.purple,
              onTap: () {
                // Navigate to production planning
              },
            ),
            DashboardCard(
              title: 'Order Reports',
              subtitle: 'View order analytics',
              icon: Icons.analytics,
              color: Colors.orange,
              onTap: () {
                // Navigate to order reports
              },
            ),
            DashboardCard(
              title: 'Quotations',
              subtitle: 'Manage price quotations',
              icon: Icons.request_quote,
              color: Colors.teal,
              onTap: () {
                // Navigate to quotations
              },
            ),
            DashboardCard(
              title: 'Delivery Tracking',
              subtitle: 'Track order deliveries',
              icon: Icons.local_shipping,
              color: Colors.indigo,
              onTap: () {
                // Navigate to delivery tracking
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomerInsights() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.insights,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Customer Insights',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to detailed customer insights
                  },
                  child: const Text('View Details'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInsightCard('Active Customers', '45', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildInsightCard('New This Month', '8', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildInsightCard('Repeat Orders', '67%', Colors.purple),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProductionStatus() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.factory,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Production Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to production details
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildProductionItem('Order #1234', 'Cutting', 0.75, Colors.blue),
            const SizedBox(height: 8),
            _buildProductionItem('Order #1235', 'Sewing', 0.45, Colors.orange),
            const SizedBox(height: 8),
            _buildProductionItem('Order #1236', 'Finishing', 0.90, Colors.green),
            const SizedBox(height: 8),
            _buildProductionItem('Order #1237', 'Quality Check', 0.60, Colors.purple),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionItem(String order, String stage, double progress, Color color) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            order,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            stage,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${(progress * 100).toInt()}%',
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentOrders() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.history,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Recent Orders',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to all orders
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildOrderItem(
              'ORD-001234',
              'ABC Fashion Ltd.',
              'In Production',
              Colors.blue,
              Icons.factory,
            ),
            _buildOrderItem(
              'ORD-001235',
              'XYZ Garments',
              'Pending',
              Colors.orange,
              Icons.pending,
            ),
            _buildOrderItem(
              'ORD-001236',
              'Fashion Hub',
              'Completed',
              Colors.green,
              Icons.check_circle,
            ),
            _buildOrderItem(
              'ORD-001237',
              'Style Works',
              'Quality Check',
              Colors.purple,
              Icons.verified,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(String orderNo, String customer, String status, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  orderNo,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  customer,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
