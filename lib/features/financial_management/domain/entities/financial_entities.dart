// Version 5 - Forcing an update
import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';
import 'financial_report_entities.dart';

///* Entity Classes

class CostCenter extends BaseEntity {
  final String costCenterCode;
  final String costCenterName;
  final String description;
  final CostCenterType type;
  final CommonStatus status;
  final String? parentCostCenterId;
  final String? parentCostCenterName;
  final String departmentId;
  final String departmentName;
  final String? managerId;
  final String? managerName;
  final CostCenterBudget budget;
  final CostCenterMetrics metrics;
  final List<String> childCostCenterIds;
  final Map<String, dynamic> customFields;

  const CostCenter({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.costCenterCode,
    required this.costCenterName,
    required this.description,
    required this.type,
    required this.status,
    this.parentCostCenterId,
    this.parentCostCenterName,
    required this.departmentId,
    required this.departmentName,
    this.managerId,
    this.managerName,
    required this.budget,
    required this.metrics,
    this.childCostCenterIds = const [],
    this.customFields = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        costCenterCode,
        costCenterName,
        description,
        type,
        status,
        parentCostCenterId,
        parentCostCenterName,
        departmentId,
        departmentName,
        managerId,
        managerName,
        budget,
        metrics,
        childCostCenterIds,
        customFields,
      ];
}

class CostCenterBudget extends Equatable {
  final double allocatedAmount;
  final double spentAmount;
  final double committedAmount;
  final double availableAmount;
  final String currency;
  final BudgetPeriod period;
  final DateTime periodStart;
  final DateTime periodEnd;
  final Map<CostCategory, double> categoryAllocations;
  final Map<CostCategory, double> categorySpent;

  const CostCenterBudget({
    required this.allocatedAmount,
    required this.spentAmount,
    required this.committedAmount,
    required this.availableAmount,
    required this.currency,
    required this.period,
    required this.periodStart,
    required this.periodEnd,
    this.categoryAllocations = const {},
    this.categorySpent = const {},
  });

  @override
  List<Object?> get props => [
        allocatedAmount,
        spentAmount,
        committedAmount,
        availableAmount,
        currency,
        period,
        periodStart,
        periodEnd,
        categoryAllocations,
        categorySpent,
      ];
}

class CostCenterMetrics extends Equatable {
  final double totalCosts;
  final double directCosts;
  final double indirectCosts;
  final double laborCosts;
  final double materialCosts;
  final double overheadCosts;
  final double costPerUnit;
  final double costPerHour;
  final double efficiency;
  final double productivity;
  final int transactionCount;
  final DateTime lastUpdated;

  const CostCenterMetrics({
    required this.totalCosts,
    required this.directCosts,
    required this.indirectCosts,
    required this.laborCosts,
    required this.materialCosts,
    required this.overheadCosts,
    required this.costPerUnit,
    required this.costPerHour,
    required this.efficiency,
    required this.productivity,
    required this.transactionCount,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        totalCosts,
        directCosts,
        indirectCosts,
        laborCosts,
        materialCosts,
        overheadCosts,
        costPerUnit,
        costPerHour,
        efficiency,
        productivity,
        transactionCount,
        lastUpdated,
      ];
}

class FinancialTransaction extends BaseEntity {
  final String transactionNumber;
  final TransactionType type;
  final TransactionCategory category;
  final double amount;
  final String currency;
  final DateTime transactionDate;
  final String description;
  final String? reference;
  final String costCenterId;
  final String costCenterName;
  final String? orderId;
  final String? orderNumber;
  final String? productionOrderId;
  final String? supplierId;
  final String? supplierName;
  final String? customerId;
  final String? customerName;
  final String? employeeId;
  final String? employeeName;
  final TransactionStatus status;
  final String? approvedBy;
  final DateTime? approvedAt;
  final String? rejectedBy;
  final DateTime? rejectedAt;
  final String? rejectionReason;
  final List<TransactionLineItem> lineItems;
  final Map<String, dynamic> metadata;

  bool get isRevenue => type == TransactionType.revenue;

  const FinancialTransaction({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.transactionNumber,
    required this.type,
    required this.category,
    required this.amount,
    required this.currency,
    required this.transactionDate,
    required this.description,
    this.reference,
    required this.costCenterId,
    required this.costCenterName,
    this.orderId,
    this.orderNumber,
    this.productionOrderId,
    this.supplierId,
    this.supplierName,
    this.customerId,
    this.customerName,
    this.employeeId,
    this.employeeName,
    required this.status,
    this.approvedBy,
    this.approvedAt,
    this.rejectedBy,
    this.rejectedAt,
    this.rejectionReason,
    this.lineItems = const [],
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        transactionNumber,
        type,
        category,
        amount,
        currency,
        transactionDate,
        description,
        reference,
        costCenterId,
        costCenterName,
        orderId,
        orderNumber,
        productionOrderId,
        supplierId,
        supplierName,
        customerId,
        customerName,
        employeeId,
        employeeName,
        status,
        approvedBy,
        approvedAt,
        rejectedBy,
        rejectedAt,
        rejectionReason,
        lineItems,
        metadata,
      ];
}

class TransactionLineItem extends Equatable {
  final String description;
  final double quantity;
  final String unit;
  final double unitPrice;
  final double totalAmount;
  final String? productId;
  final String? productName;
  final String? materialId;
  final String? materialName;
  final CostCategory costCategory;
  final Map<String, dynamic> attributes;

  const TransactionLineItem({
    required this.description,
    required this.quantity,
    required this.unit,
    required this.unitPrice,
    required this.totalAmount,
    this.productId,
    this.productName,
    this.materialId,
    this.materialName,
    required this.costCategory,
    this.attributes = const {},
  });

  @override
  List<Object?> get props => [
        description,
        quantity,
        unit,
        unitPrice,
        totalAmount,
        productId,
        productName,
        materialId,
        materialName,
        costCategory,
        attributes,
      ];
}

class Budget extends BaseEntity {
  final String budgetCode;
  final String budgetName;
  final String description;
  final BudgetType type;
  final BudgetPeriod period;
  final DateTime periodStart;
  final DateTime periodEnd;
  final CommonStatus status;
  final double totalAmount;
  final String currency;
  final String? costCenterId;
  final String? costCenterName;
  final String? departmentId;
  final String? departmentName;
  final String? projectId;
  final String? projectName;
  final String ownerId;
  final String ownerName;
  final List<BudgetLineItem> lineItems;
  final BudgetMetrics metrics;
  final List<String> approverIds;
  final BudgetApprovalStatus approvalStatus;
  final DateTime? approvedAt;
  final String? approvedBy;

  const Budget({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.budgetCode,
    required this.budgetName,
    required this.description,
    required this.type,
    required this.period,
    required this.periodStart,
    required this.periodEnd,
    required this.status,
    required this.totalAmount,
    required this.currency,
    this.costCenterId,
    this.costCenterName,
    this.departmentId,
    this.departmentName,
    this.projectId,
    this.projectName,
    required this.ownerId,
    required this.ownerName,
    this.lineItems = const [],
    required this.metrics,
    this.approverIds = const [],
    required this.approvalStatus,
    this.approvedAt,
    this.approvedBy,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        budgetCode,
        budgetName,
        description,
        type,
        period,
        periodStart,
        periodEnd,
        status,
        totalAmount,
        currency,
        costCenterId,
        costCenterName,
        departmentId,
        departmentName,
        projectId,
        projectName,
        ownerId,
        ownerName,
        lineItems,
        metrics,
        approverIds,
        approvalStatus,
        approvedAt,
        approvedBy,
      ];
}

class BudgetLineItem extends Equatable {
  final String description;
  final CostCategory category;
  final double allocatedAmount;
  final double spentAmount;
  final double committedAmount;
  final String? accountCode;
  final String? accountName;
  final Map<String, dynamic> attributes;

  const BudgetLineItem({
    required this.description,
    required this.category,
    required this.allocatedAmount,
    required this.spentAmount,
    required this.committedAmount,
    this.accountCode,
    this.accountName,
    this.attributes = const {},
  });

  @override
  List<Object?> get props => [
        description,
        category,
        allocatedAmount,
        spentAmount,
        committedAmount,
        accountCode,
        accountName,
        attributes,
      ];
}

class BudgetMetrics extends Equatable {
  final double spentAmount;
  final double committedAmount;
  final double remainingAmount;
  final double utilizationPercentage;
  final double varianceAmount;
  final double variancePercentage;
  final bool isExceeded;
  final DateTime lastUpdated;

  const BudgetMetrics({
    required this.spentAmount,
    required this.committedAmount,
    required this.remainingAmount,
    required this.utilizationPercentage,
    required this.varianceAmount,
    required this.variancePercentage,
    required this.isExceeded,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        spentAmount,
        committedAmount,
        remainingAmount,
        utilizationPercentage,
        varianceAmount,
        variancePercentage,
        isExceeded,
        lastUpdated,
      ];
}

class CostTrends extends Equatable {
  final List<CostTrend> trends;

  const CostTrends({required this.trends});

  @override
  List<Object?> get props => [trends];
}

class CostTrend extends Equatable {
  final DateTime date;
  final double amount;

  const CostTrend({required this.date, required this.amount, required CostCategory category, String? costCenterId});

  @override
  List<Object?> get props => [date, amount];
}

class FinancialSummary extends Equatable {
  final double totalRevenue;
  final double totalExpenses;
  final double netProfit;
  final double profitMargin;
  final double? revenueChange;
  final double? expenseChange;
  final double? profitChange;
  final double? marginChange;

  const FinancialSummary({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    required this.profitMargin,
    this.revenueChange,
    this.expenseChange,
    this.profitChange,
    this.marginChange,
  });

  @override
  List<Object?> get props => [
        totalRevenue,
        totalExpenses,
        netProfit,
        profitMargin,
        revenueChange,
        expenseChange,
        profitChange,
        marginChange,
      ];
}

///* Enums

enum CostCenterType {
  production,
  service,
  administrative,
  sales,
  research,
}

enum TransactionType {
  revenue,
  expense,
  transfer,
  adjustment,
}

enum TransactionCategory {
  sales,
  materialPurchase,
  laborCost,
  overhead,
  equipment,
  maintenance,
  utilities,
  rent,
  insurance,
  marketing,
  administration,
  other,
}

enum TransactionStatus {
  draft,
  pending,
  approved,
  rejected,
  posted,
  cancelled,
}

enum CostCategory {
  directMaterial,
  directLabor,
  manufacturingOverhead,
  sellingExpense,
  administrativeExpense,
  other,
}

enum BudgetType {
  operational,
  capital,
  project,
  department,
  costCenter,
}

enum BudgetPeriod {
  monthly,
  quarterly,
  semiAnnual,
  annual,
  custom,
}

enum BudgetApprovalStatus {
  draft,
  submitted,
  underReview,
  approved,
  rejected,
  revision,
}

///* Request Classes

class CreateCostCenterRequest extends Equatable {
  final String costCenterCode;
  final String costCenterName;
  final String description;
  final CostCenterType type;
  final String? parentCostCenterId;
  final String departmentId;
  final String? managerId;
  final CostCenterBudget budget;

  const CreateCostCenterRequest({
    required this.costCenterCode,
    required this.costCenterName,
    required this.description,
    required this.type,
    this.parentCostCenterId,
    required this.departmentId,
    this.managerId,
    required this.budget,
  });

  @override
  List<Object?> get props => [
        costCenterCode,
        costCenterName,
        description,
        type,
        parentCostCenterId,
        departmentId,
        managerId,
        budget,
      ];
}

class UpdateCostCenterRequest extends Equatable {
  final String id;
  final String? name;

  const UpdateCostCenterRequest({required this.id, this.name});

  @override
  List<Object?> get props => [id, name];
}

class CreateTransactionRequest extends Equatable {
  final TransactionType type;
  final TransactionCategory category;
  final double amount;
  final DateTime transactionDate;
  final String description;
  final String costCenterId;

  const CreateTransactionRequest({
    required this.type,
    required this.category,
    required this.amount,
    required this.transactionDate,
    required this.description,
    required this.costCenterId,
  });

  @override
  List<Object?> get props =>
      [type, category, amount, transactionDate, description, costCenterId];
}

class UpdateTransactionRequest extends Equatable {
  final String id;
  final TransactionType? type;
  final TransactionCategory? category;
  final double? amount;
  final DateTime? transactionDate;
  final String? description;
  final String? costCenterId;

  const UpdateTransactionRequest({
    required this.id,
    this.type,
    this.category,
    this.amount,
    this.transactionDate,
    this.description,
    this.costCenterId,
  });

  @override
  List<Object?> get props =>
      [id, type, category, amount, transactionDate, description, costCenterId];
}

class CreateBudgetRequest extends Equatable {
  final String budgetName;
  final BudgetType type;
  final BudgetPeriod period;
  final DateTime periodStart;
  final DateTime periodEnd;
  final double totalAmount;
  final String? costCenterId;
  final String? departmentId;

  const CreateBudgetRequest({
    required this.budgetName,
    required this.type,
    required this.period,
    required this.periodStart,
    required this.periodEnd,
    required this.totalAmount,
    this.costCenterId,
    this.departmentId,
  });

  @override
  List<Object?> get props => [
        budgetName,
        type,
        period,
        periodStart,
        periodEnd,
        totalAmount,
        costCenterId,
        departmentId,
      ];
}

class UpdateBudgetRequest extends Equatable {
  final String id;
  final String? budgetName;
  final BudgetType? type;
  final BudgetPeriod? period;
  final DateTime? periodStart;
  final DateTime? periodEnd;
  final double? totalAmount;
  final String? costCenterId;
  final String? departmentId;

  const UpdateBudgetRequest({
    required this.id,
    this.budgetName,
    this.type,
    this.period,
    this.periodStart,
    this.periodEnd,
    this.totalAmount,
    this.costCenterId,
    this.departmentId,
  });

  @override
  List<Object?> get props => [
        id,
        budgetName,
        type,
        period,
        periodStart,
        periodEnd,
        totalAmount,
        costCenterId,
        departmentId,
      ];
}

class GenerateReportRequest extends Equatable {
  final String reportName;
  final String description;
  final FinancialReportType type;
  final ReportPeriod period;
  final DateTime periodStart;
  final DateTime periodEnd;
  final String currency;
  final Map<String, dynamic> parameters;
  final List<String> tags;

  const GenerateReportRequest({
    required this.reportName,
    required this.description,
    required this.type,
    required this.period,
    required this.periodStart,
    required this.periodEnd,
    required this.currency,
    required this.parameters,
    this.tags = const [],
  });

  @override
  List<Object?> get props => [
        reportName,
        description,
        type,
        period,
        periodStart,
        periodEnd,
        currency,
        parameters,
        tags,
      ];
}

///* Filter Criteria Classes

class CostCenterFilterCriteria extends Equatable {
  final String? name;
  final CostCenterType? type;
  final CommonStatus? status;
  final String? departmentId;

  const CostCenterFilterCriteria({this.name, this.type, this.status, this.departmentId});

  @override
  List<Object?> get props => [name, type, status, departmentId];
}

class TransactionFilterCriteria extends Equatable {
  final TransactionType? type;
  final TransactionCategory? category;
  final TransactionStatus? status;
  final String? costCenterId;
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minAmount;
  final double? maxAmount;

  const TransactionFilterCriteria({
    this.type,
    this.category,
    this.status,
    this.costCenterId,
    this.startDate,
    this.endDate,
    this.minAmount,
    this.maxAmount,
  });

  @override
  List<Object?> get props => [
        type,
        category,
        status,
        costCenterId,
        startDate,
        endDate,
        minAmount,
        maxAmount,
      ];
}

class BudgetFilterCriteria extends Equatable {
  final BudgetApprovalStatus? approvalStatus;
  final String? departmentId;

  const BudgetFilterCriteria({this.approvalStatus, this.departmentId});
  @override
  List<Object?> get props => [approvalStatus, departmentId];
}

class ReportFilterCriteria extends Equatable {
  final FinancialReportType? type;
  final ReportPeriod? period;
  final ReportStatus? status;
  final String? generatedBy;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String>? tags;

  const ReportFilterCriteria(
      {this.type,
      this.period,
      this.status,
      this.generatedBy,
      this.startDate,
      this.endDate,
      this.tags});
  @override
  List<Object?> get props =>
      [type, period, status, generatedBy, startDate, endDate, tags];
}
