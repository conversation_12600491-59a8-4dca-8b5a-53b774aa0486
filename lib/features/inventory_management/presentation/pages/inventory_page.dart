import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:hm_collection/features/inventory_management/presentation/pages/denim_purchase_page.dart';
import 'package:hm_collection/features/inventory_management/presentation/pages/denim_return_page.dart';
import 'package:hm_collection/features/inventory_management/presentation/pages/denim_stock_tracking_page.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/route_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/inventory_entities.dart';
import '../../domain/repositories/inventory_repository.dart' as repo;
import '../bloc/inventory_bloc.dart';
import '../widgets/inventory_app_bar.dart';
import '../widgets/inventory_list_view.dart';
import '../widgets/inventory_search_bar.dart';
import 'denim_types_master_page.dart';

/// Inventory management page
class InventoryPage extends StatelessWidget {
  const InventoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<InventoryBloc>()
        ..add(const LoadInventoryItemsRequested()),
      child: const InventoryView(),
    );
  }
}

class InventoryView extends StatefulWidget {
  const InventoryView({super.key});

  @override
  State<InventoryView> createState() => _InventoryViewState();
}

class _InventoryViewState extends State<InventoryView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  repo.InventoryFilterCriteria? _currentFilter;
  String? _searchQuery;
  InventoryViewMode _viewMode = InventoryViewMode.list;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Inventory', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(),
            color: Colors.white,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            color: Colors.white,
          ),
          PopupMenuButton<String>(
            iconColor: Colors.white,
            onSelected: (value) {
              switch (value) {
                case 'purchase':
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) => const DenimPurchasePage()));
                  break;
                case 'return':
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) => const DenimReturnPage()));
                  break;
                case 'display':
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) => const DenimStockTrackingPage()));
                  break;
                case 'master':
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) => const DenimTypesMasterPage()));
                  break;
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'purchase',
                child: Text('Denim Purchase'),
              ),
              const PopupMenuItem<String>(
                value: 'return',
                child: Text('Denim Return'),
              ),
              const PopupMenuItem<String>(
                value: 'display',
                child: Text('Denim Display'),
              ),
              const PopupMenuItem<String>(
                value: 'master',
                child: Text('Denim Types Master'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllItemsTab(),
                _buildLowStockTab(),
                _buildOutOfStockTab(),
                _buildOverstockedTab(),
                _buildExpiringTab(),
                _buildMovementsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(text: 'All Items'),
          Tab(text: 'Low Stock'),
          Tab(text: 'Out of Stock'),
          Tab(text: 'Overstocked'),
          Tab(text: 'Expiring'),
          Tab(text: 'Movements'),
        ],
        onTap: _handleTabChanged,
      ),
    );
  }

  Widget _buildAllItemsTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            _buildStatisticsSection(state),
            Expanded(
              child: _buildInventoryItemsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLowStockTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildLowStockItemsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildOutOfStockTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildOutOfStockItemsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildOverstockedTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildOverstockedItemsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildExpiringTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildExpiringItemsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMovementsTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildStockMovementsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: InventorySearchBar(
        onSearchChanged: _handleSearch,
        controller: _searchQuery != null 
            ? TextEditingController(text: _searchQuery)
            : null,
      ),
    );
  }

  Widget _buildStatisticsSection(InventoryState state) {
    if (state is InventoryItemsLoaded && state.items.isNotEmpty) {
      final stats = _calculateStatistics(state.items);
      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 8.0,
        ),
        height: 120,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildStatCard(
                'Total Items',
                stats.totalItems.toString(),
                Icons.inventory_2_outlined,
                AppColors.primary,
                () => _handleStatisticTap('overview'),
              ),
              const SizedBox(width: 8),
              _buildStatCard(
                'Low Stock',
                stats.lowStockItems.toString(),
                Icons.warning_amber_outlined,
                Colors.orange,
                () => _handleStatisticTap('low_stock'),
              ),
              const SizedBox(width: 8),
              _buildStatCard(
                'Out of Stock',
                stats.outOfStockItems.toString(),
                Icons.error_outline,
                Colors.red,
                () => _handleStatisticTap('out_of_stock'),
              ),
              const SizedBox(width: 8),
              _buildStatCard(
                'Overstocked',
                stats.overstockedItems.toString(),
                Icons.all_inbox,
                Colors.purple,
                () => _handleStatisticTap('overstocked'),
              ),
              const SizedBox(width: 8),
              _buildStatCard(
                'Expiring Soon',
                stats.expiringItems.toString(),
                Icons.hourglass_bottom_outlined,
                Colors.blue,
                () => _handleStatisticTap('expiring'),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Card(
        color: color.withOpacity(0.1),
        child: Container(
          width: 200,
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(height: 8),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInventoryItemsList(InventoryState state) {
    if (state is InventoryLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is InventoryError) {
      return _buildErrorWidget(state.message);
    }

    if (state is InventoryItemsLoaded) {
      if (state.items.isEmpty) {
        return _buildEmptyState('No inventory items found', 
            'Add your first inventory item to get started.');
      }

      return RefreshIndicator(
        onRefresh: () async => _refreshInventoryItems(),
        child: InventoryListView(
          items: state.items,
          viewMode: _viewMode,
          selectedItemIds: state.selectedItemIds,
          onItemTap: _navigateToItemDetails,
          onItemLongPress: _handleItemLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStockAdjust: _handleStockAdjust,
          onLoadMore: _loadMoreItems,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: state.isRefreshing,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildLowStockItemsList(InventoryState state) {
    if (state is InventoryLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is InventoryError) {
      return _buildErrorWidget(state.message);
    }

    if (state is LowStockItemsLoaded) {
      if (state.items.isEmpty) {
        return _buildEmptyState('No Low Stock Items', 
            'Great! All items have sufficient stock levels.');
      }

      return RefreshIndicator(
        onRefresh: () async => _loadLowStockItems(),
        child: InventoryListView(
          items: state.items,
          viewMode: _viewMode,
          selectedItemIds: const [],
          onItemTap: _navigateToItemDetails,
          onItemLongPress: _handleItemLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStockAdjust: _handleStockAdjust,
          onLoadMore: _loadMoreItems,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildOutOfStockItemsList(InventoryState state) {
    if (state is InventoryLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is InventoryError) {
      return _buildErrorWidget(state.message);
    }

    if (state is OutOfStockItemsLoaded) {
      if (state.items.isEmpty) {
        return _buildEmptyState('No Out of Stock Items', 
            'Excellent! All items are in stock.');
      }

      return RefreshIndicator(
        onRefresh: () async => _loadOutOfStockItems(),
        child: InventoryListView(
          items: state.items,
          viewMode: _viewMode,
          selectedItemIds: const [],
          onItemTap: _navigateToItemDetails,
          onItemLongPress: _handleItemLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStockAdjust: _handleStockAdjust,
          onLoadMore: _loadMoreItems,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildOverstockedItemsList(InventoryState state) {
    if (state is InventoryLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is InventoryError) {
      return _buildErrorWidget(state.message);
    }

    if (state is OverstockedItemsLoaded) {
      if (state.items.isEmpty) {
        return _buildEmptyState('No Overstocked Items', 
            'Good! No items are overstocked.');
      }

      return RefreshIndicator(
        onRefresh: () async => _loadOverstockedItems(),
        child: InventoryListView(
          items: state.items,
          viewMode: _viewMode,
          selectedItemIds: const [],
          onItemTap: _navigateToItemDetails,
          onItemLongPress: _handleItemLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStockAdjust: _handleStockAdjust,
          onLoadMore: _loadMoreItems,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildExpiringItemsList(InventoryState state) {
    if (state is InventoryLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is InventoryError) {
      return _buildErrorWidget(state.message);
    }

    if (state is ExpiringItemsLoaded) {
      if (state.items.isEmpty) {
        return _buildEmptyState('No Expiring Items', 
            'No items are expiring in the next ${state.daysAhead} days.');
      }

      return RefreshIndicator(
        onRefresh: () async => _loadExpiringItems(),
        child: InventoryListView(
          items: state.items,
          viewMode: _viewMode,
          selectedItemIds: const [],
          onItemTap: _navigateToItemDetails,
          onItemLongPress: _handleItemLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStockAdjust: _handleStockAdjust,
          onLoadMore: _loadMoreItems,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildStockMovementsList(InventoryState state) {
    if (state is InventoryLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is InventoryError) {
      return _buildErrorWidget(state.message);
    }

    if (state is StockMovementsLoaded) {
      if (state.movements.isEmpty) {
        return _buildEmptyState('No Stock Movements', 
            'No stock movements recorded yet.');
      }

      return RefreshIndicator(
        onRefresh: () async => _loadStockMovements(),
        child: ListView.builder(
          itemCount: state.movements.length,
          itemBuilder: (context, index) {
            final movement = state.movements[index];
            return ListTile(
              leading: Icon(
                movement.isInbound ? Icons.add_circle : Icons.remove_circle,
                color: movement.isInbound ? Colors.green : Colors.red,
              ),
              title: Text(movement.itemName),
              subtitle: Text('${movement.movementType.displayName} - ${movement.reason}'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${movement.isInbound ? '+' : '-'}${movement.quantity}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: movement.isInbound ? Colors.green : Colors.red,
                    ),
                  ),
                  Text(
                    movement.movementDate.toString().split(' ')[0],
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            );
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading inventory',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _refreshInventoryItems,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleTabChanged(int index) {
    switch (index) {
      case 0:
        context.read<InventoryBloc>().add(const LoadInventoryItemsRequested());
        break;
      case 1:
        _loadLowStockItems();
        break;
      case 2:
        _loadOutOfStockItems();
        break;
      case 3:
        _loadOverstockedItems();
        break;
      case 4:
        _loadExpiringItems();
        break;
      case 5:
        _loadStockMovements();
        break;
    }
  }

  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query.isEmpty ? null : query;
    });

    if (query.isEmpty) {
      context.read<InventoryBloc>().add(LoadInventoryItemsRequested(filter: _currentFilter));
    } else {
      context.read<InventoryBloc>().add(SearchInventoryItemsRequested(
        query,
        filter: _currentFilter,
      ));
    }
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = null;
    });
    context.read<InventoryBloc>().add(LoadInventoryItemsRequested(filter: _currentFilter));
  }

  void _showFilterDialog() async {
    // TODO: Implement filter dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Filter dialog coming soon')),
    );
  }

  void _handleViewModeChanged(InventoryViewMode viewMode) {
    setState(() {
      _viewMode = viewMode;
    });
    context.read<InventoryBloc>().add(ChangeInventoryViewModeRequested(viewMode));
  }

  void _handleStatisticTap(String statistic) {
    // Handle statistic tap for filtering or navigation
    switch (statistic) {
      case 'low_stock':
        _tabController.animateTo(1);
        break;
      case 'out_of_stock':
        _tabController.animateTo(2);
        break;
      case 'overstocked':
        _tabController.animateTo(3);
        break;
      case 'expiring':
        _tabController.animateTo(4);
        break;
    }
  }

  void _navigateToItemDetails(String itemId) {
    // Navigate to item details page with the item ID
    context.pushNamed(
      RouteConstants.materialDetails,
      pathParameters: {'id': itemId},
    );
  }

  void _handleItemLongPress(String itemId) {
    context.read<InventoryBloc>().add(ToggleInventoryItemSelectionRequested(itemId));
  }

  void _handleSelectionChanged(List<String> selectedIds) {
    // Handle selection change for bulk operations
  }

  void _handleStockAdjust(String itemId) {
    _showStockAdjustmentDialog(itemId);
  }

  void _showStockAdjustmentDialog(String itemId) {
    // TODO: Show stock adjustment dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Stock adjustment dialog coming soon')),
    );
  }

  repo.InventoryStatistics _calculateStatistics(List<InventoryItem> items) {
    final totalItems = items.length;
    final activeItems = items.where((item) => item.status == ItemStatus.active).length;
    final lowStockItems = items.where((item) => item.isLowStock).length;
    final outOfStockItems = items.where((item) => item.isOutOfStock).length;
    final overstockedItems = items.where((item) => item.isOverstocked).length;
    final expiringItems = items.where((item) => item.isExpiringSoon).length;
    final totalValue = items.fold<double>(0.0, (sum, item) => sum + item.totalValue);
    final averageValue = totalItems > 0 ? totalValue / totalItems : 0.0;

    // Group items by category
    final itemsByCategory = <String, int>{};
    final valueByCategory = <String, double>{};
    for (final item in items) {
      final categoryName = item.category.displayName;
      itemsByCategory[categoryName] = (itemsByCategory[categoryName] ?? 0) + 1;
      valueByCategory[categoryName] = (valueByCategory[categoryName] ?? 0.0) + item.totalValue;
    }

    return repo.InventoryStatistics(
      totalItems: totalItems,
      activeItems: activeItems,
      lowStockItems: lowStockItems,
      outOfStockItems: outOfStockItems,
      overstockedItems: overstockedItems,
      expiringItems: expiringItems,
      totalValue: totalValue,
      averageValue: averageValue,
      itemsByCategory: itemsByCategory,
      valueByCategory: valueByCategory,
      movementsByType: const {}, // This would need to be calculated from movements data
    );
  }

  void _refreshInventoryItems() {
    context.read<InventoryBloc>().add(const RefreshInventoryItemsRequested());
  }

  void _loadMoreItems() {
    context.read<InventoryBloc>().add(const LoadMoreInventoryItemsRequested());
  }

  void _loadLowStockItems() {
    context.read<InventoryBloc>().add(const LoadLowStockItemsRequested());
  }

  void _loadOutOfStockItems() {
    context.read<InventoryBloc>().add(const LoadOutOfStockItemsRequested());
  }

  void _loadOverstockedItems() {
    context.read<InventoryBloc>().add(const LoadOverstockedItemsRequested());
  }

  void _loadExpiringItems() {
    context.read<InventoryBloc>().add(const LoadExpiringItemsRequested());
  }

  void _loadStockMovements() {
    context.read<InventoryBloc>().add(const LoadStockMovementsRequested());
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Inventory'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Enter search query...',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (query) {
            Navigator.of(context).pop();
            _handleSearch(query);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
