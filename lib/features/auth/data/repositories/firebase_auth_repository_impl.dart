import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/material.dart';
import 'package:hm_collection/core/auth/entities/user_entities.dart'
    as auth_entities;
import 'package:hm_collection/features/auth/domain/entities/biometric_auth_data.dart';
import 'package:hm_collection/features/auth/domain/entities/login_credentials.dart';
import 'package:hm_collection/features/auth/domain/entities/password_reset_confirm_request.dart';
import 'package:injectable/injectable.dart';
// import 'package:rxdart/rxdart.dart';

// import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/firebase/firebase_auth_service.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/auth_tokens.dart';
import '../../domain/entities/two_factor_auth_data.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../models/user_model.dart';

/// Firebase Authentication Repository Implementation
@LazySingleton(as: AuthRepository, env: [Environment.prod])
class FirebaseAuthRepositoryImpl implements AuthRepository {
  final FirebaseAuthService _firebaseAuthService;
  final firebase_auth.FirebaseAuth _firebaseAuth =
      firebase_auth.FirebaseAuth.instance;

  FirebaseAuthRepositoryImpl(this._firebaseAuthService);


  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> adminLogin(
    LoginCredentials credentials,
  ) async {
    try {
      // Sign in with email and password
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: credentials.email,
        password: credentials.password,
      );

      // Get the ID token
      final idToken = await userCredential.user?.getIdToken();
      final refreshToken = userCredential.user?.refreshToken;

      if (idToken == null || refreshToken == null) {
        return const Left(AuthFailure('Failed to get authentication tokens'));
      }

      // Here you should add additional verification to check if the user has admin role
      // For example, you might check a custom claim or a user document in Firestore
      // This is a placeholder - replace with your actual admin verification logic
      final isAdmin = await _verifyAdminRole(userCredential.user?.uid);
      
      if (!isAdmin) {
        // Sign out the user if they don't have admin privileges
        await _firebaseAuth.signOut();
        return const Left(AuthFailure('Access denied. Administrator privileges required.'));
      }

      final now = DateTime.now();
      final tokens = AuthTokens(
        accessToken: idToken,
        refreshToken: refreshToken,
        tokenType: 'Bearer',
        expiresIn: 3600, // Default expiration time in seconds
        issuedAt: now,
        expiresAt: now.add(const Duration(seconds: 3600)),
        scopes: const ['admin'],
      );

      return Right(ApiResponse<AuthTokens>(
        success: true,
        data: tokens,
        message: 'Admin login successful',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(e.message ?? 'Authentication failed'));
    } catch (e) {
      return Left(AuthFailure('An unexpected error occurred'));
    }
  }

  Future<bool> _verifyAdminRole(String? userId) async {
    if (userId == null) return false;
    
    try {
      // TODO: Implement actual admin verification logic
      // Example: Check a custom claim or a document in Firestore
      // final userDoc = await FirebaseFirestore.instance.collection('users').doc(userId).get();
      // return userDoc.data()?['role'] == 'admin';
      
      // For now, return true to allow the flow to continue
      // Remove this in production and implement the actual check
      return true;
    } catch (e) {
      debugPrint('Error verifying admin role: $e');
      return false;
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> refreshToken(
    String refreshToken,
  ) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('No authenticated user'));
      }

      // Force token refresh
      final idToken = await user.getIdToken(true);
      final newRefreshToken = user.refreshToken;

      if (idToken == null || newRefreshToken == null) {
        return const Left(AuthFailure('Failed to refresh tokens'));
      }

      final now = DateTime.now();
      final tokens = AuthTokens(
        accessToken: idToken,
        refreshToken: newRefreshToken,
        expiresIn: 3600, // Default 1 hour expiration
        tokenType: 'Bearer',
        issuedAt: now,
        expiresAt: now.add(const Duration(seconds: 3600)),
      );

      return Right(ApiResponse<AuthTokens>.success(
        data: tokens,
        message: 'Token refreshed successfully',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Token refresh failed: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, bool>> isEmailVerified() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('No authenticated user'));
      }
      
      // Refresh the user's data to get the latest email verification status
      await user.reload();
      final refreshedUser = _firebaseAuth.currentUser;
      
      return Right(refreshedUser?.emailVerified ?? false);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(e.message ?? 'Failed to check email verification status'));
    } catch (e) {
      return Left(UnknownFailure('Failed to check email verification status: $e'));
    }
  }




  @override
  Future<Either<Failure, ApiVoidResponse>> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('No authenticated user'));
      }
      
      await user.sendEmailVerification();
      return const Right(ApiVoidResponse(success: true));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(e.message ?? 'Failed to send verification email'));
    } catch (e) {
      return Left(UnknownFailure('Failed to send verification email: $e'));
    }
  }

  // Stub implementations for methods that might not be directly supported by Firebase Auth
  // or need additional setup


  @override
  Stream<auth_entities.AppUser?> get userDataChanges {
    return _firebaseAuth.userChanges().map((user) {
      if (user == null) return null;
      return auth_entities.AppUser(
        id: user.uid,
        email: user.email ?? '',
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        isAnonymous: user.isAnonymous,
        phoneNumber: user.phoneNumber,
        role: UserRole.sewingOperator,
        status: CommonStatus.active,
        departmentIds: const [],
        metadata: const {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: user.uid,
        permissions: const [],
        profile: auth_entities.UserProfile(
          firstName: user.displayName ?? '',
          lastName: '',
          employeeId: '',
          department: '',
          hireDate: null,
          phoneNumber: user.phoneNumber ?? '',
          address: '',
          emergencyContact: '',
          emergencyPhone: '',
          customFields: const {},
          profileImageUrl: user.photoURL,
        ),
      );
    });
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> updateProfile(User user) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('No authenticated user'));
      }

      if (user.fullName != currentUser.displayName) {
        await currentUser.updateDisplayName(user.fullName);
      }

      if (user.profileImageUrl != null && user.profileImageUrl != currentUser.photoURL) {
        await currentUser.updatePhotoURL(user.profileImageUrl);
      }

      if (user.email != currentUser.email && user.isEmailVerified) {
        await currentUser.verifyBeforeUpdateEmail(user.email ?? '');
      }

      await currentUser.reload();
      final updatedUser = _firebaseAuth.currentUser;
      
      if (updatedUser == null) {
        return const Left(AuthFailure('Failed to update profile'));
      }

      final updatedUserData = User(
        id: updatedUser.uid,
        email: updatedUser.email ?? '',
        isEmailVerified: updatedUser.emailVerified,
        createdAt: updatedUser.metadata.creationTime ?? DateTime.now(),
        updatedAt: updatedUser.metadata.lastSignInTime ?? DateTime.now(),
        username: updatedUser.email?.split('@').first ?? '',
        firstName: updatedUser.displayName?.split(' ').first ?? '',
        lastName: updatedUser.displayName?.split(' ').last ?? '',
        phoneNumber: updatedUser.phoneNumber ?? '',
        profileImageUrl: updatedUser.photoURL ?? '',
        role: UserRole.sewingOperator,
        department: Department.sewing,
        status: CommonStatus.active,
        isActive: true,
        isPhoneVerified: updatedUser.phoneNumber != null,
        permissions: const [],
        lastLoginAt: updatedUser.metadata.lastSignInTime,
        emailVerifiedAt: updatedUser.emailVerified ? DateTime.now() : null,
        phoneVerifiedAt: updatedUser.phoneNumber != null ? DateTime.now() : null,
      );

      return Right(ApiResponse<User>(success: true, data: updatedUserData));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(e.message ?? 'Failed to update profile'));
    } catch (e) {
      return Left(UnknownFailure('Failed to update profile: $e'));
    }
  }

  @override
  Stream<auth_entities.AppUser?> get authStateChanges {
    return _firebaseAuth.authStateChanges().asyncMap((firebaseUser) async {
      if (firebaseUser == null) return null;
      
      final displayName = firebaseUser.displayName ?? 'User';
      final nameParts = displayName.split(' ');
      final now = DateTime.now();
      
      return auth_entities.AppUser(
        id: firebaseUser.uid,
        uid: firebaseUser.uid,
        createdAt: now,
        updatedAt: now,
        email: firebaseUser.email ?? '',
        displayName: displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        phoneNumber: firebaseUser.phoneNumber ?? '',
        role: UserRole.sewingOperator, // Default role
        status: CommonStatus.active, // Default status
        profile: auth_entities.UserProfile(
          firstName: nameParts.isNotEmpty ? nameParts.first : 'User',
          lastName: nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '',
        ),
        permissions: const [], // Default empty permissions
        departmentIds: const [], // Default empty department IDs
        currentDepartmentId: null, // Default no department
        lastLoginAt: null, // Will be updated on login
        lastActiveAt: null, // Will be updated on activity
        metadata: const {}, // Default empty metadata
      );
    });
  }


  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> login(
    LoginCredentials credentials,
  ) async {
    try {
      // First, check if the email is valid
      if (credentials.email.isEmpty || credentials.password.isEmpty) {
        return const Left(ValidationFailure('Email and password are required'));
      }

      // Sign in with Firebase
      final userCredential =
          await _firebaseAuthService.signInWithEmailAndPassword(
        email: credentials.email.trim(),
        password: credentials.password,
      ).catchError((error) {
        if (error is firebase_auth.FirebaseAuthException) {
          if (error.code == 'user-not-found' || error.code == 'wrong-password') {
            throw const AuthFailure('Invalid email or password');
          } else if (error.code == 'too-many-requests') {
            throw const AuthFailure('Too many failed login attempts. Please try again later.');
          } else if (error.code == 'user-disabled') {
            throw const AuthFailure('This account has been disabled.');
          }
        }
        throw error; // Re-throw other errors
      });

      if (userCredential?.user != null) {
        final firebaseUser = userCredential!.user!;
        
        try {
          // Get Firebase ID token
          final idToken = await firebaseUser.getIdToken(true);
          
          // Update last login time
          await _firebaseAuthService.updateLastLoginTime(firebaseUser.uid);
          
          // Create auth tokens
          final tokens = AuthTokens(
            accessToken: idToken ?? '',
            refreshToken: firebaseUser.refreshToken ?? '',
            tokenType: 'Bearer',
            expiresIn: 3600, // Firebase tokens typically expire in 1 hour
            issuedAt: DateTime.now(),
            expiresAt: DateTime.now().add(const Duration(hours: 1)),
            scopes: ['firebase'],
          );

          return Right(ApiResponse.success(data: tokens));
        } catch (tokenError) {
          return Left(AuthFailure('Failed to get authentication token: $tokenError'));
        }
      } else {
        return const Left(AuthFailure('Login failed: No user returned from authentication'));
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } on AuthFailure catch (e) {
      return Left(e);
    } catch (e) {
      return Left(UnknownFailure('An unexpected error occurred during login: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> register(
    RegisterRequest request,
  ) async {
    try {
      // Create user with Firebase
      final userCredential =
          await _firebaseAuthService.createUserWithEmailAndPassword(
        email: request.email,
        password: request.password,
        firstName: request.firstName,
        lastName: request.lastName,
        role: request.role,
      );

      if (userCredential?.user != null) {
        // Wait a moment for Firestore document to be created
        await Future.delayed(const Duration(milliseconds: 1000));
        
        // Get user data from Firebase stream
        final user = await _firebaseAuthService.userStream.first;
        
        if (user != null) {
          return Right(ApiResponse.success(data: user));
        } else {
          return const Left(UnknownFailure('Failed to create user profile'));
        }
      } else {
        return const Left(AuthFailure('Registration failed'));
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Registration failed: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuth.currentUser;
      
      if (firebaseUser != null) {
        final now = DateTime.now();
        final email = firebaseUser.email ?? '';
        final displayName = firebaseUser.displayName ?? '';
        final firstName =
            displayName.isNotEmpty ? displayName.split(' ').first : '';
        final lastName = displayName.isNotEmpty
            ? displayName.split(' ').skip(1).join(' ')
            : '';

        final domainUser = User(
          id: firebaseUser.uid,
          createdAt: now,
          updatedAt: now,
          username:
              email.isNotEmpty ? email.split('@').first : firebaseUser.uid,
          email: email,
          firstName: firstName,
          lastName: lastName,
          phoneNumber: firebaseUser.phoneNumber,
          profileImageUrl: firebaseUser.photoURL,
          role: UserRole.sewingOperator,
          department: Department.sewing,
          status: firebaseUser.emailVerified
              ? CommonStatus.active
              : CommonStatus.pending,
          isActive: true,
          isEmailVerified: firebaseUser.emailVerified,
          isPhoneVerified: firebaseUser.phoneNumber != null,
          permissions: const [],
        );
        
        return Right(ApiResponse.success(data: domainUser));
      } else {
        return const Left(AuthFailure('No authenticated user'));
      }
    } catch (e) {
      return Left(UnknownFailure('Failed to get current user: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> logout() async {
    try {
      await _firebaseAuthService.signOut();
      return Right(ApiVoidResponse.success());
    } catch (e) {
      return Left(UnknownFailure('Logout failed: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> requestPasswordReset(
    PasswordResetRequest request,
  ) async {
    try {
      await _firebaseAuthService.sendPasswordResetEmail(request.email);
      return Right(ApiVoidResponse.success(
        message: 'Password reset email sent successfully',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to send password reset email: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> changePassword(
    ChangePasswordRequest request,
  ) async {
    try {
      final firebaseUser = _firebaseAuthService.currentFirebaseUser;
      
      if (firebaseUser != null) {
        // Re-authenticate user first
        final credential = firebase_auth.EmailAuthProvider.credential(
          email: firebaseUser.email!,
          password: request.currentPassword,
        );
        
        await firebaseUser.reauthenticateWithCredential(credential);
        
        // Update password
        await firebaseUser.updatePassword(request.newPassword);
        
        return Right(ApiVoidResponse.success(
          message: 'Password changed successfully',
        ));
      } else {
        return const Left(AuthFailure('No authenticated user'));
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Password change failed: $e'));
    }
  }


  @override
  Future<Either<Failure, ApiVoidResponse>> confirmPasswordReset(
    PasswordResetConfirmRequest confirmation,
  ) async {
    try {
      await _firebaseAuthService.confirmPasswordReset(
        code: confirmation.token,
        newPassword: confirmation.newPassword,
      );
      return Right(ApiVoidResponse.success(
        message: 'Password has been reset successfully',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to confirm password reset: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<String>>> enableTwoFactorAuth() async {
    try {
      final secret = await _firebaseAuthService.enableTwoFactorAuth();
      return Right(ApiResponse.success(
        data: secret,
        message: 'Two-factor authentication enabled',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
          UnknownFailure('Failed to enable two-factor authentication: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> disableTwoFactorAuth(
    String code,
  ) async {
    try {
      await _firebaseAuthService.disableTwoFactorAuth(code);
      return Right(ApiVoidResponse.success(
        message: 'Two-factor authentication disabled',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
          UnknownFailure('Failed to disable two-factor authentication: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> verifyTwoFactorAuth(
    TwoFactorAuthData data,
  ) async {
    try {
      // In a real app, you would verify the TOTP code using the data.verificationId
      // For this example, we'll pass the code directly to verifyTwoFactorCode
      final isValid = await _firebaseAuthService.verifyTwoFactorCode(data.code);
      
      if (isValid) {
        return Right(ApiVoidResponse.success(
          message: 'Two-factor authentication verified and enabled',
        ));
      } else {
        return const Left(AuthFailure('Invalid verification code'));
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
          UnknownFailure('Failed to verify two-factor authentication: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> authenticateWithBiometrics(
    BiometricAuthData data,
  ) async {
    try {
      final userCredential =
          await _firebaseAuthService.signInWithBiometric(data);
      final idToken = await userCredential.user!.getIdToken();
      
      final tokens = AuthTokens(
        accessToken: idToken ?? '',
        refreshToken: userCredential.user!.refreshToken ?? '',
        tokenType: 'Bearer',
        expiresIn: 3600,
        issuedAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
        scopes: ['firebase'],
      );
      
      return Right(ApiResponse.success(data: tokens));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Biometric authentication failed: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> registerBiometrics(
    BiometricAuthData data,
  ) async {
    try {
      await _firebaseAuthService.registerBiometric(data);
      return Right(ApiVoidResponse.success(
        message: 'Biometric authentication registered',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
          UnknownFailure('Failed to register biometric authentication: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> removeBiometrics(
    String biometricType,
  ) async {
    try {
      await _firebaseAuthService.removeBiometric(biometricType);
      return Right(ApiVoidResponse.success(
        message: 'Biometric authentication removed',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
          UnknownFailure('Failed to remove biometric authentication: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<UserSession>>>
      getUserSessions() async {
    try {
      final sessions = await _firebaseAuthService.getUserSessions();
      return Right(ApiListResponse(
        success: true,
        data: sessions,
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to get user sessions: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> revokeSession(
    String sessionId,
  ) async {
    try {
      await _firebaseAuthService.revokeSession(sessionId);
      return Right(ApiVoidResponse.success(
        message: 'Session revoked',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to revoke session: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> revokeAllOtherSessions() async {
    try {
      await _firebaseAuthService.revokeAllOtherSessions();
      return Right(ApiVoidResponse.success(
        message: 'All other sessions revoked',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to revoke other sessions: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> userExists(String username) async {
    try {
      final result = await _firebaseAuthService.checkIfUserExists(username);
      return Right(result);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to check if user exists: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> validateToken(String token) async {
    try {
      // Use the Firebase Auth instance to verify the ID token
      await _firebaseAuthService.currentFirebaseUser?.getIdToken(true);
      return const Right(true);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to validate token: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getUserPermissions() async {
    try {
      final user = _firebaseAuthService.currentFirebaseUser;
      if (user == null) {
        return const Left(AuthFailure('No authenticated user'));
      }
      
      final permissions =
          await _firebaseAuthService.getUserPermissions(user.uid);
      return Right(permissions);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to get user permissions: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> updateUserPreferences(
    UserPreferences preferences,
  ) async {
    try {
      final user = _firebaseAuthService.currentFirebaseUser;
      if (user == null) {
        return const Left(AuthFailure('No authenticated user'));
      }
      
      await _firebaseAuthService.updateUserPreferences(
          user.uid, preferences as Map<String, dynamic>);
      return Right(ApiVoidResponse.success(
        message: 'Preferences updated',
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to update user preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<UserPreferences>>>
      getUserPreferences() async {
    try {
      final user = _firebaseAuthService.currentFirebaseUser;
      if (user == null) {
        return const Left(AuthFailure('No authenticated user'));
      }
      
      // Get the preferences map from Firestore
      final preferencesMap =
          await _firebaseAuthService.getUserPreferences(user.uid);
      
      // Convert the map to a UserPreferencesModel and then to the entity
      final preferences =
          UserPreferencesModel.fromJson(preferencesMap).toEntity();
      
      return Right(ApiResponse.success(data: preferences));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(UnknownFailure('Failed to get user preferences: $e'));
    }
  }

  /// Map Firebase Auth exceptions to app failures
  Failure _mapFirebaseAuthException(firebase_auth.FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return const AuthFailure('No user found with this email address');
      case 'wrong-password':
        return const AuthFailure('Incorrect password');
      case 'email-already-in-use':
        return const ValidationFailure(
            'An account already exists with this email address');
      case 'weak-password':
        return const ValidationFailure('Password is too weak');
      case 'invalid-email':
        return const ValidationFailure('Invalid email address');
      case 'user-disabled':
        return const AuthFailure('This account has been disabled');
      case 'too-many-requests':
        return const NetworkFailure('Too many requests. Please try again later');
      case 'network-request-failed':
        return const NetworkFailure('Network error. Please check your connection');
      default:
        return AuthFailure('Authentication failed: ${e.message}');
    }
  }
}
