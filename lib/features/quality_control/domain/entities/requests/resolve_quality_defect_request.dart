import 'package:equatable/equatable.dart';

class ResolveQualityDefectRequest extends Equatable {
  final String resolutionNotes;
  final List<String>? attachmentUrls;
  final String? resolvedBy;
  final DateTime? resolvedAt;

  const ResolveQualityDefectRequest({
    required this.resolutionNotes,
    this.attachmentUrls,
    this.resolvedBy,
    this.resolvedAt,
  });

  Map<String, dynamic> toJson() => {
        'resolutionNotes': resolutionNotes,
        'attachmentUrls': attachmentUrls,
        'resolvedBy': resolvedBy,
        'resolvedAt': resolvedAt?.toIso8601String(),
      };

  @override
  List<Object?> get props => [
        resolutionNotes,
        attachmentUrls,
        resolvedBy,
        resolvedAt,
      ];
}
