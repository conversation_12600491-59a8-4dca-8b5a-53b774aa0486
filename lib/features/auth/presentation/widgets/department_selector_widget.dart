import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';

/// Department selector widget for manufacturing departments
class DepartmentSelectorWidget extends StatefulWidget {
  final Department? selectedDepartment;
  final Function(Department) onDepartmentSelected;

  const DepartmentSelectorWidget({
    super.key,
    this.selectedDepartment,
    required this.onDepartmentSelected,
  });

  @override
  State<DepartmentSelectorWidget> createState() => _DepartmentSelectorWidgetState();
}

class _DepartmentSelectorWidgetState extends State<DepartmentSelectorWidget> {
  bool _isExpanded = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  IconData _getIconForDepartment(String iconName) {
    switch (iconName) {
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'content_cut':
        return Icons.content_cut;
      case 'design_services':
        return Icons.design_services;
      case 'verified':
        return Icons.verified;
      case 'check_circle':
        return Icons.check_circle;
      case 'warehouse':
        return Icons.warehouse;
      case 'admin_panel_settings':
        return Icons.admin_panel_settings;
      default:
        return Icons.business;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Department Selection Field
        InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.textSecondary.withAlpha(77)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.business,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Department *',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        widget.selectedDepartment?.displayName ?? 'Select your department',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: widget.selectedDepartment != null
                              ? AppColors.textPrimary
                              : AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  _isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
        
        // Department List
        if (_isExpanded) ...[
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.textSecondary.withAlpha(77)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Search Field
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search departments...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value.toLowerCase();
                      });
                    },
                  ),
                ),
                
                // Department Grid
                SizedBox(
                  height: 300,
                  child: _buildDepartmentGrid(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDepartmentGrid() {
    final filteredDepartments = Department.values.where((dept) {
      if (_searchQuery.isEmpty) return true;
      return dept.displayName.toLowerCase().contains(_searchQuery) ||
             dept.description.toLowerCase().contains(_searchQuery);
    }).toList();

    // Group departments by category
    final categories = _groupDepartmentsByCategory(filteredDepartments);

    return ListView.builder(
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories.keys.elementAt(index);
        final departments = categories[category]!;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category Header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              child: Text(
                category,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ),
            
            // Department Cards
            ...departments.map((dept) => _buildDepartmentCard(dept)),
            
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildDepartmentCard(Department department) {
    final isSelected = widget.selectedDepartment == department;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          widget.onDepartmentSelected(department);
          setState(() {
            _isExpanded = false;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected 
                ? AppColors.primary.withAlpha(26)
                : AppColors.surface,
            border: Border.all(
              color: isSelected 
                  ? AppColors.primary
                  : AppColors.textSecondary.withAlpha(51),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              // Department Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppColors.primary.withAlpha(51)
                      : AppColors.textSecondary.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Icon(
                    _getIconForDepartment(department.iconName),
                    size: 20,
                    color: isSelected ? AppColors.primary : AppColors.textSecondary,
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Department Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      department.displayName,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.primary : AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      department.description,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              
              // Selection Indicator
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: AppColors.primary,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, List<Department>> _groupDepartmentsByCategory(
    List<Department> departments,
  ) {
    final Map<String, List<Department>> categories = {
      'Preparation & Cutting': [],
      'Stitching & Assembly': [],
      'Finishing & Quality': [],
      'Logistics & Merchandising': [],
      'Administration': [],
    };

    for (final dept in departments) {
      switch (dept) {
        case Department.cutting:
          categories['Preparation & Cutting']!.add(dept);
          break;
        case Department.sewing:
          categories['Stitching & Assembly']!.add(dept);
          break;
        case Department.finishing:
        case Department.quality:
          categories['Finishing & Quality']!.add(dept);
          break;
        case Department.warehouse:
        case Department.merchandising:
          categories['Logistics & Merchandising']!.add(dept);
          break;
        case Department.administration:
          categories['Administration']!.add(dept);
          break;
      }
    }

    // Remove empty categories
    categories.removeWhere((key, value) => value.isEmpty);
    
    return categories;
  }
}
