import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/facility_entities.dart';
import '../../domain/entities/machine_entities.dart';
import '../../domain/entities/worker_entities.dart';
import '../../domain/repositories/resource_repository.dart';
import '../../domain/usecases/resource_params.dart';
import '../../domain/usecases/resource_usecases.dart';

part 'resource_event.dart';
part 'resource_state.dart';

/// Resource Management Bloc
@injectable
class ResourceBloc extends Bloc<ResourceEvent, ResourceState> {
  final GetMachinesUseCase _getMachinesUseCase;
  final GetMachineByIdUseCase _getMachineByIdUseCase;
  final CreateMachineUseCase _createMachineUseCase;
  final UpdateMachineUseCase _updateMachineUseCase;
  final UpdateMachineStatusUseCase _updateMachineStatusUseCase;
  final GetMachineSchedulesUseCase _getMachineSchedulesUseCase;
  final CreateMachineScheduleUseCase _createMachineScheduleUseCase;
  final GetMachineMaintenanceRecordsUseCase _getMachineMaintenanceRecordsUseCase;
  final CreateMaintenanceRecordUseCase _createMaintenanceRecordUseCase;
  final CompleteMaintenanceUseCase _completeMaintenanceUseCase;
  final GetAvailableMachinesUseCase _getAvailableMachinesUseCase;
  final GetMachinesDueForMaintenanceUseCase _getMachinesDueForMaintenanceUseCase;
  final GetMachineUtilizationReportUseCase _getMachineUtilizationReportUseCase;
  final SearchMachinesUseCase _searchMachinesUseCase;
  final GetWorkersUseCase _getWorkersUseCase;
  final GetWorkerByIdUseCase _getWorkerByIdUseCase;
  final CreateWorkerUseCase _createWorkerUseCase;
  final UpdateWorkerUseCase _updateWorkerUseCase;
  final UpdateWorkerStatusUseCase _updateWorkerStatusUseCase;
  final GetWorkerSchedulesUseCase _getWorkerSchedulesUseCase;
  final CreateWorkerScheduleUseCase _createWorkerScheduleUseCase;
  final GetWorkerAttendanceUseCase _getWorkerAttendanceUseCase;
  final RecordAttendanceUseCase _recordAttendanceUseCase;
  final GetWorkerLeaveRequestsUseCase _getWorkerLeaveRequestsUseCase;
  final CreateLeaveRequestUseCase _createLeaveRequestUseCase;
  final ApproveLeaveRequestUseCase _approveLeaveRequestUseCase;
  final GetAvailableWorkersUseCase _getAvailableWorkersUseCase;
  final GetWorkerPerformanceReportUseCase _getWorkerPerformanceReportUseCase;
  final SearchWorkersUseCase _searchWorkersUseCase;
  final GetFacilitiesUseCase _getFacilitiesUseCase;
  final GetFacilityByIdUseCase _getFacilityByIdUseCase;
  final CreateFacilityUseCase _createFacilityUseCase;
  final GetDepartmentsUseCase _getDepartmentsUseCase;
  final CreateDepartmentUseCase _createDepartmentUseCase;
  final GetFacilityCapacityReportUseCase _getFacilityCapacityReportUseCase;
  final GetFacilityUtilizationReportUseCase _getFacilityUtilizationReportUseCase;

  ResourceBloc(
    this._getMachinesUseCase,
    this._getMachineByIdUseCase,
    this._createMachineUseCase,
    this._updateMachineUseCase,
    this._updateMachineStatusUseCase,
    this._getMachineSchedulesUseCase,
    this._createMachineScheduleUseCase,
    this._getMachineMaintenanceRecordsUseCase,
    this._createMaintenanceRecordUseCase,
    this._completeMaintenanceUseCase,
    this._getAvailableMachinesUseCase,
    this._getMachinesDueForMaintenanceUseCase,
    this._getMachineUtilizationReportUseCase,
    this._searchMachinesUseCase,
    this._getWorkersUseCase,
    this._getWorkerByIdUseCase,
    this._createWorkerUseCase,
    this._updateWorkerUseCase,
    this._updateWorkerStatusUseCase,
    this._getWorkerSchedulesUseCase,
    this._createWorkerScheduleUseCase,
    this._getWorkerAttendanceUseCase,
    this._recordAttendanceUseCase,
    this._getWorkerLeaveRequestsUseCase,
    this._createLeaveRequestUseCase,
    this._approveLeaveRequestUseCase,
    this._getAvailableWorkersUseCase,
    this._getWorkerPerformanceReportUseCase,
    this._searchWorkersUseCase,
    this._getFacilitiesUseCase,
    this._getFacilityByIdUseCase,
    this._createFacilityUseCase,
    this._getDepartmentsUseCase,
    this._createDepartmentUseCase,
    this._getFacilityCapacityReportUseCase,
    this._getFacilityUtilizationReportUseCase,
  ) : super(const ResourceInitial()) {
    // Machine events
    on<LoadMachinesRequested>(_onLoadMachinesRequested);
    on<RefreshMachinesRequested>(_onRefreshMachinesRequested);
    on<LoadMachineDetailsRequested>(_onLoadMachineDetailsRequested);
    on<CreateMachineRequested>(_onCreateMachineRequested);
    on<UpdateMachineRequested>(_onUpdateMachineRequested);
    on<UpdateMachineStatusRequested>(_onUpdateMachineStatusRequested);
    on<LoadMachineSchedulesRequested>(_onLoadMachineSchedulesRequested);
    on<CreateMachineScheduleRequested>(_onCreateMachineScheduleRequested);
    on<LoadMachineMaintenanceRecordsRequested>(
        _onLoadMachineMaintenanceRecordsRequested);
    on<CreateMaintenanceRecordRequested>(_onCreateMaintenanceRecordRequested);
    on<CompleteMaintenanceRequested>(_onCompleteMaintenanceRequested);
    on<LoadAvailableMachinesRequested>(_onLoadAvailableMachinesRequested);
    on<LoadMachinesDueForMaintenanceRequested>(
        _onLoadMachinesDueForMaintenanceRequested);
    on<LoadMachineUtilizationReportRequested>(
        _onLoadMachineUtilizationReportRequested);
    on<SearchMachinesRequested>(_onSearchMachinesRequested);

    // Worker events
    on<LoadWorkersRequested>(_onLoadWorkersRequested);
    on<RefreshWorkersRequested>(_onRefreshWorkersRequested);
    on<LoadWorkerDetailsRequested>(_onLoadWorkerDetailsRequested);
    on<CreateWorkerRequested>(_onCreateWorkerRequested);
    on<UpdateWorkerRequested>(_onUpdateWorkerRequested);
    on<UpdateWorkerStatusRequested>(_onUpdateWorkerStatusRequested);
    on<LoadWorkerSchedulesRequested>(_onLoadWorkerSchedulesRequested);
    on<CreateWorkerScheduleRequested>(_onCreateWorkerScheduleRequested);
    on<LoadWorkerAttendanceRequested>(_onLoadWorkerAttendanceRequested);
    on<RecordAttendanceRequested>(_onRecordAttendanceRequested);
    on<LoadWorkerLeaveRequestsRequested>(_onLoadWorkerLeaveRequestsRequested);
    on<CreateLeaveRequestRequested>(_onCreateLeaveRequestRequested);
    on<ApproveLeaveRequestRequested>(_onApproveLeaveRequestRequested);
    on<LoadAvailableWorkersRequested>(_onLoadAvailableWorkersRequested);
    on<LoadWorkerPerformanceReportRequested>(_onLoadWorkerPerformanceReportRequested);
    on<SearchWorkersRequested>(_onSearchWorkersRequested);

    // Facility events
    on<LoadFacilitiesRequested>(_onLoadFacilitiesRequested);
    on<LoadFacilityDetailsRequested>(_onLoadFacilityDetailsRequested);
    on<CreateFacilityRequested>(_onCreateFacilityRequested);
    on<LoadDepartmentsRequested>(_onLoadDepartmentsRequested);
    on<CreateDepartmentRequested>(_onCreateDepartmentRequested);
    on<LoadFacilityCapacityReportRequested>(_onLoadFacilityCapacityReportRequested);
    on<LoadFacilityUtilizationReportRequested>(
        _onLoadFacilityUtilizationReportRequested);

    // General events
    on<FilterResourcesRequested>(_onFilterResourcesRequested);
    on<ClearResourceState>(_onClearResourceState);
  }

  // Machine event handlers
  Future<void> _onLoadMachinesRequested(
    LoadMachinesRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getMachinesUseCase(GetMachinesParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(MachinesLoaded(
        machines: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onRefreshMachinesRequested(
    RefreshMachinesRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final currentState = state;
    if (currentState is MachinesLoaded) {
      emit(currentState.copyWith(isRefreshing: true));

      final result = await _getMachinesUseCase(GetMachinesParams(
        filter: currentState.filter,
        pagination: const PaginationParams(page: 1, perPage: 20),
      ));

      result.fold(
        (failure) => emit(currentState.copyWith(isRefreshing: false)),
        (response) => emit(currentState.copyWith(
          machines: response.data ?? [],
          pagination: response.pagination,
          isRefreshing: false,
        )),
      );
    }
  }

  Future<void> _onLoadMachineDetailsRequested(
    LoadMachineDetailsRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getMachineByIdUseCase(IdParams(event.machineId));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(MachineDetailsLoaded(response.data!));
        } else {
          emit(const ResourceError('Machine not found'));
        }
      },
    );
  }

  Future<void> _onCreateMachineRequested(
    CreateMachineRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _createMachineUseCase(event.request);

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(MachineCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create machine'));
        }
      },
    );
  }

  Future<void> _onUpdateMachineRequested(
    UpdateMachineRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _updateMachineUseCase(event.request);

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(MachineUpdated(response.data!));
        } else {
          emit(const ResourceError('Failed to update machine'));
        }
      },
    );
  }

  Future<void> _onUpdateMachineStatusRequested(
    UpdateMachineStatusRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _updateMachineStatusUseCase(UpdateMachineStatusParams(
      event.machineId,
      event.status,
      event.reason,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(MachineStatusUpdated(response.data!));
        } else {
          emit(const ResourceError('Failed to update machine status'));
        }
      },
    );
  }

  Future<void> _onLoadMachineSchedulesRequested(
    LoadMachineSchedulesRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getMachineSchedulesUseCase(GetMachineSchedulesParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(MachineSchedulesLoaded(
        schedules: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onCreateMachineScheduleRequested(
    CreateMachineScheduleRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _createMachineScheduleUseCase(event.request);

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(MachineScheduleCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create machine schedule'));
        }
      },
    );
  }

  Future<void> _onLoadMachineMaintenanceRecordsRequested(
    LoadMachineMaintenanceRecordsRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getMachineMaintenanceRecordsUseCase(
        GetMachineMaintenanceRecordsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(MachineMaintenanceRecordsLoaded(
        maintenanceRecords: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onCreateMaintenanceRecordRequested(
    CreateMaintenanceRecordRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _createMaintenanceRecordUseCase(event.request);

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(MaintenanceRecordCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create maintenance record'));
        }
      },
    );
  }

  Future<void> _onCompleteMaintenanceRequested(
    CompleteMaintenanceRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _completeMaintenanceUseCase(CompleteMaintenanceParams(
      event.maintenanceId,
      event.request,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(MaintenanceCompleted(response.data!));
        } else {
          emit(const ResourceError('Failed to complete maintenance'));
        }
      },
    );
  }

  Future<void> _onLoadAvailableMachinesRequested(
    LoadAvailableMachinesRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getAvailableMachinesUseCase(GetAvailableMachinesParams(
      startTime: event.startTime ?? DateTime.now(),
      endTime: event.endTime ?? DateTime.now().add(const Duration(days: 1)),
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(AvailableMachinesLoaded(
        machines: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadMachinesDueForMaintenanceRequested(
    LoadMachinesDueForMaintenanceRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getMachinesDueForMaintenanceUseCase(
        GetMachinesDueForMaintenanceParams(
      daysAhead: event.daysAhead,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(MachinesDueForMaintenanceLoaded(
        machines: response.data ?? [],
        pagination: response.pagination,
        daysAhead: event.daysAhead,
      )),
    );
  }

  Future<void> _onLoadMachineUtilizationReportRequested(
    LoadMachineUtilizationReportRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final endDate = event.endDate ?? DateTime.now();
    final startDate = event.startDate ?? endDate.subtract(const Duration(days: 30));

    final result = await _getMachineUtilizationReportUseCase(
        GetMachineUtilizationReportParams(
      startDate: startDate,
      endDate: endDate,
      departmentId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (report) => emit(MachineUtilizationReportLoaded(report)),
    );
  }

  Future<void> _onSearchMachinesRequested(
    SearchMachinesRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _searchMachinesUseCase(SearchMachinesParams(
      event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(MachinesSearched(
        machines: response.data ?? [],
        pagination: response.pagination,
        query: event.query,
        filter: event.filter,
      )),
    );
  }

  // Worker event handlers
  Future<void> _onLoadWorkersRequested(
    LoadWorkersRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getWorkersUseCase(GetWorkersParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(WorkersLoaded(
        workers: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onRefreshWorkersRequested(
    RefreshWorkersRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final currentState = state;
    if (currentState is WorkersLoaded) {
      emit(currentState.copyWith(isRefreshing: true));

      final result = await _getWorkersUseCase(GetWorkersParams(
        filter: currentState.filter,
        pagination: const PaginationParams(page: 1, perPage: 20),
      ));

      result.fold(
        (failure) => emit(currentState.copyWith(isRefreshing: false)),
        (response) => emit(currentState.copyWith(
          workers: response.data ?? [],
          pagination: response.pagination,
          isRefreshing: false,
        )),
      );
    }
  }

  Future<void> _onLoadWorkerDetailsRequested(
    LoadWorkerDetailsRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _getWorkerByIdUseCase(IdParams(event.workerId));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerDetailsLoaded(response.data!));
        } else {
          emit(const ResourceError('Worker not found'));
        }
      },
    );
  }

  Future<void> _onCreateWorkerRequested(
    CreateWorkerRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _createWorkerUseCase(event.request);

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create worker'));
        }
      },
    );
  }

  Future<void> _onUpdateWorkerRequested(
    UpdateWorkerRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());

    final result = await _updateWorkerUseCase(event.request);

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerUpdated(response.data!));
        } else {
          emit(const ResourceError('Failed to update worker'));
        }
      },
    );
  }

  Future<void> _onUpdateWorkerStatusRequested(
    UpdateWorkerStatusRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _updateWorkerStatusUseCase(UpdateWorkerStatusParams(
      event.workerId,
      event.status,
      event.reason,
    ));

    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerStatusUpdated(response.data!));
        } else {
          emit(const ResourceError('Failed to update worker status'));
        }
      },
    );
  }

  Future<void> _onLoadWorkerSchedulesRequested(
    LoadWorkerSchedulesRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _getWorkerSchedulesUseCase(GetWorkerSchedulesParams(
      filter: event.filter,
      pagination: event.pagination,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(WorkerSchedulesLoaded(
        schedules: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onCreateWorkerScheduleRequested(
    CreateWorkerScheduleRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _createWorkerScheduleUseCase(event.request);
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerScheduleCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create worker schedule'));
        }
      },
    );
  }

  Future<void> _onLoadWorkerAttendanceRequested(
    LoadWorkerAttendanceRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _getWorkerAttendanceUseCase(GetWorkerAttendanceParams(
      filter: event.filter,
      pagination: event.pagination,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(WorkerAttendanceLoaded(
        attendance: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onRecordAttendanceRequested(
    RecordAttendanceRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _recordAttendanceUseCase(event.request);
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerAttendanceRecorded(response.data!));
        } else {
          emit(const ResourceError('Failed to record attendance'));
        }
      },
    );
  }

  Future<void> _onLoadWorkerLeaveRequestsRequested(
    LoadWorkerLeaveRequestsRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _getWorkerLeaveRequestsUseCase(GetWorkerLeaveRequestsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(WorkerLeaveRequestsLoaded(
        leaveRequests: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onCreateLeaveRequestRequested(
    CreateLeaveRequestRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _createLeaveRequestUseCase(event.request);
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerLeaveCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create leave request'));
        }
      },
    );
  }

  Future<void> _onApproveLeaveRequestRequested(
    ApproveLeaveRequestRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _approveLeaveRequestUseCase(ApproveLeaveRequestParams(
      leaveRequestId: event.leaveRequestId,
      approverId: event.approverId,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(WorkerLeaveApproved(response.data!));
        } else {
          emit(const ResourceError('Failed to approve leave request'));
        }
      },
    );
  }

  Future<void> _onLoadAvailableWorkersRequested(
    LoadAvailableWorkersRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _getAvailableWorkersUseCase(GetAvailableWorkersParams(
      startTime: event.startTime ?? DateTime.now(),
      endTime: event.endTime ?? DateTime.now().add(const Duration(days: 1)),
      pagination: event.pagination,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(AvailableWorkersLoaded(
        workers: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadWorkerPerformanceReportRequested(
    LoadWorkerPerformanceReportRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final endDate = event.endDate ?? DateTime.now();
    final startDate = event.startDate ?? endDate.subtract(const Duration(days: 30));

    final result = await _getWorkerPerformanceReportUseCase(
      GetWorkerPerformanceReportParams(
        startDate: startDate,
        endDate: endDate,
        departmentId: event.departmentId,
      ),
    );
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (report) => emit(WorkerPerformanceReportLoaded(report)),
    );
  }

  Future<void> _onSearchWorkersRequested(
    SearchWorkersRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _searchWorkersUseCase(SearchWorkersParams(
      event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(WorkersSearched(
        workers: response.data ?? [],
        pagination: response.pagination,
        query: event.query,
        filter: event.filter,
      )),
    );
  }

  // Facility event handlers
  Future<void> _onLoadFacilitiesRequested(
    LoadFacilitiesRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _getFacilitiesUseCase(GetFacilitiesParams(
      filter: event.filter,
      pagination: event.pagination,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(FacilitiesLoaded(
        facilities: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onLoadFacilityDetailsRequested(
    LoadFacilityDetailsRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _getFacilityByIdUseCase(IdParams(event.facilityId));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FacilityDetailsLoaded(response.data!));
        } else {
          emit(const ResourceError('Facility not found'));
        }
      },
    );
  }

  Future<void> _onCreateFacilityRequested(
    CreateFacilityRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _createFacilityUseCase(event.request);
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FacilityCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create facility'));
        }
      },
    );
  }

  Future<void> _onLoadDepartmentsRequested(
    LoadDepartmentsRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _getDepartmentsUseCase(GetDepartmentsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) => emit(DepartmentsLoaded(
        departments: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onCreateDepartmentRequested(
    CreateDepartmentRequested event,
    Emitter<ResourceState> emit,
  ) async {
    emit(const ResourceLoading());
    final result = await _createDepartmentUseCase(event.request);
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(DepartmentCreated(response.data!));
        } else {
          emit(const ResourceError('Failed to create department'));
        }
      },
    );
  }

  Future<void> _onLoadFacilityCapacityReportRequested(
    LoadFacilityCapacityReportRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final result = await _getFacilityCapacityReportUseCase(
      GetFacilityCapacityReportParams(
        date: event.asOfDate ?? DateTime.now(),
        departmentId: event.facilityId,
      ),
    );
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (report) => emit(FacilityCapacityReportLoaded(report)),
    );
  }

  Future<void> _onLoadFacilityUtilizationReportRequested(
    LoadFacilityUtilizationReportRequested event,
    Emitter<ResourceState> emit,
  ) async {
    final endDate = event.endDate ?? DateTime.now();
    final startDate = event.startDate ?? endDate.subtract(const Duration(days: 30));

    final result = await _getFacilityUtilizationReportUseCase(
      GetFacilityUtilizationReportParams(
        startDate: startDate,
        endDate: endDate,
        departmentId: event.facilityId,
      ),
    );
    result.fold(
      (failure) => emit(ResourceError(failure.message)),
      (report) => emit(FacilityUtilizationReportLoaded(report)),
    );
  }

  void _onFilterResourcesRequested(
    FilterResourcesRequested event,
    Emitter<ResourceState> emit,
  ) {
    // Handle filtering logic based on resource type
  }

  void _onClearResourceState(
    ClearResourceState event,
    Emitter<ResourceState> emit,
  ) {
    emit(const ResourceInitial());
  }
}
