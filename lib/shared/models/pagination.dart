import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pagination.g.dart';

/// Pagination information for API responses
@JsonSerializable()
class Pagination extends Equatable {
  @Json<PERSON>ey(name: 'current_page')
  final int currentPage;
  
  @Json<PERSON>ey(name: 'per_page')
  final int perPage;
  
  @Json<PERSON>ey(name: 'total')
  final int total;
  
  @JsonKey(name: 'total_pages')
  final int totalPages;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'from')
  final int? from;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'to')
  final int? to;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'has_next_page')
  final bool hasNextPage;
  
  @Json<PERSON>ey(name: 'has_previous_page')
  final bool hasPreviousPage;
  
  @Json<PERSON>ey(name: 'next_page_url')
  final String? nextPageUrl;
  
  @Json<PERSON>ey(name: 'previous_page_url')
  final String? previousPageUrl;

  const Pagination({
    required this.currentPage,
    required this.perPage,
    required this.total,
    required this.totalPages,
    this.from,
    this.to,
    required this.hasNextPage,
    required this.hasPreviousPage,
    this.nextPageUrl,
    this.previousPageUrl,
  });

  /// Factory constructor for JSON deserialization
  factory Pagination.fromJson(Map<String, dynamic> json) =>
      _$PaginationFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$PaginationToJson(this);

  /// Create pagination from basic parameters
  factory Pagination.create({
    required int currentPage,
    required int perPage,
    required int total,
  }) {
    final totalPages = (total / perPage).ceil();
    final from = total > 0 ? ((currentPage - 1) * perPage) + 1 : null;
    final to = total > 0 
        ? (currentPage * perPage > total ? total : currentPage * perPage)
        : null;
    
    return Pagination(
      currentPage: currentPage,
      perPage: perPage,
      total: total,
      totalPages: totalPages,
      from: from,
      to: to,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
    );
  }

  /// Create empty pagination
  factory Pagination.empty() {
    return const Pagination(
      currentPage: 1,
      perPage: 0,
      total: 0,
      totalPages: 0,
      from: null,
      to: null,
      hasNextPage: false,
      hasPreviousPage: false,
    );
  }

  /// Get the next page number
  int? get nextPage => hasNextPage ? currentPage + 1 : null;

  /// Get the previous page number
  int? get previousPage => hasPreviousPage ? currentPage - 1 : null;

  /// Check if this is the first page
  bool get isFirstPage => currentPage == 1;

  /// Check if this is the last page
  bool get isLastPage => currentPage == totalPages;

  /// Get the range of items being displayed
  String get itemRange {
    if (total == 0) return '0 items';
    if (from == null || to == null) return '$total items';
    return '$from-$to of $total items';
  }

  /// Get page information string
  String get pageInfo {
    if (totalPages == 0) return 'Page 0 of 0';
    return 'Page $currentPage of $totalPages';
  }

  /// Calculate offset for database queries
  int get offset => (currentPage - 1) * perPage;

  /// Get list of page numbers for pagination UI
  List<int> getPageNumbers({int maxVisible = 5}) {
    if (totalPages <= maxVisible) {
      return List.generate(totalPages, (index) => index + 1);
    }

    final List<int> pages = [];
    final int half = maxVisible ~/ 2;
    
    int start = currentPage - half;
    int end = currentPage + half;

    if (start < 1) {
      start = 1;
      end = maxVisible;
    }

    if (end > totalPages) {
      end = totalPages;
      start = totalPages - maxVisible + 1;
      if (start < 1) start = 1;
    }

    for (int i = start; i <= end; i++) {
      pages.add(i);
    }

    return pages;
  }

  /// Copy with new values
  Pagination copyWith({
    int? currentPage,
    int? perPage,
    int? total,
    int? totalPages,
    int? from,
    int? to,
    bool? hasNextPage,
    bool? hasPreviousPage,
    String? nextPageUrl,
    String? previousPageUrl,
  }) {
    return Pagination(
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
      totalPages: totalPages ?? this.totalPages,
      from: from ?? this.from,
      to: to ?? this.to,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      hasPreviousPage: hasPreviousPage ?? this.hasPreviousPage,
      nextPageUrl: nextPageUrl ?? this.nextPageUrl,
      previousPageUrl: previousPageUrl ?? this.previousPageUrl,
    );
  }

  @override
  List<Object?> get props => [
        currentPage,
        perPage,
        total,
        totalPages,
        from,
        to,
        hasNextPage,
        hasPreviousPage,
        nextPageUrl,
        previousPageUrl,
      ];

  @override
  String toString() {
    return 'Pagination(currentPage: $currentPage, perPage: $perPage, total: $total, totalPages: $totalPages)';
  }
}

/// Pagination parameters for API requests
@JsonSerializable()
class PaginationParams extends Equatable {
  @JsonKey(name: 'page')
  final int page;
  
  @JsonKey(name: 'per_page')
  final int perPage;
  
  @JsonKey(name: 'sort_by')
  final String? sortBy;
  
  @JsonKey(name: 'sort_order')
  final String? sortOrder;

  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? lastDocumentId;

  @JsonKey(includeFromJson: false, includeToJson: false)
  final dynamic lastDocumentSnapshot;

  const PaginationParams({
    this.page = 1,
    this.perPage = 20,
    this.sortBy,
    this.sortOrder,
    this.lastDocumentId,
    this.lastDocumentSnapshot,
  });

  /// Factory constructor for JSON deserialization
  factory PaginationParams.fromJson(Map<String, dynamic> json) =>
      _$PaginationParamsFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$PaginationParamsToJson(this);

  /// Convert to query parameters
  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = {
      'page': page,
      'per_page': perPage,
    };

    if (sortBy != null) {
      params['sort_by'] = sortBy;
    }

    if (sortOrder != null) {
      params['sort_order'] = sortOrder;
    }

    return params;
  }

  /// Create with next page
  PaginationParams nextPage() {
    return copyWith(page: page + 1);
  }

  /// Create with previous page
  PaginationParams previousPage() {
    return copyWith(page: page > 1 ? page - 1 : 1);
  }

  /// Create with specific page
  PaginationParams withPage(int newPage) {
    return copyWith(page: newPage);
  }

  /// Create with sorting
  PaginationParams withSort(String sortBy, [String sortOrder = 'asc']) {
    return copyWith(sortBy: sortBy, sortOrder: sortOrder);
  }

  /// Copy with new values
  PaginationParams copyWith({
    int? page,
    int? perPage,
    String? sortBy,
    String? sortOrder,
    String? lastDocumentId,
    dynamic lastDocumentSnapshot,
  }) {
    return PaginationParams(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      lastDocumentId: lastDocumentId ?? this.lastDocumentId,
      lastDocumentSnapshot: lastDocumentSnapshot ?? this.lastDocumentSnapshot,
    );
  }

  @override
  List<Object?> get props => [
        page,
        perPage,
        sortBy,
        sortOrder,
        lastDocumentId,
        lastDocumentSnapshot,
      ];

  @override
  String toString() {
    return 'PaginationParams(page: $page, perPage: $perPage, sortBy: $sortBy, sortOrder: $sortOrder, lastDocumentId: $lastDocumentId)';
  }
}

/// Paginated result wrapper for API responses
@JsonSerializable(genericArgumentFactories: true)
class PaginatedResult<T> extends Equatable {
  @JsonKey(name: 'data')
  final List<T> data;

  @JsonKey(name: 'pagination')
  final Pagination pagination;

  @JsonKey(name: 'meta')
  final Map<String, dynamic>? meta;

  const PaginatedResult({
    required this.data,
    required this.pagination,
    this.meta,
  });

  /// Factory constructor for JSON deserialization
  factory PaginatedResult.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedResultFromJson(json, fromJsonT);

  /// Convert to JSON
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedResultToJson(this, toJsonT);

  /// Create empty result
  factory PaginatedResult.empty() {
    return PaginatedResult<T>(
      data: const [],
      pagination: Pagination.empty(),
    );
  }

  /// Create from list and pagination info
  factory PaginatedResult.create({
    required List<T> data,
    required int currentPage,
    required int perPage,
    required int total,
    Map<String, dynamic>? meta,
  }) {
    return PaginatedResult<T>(
      data: data,
      pagination: Pagination.create(
        currentPage: currentPage,
        perPage: perPage,
        total: total,
      ),
      meta: meta,
    );
  }

  /// Check if result is empty
  bool get isEmpty => data.isEmpty;

  /// Check if result is not empty
  bool get isNotEmpty => data.isNotEmpty;

  /// Get the number of items in current page
  int get itemCount => data.length;

  /// Check if there are more pages
  bool get hasMorePages => pagination.hasNextPage;

  /// Check if this is the first page
  bool get isFirstPage => pagination.isFirstPage;

  /// Check if this is the last page
  bool get isLastPage => pagination.isLastPage;

  /// Get total number of items across all pages
  int get totalItems => pagination.total;

  /// Get total number of pages
  int get totalPages => pagination.totalPages;

  /// Copy with new values
  PaginatedResult<T> copyWith({
    List<T>? data,
    Pagination? pagination,
    Map<String, dynamic>? meta,
  }) {
    return PaginatedResult<T>(
      data: data ?? this.data,
      pagination: pagination ?? this.pagination,
      meta: meta ?? this.meta,
    );
  }

  /// Transform the data while keeping pagination
  PaginatedResult<R> map<R>(R Function(T item) transform) {
    return PaginatedResult<R>(
      data: data.map(transform).toList(),
      pagination: pagination,
      meta: meta,
    );
  }

  @override
  List<Object?> get props => [data, pagination, meta];

  @override
  String toString() {
    return 'PaginatedResult(itemCount: $itemCount, totalItems: $totalItems, currentPage: ${pagination.currentPage})';
  }
}
