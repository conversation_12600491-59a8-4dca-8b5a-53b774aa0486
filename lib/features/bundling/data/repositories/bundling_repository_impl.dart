import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/bundle.dart';
import '../../domain/entities/bundle_input.dart';
import '../../domain/entities/bundle_statistics.dart';
import '../../domain/repositories/bundling_repository.dart';
import '../datasources/bundling_local_datasource.dart';
import '../datasources/bundling_remote_datasource.dart';
import '../models/bundle_model.dart';

/// Implementation of BundlingRepository
@injectable
class BundlingRepositoryImpl implements BundlingRepository {
  final BundlingRemoteDataSource _remoteDataSource;
  final BundlingLocalDataSource _localDataSource;
  final NetworkInfo _networkInfo;

  BundlingRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<Bundle>>> getAllBundles({
    BundleStatus? status,
    String? designNo,
    String? lotNo,
    String? rollNo,
    String? altNo,
    GarmentSize? size,
    String? assignedTo,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundles = await _remoteDataSource.getAllBundles(
          status: status,
          designNo: designNo,
          lotNo: lotNo,
          rollNo: rollNo,
          altNo: altNo,
          size: size,
          assignedTo: assignedTo,
          startDate: startDate,
          endDate: endDate,
          limit: limit,
          offset: offset,
        );
        
        // Cache the results
        await _localDataSource.cacheBundles(bundles);
        
        return Right(bundles.map((bundle) => bundle.toEntity()).toList());
      } else {
        // Return cached data when offline
        final cachedBundles = await _localDataSource.getAllBundles();
        var filteredBundles = cachedBundles;
        
        // Apply filters to cached data
        if (status != null) {
          filteredBundles = filteredBundles.where((b) => b.status == status).toList();
        }
        if (assignedTo != null) {
          filteredBundles = filteredBundles.where((b) => b.assignedTo == assignedTo).toList();
        }
        
        return Right(filteredBundles.map((bundle) => bundle.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundles: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> getBundleById(String id) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundle = await _remoteDataSource.getBundleById(id);
        await _localDataSource.cacheBundle(bundle);
        return Right(bundle.toEntity());
      } else {
        final cachedBundle = await _localDataSource.getBundleById(id);
        if (cachedBundle != null) {
          return Right(cachedBundle.toEntity());
        } else {
          return Left(CacheFailure('Bundle not found in cache'));
        }
      }
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundle: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> getBundleByNumber(String bundleNo) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundle = await _remoteDataSource.getBundleByNumber(bundleNo);
        await _localDataSource.cacheBundle(bundle);
        return Right(bundle.toEntity());
      } else {
        final cachedBundle = await _localDataSource.getBundleByNumber(bundleNo);
        if (cachedBundle != null) {
          return Right(cachedBundle.toEntity());
        } else {
          return Left(CacheFailure('Bundle not found in cache'));
        }
      }
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundle by number: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> createBundles(
    BundleInput input,
    String createdBy,
  ) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundles = await _remoteDataSource.createBundles(input, createdBy);
        
        // Cache the created bundles
        for (final bundle in bundles) {
          await _localDataSource.cacheBundle(bundle);
        }
        
        return Right(bundles.map((bundle) => bundle.toEntity()).toList());
      } else {
        // Create bundles offline and store for later sync
        final previews = input.generateBundlePreviews();
        final bundles = <BundleModel>[];
        final now = DateTime.now();
        
        for (final preview in previews) {
          final bundle = BundleModel(
            id: DateTime.now().millisecondsSinceEpoch.toString() + preview.bundleNo,
            bundleNo: preview.bundleNo,
            designNo: input.designNo,
            lotNo: input.lotNo,
            rollNo: input.rollNo,
            altNo: input.altNo,
            size: input.size,
            pieceStartNo: preview.pieceStartNo,
            pieceEndNo: preview.pieceEndNo,
            totalPieces: preview.totalPieces,
            status: BundleStatus.pending,
            supervisorId: createdBy,
            color: input.color,
            orderNo: input.orderNo,
            clientName: input.clientName,
            remarks: input.remarks,
            createdAt: now,
            updatedAt: now,
            createdBy: createdBy,
            updatedBy: createdBy,
          );
          
          bundles.add(bundle);
          await _localDataSource.storeOfflineBundle(bundle);
          await _localDataSource.cacheBundle(bundle);
        }
        
        return Right(bundles.map((bundle) => bundle.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to create bundles: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> updateBundle(Bundle bundle) async {
    try {
      final bundleModel = BundleModel.fromEntity(bundle);
      
      if (await _networkInfo.isConnected) {
        final updatedBundle = await _remoteDataSource.updateBundle(bundleModel);
        await _localDataSource.cacheBundle(updatedBundle);
        return Right(updatedBundle.toEntity());
      } else {
        // Update locally and store for sync
        final updatedBundle = bundleModel.copyWith(
          updatedAt: DateTime.now(),
          version: bundleModel.version + 1,
        );
        
        await _localDataSource.storeOfflineBundle(updatedBundle);
        await _localDataSource.cacheBundle(updatedBundle);
        return Right(updatedBundle.toEntity());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to update bundle: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteBundle(String id, String deletedBy) async {
    try {
      if (await _networkInfo.isConnected) {
        await _remoteDataSource.deleteBundle(id, deletedBy);
        await _localDataSource.removeBundleFromCache(id);
        return const Right(null);
      } else {
        // Mark for deletion offline
        final cachedBundle = await _localDataSource.getBundleById(id);
        if (cachedBundle != null) {
          final deletedBundle = cachedBundle.copyWith(
            deletedAt: DateTime.now(),
            deletedBy: deletedBy,
            updatedAt: DateTime.now(),
          );
          
          await _localDataSource.storeOfflineBundle(deletedBundle);
          await _localDataSource.removeBundleFromCache(id);
        }
        return const Right(null);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to delete bundle: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> issueBundleToSewing(
    String bundleId,
    String assignedTo,
    String issuedBy,
  ) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundle = await _remoteDataSource.issueBundleToSewing(
          bundleId,
          assignedTo,
          issuedBy,
        );
        await _localDataSource.cacheBundle(bundle);
        return Right(bundle.toEntity());
      } else {
        // Update locally and store for sync
        final cachedBundle = await _localDataSource.getBundleById(bundleId);
        if (cachedBundle != null) {
          final updatedBundle = cachedBundle.copyWith(
            status: BundleStatus.issuedToSewing,
            assignedTo: assignedTo,
            issuedAt: DateTime.now(),
            updatedAt: DateTime.now(),
            updatedBy: issuedBy,
          );
          
          await _localDataSource.storeOfflineBundle(updatedBundle);
          await _localDataSource.cacheBundle(updatedBundle);
          return Right(updatedBundle.toEntity());
        } else {
          return Left(CacheFailure('Bundle not found in cache'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to issue bundle to sewing: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> startBundleWork(
    String bundleId,
    String startedBy,
  ) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundle = await _remoteDataSource.startBundleWork(bundleId, startedBy);
        await _localDataSource.cacheBundle(bundle);
        return Right(bundle.toEntity());
      } else {
        // Update locally and store for sync
        final cachedBundle = await _localDataSource.getBundleById(bundleId);
        if (cachedBundle != null) {
          final updatedBundle = cachedBundle.copyWith(
            status: BundleStatus.inProgress,
            startedAt: DateTime.now(),
            updatedAt: DateTime.now(),
            updatedBy: startedBy,
          );
          
          await _localDataSource.storeOfflineBundle(updatedBundle);
          await _localDataSource.cacheBundle(updatedBundle);
          return Right(updatedBundle.toEntity());
        } else {
          return Left(CacheFailure('Bundle not found in cache'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to start bundle work: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> completeBundleWork(
    String bundleId,
    String completedBy,
    String? remarks,
  ) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundle = await _remoteDataSource.completeBundleWork(
          bundleId,
          completedBy,
          remarks,
        );
        await _localDataSource.cacheBundle(bundle);
        return Right(bundle.toEntity());
      } else {
        // Update locally and store for sync
        final cachedBundle = await _localDataSource.getBundleById(bundleId);
        if (cachedBundle != null) {
          final updatedBundle = cachedBundle.copyWith(
            status: BundleStatus.completed,
            completedAt: DateTime.now(),
            updatedAt: DateTime.now(),
            updatedBy: completedBy,
            remarks: remarks ?? cachedBundle.remarks,
          );
          
          await _localDataSource.storeOfflineBundle(updatedBundle);
          await _localDataSource.cacheBundle(updatedBundle);
          return Right(updatedBundle.toEntity());
        } else {
          return Left(CacheFailure('Bundle not found in cache'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to complete bundle work: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> cancelBundle(
    String bundleId,
    String cancelledBy,
    String reason,
  ) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundle = await _remoteDataSource.cancelBundle(
          bundleId,
          cancelledBy,
          reason,
        );
        await _localDataSource.cacheBundle(bundle);
        return Right(bundle.toEntity());
      } else {
        // Update locally and store for sync
        final cachedBundle = await _localDataSource.getBundleById(bundleId);
        if (cachedBundle != null) {
          final updatedBundle = cachedBundle.copyWith(
            status: BundleStatus.cancelled,
            updatedAt: DateTime.now(),
            updatedBy: cancelledBy,
            remarks: reason,
          );

          await _localDataSource.storeOfflineBundle(updatedBundle);
          await _localDataSource.cacheBundle(updatedBundle);
          return Right(updatedBundle.toEntity());
        } else {
          return Left(CacheFailure('Bundle not found in cache'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to cancel bundle: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> getBundlesAssignedTo(String userId) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundles = await _remoteDataSource.getBundlesAssignedTo(userId);

        // Cache the results
        for (final bundle in bundles) {
          await _localDataSource.cacheBundle(bundle);
        }

        return Right(bundles.map((bundle) => bundle.toEntity()).toList());
      } else {
        final cachedBundles = await _localDataSource.getBundlesAssignedTo(userId);
        return Right(cachedBundles.map((bundle) => bundle.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get assigned bundles: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> getBundlesByStatus(BundleStatus status) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundles = await _remoteDataSource.getBundlesByStatus(status);

        // Cache the results
        for (final bundle in bundles) {
          await _localDataSource.cacheBundle(bundle);
        }

        return Right(bundles.map((bundle) => bundle.toEntity()).toList());
      } else {
        final cachedBundles = await _localDataSource.getBundlesByStatus(status);
        return Right(cachedBundles.map((bundle) => bundle.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundles by status: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> getBundlesByDesign(String designNo) async {
    try {
      return await getAllBundles(designNo: designNo);
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundles by design: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> getBundlesByLotAndRoll(
    String lotNo,
    String rollNo,
  ) async {
    try {
      return await getAllBundles(lotNo: lotNo, rollNo: rollNo);
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundles by lot and roll: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> searchBundles(String query) async {
    try {
      if (await _networkInfo.isConnected) {
        final bundles = await _remoteDataSource.searchBundles(query);

        // Cache the results
        for (final bundle in bundles) {
          await _localDataSource.cacheBundle(bundle);
        }

        return Right(bundles.map((bundle) => bundle.toEntity()).toList());
      } else {
        final cachedBundles = await _localDataSource.searchBundles(query);
        return Right(cachedBundles.map((bundle) => bundle.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to search bundles: $e'));
    }
  }

  @override
  Future<Either<Failure, BundleStatistics>> getBundleStatistics({
    DateTime? fromDate,
    DateTime? toDate,
    String? designNo,
    GarmentSize? size,
    BundleStatus? status,
  }) async {
    try {
      // Get bundles based on filters
      final bundlesResult = await getAllBundles(
        designNo: designNo,
        size: size,
        status: status,
        startDate: fromDate,
        endDate: toDate,
      );

      return bundlesResult.fold(
        (failure) => Left(failure),
        (bundles) {
          // Calculate statistics
          final statistics = _calculateStatistics(bundles, fromDate, toDate);
          return Right(statistics);
        },
      );
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundle statistics: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> getBundlesForExport({
    List<String>? bundleIds,
    BundleStatus? status,
    String? designNo,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      if (bundleIds != null && bundleIds.isNotEmpty) {
        // Get specific bundles by IDs
        final bundles = <Bundle>[];
        for (final id in bundleIds) {
          final result = await getBundleById(id);
          result.fold(
            (failure) => null, // Skip failed bundles
            (bundle) => bundles.add(bundle),
          );
        }
        return Right(bundles);
      } else {
        // Get bundles by filters
        return await getAllBundles(
          status: status,
          designNo: designNo,
          startDate: fromDate,
          endDate: toDate,
        );
      }
    } catch (e) {
      return Left(UnknownFailure('Failed to get bundles for export: $e'));
    }
  }

  @override
  Future<Either<Failure, Bundle>> mergeBundles(
    List<String> bundleIds,
    String mergedBy,
    String? remarks,
  ) async {
    try {
      // This is a complex operation that would need business logic
      // For now, return a simple implementation
      return Left(UnknownFailure('Merge bundles not implemented yet'));
    } catch (e) {
      return Left(UnknownFailure('Failed to merge bundles: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Bundle>>> splitBundle(
    String bundleId,
    List<int> splitPoints,
    String splitBy,
    String? remarks,
  ) async {
    try {
      // This is a complex operation that would need business logic
      // For now, return a simple implementation
      return Left(UnknownFailure('Split bundle not implemented yet'));
    } catch (e) {
      return Left(UnknownFailure('Failed to split bundle: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> getNextBundleNumber() async {
    try {
      if (await _networkInfo.isConnected) {
        final bundleNumber = await _remoteDataSource.getNextBundleNumber();
        return Right(bundleNumber);
      } else {
        // Generate offline bundle number
        final counter = await _localDataSource.getBundleCounter();
        final nextCounter = counter + 1;
        await _localDataSource.updateBundleCounter(nextCounter);
        return Right('B${nextCounter.toString().padLeft(3, '0')}');
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get next bundle number: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> validateBundleInput(BundleInput input) async {
    try {
      // Validate input data
      if (!input.isValid) {
        return const Right(false);
      }

      // Check if bundle number would conflict (if we can)
      if (await _networkInfo.isConnected) {
        // Additional server-side validations could go here
      }

      return const Right(true);
    } catch (e) {
      return Left(UnknownFailure('Failed to validate bundle input: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> bundleNumberExists(String bundleNo) async {
    try {
      if (await _networkInfo.isConnected) {
        final exists = await _remoteDataSource.bundleNumberExists(bundleNo);
        return Right(exists);
      } else {
        final exists = await _localDataSource.bundleNumberExistsInCache(bundleNo);
        return Right(exists);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to check bundle number existence: $e'));
    }
  }

  @override
  Stream<Either<Failure, List<Bundle>>> getBundlesStream({
    BundleStatus? status,
    String? assignedTo,
  }) {
    try {
      return _remoteDataSource
          .getBundlesStream(status: status, assignedTo: assignedTo)
          .map((bundles) => Right(bundles.map((bundle) => bundle.toEntity()).toList()))
          .handleError((error) => Left(ServerFailure('Stream error: $error')));
    } catch (e) {
      return Stream.value(Left(UnknownFailure('Failed to get bundles stream: $e')));
    }
  }

  @override
  Stream<Either<Failure, BundleStatistics>> getBundleStatisticsStream() {
    try {
      return _remoteDataSource
          .getBundlesStream()
          .map((bundles) {
            final bundleEntities = bundles.map((bundle) => bundle.toEntity()).toList();
            final statistics = _calculateStatistics(bundleEntities, null, null);
            return Right(statistics);
          })
          .handleError((error) => Left(ServerFailure('Statistics stream error: $error')));
    } catch (e) {
      return Stream.value(Left(UnknownFailure('Failed to get statistics stream: $e')));
    }
  }

  @override
  Future<Either<Failure, void>> syncOfflineBundles() async {
    try {
      if (!await _networkInfo.isConnected) {
        return Left(NetworkFailure('No internet connection'));
      }

      final offlineBundles = await _localDataSource.getOfflineBundles();

      for (final bundle in offlineBundles) {
        try {
          if (bundle.deletedAt != null) {
            // Handle deleted bundles
            await _remoteDataSource.deleteBundle(bundle.id, bundle.deletedBy!);
          } else {
            // Handle created/updated bundles
            await _remoteDataSource.updateBundle(bundle);
          }
        } catch (e) {
          // Log error but continue with other bundles
          print('Failed to sync bundle ${bundle.id}: $e');
        }
      }

      // Clear offline bundles after successful sync
      await _localDataSource.clearOfflineBundles();
      await _localDataSource.updateLastSyncTime(DateTime.now());

      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to sync offline bundles: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getOfflineBundlesCount() async {
    try {
      final count = await _localDataSource.getOfflineBundlesCount();
      return Right(count);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get offline bundles count: $e'));
    }
  }

  /// Helper method to calculate bundle statistics
  BundleStatistics _calculateStatistics(
    List<Bundle> bundles,
    DateTime? fromDate,
    DateTime? toDate,
  ) {
    final designStats = <String, DesignStatistics>{};
    final sizeStats = <GarmentSize, SizeStatistics>{};
    final statusStats = <BundleStatus, StatusStatistics>{};

    int totalBundles = bundles.length;
    int pendingBundles = 0;
    int issuedBundles = 0;
    int inProgressBundles = 0;
    int completedBundles = 0;
    int cancelledBundles = 0;
    int totalPieces = 0;
    int completedPieces = 0;
    int inProgressPieces = 0;
    int pendingPieces = 0;

    for (final bundle in bundles) {
      // Count by status
      switch (bundle.status) {
        case BundleStatus.pending:
          pendingBundles++;
          pendingPieces += bundle.totalPieces;
          break;
        case BundleStatus.issuedToSewing:
          issuedBundles++;
          pendingPieces += bundle.totalPieces;
          break;
        case BundleStatus.inProgress:
          inProgressBundles++;
          inProgressPieces += bundle.totalPieces;
          break;
        case BundleStatus.completed:
          completedBundles++;
          completedPieces += bundle.totalPieces;
          break;
        case BundleStatus.cancelled:
          cancelledBundles++;
          break;
      }

      totalPieces += bundle.totalPieces;

      // Update design stats
      final designKey = bundle.designNo;
      if (designStats.containsKey(designKey)) {
        final existing = designStats[designKey]!;
        final sizeBreakdown = Map<GarmentSize, int>.from(existing.sizeBreakdown);
        sizeBreakdown[bundle.size] = (sizeBreakdown[bundle.size] ?? 0) + bundle.totalPieces;

        designStats[designKey] = DesignStatistics(
          designNo: designKey,
          totalBundles: existing.totalBundles + 1,
          totalPieces: existing.totalPieces + bundle.totalPieces,
          completedBundles: existing.completedBundles + (bundle.status == BundleStatus.completed ? 1 : 0),
          completedPieces: existing.completedPieces + (bundle.status == BundleStatus.completed ? bundle.totalPieces : 0),
          sizeBreakdown: sizeBreakdown,
        );
      } else {
        designStats[designKey] = DesignStatistics(
          designNo: designKey,
          totalBundles: 1,
          totalPieces: bundle.totalPieces,
          completedBundles: bundle.status == BundleStatus.completed ? 1 : 0,
          completedPieces: bundle.status == BundleStatus.completed ? bundle.totalPieces : 0,
          sizeBreakdown: {bundle.size: bundle.totalPieces},
        );
      }

      // Update size stats
      final sizeKey = bundle.size;
      if (sizeStats.containsKey(sizeKey)) {
        final existing = sizeStats[sizeKey]!;
        final designBreakdown = Map<String, int>.from(existing.designBreakdown);
        designBreakdown[bundle.designNo] = (designBreakdown[bundle.designNo] ?? 0) + bundle.totalPieces;

        sizeStats[sizeKey] = SizeStatistics(
          size: sizeKey,
          totalBundles: existing.totalBundles + 1,
          totalPieces: existing.totalPieces + bundle.totalPieces,
          completedBundles: existing.completedBundles + (bundle.status == BundleStatus.completed ? 1 : 0),
          completedPieces: existing.completedPieces + (bundle.status == BundleStatus.completed ? bundle.totalPieces : 0),
          designBreakdown: designBreakdown,
        );
      } else {
        sizeStats[sizeKey] = SizeStatistics(
          size: sizeKey,
          totalBundles: 1,
          totalPieces: bundle.totalPieces,
          completedBundles: bundle.status == BundleStatus.completed ? 1 : 0,
          completedPieces: bundle.status == BundleStatus.completed ? bundle.totalPieces : 0,
          designBreakdown: {bundle.designNo: bundle.totalPieces},
        );
      }
    }

    // Calculate status stats
    for (final status in BundleStatus.values) {
      int bundleCount = 0;
      int pieceCount = 0;

      switch (status) {
        case BundleStatus.pending:
          bundleCount = pendingBundles;
          pieceCount = pendingPieces;
          break;
        case BundleStatus.issuedToSewing:
          bundleCount = issuedBundles;
          pieceCount = 0; // These are counted in pending pieces
          break;
        case BundleStatus.inProgress:
          bundleCount = inProgressBundles;
          pieceCount = inProgressPieces;
          break;
        case BundleStatus.completed:
          bundleCount = completedBundles;
          pieceCount = completedPieces;
          break;
        case BundleStatus.cancelled:
          bundleCount = cancelledBundles;
          pieceCount = 0;
          break;
      }

      final percentage = totalBundles > 0 ? (bundleCount / totalBundles) * 100 : 0.0;
      statusStats[status] = StatusStatistics(
        status: status,
        bundleCount: bundleCount,
        pieceCount: pieceCount,
        percentage: percentage,
      );
    }

    return BundleStatistics(
      totalBundles: totalBundles,
      pendingBundles: pendingBundles,
      issuedBundles: issuedBundles,
      inProgressBundles: inProgressBundles,
      completedBundles: completedBundles,
      cancelledBundles: cancelledBundles,
      totalPieces: totalPieces,
      completedPieces: completedPieces,
      inProgressPieces: inProgressPieces,
      pendingPieces: pendingPieces,
      designStats: designStats,
      sizeStats: sizeStats,
      statusStats: statusStats,
      fromDate: fromDate,
      toDate: toDate,
    );
  }
}
