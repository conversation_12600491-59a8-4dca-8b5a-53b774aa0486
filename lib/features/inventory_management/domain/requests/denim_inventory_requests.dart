import 'package:cloud_firestore/cloud_firestore.dart';

import '../entities/denim_inventory_entities.dart';

class CreateDenimTypeRequest {
  final String denimCode;
  final String type;
  final String description;
  final double gsm;
  final double width;
  final String? supplierId;
  final String? supplierName;

  CreateDenimTypeRequest({
    required this.denimCode,
    required this.type,
    required this.description,
    required this.gsm,
    required this.width,
    this.supplierId,
    this.supplierName,
  });

  Map<String, dynamic> toJson() {
    return {
      'denimCode': denimCode,
      'type': type,
      'description': description,
      'gsm': gsm,
      'width': width,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'isActive': true,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }
}

class UpdateDenimTypeRequest {
  final String id;
  final String denimCode;
  final String type;
  final String description;
  final double gsm;
  final double width;
  final String? supplierId;
  final String? supplierName;

  UpdateDenimTypeRequest({
    required this.id,
    required this.denimCode,
    required this.type,
    required this.description,
    required this.gsm,
    required this.width,
    this.supplierId,
    this.supplierName,
  });

  Map<String, dynamic> toJson() {
    return {
      'denimCode': denimCode,
      'type': type,
      'description': description,
      'gsm': gsm,
      'width': width,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }
}

class CreatePurchaseRequest {
  final DateTime purchaseDate;
  final String supplierName;
  final String? supplierId;
  final String rollId;
  final String denimTypeId;
  final String color;
  final double width;
  final double length;
  final String unit;
  final double gsm;
  final double pcs;
  final double ratePerUnit;
  final double totalCost;
  final String invoiceNo;
  final String? remarks;
  final String? driverName;
  final String? vehicleNumber;

  CreatePurchaseRequest({
    required this.purchaseDate,
    required this.supplierName,
    this.supplierId,
    required this.rollId,
    required this.denimTypeId,
    required this.color,
    required this.width,
    required this.length,
    required this.unit,
    required this.gsm,
    required this.pcs,
    required this.ratePerUnit,
    required this.totalCost,
    required this.invoiceNo,
    this.remarks,
    this.driverName,
    this.vehicleNumber,
  });
}

class CreateReturnRequest {
  final DateTime returnDate;
  final String rollId;
  final String? supplierId;
  final String supplierName;
  final String reasonForReturn;
  final double quantityReturned;
  final String unit;
  final String debitNoteNo;
  final String? remarks;

  CreateReturnRequest({
    required this.returnDate,
    required this.rollId,
    this.supplierId,
    required this.supplierName,
    required this.reasonForReturn,
    required this.quantityReturned,
    required this.unit,
    required this.debitNoteNo,
    this.remarks,
  });
}

class DenimInventoryFilterRequest {
  final String? denimTypeId;
  final String? color;
  final DenimRollStatus? status;
  final String? location;
  final String? supplierId;
  final bool? hasStock;

  DenimInventoryFilterRequest({
    this.denimTypeId,
    this.color,
    this.status,
    this.location,
    this.supplierId,
    this.hasStock,
  });
}

class SearchDenimInventoryRequest {
  final String query;
  final DenimInventoryFilterRequest? filter;
  final int? limit;

  SearchDenimInventoryRequest({
    required this.query,
    this.filter,
    this.limit,
  });
}

class UpdateStockRequest {
  final String rollId;
  final double? usedQty;
  final double? returnedQty;
  final DenimRollStatus? status;
  final String? location;

  UpdateStockRequest({
    required this.rollId,
    this.usedQty,
    this.returnedQty,
    this.status,
    this.location,
  });
}

class BulkStockUpdateRequest {
  final List<UpdateStockRequest> updates;

  BulkStockUpdateRequest({required this.updates});
}

class StockTransferRequest {
  final String rollId;
  final double quantity;
  final String toLocation;

  StockTransferRequest({
    required this.rollId,
    required this.quantity,
    required this.toLocation,
  });
}
