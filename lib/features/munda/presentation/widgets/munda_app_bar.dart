import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../bloc/munda_bloc.dart';
import '../bloc/munda_event.dart';
import '../bloc/munda_state.dart';

/// Custom app bar for Munda screens
class MundaAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final List<Widget>? actions;
  final bool showSyncButton;
  final bool showNotifications;

  const MundaAppBar({
    super.key,
    this.title,
    this.actions,
    this.showSyncButton = true,
    this.showNotifications = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          // Munda icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.design_services,
              size: 20,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          
          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title ?? 'Munda Operations',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
                  builder: (context, state) {
                    if (state is FirebaseAuthAuthenticated) {
                      return Text(
                        state.user.displayName,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white.withOpacity(0.8),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        // Sync button
        if (showSyncButton) _buildSyncButton(context),
        
        // Notifications
        if (showNotifications) _buildNotificationButton(context),
        
        // Custom actions
        if (actions != null) ...actions!,
        
        // Profile menu
        _buildProfileMenu(context),
        
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildSyncButton(BuildContext context) {
    return BlocBuilder<MundaBloc, MundaState>(
      builder: (context, state) {
        final isSyncing = state is OfflineSyncInProgress;
        
        return IconButton(
          onPressed: isSyncing ? null : () => _handleSync(context),
          icon: isSyncing
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.white.withOpacity(0.8),
                    ),
                  ),
                )
              : Icon(
                  Icons.sync,
                  color: Colors.white.withOpacity(0.9),
                ),
          tooltip: 'Sync Data',
        );
      },
    );
  }

  Widget _buildNotificationButton(BuildContext context) {
    return Stack(
      children: [
        IconButton(
          onPressed: () => _showNotifications(context),
          icon: Icon(
            Icons.notifications_outlined,
            color: Colors.white.withOpacity(0.9),
          ),
          tooltip: 'Notifications',
        ),
        
        // Notification badge
        Positioned(
          right: 8,
          top: 8,
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.error,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileMenu(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is! FirebaseAuthAuthenticated) {
          return const SizedBox.shrink();
        }

        return PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(context, value),
          icon: CircleAvatar(
            radius: 16,
            backgroundColor: Colors.white.withOpacity(0.2),
            child: Text(
              state.user.displayName.isNotEmpty 
                  ? state.user.displayName[0].toUpperCase()
                  : 'U',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person_outline, color: AppColors.textSecondary),
                  SizedBox(width: 12),
                  Text('Profile'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings_outlined, color: AppColors.textSecondary),
                  SizedBox(width: 12),
                  Text('Settings'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'help',
              child: Row(
                children: [
                  Icon(Icons.help_outline, color: AppColors.textSecondary),
                  SizedBox(width: 12),
                  Text('Help & Support'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'about',
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.textSecondary),
                  SizedBox(width: 12),
                  Text('About'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: AppColors.error),
                  SizedBox(width: 12),
                  Text(
                    'Sign Out',
                    style: TextStyle(color: AppColors.error),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleSync(BuildContext context) {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthAuthenticated) {
      context.read<MundaBloc>().add(
        SyncOfflineData(userId: authState.user.id),
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Syncing data...'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                const Icon(Icons.notifications, color: AppColors.primary),
                const SizedBox(width: 12),
                Text(
                  'Notifications',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Placeholder for notifications
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Icon(
                    Icons.notifications_none,
                    size: 48,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No new notifications',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'profile':
        _showProfile(context);
        break;
      case 'settings':
        _showSettings(context);
        break;
      case 'help':
        _showHelp(context);
        break;
      case 'about':
        _showAbout(context);
        break;
      case 'logout':
        _handleLogout(context);
        break;
    }
  }

  void _showProfile(BuildContext context) {
    // TODO: Navigate to profile page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Profile page coming soon')),
    );
  }

  void _showSettings(BuildContext context) {
    // TODO: Navigate to settings page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings page coming soon')),
    );
  }

  void _showHelp(BuildContext context) {
    // TODO: Navigate to help page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Help page coming soon')),
    );
  }

  void _showAbout(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Munda Operations',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.design_services,
          color: Colors.white,
          size: 24,
        ),
      ),
      children: [
        const Text(
          'Munda Operations module for cloth manufacturing management. '
          'Designed for data entry and task management by Munda operators.',
        ),
      ],
    );
  }

  void _handleLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<FirebaseAuthBloc>().add(const FirebaseSignOutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
