import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import 'all_markers_page.dart';

/// Cutting Master Dashboard Page - Fabric cutting and marker planning
class CuttingMasterDashboardPage extends StatelessWidget {
  const CuttingMasterDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cutting Master Dashboard'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildCuttingMasterDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildCuttingMasterDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildWorkOverview(),
          const SizedBox(height: 24),
          _buildCuttingActions(context),
          const SizedBox(height: 24),
          _buildMyTasks(),
          const SizedBox(height: 24),
          _buildMarkerPlanning(context),
          const SizedBox(height: 24),
          _buildFabricUtilization(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.deepPurple,
            Colors.deepPurple.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role.displayName ?? 'Cutting Master'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Fabric cutting and marker planning',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.design_services,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildWorkOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Today\'s Work Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: const [
            DashboardMetricCard(
              title: 'My Tasks',
              value: '6',
              icon: Icons.assignment,
              color: Colors.blue,
              trend: '+2',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Completed',
              value: '4',
              icon: Icons.check_circle,
              color: Colors.green,
              trend: '+1',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Fabric Efficiency',
              value: '94%',
              icon: Icons.trending_up,
              color: Colors.purple,
              trend: '+2%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Markers Created',
              value: '8',
              icon: Icons.straighten,
              color: Colors.orange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCuttingActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cutting Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 2.8,
          children: [
            DashboardCard(
              title: 'Create Marker',
              subtitle: 'Design cutting markers',
              icon: Icons.straighten,
              color: Colors.blue,
              onTap: () => _showCreateMarkerDialog(context),
            ),
            DashboardCard(
              title: 'Start Cutting',
              subtitle: 'Begin cutting process',
              icon: Icons.content_cut,
              color: Colors.green,
              onTap: () => _showStartCuttingDialog(context),
            ),
            DashboardCard(
              title: 'Fabric Calculator',
              subtitle: 'Calculate fabric requirements',
              icon: Icons.calculate,
              color: Colors.purple,
              onTap: () => _showFabricCalculatorDialog(context),
            ),
            DashboardCard(
              title: 'Quality Check',
              subtitle: 'Inspect cut pieces',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () => _showQualityCheckDialog(context),
            ),
            DashboardCard(
              title: 'Task Updates',
              subtitle: 'Update task progress',
              icon: Icons.update,
              color: Colors.teal,
              onTap: () => _showTaskUpdateDialog(context),
            ),
            DashboardCard(
              title: 'Production Log',
              subtitle: 'Log production data',
              icon: Icons.history,
              color: Colors.indigo,
              onTap: () => _showProductionLogDialog(context),
            ),
          ],
        ), 
      ],
    );
  }

  Widget _buildMyTasks() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.assignment,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'My Cutting Tasks',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to all tasks
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTaskItem(
              'CUT-001',
              'Shirt cutting - Order #1234',
              'High',
              'In Progress',
              Colors.blue,
              0.65,
            ),
            _buildTaskItem(
              'CUT-002',
              'Trouser cutting - Order #1235',
              'Medium',
              'Pending',
              Colors.orange,
              0.0,
            ),
            _buildTaskItem(
              'CUT-003',
              'Dress cutting - Order #1236',
              'High',
              'Completed',
              Colors.green,
              1.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskItem(String taskId, String description, String priority, String status, Color color, double progress) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      taskId,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getPriorityColor(priority).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  priority,
                  style: TextStyle(
                    color: _getPriorityColor(priority),
                    fontWeight: FontWeight.w500,
                    fontSize: 10,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget _buildMarkerPlanning(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.straighten,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Marker Planning',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const AllMarkersPage()),
                    );
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance.collection('markers').limit(5).snapshots(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return const Text('Something went wrong');
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: snapshot.data!.docs.length,
                  itemBuilder: (context, index) {
                    final doc = snapshot.data!.docs[index];
                    return _buildMarkerListItem(context, doc);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarkerListItem(BuildContext context, QueryDocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final designNumber = data['designNumber'] ?? 'No Design Number';
    final status = data['status'] ?? 'N/A';
    final createdAt = (data['createdAt'] as Timestamp).toDate();

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListTile(
        title: Text(designNumber),
        subtitle: Text('Status: $status\nCreated: ${createdAt.toLocal()}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _exportExistingMarker(context, doc.id, 'pdf'),
              icon: const Icon(Icons.picture_as_pdf),
              tooltip: 'Export PDF',
            ),
            IconButton(
              onPressed: () => _exportExistingMarker(context, doc.id, 'excel'),
              icon: const Icon(Icons.table_chart),
              tooltip: 'Export Excel',
            ),
            TextButton(
              onPressed: () => _showMarkerViewDialog(context, doc),
              child: const Text('View'),
            ),
          ],
        ),
      ),
    );
  }

  void _showMarkerViewDialog(BuildContext context, QueryDocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final designNumber = data['designNumber'] ?? 'No Design Number';
    final notes = data['notes'] ?? '';
    final markerItems = List<Map<String, dynamic>>.from(data['markerItems'] ?? []);
    final createdByName = data['createdByName'] ?? 'N/A';
    final createdAt = (data['createdAt'] as Timestamp).toDate();
    final totalPcs = markerItems.fold(0, (sum, item) => sum + (int.tryParse(item['pcs'] ?? '0') ?? 0));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Marker Details: $designNumber'),
        content: SizedBox(
          width: 800,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCompanyHeader(),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Date: ${createdAt.toLocal()}'),
                    Text('Created By: $createdByName'),
                  ],
                ),
                const SizedBox(height: 20),
                DataTable(
                  columns: const [
                    DataColumn(label: Text('Sort')),
                    DataColumn(label: Text('Roll')),
                    DataColumn(label: Text('MTR')),
                    DataColumn(label: Text('Panna')),
                    DataColumn(label: Text('Palla')),
                    DataColumn(label: Text('Pcs')),
                    DataColumn(label: Text('Length')),
                  ],
                  rows: markerItems.map((item) {
                    return DataRow(
                      cells: [
                        DataCell(Text(item['sort'] ?? '')),
                        DataCell(Text(item['roll'] ?? '')),
                        DataCell(Text(item['mtr'] ?? '')),
                        DataCell(Text(item['panna'] ?? '')),
                        DataCell(Text(item['palla'] ?? '')),
                        DataCell(Text(item['pcs'] ?? '')),
                        DataCell(Text(item['length'] ?? '')),
                      ],
                    );
                  }).toList(),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text('Total Pcs: $totalPcs', style: const TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
                if (notes.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Text('Notes: $notes'),
                  ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyHeader() {
    return Row(
      children: [
        // Replace with your company logo
        const Icon(Icons.business, size: 50),
        const SizedBox(width: 20),
        const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('HM Collection', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            Text('Your Company Address'),
            Text('Your Company Contact Info'),
          ],
        ),
      ],
    );
  }

  Widget _buildFabricUtilization() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.pie_chart,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Fabric Utilization',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Excellent',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildUtilizationCard('Today', '94%', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('This Week', '91%', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('This Month', '89%', Colors.purple),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('Target', '90%', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUtilizationCard(String period, String percentage, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            percentage,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            period,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // CRUD Dialog Methods

  /// Show Create Marker Dialog
  void _showCreateMarkerDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final designNumberController = TextEditingController();
    final notesController = TextEditingController();

    // Size options
    final numericSizes = ['26', '28', '30', '32', '34', '36', '38', '40', '42', '44'];
    final alphabeticSizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];
    String sizeType = 'numeric'; // 'numeric' or 'alphabetic'

    List<Map<String, dynamic>> markerItems = [];

    void _addMarkerItem() {
      markerItems.add({
        'sort': TextEditingController(),
        'roll': TextEditingController(),
        'mtr': TextEditingController(),
        'panna': TextEditingController(),
        'palla': TextEditingController(),
        'pcs': TextEditingController(),
        'selectedSizes': <String>[], // List of selected sizes
      });
    }

    _addMarkerItem(); // Add the first item

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Marker'),
        content: SizedBox(
          width: 600,
          child: Form(
            key: formKey,
            child: StatefulBuilder(
              builder: (context, setState) {
                return SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextFormField(
                        controller: designNumberController,
                        decoration: const InputDecoration(
                          labelText: 'Design Number',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) =>
                            value?.isEmpty == true ? 'Required' : null,
                      ),
                      const SizedBox(height: 16),
                      // Size Type Selector
                      Row(
                        children: [
                          const Text('Size Type: ', style: TextStyle(fontWeight: FontWeight.w500)),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: sizeType,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: 'numeric', child: Text('Numeric (26, 28, 30...)')),
                                DropdownMenuItem(value: 'alphabetic', child: Text('Alphabetic (XS, S, M, L...)')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  sizeType = value!;
                                  // Clear all selected sizes when changing type
                                  for (var item in markerItems) {
                                    item['selectedSizes'] = <String>[];
                                  }
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: markerItems.length,
                        itemBuilder: (context, index) {
                          return Card(
                            margin: const EdgeInsets.symmetric(vertical: 8),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextFormField(
                                          controller: markerItems[index]['sort'],
                                          decoration: const InputDecoration(
                                            labelText: 'Sort',
                                            border: OutlineInputBorder(),
                                          ),
                                          validator: (value) =>
                                              value?.isEmpty == true
                                                  ? 'Required'
                                                  : null,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: TextFormField(
                                          controller: markerItems[index]['roll'],
                                          decoration: const InputDecoration(
                                            labelText: 'Roll',
                                            border: OutlineInputBorder(),
                                          ),
                                          validator: (value) =>
                                              value?.isEmpty == true
                                                  ? 'Required'
                                                  : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextFormField(
                                          controller: markerItems[index]['mtr'],
                                          decoration: const InputDecoration(
                                            labelText: 'MTR',
                                            border: OutlineInputBorder(),
                                          ),
                                          keyboardType: TextInputType.number,
                                          validator: (value) =>
                                              value?.isEmpty == true
                                                  ? 'Required'
                                                  : null,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: TextFormField(
                                          controller: markerItems[index]['panna'],
                                          decoration: const InputDecoration(
                                            labelText: 'Panna',
                                            border: OutlineInputBorder(),
                                          ),
                                          keyboardType: TextInputType.number,
                                          validator: (value) =>
                                              value?.isEmpty == true
                                                  ? 'Required'
                                                  : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextFormField(
                                          controller: markerItems[index]['palla'],
                                          decoration: const InputDecoration(
                                            labelText: 'Palla',
                                            border: OutlineInputBorder(),
                                          ),
                                          keyboardType: TextInputType.number,
                                          validator: (value) =>
                                              value?.isEmpty == true
                                                  ? 'Required'
                                                  : null,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: TextFormField(
                                          controller: markerItems[index]['pcs'],
                                          decoration: const InputDecoration(
                                            labelText: 'Pcs',
                                            border: OutlineInputBorder(),
                                          ),
                                          keyboardType: TextInputType.number,
                                          validator: (value) =>
                                              value?.isEmpty == true
                                                  ? 'Required'
                                                  : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  // Size Selection
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Text('Sizes:', style: TextStyle(fontWeight: FontWeight.w500)),
                                      const SizedBox(height: 8),
                                      Container(
                                        width: double.infinity,
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Wrap(
                                          spacing: 8,
                                          runSpacing: 8,
                                          children: (sizeType == 'numeric' ? numericSizes : alphabeticSizes)
                                              .map((size) => FilterChip(
                                                    label: Text(size),
                                                    selected: markerItems[index]['selectedSizes'].contains(size),
                                                    onSelected: (selected) {
                                                      setState(() {
                                                        if (selected) {
                                                          markerItems[index]['selectedSizes'].add(size);
                                                        } else {
                                                          markerItems[index]['selectedSizes'].remove(size);
                                                        }
                                                      });
                                                    },
                                                  ))
                                              .toList(),
                                        ),
                                      ),
                                      if (markerItems[index]['selectedSizes'].isEmpty)
                                        const Padding(
                                          padding: EdgeInsets.only(top: 8),
                                          child: Text(
                                            'Please select at least one size',
                                            style: TextStyle(color: Colors.red, fontSize: 12),
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 16),
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _addMarkerItem();
                          });
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('Add Item'),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: notesController,
                        decoration: const InputDecoration(
                          labelText: 'Notes (Optional)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton.icon(
            onPressed: () => _exportMarkerToPDF(context, {
              'designNumber': designNumberController.text,
              'notes': notesController.text,
              'markerItems': markerItems,
              'sizeType': sizeType,
            }),
            icon: const Icon(Icons.picture_as_pdf),
            label: const Text('Export PDF'),
          ),
          TextButton.icon(
            onPressed: () => _exportMarkerToExcel(context, {
              'designNumber': designNumberController.text,
              'notes': notesController.text,
              'markerItems': markerItems,
              'sizeType': sizeType,
            }),
            icon: const Icon(Icons.table_chart),
            label: const Text('Export Excel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Validate that at least one size is selected for each item
              bool hasValidSizes = markerItems.every((item) =>
                item['selectedSizes'].isNotEmpty);

              if (formKey.currentState!.validate() && hasValidSizes) {
                _createMarker(context, {
                  'designNumber': designNumberController.text,
                  'notes': notesController.text,
                  'markerItems': markerItems
                      .map((item) => {
                            'sort': item['sort']!.text,
                            'roll': item['roll']!.text,
                            'mtr': item['mtr']!.text,
                            'panna': item['panna']!.text,
                            'palla': item['palla']!.text,
                            'pcs': item['pcs']!.text,
                            'selectedSizes': item['selectedSizes'],
                          })
                      .toList(),
                  'sizeType': sizeType,
                });
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  /// Show Start Cutting Dialog
  void _showStartCuttingDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final taskNumberController = TextEditingController();
    final markerIdController = TextEditingController();
    final plannedQuantityController = TextEditingController();
    final assignedToController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start Cutting Task'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: taskNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Task Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: markerIdController,
                    decoration: const InputDecoration(
                      labelText: 'Marker ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: plannedQuantityController,
                    decoration: const InputDecoration(
                      labelText: 'Planned Quantity',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: assignedToController,
                    decoration: const InputDecoration(
                      labelText: 'Assigned To',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes (Optional)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _startCuttingTask(context, formKey, {
              'taskNumber': taskNumberController.text,
              'markerId': markerIdController.text,
              'plannedQuantity': plannedQuantityController.text,
              'assignedTo': assignedToController.text,
              'notes': notesController.text,
            }),
            child: const Text('Start Task'),
          ),
        ],
      ),
    );
  }

  /// Show Fabric Calculator Dialog
  void _showFabricCalculatorDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final calculationNumberController = TextEditingController();
    final productIdController = TextEditingController();
    final productNameController = TextEditingController();
    final fabricTypeController = TextEditingController();
    final fabricWidthController = TextEditingController();
    final wastagePercentageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fabric Calculator'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: calculationNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Calculation Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: productIdController,
                    decoration: const InputDecoration(
                      labelText: 'Product ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: productNameController,
                    decoration: const InputDecoration(
                      labelText: 'Product Name',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: fabricTypeController,
                    decoration: const InputDecoration(
                      labelText: 'Fabric Type',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: fabricWidthController,
                          decoration: const InputDecoration(
                            labelText: 'Fabric Width (cm)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: wastagePercentageController,
                          decoration: const InputDecoration(
                            labelText: 'Wastage %',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _calculateFabric(context, formKey, {
              'calculationNumber': calculationNumberController.text,
              'productId': productIdController.text,
              'productName': productNameController.text,
              'fabricType': fabricTypeController.text,
              'fabricWidth': fabricWidthController.text,
              'wastagePercentage': wastagePercentageController.text,
            }),
            child: const Text('Calculate'),
          ),
        ],
      ),
    );
  }

  /// Show Quality Check Dialog
  void _showQualityCheckDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final taskIdController = TextEditingController();
    final checkTypeController = TextEditingController();
    final scoreController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quality Check'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: taskIdController,
                  decoration: const InputDecoration(
                    labelText: 'Task ID',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: checkTypeController,
                  decoration: const InputDecoration(
                    labelText: 'Check Type',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: scoreController,
                  decoration: const InputDecoration(
                    labelText: 'Quality Score (0-100)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _addQualityCheck(context, formKey, {
              'taskId': taskIdController.text,
              'checkType': checkTypeController.text,
              'score': scoreController.text,
              'notes': notesController.text,
            }),
            child: const Text('Add Check'),
          ),
        ],
      ),
    );
  }

  /// Show Task Update Dialog
  void _showTaskUpdateDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final taskIdController = TextEditingController();
    final actualQuantityController = TextEditingController();
    final statusController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Task'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: taskIdController,
                  decoration: const InputDecoration(
                    labelText: 'Task ID',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: actualQuantityController,
                  decoration: const InputDecoration(
                    labelText: 'Actual Quantity',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: statusController.text.isEmpty ? null : statusController.text,
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'pending', child: Text('Pending')),
                    DropdownMenuItem(value: 'inProgress', child: Text('In Progress')),
                    DropdownMenuItem(value: 'completed', child: Text('Completed')),
                    DropdownMenuItem(value: 'onHold', child: Text('On Hold')),
                  ],
                  onChanged: (value) => statusController.text = value ?? '',
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _updateTask(context, formKey, {
              'taskId': taskIdController.text,
              'actualQuantity': actualQuantityController.text,
              'status': statusController.text,
              'notes': notesController.text,
            }),
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  /// Show Production Log Dialog
  void _showProductionLogDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final logNumberController = TextEditingController();
    final taskIdController = TextEditingController();
    final taskTypeController = TextEditingController();
    final operatorIdController = TextEditingController();
    final operatorNameController = TextEditingController();
    final quantityProducedController = TextEditingController();
    final qualityScoreController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Production Log'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: logNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Log Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: taskIdController,
                    decoration: const InputDecoration(
                      labelText: 'Task ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: taskTypeController,
                    decoration: const InputDecoration(
                      labelText: 'Task Type',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: operatorIdController,
                          decoration: const InputDecoration(
                            labelText: 'Operator ID',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: operatorNameController,
                          decoration: const InputDecoration(
                            labelText: 'Operator Name',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: quantityProducedController,
                          decoration: const InputDecoration(
                            labelText: 'Quantity Produced',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: qualityScoreController,
                          decoration: const InputDecoration(
                            labelText: 'Quality Score',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _createProductionLog(context, formKey, {
              'logNumber': logNumberController.text,
              'taskId': taskIdController.text,
              'taskType': taskTypeController.text,
              'operatorId': operatorIdController.text,
              'operatorName': operatorNameController.text,
              'quantityProduced': quantityProducedController.text,
              'qualityScore': qualityScoreController.text,
              'notes': notesController.text,
            }),
            child: const Text('Create Log'),
          ),
        ],
      ),
    );
  }

  // CRUD Functions

  /// Create Marker in Firebase
  Future<void> _createMarker(
      BuildContext context, Map<String, dynamic> data) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      await firestore.collection('markers').add({
        'designNumber': data['designNumber'],
        'notes': data['notes'],
        'markerItems': data['markerItems'],
        'sizeType': data['sizeType'],
        'status': 'draft',
        'createdBy': 'current_user_id', // Replace with actual user ID
        'createdByName': 'Current User', // Replace with actual user name
        'createdAt': now,
        'updatedAt': now,
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Marker created successfully!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating marker: $e')),
      );
    }
  }

  /// Start Cutting Task in Firebase
  Future<void> _startCuttingTask(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      await firestore.collection('cutting_tasks').add({
        'taskNumber': data['taskNumber'],
        'markerId': data['markerId'],
        'markerNumber': 'MARKER-${data['markerId']}', // Simplified
        'productId': 'PROD-001', // Simplified
        'productName': 'Sample Product', // Simplified
        'fabricType': 'Cotton', // Simplified
        'plannedQuantity': int.tryParse(data['plannedQuantity'] ?? '0') ?? 0,
        'actualQuantity': 0,
        'status': 'pending',
        'assignedTo': data['assignedTo'],
        'assignedToName': data['assignedTo'], // Simplified
        'assignedBy': 'current_user_id', // Replace with actual user ID
        'assignedByName': 'Current User', // Replace with actual user name
        'plannedStartDate': now,
        'plannedEndDate': Timestamp.fromDate(DateTime.now().add(const Duration(days: 1))),
        'cutPieces': [],
        'qualityChecks': [],
        'notes': data['notes'],
        'metadata': {},
        'createdAt': now,
        'updatedAt': now,
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cutting task started successfully!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error starting cutting task: $e')),
      );
    }
  }

  /// Calculate Fabric in Firebase
  Future<void> _calculateFabric(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      final fabricWidth = double.tryParse(data['fabricWidth'] ?? '0') ?? 0.0;
      final wastagePercentage = double.tryParse(data['wastagePercentage'] ?? '0') ?? 0.0;

      // Simplified calculation - in real app, this would be more complex
      final totalFabricRequired = fabricWidth * 2.0; // Simplified
      final totalFabricWithWastage = totalFabricRequired * (1 + wastagePercentage / 100);

      await firestore.collection('fabric_calculations').add({
        'calculationNumber': data['calculationNumber'],
        'productId': data['productId'],
        'productName': data['productName'],
        'fabricType': data['fabricType'],
        'fabricWidth': fabricWidth,
        'sizeQuantities': {'S': 10, 'M': 15, 'L': 12, 'XL': 8}, // Simplified
        'totalFabricRequired': totalFabricRequired,
        'wastagePercentage': wastagePercentage,
        'totalFabricWithWastage': totalFabricWithWastage,
        'calculatedBy': 'current_user_id', // Replace with actual user ID
        'calculatedByName': 'Current User', // Replace with actual user name
        'status': 'draft',
        'createdAt': now,
        'updatedAt': now,
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Fabric calculation completed! Total required: ${totalFabricWithWastage.toStringAsFixed(2)} meters')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error calculating fabric: $e')),
      );
    }
  }

  /// Add Quality Check in Firebase
  Future<void> _addQualityCheck(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      final qualityCheck = {
        'checkId': 'QC-${DateTime.now().millisecondsSinceEpoch}',
        'checkType': data['checkType'],
        'status': double.tryParse(data['score'] ?? '0')! >= 80 ? 'passed' : 'failed',
        'score': double.tryParse(data['score'] ?? '0') ?? 0.0,
        'checkedBy': 'current_user_id', // Replace with actual user ID
        'checkedByName': 'Current User', // Replace with actual user name
        'checkedAt': now,
        'notes': data['notes'],
        'defects': [],
      };

      // Update the cutting task with the quality check
      final taskDoc = await firestore.collection('cutting_tasks').doc(data['taskId']).get();
      if (taskDoc.exists) {
        final currentQualityChecks = List<Map<String, dynamic>>.from(taskDoc.data()?['qualityChecks'] ?? []);
        currentQualityChecks.add(qualityCheck);

        await firestore.collection('cutting_tasks').doc(data['taskId']).update({
          'qualityChecks': currentQualityChecks,
          'updatedAt': now,
        });

        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Quality check added successfully!')),
        );
      } else {
        throw Exception('Task not found');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding quality check: $e')),
      );
    }
  }

  /// Update Task in Firebase
  Future<void> _updateTask(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      final updateData = <String, dynamic>{
        'updatedAt': now,
      };

      if (data['actualQuantity']?.isNotEmpty == true) {
        updateData['actualQuantity'] = int.tryParse(data['actualQuantity']!) ?? 0;
      }

      if (data['status']?.isNotEmpty == true) {
        updateData['status'] = data['status'];

        // Set actual dates based on status
        if (data['status'] == 'inProgress') {
          updateData['actualStartDate'] = now;
        } else if (data['status'] == 'completed') {
          updateData['actualEndDate'] = now;
        }
      }

      if (data['notes']?.isNotEmpty == true) {
        updateData['notes'] = data['notes'];
      }

      await firestore.collection('cutting_tasks').doc(data['taskId']).update(updateData);

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Task updated successfully!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating task: $e')),
      );
    }
  }

  /// Create Production Log in Firebase
  Future<void> _createProductionLog(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      await firestore.collection('production_logs').add({
        'logNumber': data['logNumber'],
        'taskId': data['taskId'],
        'taskType': data['taskType'],
        'operatorId': data['operatorId'],
        'operatorName': data['operatorName'],
        'startTime': now,
        'endTime': null, // Will be set when completed
        'quantityProduced': int.tryParse(data['quantityProduced'] ?? '0') ?? 0,
        'qualityScore': double.tryParse(data['qualityScore'] ?? '0'),
        'notes': data['notes'],
        'metrics': {
          'efficiency': 95.0,
          'defectRate': 2.0,
          'timeSpent': 0.0,
        },
        'createdAt': now,
        'updatedAt': now,
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Production log created successfully!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating production log: $e')),
      );
    }
  }

  // Export Functions

  /// Export Marker to PDF
  Future<void> _exportMarkerToPDF(BuildContext context, Map<String, dynamic> markerData) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              // Header
              pw.Header(
                level: 0,
                child: pw.Text(
                  'Marker Design Report',
                  style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
                ),
              ),
              pw.SizedBox(height: 20),

              // Design Information
              pw.Container(
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Design Information',
                      style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text('Design Number: ${markerData['designNumber']}'),
                    pw.Text('Size Type: ${markerData['sizeType'] == 'numeric' ? 'Numeric (26, 28, 30...)' : 'Alphabetic (XS, S, M, L...)'}'),
                    pw.Text('Created: ${DateTime.now().toString().split('.')[0]}'),
                    if (markerData['notes']?.isNotEmpty == true)
                      pw.Text('Notes: ${markerData['notes']}'),
                  ],
                ),
              ),
              pw.SizedBox(height: 20),

              // Marker Items Table
              pw.Text(
                'Marker Items',
                style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),

              pw.Table(
                border: pw.TableBorder.all(color: PdfColors.grey),
                columnWidths: {
                  0: const pw.FlexColumnWidth(1),
                  1: const pw.FlexColumnWidth(1),
                  2: const pw.FlexColumnWidth(1),
                  3: const pw.FlexColumnWidth(1),
                  4: const pw.FlexColumnWidth(1),
                  5: const pw.FlexColumnWidth(1),
                  6: const pw.FlexColumnWidth(2),
                },
                children: [
                  // Header row
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Sort', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Roll', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('MTR', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Panna', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Palla', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Pcs', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Sizes', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                    ],
                  ),
                  // Data rows
                  ...markerData['markerItems'].map<pw.TableRow>((item) {
                    return pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item['sort']?.text ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item['roll']?.text ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item['mtr']?.text ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item['panna']?.text ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item['palla']?.text ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item['pcs']?.text ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item['selectedSizes']?.join(', ') ?? ''),
                        ),
                      ],
                    );
                  }).toList(),
                ],
              ),

            ];
          },
        ),
      );

      // Show print preview
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'Marker_${markerData['designNumber']}_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('PDF export completed!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting PDF: $e')),
      );
    }
  }

  /// Export Marker to Excel (CSV format)
  Future<void> _exportMarkerToExcel(BuildContext context, Map<String, dynamic> markerData) async {
    try {
      // Create CSV content
      final StringBuffer csvContent = StringBuffer();

      // Header information
      csvContent.writeln('Marker Design Report');
      csvContent.writeln('Design Number,${markerData['designNumber']}');
      csvContent.writeln('Size Type,${markerData['sizeType'] == 'numeric' ? 'Numeric (26, 28, 30...)' : 'Alphabetic (XS, S, M, L...)'}');
      csvContent.writeln('Created,${DateTime.now().toString().split('.')[0]}');
      if (markerData['notes']?.isNotEmpty == true) {
        csvContent.writeln('Notes,"${markerData['notes']}"');
      }
      csvContent.writeln('');

      // Table headers
      csvContent.writeln('Sort,Roll,MTR,Panna,Palla,Pcs,Sizes');

      // Table data
      for (var item in markerData['markerItems']) {
        final row = [
          item['sort']?.text ?? '',
          item['roll']?.text ?? '',
          item['mtr']?.text ?? '',
          item['panna']?.text ?? '',
          item['palla']?.text ?? '',
          item['pcs']?.text ?? '',
          '"${item['selectedSizes']?.join(', ') ?? ''}"',
        ].join(',');
        csvContent.writeln(row);
      }

      // Convert to bytes
      final bytes = Uint8List.fromList(csvContent.toString().codeUnits);

      // Save/share the file
      await Printing.sharePdf(
        bytes: bytes,
        filename: 'Marker_${markerData['designNumber']}_${DateTime.now().millisecondsSinceEpoch}.csv',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Excel/CSV export completed!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting Excel: $e')),
      );
    }
  }

  /// Export existing marker from Firebase
  Future<void> _exportExistingMarker(BuildContext context, String markerId, String format) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final doc = await firestore.collection('markers').doc(markerId).get();

      if (!doc.exists) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Marker not found')),
        );
        return;
      }

      final markerData = doc.data()!;

      if (format == 'pdf') {
        await _exportMarkerToPDF(context, markerData);
      } else {
        await _exportMarkerToExcel(context, markerData);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting marker: $e')),
      );
    }
  }
}