
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/core/usecases/usecase.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/shared/models/pagination.dart';
import 'package:hm_collection/features/production_planning/domain/entities/production_entities.dart';
import 'package:hm_collection/features/production_planning/domain/repositories/production_repository.dart';

import '../../data/models/production_order_filter.dart';
import '../../data/models/production_task_filter.dart';
import '../models/create_production_order_request.dart';
import '../models/update_production_order_request.dart';

class GetProductionOrdersUseCase
    implements
        UseCase<ApiListResponse<ProductionOrder>,
            GetProductionOrdersParams> {
  final ProductionRepository repository;

  GetProductionOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> call(
      GetProductionOrdersParams params) async {
    return await repository.getProductionOrders(
        filter: params.filter, pagination: params.pagination);
  }
}

class GetProductionOrderByIdUseCase
    implements UseCase<ApiResponse<ProductionOrder>, IdParams> {
  final ProductionRepository repository;

  GetProductionOrderByIdUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      IdParams params) async {
    return await repository.getProductionOrderById(params.id);
  }
}

class CreateProductionOrderUseCase
    implements UseCase<ApiResponse<ProductionOrder>, CreateProductionOrderRequest> {
  final ProductionRepository repository;

  CreateProductionOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      CreateProductionOrderRequest params) async {
    return await repository.createProductionOrder(params);
  }
}

class UpdateProductionOrderUseCase
    implements UseCase<ApiResponse<ProductionOrder>, UpdateProductionOrderRequest> {
  final ProductionRepository repository;

  UpdateProductionOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      UpdateProductionOrderRequest params) async {
    return await repository.updateProductionOrder(params);
  }
}

class UpdateProductionOrderStatusUseCase
    implements
        UseCase<ApiResponse<ProductionOrder>,
            UpdateProductionOrderStatusParams> {
  final ProductionRepository repository;

  UpdateProductionOrderStatusUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      UpdateProductionOrderStatusParams params) async {
    return await repository.updateProductionOrderStatus(params);
  }
}

class StartProductionOrderUseCase
    implements UseCase<ApiResponse<ProductionOrder>, IdParams> {
  final ProductionRepository repository;

  StartProductionOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      IdParams params) async {
    return await repository.startProductionOrder(params.id);
  }
}

class PauseProductionOrderUseCase
    implements
        UseCase<ApiResponse<ProductionOrder>, PauseProductionOrderParams> {
  final ProductionRepository repository;

  PauseProductionOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      PauseProductionOrderParams params) async {
    return await repository.pauseProductionOrder(params);
  }
}

class ResumeProductionOrderUseCase
    implements UseCase<ApiResponse<ProductionOrder>, IdParams> {
  final ProductionRepository repository;

  ResumeProductionOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      IdParams params) async {
    return await repository.resumeProductionOrder(params.id);
  }
}

class CompleteProductionOrderUseCase
    implements UseCase<ApiResponse<ProductionOrder>, IdParams> {
  final ProductionRepository repository;

  CompleteProductionOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> call(
      IdParams params) async {
    return await repository.completeProductionOrder(params.id);
  }
}

class GetProductionTasksUseCase
    implements
        UseCase<ApiListResponse<ProductionTask>, GetProductionTasksParams> {
  final ProductionRepository repository;

  GetProductionTasksUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ProductionTask>>> call(
      GetProductionTasksParams params) async {
    return await repository.getProductionTasks(params);
  }
}

class UpdateTaskStatusUseCase
    implements UseCase<ApiResponse<ProductionTask>, UpdateTaskStatusParams> {
  final ProductionRepository repository;

  UpdateTaskStatusUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionTask>>> call(
      UpdateTaskStatusParams params) async {
    return await repository.updateTaskStatus(params);
  }
}

class AssignTaskToWorkersUseCase
    implements UseCase<ApiResponse<ProductionTask>, AssignTaskToWorkersParams> {
  final ProductionRepository repository;

  AssignTaskToWorkersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionTask>>> call(
      AssignTaskToWorkersParams params) async {
    return await repository.assignTaskToWorkers(params);
  }
}

class UpdateTaskProgressUseCase
    implements UseCase<ApiResponse<ProductionTask>, UpdateTaskProgressParams> {
  final ProductionRepository repository;

  UpdateTaskProgressUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ProductionTask>>> call(
      UpdateTaskProgressParams params) async {
    return await repository.updateTaskProgress(params);
  }
}

class GetResourceAllocationsUseCase
    implements
        UseCase<ApiListResponse<ResourceAllocation>,
            GetResourceAllocationsParams> {
  final ProductionRepository repository;

  GetResourceAllocationsUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ResourceAllocation>>> call(
      GetResourceAllocationsParams params) async {
    return await repository.getResourceAllocations(params);
  }
}

class AllocateResourceUseCase
    implements UseCase<ApiResponse<ResourceAllocation>, AllocateResourceRequest> {
  final ProductionRepository repository;

  AllocateResourceUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ResourceAllocation>>> call(
      AllocateResourceRequest params) async {
    return await repository.allocateResource(params);
  }
}

class ReleaseResourceUseCase
    implements UseCase<ApiVoidResponse, ReleaseResourceParams> {
  final ProductionRepository repository;

  ReleaseResourceUseCase(this.repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(
      ReleaseResourceParams params) async {
    return await repository.releaseResource(params);
  }
}

class GetProductionStatisticsUseCase
    implements UseCase<ProductionStatistics, GetProductionStatisticsParams> {
  final ProductionRepository repository;

  GetProductionStatisticsUseCase(this.repository);

  @override
  Future<Either<Failure, ProductionStatistics>> call(
      GetProductionStatisticsParams params) async {
    return await repository.getProductionStatistics(params);
  }
}

class GetProductionCapacityUseCase
    implements UseCase<ProductionCapacity, GetProductionCapacityParams> {
  final ProductionRepository repository;

  GetProductionCapacityUseCase(this.repository);

  @override
  Future<Either<Failure, ProductionCapacity>> call(
      GetProductionCapacityParams params) async {
    return await repository.getProductionCapacity(params);
  }
}

class GetProductionBottlenecksUseCase
    implements
        UseCase<List<ProductionBottleneck>, GetProductionBottlenecksParams> {
  final ProductionRepository repository;

  GetProductionBottlenecksUseCase(this.repository);

  @override
  Future<Either<Failure, List<ProductionBottleneck>>> call(
      GetProductionBottlenecksParams params) async {
    return await repository.getProductionBottlenecks(params);
  }
}

class GetOverdueProductionOrdersUseCase
    implements
        UseCase<ApiListResponse<ProductionOrder>,
            GetOverdueProductionOrdersParams> {
  final ProductionRepository repository;

  GetOverdueProductionOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> call(
      GetOverdueProductionOrdersParams params) async {
    return await repository.getOverdueProductionOrders(params);
  }
}

class GetUrgentProductionOrdersUseCase
    implements
        UseCase<ApiListResponse<ProductionOrder>,
            GetUrgentProductionOrdersParams> {
  final ProductionRepository repository;

  GetUrgentProductionOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> call(
      GetUrgentProductionOrdersParams params) async {
    return await repository.getUrgentProductionOrders(params);
  }
}

class SearchProductionOrdersUseCase
    implements
        UseCase<ApiListResponse<ProductionOrder>,
            SearchProductionOrdersParams> {
  final ProductionRepository repository;

  SearchProductionOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> call(
      SearchProductionOrdersParams params) async {
    return await repository.searchProductionOrders(params);
  }
}

class GetProductionOrdersParams extends Equatable {
  final ProductionOrderFilter? filter;
  final PaginationParams? pagination;

  const GetProductionOrdersParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class UpdateProductionOrderStatusParams extends Equatable {
  final String productionOrderId;
  final String status;
  final String? reason;

  const UpdateProductionOrderStatusParams(this.productionOrderId, this.status,
      {this.reason});

  @override
  List<Object?> get props => [productionOrderId, status, reason];
}

class PauseProductionOrderParams extends Equatable {
  final String productionOrderId;
  final String reason;

  const PauseProductionOrderParams(this.productionOrderId, this.reason);

  @override
  List<Object?> get props => [productionOrderId, reason];
}

class GetProductionTasksParams extends Equatable {
  final ProductionTaskFilter? filter;
  final PaginationParams? pagination;

  const GetProductionTasksParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class UpdateTaskStatusParams extends Equatable {
  final String taskId;
  final String status;
  final String? reason;

  const UpdateTaskStatusParams(this.taskId, this.status, {this.reason});

  @override
  List<Object?> get props => [taskId, status, reason];
}

class AssignTaskToWorkersParams extends Equatable {
  final String taskId;
  final List<String> workerIds;

  const AssignTaskToWorkersParams(this.taskId, this.workerIds);

  @override
  List<Object?> get props => [taskId, workerIds];
}

class UpdateTaskProgressParams extends Equatable {
  final String taskId;
  final double completionPercentage;
  final String? notes;

  const UpdateTaskProgressParams(this.taskId, this.completionPercentage, {this.notes});

  @override
  List<Object?> get props => [taskId, completionPercentage, notes];
}

class GetResourceAllocationsParams extends Equatable {
  final ResourceAllocationFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetResourceAllocationsParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class ReleaseResourceParams extends Equatable {
  final String allocationId;
  final String? reason;

  const ReleaseResourceParams(this.allocationId, {this.reason});

  @override
  List<Object?> get props => [allocationId, reason];
}

class GetProductionStatisticsParams extends Equatable {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? departmentId;

  const GetProductionStatisticsParams(
      {this.startDate, this.endDate, this.departmentId});

  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

class GetProductionCapacityParams extends Equatable {
  final DateTime? date;
  final String? departmentId;

  const GetProductionCapacityParams({this.date, this.departmentId});

  @override
  List<Object?> get props => [date, departmentId];
}

class GetProductionBottlenecksParams extends Equatable {
  final DateTime? startDate;
  final DateTime? endDate;

  const GetProductionBottlenecksParams({this.startDate, this.endDate});

  @override
  List<Object?> get props => [startDate, endDate];
}

class GetOverdueProductionOrdersParams extends Equatable {
  final PaginationParams? pagination;

  const GetOverdueProductionOrdersParams({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

class GetUrgentProductionOrdersParams extends Equatable {
  final PaginationParams? pagination;

  const GetUrgentProductionOrdersParams({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

class SearchProductionOrdersParams extends Equatable {
  final String query;
  final ProductionOrderFilter? filter;
  final PaginationParams? pagination;

  const SearchProductionOrdersParams(this.query, {this.filter, this.pagination});

  @override
  List<Object?> get props => [query, filter, pagination];
}
