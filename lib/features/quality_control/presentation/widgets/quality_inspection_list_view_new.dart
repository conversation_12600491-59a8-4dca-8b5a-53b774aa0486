import 'package:flutter/material.dart';
import '../../domain/entities/quality_entities.dart';
import '../bloc/quality_bloc.dart';

// Re-export the InspectionStatus enum for convenience
export '../../domain/entities/quality_entities.dart' show InspectionStatus, QualityViewMode;

/// List view for quality inspections
class QualityInspectionListView extends StatelessWidget {
  final List<QualityInspection> inspections;
  final Function(QualityInspection)? onInspectionTap;
  final Function(QualityInspection)? onInspectionEdit;
  final Function(QualityInspection)? onInspectionDelete;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final QualityViewMode viewMode;
  final List<String> selectedInspectionIds;
  final Function(String)? onInspectionLongPress;
  final Function(List<String>)? onSelectionChanged;
  final Function(QualityInspection)? onStatusChanged;
  final VoidCallback? onLoadMore;
  final bool hasMore;

  const QualityInspectionListView({
    Key? key,
    required this.inspections,
    this.onInspectionTap,
    this.onInspectionEdit,
    this.onInspectionDelete,
    this.isLoading = false,
    this.onRefresh,
    this.viewMode = QualityViewMode.list,
    this.selectedInspectionIds = const [],
    this.onInspectionLongPress,
    this.onSelectionChanged,
    this.onStatusChanged,
    this.onLoadMore,
    this.hasMore = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (inspections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.verified,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No quality inspections found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Quality inspections will appear here when they are created',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final scrollController = ScrollController();
    
    if (onLoadMore != null) {
      scrollController.addListener(() {
        if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 && 
            hasMore && 
            !isLoading) {
          onLoadMore!();
        }
      });
    }
    
    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: NotificationListener<ScrollNotification>(
        onNotification: (scrollInfo) {
          if (scrollInfo is ScrollEndNotification && 
              scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent &&
              hasMore && 
              !isLoading) {
            onLoadMore?.call();
          }
          return true;
        },
        child: _buildContent(scrollController, context),
      ),
    );
  }

  Widget _buildContent(ScrollController scrollController, BuildContext context) {
    switch (viewMode) {
      case QualityViewMode.kanban:
        return _buildKanbanView(context);
      case QualityViewMode.timeline:
        return _buildTimelineView();
      case QualityViewMode.dashboard:
        return _buildDashboardView(context);
      case QualityViewMode.list:
      default:
        return _buildListView(scrollController);
    }
  }

  Widget _buildListView(ScrollController scrollController) {
    return ListView.builder(
      controller: scrollController,
      itemCount: inspections.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        // Show loading indicator at the bottom when loading more items
        if (index >= inspections.length) {
          return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Center(child: CircularProgressIndicator()),
          );
        }
        
        final inspection = inspections[index];
        final isSelected = selectedInspectionIds.contains(inspection.id);
        
        return QualityInspectionListTile(
          inspection: inspection,
          isSelected: isSelected,
          onTap: () => onInspectionTap?.call(inspection),
          onLongPress: () {
            onInspectionLongPress?.call(inspection.id);
            _handleSelection(inspection.id);
          },
          onEdit: () => onInspectionEdit?.call(inspection),
          onDelete: () => onInspectionDelete?.call(inspection),
          onStatusChanged: onStatusChanged != null ? () => onStatusChanged!(inspection) : null,
        );
      },
    );
  }

  Widget _buildKanbanView(BuildContext context) {
    // Group inspections by status
    final statusGroups = <InspectionStatus, List<QualityInspection>>{};
    for (var status in InspectionStatus.values) {
      statusGroups[status] = [];
    }
    
    for (var inspection in inspections) {
      statusGroups[inspection.status]?.add(inspection);
    }

    return ListView(
      scrollDirection: Axis.horizontal,
      children: statusGroups.entries.map((entry) {
        return Container(
          width: 300,
          margin: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  '${entry.key.toString().split('.').last} (${entry.value.length})',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: entry.value.length,
                  itemBuilder: (context, index) {
                    final inspection = entry.value[index];
                    return Card(
                      margin: const EdgeInsets.all(4.0),
                      child: ListTile(
                        title: Text(inspection.inspectionNumber),
                        subtitle: Text(inspection.product?.name ?? inspection.orderNumber),
                        onTap: () => onInspectionTap?.call(inspection),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTimelineView() {
    return ListView.builder(
      itemCount: inspections.length,
      itemBuilder: (context, index) {
        final inspection = inspections[index];
        return ListTile(
          leading: const Icon(Icons.timeline),
          title: Text('${inspection.inspectionNumber} - ${inspection.product}'),
          subtitle: Text('Scheduled: ${inspection.inspectionDate}'),
          onTap: () => onInspectionTap?.call(inspection),
        );
      },
    );
  }

  Widget _buildDashboardView(BuildContext context) {
    // Simple dashboard with summary cards
    final pendingCount = inspections.where((i) => i.status == InspectionStatus.scheduled).length;
    final inProgressCount = inspections.where((i) => i.status == InspectionStatus.inProgress).length;
    final completedCount = inspections.where((i) => i.status == InspectionStatus.completed).length;

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Row(
          children: [
            _buildDashboardCard('Pending', pendingCount, Colors.orange, context),
            const SizedBox(width: 16),
            _buildDashboardCard('In Progress', inProgressCount, Colors.blue, context),
          ],
        ),
        const SizedBox(height: 16),
        _buildDashboardCard('Completed', completedCount, Colors.green, context),
        const SizedBox(height: 16),
        const Text('Recent Inspections', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        ...inspections.take(5).map((inspection) => ListTile(
          title: Text(inspection.inspectionNumber),
          subtitle: Text('Status: ${inspection.status.toString().split('.').last}'),
          onTap: () => onInspectionTap?.call(inspection),
        )).toList(),
      ],
    );
  }

  Widget _buildDashboardCard(String title, int count, Color color, BuildContext context) {
    return Expanded(
      child: Card(
        color: color.withOpacity(0.1),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(color: color, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                count.toString(),
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleSelection(String inspectionId) {
    if (onSelectionChanged == null) return;
    
    final newSelection = List<String>.from(selectedInspectionIds);
    if (newSelection.contains(inspectionId)) {
      newSelection.remove(inspectionId);
    } else {
      newSelection.add(inspectionId);
    }
    onSelectionChanged?.call(newSelection);
  }
  }

/// Individual quality inspection list tile
class QualityInspectionListTile extends StatelessWidget {
  final QualityInspection inspection;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onStatusChanged;

  const QualityInspectionListTile({
    Key? key,
    required this.inspection,
    this.isSelected = false,
    this.onTap,
    this.onLongPress,
    this.onEdit,
    this.onDelete,
    this.onStatusChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      color: isSelected 
          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
          : null,
      elevation: isSelected ? 2 : 1,
      shape: isSelected
          ? RoundedRectangleBorder(
              side: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 1.5,
              ),
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      child: ListTile(
        onTap: onTap,
        onLongPress: onLongPress,
        leading: isSelected
            ? Checkbox(
                value: true,
                onChanged: (_) {},
                activeColor: Theme.of(context).colorScheme.primary,
              )
            : CircleAvatar(
                backgroundColor: _getStatusColor(inspection.status),
                child: Icon(
                  _getStatusIcon(inspection.status),
                  color: Colors.white,
                  size: 20,
                ),
              ),
        title: Text(
          inspection.inspectionNumber,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: isSelected ? Theme.of(context).colorScheme.primary : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Product: ${inspection.product?.name ?? 'N/A'}'),
            Text('Status: ${inspection.status.toString().split('.').last}'),
            if (inspection.completedDate != null)
              Text('Completed: ${_formatDate(inspection.completedDate!)}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (inspection.result?.qualityScore != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getScoreColor(inspection.result!.qualityScore).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${inspection.result!.qualityScore.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: _getScoreColor(inspection.result!.qualityScore),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            if ((onEdit != null || onDelete != null || onStatusChanged != null) && !isSelected) ...[
              const SizedBox(width: 8),
              PopupMenuButton(
                itemBuilder: (context) => [
                  if (onStatusChanged != null && inspection.status != InspectionStatus.completed)
                    PopupMenuItem(
                      value: 'status',
                      onTap: onStatusChanged,
                      child: const Row(
                        children: [
                          Icon(Icons.update, size: 20),
                          SizedBox(width: 8),
                          Text('Update Status'),
                        ],
                      ),
                    ),
                  if (onEdit != null)
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                  if (onDelete != null)
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                ],
                onSelected: (value) {
                  switch (value) {
                    case 'status':
                      onStatusChanged?.call();
                      break;
                    case 'edit':
                      onEdit?.call();
                      break;
                    case 'delete':
                      onDelete?.call();
                      break;
                  }
                },
                icon: const Icon(Icons.more_vert),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(InspectionStatus status) {
    switch (status) {
      case InspectionStatus.scheduled:
        return Colors.blue;
      case InspectionStatus.inProgress:
        return Colors.orange;
      case InspectionStatus.completed:
        return Colors.green;
      case InspectionStatus.onHold:
        return Colors.amber;
      case InspectionStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(InspectionStatus status) {
    switch (status) {
      case InspectionStatus.scheduled:
        return Icons.schedule;
      case InspectionStatus.inProgress:
        return Icons.play_arrow;
      case InspectionStatus.completed:
        return Icons.check_circle;
      case InspectionStatus.onHold:
        return Icons.pause_circle_filled;
      case InspectionStatus.cancelled:
        return Icons.cancel;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Quality inspection grid view
class QualityInspectionGridView extends StatelessWidget {
  final List<QualityInspection> inspections;
  final Function(QualityInspection)? onInspectionTap;
  final bool isLoading;

  const QualityInspectionGridView({
    Key? key,
    required this.inspections,
    this.onInspectionTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (inspections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.verified,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No quality inspections found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: inspections.length,
      itemBuilder: (context, index) {
        final inspection = inspections[index];
        return QualityInspectionCard(
          inspection: inspection,
          onTap: () => onInspectionTap?.call(inspection),
        );
      },
    );
  }
}

/// Individual quality inspection card
class QualityInspectionCard extends StatelessWidget {
  final QualityInspection inspection;
  final VoidCallback? onTap;

  const QualityInspectionCard({
    Key? key,
    required this.inspection,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStatusColor(inspection.status),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      inspection.inspectionNumber,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Order: ${inspection.orderNumber}',
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                'Inspector: ${inspection.inspectorName}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _formatDate(inspection.inspectionDate),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              if (inspection.metrics.firstPassYield != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: inspection.metrics.firstPassYield! / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getScoreColor(inspection.metrics.firstPassYield!),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${inspection.metrics.firstPassYield!.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _getScoreColor(inspection.metrics.firstPassYield!),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(InspectionStatus status) {
    switch (status) {
      case InspectionStatus.scheduled:
        return Colors.blue;
      case InspectionStatus.inProgress:
        return Colors.orange;
      case InspectionStatus.completed:
        return Colors.green;
      case InspectionStatus.onHold:
        return Colors.amber;
      case InspectionStatus.cancelled:
        return Colors.red;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Extension for displaying user-friendly status names
extension InspectionStatusExtension on InspectionStatus {
  String get displayName {
    switch (this) {
      case InspectionStatus.scheduled:
        return 'Scheduled';
      case InspectionStatus.inProgress:
        return 'In Progress';
      case InspectionStatus.completed:
        return 'Completed';
      case InspectionStatus.onHold:
        return 'On Hold';
      case InspectionStatus.cancelled:
        return 'Cancelled';
    }
  }
}
