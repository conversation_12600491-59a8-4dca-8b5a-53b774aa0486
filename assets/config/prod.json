{"apiBaseUrl": "https://api.hmcollection.com", "enableLogging": false, "enableAnalytics": true, "cacheTimeout": 3600, "features": {"biometricAuth": true, "offlineMode": true, "realTimeUpdates": true, "debugMode": false}, "database": {"name": "hm_collection.db", "version": 1}, "notifications": {"enabled": true, "channels": {"orders": "Order Updates", "production": "Production Alerts", "quality": "Quality Control", "inventory": "Inventory Alerts", "system": "System Notifications"}}, "security": {"tokenExpiryMinutes": 30, "refreshTokenExpiryDays": 7, "maxLoginAttempts": 3, "lockoutDurationMinutes": 30}, "ui": {"theme": "system", "language": "en", "dateFormat": "MMM dd, yyyy", "timeFormat": "12h"}}