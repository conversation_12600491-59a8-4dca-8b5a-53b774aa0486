import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/validation_utils.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../auth/presentation/widgets/auth_text_field.dart';
import '../../../auth/presentation/widgets/loading_button.dart';
import '../../domain/entities/user_management.dart';
import '../bloc/user_management_bloc.dart';

/// Create user page
class CreateUserPage extends StatefulWidget {
  const CreateUserPage({super.key});

  @override
  State<CreateUserPage> createState() => _CreateUserPageState();
}

class _CreateUserPageState extends State<CreateUserPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  UserRole _selectedRole = UserRole.sewingOperator;
  Department _selectedDepartment = Department.sewing;
  bool _sendWelcomeEmail = true;
  bool _requirePasswordChange = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<UserManagementBloc>(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Create User'),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _saveUser,
              child: const Text('Save'),
            ),
          ],
        ),
        body: BlocListener<UserManagementBloc, UserManagementState>(
          listener: _handleStateChange,
          child: BlocBuilder<UserManagementBloc, UserManagementState>(
            builder: (context, state) {
              _isLoading = state is UserManagementLoading;
              
              return SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPersonalInfoSection(),
                      const SizedBox(height: 24),
                      _buildAccountInfoSection(),
                      const SizedBox(height: 24),
                      _buildRoleSection(),
                      const SizedBox(height: 24),
                      _buildPasswordSection(),
                      const SizedBox(height: 24),
                      _buildOptionsSection(),
                      const SizedBox(height: 32),
                      _buildCreateButton(),
                      if (state is UserManagementError) ...[
                        const SizedBox(height: 16),
                        _buildErrorMessage(state.message),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return _buildSection(
      title: 'Personal Information',
      icon: Icons.person_outline,
      children: [
        Row(
          children: [
            Expanded(
              child: AuthTextField(
                controller: _firstNameController,
                labelText: 'First Name',
                hintText: 'Enter first name',
                validator: (value) => ValidationUtils.validateRequired(value, 'First name'),
                enabled: !_isLoading,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AuthTextField(
                controller: _lastNameController,
                labelText: 'Last Name',
                hintText: 'Enter last name',
                validator: (value) => ValidationUtils.validateRequired(value, 'Last name'),
                enabled: !_isLoading,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        PhoneTextField(
          controller: _phoneController,
          enabled: !_isLoading,
        ),
      ],
    );
  }

  Widget _buildAccountInfoSection() {
    return _buildSection(
      title: 'Account Information',
      icon: Icons.account_circle_outlined,
      children: [
        AuthTextField(
          controller: _usernameController,
          labelText: 'Username',
          hintText: 'Enter username',
          validator: (value) => ValidationUtils.validateRequired(value, 'Username'),
          enabled: !_isLoading,
        ),
        const SizedBox(height: 16),
        EmailTextField(
          controller: _emailController,
          validator: (value) => ValidationUtils.validateEmail(value),
          enabled: !_isLoading,
        ),
      ],
    );
  }

  Widget _buildRoleSection() {
    return _buildSection(
      title: 'Role & Department',
      icon: Icons.work_outline,
      children: [
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<UserRole>(
                value: _selectedRole,
                decoration: const InputDecoration(
                  labelText: 'Role',
                  border: OutlineInputBorder(),
                ),
                items: UserRole.values.map((role) => DropdownMenuItem(
                  value: role,
                  child: Text(role.displayName),
                )).toList(),
                onChanged: _isLoading ? null : (value) {
                  if (value != null) {
                    setState(() {
                      _selectedRole = value;
                      _selectedDepartment = value.department;
                    });
                  }
                },
                validator: (value) => value == null ? 'Please select a role' : null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<Department>(
                value: _selectedDepartment,
                decoration: const InputDecoration(
                  labelText: 'Department',
                  border: OutlineInputBorder(),
                ),
                items: Department.values.map((dept) => DropdownMenuItem(
                  value: dept,
                  child: Text(dept.displayName),
                )).toList(),
                onChanged: _isLoading ? null : (value) {
                  if (value != null) {
                    setState(() {
                      _selectedDepartment = value;
                    });
                  }
                },
                validator: (value) => value == null ? 'Please select a department' : null,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return _buildSection(
      title: 'Password',
      icon: Icons.lock_outline,
      children: [
        PasswordTextField(
          controller: _passwordController,
          labelText: 'Password',
          hintText: 'Enter password',
          validator: (value) => ValidationUtils.validatePassword(value),
          enabled: !_isLoading,
        ),
        const SizedBox(height: 16),
        PasswordTextField(
          controller: _confirmPasswordController,
          labelText: 'Confirm Password',
          hintText: 'Confirm password',
          validator: (value) {
            if (value != _passwordController.text) {
              return 'Passwords do not match';
            }
            return null;
          },
          enabled: !_isLoading,
        ),
      ],
    );
  }

  Widget _buildOptionsSection() {
    return _buildSection(
      title: 'Options',
      icon: Icons.settings_outlined,
      children: [
        CheckboxListTile(
          title: const Text('Send welcome email'),
          subtitle: const Text('User will receive login credentials via email'),
          value: _sendWelcomeEmail,
          onChanged: _isLoading ? null : (value) {
            setState(() {
              _sendWelcomeEmail = value ?? true;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('Require password change on first login'),
          subtitle: const Text('User must change password when they first log in'),
          value: _requirePasswordChange,
          onChanged: _isLoading ? null : (value) {
            setState(() {
              _requirePasswordChange = value ?? true;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildCreateButton() {
    return SizedBox(
      width: double.infinity,
      child: LoadingButton(
        onPressed: _saveUser,
        isLoading: _isLoading,
        child: const Text('Create User'),
      ),
    );
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.errorLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: AppColors.error,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveUser() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final request = CreateUserRequest(
      username: _usernameController.text.trim(),
      email: _emailController.text.trim(),
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      phoneNumber: _phoneController.text.trim().isEmpty 
          ? null 
          : _phoneController.text.trim(),
      role: _selectedRole,
      department: _selectedDepartment,
      password: _passwordController.text,
      sendWelcomeEmail: _sendWelcomeEmail,
      requirePasswordChange: _requirePasswordChange,
    );

    context.read<UserManagementBloc>().add(CreateUserRequested(request));
  }

  void _handleStateChange(BuildContext context, UserManagementState state) {
    if (state is UserCreated) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('User ${state.user.displayName} created successfully'),
          backgroundColor: AppColors.success,
        ),
      );
      Navigator.of(context).pop(state.user);
    }
  }
}
