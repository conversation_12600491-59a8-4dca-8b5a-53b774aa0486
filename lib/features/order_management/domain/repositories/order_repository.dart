import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:hm_collection/features/order_management/domain/entities/client_entities.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../../shared/enums/common_enums.dart';
import '../models/create_order_request.dart';
import '../entities/order_entities.dart';
import '../usecases/order_usecases.dart';

/// Order repository interface
abstract class OrderRepository {
  /// Get orders with filtering and pagination
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOrders({
    OrderFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get order by ID
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> getOrderById(
    String orderId,
  );

  /// Create new order
  /// 
  /// [request] The request containing order details
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> createOrder(
    CreateOrderRequest request,
  );

  /// Update existing order
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> updateOrder(
    UpdateOrderRequest request,
  );

  /// Delete order
  Future<Either<Failure, ApiVoidResponse>> deleteOrder(
    String orderId,
    String? reason,
  );

  /// Update order status
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> updateOrderStatus(
    String orderId,
    OrderStatus status,
    String? reason,
  );

  /// Add order note
  Future<Either<Failure, ApiResponse<OrderNote>>> addOrderNote(
    AddOrderNoteRequest request,
  );

  /// Update order note
  Future<Either<Failure, ApiResponse<OrderNote>>> updateOrderNote(
    UpdateOrderNoteRequest request,
  );

  /// Delete order note
  Future<Either<Failure, ApiVoidResponse>> deleteOrderNote(
    String noteId,
  );

  /// Add order attachment
  Future<Either<Failure, ApiResponse<OrderAttachment>>> addOrderAttachment(
    AddOrderAttachmentRequest request,
  );

  /// Delete order attachment
  Future<Either<Failure, ApiVoidResponse>> deleteOrderAttachment(
    String attachmentId,
  );

  /// Update order item status
  Future<Either<Failure, ApiResponse<OrderItem>>> updateOrderItemStatus(
    String orderId,
    String itemId,
    OrderItemStatus status,
    int? completedQuantity,
  );

  /// Get order timeline
  Future<Either<Failure, List<OrderTimelineEvent>>> getOrderTimeline(
    String orderId,
  );

  /// Search orders
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> searchOrders({
    required String query,
    OrderFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get orders by client
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOrdersByClient(
    String clientId, {
    PaginationParams? pagination,
  });

  /// Get orders by status
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOrdersByStatus(
    OrderStatus status, {
    PaginationParams? pagination,
  });

  /// Get overdue orders
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOverdueOrders({
    PaginationParams? pagination,
  });

  /// Get urgent orders
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getUrgentOrders({
    PaginationParams? pagination,
  });

  /// Duplicate order
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> duplicateOrder(
    String orderId,
    DuplicateOrderRequest request,
  );

  /// Cancel order
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> cancelOrder(
    String orderId,
    String reason,
  );

  /// Hold order
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> holdOrder(
    String orderId,
    String reason,
  );

  /// Resume order
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> resumeOrder(
    String orderId,
  );

  /// Get order statistics
  Future<Either<Failure, OrderStatistics>> getOrderStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? clientId,
  });

  /// Export orders
  Future<Either<Failure, String>> exportOrders({
    OrderFilterCriteria? filter,
    String format = 'csv',
  });

  /// Import orders
  Future<Either<Failure, ApiResponse<OrderImportResult>>> importOrders(
    String filePath, {
    bool validateOnly = false,
  });

  /// Bulk update orders
  Future<Either<Failure, ApiResponse<BulkUpdateResult>>> bulkUpdateOrders(
    List<BulkUpdateOrderRequest> requests,
  );

  /// Get order production schedule
  Future<Either<Failure, List<OrderProductionSchedule>>> getOrderProductionSchedule(
    String orderId,
  );

  /// Update order production schedule
  Future<Either<Failure, ApiVoidResponse>> updateOrderProductionSchedule(
    String orderId,
    List<OrderProductionSchedule> schedule,
  );
}

/// Client repository interface
abstract class ClientRepository {
  /// Get clients with filtering and pagination
  Future<Either<Failure, ApiListResponse<Client>>> getClients({
    ClientFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get client by ID
  Future<Either<Failure, ApiResponse<Client>>> getClientById(
    String clientId,
  );

  /// Create new client
  Future<Either<Failure, ApiResponse<Client>>> createClient(
    CreateClientRequest request,
  );

  /// Update existing client
  Future<Either<Failure, ApiResponse<Client>>> updateClient(
    UpdateClientRequest request,
  );

  /// Delete client
  Future<Either<Failure, ApiVoidResponse>> deleteClient(
    String clientId,
    String? reason,
  );

  /// Search clients
  Future<Either<Failure, ApiListResponse<Client>>> searchClients({
    required String query,
    ClientFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get client statistics
  Future<Either<Failure, ClientStatistics>> getClientStatistics(
    String clientId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Update client rating
  Future<Either<Failure, ApiResponse<Client>>> updateClientRating(
    String clientId,
    ClientRating rating,
  );

  /// Get client order history
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getClientOrderHistory(
    String clientId, {
    PaginationParams? pagination,
  });

  /// Export clients
  Future<Either<Failure, String>> exportClients({
    ClientFilterCriteria? filter,
    String format = 'csv',
  });

  /// Import clients
  Future<Either<Failure, ApiResponse<ClientImportResult>>> importClients(
    String filePath, {
    bool validateOnly = false,
  });
}

// Request classes

/// Create order item request
class CreateOrderItemRequest extends Equatable {
  final String productId;
  final String productName;
  final String? productCode;
  final String? description;
  final int quantity;
  final String unit;
  final double unitPrice;
  final GarmentSize? size;
  final String? color;
  final String? fabric;
  final Map<String, dynamic> specifications;

  const CreateOrderItemRequest({
    required this.productId,
    required this.productName,
    this.productCode,
    this.description,
    required this.quantity,
    required this.unit,
    required this.unitPrice,
    this.size,
    this.color,
    this.fabric,
    this.specifications = const {},
  });

  @override
  List<Object?> get props => [
        productId,
        productName,
        productCode,
        description,
        quantity,
        unit,
        unitPrice,
        size,
        color,
        fabric,
        specifications,
      ];

  /// Validate request
  bool get isValid {
    return productId.isNotEmpty &&
           productName.isNotEmpty &&
           quantity > 0 &&
           unitPrice >= 0;
  }

  /// Get total price
  double get totalPrice => quantity * unitPrice;
}

/// Update order request
class UpdateOrderRequest extends Equatable {
  final String orderId;
  final String? clientId;
  final OrderType? type;
  final OrderPriority? priority;
  final DateTime? requiredDate;
  final List<UpdateOrderItemRequest>? items;
  final String? assignedMerchandiser;

  const UpdateOrderRequest({
    required this.orderId,
    this.clientId,
    this.type,
    this.priority,
    this.requiredDate,
    this.items,
    this.assignedMerchandiser,
  });

  @override
  List<Object?> get props => [
        orderId,
        clientId,
        type,
        priority,
        requiredDate,
        items,
        assignedMerchandiser,
      ];

  /// Check if request has updates
  bool get hasUpdates {
    return clientId != null ||
           type != null ||
           priority != null ||
           requiredDate != null ||
           items != null ||
           assignedMerchandiser != null;
  }
}

/// Update order item request
class UpdateOrderItemRequest extends Equatable {
  final String? itemId;
  final String? productId;
  final String? productName;
  final String? productCode;
  final String? description;
  final int? quantity;
  final String? unit;
  final double? unitPrice;
  final GarmentSize? size;
  final String? color;
  final String? fabric;
  final Map<String, dynamic>? specifications;

  const UpdateOrderItemRequest({
    this.itemId,
    this.productId,
    this.productName,
    this.productCode,
    this.description,
    this.quantity,
    this.unit,
    this.unitPrice,
    this.size,
    this.color,
    this.fabric,
    this.specifications,
  });

  @override
  List<Object?> get props => [
        itemId,
        productId,
        productName,
        productCode,
        description,
        quantity,
        unit,
        unitPrice,
        size,
        color,
        fabric,
        specifications,
      ];
}

// Additional request and response classes would be defined here...
// For brevity, I'll include the key ones

/// Order statistics
class OrderStatistics extends Equatable {
  final int totalOrders;
  final int pendingOrders;
  final int inProductionOrders;
  final int completedOrders;
  final int overdueOrders;
  final double totalValue;
  final double averageOrderValue;
  final Map<String, int> ordersByStatus;
  final Map<String, int> ordersByPriority;
  final Map<String, double> revenueByMonth;

  const OrderStatistics({
    required this.totalOrders,
    required this.pendingOrders,
    required this.inProductionOrders,
    required this.completedOrders,
    required this.overdueOrders,
    required this.totalValue,
    required this.averageOrderValue,
    required this.ordersByStatus,
    required this.ordersByPriority,
    required this.revenueByMonth,
  });

  @override
  List<Object?> get props => [
        totalOrders,
        pendingOrders,
        inProductionOrders,
        completedOrders,
        overdueOrders,
        totalValue,
        averageOrderValue,
        ordersByStatus,
        ordersByPriority,
        revenueByMonth,
      ];
}

/// Order production schedule
class OrderProductionSchedule extends Equatable {
  final String department;
  final DateTime startDate;
  final DateTime endDate;
  final int estimatedDays;
  final String? assignedTeam;
  final String? notes;

  const OrderProductionSchedule({
    required this.department,
    required this.startDate,
    required this.endDate,
    required this.estimatedDays,
    this.assignedTeam,
    this.notes,
  });

  @override
  List<Object?> get props => [
        department,
        startDate,
        endDate,
        estimatedDays,
        assignedTeam,
        notes,
      ];
}

// Additional classes for client management, import/export, etc. would be defined here...
