import 'package:equatable/equatable.dart';

/// Represents anomaly detection data for analytics
class AnomalyDetectionData extends Equatable {
  /// The list of detected anomalies with their details
  final List<AnomalyPoint> anomalies;
  
  /// The metric being analyzed for anomalies
  final String metric;
  
  /// The time period the analysis covers (e.g., 'daily', 'weekly', 'monthly')
  final String timePeriod;
  
  /// The total number of anomalies detected
  final int totalAnomalies;
  
  /// The anomaly rate as a percentage
  final double anomalyRate;
  
  /// The detection accuracy percentage
  final double detectionAccuracy;
  
  /// The timestamp when the analysis was performed
  final DateTime analyzedAt;

  const AnomalyDetectionData({
    required this.anomalies,
    required this.metric,
    required this.timePeriod,
    required this.totalAnomalies,
    required this.anomalyRate,
    required this.detectionAccuracy,
    required this.analyzedAt,
  });

  @override
  List<Object?> get props => [
        anomalies,
        metric,
        timePeriod,
        totalAnomalies,
        anomalyRate,
        detectionAccuracy,
        analyzedAt,
      ];
}

/// Represents a single detected anomaly
class AnomalyPoint extends Equatable {
  /// The timestamp when the anomaly was detected
  final DateTime timestamp;
  
  /// The actual value that was detected as an anomaly
  final double value;
  
  /// The expected value (what was predicted)
  final double expectedValue;
  
  /// The deviation from the expected value
  final double deviation;
  
  /// The severity of the anomaly ('low', 'medium', 'high')
  final String severity;
  
  /// The type of anomaly ('spike' for values above expected, 'dip' for values below)
  final String type;

  const AnomalyPoint({
    required this.timestamp,
    required this.value,
    required this.expectedValue,
    required this.deviation,
    required this.severity,
    required this.type,
  });

  @override
  List<Object?> get props => [
        timestamp,
        value,
        expectedValue,
        deviation,
        severity,
        type,
      ];
}
