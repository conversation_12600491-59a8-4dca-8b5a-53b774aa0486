import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// User creation request
class CreateUserRequest extends Equatable {
  final String username;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final UserRole role;
  final Department department;
  final String? password;
  final bool sendWelcomeEmail;
  final bool requirePasswordChange;
  final List<String> additionalPermissions;

  const CreateUserRequest({
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    required this.role,
    required this.department,
    this.password,
    this.sendWelcomeEmail = true,
    this.requirePasswordChange = true,
    this.additionalPermissions = const [],
  });

  /// Validate the request
  bool get isValid {
    return username.isNotEmpty &&
           email.isNotEmpty &&
           firstName.isNotEmpty &&
           lastName.isNotEmpty;
  }

  @override
  List<Object?> get props => [
        username,
        email,
        firstName,
        lastName,
        phoneNumber,
        role,
        department,
        password,
        sendWelcomeEmail,
        requirePasswordChange,
        additionalPermissions,
      ];
}

/// User update request
class UpdateUserRequest extends Equatable {
  final String userId;
  final String? username;
  final String? email;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final UserRole? role;
  final Department? department;
  final CommonStatus? status;
  final bool? isActive;
  final List<String>? permissions;

  const UpdateUserRequest({
    required this.userId,
    this.username,
    this.email,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.profileImageUrl,
    this.role,
    this.department,
    this.status,
    this.isActive,
    this.permissions,
  });

  /// Check if request has any updates
  bool get hasUpdates {
    return username != null ||
           email != null ||
           firstName != null ||
           lastName != null ||
           phoneNumber != null ||
           profileImageUrl != null ||
           role != null ||
           department != null ||
           status != null ||
           isActive != null ||
           permissions != null;
  }

  @override
  List<Object?> get props => [
        userId,
        username,
        email,
        firstName,
        lastName,
        phoneNumber,
        profileImageUrl,
        role,
        department,
        status,
        isActive,
        permissions,
      ];
}

/// Role assignment request
class RoleAssignmentRequest extends Equatable {
  final String userId;
  final UserRole role;
  final Department department;
  final String? reason;
  final DateTime? effectiveDate;
  final List<String>? additionalPermissions;

  const RoleAssignmentRequest({
    required this.userId,
    required this.role,
    required this.department,
    this.reason,
    this.effectiveDate,
    this.additionalPermissions,
  });

  @override
  List<Object?> get props => [
        userId,
        role,
        department,
        reason,
        effectiveDate,
        additionalPermissions,
      ];
}

/// Permission assignment request
class PermissionAssignmentRequest extends Equatable {
  final String userId;
  final List<String> permissions;
  final bool isGrant; // true for grant, false for revoke
  final String? reason;

  const PermissionAssignmentRequest({
    required this.userId,
    required this.permissions,
    required this.isGrant,
    this.reason,
  });

  @override
  List<Object?> get props => [userId, permissions, isGrant, reason];
}

/// User filter criteria
class UserFilterCriteria extends Equatable {
  final String? searchQuery;
  final UserRole? role;
  final Department? department;
  final CommonStatus? status;
  final bool? isActive;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final DateTime? lastLoginAfter;
  final DateTime? lastLoginBefore;

  const UserFilterCriteria({
    this.searchQuery,
    this.role,
    this.department,
    this.status,
    this.isActive,
    this.createdAfter,
    this.createdBefore,
    this.lastLoginAfter,
    this.lastLoginBefore,
  });

  /// Convert to query parameters
  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = {};

    if (searchQuery != null && searchQuery!.isNotEmpty) {
      params['search'] = searchQuery;
    }
    if (role != null) {
      params['role'] = role!.value;
    }
    if (department != null) {
      params['department'] = department!.value;
    }
    if (status != null) {
      params['status'] = status!.value;
    }
    if (isActive != null) {
      params['is_active'] = isActive;
    }
    if (createdAfter != null) {
      params['created_after'] = createdAfter!.toIso8601String();
    }
    if (createdBefore != null) {
      params['created_before'] = createdBefore!.toIso8601String();
    }
    if (lastLoginAfter != null) {
      params['last_login_after'] = lastLoginAfter!.toIso8601String();
    }
    if (lastLoginBefore != null) {
      params['last_login_before'] = lastLoginBefore!.toIso8601String();
    }

    return params;
  }

  @override
  List<Object?> get props => [
        searchQuery,
        role,
        department,
        status,
        isActive,
        createdAfter,
        createdBefore,
        lastLoginAfter,
        lastLoginBefore,
      ];
}

/// User statistics
class UserStatistics extends Equatable {
  final int totalUsers;
  final int activeUsers;
  final int inactiveUsers;
  final Map<String, int> usersByRole;
  final Map<String, int> usersByDepartment;
  final Map<String, int> usersByStatus;
  final int usersLoggedInToday;
  final int usersLoggedInThisWeek;
  final int usersLoggedInThisMonth;

  const UserStatistics({
    required this.totalUsers,
    required this.activeUsers,
    required this.inactiveUsers,
    required this.usersByRole,
    required this.usersByDepartment,
    required this.usersByStatus,
    required this.usersLoggedInToday,
    required this.usersLoggedInThisWeek,
    required this.usersLoggedInThisMonth,
  });

  @override
  List<Object?> get props => [
        totalUsers,
        activeUsers,
        inactiveUsers,
        usersByRole,
        usersByDepartment,
        usersByStatus,
        usersLoggedInToday,
        usersLoggedInThisWeek,
        usersLoggedInThisMonth,
      ];
}

/// User activity log
class UserActivityLog extends BaseEntity {
  final String userId;
  final String action;
  final String? description;
  final Map<String, dynamic>? metadata;
  final String? ipAddress;
  final String? userAgent;
  final String? deviceInfo;

  const UserActivityLog({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.userId,
    required this.action,
    this.description,
    this.metadata,
    this.ipAddress,
    this.userAgent,
    this.deviceInfo,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        userId,
        action,
        description,
        metadata,
        ipAddress,
        userAgent,
        deviceInfo,
      ];
}

/// Permission definition
class Permission extends Equatable {
  final String name;
  final String displayName;
  final String description;
  final String category;
  final bool isSystemPermission;

  const Permission({
    required this.name,
    required this.displayName,
    required this.description,
    required this.category,
    this.isSystemPermission = false,
  });

  @override
  List<Object?> get props => [
        name,
        displayName,
        description,
        category,
        isSystemPermission,
      ];
}

/// Role definition with permissions
class RoleDefinition extends Equatable {
  final UserRole role;
  final String displayName;
  final String description;
  final Department department;
  final List<String> permissions;
  final int hierarchyLevel;
  final bool canManageUsers;
  final bool canAssignTasks;
  final bool canViewReports;

  const RoleDefinition({
    required this.role,
    required this.displayName,
    required this.description,
    required this.department,
    required this.permissions,
    required this.hierarchyLevel,
    this.canManageUsers = false,
    this.canAssignTasks = false,
    this.canViewReports = false,
  });

  @override
  List<Object?> get props => [
        role,
        displayName,
        description,
        department,
        permissions,
        hierarchyLevel,
        canManageUsers,
        canAssignTasks,
        canViewReports,
      ];
}
