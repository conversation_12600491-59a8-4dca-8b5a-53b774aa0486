import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:hm_collection/features/auth/presentation/pages/admin_creation_page.dart';
import 'package:hm_collection/features/auth/presentation/pages/admin_login_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/admin_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/administrator_dashboard_page.dart';
import '../../features/dashboard/presentation/pages/admin_dashboard_page.dart';
import '../../features/dashboard/presentation/pages/manager_dashboard_page.dart';
import '../../features/dashboard/presentation/pages/viewer_dashboard_page.dart';
import '../../features/manufacturing/presentation/pages/manufacturing_dashboard_page.dart';

import '../auth/widgets/auth_app_wrapper.dart';
import '../auth/widgets/auth_guard.dart' as guard;
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/firebase_login_page.dart';
import '../../features/auth/presentation/pages/firebase_signup_page.dart';
import '../../features/auth/presentation/pages/cutting_master_login_page.dart';
import '../../features/dashboard/presentation/pages/role_based_dashboard.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/dashboard/presentation/pages/cutting_master_dashboard_page.dart';
import '../../features/order_management/presentation/pages/orders_page.dart';
import '../../features/production_planning/presentation/pages/production_page.dart';
import '../../features/quality_control/presentation/pages/quality_page.dart';
import '../../features/inventory_management/presentation/pages/inventory_page.dart';
import '../../features/financial_management/presentation/pages/financial_dashboard_page.dart';
import '../../features/analytics_reporting/presentation/pages/analytics_page.dart';
import '../../features/collaboration/presentation/pages/collaboration_page.dart';
import '../../features/manufacturing/presentation/pages/department_management_page.dart';
import '../../features/manufacturing/presentation/pages/task_management_page.dart';
import '../../features/manufacturing/presentation/pages/worker_management_page.dart';
import '../../features/manufacturing/presentation/pages/production_monitoring_page.dart';
import '../../features/auth/presentation/pages/manufacturing_auth_page.dart';
import '../../features/auth/presentation/screens/two_factor_setup_screen.dart';
import '../../features/auth/presentation/pages/post_signup_screen.dart';
import '../../features/numbering/presentation/pages/numbering_page.dart';

/// App router configuration
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final authBloc = GetIt.instance<AuthBloc>();
      final isAuthenticated = authBloc.state is AuthAuthenticated;
      const publicRoutes = [
        '/login',
        '/firebase-login',
        '/signup',
        '/post-signup',
        '/admin-creation',
        '/manufacturing-auth',
      ];
      final isPublicRoute = publicRoutes.contains(state.matchedLocation);

      if (isAuthenticated && isPublicRoute) {
        return '/dashboard';
      }

      if (!isAuthenticated && !isPublicRoute) {
        return '/firebase-login';
      }

      return null;
    },
    routes: [
      // Root route - Authentication wrapper
      GoRoute(
        path: '/',
        name: 'root',
        builder: (context, state) => const AuthAppWrapper(),
      ),

      // Auth routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const FirebaseLoginPage(),
      ),
      GoRoute(
        path: '/firebase-login',
        name: 'firebase-login',
        builder: (context, state) => const FirebaseLoginPage(),
      ),
      GoRoute(
        path: '/signup',
        name: 'signup',
        builder: (context, state) => const FirebaseSignUpPage(),
      ),
      GoRoute(
        path: '/post-signup',
        name: 'post-signup',
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          final emailVerified =
              state.uri.queryParameters['verified'] == 'false';
          final requiresApproval =
              state.uri.queryParameters['requiresApproval'] == 'false';
          return PostSignupScreen(
            email: email,
            emailVerified: emailVerified,
            requiresAdminApproval: requiresApproval,
          );
        },
      ),
      GoRoute(
        path: '/admin-creation',
        name: 'admin-creation',
        builder: (context, state) => const FirebaseLoginPage(),
      ),
      // GoRoute(
      //   path: '/setup-2fa',
      //   name: 'setup-2fa',
      //   builder: (context, state) => const TwoFactorSetupScreen(),
      // ),
      GoRoute(
        path: '/manufacturing-auth',
        name: 'manufacturing-auth',
        builder: (context, state) => const ManufacturingAuthPage(),
      ),
      GoRoute(
        path: '/cutting-master-login',
        name: 'cutting-master-login',
        builder: (context, state) => const CuttingMasterLoginPage(),
      ),

      // Protected routes
      ShellRoute(
        builder: (context, state, child) {
          return guard.AuthGuard(child: child);
        },
        routes: [
          // Dashboard
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const RoleBasedDashboard(),
          ),
          GoRoute(
            path: '/admin_dashboard',
            name: 'admin_dashboard',
            builder: (context, state) => const AdministratorDashboardPage(),
          ),
          GoRoute(
            path: '/merchandiser_dashboard',
            name: 'merchandiser_dashboard',
            builder: (context, state) => const ManagerDashboardPage(),
          ),
          GoRoute(
            path: '/viewer_dashboard',
            name: 'viewer_dashboard',
            builder: (context, state) => const ViewerDashboardPage(),
          ),
          GoRoute(
            path: '/cutting-master-dashboard',
            name: 'cutting-master-dashboard',
            builder: (context, state) => const CuttingMasterDashboardPage(),
          ),
          GoRoute(
            path: '/admin_dashboard',
            name: 'admin_dashboard',
            builder: (context, state) => const AdministratorDashboardPage(),
          ),

          // Orders Management
          GoRoute(
            path: '/orders',
            name: 'orders',
            builder: (context, state) => const OrdersPage(),
            routes: [
              GoRoute(
                path: '/create',
                name: 'create-order',
                builder: (context, state) =>
                    const OrdersPage(), // Will show create form
              ),
              GoRoute(
                path: '/:orderId',
                name: 'order-details',
                builder: (context, state) {
                  final orderId = state.pathParameters['orderId']!;
                  return const OrdersPage(); // Will show order details
                },
              ),
              GoRoute(
                path: '/:orderId/edit',
                name: 'edit-order',
                builder: (context, state) {
                  final orderId = state.pathParameters['orderId']!;
                  return const OrdersPage(); // Will show edit form
                },
              ),
            ],
          ),

          // Production Management
          GoRoute(
            path: '/production',
            name: 'production',
            builder: (context, state) => const ProductionPage(),
            routes: [
              GoRoute(
                path: '/planning',
                name: 'production-planning',
                builder: (context, state) => const ProductionPage(),
              ),
              GoRoute(
                path: '/monitoring',
                name: 'production-monitoring',
                builder: (context, state) => const ProductionPage(),
              ),
              GoRoute(
                path: '/schedules',
                name: 'production-schedules',
                builder: (context, state) => const ProductionPage(),
              ),
            ],
          ),

          // Numbering Management
          GoRoute(
            path: '/numbering',
            name: 'numbering',
            builder: (context, state) => const NumberingPage(),
          ),

          // Quality Control
          GoRoute(
            path: '/quality',
            name: 'quality',
            builder: (context, state) => const QualityPage(),
            routes: [
              GoRoute(
                path: '/inspections',
                name: 'quality-inspections',
                builder: (context, state) => const QualityPage(),
              ),
              GoRoute(
                path: '/standards',
                name: 'quality-standards',
                builder: (context, state) => const QualityPage(),
              ),
              GoRoute(
                path: '/reports',
                name: 'quality-reports',
                builder: (context, state) => const QualityPage(),
              ),
            ],
          ),

          // Inventory Management
          GoRoute(
            path: '/inventory',
            name: 'inventory',
            builder: (context, state) => const InventoryPage(),
            routes: [
              GoRoute(
                path: '/materials',
                name: 'inventory-materials',
                builder: (context, state) => const InventoryPage(),
              ),
              GoRoute(
                path: '/products',
                name: 'inventory-products',
                builder: (context, state) => const InventoryPage(),
              ),
              GoRoute(
                path: '/tracking',
                name: 'inventory-tracking',
                builder: (context, state) => const InventoryPage(),
              ),
            ],
          ),

          // Financial Management
          GoRoute(
            path: '/financial',
            name: 'financial',
            builder: (context, state) => const FinancialDashboardPage(),
            routes: [
              GoRoute(
                path: '/accounting',
                name: 'financial-accounting',
                builder: (context, state) => const FinancialDashboardPage(),
              ),
              GoRoute(
                path: '/budgeting',
                name: 'financial-budgeting',
                builder: (context, state) => const FinancialDashboardPage(),
              ),
              GoRoute(
                path: '/reports',
                name: 'financial-reports',
                builder: (context, state) => const FinancialDashboardPage(),
              ),
            ],
          ),

          // Analytics & Reporting
          GoRoute(
            path: '/analytics',
            name: 'analytics',
            builder: (context, state) => const AnalyticsPage(),
            routes: [
              GoRoute(
                path: '/dashboards',
                name: 'analytics-dashboards',
                builder: (context, state) => const AnalyticsPage(),
              ),
              GoRoute(
                path: '/reports',
                name: 'analytics-reports',
                builder: (context, state) => const AnalyticsPage(),
              ),
              GoRoute(
                path: '/insights',
                name: 'analytics-insights',
                builder: (context, state) => const AnalyticsPage(),
              ),
            ],
          ),

          // Team Collaboration
          GoRoute(
            path: '/collaboration',
            name: 'collaboration',
            builder: (context, state) => const CollaborationPage(),
            routes: [
              GoRoute(
                path: '/workspaces',
                name: 'collaboration-workspaces',
                builder: (context, state) => const CollaborationPage(),
              ),
              GoRoute(
                path: '/messages',
                name: 'collaboration-messages',
                builder: (context, state) => const CollaborationPage(),
              ),
              GoRoute(
                path: '/sessions',
                name: 'collaboration-sessions',
                builder: (context, state) => const CollaborationPage(),
              ),
            ],
          ),

          // Manufacturing Management
          GoRoute(
            path: '/manufacturing',
            name: 'manufacturing',
            builder: (context, state) => const ManufacturingDashboardPage(),
            routes: [
              GoRoute(
                path: '/departments',
                name: 'manufacturing-departments',
                builder: (context, state) => const DepartmentManagementPage(),
              ),
              GoRoute(
                path: '/tasks',
                name: 'manufacturing-tasks',
                builder: (context, state) => const TaskManagementPage(),
              ),
              GoRoute(
                path: '/workers',
                name: 'manufacturing-workers',
                builder: (context, state) => const WorkerManagementPage(),
              ),
              GoRoute(
                path: '/production',
                name: 'manufacturing-production',
                builder: (context, state) => const ProductionMonitoringPage(),
              ),
            ],
          ),

          // Settings and Profile
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
            routes: [
              GoRoute(
                path: '/profile',
                name: 'settings-profile',
                builder: (context, state) => const SettingsPage(),
              ),
              GoRoute(
                path: '/preferences',
                name: 'settings-preferences',
                builder: (context, state) => const SettingsPage(),
              ),
              GoRoute(
                path: '/security',
                name: 'settings-security',
                builder: (context, state) => const SettingsPage(),
              ),
            ],
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorPage(error: state.error.toString()),
  );
}

/// Settings page placeholder
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: const Center(
        child: Text('Settings page will be implemented here'),
      ),
    );
  }
}

/// Error page for routing errors
class ErrorPage extends StatelessWidget {
  final String error;

  const ErrorPage({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Page Not Found',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    );
  }
}
