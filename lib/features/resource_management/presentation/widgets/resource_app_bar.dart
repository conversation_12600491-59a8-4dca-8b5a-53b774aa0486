import 'package:flutter/material.dart';
import 'package:hm_collection/features/resource_management/presentation/bloc/resource_bloc.dart';



/// Custom app bar for resource management pages
class ResourceAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Function(String)? onSearchPressed;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onAdd;
  final Function(ResourceViewMode)? onViewModeChanged;
  final ResourceViewMode? currentViewMode;
  final ResourceType? currentResourceType;
  final bool showSearch;
  final bool showFilter;
  final bool showAdd;

  const ResourceAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.onSearchPressed,
    this.onFilterPressed,
    this.onAdd,
    this.onViewModeChanged,
    this.currentViewMode,
    this.currentResourceType,
    this.showSearch = true,
    this.showFilter = true,
    this.showAdd = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (showSearch)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
            tooltip: 'Search',
          ),
        if (showFilter)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: onFilterPressed,
            tooltip: 'Filter',
          ),
        if (onViewModeChanged != null && currentViewMode != null)
          PopupMenuButton<ResourceViewMode>(
            icon: const Icon(Icons.view_list),
            tooltip: 'Change View Mode',
            onSelected: onViewModeChanged,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: ResourceViewMode.list,
                child: ListTile(
                  leading: Icon(
                    Icons.list,
                    color: currentViewMode == ResourceViewMode.list
                        ? Theme.of(context).primaryColor
                        : null,
                  ),
                  title: const Text('List View'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              PopupMenuItem(
                value: ResourceViewMode.grid,
                child: ListTile(
                  leading: Icon(
                    Icons.grid_view,
                    color: currentViewMode == ResourceViewMode.grid
                        ? Theme.of(context).primaryColor
                        : null,
                  ),
                  title: const Text('Grid View'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              PopupMenuItem(
                value: ResourceViewMode.kanban,
                child: ListTile(
                  leading: Icon(
                    Icons.view_kanban,
                    color: currentViewMode == ResourceViewMode.kanban
                        ? Theme.of(context).primaryColor
                        : null,
                  ),
                  title: const Text('Kanban View'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        if (showAdd)
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: onAdd,
            tooltip: 'Add ${currentResourceType?.displayName ?? 'Resource'}',
          ),
        ...?actions,
      ],
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Search ${currentResourceType?.displayName ?? 'Resources'}'),
        content: TextField(
          autofocus: true,
          decoration: InputDecoration(
            hintText: 'Enter search term...',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onSubmitted: (value) {
            Navigator.of(context).pop();
            onSearchPressed?.call(value);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}