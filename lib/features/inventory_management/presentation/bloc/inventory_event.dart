part of 'inventory_bloc.dart';

/// Base inventory event
abstract class InventoryEvent extends Equatable {
  const InventoryEvent();

  @override
  List<Object?> get props => [];
}

/// Load inventory items
class LoadInventoryItemsRequested extends InventoryEvent {
  final InventoryFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadInventoryItemsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Refresh inventory items
class RefreshInventoryItemsRequested extends InventoryEvent {
  const RefreshInventoryItemsRequested();
}

/// Load inventory item details
class LoadInventoryItemDetailsRequested extends InventoryEvent {
  final String itemId;

  const LoadInventoryItemDetailsRequested(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

/// Create inventory item
class CreateInventoryItemRequested extends InventoryEvent {
  final CreateInventoryItemRequest request;

  const CreateInventoryItemRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update inventory item
class UpdateInventoryItemRequested extends InventoryEvent {
  final UpdateInventoryItemRequest request;

  const UpdateInventoryItemRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete inventory item
class DeleteInventoryItemRequested extends InventoryEvent {
  final String itemId;
  final String? reason;

  const DeleteInventoryItemRequested(this.itemId, {this.reason});

  @override
  List<Object?> get props => [itemId, reason];
}

/// Update stock level
class UpdateStockLevelRequested extends InventoryEvent {
  final String itemId;
  final double newStock;
  final String reason;

  const UpdateStockLevelRequested(
    this.itemId,
    this.newStock,
    this.reason,
  );

  @override
  List<Object?> get props => [itemId, newStock, reason];
}

/// Adjust stock
class AdjustStockRequested extends InventoryEvent {
  final AdjustStockRequest request;

  const AdjustStockRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Reserve stock
class ReserveStockRequested extends InventoryEvent {
  final String itemId;
  final double quantity;
  final String reason;
  final String? orderId;

  const ReserveStockRequested(
    this.itemId,
    this.quantity,
    this.reason, {
    this.orderId,
  });

  @override
  List<Object?> get props => [itemId, quantity, reason, orderId];
}

/// Release reserved stock
class ReleaseReservedStockRequested extends InventoryEvent {
  final String itemId;
  final double quantity;
  final String reason;

  const ReleaseReservedStockRequested(
    this.itemId,
    this.quantity,
    this.reason,
  );

  @override
  List<Object?> get props => [itemId, quantity, reason];
}

/// Load stock movements
class LoadStockMovementsRequested extends InventoryEvent {
  final StockMovementFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadStockMovementsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load stock movements for item
class LoadStockMovementsForItemRequested extends InventoryEvent {
  final String itemId;
  final PaginationParams? pagination;

  const LoadStockMovementsForItemRequested(
    this.itemId, {
    this.pagination,
  });

  @override
  List<Object?> get props => [itemId, pagination];
}

/// Load low stock items
class LoadLowStockItemsRequested extends InventoryEvent {
  final PaginationParams? pagination;

  const LoadLowStockItemsRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Load out of stock items
class LoadOutOfStockItemsRequested extends InventoryEvent {
  final PaginationParams? pagination;

  const LoadOutOfStockItemsRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Load overstocked items
class LoadOverstockedItemsRequested extends InventoryEvent {
  final PaginationParams? pagination;

  const LoadOverstockedItemsRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Load expiring items
class LoadExpiringItemsRequested extends InventoryEvent {
  final int daysAhead;
  final PaginationParams? pagination;

  const LoadExpiringItemsRequested({
    this.daysAhead = 30,
    this.pagination,
  });

  @override
  List<Object?> get props => [daysAhead, pagination];
}

/// Search inventory items
class SearchInventoryItemsRequested extends InventoryEvent {
  final String query;
  final InventoryFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchInventoryItemsRequested(
    this.query, {
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Filter inventory items
class FilterInventoryItemsRequested extends InventoryEvent {
  final InventoryFilterCriteria filter;
  final PaginationParams? pagination;

  const FilterInventoryItemsRequested(
    this.filter, {
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load inventory statistics
class LoadInventoryStatisticsRequested extends InventoryEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? categoryId;

  const LoadInventoryStatisticsRequested({
    this.startDate,
    this.endDate,
    this.categoryId,
  });

  @override
  List<Object?> get props => [startDate, endDate, categoryId];
}

/// Load inventory valuation
class LoadInventoryValuationRequested extends InventoryEvent {
  final DateTime? asOfDate;
  final String? categoryId;

  const LoadInventoryValuationRequested({
    this.asOfDate,
    this.categoryId,
  });

  @override
  List<Object?> get props => [asOfDate, categoryId];
}

/// Generate reorder suggestions
class GenerateReorderSuggestionsRequested extends InventoryEvent {
  final String? categoryId;
  final int daysAhead;

  const GenerateReorderSuggestionsRequested({
    this.categoryId,
    this.daysAhead = 30,
  });

  @override
  List<Object?> get props => [categoryId, daysAhead];
}

/// Create automatic reorder
class CreateAutomaticReorderRequested extends InventoryEvent {
  final List<String> itemIds;

  const CreateAutomaticReorderRequested(this.itemIds);

  @override
  List<Object?> get props => [itemIds];
}

/// Clear inventory state
class ClearInventoryState extends InventoryEvent {
  const ClearInventoryState();
}

/// Load more inventory items (pagination)
class LoadMoreInventoryItemsRequested extends InventoryEvent {
  const LoadMoreInventoryItemsRequested();
}

/// Sort inventory items
class SortInventoryItemsRequested extends InventoryEvent {
  final String sortBy;
  final bool ascending;

  const SortInventoryItemsRequested(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Select inventory item
class SelectInventoryItemRequested extends InventoryEvent {
  final String itemId;

  const SelectInventoryItemRequested(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

/// Deselect inventory item
class DeselectInventoryItemRequested extends InventoryEvent {
  final String itemId;

  const DeselectInventoryItemRequested(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

/// Select all inventory items
class SelectAllInventoryItemsRequested extends InventoryEvent {
  const SelectAllInventoryItemsRequested();
}

/// Deselect all inventory items
class DeselectAllInventoryItemsRequested extends InventoryEvent {
  const DeselectAllInventoryItemsRequested();
}

/// Toggle inventory item selection
class ToggleInventoryItemSelectionRequested extends InventoryEvent {
  final String itemId;

  const ToggleInventoryItemSelectionRequested(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

/// Change inventory view mode
class ChangeInventoryViewModeRequested extends InventoryEvent {
  final InventoryViewMode viewMode;

  const ChangeInventoryViewModeRequested(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Inventory view mode enum
enum InventoryViewMode {
  list,
  grid,
  card,
  table,
}

/// Inventory view mode extension
extension InventoryViewModeExtension on InventoryViewMode {
  String get displayName {
    switch (this) {
      case InventoryViewMode.list:
        return 'List';
      case InventoryViewMode.grid:
        return 'Grid';
      case InventoryViewMode.card:
        return 'Card';
      case InventoryViewMode.table:
        return 'Table';
    }
  }

  IconData get icon {
    switch (this) {
      case InventoryViewMode.list:
        return Icons.list;
      case InventoryViewMode.grid:
        return Icons.grid_view;
      case InventoryViewMode.card:
        return Icons.view_agenda;
      case InventoryViewMode.table:
        return Icons.table_chart;
    }
  }
}
