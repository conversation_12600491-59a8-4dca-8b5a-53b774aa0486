import 'package:equatable/equatable.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Munda record entity representing a single data entry
class MundaRecord extends AuditableEntity {
  /// Serial number (auto-generated)
  final int serialNumber;
  
  /// Entry date
  final DateTime entryDate;
  
  /// Design number of fabric/garment
  final String designNumber;
  
  /// Lot or batch number of fabric
  final String lotNumber;
  
  /// Roll identification number
  final String rollNumber;
  
  /// Type of thread used
  final ThreadType threadType;
  
  /// Custom thread description if not in enum
  final String? customThread;
  
  /// Number of pieces from that roll
  final int pieces;
  
  /// Rate per piece or per roll in rupees
  final double rate;
  
  /// Auto-calculated amount (pieces × rate)
  final double amount;
  
  /// Staff name who checked
  final String checkedBy;
  
  /// Extra notes/comments
  final String? remarks;
  
  /// Associated task ID if this record is part of a task
  final String? taskId;
  
  /// User ID who created this record
  final String userId;
  
  /// Status of the record
  final CommonStatus status;

  const MundaRecord({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    super.createdBy,
    super.updatedBy,
    super.deletedBy,
    super.version = 1,
    required this.serialNumber,
    required this.entryDate,
    required this.designNumber,
    required this.lotNumber,
    required this.rollNumber,
    required this.threadType,
    this.customThread,
    required this.pieces,
    required this.rate,
    required this.amount,
    required this.checkedBy,
    this.remarks,
    this.taskId,
    required this.userId,
    required this.status,
  });

  /// Get thread display name
  String get threadDisplayName {
    if (threadType == ThreadType.blended && customThread != null) {
      return customThread!;
    }
    return threadType.displayName;
  }

  /// Check if record is valid
  bool get isValid {
    return designNumber.isNotEmpty &&
           lotNumber.isNotEmpty &&
           rollNumber.isNotEmpty &&
           pieces > 0 &&
           rate > 0 &&
           checkedBy.isNotEmpty;
  }

  /// Check if record is active
  @override
  bool get isActive => status == CommonStatus.active && !isDeleted;

  /// Copy with new values
  MundaRecord copyWith({
    int? serialNumber,
    DateTime? entryDate,
    String? designNumber,
    String? lotNumber,
    String? rollNumber,
    ThreadType? threadType,
    String? customThread,
    int? pieces,
    double? rate,
    double? amount,
    String? checkedBy,
    String? remarks,
    String? taskId,
    String? userId,
    CommonStatus? status,
    DateTime? updatedAt,
    String? updatedBy,
    int? version,
  }) {
    return MundaRecord(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy,
      version: version ?? this.version + 1,
      serialNumber: serialNumber ?? this.serialNumber,
      entryDate: entryDate ?? this.entryDate,
      designNumber: designNumber ?? this.designNumber,
      lotNumber: lotNumber ?? this.lotNumber,
      rollNumber: rollNumber ?? this.rollNumber,
      threadType: threadType ?? this.threadType,
      customThread: customThread ?? this.customThread,
      pieces: pieces ?? this.pieces,
      rate: rate ?? this.rate,
      amount: amount ?? this.amount,
      checkedBy: checkedBy ?? this.checkedBy,
      remarks: remarks ?? this.remarks,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      status: status ?? this.status,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'serialNumber': serialNumber,
      'entryDate': entryDate,
      'designNumber': designNumber,
      'lotNumber': lotNumber,
      'rollNumber': rollNumber,
      'threadType': threadType.value,
      'customThread': customThread,
      'pieces': pieces,
      'rate': rate,
      'amount': amount,
      'checkedBy': checkedBy,
      'remarks': remarks,
      'taskId': taskId,
      'userId': userId,
      'status': status.value,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'deletedBy': deletedBy,
      'version': version,
    };
  }

  /// Create from Firestore document
  factory MundaRecord.fromFirestore(String id, Map<String, dynamic> data) {
    return MundaRecord(
      id: id,
      createdAt: (data['createdAt'] as DateTime?) ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as DateTime?) ?? DateTime.now(),
      deletedAt: data['deletedAt'] as DateTime?,
      createdBy: data['createdBy'] as String?,
      updatedBy: data['updatedBy'] as String?,
      deletedBy: data['deletedBy'] as String?,
      version: data['version'] as int? ?? 1,
      serialNumber: data['serialNumber'] as int,
      entryDate: data['entryDate'] as DateTime,
      designNumber: data['designNumber'] as String,
      lotNumber: data['lotNumber'] as String,
      rollNumber: data['rollNumber'] as String,
      threadType: ThreadType.fromString(data['threadType'] as String),
      customThread: data['customThread'] as String?,
      pieces: data['pieces'] as int,
      rate: (data['rate'] as num).toDouble(),
      amount: (data['amount'] as num).toDouble(),
      checkedBy: data['checkedBy'] as String,
      remarks: data['remarks'] as String?,
      taskId: data['taskId'] as String?,
      userId: data['userId'] as String,
      status: CommonStatus.fromString(data['status'] as String),
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        serialNumber,
        entryDate,
        designNumber,
        lotNumber,
        rollNumber,
        threadType,
        customThread,
        pieces,
        rate,
        amount,
        checkedBy,
        remarks,
        taskId,
        userId,
        status,
      ];

  @override
  String toString() {
    return 'MundaRecord(id: $id, serialNumber: $serialNumber, designNumber: $designNumber, lotNumber: $lotNumber, rollNumber: $rollNumber, pieces: $pieces, amount: $amount)';
  }
}

/// Munda record summary for aggregated data
class MundaRecordSummary extends Equatable {
  /// Total number of rolls
  final int totalRolls;
  
  /// Total number of pieces
  final int totalPieces;
  
  /// Total amount in rupees
  final double totalAmount;
  
  /// Date range for the summary
  final DateTime? fromDate;
  final DateTime? toDate;
  
  /// User ID for user-specific summary
  final String? userId;

  const MundaRecordSummary({
    required this.totalRolls,
    required this.totalPieces,
    required this.totalAmount,
    this.fromDate,
    this.toDate,
    this.userId,
  });

  /// Create empty summary
  factory MundaRecordSummary.empty() {
    return const MundaRecordSummary(
      totalRolls: 0,
      totalPieces: 0,
      totalAmount: 0.0,
    );
  }

  /// Average amount per roll
  double get averageAmountPerRoll {
    return totalRolls > 0 ? totalAmount / totalRolls : 0.0;
  }

  /// Average amount per piece
  double get averageAmountPerPiece {
    return totalPieces > 0 ? totalAmount / totalPieces : 0.0;
  }

  /// Average pieces per roll
  double get averagePiecesPerRoll {
    return totalRolls > 0 ? totalPieces / totalRolls : 0.0;
  }

  @override
  List<Object?> get props => [
        totalRolls,
        totalPieces,
        totalAmount,
        fromDate,
        toDate,
        userId,
      ];

  @override
  String toString() {
    return 'MundaRecordSummary(totalRolls: $totalRolls, totalPieces: $totalPieces, totalAmount: $totalAmount)';
  }
}
