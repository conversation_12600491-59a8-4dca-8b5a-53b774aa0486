import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/collaboration_entities.dart';

/// Collaboration repository interface
abstract class CollaborationRepository {
  // Workspace Management
  Future<Either<Failure, ApiListResponse<CollaborationWorkspace>>> getWorkspaces({
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> getWorkspaceById(String workspaceId);

  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> createWorkspace(CreateWorkspaceRequest request);

  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> updateWorkspace(UpdateWorkspaceRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteWorkspace(String workspaceId);

  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> archiveWorkspace(String workspaceId);

  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> restoreWorkspace(String workspaceId);

  // Member Management
  Future<Either<Failure, ApiResponse<WorkspaceMember>>> addMember(AddMemberRequest request);

  Future<Either<Failure, ApiResponse<WorkspaceMember>>> updateMember(UpdateMemberRequest request);

  Future<Either<Failure, ApiVoidResponse>> removeMember(String workspaceId, String userId);

  Future<Either<Failure, ApiListResponse<WorkspaceMember>>> getWorkspaceMembers({
    required String workspaceId,
    MemberFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<WorkspaceMember>>> inviteMember(InviteMemberRequest request);

  Future<Either<Failure, ApiVoidResponse>> acceptInvitation(String invitationId);

  Future<Either<Failure, ApiVoidResponse>> declineInvitation(String invitationId);

  // Channel Management
  Future<Either<Failure, ApiListResponse<CollaborationChannel>>> getChannels({
    required String workspaceId,
    ChannelFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<CollaborationChannel>>> getChannelById(String channelId);

  Future<Either<Failure, ApiResponse<CollaborationChannel>>> createChannel(CreateChannelRequest request);

  Future<Either<Failure, ApiResponse<CollaborationChannel>>> updateChannel(UpdateChannelRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteChannel(String channelId);

  Future<Either<Failure, ApiResponse<CollaborationChannel>>> archiveChannel(String channelId);

  Future<Either<Failure, ApiResponse<CollaborationChannel>>> joinChannel(String channelId);

  Future<Either<Failure, ApiVoidResponse>> leaveChannel(String channelId);

  // Message Management
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> getMessages({
    required String channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<CollaborationMessage>>> getMessageById(String messageId);

  Future<Either<Failure, ApiResponse<CollaborationMessage>>> sendMessage(SendMessageRequest request);

  Future<Either<Failure, ApiResponse<CollaborationMessage>>> editMessage(EditMessageRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteMessage(String messageId);

  Future<Either<Failure, ApiResponse<CollaborationMessage>>> pinMessage(String messageId);

  Future<Either<Failure, ApiVoidResponse>> unpinMessage(String messageId);

  Future<Either<Failure, ApiResponse<CollaborationMessage>>> addReaction(AddReactionRequest request);

  Future<Either<Failure, ApiVoidResponse>> removeReaction(RemoveReactionRequest request);

  Future<Either<Failure, ApiVoidResponse>> markMessageAsRead(String messageId);

  Future<Either<Failure, ApiVoidResponse>> markChannelAsRead(String channelId);

  // Thread Management
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> getThreadMessages({
    required String parentMessageId,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<CollaborationMessage>>> replyToThread(ReplyToThreadRequest request);

  // File Management
  Future<Either<Failure, ApiResponse<MessageAttachment>>> uploadFile(UploadFileRequest request);

  Future<Either<Failure, String>> getFileDownloadUrl(String attachmentId);

  Future<Either<Failure, ApiVoidResponse>> deleteFile(String attachmentId);

  Future<Either<Failure, ApiListResponse<MessageAttachment>>> getChannelFiles({
    required String channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  });

  // Real-time Features
  Stream<CollaborationMessage> getMessageStream(String channelId);

  Stream<RealTimeActivity> getActivityStream(String workspaceId);

  Stream<List<WorkspaceMember>> getOnlineMembersStream(String workspaceId);

  Future<Either<Failure, ApiVoidResponse>> updateUserPresence(UpdatePresenceRequest request);

  Future<Either<Failure, ApiVoidResponse>> sendTypingIndicator(TypingIndicatorRequest request);

  // Session Management
  Future<Either<Failure, ApiListResponse<CollaborationSession>>> getSessions({
    required String workspaceId,
    SessionFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<CollaborationSession>>> getSessionById(String sessionId);

  Future<Either<Failure, ApiResponse<CollaborationSession>>> createSession(CreateSessionRequest request);

  Future<Either<Failure, ApiResponse<CollaborationSession>>> updateSession(UpdateSessionRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteSession(String sessionId);

  Future<Either<Failure, ApiResponse<CollaborationSession>>> startSession(String sessionId);

  Future<Either<Failure, ApiResponse<CollaborationSession>>> endSession(String sessionId);

  Future<Either<Failure, ApiResponse<SessionParticipant>>> joinSession(JoinSessionRequest request);

  Future<Either<Failure, ApiVoidResponse>> leaveSession(String sessionId);

  Future<Either<Failure, ApiResponse<SessionParticipant>>> updateParticipant(UpdateParticipantRequest request);

  // Search and Discovery
  Future<Either<Failure, ApiListResponse<CollaborationWorkspace>>> searchWorkspaces({
    required String query,
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> searchMessages({
    required String query,
    required String workspaceId,
    String? channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiListResponse<MessageAttachment>>> searchFiles({
    required String query,
    required String workspaceId,
    String? channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  });

  // Analytics and Insights
  Future<Either<Failure, Map<String, dynamic>>> getWorkspaceAnalytics({
    required String workspaceId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<Either<Failure, Map<String, dynamic>>> getChannelAnalytics({
    required String channelId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<Either<Failure, Map<String, dynamic>>> getUserActivityAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  // Notifications
  Future<Either<Failure, ApiListResponse<RealTimeActivity>>> getNotifications({
    NotificationFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiVoidResponse>> markNotificationAsRead(String activityId);

  Future<Either<Failure, ApiVoidResponse>> markAllNotificationsAsRead();

  Future<Either<Failure, ApiVoidResponse>> updateNotificationSettings(UpdateNotificationSettingsRequest request);
}

// Request classes

/// Create workspace request
class CreateWorkspaceRequest {
  final String workspaceName;
  final String description;
  final WorkspaceType type;
  final WorkspaceSettings settings;
  final List<String> tags;
  final bool isPublic;

  const CreateWorkspaceRequest({
    required this.workspaceName,
    required this.description,
    required this.type,
    required this.settings,
    this.tags = const [],
    this.isPublic = false,
  });
}

/// Update workspace request
class UpdateWorkspaceRequest {
  final String workspaceId;
  final String? workspaceName;
  final String? description;
  final WorkspaceSettings? settings;
  final List<String>? tags;
  final bool? isPublic;

  const UpdateWorkspaceRequest({
    required this.workspaceId,
    this.workspaceName,
    this.description,
    this.settings,
    this.tags,
    this.isPublic,
  });
}

/// Add member request
class AddMemberRequest {
  final String workspaceId;
  final String userId;
  final WorkspaceRole role;
  final Map<String, dynamic> permissions;

  const AddMemberRequest({
    required this.workspaceId,
    required this.userId,
    required this.role,
    this.permissions = const {},
  });
}

/// Update member request
class UpdateMemberRequest {
  final String workspaceId;
  final String userId;
  final WorkspaceRole? role;
  final Map<String, dynamic>? permissions;

  const UpdateMemberRequest({
    required this.workspaceId,
    required this.userId,
    this.role,
    this.permissions,
  });
}

/// Invite member request
class InviteMemberRequest {
  final String workspaceId;
  final String email;
  final WorkspaceRole role;
  final String? message;

  const InviteMemberRequest({
    required this.workspaceId,
    required this.email,
    required this.role,
    this.message,
  });
}

/// Create channel request
class CreateChannelRequest {
  final String workspaceId;
  final String channelName;
  final String description;
  final ChannelType type;
  final ChannelSettings settings;
  final List<String> memberIds;

  const CreateChannelRequest({
    required this.workspaceId,
    required this.channelName,
    required this.description,
    required this.type,
    required this.settings,
    this.memberIds = const [],
  });
}

/// Update channel request
class UpdateChannelRequest {
  final String channelId;
  final String? channelName;
  final String? description;
  final ChannelSettings? settings;

  const UpdateChannelRequest({
    required this.channelId,
    this.channelName,
    this.description,
    this.settings,
  });
}

/// Send message request
class SendMessageRequest {
  final String channelId;
  final MessageType type;
  final String content;
  final List<String> attachmentIds;
  final String? replyToMessageId;
  final List<String> mentions;

  const SendMessageRequest({
    required this.channelId,
    required this.type,
    required this.content,
    this.attachmentIds = const [],
    this.replyToMessageId,
    this.mentions = const [],
  });
}

/// Edit message request
class EditMessageRequest {
  final String messageId;
  final String content;

  const EditMessageRequest({
    required this.messageId,
    required this.content,
  });
}

/// Add reaction request
class AddReactionRequest {
  final String messageId;
  final String emoji;

  const AddReactionRequest({
    required this.messageId,
    required this.emoji,
  });
}

/// Remove reaction request
class RemoveReactionRequest {
  final String messageId;
  final String emoji;

  const RemoveReactionRequest({
    required this.messageId,
    required this.emoji,
  });
}

/// Reply to thread request
class ReplyToThreadRequest {
  final String parentMessageId;
  final String content;
  final List<String> attachmentIds;
  final List<String> mentions;

  const ReplyToThreadRequest({
    required this.parentMessageId,
    required this.content,
    this.attachmentIds = const [],
    this.mentions = const [],
  });
}

/// Upload file request
class UploadFileRequest {
  final String channelId;
  final String fileName;
  final String fileType;
  final int fileSize;
  final List<int> fileData;
  final Map<String, dynamic> metadata;

  const UploadFileRequest({
    required this.channelId,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    required this.fileData,
    this.metadata = const {},
  });
}

/// Update presence request
class UpdatePresenceRequest {
  final String workspaceId;
  final bool isOnline;
  final String? status;

  const UpdatePresenceRequest({
    required this.workspaceId,
    required this.isOnline,
    this.status,
  });
}

/// Typing indicator request
class TypingIndicatorRequest {
  final String channelId;
  final bool isTyping;

  const TypingIndicatorRequest({
    required this.channelId,
    required this.isTyping,
  });
}

/// Create session request
class CreateSessionRequest {
  final String workspaceId;
  final String? channelId;
  final SessionType type;
  final String title;
  final String description;
  final SessionSettings settings;
  final DateTime? scheduledAt;

  const CreateSessionRequest({
    required this.workspaceId,
    this.channelId,
    required this.type,
    required this.title,
    required this.description,
    required this.settings,
    this.scheduledAt,
  });
}

/// Update session request
class UpdateSessionRequest {
  final String sessionId;
  final String? title;
  final String? description;
  final SessionSettings? settings;
  final DateTime? scheduledAt;

  const UpdateSessionRequest({
    required this.sessionId,
    this.title,
    this.description,
    this.settings,
    this.scheduledAt,
  });
}

/// Join session request
class JoinSessionRequest {
  final String sessionId;
  final String? password;

  const JoinSessionRequest({
    required this.sessionId,
    this.password,
  });
}

/// Update participant request
class UpdateParticipantRequest {
  final String sessionId;
  final String userId;
  final bool? isMuted;
  final bool? isScreenSharing;
  final ParticipantRole? role;

  const UpdateParticipantRequest({
    required this.sessionId,
    required this.userId,
    this.isMuted,
    this.isScreenSharing,
    this.role,
  });
}

/// Update notification settings request
class UpdateNotificationSettingsRequest {
  final String workspaceId;
  final bool enableNotifications;
  final bool enableEmailNotifications;
  final bool enablePushNotifications;
  final Map<String, bool> channelNotifications;

  const UpdateNotificationSettingsRequest({
    required this.workspaceId,
    required this.enableNotifications,
    required this.enableEmailNotifications,
    required this.enablePushNotifications,
    this.channelNotifications = const {},
  });
}

// Filter criteria classes

/// Workspace filter criteria
class WorkspaceFilterCriteria {
  final WorkspaceType? type;
  final String? ownerId;
  final bool? isPublic;
  final bool? isArchived;
  final List<String>? tags;
  final DateTime? createdAfter;
  final DateTime? createdBefore;

  const WorkspaceFilterCriteria({
    this.type,
    this.ownerId,
    this.isPublic,
    this.isArchived,
    this.tags,
    this.createdAfter,
    this.createdBefore,
  });
}

/// Member filter criteria
class MemberFilterCriteria {
  final WorkspaceRole? role;
  final bool? isActive;
  final bool? isOnline;
  final DateTime? joinedAfter;
  final DateTime? joinedBefore;

  const MemberFilterCriteria({
    this.role,
    this.isActive,
    this.isOnline,
    this.joinedAfter,
    this.joinedBefore,
  });
}

/// Channel filter criteria
class ChannelFilterCriteria {
  final ChannelType? type;
  final bool? isArchived;
  final bool? hasUnreadMessages;
  final DateTime? createdAfter;
  final DateTime? createdBefore;

  const ChannelFilterCriteria({
    this.type,
    this.isArchived,
    this.hasUnreadMessages,
    this.createdAfter,
    this.createdBefore,
  });
}

/// Message filter criteria
class MessageFilterCriteria {
  final MessageType? type;
  final String? senderId;
  final bool? isPinned;
  final bool? hasAttachments;
  final DateTime? sentAfter;
  final DateTime? sentBefore;

  const MessageFilterCriteria({
    this.type,
    this.senderId,
    this.isPinned,
    this.hasAttachments,
    this.sentAfter,
    this.sentBefore,
  });
}

/// File filter criteria
class FileFilterCriteria {
  final String? fileType;
  final String? uploadedBy;
  final int? minSize;
  final int? maxSize;
  final DateTime? uploadedAfter;
  final DateTime? uploadedBefore;

  const FileFilterCriteria({
    this.fileType,
    this.uploadedBy,
    this.minSize,
    this.maxSize,
    this.uploadedAfter,
    this.uploadedBefore,
  });
}

/// Session filter criteria
class SessionFilterCriteria {
  final SessionType? type;
  final SessionStatus? status;
  final String? hostId;
  final DateTime? scheduledAfter;
  final DateTime? scheduledBefore;

  const SessionFilterCriteria({
    this.type,
    this.status,
    this.hostId,
    this.scheduledAfter,
    this.scheduledBefore,
  });
}

/// Notification filter criteria
class NotificationFilterCriteria {
  final ActivityType? type;
  final ActivityPriority? priority;
  final bool? isRead;
  final String? workspaceId;
  final DateTime? createdAfter;
  final DateTime? createdBefore;

  const NotificationFilterCriteria({
    this.type,
    this.priority,
    this.isRead,
    this.workspaceId,
    this.createdAfter,
    this.createdBefore,
  });
}
