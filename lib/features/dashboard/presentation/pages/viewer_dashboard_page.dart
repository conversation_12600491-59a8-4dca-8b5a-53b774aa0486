import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Viewer Dashboard Page - Read-only access to reports and data
class ViewerDashboardPage extends StatelessWidget {
  const ViewerDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Viewer Dashboard'),
        backgroundColor: Colors.grey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh data
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildViewerDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildViewerDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildSystemOverview(),
          const SizedBox(height: 24),
          _buildViewerActions(context),
          const SizedBox(height: 24),
          _buildReportsAccess(),
          const SizedBox(height: 24),
          _buildDashboardData(),
          const SizedBox(height: 24),
          _buildSystemStatus(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey,
            Colors.grey.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Viewer'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Read-only access to reports and data',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.visibility,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildSystemOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'System Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Total Orders',
              value: '1,245',
              icon: Icons.shopping_cart,
              color: Colors.blue,
            ),
            DashboardMetricCard(
              title: 'Active Users',
              value: '89',
              icon: Icons.people,
              color: Colors.green,
            ),
            DashboardMetricCard(
              title: 'Production Lines',
              value: '12',
              icon: Icons.linear_scale,
              color: Colors.purple,
            ),
            DashboardMetricCard(
              title: 'System Health',
              value: '98%',
              icon: Icons.health_and_safety,
              color: Colors.orange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildViewerActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Available Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'View Reports',
              subtitle: 'Access system reports',
              icon: Icons.analytics,
              color: Colors.blue,
              onTap: () {
                // Navigate to reports
              },
            ),
            DashboardCard(
              title: 'Dashboard Data',
              subtitle: 'View dashboard metrics',
              icon: Icons.dashboard,
              color: Colors.green,
              onTap: () {
                // Navigate to dashboard data
              },
            ),
            DashboardCard(
              title: 'Export Data',
              subtitle: 'Export reports to file',
              icon: Icons.download,
              color: Colors.orange,
              onTap: () {
                // Navigate to export data
              },
            ),
            DashboardCard(
              title: 'System Status',
              subtitle: 'Check system health',
              icon: Icons.monitor_heart,
              color: Colors.purple,
              onTap: () {
                // Navigate to system status
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReportsAccess() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.analytics,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Available Reports',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Read Only',
                    style: TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildReportItem(
              'Production Report',
              'Daily production metrics and KPIs',
              'Updated 1 hour ago',
              Colors.blue,
              Icons.factory,
            ),
            _buildReportItem(
              'Quality Report',
              'Quality control and defect analysis',
              'Updated 2 hours ago',
              Colors.green,
              Icons.verified,
            ),
            _buildReportItem(
              'Inventory Report',
              'Stock levels and material usage',
              'Updated 30 minutes ago',
              Colors.orange,
              Icons.inventory,
            ),
            _buildReportItem(
              'Performance Report',
              'Employee and department performance',
              'Updated 3 hours ago',
              Colors.purple,
              Icons.trending_up,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportItem(String title, String description, String lastUpdated, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
                Text(
                  lastUpdated,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey[400],
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardData() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.dashboard,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Dashboard Metrics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Refresh data
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard('Orders Today', '45', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard('Efficiency', '89%', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard('Quality Rate', '96%', Colors.purple),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard('On Time', '92%', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStatus() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.monitor_heart,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'System Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'All Systems Operational',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusItem('Database', 'Online', Colors.green, Icons.storage),
            const SizedBox(height: 8),
            _buildStatusItem('API Services', 'Online', Colors.green, Icons.api),
            const SizedBox(height: 8),
            _buildStatusItem('Authentication', 'Online', Colors.green, Icons.security),
            const SizedBox(height: 8),
            _buildStatusItem('File Storage', 'Online', Colors.green, Icons.cloud),
            const SizedBox(height: 8),
            _buildStatusItem('Backup System', 'Online', Colors.green, Icons.backup),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String service, String status, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            service,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            status,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }
}
