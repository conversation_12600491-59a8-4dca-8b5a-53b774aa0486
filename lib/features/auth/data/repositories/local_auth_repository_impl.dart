import 'dart:convert';

import 'package:dartz/dartz.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:hm_collection/features/auth/domain/entities/login_credentials.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/auth_tokens.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../models/auth_tokens_model.dart';
import '../models/user_model.dart';


/// Stub Local authentication repository implementation (no local_auth dependency)
@LazySingleton(as: LocalAuthRepository)
class LocalAuthRepositoryImpl implements LocalAuthRepository {
  final SharedPreferences _prefs;
  final DeviceInfoPlugin _deviceInfo;

  const LocalAuthRepositoryImpl(
    this._prefs,
    this._deviceInfo,
  );

  @override
  Future<Either<Failure, void>> saveTokens(AuthTokens tokens) async {
    try {
      final tokensModel = AuthTokensModel.fromEntity(tokens);
      final tokensJson = json.encode(tokensModel.toJson());

      final success = await _prefs.setString(
        AppConstants.accessTokenKey,
        tokensJson,
      );

      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to save authentication tokens'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to save tokens: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthTokens?>> getTokens() async {
    try {
      final tokensJson = _prefs.getString(AppConstants.accessTokenKey);

      if (tokensJson == null) {
        return const Right(null);
      }

      final tokensMap = json.decode(tokensJson) as Map<String, dynamic>;
      final tokensModel = AuthTokensModel.fromJson(tokensMap);
      return Right(tokensModel.toEntity());
    } catch (e) {
      return Left(CacheFailure('Failed to get tokens: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearTokens() async {
    try {
      final success = await _prefs.remove(AppConstants.accessTokenKey);
      if (success) {
        return const Right(null);
      } else {
        return const Left(
            CacheFailure('Failed to clear authentication tokens'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to clear tokens: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveUser(User user) async {
    try {
      final userModel = UserModel.fromEntity(user);
      final userJson = json.encode(userModel.toJson());

      final success = await _prefs.setString(
        AppConstants.userDataKey,
        userJson,
      );

      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to save user data'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to save user: $e'));
    }
  }

  @override
  Future<Either<Failure, User?>> getUser() async {
    try {
      final userJson = _prefs.getString(AppConstants.userDataKey);

      if (userJson == null) {
        return const Right(null);
      }

      final userMap = json.decode(userJson) as Map<String, dynamic>;
      final userModel = UserModel.fromJson(userMap);
      return Right(userModel.toEntity());
    } catch (e) {
      return Left(CacheFailure('Failed to get user: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearUser() async {
    try {
      final success = await _prefs.remove(AppConstants.userDataKey);
      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to clear user data'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to clear user: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveUserPreferences(
      UserPreferences preferences) async {
    try {
      final preferencesModel = UserPreferencesModel.fromEntity(preferences);
      final preferencesJson = json.encode(preferencesModel.toJson());

      final success = await _prefs.setString(
        AppConstants.userPreferencesKey,
        preferencesJson,
      );

      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to save user preferences'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to save preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, UserPreferences?>> getUserPreferences() async {
    try {
      final preferencesJson = _prefs.getString(AppConstants.userPreferencesKey);

      if (preferencesJson == null) {
        return const Right(null);
      }

      final preferencesMap =
          json.decode(preferencesJson) as Map<String, dynamic>;
      final preferencesModel = UserPreferencesModel.fromJson(preferencesMap);
      return Right(preferencesModel.toEntity());
    } catch (e) {
      return Left(CacheFailure('Failed to get preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearUserPreferences() async {
    try {
      final success = await _prefs.remove(AppConstants.userPreferencesKey);
      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to clear user preferences'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to clear preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isBiometricAvailable() async {
    // Stub implementation - always return false since local_auth is removed
    return const Right(false);
  }

  @override
  Future<Either<Failure, bool>> isBiometricEnabled() async {
    // Stub implementation - always return false since local_auth is removed
    return const Right(false);
  }

  @override
  Future<Either<Failure, void>> enableBiometric() async {
    // Stub implementation - do nothing since local_auth is removed
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> disableBiometric() async {
    // Stub implementation - do nothing since local_auth is removed
    return const Right(null);
  }

  @override
  Future<Either<Failure, bool>> authenticateWithBiometric() async {
    // Stub implementation - always return false since local_auth is removed
    return const Right(false);
  }

  @override
  Future<Either<Failure, void>> saveLoginCredentials(
      LoginCredentials credentials) async {
    try {
      final credentialsJson = json.encode(credentials.toJson());

      final success = await _prefs.setString(
        AppConstants.loginCredentialsKey,
        credentialsJson,
      );

      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to save login credentials'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to save credentials: $e'));
    }
  }

  @override
  Future<Either<Failure, LoginCredentials?>> getLoginCredentials() async {
    try {
      final credentialsJson =
          _prefs.getString(AppConstants.loginCredentialsKey);

      if (credentialsJson == null) {
        return const Right(null);
      }

      final credentialsMap =
          json.decode(credentialsJson) as Map<String, dynamic>;
      final credentials = LoginCredentialsModel.fromJson(credentialsMap);
      return Right(credentials);
    } catch (e) {
      return Left(CacheFailure('Failed to get credentials: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearLoginCredentials() async {
    try {
      final success = await _prefs.remove(AppConstants.loginCredentialsKey);
      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to clear login credentials'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to clear credentials: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isAutoLoginEnabled() async {
    try {
      return Right(_prefs.getBool(AppConstants.autoLoginKey) ?? false);
    } catch (e) {
      return Left(CacheFailure('Failed to check auto-login status: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> enableAutoLogin() async {
    try {
      final success = await _prefs.setBool(AppConstants.autoLoginKey, true);
      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to enable auto-login'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to enable auto-login: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> disableAutoLogin() async {
    try {
      final success = await _prefs.setBool(AppConstants.autoLoginKey, false);
      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to disable auto-login'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to disable auto-login: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, String>>> getDeviceInfo() async {
    try {
      final deviceInfo = await _deviceInfo.deviceInfo;
      final Map<String, String> deviceData = {};

      if (deviceInfo.data.containsKey('androidId')) {
        deviceData['deviceId'] = deviceInfo.data['androidId'] ?? 'unknown';
      }
      if (deviceInfo.data.containsKey('brand')) {
        deviceData['brand'] = deviceInfo.data['brand'] ?? 'unknown';
      }
      if (deviceData.containsKey('model')) {
        deviceData['model'] = deviceInfo.data['model'] ?? 'unknown';
      }
      if (deviceData.containsKey('version.release')) {
        deviceData['osVersion'] =
            deviceInfo.data['version.release'] ?? 'unknown';
      }

      return Right(deviceData);
    } catch (e) {
      return Left(CacheFailure('Failed to get device info: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveLastLoginTime(DateTime timestamp) async {
    try {
      final success = await _prefs.setString(
        AppConstants.lastLoginTimeKey,
        timestamp.toIso8601String(),
      );
      if (success) {
        return const Right(null);
      } else {
        return const Left(CacheFailure('Failed to save last login time'));
      }
    } catch (e) {
      return Left(CacheFailure('Failed to save last login time: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearAllAuthData() async {
    try {
      final futures = <Future<bool>>[];

      futures.add(_prefs.remove(AppConstants.accessTokenKey));
      futures.add(_prefs.remove(AppConstants.userDataKey));
      futures.add(_prefs.remove(AppConstants.userPreferencesKey));
      futures.add(_prefs.remove(AppConstants.loginCredentialsKey));
      futures.add(_prefs.remove(AppConstants.autoLoginKey));
      futures.add(_prefs.remove(AppConstants.lastLoginTimeKey));

      await Future.wait(futures);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to clear all auth data: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> generateDeviceId() async {
    try {
      final deviceInfo = await _deviceInfo.deviceInfo;
      String deviceId = 'unknown';

      if (deviceInfo.data.containsKey('androidId')) {
        deviceId = deviceInfo.data['androidId'] ?? 'unknown';
      } else if (deviceInfo.data.containsKey('identifierForVendor')) {
        deviceId = deviceInfo.data['identifierForVendor'] ?? 'unknown';
      }

      return Right(deviceId);
    } catch (e) {
      return Left(CacheFailure('Failed to generate device ID: $e'));
    }
  }

  @override
  Future<Either<Failure, DateTime?>> getLastLoginTime() async {
    try {
      final lastLoginTimeString =
          _prefs.getString(AppConstants.lastLoginTimeKey);

      if (lastLoginTimeString == null) {
        return const Right(null);
      }

      final lastLoginTime = DateTime.parse(lastLoginTimeString);
      return Right(lastLoginTime);
    } catch (e) {
      return Left(CacheFailure('Failed to get last login time: $e'));
    }
  }
}
