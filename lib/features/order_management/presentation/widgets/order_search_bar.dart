import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';

/// Order search bar widget
class OrderSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final VoidCallback onClear;
  final String? initialQuery;
  final String? hintText;
  final bool enabled;

  const OrderSearchBar({
    super.key,
    required this.onSearch,
    required this.onClear,
    this.initialQuery,
    this.hintText,
    this.enabled = true,
  });

  @override
  State<OrderSearchBar> createState() => _OrderSearchBarState();
}

class _OrderSearchBarState extends State<OrderSearchBar> {
  late TextEditingController _controller;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
    _hasText = widget.initialQuery?.isNotEmpty ?? false;
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _onSubmitted(String value) {
    widget.onSearch(value.trim());
  }

  void _onClear() {
    _controller.clear();
    widget.onClear();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border.withValues(alpha: 0.3),
        ),
      ),
      child: TextField(
        controller: _controller,
        enabled: widget.enabled,
        decoration: InputDecoration(
          hintText: widget.hintText ?? 'Search orders, clients, products...',
          hintStyle: TextStyle(
            color: AppColors.textSecondary,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.textSecondary,
          ),
          suffixIcon: _hasText
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: AppColors.textSecondary,
                  ),
                  onPressed: _onClear,
                  tooltip: 'Clear search',
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onSubmitted: _onSubmitted,
        textInputAction: TextInputAction.search,
      ),
    );
  }
}

/// Advanced search dialog
class AdvancedSearchDialog extends StatefulWidget {
  final Function(Map<String, dynamic>) onSearch;
  final Map<String, dynamic>? initialCriteria;

  const AdvancedSearchDialog({
    super.key,
    required this.onSearch,
    this.initialCriteria,
  });

  @override
  State<AdvancedSearchDialog> createState() => _AdvancedSearchDialogState();
}

class _AdvancedSearchDialogState extends State<AdvancedSearchDialog> {
  final _formKey = GlobalKey<FormState>();
  final _orderNumberController = TextEditingController();
  final _clientNameController = TextEditingController();
  final _productNameController = TextEditingController();
  
  String? _selectedStatus;
  String? _selectedPriority;
  DateTimeRange? _orderDateRange;
  DateTimeRange? _requiredDateRange;
  double? _minValue;
  double? _maxValue;

  @override
  void initState() {
    super.initState();
    _initializeFromCriteria();
  }

  void _initializeFromCriteria() {
    if (widget.initialCriteria != null) {
      final criteria = widget.initialCriteria!;
      _orderNumberController.text = criteria['order_number'] ?? '';
      _clientNameController.text = criteria['client_name'] ?? '';
      _productNameController.text = criteria['product_name'] ?? '';
      _selectedStatus = criteria['status'];
      _selectedPriority = criteria['priority'];
      _minValue = criteria['min_value']?.toDouble();
      _maxValue = criteria['max_value']?.toDouble();
    }
  }

  @override
  void dispose() {
    _orderNumberController.dispose();
    _clientNameController.dispose();
    _productNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Advanced Search'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTextField(
                  controller: _orderNumberController,
                  label: 'Order Number',
                  hint: 'Enter order number',
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _clientNameController,
                  label: 'Client Name',
                  hint: 'Enter client name',
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _productNameController,
                  label: 'Product Name',
                  hint: 'Enter product name',
                ),
                const SizedBox(height: 16),
                _buildStatusDropdown(),
                const SizedBox(height: 16),
                _buildPriorityDropdown(),
                const SizedBox(height: 16),
                _buildDateRangeSelector(
                  label: 'Order Date Range',
                  dateRange: _orderDateRange,
                  onChanged: (range) => setState(() => _orderDateRange = range),
                ),
                const SizedBox(height: 16),
                _buildDateRangeSelector(
                  label: 'Required Date Range',
                  dateRange: _requiredDateRange,
                  onChanged: (range) => setState(() => _requiredDateRange = range),
                ),
                const SizedBox(height: 16),
                _buildValueRangeInputs(),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _clearAll,
          child: const Text('Clear All'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _search,
          child: const Text('Search'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
      ),
    );
  }

  Widget _buildStatusDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedStatus,
      decoration: const InputDecoration(
        labelText: 'Status',
        border: OutlineInputBorder(),
      ),
      items: const [
        DropdownMenuItem(value: 'draft', child: Text('Draft')),
        DropdownMenuItem(value: 'pending', child: Text('Pending')),
        DropdownMenuItem(value: 'confirmed', child: Text('Confirmed')),
        DropdownMenuItem(value: 'in_production', child: Text('In Production')),
        DropdownMenuItem(value: 'quality_check', child: Text('Quality Check')),
        DropdownMenuItem(value: 'completed', child: Text('Completed')),
        DropdownMenuItem(value: 'shipped', child: Text('Shipped')),
        DropdownMenuItem(value: 'delivered', child: Text('Delivered')),
        DropdownMenuItem(value: 'cancelled', child: Text('Cancelled')),
        DropdownMenuItem(value: 'on_hold', child: Text('On Hold')),
      ],
      onChanged: (value) => setState(() => _selectedStatus = value),
    );
  }

  Widget _buildPriorityDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedPriority,
      decoration: const InputDecoration(
        labelText: 'Priority',
        border: OutlineInputBorder(),
      ),
      items: const [
        DropdownMenuItem(value: 'low', child: Text('Low')),
        DropdownMenuItem(value: 'normal', child: Text('Normal')),
        DropdownMenuItem(value: 'high', child: Text('High')),
        DropdownMenuItem(value: 'urgent', child: Text('Urgent')),
        DropdownMenuItem(value: 'critical', child: Text('Critical')),
      ],
      onChanged: (value) => setState(() => _selectedPriority = value),
    );
  }

  Widget _buildDateRangeSelector({
    required String label,
    required DateTimeRange? dateRange,
    required Function(DateTimeRange?) onChanged,
  }) {
    return InkWell(
      onTap: () => _selectDateRange(onChanged),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: dateRange != null
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => onChanged(null),
                )
              : const Icon(Icons.date_range),
        ),
        child: Text(
          dateRange != null
              ? '${_formatDate(dateRange.start)} - ${_formatDate(dateRange.end)}'
              : 'Select date range',
          style: TextStyle(
            color: dateRange != null
                ? Theme.of(context).textTheme.bodyLarge?.color
                : Theme.of(context).hintColor,
          ),
        ),
      ),
    );
  }

  Widget _buildValueRangeInputs() {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              labelText: 'Min Value',
              border: OutlineInputBorder(),
              prefixText: '\$',
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              _minValue = double.tryParse(value);
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              labelText: 'Max Value',
              border: OutlineInputBorder(),
              prefixText: '\$',
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              _maxValue = double.tryParse(value);
            },
          ),
        ),
      ],
    );
  }

  Future<void> _selectDateRange(Function(DateTimeRange?) onChanged) async {
    final range = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    onChanged(range);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _clearAll() {
    setState(() {
      _orderNumberController.clear();
      _clientNameController.clear();
      _productNameController.clear();
      _selectedStatus = null;
      _selectedPriority = null;
      _orderDateRange = null;
      _requiredDateRange = null;
      _minValue = null;
      _maxValue = null;
    });
  }

  void _search() {
    final criteria = <String, dynamic>{};
    
    if (_orderNumberController.text.isNotEmpty) {
      criteria['order_number'] = _orderNumberController.text;
    }
    if (_clientNameController.text.isNotEmpty) {
      criteria['client_name'] = _clientNameController.text;
    }
    if (_productNameController.text.isNotEmpty) {
      criteria['product_name'] = _productNameController.text;
    }
    if (_selectedStatus != null) {
      criteria['status'] = _selectedStatus;
    }
    if (_selectedPriority != null) {
      criteria['priority'] = _selectedPriority;
    }
    if (_orderDateRange != null) {
      criteria['order_date_from'] = _orderDateRange!.start;
      criteria['order_date_to'] = _orderDateRange!.end;
    }
    if (_requiredDateRange != null) {
      criteria['required_date_from'] = _requiredDateRange!.start;
      criteria['required_date_to'] = _requiredDateRange!.end;
    }
    if (_minValue != null) {
      criteria['min_value'] = _minValue;
    }
    if (_maxValue != null) {
      criteria['max_value'] = _maxValue;
    }

    widget.onSearch(criteria);
    Navigator.of(context).pop();
  }
}
