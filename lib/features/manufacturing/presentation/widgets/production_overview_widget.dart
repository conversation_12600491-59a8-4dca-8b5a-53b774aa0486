import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';

/// Production overview widget with beautiful charts and progress indicators
class ProductionOverviewWidget extends StatefulWidget {
  const ProductionOverviewWidget({Key? key}) : super(key: key);

  @override
  State<ProductionOverviewWidget> createState() => _ProductionOverviewWidgetState();
}

class _ProductionOverviewWidgetState extends State<ProductionOverviewWidget>
    with TickerProviderStateMixin {
  late AnimationController _progressAnimationController;
  late List<Animation<double>> _progressAnimations;

  @override
  void initState() {
    super.initState();
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Create progress animations for different departments
    _progressAnimations = [
      Tween<double>(begin: 0.0, end: 0.85).animate(CurvedAnimation(
        parent: _progressAnimationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      )),
      Tween<double>(begin: 0.0, end: 0.72).animate(CurvedAnimation(
        parent: _progressAnimationController,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOut),
      )),
      Tween<double>(begin: 0.0, end: 0.93).animate(CurvedAnimation(
        parent: _progressAnimationController,
        curve: const Interval(0.4, 0.9, curve: Curves.easeOut),
      )),
      Tween<double>(begin: 0.0, end: 0.68).animate(CurvedAnimation(
        parent: _progressAnimationController,
        curve: const Interval(0.6, 1.0, curve: Curves.easeOut),
      )),
    ];

    _progressAnimationController.forward();
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Production Overview',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.trending_up,
                        color: Colors.green,
                        size: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'On Track',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildDepartmentProgress(),
            const SizedBox(height: 24),
            _buildProductionMetrics(),
          ],
        ),
      ),
    );
  }

  Widget _buildDepartmentProgress() {
    final departments = [
      _DepartmentProgress(
        name: 'Cutting',
        progress: 0.85,
        color: Colors.blue,
        icon: Icons.content_cut,
        animationIndex: 0,
      ),
      _DepartmentProgress(
        name: 'Sewing',
        progress: 0.72,
        color: Colors.green,
        icon: Icons.precision_manufacturing,
        animationIndex: 1,
      ),
      _DepartmentProgress(
        name: 'Finishing',
        progress: 0.93,
        color: Colors.orange,
        icon: Icons.check_circle,
        animationIndex: 2,
      ),
      _DepartmentProgress(
        name: 'Packaging',
        progress: 0.68,
        color: Colors.purple,
        icon: Icons.inventory,
        animationIndex: 3,
      ),
    ];

    return Column(
      children: departments.map((dept) => _buildProgressItem(dept)).toList(),
    );
  }

  Widget _buildProgressItem(_DepartmentProgress dept) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: dept.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              dept.icon,
              color: dept.color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      dept.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    AnimatedBuilder(
                      animation: _progressAnimations[dept.animationIndex],
                      builder: (context, child) {
                        return Text(
                          '${(_progressAnimations[dept.animationIndex].value * 100).toInt()}%',
                          style: TextStyle(
                            color: dept.color,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                AnimatedBuilder(
                  animation: _progressAnimations[dept.animationIndex],
                  builder: (context, child) {
                    return LinearProgressIndicator(
                      value: _progressAnimations[dept.animationIndex].value,
                      backgroundColor: dept.color.withOpacity(0.1),
                      valueColor: AlwaysStoppedAnimation<Color>(dept.color),
                      minHeight: 6,
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductionMetrics() {
    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            'Daily Target',
            '1,250',
            'pieces',
            Icons.flag,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMetricCard(
            'Completed',
            '1,087',
            'pieces',
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMetricCard(
            'Efficiency',
            '87%',
            'rate',
            Icons.speed,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            unit,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _DepartmentProgress {
  final String name;
  final double progress;
  final Color color;
  final IconData icon;
  final int animationIndex;

  _DepartmentProgress({
    required this.name,
    required this.progress,
    required this.color,
    required this.icon,
    required this.animationIndex,
  });
}
