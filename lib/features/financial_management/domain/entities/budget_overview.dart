import 'package:equatable/equatable.dart';

import 'financial_entities.dart';

/// Represents an overview of budget information
class BudgetOverview extends Equatable {
  /// Total allocated budget amount across all budgets
  final double totalAllocated;

  /// Total amount spent from all budgets
  final double totalSpent;

  /// Total remaining amount across all budgets
  final double totalRemaining;

  /// Overall budget utilization percentage (0.0 to 1.0)
  final double utilizationPercentage;

  /// Number of active budgets
  final int activeBudgetsCount;

  /// Number of budgets that are over their allocated amount
  final int overBudgetCount;

  const BudgetOverview({
    required this.totalAllocated,
    required this.totalSpent,
    required this.totalRemaining,
    required this.utilizationPercentage,
    required this.activeBudgetsCount,
    required this.overBudgetCount,
  });

  @override
  List<Object> get props => [
        totalAllocated,
        totalSpent,
        totalRemaining,
        utilizationPercentage,
        activeBudgetsCount,
        overBudgetCount,
      ];

  /// Creates an empty BudgetOverview
  factory BudgetOverview.empty() {
    return const BudgetOverview(
      totalAllocated: 0.0,
      totalSpent: 0.0,
      totalRemaining: 0.0,
      utilizationPercentage: 0.0,
      activeBudgetsCount: 0,
      overBudgetCount: 0,
    );
  }

  /// Creates a BudgetOverview with the given list of budgets
  factory BudgetOverview.fromBudgets(List<Budget> budgets) {
    if (budgets.isEmpty) return BudgetOverview.empty();

    final totalAllocated = budgets.fold<double>(
      0,
      (sum, budget) => sum + budget.totalAmount,
    );

    final totalSpent = budgets.fold<double>(
      0,
      (sum, budget) => sum + budget.metrics.spentAmount,
    );

    final totalRemaining = budgets.fold<double>(
      0,
      (sum, budget) => sum + budget.metrics.remainingAmount,
    );

    final overBudgetCount = budgets.where((b) => b.metrics.isExceeded).length;

    return BudgetOverview(
      totalAllocated: totalAllocated,
      totalSpent: totalSpent,
      totalRemaining: totalRemaining,
      utilizationPercentage: totalAllocated > 0 ? totalSpent / totalAllocated : 0.0,
      activeBudgetsCount: budgets.length,
      overBudgetCount: overBudgetCount,
    );
  }

  /// Creates a copy of this BudgetOverview with the given fields replaced
  BudgetOverview copyWith({
    double? totalAllocated,
    double? totalSpent,
    double? totalRemaining,
    double? utilizationPercentage,
    int? activeBudgetsCount,
    int? overBudgetCount,
  }) {
    return BudgetOverview(
      totalAllocated: totalAllocated ?? this.totalAllocated,
      totalSpent: totalSpent ?? this.totalSpent,
      totalRemaining: totalRemaining ?? this.totalRemaining,
      utilizationPercentage: utilizationPercentage ?? this.utilizationPercentage,
      activeBudgetsCount: activeBudgetsCount ?? this.activeBudgetsCount,
      overBudgetCount: overBudgetCount ?? this.overBudgetCount,
    );
  }
}
