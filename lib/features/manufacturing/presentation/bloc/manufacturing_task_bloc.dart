import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:hm_collection/features/department/domain/entities/department.dart' as dept_entity;
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/manufacturing_task_entities.dart';
import '../../domain/entities/manufacturing_task_analytics.dart';
import '../../domain/models/manufacturing_task_requests.dart';
import '../../domain/usecases/manufacturing_task_usecases.dart';

part 'manufacturing_task_event.dart';
part 'manufacturing_task_state.dart';

/// BLoC for managing manufacturing task operations
class ManufacturingTaskBloc extends Bloc<ManufacturingTaskEvent, ManufacturingTaskState> {
  final GetManufacturingTaskOrdersUseCase _getManufacturingTaskOrdersUseCase;
  final GetManufacturingTaskOrderByIdUseCase _getManufacturingTaskOrderByIdUseCase;
  final CreateManufacturingTaskOrderUseCase _createManufacturingTaskOrderUseCase;
  final UpdateManufacturingTaskOrderUseCase _updateManufacturingTaskOrderUseCase;
  final DeleteManufacturingTaskOrderUseCase _deleteManufacturingTaskOrderUseCase;
  final StartManufacturingTaskOrderUseCase _startManufacturingTaskOrderUseCase;
  final PauseManufacturingTaskOrderUseCase _pauseManufacturingTaskOrderUseCase;
  final ResumeManufacturingTaskOrderUseCase _resumeManufacturingTaskOrderUseCase;
  final CompleteManufacturingTaskOrderUseCase _completeManufacturingTaskOrderUseCase;
  
  final GetStageTasksForOrderUseCase _getStageTasksForOrderUseCase;
  final GetStageTasksByDepartmentUseCase _getStageTasksByDepartmentUseCase;
  final GetStageTasksByStageUseCase _getStageTasksByStageUseCase;
  final GetStageTaskByIdUseCase _getStageTaskByIdUseCase;
  final CreateStageTaskUseCase _createStageTaskUseCase;
  final UpdateStageTaskUseCase _updateStageTaskUseCase;
  final StartStageTaskUseCase _startStageTaskUseCase;
  final CompleteStageTaskUseCase _completeStageTaskUseCase;
  final AssignWorkersToStageTaskUseCase _assignWorkersToStageTaskUseCase;
  final UpdateStageTaskProgressUseCase _updateStageTaskProgressUseCase;
  
  final CreateStageTransferUseCase _createStageTransferUseCase;
  final GetTransfersForOrderUseCase _getTransfersForOrderUseCase;
  final GetPendingTransfersForDepartmentUseCase _getPendingTransfersForDepartmentUseCase;
  final AcceptStageTransferUseCase _acceptStageTransferUseCase;
  final RejectStageTransferUseCase _rejectStageTransferUseCase;
  final CompleteStageTransferUseCase _completeStageTransferUseCase;
  
  final AddNoteToOrderUseCase _addNoteToOrderUseCase;
  final AddNoteToStageTaskUseCase _addNoteToStageTaskUseCase;
  final GetNotesForOrderUseCase _getNotesForOrderUseCase;
  final GetNotesForStageTaskUseCase _getNotesForStageTaskUseCase;
  final UpdateNoteUseCase _updateNoteUseCase;
  final DeleteNoteUseCase _deleteNoteUseCase;
  
  final GetManufacturingTaskAnalyticsUseCase _getManufacturingTaskAnalyticsUseCase;
  final GetStagePerformanceMetricsUseCase _getStagePerformanceMetricsUseCase;
  final GetBottleneckAnalysisUseCase _getBottleneckAnalysisUseCase;
  final GetEfficiencyMetricsUseCase _getEfficiencyMetricsUseCase;
  
  final SearchManufacturingTaskOrdersUseCase _searchManufacturingTaskOrdersUseCase;
  final GetOverdueManufacturingTaskOrdersUseCase _getOverdueManufacturingTaskOrdersUseCase;
  final GetUrgentManufacturingTaskOrdersUseCase _getUrgentManufacturingTaskOrdersUseCase;
  final GetManufacturingTaskOrdersByStatusUseCase _getManufacturingTaskOrdersByStatusUseCase;
  final GetStageTasksByWorkerUseCase _getStageTasksByWorkerUseCase;
  final GetStageTasksByStatusUseCase _getStageTasksByStatusUseCase;

  ManufacturingTaskBloc({
    required GetManufacturingTaskOrdersUseCase getManufacturingTaskOrdersUseCase,
    required GetManufacturingTaskOrderByIdUseCase getManufacturingTaskOrderByIdUseCase,
    required CreateManufacturingTaskOrderUseCase createManufacturingTaskOrderUseCase,
    required UpdateManufacturingTaskOrderUseCase updateManufacturingTaskOrderUseCase,
    required DeleteManufacturingTaskOrderUseCase deleteManufacturingTaskOrderUseCase,
    required StartManufacturingTaskOrderUseCase startManufacturingTaskOrderUseCase,
    required PauseManufacturingTaskOrderUseCase pauseManufacturingTaskOrderUseCase,
    required ResumeManufacturingTaskOrderUseCase resumeManufacturingTaskOrderUseCase,
    required CompleteManufacturingTaskOrderUseCase completeManufacturingTaskOrderUseCase,
    required GetStageTasksForOrderUseCase getStageTasksForOrderUseCase,
    required GetStageTasksByDepartmentUseCase getStageTasksByDepartmentUseCase,
    required GetStageTasksByStageUseCase getStageTasksByStageUseCase,
    required GetStageTaskByIdUseCase getStageTaskByIdUseCase,
    required CreateStageTaskUseCase createStageTaskUseCase,
    required UpdateStageTaskUseCase updateStageTaskUseCase,
    required StartStageTaskUseCase startStageTaskUseCase,
    required CompleteStageTaskUseCase completeStageTaskUseCase,
    required AssignWorkersToStageTaskUseCase assignWorkersToStageTaskUseCase,
    required UpdateStageTaskProgressUseCase updateStageTaskProgressUseCase,
    required CreateStageTransferUseCase createStageTransferUseCase,
    required GetTransfersForOrderUseCase getTransfersForOrderUseCase,
    required GetPendingTransfersForDepartmentUseCase getPendingTransfersForDepartmentUseCase,
    required AcceptStageTransferUseCase acceptStageTransferUseCase,
    required RejectStageTransferUseCase rejectStageTransferUseCase,
    required CompleteStageTransferUseCase completeStageTransferUseCase,
    required AddNoteToOrderUseCase addNoteToOrderUseCase,
    required AddNoteToStageTaskUseCase addNoteToStageTaskUseCase,
    required GetNotesForOrderUseCase getNotesForOrderUseCase,
    required GetNotesForStageTaskUseCase getNotesForStageTaskUseCase,
    required UpdateNoteUseCase updateNoteUseCase,
    required DeleteNoteUseCase deleteNoteUseCase,
    required GetManufacturingTaskAnalyticsUseCase getManufacturingTaskAnalyticsUseCase,
    required GetStagePerformanceMetricsUseCase getStagePerformanceMetricsUseCase,
    required GetBottleneckAnalysisUseCase getBottleneckAnalysisUseCase,
    required GetEfficiencyMetricsUseCase getEfficiencyMetricsUseCase,
    required SearchManufacturingTaskOrdersUseCase searchManufacturingTaskOrdersUseCase,
    required GetOverdueManufacturingTaskOrdersUseCase getOverdueManufacturingTaskOrdersUseCase,
    required GetUrgentManufacturingTaskOrdersUseCase getUrgentManufacturingTaskOrdersUseCase,
    required GetManufacturingTaskOrdersByStatusUseCase getManufacturingTaskOrdersByStatusUseCase,
    required GetStageTasksByWorkerUseCase getStageTasksByWorkerUseCase,
    required GetStageTasksByStatusUseCase getStageTasksByStatusUseCase,
  })  : _getManufacturingTaskOrdersUseCase = getManufacturingTaskOrdersUseCase,
        _getManufacturingTaskOrderByIdUseCase = getManufacturingTaskOrderByIdUseCase,
        _createManufacturingTaskOrderUseCase = createManufacturingTaskOrderUseCase,
        _updateManufacturingTaskOrderUseCase = updateManufacturingTaskOrderUseCase,
        _deleteManufacturingTaskOrderUseCase = deleteManufacturingTaskOrderUseCase,
        _startManufacturingTaskOrderUseCase = startManufacturingTaskOrderUseCase,
        _pauseManufacturingTaskOrderUseCase = pauseManufacturingTaskOrderUseCase,
        _resumeManufacturingTaskOrderUseCase = resumeManufacturingTaskOrderUseCase,
        _completeManufacturingTaskOrderUseCase = completeManufacturingTaskOrderUseCase,
        _getStageTasksForOrderUseCase = getStageTasksForOrderUseCase,
        _getStageTasksByDepartmentUseCase = getStageTasksByDepartmentUseCase,
        _getStageTasksByStageUseCase = getStageTasksByStageUseCase,
        _getStageTaskByIdUseCase = getStageTaskByIdUseCase,
        _createStageTaskUseCase = createStageTaskUseCase,
        _updateStageTaskUseCase = updateStageTaskUseCase,
        _startStageTaskUseCase = startStageTaskUseCase,
        _completeStageTaskUseCase = completeStageTaskUseCase,
        _assignWorkersToStageTaskUseCase = assignWorkersToStageTaskUseCase,
        _updateStageTaskProgressUseCase = updateStageTaskProgressUseCase,
        _createStageTransferUseCase = createStageTransferUseCase,
        _getTransfersForOrderUseCase = getTransfersForOrderUseCase,
        _getPendingTransfersForDepartmentUseCase = getPendingTransfersForDepartmentUseCase,
        _acceptStageTransferUseCase = acceptStageTransferUseCase,
        _rejectStageTransferUseCase = rejectStageTransferUseCase,
        _completeStageTransferUseCase = completeStageTransferUseCase,
        _addNoteToOrderUseCase = addNoteToOrderUseCase,
        _addNoteToStageTaskUseCase = addNoteToStageTaskUseCase,
        _getNotesForOrderUseCase = getNotesForOrderUseCase,
        _getNotesForStageTaskUseCase = getNotesForStageTaskUseCase,
        _updateNoteUseCase = updateNoteUseCase,
        _deleteNoteUseCase = deleteNoteUseCase,
        _getManufacturingTaskAnalyticsUseCase = getManufacturingTaskAnalyticsUseCase,
        _getStagePerformanceMetricsUseCase = getStagePerformanceMetricsUseCase,
        _getBottleneckAnalysisUseCase = getBottleneckAnalysisUseCase,
        _getEfficiencyMetricsUseCase = getEfficiencyMetricsUseCase,
        _searchManufacturingTaskOrdersUseCase = searchManufacturingTaskOrdersUseCase,
        _getOverdueManufacturingTaskOrdersUseCase = getOverdueManufacturingTaskOrdersUseCase,
        _getUrgentManufacturingTaskOrdersUseCase = getUrgentManufacturingTaskOrdersUseCase,
        _getManufacturingTaskOrdersByStatusUseCase = getManufacturingTaskOrdersByStatusUseCase,
        _getStageTasksByWorkerUseCase = getStageTasksByWorkerUseCase,
        _getStageTasksByStatusUseCase = getStageTasksByStatusUseCase,
        super(const ManufacturingTaskInitial()) {
    
    // Manufacturing Task Order Events
    on<LoadManufacturingTaskOrdersRequested>(_onLoadManufacturingTaskOrdersRequested);
    on<LoadManufacturingTaskOrderByIdRequested>(_onLoadManufacturingTaskOrderByIdRequested);
    on<CreateManufacturingTaskOrderRequested>(_onCreateManufacturingTaskOrderRequested);
    on<UpdateManufacturingTaskOrderRequested>(_onUpdateManufacturingTaskOrderRequested);
    on<DeleteManufacturingTaskOrderRequested>(_onDeleteManufacturingTaskOrderRequested);
    on<StartManufacturingTaskOrderRequested>(_onStartManufacturingTaskOrderRequested);
    on<PauseManufacturingTaskOrderRequested>(_onPauseManufacturingTaskOrderRequested);
    on<ResumeManufacturingTaskOrderRequested>(_onResumeManufacturingTaskOrderRequested);
    on<CompleteManufacturingTaskOrderRequested>(_onCompleteManufacturingTaskOrderRequested);
    
    // Stage Task Events
    on<LoadStageTasksForOrderRequested>(_onLoadStageTasksForOrderRequested);
    on<LoadStageTasksByDepartmentRequested>(_onLoadStageTasksByDepartmentRequested);
    on<LoadStageTasksByStageRequested>(_onLoadStageTasksByStageRequested);
    on<LoadStageTaskByIdRequested>(_onLoadStageTaskByIdRequested);
    on<CreateStageTaskRequested>(_onCreateStageTaskRequested);
    on<UpdateStageTaskRequested>(_onUpdateStageTaskRequested);
    on<StartStageTaskRequested>(_onStartStageTaskRequested);
    on<CompleteStageTaskRequested>(_onCompleteStageTaskRequested);
    on<AssignWorkersToStageTaskRequested>(_onAssignWorkersToStageTaskRequested);
    on<UpdateStageTaskProgressRequested>(_onUpdateStageTaskProgressRequested);
    
    // Stage Transfer Events
    on<CreateStageTransferRequested>(_onCreateStageTransferRequested);
    on<LoadTransfersForOrderRequested>(_onLoadTransfersForOrderRequested);
    on<LoadPendingTransfersForDepartmentRequested>(_onLoadPendingTransfersForDepartmentRequested);
    on<AcceptStageTransferRequested>(_onAcceptStageTransferRequested);
    on<RejectStageTransferRequested>(_onRejectStageTransferRequested);
    on<CompleteStageTransferRequested>(_onCompleteStageTransferRequested);
    
    // Notes Events
    on<AddNoteToOrderRequested>(_onAddNoteToOrderRequested);
    on<AddNoteToStageTaskRequested>(_onAddNoteToStageTaskRequested);
    on<LoadNotesForOrderRequested>(_onLoadNotesForOrderRequested);
    on<LoadNotesForStageTaskRequested>(_onLoadNotesForStageTaskRequested);
    on<UpdateNoteRequested>(_onUpdateNoteRequested);
    on<DeleteNoteRequested>(_onDeleteNoteRequested);
    
    // Analytics Events
    on<LoadManufacturingTaskAnalyticsRequested>(_onLoadManufacturingTaskAnalyticsRequested);
    on<LoadStagePerformanceMetricsRequested>(_onLoadStagePerformanceMetricsRequested);
    on<LoadBottleneckAnalysisRequested>(_onLoadBottleneckAnalysisRequested);
    on<LoadEfficiencyMetricsRequested>(_onLoadEfficiencyMetricsRequested);
    
    // Search and Filter Events
    on<SearchManufacturingTaskOrdersRequested>(_onSearchManufacturingTaskOrdersRequested);
    on<LoadOverdueManufacturingTaskOrdersRequested>(_onLoadOverdueManufacturingTaskOrdersRequested);
    on<LoadUrgentManufacturingTaskOrdersRequested>(_onLoadUrgentManufacturingTaskOrdersRequested);
    on<LoadManufacturingTaskOrdersByStatusRequested>(_onLoadManufacturingTaskOrdersByStatusRequested);
    on<LoadStageTasksByWorkerRequested>(_onLoadStageTasksByWorkerRequested);
    on<LoadStageTasksByStatusRequested>(_onLoadStageTasksByStatusRequested);
    
    // Utility Events
    on<RefreshManufacturingTaskDataRequested>(_onRefreshManufacturingTaskDataRequested);
    on<ClearManufacturingTaskStateRequested>(_onClearManufacturingTaskStateRequested);
  }

  // Manufacturing Task Order Event Handlers

  /// Load manufacturing task orders
  Future<void> _onLoadManufacturingTaskOrdersRequested(
    LoadManufacturingTaskOrdersRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getManufacturingTaskOrdersUseCase(
      GetManufacturingTaskOrdersParams(
        filter: event.filter,
        pagination: event.pagination,
      ),
    );

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrdersLoaded(
            orders: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load manufacturing task orders'));
        }
      },
    );
  }

  /// Load manufacturing task order by ID
  Future<void> _onLoadManufacturingTaskOrderByIdRequested(
    LoadManufacturingTaskOrderByIdRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getManufacturingTaskOrderByIdUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderLoaded(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load manufacturing task order'));
        }
      },
    );
  }

  /// Create manufacturing task order
  Future<void> _onCreateManufacturingTaskOrderRequested(
    CreateManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _createManufacturingTaskOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderCreated(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to create manufacturing task order'));
        }
      },
    );
  }

  /// Update manufacturing task order
  Future<void> _onUpdateManufacturingTaskOrderRequested(
    UpdateManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _updateManufacturingTaskOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderUpdated(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to update manufacturing task order'));
        }
      },
    );
  }

  /// Delete manufacturing task order
  Future<void> _onDeleteManufacturingTaskOrderRequested(
    DeleteManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _deleteManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success) {
          emit(ManufacturingTaskOrderDeleted(orderId: event.orderId));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to delete manufacturing task order'));
        }
      },
    );
  }

  /// Start manufacturing task order
  Future<void> _onStartManufacturingTaskOrderRequested(
    StartManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _startManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderStarted(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to start manufacturing task order'));
        }
      },
    );
  }

  /// Pause manufacturing task order
  Future<void> _onPauseManufacturingTaskOrderRequested(
    PauseManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _pauseManufacturingTaskOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderPaused(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to pause manufacturing task order'));
        }
      },
    );
  }

  /// Resume manufacturing task order
  Future<void> _onResumeManufacturingTaskOrderRequested(
    ResumeManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _resumeManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderResumed(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to resume manufacturing task order'));
        }
      },
    );
  }

  /// Complete manufacturing task order
  Future<void> _onCompleteManufacturingTaskOrderRequested(
    CompleteManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _completeManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderCompleted(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to complete manufacturing task order'));
        }
      },
    );
  }

  // Stage Task Event Handlers
  Future<void> _onLoadStageTasksForOrderRequested(
    LoadStageTasksForOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getStageTasksForOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTasksLoaded(
            tasks: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load stage tasks'));
        }
      },
    );
  }

  Future<void> _onLoadStageTasksByDepartmentRequested(
    LoadStageTasksByDepartmentRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getStageTasksByDepartmentUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTasksLoaded(
            tasks: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load stage tasks by department'));
        }
      },
    );
  }

  Future<void> _onLoadStageTasksByStageRequested(
    LoadStageTasksByStageRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getStageTasksByStageUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTasksLoaded(
            tasks: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load stage tasks by stage'));
        }
      },
    );
  }

  Future<void> _onLoadStageTaskByIdRequested(
    LoadStageTaskByIdRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getStageTaskByIdUseCase(IdParams(event.taskId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTaskLoaded(task: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load stage task'));
        }
      },
    );
  }

  Future<void> _onCreateStageTaskRequested(
    CreateStageTaskRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _createStageTaskUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTaskCreated(task: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to create stage task'));
        }
      },
    );
  }

  Future<void> _onUpdateStageTaskRequested(
    UpdateStageTaskRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _updateStageTaskUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTaskUpdated(task: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to update stage task'));
        }
      },
    );
  }

  Future<void> _onStartStageTaskRequested(
    StartStageTaskRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _startStageTaskUseCase(IdParams(event.taskId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTaskStarted(task: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to start stage task'));
        }
      },
    );
  }

  Future<void> _onCompleteStageTaskRequested(
    CompleteStageTaskRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _completeStageTaskUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTaskCompleted(task: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to complete stage task'));
        }
      },
    );
  }

  Future<void> _onAssignWorkersToStageTaskRequested(
    AssignWorkersToStageTaskRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _assignWorkersToStageTaskUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(WorkersAssignedToStageTask(task: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to assign workers to stage task'));
        }
      },
    );
  }

  Future<void> _onUpdateStageTaskProgressRequested(
    UpdateStageTaskProgressRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _updateStageTaskProgressUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTaskProgressUpdated(task: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to update stage task progress'));
        }
      },
    );
  }

  // Stage Transfer Events
  Future<void> _onCreateStageTransferRequested(
    CreateStageTransferRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _createStageTransferUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTransferCreated(transfer: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to create stage transfer'));
        }
      },
    );
  }

  Future<void> _onLoadTransfersForOrderRequested(
    LoadTransfersForOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getTransfersForOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTransfersLoaded(
            transfers: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load transfers for order'));
        }
      },
    );
  }

  Future<void> _onLoadPendingTransfersForDepartmentRequested(
    LoadPendingTransfersForDepartmentRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getPendingTransfersForDepartmentUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTransfersLoaded(
            transfers: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load pending transfers for department'));
        }
      },
    );
  }

  Future<void> _onAcceptStageTransferRequested(
    AcceptStageTransferRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _acceptStageTransferUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTransferAccepted(transfer: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to accept stage transfer'));
        }
      },
    );
  }

  Future<void> _onRejectStageTransferRequested(
    RejectStageTransferRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _rejectStageTransferUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTransferRejected(transfer: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to reject stage transfer'));
        }
      },
    );
  }

  Future<void> _onCompleteStageTransferRequested(
    CompleteStageTransferRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _completeStageTransferUseCase(IdParams(event.transferId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTransferCompleted(transfer: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to complete stage transfer'));
        }
      },
    );
  }

  // Notes Events
  Future<void> _onAddNoteToOrderRequested(
    AddNoteToOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _addNoteToOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(NoteAdded(note: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to add note to order'));
        }
      },
    );
  }

  Future<void> _onAddNoteToStageTaskRequested(
    AddNoteToStageTaskRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _addNoteToStageTaskUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(NoteAdded(note: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to add note to stage task'));
        }
      },
    );
  }

  Future<void> _onLoadNotesForOrderRequested(
    LoadNotesForOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getNotesForOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(NotesLoaded(
            notes: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load notes for order'));
        }
      },
    );
  }

  Future<void> _onLoadNotesForStageTaskRequested(
    LoadNotesForStageTaskRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getNotesForStageTaskUseCase(IdParams(event.taskId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(NotesLoaded(
            notes: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load notes for stage task'));
        }
      },
    );
  }

  Future<void> _onUpdateNoteRequested(
    UpdateNoteRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _updateNoteUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(NoteUpdated(note: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to update note'));
        }
      },
    );
  }

  Future<void> _onDeleteNoteRequested(
    DeleteNoteRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _deleteNoteUseCase(IdParams(event.noteId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success) {
          emit(NoteDeleted(noteId: event.noteId));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to delete note'));
        }
      },
    );
  }

  // Analytics Events
  Future<void> _onLoadManufacturingTaskAnalyticsRequested(
    LoadManufacturingTaskAnalyticsRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getManufacturingTaskAnalyticsUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (analytics) {
        emit(ManufacturingTaskAnalyticsLoaded(analytics: analytics));
      },
    );
  }

  Future<void> _onLoadStagePerformanceMetricsRequested(
    LoadStagePerformanceMetricsRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getStagePerformanceMetricsUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (metrics) {
        emit(StagePerformanceMetricsLoaded(metrics: metrics));
      },
    );
  }

  Future<void> _onLoadBottleneckAnalysisRequested(
    LoadBottleneckAnalysisRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getBottleneckAnalysisUseCase(event.params as GetBottleneckAnalysisRequest);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(BottleneckAnalysisLoaded(bottlenecks: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load bottleneck analysis'));
        }
      },
    );
  }

  Future<void> _onLoadEfficiencyMetricsRequested(
    LoadEfficiencyMetricsRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getEfficiencyMetricsUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (metrics) {
        emit(EfficiencyMetricsLoaded(metrics: metrics));
      },
    );
  }

  // Search and Filter Events
  Future<void> _onSearchManufacturingTaskOrdersRequested(
    SearchManufacturingTaskOrdersRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _searchManufacturingTaskOrdersUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrdersLoaded(
            orders: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to search manufacturing task orders'));
        }
      },
    );
  }

  Future<void> _onLoadOverdueManufacturingTaskOrdersRequested(
    LoadOverdueManufacturingTaskOrdersRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final request = GetOverdueManufacturingTaskOrdersRequest(
      departments: event.filter?.departments,
      priorities: event.filter?.priorities,
      pagination: event.pagination,
    );

    final result = await _getOverdueManufacturingTaskOrdersUseCase(request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrdersLoaded(
            orders: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load overdue manufacturing task orders'));
        }
      },
    );
  }

  Future<void> _onLoadUrgentManufacturingTaskOrdersRequested(
    LoadUrgentManufacturingTaskOrdersRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final params = GetUrgentManufacturingTaskOrdersRequest(
      departments: event.departments?.map((dept) => (dept as dept_entity.Department).type).toList(),
      pagination: event.pagination,
    );

    final result = await _getUrgentManufacturingTaskOrdersUseCase(params);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrdersLoaded(
            orders: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load urgent manufacturing task orders'));
        }
      },
    );
  }

  Future<void> _onLoadManufacturingTaskOrdersByStatusRequested(
    LoadManufacturingTaskOrdersByStatusRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getManufacturingTaskOrdersByStatusUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrdersLoaded(
            orders: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load manufacturing task orders by status'));
        }
      },
    );
  }

  Future<void> _onLoadStageTasksByWorkerRequested(
    LoadStageTasksByWorkerRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getStageTasksByWorkerUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTasksLoaded(
            tasks: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load stage tasks by worker'));
        }
      },
    );
  }

  Future<void> _onLoadStageTasksByStatusRequested(
    LoadStageTasksByStatusRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getStageTasksByStatusUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(StageTasksLoaded(
            tasks: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load stage tasks by status'));
        }
      },
    );
  }

  // Utility Events
  Future<void> _onRefreshManufacturingTaskDataRequested(
    RefreshManufacturingTaskDataRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    // Re-load all relevant data
    add(const LoadManufacturingTaskOrdersRequested());
  }

  Future<void> _onClearManufacturingTaskStateRequested(
    ClearManufacturingTaskStateRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskInitial());
  }
}
