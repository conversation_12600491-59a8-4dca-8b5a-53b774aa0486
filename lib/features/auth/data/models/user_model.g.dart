// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      deletedBy: json['deletedBy'] as String?,
      version: (json['version'] as num?)?.toInt() ?? 1,
      username: json['username'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      role: $enumDecode(_$UserRoleEnumMap, json['role']),
      department: $enumDecode(_$DepartmentEnumMap, json['department']),
      status: $enumDecode(_$CommonStatusEnumMap, json['status']),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      emailVerifiedAt: json['emailVerifiedAt'] == null
          ? null
          : DateTime.parse(json['emailVerifiedAt'] as String),
      phoneVerifiedAt: json['phoneVerifiedAt'] == null
          ? null
          : DateTime.parse(json['phoneVerifiedAt'] as String),
      isActive: json['isActive'] as bool,
      isEmailVerified: json['isEmailVerified'] as bool,
      isPhoneVerified: json['isPhoneVerified'] as bool,
      preferences: json['preferences'] as Map<String, dynamic>?,
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
      'deletedBy': instance.deletedBy,
      'version': instance.version,
      'username': instance.username,
      'email': instance.email,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'phoneNumber': instance.phoneNumber,
      'profileImageUrl': instance.profileImageUrl,
      'role': _$UserRoleEnumMap[instance.role]!,
      'department': _$DepartmentEnumMap[instance.department]!,
      'status': _$CommonStatusEnumMap[instance.status]!,
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'emailVerifiedAt': instance.emailVerifiedAt?.toIso8601String(),
      'phoneVerifiedAt': instance.phoneVerifiedAt?.toIso8601String(),
      'isActive': instance.isActive,
      'isEmailVerified': instance.isEmailVerified,
      'isPhoneVerified': instance.isPhoneVerified,
      'preferences': instance.preferences,
      'permissions': instance.permissions,
    };

const _$UserRoleEnumMap = {
  UserRole.administrator: 'administrator',
  UserRole.merchandiser: 'merchandiser',
  UserRole.inventoryManager: 'inventoryManager',
  UserRole.cuttingHead: 'cuttingHead',
  UserRole.cuttingMaster: 'cuttingMaster',
  UserRole.cuttingHelper: 'cuttingHelper',
  UserRole.sewingHead: 'sewingHead',
  UserRole.sewingSupervisor: 'sewingSupervisor',
  UserRole.sewingOperator: 'sewingOperator',
  UserRole.qualityController: 'qualityController',
  UserRole.finishingHead: 'finishingHead',
  UserRole.finishingOperator: 'finishingOperator',
  UserRole.warehouseManager: 'warehouseManager',
  UserRole.viewer: 'viewer',
};

const _$DepartmentEnumMap = {
  Department.merchandising: 'merchandising',
  Department.cutting: 'cutting',
  Department.sewing: 'sewing',
  Department.quality: 'quality',
  Department.finishing: 'finishing',
  Department.warehouse: 'warehouse',
  Department.administration: 'administration',
};

const _$CommonStatusEnumMap = {
  CommonStatus.active: 'active',
  CommonStatus.inactive: 'inactive',
  CommonStatus.pending: 'pending',
  CommonStatus.suspended: 'suspended',
  CommonStatus.archived: 'archived',
};

UserSessionModel _$UserSessionModelFromJson(Map<String, dynamic> json) =>
    UserSessionModel(
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String,
      deviceId: json['deviceId'] as String,
      deviceName: json['deviceName'] as String,
      ipAddress: json['ipAddress'] as String,
      userAgent: json['userAgent'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastActiveAt: DateTime.parse(json['lastActiveAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$UserSessionModelToJson(UserSessionModel instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'userId': instance.userId,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastActiveAt': instance.lastActiveAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'isActive': instance.isActive,
    };

UserPreferencesModel _$UserPreferencesModelFromJson(
        Map<String, dynamic> json) =>
    UserPreferencesModel(
      theme: json['theme'] as String? ?? 'light',
      language: json['language'] as String? ?? 'en',
      dateFormat: json['date_format'] as String? ?? 'MM/dd/yyyy',
      timeFormat: json['time_format'] as String? ?? '12h',
      enableNotifications: json['enable_notifications'] as bool? ?? true,
      enableEmailNotifications:
          json['enable_email_notifications'] as bool? ?? true,
      enablePushNotifications:
          json['enable_push_notifications'] as bool? ?? true,
      notificationChannels:
          (json['notification_channels'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, e as bool),
              ) ??
              {},
      dashboardLayout: json['dashboard_layout'] as Map<String, dynamic>? ?? {},
      customSettings: json['custom_settings'] as Map<String, dynamic>? ?? {},
    );

Map<String, dynamic> _$UserPreferencesModelToJson(
        UserPreferencesModel instance) =>
    <String, dynamic>{
      'theme': instance.theme,
      'language': instance.language,
      'date_format': instance.dateFormat,
      'time_format': instance.timeFormat,
      'enable_notifications': instance.enableNotifications,
      'enable_email_notifications': instance.enableEmailNotifications,
      'enable_push_notifications': instance.enablePushNotifications,
      'notification_channels': instance.notificationChannels,
      'dashboard_layout': instance.dashboardLayout,
      'custom_settings': instance.customSettings,
    };
