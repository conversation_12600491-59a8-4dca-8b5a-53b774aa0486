/**
 * User role enumeration
 */
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  SUPERVISOR = 'supervisor',
  OPERATOR = 'operator',
  INSPECTOR = 'inspector',
  VIEWER = 'viewer',
}

/**
 * User status enumeration
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

/**
 * User profile interface
 */
export interface UserProfile {
  firstName: string;
  lastName: string;
  employeeId: string;
  department: string;
  position: string;
  hireDate: Date | null;
  phoneNumber: string;
  address: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
}

/**
 * User data interface
 */
export interface UserData {
  uid: string;
  email: string;
  displayName: string;
  emailVerified: boolean;
  phoneNumber: string | null;
  photoURL: string | null;
  role: UserRole;
  status: UserStatus;
  profile: UserProfile;
  permissions: string[];
  departmentIds: string[];
  currentDepartmentId: string | null;
  lastLoginAt: Date | null;
  lastActiveAt: Date;
  metadata: {
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
    version: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User activity log interface
 */
export interface UserActivityLog {
  userId: string;
  action: string;
  details: any;
  timestamp: Date;
}

/**
 * User session interface
 */
export interface UserSession {
  sessionId: string;
  userId: string;
  deviceId: string;
  deviceName: string;
  platform: string;
  appVersion: string;
  loginAt: Date;
  logoutAt: Date | null;
  lastActiveAt: Date;
  ipAddress: string;
  location: {
    country: string;
    region: string;
    city: string;
  } | null;
  isActive: boolean;
}

/**
 * Permission interface
 */
export interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  resource: string;
  action: string;
}

/**
 * Role permission mapping interface
 */
export interface RolePermissions {
  role: UserRole;
  permissions: string[];
  description: string;
  hierarchyLevel: number;
}

/**
 * User notification interface
 */
export interface UserNotification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  createdAt: Date;
  readAt: Date | null;
  expiresAt: Date | null;
}

/**
 * Department interface
 */
export interface Department {
  id: string;
  name: string;
  description: string;
  managerId: string;
  supervisorIds: string[];
  operatorIds: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User authentication result interface
 */
export interface AuthResult {
  user: UserData;
  token: string;
  refreshToken: string;
  expiresAt: Date;
  permissions: string[];
  sessionId: string;
}

/**
 * User registration data interface
 */
export interface UserRegistrationData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  employeeId?: string;
  role: UserRole;
  departmentId?: string;
}

/**
 * User update data interface
 */
export interface UserUpdateData {
  displayName?: string;
  phoneNumber?: string;
  photoURL?: string;
  profile?: Partial<UserProfile>;
  role?: UserRole;
  status?: UserStatus;
  permissions?: string[];
  departmentIds?: string[];
  currentDepartmentId?: string;
}

/**
 * Password reset request interface
 */
export interface PasswordResetRequest {
  email: string;
  token: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}

/**
 * Email verification request interface
 */
export interface EmailVerificationRequest {
  userId: string;
  email: string;
  token: string;
  expiresAt: Date;
  verified: boolean;
  createdAt: Date;
}

/**
 * Two-factor authentication interface
 */
export interface TwoFactorAuth {
  userId: string;
  enabled: boolean;
  secret: string;
  backupCodes: string[];
  lastUsedAt: Date | null;
  createdAt: Date;
}

/**
 * User preferences interface
 */
export interface UserPreferences {
  userId: string;
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    inApp: boolean;
  };
  dashboard: {
    layout: string;
    widgets: string[];
  };
  privacy: {
    showOnlineStatus: boolean;
    showLastActive: boolean;
  };
}

/**
 * Audit log interface
 */
export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  oldValues: any;
  newValues: any;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

/**
 * System configuration interface
 */
export interface SystemConfig {
  id: string;
  key: string;
  value: any;
  description: string;
  category: string;
  isPublic: boolean;
  updatedBy: string;
  updatedAt: Date;
}

/**
 * API response interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
}

/**
 * Pagination interface
 */
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Query filter interface
 */
export interface QueryFilter {
  field: string;
  operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains' | 'array-contains-any';
  value: any;
}

/**
 * Sort order interface
 */
export interface SortOrder {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Query options interface
 */
export interface QueryOptions {
  filters?: QueryFilter[];
  sort?: SortOrder[];
  pagination?: {
    page: number;
    limit: number;
  };
  select?: string[];
}

/**
 * Batch operation interface
 */
export interface BatchOperation {
  type: 'create' | 'update' | 'delete';
  collection: string;
  documentId: string;
  data?: any;
}

/**
 * File upload interface
 */
export interface FileUpload {
  id: string;
  fileName: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
  metadata?: any;
}

/**
 * Error codes enumeration
 */
export enum ErrorCodes {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
}

/**
 * Event types enumeration
 */
export enum EventTypes {
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  USER_LOGIN = 'USER_LOGIN',
  USER_LOGOUT = 'USER_LOGOUT',
  ROLE_CHANGED = 'ROLE_CHANGED',
  STATUS_CHANGED = 'STATUS_CHANGED',
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  EMAIL_VERIFIED = 'EMAIL_VERIFIED',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PERMISSION_REVOKED = 'PERMISSION_REVOKED',
}
