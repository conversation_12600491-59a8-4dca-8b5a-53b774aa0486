import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/client_entities.dart' hide OrderStatistics, UpdateOrderRequest;
import '../models/create_order_request.dart';
import '../entities/order_entities.dart';
import '../repositories/order_repository.dart' show OrderRepository, OrderStatistics, ClientRepository, UpdateOrderRequest;

/// Get orders use case
@injectable
class GetOrdersUseCase implements UseCase<ApiListResponse<ManufacturingOrder>, GetOrdersParams> {
  final OrderRepository _repository;

  const GetOrdersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> call(GetOrdersParams params) async {
    return await _repository.getOrders(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get order by ID use case
@injectable
class GetOrderByIdUseCase implements UseCase<ApiResponse<ManufacturingOrder>, IdParams> {
  final OrderRepository _repository;

  const GetOrderByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> call(IdParams params) async {
    return await _repository.getOrderById(params.id);
  }
}

/// Create order use case
@injectable
class CreateOrderUseCase implements UseCase<ApiResponse<ManufacturingOrder>, CreateOrderRequest> {
  final OrderRepository _repository;

  const CreateOrderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> call(CreateOrderRequest params) async {
    if (!params.isValid) {
      return Left(ValidationFailure('Invalid order data'));
    }
    return await _repository.createOrder(params);
  }
}

/// Update order use case
@injectable
class UpdateOrderUseCase implements UseCase<ApiResponse<ManufacturingOrder>, UpdateOrderRequest> {
  final OrderRepository _repository;

  const UpdateOrderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> call(UpdateOrderRequest params) async {
    if (!params.hasUpdates) {
      return const Left(ValidationFailure('No updates provided'));
    }
    return await _repository.updateOrder(params);
  }
}

/// Delete order use case
@injectable
class DeleteOrderUseCase implements UseCase<ApiVoidResponse, DeleteOrderParams> {
  final OrderRepository _repository;

  const DeleteOrderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(DeleteOrderParams params) async {
    return await _repository.deleteOrder(params.orderId, params.reason);
  }
}

/// Update order status use case
@injectable
class UpdateOrderStatusUseCase implements UseCase<ApiResponse<ManufacturingOrder>, UpdateOrderStatusParams> {
  final OrderRepository _repository;

  const UpdateOrderStatusUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> call(UpdateOrderStatusParams params) async {
    return await _repository.updateOrderStatus(
      params.orderId,
      params.status,
      params.reason,
    );
  }
}

/// Search orders use case
@injectable
class SearchOrdersUseCase implements UseCase<ApiListResponse<ManufacturingOrder>, SearchOrdersParams> {
  final OrderRepository _repository;

  const SearchOrdersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> call(SearchOrdersParams params) async {
    return await _repository.searchOrders(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get orders by client use case
@injectable
class GetOrdersByClientUseCase implements UseCase<ApiListResponse<ManufacturingOrder>, GetOrdersByClientParams> {
  final OrderRepository _repository;

  const GetOrdersByClientUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> call(GetOrdersByClientParams params) async {
    return await _repository.getOrdersByClient(
      params.clientId,
      pagination: params.pagination,
    );
  }
}

/// Get overdue orders use case
@injectable
class GetOverdueOrdersUseCase implements UseCase<ApiListResponse<ManufacturingOrder>, GetOverdueOrdersParams> {
  final OrderRepository _repository;

  const GetOverdueOrdersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> call(GetOverdueOrdersParams params) async {
    return await _repository.getOverdueOrders(pagination: params.pagination);
  }
}

/// Get urgent orders use case
@injectable
class GetUrgentOrdersUseCase implements UseCase<ApiListResponse<ManufacturingOrder>, GetUrgentOrdersParams> {
  final OrderRepository _repository;

  const GetUrgentOrdersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> call(GetUrgentOrdersParams params) async {
    return await _repository.getUrgentOrders(pagination: params.pagination);
  }
}

/// Add order note use case
@injectable
class AddOrderNoteUseCase implements UseCase<ApiResponse<OrderNote>, AddOrderNoteRequest> {
  final OrderRepository _repository;

  const AddOrderNoteUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<OrderNote>>> call(AddOrderNoteRequest params) async {
    return await _repository.addOrderNote(params);
  }
}

/// Cancel order use case
@injectable
class CancelOrderUseCase implements UseCase<ApiResponse<ManufacturingOrder>, CancelOrderParams> {
  final OrderRepository _repository;

  const CancelOrderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> call(CancelOrderParams params) async {
    return await _repository.cancelOrder(params.orderId, params.reason);
  }
}

/// Duplicate order use case
@injectable
class DuplicateOrderUseCase implements UseCase<ApiResponse<ManufacturingOrder>, DuplicateOrderParams> {
  final OrderRepository _repository;

  const DuplicateOrderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> call(DuplicateOrderParams params) async {
    return await _repository.duplicateOrder(params.orderId, params.request);
  }
}

/// Get order statistics use case
@injectable
class GetOrderStatisticsUseCase implements UseCase<OrderStatistics, GetOrderStatisticsParams> {
  final OrderRepository _repository;

  const GetOrderStatisticsUseCase(this._repository);

  @override
  Future<Either<Failure, OrderStatistics>> call(GetOrderStatisticsParams params) async {
    return await _repository.getOrderStatistics(
      startDate: params.startDate,
      endDate: params.endDate,
      clientId: params.clientId,
    );
  }
}

/// Get order timeline use case
@injectable
class GetOrderTimelineUseCase implements UseCase<List<OrderTimelineEvent>, IdParams> {
  final OrderRepository _repository;

  const GetOrderTimelineUseCase(this._repository);

  @override
  Future<Either<Failure, List<OrderTimelineEvent>>> call(IdParams params) async {
    return await _repository.getOrderTimeline(params.id);
  }
}

// Client use cases

/// Get clients use case
@injectable
class GetClientsUseCase implements UseCase<ApiListResponse<Client>, GetClientsParams> {
  final ClientRepository _repository;

  const GetClientsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<Client>>> call(GetClientsParams params) async {
    return await _repository.getClients(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get client by ID use case
@injectable
class GetClientByIdUseCase implements UseCase<ApiResponse<Client>, IdParams> {
  final ClientRepository _repository;

  const GetClientByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<Client>>> call(IdParams params) async {
    return await _repository.getClientById(params.id);
  }
}

/// Create client use case
@injectable
class CreateClientUseCase implements UseCase<ApiResponse<Client>, CreateClientRequest> {
  final ClientRepository _repository;

  const CreateClientUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<Client>>> call(CreateClientRequest params) async {
    return await _repository.createClient(params);
  }
}

/// Search clients use case
@injectable
class SearchClientsUseCase implements UseCase<ApiListResponse<Client>, SearchClientsParams> {
  final ClientRepository _repository;

  const SearchClientsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<Client>>> call(SearchClientsParams params) async {
    return await _repository.searchClients(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

// Parameter classes

class GetOrdersParams {
  final OrderFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetOrdersParams({this.filter, this.pagination});
}

class DeleteOrderParams {
  final String orderId;
  final String? reason;

  const DeleteOrderParams(this.orderId, {this.reason});
}

class UpdateOrderStatusParams {
  final String orderId;
  final OrderStatus status;
  final String? reason;

  const UpdateOrderStatusParams(this.orderId, this.status, {this.reason});
}

class SearchOrdersParams {
  final String query;
  final OrderFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchOrdersParams(this.query, {this.filter, this.pagination});
}

class GetOrdersByClientParams {
  final String clientId;
  final PaginationParams? pagination;

  const GetOrdersByClientParams(this.clientId, {this.pagination});
}

class GetOverdueOrdersParams {
  final PaginationParams? pagination;

  const GetOverdueOrdersParams({this.pagination});
}

class GetUrgentOrdersParams {
  final PaginationParams? pagination;

  const GetUrgentOrdersParams({this.pagination});
}

class CancelOrderParams {
  final String orderId;
  final String reason;

  const CancelOrderParams(this.orderId, this.reason);
}

class DuplicateOrderParams {
  final String orderId;
  final DuplicateOrderRequest request;

  const DuplicateOrderParams(this.orderId, this.request);
}

class GetOrderStatisticsParams {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? clientId;

  const GetOrderStatisticsParams({this.startDate, this.endDate, this.clientId});
}

class GetClientsParams {
  final ClientFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetClientsParams({this.filter, this.pagination});
}

class SearchClientsParams {
  final String query;
  final ClientFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchClientsParams(this.query, {this.filter, this.pagination});
}

// Additional request classes

class AddOrderNoteRequest extends Equatable {
  final String orderId;
  final String content;
  final OrderNoteType type;
  final bool isInternal;

  const AddOrderNoteRequest({
    required this.orderId,
    required this.content,
    required this.type,
    this.isInternal = false,
  });

  @override
  List<Object?> get props => [orderId, content, type, isInternal];
}

class UpdateOrderNoteRequest extends Equatable {
  final String noteId;
  final String content;
  final OrderNoteType? type;
  final bool? isInternal;

  const UpdateOrderNoteRequest({
    required this.noteId,
    required this.content,
    this.type,
    this.isInternal,
  });

  @override
  List<Object?> get props => [noteId, content, type, isInternal];
}

class AddOrderAttachmentRequest extends Equatable {
  final String orderId;
  final String fileName;
  final String fileUrl;
  final String fileType;
  final int fileSize;
  final OrderAttachmentType type;

  const AddOrderAttachmentRequest({
    required this.orderId,
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
    required this.type,
  });

  @override
  List<Object?> get props => [orderId, fileName, fileUrl, fileType, fileSize, type];
}

class DuplicateOrderRequest extends Equatable {
  final String? newClientId;
  final DateTime? newOrderDate;
  final DateTime? newRequiredDate;
  final bool copyNotes;
  final bool copyAttachments;

  const DuplicateOrderRequest({
    this.newClientId,
    this.newOrderDate,
    this.newRequiredDate,
    this.copyNotes = false,
    this.copyAttachments = false,
  });

  @override
  List<Object?> get props => [newClientId, newOrderDate, newRequiredDate, copyNotes, copyAttachments];
}

class CreateClientRequest extends Equatable {
  final String companyName;
  final String? displayName;
  final ClientType type;
  final ClientContact primaryContact;
  final ClientAddress billingAddress;
  final ClientAddress? shippingAddress;
  final ClientBusinessInfo businessInfo;
  final ClientPreferences preferences;
  final List<String> tags;
  final double creditLimit;

  const CreateClientRequest({
    required this.companyName,
    this.displayName,
    required this.type,
    required this.primaryContact,
    required this.billingAddress,
    this.shippingAddress,
    required this.businessInfo,
    required this.preferences,
    this.tags = const [],
    this.creditLimit = 0.0,
  });

  @override
  List<Object?> get props => [
        companyName,
        displayName,
        type,
        primaryContact,
        billingAddress,
        shippingAddress,
        businessInfo,
        preferences,
        tags,
        creditLimit,
      ];
}

class UpdateClientRequest extends Equatable {
  final String clientId;
  final String? companyName;
  final String? displayName;
  final ClientType? type;
  final ClientContact? primaryContact;
  final ClientAddress? billingAddress;
  final ClientAddress? shippingAddress;
  final ClientBusinessInfo? businessInfo;
  final ClientPreferences? preferences;
  final List<String>? tags;
  final double? creditLimit;

  const UpdateClientRequest({
    required this.clientId,
    this.companyName,
    this.displayName,
    this.type,
    this.primaryContact,
    this.billingAddress,
    this.shippingAddress,
    this.businessInfo,
    this.preferences,
    this.tags,
    this.creditLimit,
  });

  @override
  List<Object?> get props => [
        clientId,
        companyName,
        displayName,
        type,
        primaryContact,
        billingAddress,
        shippingAddress,
        businessInfo,
        preferences,
        tags,
        creditLimit,
      ];
}

// Additional classes for client filtering, statistics, etc.
class ClientFilterCriteria extends Equatable {
  final ClientType? type;
  final CommonStatus? status;
  final String? assignedMerchandiser;
  final double? minCreditLimit;
  final double? maxCreditLimit;
  final ClientRatingCategory? ratingCategory;
  final List<String>? tags;

  const ClientFilterCriteria({
    this.type,
    this.status,
    this.assignedMerchandiser,
    this.minCreditLimit,
    this.maxCreditLimit,
    this.ratingCategory,
    this.tags,
  });

  @override
  List<Object?> get props => [
        type,
        status,
        assignedMerchandiser,
        minCreditLimit,
        maxCreditLimit,
        ratingCategory,
        tags,
      ];
}

class ClientStatistics extends Equatable {
  final int totalOrders;
  final double totalValue;
  final double averageOrderValue;
  final DateTime? lastOrderDate;
  final int overdueOrders;
  final double outstandingBalance;
  final ClientRating rating;

  const ClientStatistics({
    required this.totalOrders,
    required this.totalValue,
    required this.averageOrderValue,
    this.lastOrderDate,
    required this.overdueOrders,
    required this.outstandingBalance,
    required this.rating,
  });

  @override
  List<Object?> get props => [
        totalOrders,
        totalValue,
        averageOrderValue,
        lastOrderDate,
        overdueOrders,
        outstandingBalance,
        rating,
      ];
}

// Import/Export result classes
class OrderImportResult extends Equatable {
  final int totalRecords;
  final int successfulImports;
  final int failedImports;
  final List<String> errors;

  const OrderImportResult({
    required this.totalRecords,
    required this.successfulImports,
    required this.failedImports,
    required this.errors,
  });

  @override
  List<Object?> get props => [totalRecords, successfulImports, failedImports, errors];
}

class ClientImportResult extends Equatable {
  final int totalRecords;
  final int successfulImports;
  final int failedImports;
  final List<String> errors;

  const ClientImportResult({
    required this.totalRecords,
    required this.successfulImports,
    required this.failedImports,
    required this.errors,
  });

  @override
  List<Object?> get props => [totalRecords, successfulImports, failedImports, errors];
}

class BulkUpdateResult extends Equatable {
  final int totalRecords;
  final int successfulUpdates;
  final int failedUpdates;
  final List<String> errors;

  const BulkUpdateResult({
    required this.totalRecords,
    required this.successfulUpdates,
    required this.failedUpdates,
    required this.errors,
  });

  @override
  List<Object?> get props => [totalRecords, successfulUpdates, failedUpdates, errors];
}

class BulkUpdateOrderRequest extends Equatable {
  final String orderId;
  final OrderStatus? status;
  final OrderPriority? priority;
  final String? assignedMerchandiser;

  const BulkUpdateOrderRequest({
    required this.orderId,
    this.status,
    this.priority,
    this.assignedMerchandiser,
  });

  @override
  List<Object?> get props => [orderId, status, priority, assignedMerchandiser];
}
