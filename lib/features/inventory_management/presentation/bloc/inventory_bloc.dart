import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/inventory_entities.dart';
import '../../domain/entities/supplier_entities.dart';
import '../../domain/repositories/inventory_repository.dart';
import '../../domain/usecases/inventory_usecases.dart';

part 'inventory_event.dart';
part 'inventory_state.dart';

/// Inventory Bloc
@injectable
class InventoryBloc extends Bloc<InventoryEvent, InventoryState> {
  final GetInventoryItemsUseCase _getInventoryItemsUseCase;
  final GetInventoryItemByIdUseCase _getInventoryItemByIdUseCase;
  final CreateInventoryItemUseCase _createInventoryItemUseCase;
  final UpdateInventoryItemUseCase _updateInventoryItemUseCase;
  final DeleteInventoryItemUseCase _deleteInventoryItemUseCase;
  final UpdateStockLevelUseCase _updateStockLevelUseCase;
  final AdjustStockUseCase _adjustStockUseCase;
  final ReserveStockUseCase _reserveStockUseCase;
  final ReleaseReservedStockUseCase _releaseReservedStockUseCase;
  final GetStockMovementsUseCase _getStockMovementsUseCase;
  final GetStockMovementsForItemUseCase _getStockMovementsForItemUseCase;
  final GetLowStockItemsUseCase _getLowStockItemsUseCase;
  final GetOutOfStockItemsUseCase _getOutOfStockItemsUseCase;
  final GetOverstockedItemsUseCase _getOverstockedItemsUseCase;
  final GetExpiringItemsUseCase _getExpiringItemsUseCase;
  final SearchInventoryItemsUseCase _searchInventoryItemsUseCase;
  final GetInventoryStatisticsUseCase _getInventoryStatisticsUseCase;
  final GetInventoryValuationUseCase _getInventoryValuationUseCase;
  final GenerateReorderSuggestionsUseCase _generateReorderSuggestionsUseCase;
  final CreateAutomaticReorderUseCase _createAutomaticReorderUseCase;

  InventoryBloc(
    this._getInventoryItemsUseCase,
    this._getInventoryItemByIdUseCase,
    this._createInventoryItemUseCase,
    this._updateInventoryItemUseCase,
    this._deleteInventoryItemUseCase,
    this._updateStockLevelUseCase,
    this._adjustStockUseCase,
    this._reserveStockUseCase,
    this._releaseReservedStockUseCase,
    this._getStockMovementsUseCase,
    this._getStockMovementsForItemUseCase,
    this._getLowStockItemsUseCase,
    this._getOutOfStockItemsUseCase,
    this._getOverstockedItemsUseCase,
    this._getExpiringItemsUseCase,
    this._searchInventoryItemsUseCase,
    this._getInventoryStatisticsUseCase,
    this._getInventoryValuationUseCase,
    this._generateReorderSuggestionsUseCase,
    this._createAutomaticReorderUseCase,
  ) : super(const InventoryInitial()) {
    on<LoadInventoryItemsRequested>(_onLoadInventoryItemsRequested);
    on<RefreshInventoryItemsRequested>(_onRefreshInventoryItemsRequested);
    on<LoadInventoryItemDetailsRequested>(_onLoadInventoryItemDetailsRequested);
    on<CreateInventoryItemRequested>(_onCreateInventoryItemRequested);
    on<UpdateInventoryItemRequested>(_onUpdateInventoryItemRequested);
    on<DeleteInventoryItemRequested>(_onDeleteInventoryItemRequested);
    on<UpdateStockLevelRequested>(_onUpdateStockLevelRequested);
    on<AdjustStockRequested>(_onAdjustStockRequested);
    on<ReserveStockRequested>(_onReserveStockRequested);
    on<ReleaseReservedStockRequested>(_onReleaseReservedStockRequested);
    on<LoadStockMovementsRequested>(_onLoadStockMovementsRequested);
    on<LoadStockMovementsForItemRequested>(_onLoadStockMovementsForItemRequested);
    on<LoadLowStockItemsRequested>(_onLoadLowStockItemsRequested);
    on<LoadOutOfStockItemsRequested>(_onLoadOutOfStockItemsRequested);
    on<LoadOverstockedItemsRequested>(_onLoadOverstockedItemsRequested);
    on<LoadExpiringItemsRequested>(_onLoadExpiringItemsRequested);
    on<SearchInventoryItemsRequested>(_onSearchInventoryItemsRequested);
    on<FilterInventoryItemsRequested>(_onFilterInventoryItemsRequested);
    on<LoadInventoryStatisticsRequested>(_onLoadInventoryStatisticsRequested);
    on<LoadInventoryValuationRequested>(_onLoadInventoryValuationRequested);
    on<GenerateReorderSuggestionsRequested>(_onGenerateReorderSuggestionsRequested);
    on<CreateAutomaticReorderRequested>(_onCreateAutomaticReorderRequested);
    on<ClearInventoryState>(_onClearInventoryState);
  }

  Future<void> _onLoadInventoryItemsRequested(
    LoadInventoryItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getInventoryItemsUseCase(GetInventoryItemsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(InventoryItemsLoaded(
        items: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onRefreshInventoryItemsRequested(
    RefreshInventoryItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final currentState = state;
    if (currentState is InventoryItemsLoaded) {
      emit(currentState.copyWith(isRefreshing: true));

      final result = await _getInventoryItemsUseCase(GetInventoryItemsParams(
        filter: currentState.filter,
        pagination: const PaginationParams(page: 1, perPage: 20),
      ));

      result.fold(
        (failure) => emit(currentState.copyWith(isRefreshing: false)),
        (response) => emit(currentState.copyWith(
          items: response.data ?? [],
          pagination: response.pagination,
          isRefreshing: false,
        )),
      );
    }
  }

  Future<void> _onLoadInventoryItemDetailsRequested(
    LoadInventoryItemDetailsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getInventoryItemByIdUseCase(IdParams(event.itemId));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(InventoryItemDetailsLoaded(response.data!));
        } else {
          emit(const InventoryError('Inventory item not found'));
        }
      },
    );
  }

  Future<void> _onCreateInventoryItemRequested(
    CreateInventoryItemRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _createInventoryItemUseCase(event.request);

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(InventoryItemCreated(response.data!));
        } else {
          emit(const InventoryError('Failed to create inventory item'));
        }
      },
    );
  }

  Future<void> _onUpdateInventoryItemRequested(
    UpdateInventoryItemRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _updateInventoryItemUseCase(event.request);

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(InventoryItemUpdated(response.data!));
        } else {
          emit(const InventoryError('Failed to update inventory item'));
        }
      },
    );
  }

  Future<void> _onDeleteInventoryItemRequested(
    DeleteInventoryItemRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _deleteInventoryItemUseCase(DeleteInventoryItemParams(
      event.itemId,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (success) => emit(InventoryItemDeleted(event.itemId)),
    );
  }

  Future<void> _onUpdateStockLevelRequested(
    UpdateStockLevelRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _updateStockLevelUseCase(UpdateStockLevelParams(
      event.itemId,
      event.newStock,
      event.reason,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(StockLevelUpdated(response.data!));
        } else {
          emit(const InventoryError('Failed to update stock level'));
        }
      },
    );
  }

  Future<void> _onAdjustStockRequested(
    AdjustStockRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _adjustStockUseCase(event.request);

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(StockAdjusted(response.data!));
        } else {
          emit(const InventoryError('Failed to adjust stock'));
        }
      },
    );
  }

  Future<void> _onReserveStockRequested(
    ReserveStockRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _reserveStockUseCase(ReserveStockParams(
      event.itemId,
      event.quantity,
      event.reason,
      orderId: event.orderId,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(StockReserved(response.data!));
        } else {
          emit(const InventoryError('Failed to reserve stock'));
        }
      },
    );
  }

  Future<void> _onReleaseReservedStockRequested(
    ReleaseReservedStockRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _releaseReservedStockUseCase(ReleaseReservedStockParams(
      event.itemId,
      event.quantity,
      event.reason,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ReservedStockReleased(response.data!));
        } else {
          emit(const InventoryError('Failed to release reserved stock'));
        }
      },
    );
  }

  Future<void> _onLoadStockMovementsRequested(
    LoadStockMovementsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getStockMovementsUseCase(GetStockMovementsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(StockMovementsLoaded(
        movements: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onLoadStockMovementsForItemRequested(
    LoadStockMovementsForItemRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getStockMovementsForItemUseCase(GetStockMovementsForItemParams(
      event.itemId,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(StockMovementsForItemLoaded(
        itemId: event.itemId,
        movements: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadLowStockItemsRequested(
    LoadLowStockItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getLowStockItemsUseCase(GetLowStockItemsParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(LowStockItemsLoaded(
        items: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadOutOfStockItemsRequested(
    LoadOutOfStockItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getOutOfStockItemsUseCase(GetOutOfStockItemsParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(OutOfStockItemsLoaded(
        items: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadOverstockedItemsRequested(
    LoadOverstockedItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getOverstockedItemsUseCase(GetOverstockedItemsParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(OverstockedItemsLoaded(
        items: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadExpiringItemsRequested(
    LoadExpiringItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getExpiringItemsUseCase(GetExpiringItemsParams(
      daysAhead: event.daysAhead,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(ExpiringItemsLoaded(
        items: response.data ?? [],
        pagination: response.pagination,
        daysAhead: event.daysAhead,
      )),
    );
  }

  Future<void> _onSearchInventoryItemsRequested(
    SearchInventoryItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _searchInventoryItemsUseCase(SearchInventoryItemsParams(
      event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(InventoryItemsSearched(
        items: response.data ?? [],
        pagination: response.pagination,
        query: event.query,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onFilterInventoryItemsRequested(
    FilterInventoryItemsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    emit(const InventoryLoading());

    final result = await _getInventoryItemsUseCase(GetInventoryItemsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) => emit(InventoryItemsFiltered(
        items: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onLoadInventoryStatisticsRequested(
    LoadInventoryStatisticsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _getInventoryStatisticsUseCase(GetInventoryStatisticsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      categoryId: event.categoryId,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (statistics) => emit(InventoryStatisticsLoaded(statistics)),
    );
  }

  Future<void> _onLoadInventoryValuationRequested(
    LoadInventoryValuationRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _getInventoryValuationUseCase(GetInventoryValuationParams(
      asOfDate: event.asOfDate,
      categoryId: event.categoryId,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (valuation) => emit(InventoryValuationLoaded(valuation)),
    );
  }

  Future<void> _onGenerateReorderSuggestionsRequested(
    GenerateReorderSuggestionsRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _generateReorderSuggestionsUseCase(GenerateReorderSuggestionsParams(
      categoryId: event.categoryId,
      daysAhead: event.daysAhead,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (suggestions) => emit(ReorderSuggestionsGenerated(suggestions)),
    );
  }

  Future<void> _onCreateAutomaticReorderRequested(
    CreateAutomaticReorderRequested event,
    Emitter<InventoryState> emit,
  ) async {
    final result = await _createAutomaticReorderUseCase(CreateAutomaticReorderParams(
      event.itemIds,
    ));

    result.fold(
      (failure) => emit(InventoryError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AutomaticReorderCreated(response.data!));
        } else {
          emit(const InventoryError('Failed to create automatic reorder'));
        }
      },
    );
  }

  void _onClearInventoryState(
    ClearInventoryState event,
    Emitter<InventoryState> emit,
  ) {
    emit(const InventoryInitial());
  }
}
