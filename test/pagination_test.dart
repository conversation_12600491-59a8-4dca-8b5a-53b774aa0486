// import 'package:flutter_test/flutter_test.dart';
// import 'package:hm_collection/features/financial_management/data/datasources/financial_mock_datasource.dart';
// import 'package:hm_collection/shared/models/pagination.dart';
//
// class MockPaginationParams extends Mock implements PaginationParams {
//   @override
//   final int page;
//
//   @override
//   final int perPage;
//
//   @override
//   final String? sortBy;
//
//   @override
//   final String? sortOrder;
//
//   MockPaginationParams({
//     this.page = 1,
//     this.perPage = 20,
//     this.sortBy,
//     this.sortOrder,
//   });
//
//   @override
//   Map<String, dynamic> toJson() => {
//     'page': page,
//     'per_page': perPage,
//     if (sortBy != null) 'sort_by': sortBy,
//     if (sortOrder != null) 'sort_order': sortOrder,
//   };
//
//   @override
//   Map<String, dynamic> toQueryParams() => toJson();
//
//   @override
//   PaginationParams nextPage() => copyWith(page: page + 1);
//
//   @override
//   PaginationParams previousPage() => copyWith(page: page > 1 ? page - 1 : 1);
//
//   @override
//   PaginationParams withPage(int newPage) => copyWith(page: newPage);
//
//   @override
//   PaginationParams withSorting(String? sortBy, String? sortOrder) =>
//       copyWith(sortBy: sortBy, sortOrder: sortOrder);
//
//   @override
//   PaginationParams copyWith({
//     int? page,
//     int? perPage,
//     String? sortBy,
//     String? sortOrder,
//   }) {
//     return MockPaginationParams(
//       page: page ?? this.page,
//       perPage: perPage ?? this.perPage,
//       sortBy: sortBy ?? this.sortBy,
//       sortOrder: sortOrder ?? this.sortOrder,
//     );
//   }
//
//   @override
//   List<Object?> get props => [page, perPage, sortBy, sortOrder];
//
//   @override
//   bool? get stringify => true;
// }
//
// void main() {
//   late FinancialMockDataSource dataSource;
//
//   setUp(() {
//     dataSource = FinancialMockDataSource();
//   });
//
//   test('Pagination calculations return correct types and values', () async {
//     // Create test data
//     final testBudgets = List.generate(50, (index) => Budget(
//       id: 'budget_$index',
//       name: 'Budget $index',
//       amount: 1000.0 + index,
//       startDate: DateTime(2023, 1, 1),
//       endDate: DateTime(2023, 12, 31),
//       category: BudgetCategory.operating,
//       status: BudgetStatus.draft,
//       ownerId: 'user_1',
//       isActive: true,
//       createdAt: DateTime.now(),
//       updatedAt: DateTime.now(),
//     ));
//
//     // Set up the test
//     dataSource.setBudgets(testBudgets);
//
//     // Test pagination with page 1, 10 items per page
//     final pagination = MockPaginationParams(page: 0, limit: 10);
//     final response = await dataSource.getBudgets(pagination: pagination);
//
//     // Verify the response
//     expect(response, isA<ApiListResponse<Budget>>());
//     expect(response.pagination, isA<Pagination>());
//
//     // Verify pagination values
//     expect(response.pagination.currentPage, 1); // 1-based index
//     expect(response.pagination.perPage, 10);
//     expect(response.pagination.total, 50);
//     expect(response.pagination.totalPages, 5);
//     expect(response.pagination.from, 1);
//     expect(response.pagination.to, 10);
//     expect(response.pagination.hasNextPage, true);
//     expect(response.pagination.hasPreviousPage, false);
//
//     // Verify data
//     expect(response.data.length, 10);
//     expect(response.data.first.id, 'budget_0');
//     expect(response.data.last.id, 'budget_9');
//   });
// }
