import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../domain/entities/dashboard_entities.dart';

/// Quick actions section widget
class QuickActionsSection extends StatelessWidget {
  final List<QuickAction> quickActions;
  final Function(String) onActionTap;

  const QuickActionsSection({
    super.key,
    required this.quickActions,
    required this.onActionTap,
  });

  @override
  Widget build(BuildContext context) {
    if (quickActions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Sized<PERSON>ox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: quickActions.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.only(
                  right: index < quickActions.length - 1 ? 16 : 0,
                ),
                child: QuickActionCard(
                  action: quickActions[index],
                  onTap: () => onActionTap(quickActions[index].id),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Individual quick action card
class QuickActionCard extends StatelessWidget {
  final QuickAction action;
  final VoidCallback onTap;

  const QuickActionCard({
    super.key,
    required this.action,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 140,
      height: 116, // Fixed height to prevent overflow
      child: Card(
        elevation: 2,
        margin: const EdgeInsets.only(bottom: 4), // Add margin to prevent clipping
        child: InkWell(
          onTap: action.isEnabled ? onTap : null,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8), // Reduced vertical padding
            child: Column(
              mainAxisSize: MainAxisSize.min, // Use min to prevent overflow
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 40, // Slightly smaller icon container
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getActionColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    _getActionIcon(),
                    color: _getActionColor(),
                    size: 20, // Slightly smaller icon
                  ),
                ),
                const SizedBox(height: 6), // Reduced spacing
                Text(
                  action.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 12, // Slightly smaller font
                    fontWeight: FontWeight.w600,
                    color: action.isEnabled 
                        ? AppColors.textPrimary 
                        : AppColors.textDisabled,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getActionColor() {
    if (!action.isEnabled) {
      return AppColors.textDisabled;
    }
    
    if (action.color != null) {
      return Color(int.parse(action.color!.replaceFirst('#', '0xFF')));
    }
    
    // Default colors based on action type
    switch (action.id) {
      case 'create_order':
        return AppColors.primary;
      case 'view_production':
        return AppColors.info;
      case 'check_inventory':
        return AppColors.warning;
      case 'quality_report':
        return AppColors.success;
      case 'user_management':
        return AppColors.secondary;
      case 'analytics':
        return const Color(0xFF8B5CF6);
      default:
        return AppColors.primary;
    }
  }

  IconData _getActionIcon() {
    switch (action.icon) {
      case 'add_shopping_cart':
        return Icons.add_shopping_cart;
      case 'precision_manufacturing':
        return Icons.precision_manufacturing;
      case 'inventory':
        return Icons.inventory_2;
      case 'verified':
        return Icons.verified;
      case 'people':
        return Icons.people;
      case 'analytics':
        return Icons.analytics;
      case 'settings':
        return Icons.settings;
      case 'report':
        return Icons.assessment;
      case 'calendar':
        return Icons.calendar_today;
      case 'notifications':
        return Icons.notifications;
      default:
        return Icons.touch_app;
    }
  }
}

/// Expandable quick actions section
class ExpandableQuickActionsSection extends StatefulWidget {
  final List<QuickAction> quickActions;
  final Function(String) onActionTap;
  final int initialVisibleCount;

  const ExpandableQuickActionsSection({
    super.key,
    required this.quickActions,
    required this.onActionTap,
    this.initialVisibleCount = 4,
  });

  @override
  State<ExpandableQuickActionsSection> createState() => _ExpandableQuickActionsSectionState();
}

class _ExpandableQuickActionsSectionState extends State<ExpandableQuickActionsSection> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    if (widget.quickActions.isEmpty) {
      return const SizedBox.shrink();
    }

    final visibleActions = _isExpanded 
        ? widget.quickActions 
        : widget.quickActions.take(widget.initialVisibleCount).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (widget.quickActions.length > widget.initialVisibleCount)
              TextButton(
                onPressed: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                child: Text(_isExpanded ? 'Show Less' : 'Show More'),
              ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _getCrossAxisCount(context),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
          ),
          itemCount: visibleActions.length,
          itemBuilder: (context, index) {
            return QuickActionCard(
              action: visibleActions[index],
              onTap: () => widget.onActionTap(visibleActions[index].id),
            );
          },
        ),
      ],
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 6;
    if (width > 800) return 4;
    if (width > 600) return 3;
    return 2;
  }
}

/// Floating quick action button
class FloatingQuickAction extends StatelessWidget {
  final QuickAction action;
  final VoidCallback onTap;

  const FloatingQuickAction({
    super.key,
    required this.action,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: action.isEnabled ? onTap : null,
      backgroundColor: _getActionColor(),
      foregroundColor: Colors.white,
      icon: Icon(_getActionIcon()),
      label: Text(action.title),
      tooltip: action.description,
    );
  }

  Color _getActionColor() {
    if (!action.isEnabled) {
      return AppColors.textDisabled;
    }
    
    if (action.color != null) {
      return Color(int.parse(action.color!.replaceFirst('#', '0xFF')));
    }
    
    return AppColors.primary;
  }

  IconData _getActionIcon() {
    switch (action.icon) {
      case 'add_shopping_cart':
        return Icons.add_shopping_cart;
      case 'precision_manufacturing':
        return Icons.precision_manufacturing;
      case 'inventory':
        return Icons.inventory_2;
      case 'verified':
        return Icons.verified;
      case 'people':
        return Icons.people;
      case 'analytics':
        return Icons.analytics;
      default:
        return Icons.touch_app;
    }
  }
}
