import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

import '../auth/entities/user_entities.dart';
import '../../shared/enums/common_enums.dart';

/// Service for managing real-time user status and presence
@injectable
class UserStatusService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final DeviceInfoPlugin _deviceInfo;

  Timer? _presenceTimer;
  StreamSubscription<DocumentSnapshot>? _userStatusSubscription;
  String? _currentSessionId;

  UserStatusService(
    this._firestore,
    this._auth,
    this._deviceInfo,
  );

  /// Initialize user status monitoring
  Future<void> initialize() async {
    final user = _auth.currentUser;
    if (user != null) {
      await _startPresenceTracking(user.uid);
      _startStatusMonitoring(user.uid);
    }

    // Listen for auth state changes
    _auth.authStateChanges().listen((user) async {
      if (user != null) {
        await _startPresenceTracking(user.uid);
        _startStatusMonitoring(user.uid);
      } else {
        await _stopPresenceTracking();
        _stopStatusMonitoring();
      }
    });
  }

  /// Start tracking user presence
  Future<void> _startPresenceTracking(String userId) async {
    try {
      // Create session
      _currentSessionId = await _createUserSession(userId);

      // Update user as online
      await _updateUserPresence(userId, true);

      // Start periodic presence updates
      _presenceTimer = Timer.periodic(
        const Duration(minutes: 1),
        (_) => _updateUserPresence(userId, true),
      );

      // Handle app lifecycle changes
      _handleAppLifecycleChanges(userId);
    } catch (e) {
      print('Error starting presence tracking: $e');
    }
  }

  /// Stop tracking user presence
  Future<void> _stopPresenceTracking() async {
    _presenceTimer?.cancel();
    _presenceTimer = null;

    final user = _auth.currentUser;
    if (user != null) {
      await _updateUserPresence(user.uid, false);
      if (_currentSessionId != null) {
        await _endUserSession(_currentSessionId!);
        _currentSessionId = null;
      }
    }
  }

  /// Start monitoring user status changes
  void _startStatusMonitoring(String userId) {
    _userStatusSubscription = _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        final userData = snapshot.data() as Map<String, dynamic>;
        final status = CommonStatus.values.firstWhere(
          (s) => s.name == userData['status'],
          orElse: () => CommonStatus.active,
        );

        // Handle status changes
        _handleStatusChange(status);
      }
    });
  }

  /// Stop monitoring user status changes
  void _stopStatusMonitoring() {
    _userStatusSubscription?.cancel();
    _userStatusSubscription = null;
  }

  /// Update user presence status
  Future<void> _updateUserPresence(String userId, bool isOnline) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'isOnline': isOnline,
        'lastActiveAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update session if exists
      if (_currentSessionId != null) {
        await _firestore.collection('userSessions').doc(_currentSessionId!).update({
          'lastActiveAt': FieldValue.serverTimestamp(),
          'isActive': isOnline,
        });
      }
    } catch (e) {
      print('Error updating user presence: $e');
    }
  }

  /// Create a new user session
  Future<String> _createUserSession(String userId) async {
    try {
      final deviceInfo = await _getDeviceInfo();
      final sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';

      await _firestore.collection('userSessions').doc(sessionId).set({
        'sessionId': sessionId,
        'userId': userId,
        'deviceId': deviceInfo['deviceId'],
        'deviceName': deviceInfo['deviceName'],
        'platform': deviceInfo['platform'],
        'appVersion': deviceInfo['appVersion'],
        'loginAt': FieldValue.serverTimestamp(),
        'logoutAt': null,
        'lastActiveAt': FieldValue.serverTimestamp(),
        'ipAddress': await _getIPAddress(),
        'isActive': true,
      });

      // Update user's current session
      await _firestore.collection('users').doc(userId).update({
        'currentSessionId': sessionId,
        'lastLoginAt': FieldValue.serverTimestamp(),
      });

      return sessionId;
    } catch (e) {
      print('Error creating user session: $e');
      rethrow;
    }
  }

  /// End user session
  Future<void> _endUserSession(String sessionId) async {
    try {
      await _firestore.collection('userSessions').doc(sessionId).update({
        'logoutAt': FieldValue.serverTimestamp(),
        'isActive': false,
      });
    } catch (e) {
      print('Error ending user session: $e');
    }
  }

  /// Get device information
  Future<Map<String, String>> _getDeviceInfo() async {
    try {
      if (kIsWeb) {
        final webInfo = await _deviceInfo.webBrowserInfo;
        return {
          'deviceId': 'web_${webInfo.userAgent?.hashCode ?? 'unknown'}',
          'deviceName': '${webInfo.browserName} ${webInfo.platform}',
          'platform': 'web',
          'appVersion': '1.0.0', // Get from package info
        };
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'deviceId': androidInfo.id,
          'deviceName': '${androidInfo.brand} ${androidInfo.model}',
          'platform': 'android',
          'appVersion': '1.0.0', // Get from package info
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return {
          'deviceId': iosInfo.identifierForVendor ?? 'unknown',
          'deviceName': '${iosInfo.name} ${iosInfo.model}',
          'platform': 'ios',
          'appVersion': '1.0.0', // Get from package info
        };
      } else {
        return {
          'deviceId': 'unknown',
          'deviceName': 'Unknown Device',
          'platform': 'unknown',
          'appVersion': '1.0.0',
        };
      }
    } catch (e) {
      print('Error getting device info: $e');
      return {
        'deviceId': 'unknown',
        'deviceName': 'Unknown Device',
        'platform': 'unknown',
        'appVersion': '1.0.0',
      };
    }
  }

  /// Get IP address (simplified implementation)
  Future<String> _getIPAddress() async {
    // In a real implementation, you would get the actual IP address
    // This is a placeholder
    return 'unknown';
  }

  /// Handle app lifecycle changes
  void _handleAppLifecycleChanges(String userId) {
    // This would be implemented using WidgetsBindingObserver
    // to detect when the app goes to background/foreground
  }

  /// Handle user status changes
  void _handleStatusChange(CommonStatus status) {
    switch (status) {
      case CommonStatus.suspended:
      case CommonStatus.inactive:
        // Force logout if user is suspended or inactive
        _auth.signOut();
        break;
      case CommonStatus.active:
        // User is active, continue normal operation
        break;
      case CommonStatus.pending:
        // User is pending approval
        break;
      case CommonStatus.archived:
        // User is archived, force logout
        _auth.signOut();
        break;
    }
  }

  /// Get user's active sessions
  Stream<List<UserSession>> getUserSessions(String userId) {
    return _firestore
        .collection('userSessions')
        .where('userId', isEqualTo: userId)
        .where('isActive', isEqualTo: true)
        .orderBy('loginAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => UserSession.fromFirestore(doc))
            .toList());
  }

  /// Terminate a specific session
  Future<void> terminateSession(String sessionId) async {
    try {
      await _firestore.collection('userSessions').doc(sessionId).update({
        'logoutAt': FieldValue.serverTimestamp(),
        'isActive': false,
        'terminatedBy': _auth.currentUser?.uid,
      });
    } catch (e) {
      print('Error terminating session: $e');
      rethrow;
    }
  }

  /// Get real-time user status
  Stream<AppUser?> getUserStatus(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        return AppUser.fromFirestore(snapshot);
      }
      return null;
    });
  }

  /// Update user's last active time manually
  Future<void> updateLastActive() async {
    final user = _auth.currentUser;
    if (user != null) {
      await _updateUserPresence(user.uid, true);
    }
  }

  /// Check if user is online
  Future<bool> isUserOnline(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return data['isOnline'] ?? false;
      }
      return false;
    } catch (e) {
      print('Error checking user online status: $e');
      return false;
    }
  }

  /// Get users who are currently online
  Stream<List<AppUser>> getOnlineUsers() {
    return _firestore
        .collection('users')
        .where('isOnline', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => AppUser.fromFirestore(doc))
            .toList());
  }

  /// Dispose resources
  void dispose() {
    _presenceTimer?.cancel();
    _userStatusSubscription?.cancel();
  }
}

/// Extension to create UserSession from Firestore document
extension UserSessionFirestore on UserSession {
  static UserSession fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserSession(
      sessionId: data['sessionId'] ?? '',
      userId: data['userId'] ?? '',
      deviceId: data['deviceId'] ?? '',
      deviceName: data['deviceName'] ?? '',
      platform: data['platform'] ?? '',
      appVersion: data['appVersion'] ?? '',
      loginAt: (data['loginAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      logoutAt: (data['logoutAt'] as Timestamp?)?.toDate(),
      lastActiveAt: (data['lastActiveAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      ipAddress: data['ipAddress'] ?? '',
      location: data['location'] != null 
          ? Map<String, String>.from(data['location'])
          : null,
      isActive: data['isActive'] ?? false,
    );
  }
}

/// Extension to create AppUser from Firestore document
extension AppUserFirestore on AppUser {
  static AppUser fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AppUser(
      id: doc.id,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      uid: data['uid'] ?? '',
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      emailVerified: data['emailVerified'] ?? false,
      phoneNumber: data['phoneNumber'],
      role: UserRole.values.firstWhere(
        (role) => role.name == data['role'],
        orElse: () => UserRole.sewingOperator,
      ),
      status: CommonStatus.values.firstWhere(
        (status) => status.name == data['status'],
        orElse: () => CommonStatus.pending,
      ),
      profile: UserProfile.fromMap(data['profile'] ?? {}),
      permissions: List<String>.from(data['permissions'] ?? []),
      departmentIds: List<String>.from(data['departmentIds'] ?? []),
      currentDepartmentId: data['currentDepartmentId'],
      lastLoginAt: (data['lastLoginAt'] as Timestamp?)?.toDate(),
      lastActiveAt: (data['lastActiveAt'] as Timestamp?)?.toDate(),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }
}
