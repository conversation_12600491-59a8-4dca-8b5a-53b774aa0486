import 'package:flutter/material.dart';

import '../../domain/entities/production_entities.dart';

/// Timeline view for production orders
class ProductionTimelineView extends StatelessWidget {
  final List<ProductionOrder> orders;
  final Function(ProductionOrder)? onOrderTap;
  final DateTime? startDate;
  final DateTime? endDate;

  const ProductionTimelineView({
    Key? key,
    required this.orders,
    this.onOrderTap,
    this.startDate,
    this.endDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timeline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No production orders in timeline',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTimelineHeader(context),
          const SizedBox(height: 16),
          ...orders.map((order) => _buildTimelineItem(context, order)),
        ],
      ),
    );
  }

  Widget _buildTimelineHeader(BuildContext context) {
    final start = startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now().add(const Duration(days: 30));

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            Icons.timeline,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            'Production Timeline',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Text(
            '${_formatDate(start)} - ${_formatDate(end)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(BuildContext context, ProductionOrder order) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Timeline indicator
            Column(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getStatusColor(order.status),
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Container(
                    width: 2,
                    color: Colors.grey[300],
                  ),
                ),
              ],
            ),
            const SizedBox(width: 16),
            
            // Order card
            Expanded(
              child: Card(
                elevation: 2,
                child: InkWell(
                  onTap: () => onOrderTap?.call(order),
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                order.orderNumber,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor(order.status).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                order.status.displayName,
                                style: TextStyle(
                                  color: _getStatusColor(order.status),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          order.clientName,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Quantity: ${order.totalQuantity}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // Timeline bar
                        _buildTimelineBar(context, order),
                        
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.schedule,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${_formatDate(order.plannedStartDate)} - ${_formatDate(order.plannedEndDate)}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineBar(BuildContext context, ProductionOrder order) {
    final now = DateTime.now();
    final totalDuration = order.plannedEndDate.difference(order.plannedStartDate).inDays;
    final elapsedDuration = now.difference(order.plannedStartDate).inDays;
    final progress = totalDuration > 0 ? (elapsedDuration / totalDuration).clamp(0.0, 1.0) : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${(progress * 100).toStringAsFixed(0)}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor(order.status)),
        ),
      ],
    );
  }

  Color _getStatusColor(ProductionStatus status) {
    switch (status) {
      case ProductionStatus.planned:
      case ProductionStatus.scheduled:
        return Colors.blue;
      case ProductionStatus.inProgress:
        return Colors.orange;
      case ProductionStatus.completed:
        return Colors.green;
      case ProductionStatus.cancelled:
        return Colors.red;
      case ProductionStatus.paused:
      case ProductionStatus.onHold:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Gantt chart style timeline view
class ProductionGanttView extends StatelessWidget {
  final List<ProductionOrder> orders;
  final Function(ProductionOrder)? onOrderTap;
  final DateTime startDate;
  final DateTime endDate;

  const ProductionGanttView({
    Key? key,
    required this.orders,
    this.onOrderTap,
    required this.startDate,
    required this.endDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final totalDays = endDate.difference(startDate).inDays;

    return SingleChildScrollView(
      child: Column(
        children: [
          // Header with dates
          _buildDateHeader(context, totalDays),
          
          // Orders
          ...orders.map((order) => _buildGanttRow(context, order, totalDays)),
        ],
      ),
    );
  }

  Widget _buildDateHeader(BuildContext context, int totalDays) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          SizedBox(
            width: 200,
            child: Text(
              'Production Orders',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: List.generate(
                (totalDays / 7).ceil(),
                (index) {
                  final weekStart = startDate.add(Duration(days: index * 7));
                  return Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Text(
                        '${weekStart.day}/${weekStart.month}',
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGanttRow(BuildContext context, ProductionOrder order, int totalDays) {
    final orderStart = order.plannedStartDate.difference(startDate).inDays;
    final orderDuration = order.plannedEndDate.difference(order.plannedStartDate).inDays;
    final startPercent = orderStart / totalDays;
    final widthPercent = orderDuration / totalDays;

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 200,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  order.orderNumber,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  order.clientName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Expanded(
            child: Stack(
              children: [
                Container(
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                ),
                Positioned(
                  left: startPercent * MediaQuery.of(context).size.width * 0.6,
                  child: GestureDetector(
                    onTap: () => onOrderTap?.call(order),
                    child: Container(
                      width: widthPercent * MediaQuery.of(context).size.width * 0.6,
                      height: 30,
                      margin: const EdgeInsets.symmetric(vertical: 5),
                      decoration: BoxDecoration(
                        color: _getStatusColor(order.status),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: Text(
                          order.orderNumber,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ProductionStatus status) {
    switch (status) {
      case ProductionStatus.planned:
      case ProductionStatus.scheduled:
        return Colors.blue;
      case ProductionStatus.inProgress:
        return Colors.orange;
      case ProductionStatus.completed:
        return Colors.green;
      case ProductionStatus.cancelled:
        return Colors.red;
      case ProductionStatus.paused:
      case ProductionStatus.onHold:
        return Colors.grey;
    }
  }
}