import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../models/api_response.dart';
import '../models/pagination.dart';

/// Base repository interface
abstract class BaseRepository<T, ID> {
  /// Get all items with optional pagination
  Future<Either<Failure, ApiListResponse<T>>> getAll({
    PaginationParams? pagination,
    Map<String, dynamic>? filters,
  });

  /// Get item by ID
  Future<Either<Failure, ApiResponse<T>>> getById(ID id);

  /// Create new item
  Future<Either<Failure, ApiResponse<T>>> create(T item);

  /// Update existing item
  Future<Either<Failure, ApiResponse<T>>> update(ID id, T item);

  /// Delete item by ID
  Future<Either<Failure, ApiVoidResponse>> delete(ID id);

  /// Search items
  Future<Either<Failure, ApiListResponse<T>>> search({
    required String query,
    PaginationParams? pagination,
    Map<String, dynamic>? filters,
  });

  /// Get items by filter
  Future<Either<Failure, ApiListResponse<T>>> getByFilter(
    Map<String, dynamic> filters, {
    PaginationParams? pagination,
  });

  /// Bulk create items
  Future<Either<Failure, ApiListResponse<T>>> bulkCreate(List<T> items);

  /// Bulk update items
  Future<Either<Failure, ApiListResponse<T>>> bulkUpdate(
    Map<ID, T> items,
  );

  /// Bulk delete items
  Future<Either<Failure, ApiVoidResponse>> bulkDelete(List<ID> ids);

  /// Check if item exists
  Future<Either<Failure, bool>> exists(ID id);

  /// Get count of items
  Future<Either<Failure, int>> count({
    Map<String, dynamic>? filters,
  });
}

/// Base repository implementation with common functionality
abstract class BaseRepositoryImpl<T, ID> implements BaseRepository<T, ID> {
  /// API endpoint for this resource
  String get endpoint;

  /// Convert JSON to entity
  T fromJson(Map<String, dynamic> json);

  /// Convert entity to JSON
  Map<String, dynamic> toJson(T entity);

  /// Get ID from entity
  ID getId(T entity);

  @override
  Future<Either<Failure, bool>> exists(ID id) async {
    final result = await getById(id);
    return result.fold(
      (failure) => Left(failure),
      (response) => Right(response.success && response.hasData),
    );
  }

  @override
  Future<Either<Failure, int>> count({
    Map<String, dynamic>? filters,
  }) async {
    final result = await getAll(
      pagination: const PaginationParams(page: 1, perPage: 1),
      filters: filters,
    );
    
    return result.fold(
      (failure) => Left(failure),
      (response) => Right(response.pagination?.total ?? 0),
    );
  }

  /// Build query parameters from pagination and filters
  Map<String, dynamic> buildQueryParams({
    PaginationParams? pagination,
    Map<String, dynamic>? filters,
  }) {
    final Map<String, dynamic> params = {};

    if (pagination != null) {
      params.addAll(pagination.toQueryParams());
    }

    if (filters != null) {
      params.addAll(filters);
    }

    return params;
  }

  /// Handle API response and convert to domain objects
  Either<Failure, ApiResponse<T>> handleSingleResponse(
    ApiResponse<Map<String, dynamic>> response,
  ) {
    if (!response.success) {
      return Left(ServerFailure(
        response.firstError ?? 'Unknown error occurred',
        details: response.errors,
      ));
    }

    if (!response.hasData) {
      return Left(ServerFailure('No data received'));
    }

    try {
      final entity = fromJson(response.data!);
      return Right(ApiResponse.success(
        data: entity,
        message: response.message,
        pagination: response.pagination,
        meta: response.meta,
      ));
    } catch (e) {
      return Left(FormatFailure('Failed to parse response: $e'));
    }
  }

  /// Handle API list response and convert to domain objects
  Either<Failure, ApiListResponse<T>> handleListResponse(
    ApiResponse<List<Map<String, dynamic>>> response,
  ) {
    if (!response.success) {
      return Left(ServerFailure(
        response.firstError ?? 'Unknown error occurred',
        details: response.errors,
      ));
    }

    try {
      final entities = response.data?.map((json) => fromJson(json)).toList() ?? [];
      return Right(ApiListResponse<T>(
        success: true,
        data: entities,
        message: response.message,
        pagination: response.pagination,
        meta: response.meta,
      ));
    } catch (e) {
      return Left(FormatFailure('Failed to parse response: $e'));
    }
  }

  /// Handle void response
  Either<Failure, ApiVoidResponse> handleVoidResponse(
    ApiVoidResponse response,
  ) {
    if (!response.success) {
      return Left(ServerFailure(
        response.firstError ?? 'Unknown error occurred',
        details: response.errors,
      ));
    }

    return Right(response);
  }

  /// Validate entity before operations
  Either<Failure, T> validateEntity(T entity) {
    // Override in subclasses for specific validation
    return Right(entity);
  }

  /// Validate ID format
  Either<Failure, ID> validateId(ID id) {
    // Override in subclasses for specific validation
    return Right(id);
  }

  /// Transform entity before create/update
  T transformForSave(T entity) {
    // Override in subclasses for specific transformations
    return entity;
  }

  /// Transform entity after fetch
  T transformAfterFetch(T entity) {
    // Override in subclasses for specific transformations
    return entity;
  }
}

/// Repository mixin for caching functionality
mixin CacheRepositoryMixin<T, ID> on BaseRepositoryImpl<T, ID> {
  /// Cache duration
  Duration get cacheDuration => const Duration(minutes: 5);

  /// Cache key prefix
  String get cacheKeyPrefix => endpoint;

  /// Generate cache key for single item
  String getCacheKey(ID id) => '${cacheKeyPrefix}_$id';

  /// Generate cache key for list
  String getListCacheKey({
    PaginationParams? pagination,
    Map<String, dynamic>? filters,
  }) {
    final params = buildQueryParams(pagination: pagination, filters: filters);
    final paramString = params.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');
    return '${cacheKeyPrefix}_list_$paramString';
  }

  /// Clear cache for specific item
  Future<void> clearItemCache(ID id);

  /// Clear all cache for this repository
  Future<void> clearAllCache();

  /// Get from cache
  Future<T?> getFromCache(String key);

  /// Save to cache
  Future<void> saveToCache(String key, T item);

  /// Get list from cache
  Future<List<T>?> getListFromCache(String key);

  /// Save list to cache
  Future<void> saveListToCache(String key, List<T> items);
}

/// Repository mixin for offline functionality
mixin OfflineRepositoryMixin<T, ID> on BaseRepositoryImpl<T, ID> {
  /// Get offline storage key
  String get offlineStorageKey => '${endpoint}_offline';

  /// Save for offline use
  Future<void> saveForOffline(T item);

  /// Get offline items
  Future<List<T>> getOfflineItems();

  /// Sync offline changes
  Future<Either<Failure, List<T>>> syncOfflineChanges();

  /// Check if item is available offline
  Future<bool> isAvailableOffline(ID id);

  /// Mark item for offline deletion
  Future<void> markForOfflineDeletion(ID id);

  /// Get pending offline operations
  Future<List<OfflineOperation<T, ID>>> getPendingOperations();
}

/// Represents an offline operation
class OfflineOperation<T, ID> {
  final OfflineOperationType type;
  final ID? id;
  final T? data;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const OfflineOperation({
    required this.type,
    this.id,
    this.data,
    required this.timestamp,
    this.metadata,
  });
}

/// Types of offline operations
enum OfflineOperationType {
  create,
  update,
  delete,
}

/// Repository mixin for real-time updates
mixin RealtimeRepositoryMixin<T, ID> on BaseRepositoryImpl<T, ID> {
  /// Subscribe to real-time updates
  Stream<T> subscribeToUpdates(ID id);

  /// Subscribe to list updates
  Stream<List<T>> subscribeToListUpdates({
    Map<String, dynamic>? filters,
  });

  /// Unsubscribe from updates
  Future<void> unsubscribeFromUpdates();

  /// Handle real-time update
  void handleRealtimeUpdate(Map<String, dynamic> update);
}

/// Repository mixin for audit functionality
mixin AuditRepositoryMixin<T, ID> on BaseRepositoryImpl<T, ID> {
  /// Get audit trail for item
  Future<Either<Failure, List<AuditEntry>>> getAuditTrail(ID id);

  /// Log audit entry
  Future<void> logAuditEntry(AuditEntry entry);
}

/// Represents an audit entry
class AuditEntry {
  final String id;
  final String entityId;
  final String entityType;
  final String action;
  final String? userId;
  final Map<String, dynamic>? oldValues;
  final Map<String, dynamic>? newValues;
  final DateTime timestamp;
  final String? reason;

  const AuditEntry({
    required this.id,
    required this.entityId,
    required this.entityType,
    required this.action,
    this.userId,
    this.oldValues,
    this.newValues,
    required this.timestamp,
    this.reason,
  });
}
