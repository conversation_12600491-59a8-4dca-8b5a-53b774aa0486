import 'package:equatable/equatable.dart';

/// Request model for creating a quality standard
class CreateQualityStandardRequest extends Equatable {
  final String name;
  final String description;
  final String category;
  final List<String> applicableDepartments;
  final Map<String, dynamic> criteria;
  final String? referenceDocumentUrl;
  final String? notes;

  const CreateQualityStandardRequest({
    required this.name,
    required this.description,
    required this.category,
    required this.applicableDepartments,
    required this.criteria,
    this.referenceDocumentUrl,
    this.notes,
  });

  /// Convert the request to a JSON map
  Map<String, dynamic> toJson() => {
        'name': name,
        'description': description,
        'category': category,
        'applicableDepartments': applicableDepartments,
        'criteria': criteria,
        if (referenceDocumentUrl != null) 'referenceDocumentUrl': referenceDocumentUrl,
        if (notes != null) 'notes': notes,
      };

  @override
  List<Object?> get props => [
        name,
        description,
        category,
        applicableDepartments,
        criteria,
        referenceDocumentUrl,
        notes,
      ];
}
