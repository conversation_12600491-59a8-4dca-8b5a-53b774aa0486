// // This file contains the remaining methods for FirebaseDenimInventoryRepository
// // These methods should be added to the main repository file
//
//   // Return Management
//   import 'package:dartz/dartz.dart';
//
// import '../../../../core/errors/failures.dart';
// import '../../domain/entities/denim_inventory_entities.dart';
// import '../../domain/requests/denim_inventory_requests.dart';
//
// @override
//   Future<Either<Failure, DenimReturnRecord>> createReturnRecord(
//     CreateReturnRequest request,
//   ) async {
//     try {
//       // Start a batch write for atomic operations
//       final batch = _firestore.batch();
//
//       // Generate serial number
//       final srNoResult = await getNextSerialNumber('return');
//       if (srNoResult.isLeft()) {
//         return Left(srNoResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
//       }
//       final srNo = srNoResult.fold((l) => 0, (r) => r);
//
//       // Get existing denim roll
//       final rollResult = await getDenimRollByRollId(request.rollId);
//       if (rollResult.isLeft()) {
//         return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
//       }
//
//       final existingRoll = rollResult.fold((l) => null, (r) => r);
//       if (existingRoll == null) {
//         return const Left(ValidationFailure('Roll ID not found'));
//       }
//
//       // Validate return quantity
//       if (request.quantityReturned > existingRoll.balanceQty) {
//         return const Left(ValidationFailure('Return quantity cannot exceed available balance'));
//       }
//
//       final now = DateTime.now();
//
//       // Create return record
//       final returnDocRef = _firestore.collection(_returnRecordsCollection).doc();
//       final returnRecord = DenimReturnRecordModel(
//         id: returnDocRef.id,
//         srNo: srNo,
//         returnDate: request.returnDate,
//         rollId: request.rollId,
//         supplierName: request.supplierName,
//         supplierId: request.supplierId,
//         reasonForReturn: request.reasonForReturn,
//         quantityReturned: request.quantityReturned,
//         unit: request.unit,
//         debitNoteNo: request.debitNoteNo,
//         remarks: request.remarks,
//         status: ReturnStatus.pending,
//         createdAt: now,
//         updatedAt: now,
//       );
//
//       // Update denim roll with returned quantity
//       final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
//       final updatedRoll = (existingRoll as DenimRollModel).updateStock(
//         returnedQty: existingRoll.returnedQty + request.quantityReturned,
//         status: existingRoll.balanceQty - request.quantityReturned <= 0
//             ? DenimRollStatus.returned
//             : existingRoll.status,
//       );
//
//       // Add to batch
//       batch.set(returnDocRef, returnRecord.toFirestore());
//       batch.update(rollDocRef, updatedRoll.toFirestore());
//
//       // Commit batch
//       await batch.commit();
//
//       return Right(returnRecord);
//     } catch (e) {
//       return Left(ServerFailure('Failed to create return record: ${e.toString()}'));
//     }
//   }
//
//   @override
//   Future<Either<Failure, List<DenimReturnRecord>>> getReturnRecords({
//     String? supplierId,
//     DateTime? fromDate,
//     DateTime? toDate,
//     ReturnStatus? status,
//     int? limit,
//     String? lastDocumentId,
//   }) async {
//     try {
//       Query query = _firestore.collection(_returnRecordsCollection);
//
//       // Apply filters
//       if (supplierId != null) {
//         query = query.where('supplierId', isEqualTo: supplierId);
//       }
//       if (fromDate != null) {
//         query = query.where('returnDate', isGreaterThanOrEqualTo: Timestamp.fromDate(fromDate));
//       }
//       if (toDate != null) {
//         query = query.where('returnDate', isLessThanOrEqualTo: Timestamp.fromDate(toDate));
//       }
//       if (status != null) {
//         query = query.where('status', isEqualTo: status.value);
//       }
//
//       // Apply pagination
//       if (lastDocumentId != null) {
//         final lastDoc = await _firestore
//             .collection(_returnRecordsCollection)
//             .doc(lastDocumentId)
//             .get();
//         if (lastDoc.exists) {
//           query = query.startAfterDocument(lastDoc);
//         }
//       }
//
//       query = query.orderBy('returnDate', descending: true);
//
//       if (limit != null) {
//         query = query.limit(limit);
//       }
//
//       final snapshot = await query.get();
//       final returnRecords = snapshot.docs
//           .map((doc) => DenimReturnRecordModel.fromFirestore(doc))
//           .toList();
//
//       return Right(returnRecords);
//     } catch (e) {
//       return Left(ServerFailure('Failed to get return records: ${e.toString()}'));
//     }
//   }
//
//   @override
//   Future<Either<Failure, DenimReturnRecord>> getReturnRecordById(String id) async {
//     try {
//       final doc = await _firestore
//           .collection(_returnRecordsCollection)
//           .doc(id)
//           .get();
//
//       if (!doc.exists) {
//         return const Left(NotFoundFailure('Return record not found'));
//       }
//
//       final returnRecord = DenimReturnRecordModel.fromFirestore(doc);
//       return Right(returnRecord);
//     } catch (e) {
//       return Left(ServerFailure('Failed to get return record: ${e.toString()}'));
//     }
//   }
//
//   @override
//   Future<Either<Failure, List<DenimReturnRecord>>> getReturnRecordsByRollId(
//     String rollId,
//   ) async {
//     try {
//       final query = await _firestore
//           .collection(_returnRecordsCollection)
//           .where('rollId', isEqualTo: rollId)
//           .orderBy('returnDate', descending: true)
//           .get();
//
//       final returnRecords = query.docs
//           .map((doc) => DenimReturnRecordModel.fromFirestore(doc))
//           .toList();
//
//       return Right(returnRecords);
//     } catch (e) {
//       return Left(ServerFailure('Failed to get return records by roll ID: ${e.toString()}'));
//     }
//   }
//
//   @override
//   Future<Either<Failure, DenimReturnRecord>> updateReturnStatus(
//     String id,
//     ReturnStatus status,
//     String? remarks,
//   ) async {
//     try {
//       final docRef = _firestore.collection(_returnRecordsCollection).doc(id);
//       final updateData = {
//         'status': status.value,
//         'updatedAt': Timestamp.fromDate(DateTime.now()),
//       };
//
//       if (remarks != null) {
//         updateData['remarks'] = remarks;
//       }
//
//       await docRef.update(updateData);
//
//       // Get updated document
//       final updatedDoc = await docRef.get();
//       final updatedReturnRecord = DenimReturnRecordModel.fromFirestore(updatedDoc);
//
//       return Right(updatedReturnRecord);
//     } catch (e) {
//       return Left(ServerFailure('Failed to update return status: ${e.toString()}'));
//     }
//   }
//
//   // Stock Management
//   @override
//   Future<Either<Failure, DenimRoll>> updateStock(
//     UpdateStockRequest request,
//   ) async {
//     try {
//       // Get existing denim roll
//       final rollResult = await getDenimRollByRollId(request.rollId);
//       if (rollResult.isLeft()) {
//         return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
//       }
//
//       final existingRoll = rollResult.fold((l) => null, (r) => r);
//       if (existingRoll == null) {
//         return const Left(ValidationFailure('Roll ID not found'));
//       }
//
//       // Update stock quantities
//       final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
//       final updatedRoll = (existingRoll as DenimRollModel).updateStock(
//         usedQty: request.usedQty,
//         returnedQty: request.returnedQty,
//         status: request.status,
//         location: request.location,
//       );
//
//       await rollDocRef.update(updatedRoll.toFirestore());
//
//       return Right(updatedRoll);
//     } catch (e) {
//       return Left(ServerFailure('Failed to update stock: ${e.toString()}'));
//     }
//   }
//
//   @override
//   Future<Either<Failure, List<DenimRoll>>> bulkUpdateStock(
//     BulkStockUpdateRequest request,
//   ) async {
//     try {
//       final batch = _firestore.batch();
//       final updatedRolls = <DenimRoll>[];
//
//       for (final updateRequest in request.updates) {
//         // Get existing denim roll
//         final rollResult = await getDenimRollByRollId(updateRequest.rollId);
//         if (rollResult.isLeft()) {
//           return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
//         }
//
//         final existingRoll = rollResult.fold((l) => null, (r) => r);
//         if (existingRoll == null) {
//           return Left(ValidationFailure('Roll ID ${updateRequest.rollId} not found'));
//         }
//
//         // Update stock quantities
//         final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
//         final updatedRoll = (existingRoll as DenimRollModel).updateStock(
//           usedQty: updateRequest.usedQty,
//           returnedQty: updateRequest.returnedQty,
//           status: updateRequest.status,
//           location: updateRequest.location,
//         );
//
//         batch.update(rollDocRef, updatedRoll.toFirestore());
//         updatedRolls.add(updatedRoll);
//       }
//
//       await batch.commit();
//       return Right(updatedRolls);
//     } catch (e) {
//       return Left(ServerFailure('Failed to bulk update stock: ${e.toString()}'));
//     }
//   }
//
//   @override
//   Future<Either<Failure, DenimRoll>> transferStock(
//     StockTransferRequest request,
//   ) async {
//     try {
//       // Get existing denim roll
//       final rollResult = await getDenimRollByRollId(request.rollId);
//       if (rollResult.isLeft()) {
//         return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
//       }
//
//       final existingRoll = rollResult.fold((l) => null, (r) => r);
//       if (existingRoll == null) {
//         return const Left(ValidationFailure('Roll ID not found'));
//       }
//
//       // Validate transfer quantity
//       if (request.quantity > existingRoll.balanceQty) {
//         return const Left(ValidationFailure('Transfer quantity cannot exceed available balance'));
//       }
//
//       // Update location
//       final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
//       final updatedRoll = (existingRoll as DenimRollModel).updateStock(
//         location: request.toLocation,
//       );
//
//       await rollDocRef.update(updatedRoll.toFirestore());
//
//       return Right(updatedRoll);
//     } catch (e) {
//       return Left(ServerFailure('Failed to transfer stock: ${e.toString()}'));
//     }
//   }
