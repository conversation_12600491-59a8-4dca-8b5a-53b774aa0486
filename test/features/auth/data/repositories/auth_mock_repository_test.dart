// import 'package:dartz/dartz.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:hm_collection/core/auth/repositories/auth_repository.dart';
// import 'package:hm_collection/features/auth/data/repositories/auth_mock_repository.dart';
// import 'package:hm_collection/features/auth/domain/entities/auth_tokens.dart';
// import 'package:hm_collection/features/auth/domain/entities/login_credentials.dart';
// import 'package:hm_collection/features/auth/domain/entities/user.dart';
// import 'package:hm_collection/features/auth/domain/repositories/auth_repository.dart';
// import 'package:hm_collection/shared/enums/common_enums.dart';
// import 'package:hm_collection/shared/models/api_response.dart';
// import 'package:mocktail/mocktail.dart';
// void main() {
//   late AuthMockRepository repository;
//
//   setUp(() {
//     repository = AuthMockRepository();
//   });
//
//   group('login', () {
//     test('should return AuthTokens when login is successful', () async {
//       // Arrange
//       final credentials = LoginCredentials(
//         email: '<EMAIL>',
//         password: 'password',
//         rememberMe: true
//       );
//
//       // Act
//       final result = await repository.login(credentials);
//
//       // Assert
//       expect(result, isA<Right<dynamic, ApiResponse<AuthTokens>>>());
//       result.fold(
//         (failure) => fail('Login should not fail'),
//         (response) {
//           expect(response.success, true);
//           expect(response.data, isA<AuthTokens>());
//           expect(response.data!.accessToken, isNotEmpty);
//         },
//       );
//     });
//
//     test('should fail when password is empty', () async {
//       // Arrange
//       final credentials = LoginCredentials(
//         email: '<EMAIL>',
//         password: '',
//         rememberMe: true
//       );
//
//       // Act
//       final result = await repository.login(credentials);
//
//       // Assert
//       expect(result.isLeft(), true);
//     });
//   });
//
//   group('getCurrentUser', () {
//     test('should return current user when authenticated', () async {
//       // Act
//       final result = await repository.getCurrentUser();
//
//       // Assert
//       expect(result, isA<Right<dynamic, ApiResponse<User>>>());
//       result.fold(
//         (failure) => fail('Should not fail'),
//         (response) {
//           expect(response.success, true);
//           expect(response.data, isA<User>());
//           expect(response.data!.email, '<EMAIL>');
//         },
//       );
//     });
//   });
//
//   group('refreshToken', () {
//     test('should return new tokens when refresh is successful', () async {
//       // Arrange
//       const refreshToken = 'valid_refresh_token';
//
//       // Act
//       final result = await repository.refreshToken(refreshToken);
//
//       // Assert
//       expect(result, isA<Right<dynamic, ApiResponse<AuthTokens>>>());
//       result.fold(
//         (failure) => fail('Should not fail'),
//         (response) {
//           expect(response.success, true);
//           expect(response.data!.accessToken, contains('new_mock_access_token'));
//         },
//       );
//     });
//   });
//
//   group('changePassword', () {
//     test('should return success when password is changed', () async {
//       // Arrange
//       final request = ChangePasswordRequest(
//         currentPassword: 'oldPassword',
//         newPassword: 'newPassword',
//         confirmPassword: 'newPassword',
//
//       );
//
//       // Act
//       final result = await repository.changePassword(request);
//
//       // Assert
//       expect(result, isA<Right<dynamic, ApiVoidResponse>>());
//       result.fold(
//         (failure) => fail('Should not fail'),
//         (response) {
//           expect(response.success, true);
//           expect(response.message, 'Password changed successfully');
//         },
//       );
//     });
//   });
//
//   group('userExists', () {
//     test('should return true when username exists', () async {
//       // Act
//       final result = await repository.userExists('admin');
//
//       // Assert
//       expect(result, const Right(true));
//     });
//
//     test('should return false when username does not exist', () async {
//       // Act
//       final result = await repository.userExists('nonexistent');
//
//       // Assert
//       expect(result, const Right(false));
//     });
//   });
//
//   group('getUserPreferences', () {
//     test('should return user preferences', () async {
//       // Act
//       final result = await repository.getUserPreferences();
//
//       // Assert
//       expect(result, isA<Right<dynamic, ApiResponse<UserPreferences>>>());
//       result.fold(
//         (failure) => fail('Should not fail'),
//         (response) {
//           expect(response.success, true);
//           expect(response.data, isA<UserPreferences>());
//           expect(response.data!.theme, 'light');
//         },
//       );
//     });
//   });
//
//   group('register', () {
//     test('should return success when registration is successful', () async {
//       // Arrange
//       final request = RegisterRequest(
//         email: '<EMAIL>',
//         role: UserRole.sewingHead,
//         password: 'password123',
//         firstName: 'New',
//         lastName: 'User',
//       );
//
//       // Act
//       final result = await repository.register(request);
//
//       // Assert
//       expect(result, isA<Right<dynamic, ApiVoidResponse>>());
//       result.fold(
//         (failure) => fail('Should not fail'),
//         (response) {
//           expect(response.success, true);
//           expect(response.message, contains('Registration successful'));
//         },
//       );
//     });
//   });
//
//   group('getUserSessions', () {
//     test('should return list of user sessions', () async {
//       // Act
//       final result = await repository.getUserSessions();
//
//       // Assert
//       expect(result, isA<Right<dynamic, ApiListResponse<UserSession>>>());
//       result.fold(
//         (failure) => fail('Should not fail'),
//         (response) {
//           expect(response.success, true);
//           expect(response.data, isNotEmpty);
//           expect(response.data!.first.userId, 'user_1');
//         },
//       );
//     });
//   });
// }
