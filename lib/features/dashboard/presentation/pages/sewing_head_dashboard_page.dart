import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Sewing Head Dashboard Page - Sewing department management
class SewingHeadDashboardPage extends StatelessWidget {
  const SewingHeadDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sewing Head Dashboard'),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildSewingHeadDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildSewingHeadDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildSewingOverview(),
          const SizedBox(height: 24),
          _buildSewingActions(context),
          const SizedBox(height: 24),
          _buildProductionLines(),
          const SizedBox(height: 24),
          _buildTeamPerformance(),
          const SizedBox(height: 24),
          _buildQualityMetrics(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.cyan,
            Colors.cyan.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Sewing Head'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Sewing department management',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.design_services,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildSewingOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Sewing Department Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Active Lines',
              value: '5',
              icon: Icons.linear_scale,
              color: Colors.blue,
            ),
            DashboardMetricCard(
              title: 'Total Operators',
              value: '45',
              icon: Icons.people,
              color: Colors.green,
            ),
            DashboardMetricCard(
              title: 'Daily Target',
              value: '850',
              icon: Icons.flag,
              color: Colors.purple,
            ),
            DashboardMetricCard(
              title: 'Efficiency',
              value: '88%',
              icon: Icons.trending_up,
              color: Colors.orange,
              trend: '+3%',
              isPositiveTrend: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSewingActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Department Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Line Management',
              subtitle: 'Manage production lines',
              icon: Icons.linear_scale,
              color: Colors.blue,
              onTap: () {
                // Navigate to line management
              },
            ),
            DashboardCard(
              title: 'Task Assignment',
              subtitle: 'Assign tasks to operators',
              icon: Icons.assignment_ind,
              color: Colors.green,
              onTap: () {
                // Navigate to task assignment
              },
            ),
            DashboardCard(
              title: 'Production Planning',
              subtitle: 'Plan sewing schedules',
              icon: Icons.calendar_today,
              color: Colors.purple,
              onTap: () {
                // Navigate to production planning
              },
            ),
            DashboardCard(
              title: 'Quality Control',
              subtitle: 'Monitor sewing quality',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () {
                // Navigate to quality control
              },
            ),
            DashboardCard(
              title: 'Team Training',
              subtitle: 'Manage operator training',
              icon: Icons.school,
              color: Colors.teal,
              onTap: () {
                // Navigate to training
              },
            ),
            DashboardCard(
              title: 'Reports',
              subtitle: 'View department reports',
              icon: Icons.analytics,
              color: Colors.indigo,
              onTap: () {
                // Navigate to reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProductionLines() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.linear_scale,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Production Lines Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '5/5 Active',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildLineItem('Line A', 'Shirts', '85%', 'On Target', Colors.green),
            _buildLineItem('Line B', 'Trousers', '92%', 'Ahead', Colors.blue),
            _buildLineItem('Line C', 'Dresses', '78%', 'Behind', Colors.orange),
            _buildLineItem('Line D', 'Jackets', '88%', 'On Target', Colors.green),
            _buildLineItem('Line E', 'Skirts', '95%', 'Ahead', Colors.blue),
          ],
        ),
      ),
    );
  }

  Widget _buildLineItem(String line, String product, String efficiency, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              line,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              product,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              efficiency,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                status,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w500,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTeamPerformance() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.people,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Team Performance',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to detailed performance
                  },
                  child: const Text('View Details'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPerformanceCard('Supervisors', '5', '92%', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceCard('Operators', '40', '88%', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceCard('Avg. Efficiency', '88%', '+3%', Colors.purple),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(String title, String value, String subtitle, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 9,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQualityMetrics() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.verified,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Quality Metrics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Good',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildQualityItem('First Pass Rate', '94%', Colors.green, 0.94),
            const SizedBox(height: 8),
            _buildQualityItem('Defect Rate', '2.1%', Colors.orange, 0.021),
            const SizedBox(height: 8),
            _buildQualityItem('Rework Rate', '3.5%', Colors.red, 0.035),
            const SizedBox(height: 8),
            _buildQualityItem('Customer Satisfaction', '96%', Colors.blue, 0.96),
          ],
        ),
      ),
    );
  }

  Widget _buildQualityItem(String metric, String value, Color color, double progress) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            metric,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 2,
          child: LinearProgressIndicator(
            value: progress > 1 ? progress / 100 : progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
