# Chrome Web Debugging Guide for HM Collection

This guide will help you debug the Flutter web application in Chrome effectively.

## 🚀 Quick Start

### 1. Fix Current Issues and Run

```bash
# Run the debug helper script
debug_web.bat

# Or manually:
flutter clean
flutter pub get
flutter run -d chrome --web-port=3000
```

### 2. Open Chrome DevTools

Once the app is running:
1. Press `F12` or right-click and select "Inspect"
2. The DevTools will open with multiple tabs for debugging

## 🔧 Fixed Issues

I've identified and fixed several issues in your code:

### ✅ Fixed Syntax Error
- **File**: `lib/main.dart` line 164
- **Issue**: Extra characters `+`U,` causing syntax error
- **Fix**: Removed the invalid characters

### ✅ Fixed Missing Auth Guard
- **File**: `lib/core/auth/widgets/auth_guard.dart`
- **Issue**: Missing auth guard widget for route protection
- **Fix**: Created the auth guard widget

### ✅ Fixed Router Imports
- **File**: `lib/core/routing/app_router.dart`
- **Issue**: Incorrect import paths for feature pages
- **Fix**: Updated imports to match actual file structure

### ✅ Created Missing Analytics Page
- **File**: `lib/features/analytics_reporting/presentation/pages/analytics_page.dart`
- **Issue**: Missing analytics page referenced in router
- **Fix**: Created comprehensive analytics page

## 🐛 Chrome Debugging Techniques

### 1. Console Debugging

Open the **Console** tab in DevTools to see:
- Flutter framework logs
- Your custom print statements
- JavaScript errors
- Network request logs

**Useful Console Commands:**
```javascript
// Check Flutter app status
window.flutterCanvasKit

// View current route
window.location.pathname

// Check for errors
console.error('Test error')

// Performance timing
console.time('operation')
// ... your code
console.timeEnd('operation')
```

### 2. Sources Tab - Dart Debugging

In the **Sources** tab:
1. Navigate to `packages/your_app/lib/`
2. Set breakpoints by clicking line numbers
3. Use the debugger controls:
   - **Continue** (F8)
   - **Step Over** (F10)
   - **Step Into** (F11)
   - **Step Out** (Shift+F11)

### 3. Network Tab - API Debugging

Monitor network requests:
1. Open **Network** tab
2. Filter by XHR/Fetch for API calls
3. Check request/response headers
4. Inspect response data

### 4. Application Tab - Storage Debugging

Check local storage and cache:
1. **Local Storage** - View stored data
2. **Session Storage** - Session-specific data
3. **IndexedDB** - Database storage
4. **Cache Storage** - Service worker cache

### 5. Performance Tab

Analyze app performance:
1. Click **Record** button
2. Interact with your app
3. Stop recording
4. Analyze the flame chart

## 🔍 Common Debugging Scenarios

### Scenario 1: App Won't Load

**Symptoms**: White screen or loading forever

**Debug Steps**:
1. Check Console for errors
2. Look for failed network requests in Network tab
3. Verify all imports are correct
4. Check if service worker is blocking

**Common Fixes**:
```bash
# Clear browser cache
Ctrl+Shift+R (hard refresh)

# Disable service worker
# In DevTools > Application > Service Workers > Unregister
```

### Scenario 2: Navigation Issues

**Symptoms**: Routes not working, 404 errors

**Debug Steps**:
1. Check router configuration
2. Verify route paths in Network tab
3. Check browser URL vs expected route

**Common Fixes**:
- Ensure server redirects all routes to index.html
- Check base href configuration
- Verify route definitions

### Scenario 3: State Management Issues

**Symptoms**: UI not updating, incorrect data

**Debug Steps**:
1. Add breakpoints in BLoC event handlers
2. Check state transitions in Console
3. Use Flutter Inspector

**Common Fixes**:
```dart
// Add debug prints
print('Current state: $state');
print('Event received: $event');
```

### Scenario 4: Performance Issues

**Symptoms**: Slow loading, laggy animations

**Debug Steps**:
1. Use Performance tab to record
2. Check for memory leaks in Memory tab
3. Analyze bundle size in Network tab

**Common Fixes**:
- Optimize images
- Implement lazy loading
- Use const constructors

## 🛠️ Flutter Web Specific Debugging

### 1. Flutter Inspector

Access via:
- DevTools URL (shown in terminal)
- Or add `?debug=true` to your URL

Features:
- Widget tree inspection
- Property editing
- Layout debugging

### 2. Hot Reload

While debugging:
- Press `r` in terminal for hot reload
- Press `R` for hot restart
- Changes reflect immediately

### 3. Debug vs Release Builds

**Debug Build** (for development):
```bash
flutter run -d chrome --debug
```

**Profile Build** (for performance testing):
```bash
flutter run -d chrome --profile
```

**Release Build** (for production):
```bash
flutter build web --release
```

## 📱 Responsive Debugging

### Device Simulation

In Chrome DevTools:
1. Click device icon (Toggle device toolbar)
2. Select different screen sizes
3. Test responsive behavior

### Custom Breakpoints

Add custom breakpoints:
1. Click "Responsive" dropdown
2. Click "Edit..."
3. Add custom dimensions

## 🔧 Advanced Debugging

### 1. Source Maps

Enable source maps for better debugging:
```bash
flutter build web --source-maps
```

### 2. Verbose Logging

Enable verbose logging:
```bash
flutter run -d chrome -v
```

### 3. Web Renderer Options

Test different renderers:
```bash
# HTML renderer (smaller bundle)
flutter run -d chrome --web-renderer html

# CanvasKit renderer (better performance)
flutter run -d chrome --web-renderer canvaskit
```

## 🚨 Troubleshooting Common Errors

### Error: "Failed to load resource"
**Solution**: Check file paths and ensure assets are properly configured

### Error: "CORS policy"
**Solution**: Run with proper CORS headers or use a local server

### Error: "Module not found"
**Solution**: Run `flutter pub get` and check import paths

### Error: "White screen"
**Solution**: Check console for JavaScript errors and verify main.dart

## 📊 Performance Monitoring

### Key Metrics to Watch

1. **First Contentful Paint (FCP)** - < 1.8s
2. **Largest Contentful Paint (LCP)** - < 2.5s
3. **First Input Delay (FID)** - < 100ms
4. **Cumulative Layout Shift (CLS)** - < 0.1

### Tools for Monitoring

1. **Lighthouse** (in DevTools Audits tab)
2. **Performance tab** for detailed analysis
3. **Memory tab** for memory usage

## 🎯 Next Steps

1. **Run the debug script**: `debug_web.bat`
2. **Open Chrome DevTools**: Press F12
3. **Check Console**: Look for any errors
4. **Test Navigation**: Try different routes
5. **Monitor Performance**: Use Performance tab

## 📞 Getting Help

If you encounter issues:

1. **Check Console first** - Most errors appear here
2. **Use Flutter Inspector** - For widget-specific issues
3. **Check Network tab** - For API/loading issues
4. **Review this guide** - For common solutions

## 🔗 Useful Resources

- [Flutter Web Debugging](https://docs.flutter.dev/platform-integration/web/debugging)
- [Chrome DevTools Guide](https://developers.google.com/web/tools/chrome-devtools)
- [Flutter Performance](https://docs.flutter.dev/perf)

---

**Happy Debugging! 🐛➡️✨**
