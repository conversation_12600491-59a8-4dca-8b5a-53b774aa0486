import 'package:flutter/material.dart';

import '../../../../core/auth/widgets/permission_guard.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../production_planning/domain/entities/production_entities.dart';
import '../widgets/task_stats_widget.dart';
import '../widgets/task_kanban_board.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../domain/entities/manufacturing_entities.dart';

/// Task management page for manufacturing workflow
class TaskManagementPage extends StatefulWidget {
  const TaskManagementPage({super.key});

  @override
  State<TaskManagementPage> createState() => _TaskManagementPageState();
}

class _TaskManagementPageState extends State<TaskManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  TaskStatus? _selectedStatusFilter;
  TaskPriority? _selectedPriorityFilter;
  String? _selectedDepartmentFilter;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Task Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
            tooltip: 'Filter Tasks',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(),
            tooltip: 'Search Tasks',
          ),
          PermissionGuard(
            permission: 'manufacturing.tasks.create',
            child: IconButton(
              icon: const Icon(Icons.add_task),
              onPressed: () => _showCreateTaskDialog(),
              tooltip: 'Create Task',
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildKanbanTab(),
                _buildMyTasksTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: PermissionGuard(
        permission: 'manufacturing.tasks.create',
        child: FloatingActionButton(
          onPressed: () => _showCreateTaskDialog(),
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add_task, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        isScrollable: true,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.view_kanban),
            text: 'Kanban Board',
          ),
          Tab(
            icon: Icon(Icons.assignment_ind),
            text: 'My Tasks',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'Analytics',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Task Management Overview',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Task Statistics
          const TaskStatsWidget(),
          
          const SizedBox(height: 24),
          
          // Priority Tasks
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'High Priority Tasks',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => _tabController.animateTo(1),
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPriorityTasksList(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Department Task Distribution
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Task Distribution by Department',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDepartmentTaskDistribution(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Recent Activities
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Recent Task Activities',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildRecentActivities(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKanbanTab() {
    // TODO: Replace with actual tasks from your state management
    final List<ManufacturingTask> mockTasks = [
      // Add sample tasks here or fetch from your state
    ];

    return PermissionGuard(
      permission: 'manufacturing.tasks.read',
      fallback: const Center(
        child: Text('You do not have permission to view tasks'),
      ),
      child: TaskKanbanBoard(
        tasks: mockTasks,
        onStatusChanged: (task, newStatus) {
          // TODO: Implement status change logic
        },
        onTaskTap: (ManufacturingTask task) {
          // Handle task tap (e.g., navigate to task details)
          if (task != null) {
            _viewTaskDetails(task.title);
          }
        },
      ),
    );
  }

  Widget _buildMyTasksTab() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My Assigned Tasks',
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  if (_selectedStatusFilter != null)
                    Chip(
                      label: Text(_selectedStatusFilter!.name.toUpperCase()),
                      onDeleted: () => setState(() => _selectedStatusFilter = null),
                      deleteIcon: const Icon(Icons.close, size: 16),
                    ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: () => _refreshTasks(),
                    tooltip: 'Refresh Tasks',
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Expanded(
            child: _buildMyTasksList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Task Analytics',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Performance Metrics
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Completion Rate',
                  '89.5%',
                  Icons.task_alt,
                  AppColors.success,
                  '+3.2% vs last week',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Average Time',
                  '4.2h',
                  Icons.schedule,
                  AppColors.primary,
                  'Per task completion',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Overdue Tasks',
                  '12',
                  Icons.warning,
                  AppColors.error,
                  '5% of total tasks',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Quality Score',
                  '94.8%',
                  Icons.verified,
                  AppColors.warning,
                  '+1.5% improvement',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Task Completion Trends
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Task Completion Trends',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 300,
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text('Task completion trend charts will be displayed here'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityTasksList() {
    // Mock high priority tasks
    final priorityTasks = [
      {'title': 'Quality Check - Batch #1234', 'department': 'Quality Control', 'deadline': '2 hours', 'priority': 'Critical'},
      {'title': 'Cutting - Order #5678', 'department': 'Cutting', 'deadline': '4 hours', 'priority': 'High'},
      {'title': 'Stitching - Urgent Repair', 'department': 'Stitching', 'deadline': '6 hours', 'priority': 'High'},
    ];

    return Column(
      children: priorityTasks.map((task) => ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getPriorityColor(task['priority'] as String).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.priority_high,
            color: _getPriorityColor(task['priority'] as String),
            size: 20,
          ),
        ),
        title: Text(task['title'] as String),
        subtitle: Text('${task['department']} • Due in ${task['deadline']}'),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getPriorityColor(task['priority'] as String).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            task['priority'] as String,
            style: AppTextStyles.bodySmall.copyWith(
              color: _getPriorityColor(task['priority'] as String),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        onTap: () => _viewTaskDetails(task['title'] as String),
      )).toList(),
    );
  }

  Widget _buildDepartmentTaskDistribution() {
    // Mock department task data
    final departmentData = [
      {'name': 'Cutting', 'tasks': 45, 'completed': 38, 'percentage': 84.4},
      {'name': 'Stitching', 'tasks': 67, 'completed': 59, 'percentage': 88.1},
      {'name': 'Quality Control', 'tasks': 23, 'completed': 22, 'percentage': 95.7},
      {'name': 'Packing', 'tasks': 34, 'completed': 31, 'percentage': 91.2},
    ];

    return Column(
      children: departmentData.map((dept) => Container(
        margin: const EdgeInsets.only(bottom: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                dept['name'] as String,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: LinearProgressIndicator(
                value: (dept['percentage'] as double) / 100,
                backgroundColor: AppColors.surface,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '${dept['completed']}/${dept['tasks']}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${dept['percentage']}%',
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildRecentActivities() {
    // Mock recent activities
    final activities = [
      {'action': 'Task completed', 'task': 'Cutting - Batch #1234', 'user': 'John Doe', 'time': '5 min ago'},
      {'action': 'Task assigned', 'task': 'Quality Check - Order #5678', 'user': 'Jane Smith', 'time': '15 min ago'},
      {'action': 'Task started', 'task': 'Stitching - Urgent Repair', 'user': 'Mike Wilson', 'time': '30 min ago'},
      {'action': 'Task rejected', 'task': 'Packing - Batch #9876', 'user': 'Sarah Johnson', 'time': '1 hour ago'},
    ];

    return Column(
      children: activities.map((activity) => ListTile(
        leading: CircleAvatar(
          backgroundColor: _getActivityColor(activity['action'] as String).withOpacity(0.1),
          child: Icon(
            _getActivityIcon(activity['action'] as String),
            color: _getActivityColor(activity['action'] as String),
            size: 20,
          ),
        ),
        title: Text('${activity['action']} - ${activity['task']}'),
        subtitle: Text('by ${activity['user']}'),
        trailing: Text(
          activity['time'] as String,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      )).toList(),
    );
  }

  Widget _buildMyTasksList() {
    // Mock user tasks
    final myTasks = _getMockUserTasks();
    final filteredTasks = myTasks.where((task) {
      if (_selectedStatusFilter != null && task.status != _selectedStatusFilter) {
        return false;
      }
      if (_searchQuery.isNotEmpty && !task.title.toLowerCase().contains(_searchQuery.toLowerCase())) {
        return false;
      }
      return true;
    }).toList();

    return ListView.builder(
      itemCount: filteredTasks.length,
      itemBuilder: (context, index) {
        final task = filteredTasks[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12, left: 8, right: 8),
          elevation: 1,
          child: InkWell(
            onTap: () => _viewTaskDetails(task.title),
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          task.title,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit, size: 18),
                        onPressed: () => _editTask(task),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  if (task.deadline != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Due: ${_formatDate(task.deadline)}, ${_formatTime(task.deadline)}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                        ),
                      ],
                    ),
                  ],
                  if (task.assignedToName != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.person_outline,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          task.assignedToName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                        ),
                      ],
                    ),
                  ],
                  const SizedBox(height: 8),
                  if (task.priority != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(task.priority! as String).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: _getPriorityColor(task.priority! as String).withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        task.priority!.name.toUpperCase(),
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: _getPriorityColor(task.priority!.name),
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  DropdownButtonFormField<TaskStatus>(
                    value: task.status,
                    items: TaskStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(
                          status.toString().split('.').last,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      );
                    }).toList(),
                    onChanged: (newStatus) {
                      if (newStatus != null) {
                        _updateTaskStatus(task, newStatus);
                      }
                    },
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      isDense: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
  
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ManufacturingTask> _getMockUserTasks() {
    return [
      ManufacturingTask(
        id: '1',
        title: 'Cut fabric for Order #1234',
        description: 'Cut 100 pieces of cotton fabric according to pattern specifications',
        departmentId: 'dept_1',
        departmentName: 'Cutting',
        assignedToId: 'user_1',
        assignedToName: 'Current User',
        assignedById: 'manager_1',
        assignedByName: 'Production Manager',
        type: TaskType.cutting,
        priority: TaskPriority.high,
        status: TaskStatus.inProgress,
        deadline: DateTime.now().add(const Duration(hours: 4)),
        startedAt: DateTime.now().subtract(const Duration(hours: 1)),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now(),
        attachments: [],
        comments: [],
      ),
      // Add more mock tasks...
    ];
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'critical':
        return AppColors.error;
      case 'high':
        return AppColors.warning;
      case 'normal':
        return AppColors.primary;
      case 'low':
        return AppColors.success;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getActivityColor(String action) {
    switch (action.toLowerCase()) {
      case 'task completed':
        return AppColors.success;
      case 'task assigned':
        return AppColors.primary;
      case 'task started':
        return AppColors.warning;
      case 'task rejected':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getActivityIcon(String action) {
    switch (action.toLowerCase()) {
      case 'task completed':
        return Icons.check_circle;
      case 'task assigned':
        return Icons.assignment;
      case 'task started':
        return Icons.play_circle;
      case 'task rejected':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  void _showFilterDialog() {
    // Implement filter dialog
  }

  void _showSearchDialog() {
    // Implement search dialog
  }

  void _showCreateTaskDialog() {
    // Implement create task dialog
  }

  void _viewTaskDetails(String taskTitle) {
    // Navigate to task details page
  }

  void _updateTaskStatus(ManufacturingTask task, TaskStatus status) {
    // Implement task status update
  }

  void _editTask(ManufacturingTask task) {
    // Implement task editing
  }

  void _refreshTasks() {
    // Implement task refresh
  }
}
