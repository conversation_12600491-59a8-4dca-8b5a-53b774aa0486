# Munda Features Integration Guide

## Overview
This guide provides step-by-step instructions for integrating the Munda Operations feature into your existing Flutter application.

## Prerequisites
- Existing Flutter app with Firebase setup
- Firebase Authentication configured
- Firebase Firestore enabled
- BLoC state management pattern in use

## Integration Steps

### 1. Dependencies
Ensure these dependencies are in your `pubspec.yaml`:

```yaml
dependencies:
  flutter_bloc: ^8.1.3
  dartz: ^0.10.1
  equatable: ^2.0.5
  cloud_firestore: ^4.9.1
  firebase_auth: ^4.9.0
  get_it: ^7.6.4  # For dependency injection
```

### 2. Dependency Injection Setup
Add Munda services to your dependency injection container (typically in `main.dart` or a separate DI file):

```dart
// Add to your GetIt setup
void setupMundaDependencies() {
  // Data sources
  sl.registerLazySingleton<MundaFirebaseDataSource>(
    () => MundaFirebaseDataSource(
      firestore: sl<FirebaseFirestore>(),
      auth: sl<FirebaseAuth>(),
    ),
  );

  // Repositories
  sl.registerLazySingleton<MundaRepository>(
    () => MundaRepositoryImpl(dataSource: sl<MundaFirebaseDataSource>()),
  );

  // BLoCs
  sl.registerFactory(() => MundaBloc(repository: sl<MundaRepository>()));
}
```

### 3. Firebase Security Rules
Add these Firestore security rules for Munda collections:

```javascript
// Add to your firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Munda Records
    match /munda_records/{recordId} {
      allow read, write: if request.auth != null 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'mundaOperator';
    }
    
    // Munda Tasks
    match /munda_tasks/{taskId} {
      allow read, write: if request.auth != null 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'mundaOperator';
    }
  }
}
```

### 4. User Role Extension
Ensure your User entity includes the `mundaOperator` role:

```dart
// In your UserRole enum
enum UserRole {
  admin,
  manager,
  operator,
  mundaOperator, // Add this
  // ... other roles
}
```

### 5. Navigation Integration
Add Munda routes to your app's routing system:

#### Using GoRouter:
```dart
// Add to your GoRouter routes
GoRoute(
  path: '/munda-login',
  builder: (context, state) => const MundaLoginPage(),
),
GoRoute(
  path: '/munda-dashboard',
  builder: (context, state) => BlocProvider(
    create: (context) => sl<MundaBloc>(),
    child: const MundaDashboardPage(),
  ),
),
```

#### Using Named Routes:
```dart
// Add to your MaterialApp routes
'/munda-login': (context) => const MundaLoginPage(),
'/munda-dashboard': (context) => BlocProvider(
  create: (context) => sl<MundaBloc>(),
  child: const MundaDashboardPage(),
),
```

### 6. Main App Integration
Add Munda access to your main navigation:

```dart
// In your main dashboard or navigation drawer
ListTile(
  leading: Icon(Icons.design_services),
  title: Text('Munda Operations'),
  onTap: () {
    // Check user role before navigation
    final user = context.read<FirebaseAuthBloc>().state;
    if (user is Authenticated && user.user.role == UserRole.mundaOperator) {
      Navigator.pushNamed(context, '/munda-dashboard');
    } else {
      Navigator.pushNamed(context, '/munda-login');
    }
  },
),
```

### 7. BLoC Provider Setup
Wrap your app or specific routes with BLoC providers:

```dart
// Option 1: Global provider (in main.dart)
MultiBlocProvider(
  providers: [
    BlocProvider<FirebaseAuthBloc>(create: (context) => sl<FirebaseAuthBloc>()),
    BlocProvider<MundaBloc>(create: (context) => sl<MundaBloc>()),
    // ... other providers
  ],
  child: MyApp(),
)

// Option 2: Route-specific provider (recommended)
// Provide MundaBloc only when needed in specific routes
```

## Testing Guide

### 1. Unit Tests
Create tests for your business logic:

```dart
// test/features/munda/domain/entities/munda_record_test.dart
void main() {
  group('MundaRecord', () {
    test('should calculate amount correctly', () {
      final record = MundaRecord(
        pieces: 10,
        rate: 5.50,
        // ... other required fields
      );
      
      expect(record.amount, equals(55.0));
    });
  });
}
```

### 2. Widget Tests
Test your UI components:

```dart
// test/features/munda/presentation/widgets/munda_summary_card_test.dart
void main() {
  testWidgets('MundaSummaryCard displays summary correctly', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<MundaBloc>(
          create: (context) => MockMundaBloc(),
          child: MundaSummaryCard(),
        ),
      ),
    );
    
    expect(find.text('Today\'s Summary'), findsOneWidget);
  });
}
```

### 3. Integration Tests
Test the complete flow:

```dart
// integration_test/munda_flow_test.dart
void main() {
  group('Munda Operations Flow', () {
    testWidgets('Complete data entry flow', (tester) async {
      // Test login -> dashboard -> data entry -> save
    });
  });
}
```

## Troubleshooting

### Common Issues:

1. **Authentication Errors**
   - Ensure Firebase Auth is properly configured
   - Check that user roles are correctly set in Firestore
   - Verify security rules allow Munda operations

2. **State Management Issues**
   - Ensure BLoC providers are properly set up
   - Check that events are being dispatched correctly
   - Verify state transitions in BLoC

3. **Navigation Issues**
   - Ensure routes are properly defined
   - Check that navigation guards are working
   - Verify deep linking configuration

4. **Data Persistence Issues**
   - Check Firestore security rules
   - Verify network connectivity
   - Ensure proper error handling

### Debug Tips:

1. **Enable BLoC Logging**:
```dart
// In main.dart
if (kDebugMode) {
  Bloc.observer = SimpleBlocObserver();
}
```

2. **Firebase Debugging**:
```dart
// Enable Firestore logging
FirebaseFirestore.instance.settings = const Settings(
  persistenceEnabled: true,
  cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
);
```

3. **Network Debugging**:
```dart
// Add network interceptors for debugging
// Use packages like dio_logging_interceptor
```

## Performance Optimization

### 1. Pagination
- Implement proper pagination for large datasets
- Use Firestore's `limit()` and `startAfter()` for efficient queries

### 2. Caching
- Implement local caching for frequently accessed data
- Use Firestore's offline persistence

### 3. Image Optimization
- Compress images before upload
- Use appropriate image formats and sizes

### 4. State Management
- Dispose of BLoCs properly to prevent memory leaks
- Use `BlocListener` instead of `BlocBuilder` when you don't need to rebuild UI

## Security Considerations

1. **Data Validation**
   - Always validate data on both client and server side
   - Use Firestore security rules for server-side validation

2. **User Authentication**
   - Implement proper session management
   - Use secure token storage

3. **Role-based Access**
   - Implement proper role checking
   - Use server-side role validation

## Deployment Checklist

- [ ] Firebase project configured for production
- [ ] Security rules deployed and tested
- [ ] User roles properly set up in production database
- [ ] App permissions configured correctly
- [ ] Performance monitoring enabled
- [ ] Error reporting configured
- [ ] Backup and recovery procedures in place

## Support and Maintenance

### Regular Tasks:
1. Monitor Firebase usage and costs
2. Review and update security rules
3. Backup critical data regularly
4. Monitor app performance and errors
5. Update dependencies regularly

### Monitoring:
- Set up Firebase Performance Monitoring
- Configure Crashlytics for error tracking
- Monitor Firestore usage and costs
- Set up alerts for critical issues

## Next Steps

After integration, consider implementing:

1. **Advanced Analytics**: More detailed reporting and insights
2. **Export Features**: PDF/Excel export capabilities
3. **Notifications**: Push notifications for task assignments
4. **Offline Mode**: Enhanced offline capabilities with conflict resolution
5. **Barcode Scanning**: For quick data entry
6. **Photo Attachments**: Add image support to records
7. **Approval Workflows**: Multi-level approval processes
8. **Audit Trails**: Detailed change tracking
9. **Bulk Operations**: Batch import/export features
10. **Mobile Optimization**: Enhanced mobile experience

For additional support or questions, refer to the code documentation and comments throughout the implementation.
