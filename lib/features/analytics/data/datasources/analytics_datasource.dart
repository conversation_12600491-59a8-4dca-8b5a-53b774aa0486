import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/analytics_entities.dart';
import '../../domain/repositories/analytics_repository.dart';

/// Analytics data source interface
abstract class AnalyticsDataSource {
  // Dashboard Management
  Future<ApiListResponse<AnalyticsDashboard>> getDashboards({
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<AnalyticsDashboard>> getDashboardById(String dashboardId);

  Future<ApiResponse<AnalyticsDashboard>> createDashboard(CreateDashboardRequest request);

  Future<ApiResponse<AnalyticsDashboard>> updateDashboard(UpdateDashboardRequest request);

  Future<ApiVoidResponse> deleteDashboard(String dashboardId);

  Future<ApiResponse<AnalyticsDashboard>> cloneDashboard(String dashboardId, String newName);

  Future<ApiResponse<AnalyticsDashboard>> shareDashboard(ShareDashboardRequest request);

  // Widget Management
  Future<ApiResponse<DashboardWidget>> addWidget(AddWidgetRequest request);

  Future<ApiResponse<DashboardWidget>> updateWidget(UpdateWidgetRequest request);

  Future<ApiVoidResponse> removeWidget(String dashboardId, String widgetId);

  Future<ApiResponse<AnalyticsDashboard>> reorderWidgets(ReorderWidgetsRequest request);

  // Report Management
  Future<ApiListResponse<AnalyticsReport>> getReports({
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<AnalyticsReport>> getReportById(String reportId);

  Future<ApiResponse<AnalyticsReport>> generateReport(GenerateReportRequest request);

  Future<ApiResponse<AnalyticsReport>> scheduleReport(ScheduleReportRequest request);

  Future<ApiVoidResponse> cancelReport(String reportId);

  Future<ApiVoidResponse> deleteReport(String reportId);

  // Data Analytics
  Future<Map<String, dynamic>> getKpiData({
    required List<String> metrics,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  });

  Future<ChartData> getChartData({
    required String chartType,
    required List<String> metrics,
    required List<String> dimensions,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  });

  Future<TableData> getTableData({
    required List<String> columns,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
    String? sortBy,
    SortOrder? sortOrder,
    PaginationParams? pagination,
  });

  // Performance Analytics
  Future<Map<String, dynamic>> getProductionMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  });

  Future<Map<String, dynamic>> getQualityMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  });

  Future<Map<String, dynamic>> getFinancialMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  });

  Future<Map<String, dynamic>> getInventoryMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? warehouseId,
    String? categoryId,
  });

  Future<Map<String, dynamic>> getEfficiencyMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? machineId,
  });

  // Trend Analysis
  Future<List<DataPoint>> getTrendData({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required String granularity,
    Map<String, dynamic>? filters,
  });

  Future<Map<String, dynamic>> getComparativeAnalysis({
    required List<String> metrics,
    required DateTime currentPeriodStart,
    required DateTime currentPeriodEnd,
    required DateTime previousPeriodStart,
    required DateTime previousPeriodEnd,
    Map<String, dynamic>? filters,
  });

  // Predictive Analytics
  Future<Map<String, dynamic>> getForecast({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required int forecastPeriods,
    Map<String, dynamic>? parameters,
  });

  Future<Map<String, dynamic>> getAnomalyDetection({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? parameters,
  });

  // Real-time Analytics
  Future<Map<String, dynamic>> getRealTimeMetrics({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  });

  Stream<Map<String, dynamic>> getRealTimeUpdates({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  });

  // Export and Sharing
  Future<String> exportDashboard({
    required String dashboardId,
    required String format,
    Map<String, dynamic>? options,
  });

  Future<String> exportReport({
    required String reportId,
    required String format,
    Map<String, dynamic>? options,
  });

  Future<String> exportData({
    required String dataType,
    required Map<String, dynamic> parameters,
    required String format,
  });

  // Search and Discovery
  Future<ApiListResponse<AnalyticsDashboard>> searchDashboards({
    required String query,
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiListResponse<AnalyticsReport>> searchReports({
    required String query,
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<List<String>> getAvailableMetrics({
    String? category,
    String? dataSource,
  });

  Future<List<String>> getAvailableDimensions({
    String? category,
    String? dataSource,
  });
}
