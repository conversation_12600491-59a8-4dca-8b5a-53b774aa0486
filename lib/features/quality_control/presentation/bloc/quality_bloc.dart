import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/compliance_entities.dart';
import '../../domain/entities/defect_analysis_entity.dart';
import '../../domain/entities/quality_defect_filter.dart';
import '../../domain/entities/quality_defect_filter_criteria.dart';
import '../../domain/entities/quality_entities.dart';
import '../../domain/entities/quality_inspection_filter.dart';
import '../../domain/entities/quality_standard_filter.dart';
import '../../domain/entities/quality_standard_filter_criteria.dart';
import '../../domain/entities/quality_trends_entity.dart';
import '../../domain/entities/requests/create_quality_standard_request.dart';
import '../../domain/entities/requests/resolve_quality_defect_request.dart';
import '../../domain/entities/requests/update_checkpoint_result_request.dart';
import '../../domain/entities/requests/update_quality_defect_request.dart';
import '../../domain/entities/requests/update_quality_inspection_request.dart';
import '../../domain/models/requests/add_quality_defect_request.dart';
import '../../domain/models/requests/create_quality_inspection_request.dart';
import '../../domain/repositories/quality_repository.dart';
import '../../domain/usecases/quality_usecases.dart';
import '../widgets/quality_statistics_card.dart';

part 'quality_event.dart';
part 'quality_state.dart';

/// Quality Control Bloc
@injectable
class QualityBloc extends Bloc<QualityEvent, QualityState> {
  final GetQualityInspectionsUseCase _getQualityInspectionsUseCase;
  final GetQualityInspectionByIdUseCase _getQualityInspectionByIdUseCase;
  final CreateQualityInspectionUseCase _createQualityInspectionUseCase;
  final UpdateQualityInspectionUseCase _updateQualityInspectionUseCase;
  final StartQualityInspectionUseCase _startQualityInspectionUseCase;
  final CompleteQualityInspectionUseCase _completeQualityInspectionUseCase;
  final UpdateCheckpointResultUseCase _updateCheckpointResultUseCase;
  final AddQualityDefectUseCase _addQualityDefectUseCase;
  final UpdateQualityDefectUseCase _updateQualityDefectUseCase;
  final ResolveQualityDefectUseCase _resolveQualityDefectUseCase;
  final GetQualityDefectsUseCase _getQualityDefectsUseCase;
  final GetQualityStandardsUseCase _getQualityStandardsUseCase;
  final CreateQualityStandardUseCase _createQualityStandardUseCase;
  final ApproveQualityStandardUseCase _approveQualityStandardUseCase;
  final GetQualityStatisticsUseCase _getQualityStatisticsUseCase;
  final GetQualityTrendsUseCase _getQualityTrendsUseCase;
  final GetDefectAnalysisUseCase _getDefectAnalysisUseCase;
  final SearchQualityInspectionsUseCase _searchQualityInspectionsUseCase;
  final GetPendingInspectionsUseCase _getPendingInspectionsUseCase;
  final GetFailedInspectionsUseCase _getFailedInspectionsUseCase;

  QualityBloc(
    this._getQualityInspectionsUseCase,
    this._getQualityInspectionByIdUseCase,
    this._createQualityInspectionUseCase,
    this._updateQualityInspectionUseCase,
    this._startQualityInspectionUseCase,
    this._completeQualityInspectionUseCase,
    this._updateCheckpointResultUseCase,
    this._addQualityDefectUseCase,
    this._updateQualityDefectUseCase,
    this._resolveQualityDefectUseCase,
    this._getQualityDefectsUseCase,
    this._getQualityStandardsUseCase,
    this._createQualityStandardUseCase,
    this._approveQualityStandardUseCase,
    this._getQualityStatisticsUseCase,
    this._getQualityTrendsUseCase,
    this._getDefectAnalysisUseCase,
    this._searchQualityInspectionsUseCase,
    this._getPendingInspectionsUseCase,
    this._getFailedInspectionsUseCase,
  ) : super(const QualityInitial()) {
    on<LoadQualityInspectionsRequested>(_onLoadQualityInspectionsRequested);
    on<RefreshQualityInspectionsRequested>(_onRefreshQualityInspectionsRequested);
    on<LoadQualityInspectionDetailsRequested>(_onLoadQualityInspectionDetailsRequested);
    on<CreateQualityInspectionRequested>(_onCreateQualityInspectionRequested);
    on<UpdateQualityInspectionRequested>(_onUpdateQualityInspectionRequested);
    on<StartQualityInspectionRequested>(_onStartQualityInspectionRequested);
    on<CompleteQualityInspectionRequested>(_onCompleteQualityInspectionRequested);
    on<UpdateCheckpointResultRequested>(_onUpdateCheckpointResultRequested);
    on<AddQualityDefectRequested>(_onAddQualityDefectRequested);
    on<UpdateQualityDefectRequested>(_onUpdateQualityDefectRequested);
    on<ResolveQualityDefectRequested>(_onResolveQualityDefectRequested);
    on<LoadQualityDefectsRequested>(_onLoadQualityDefectsRequested);
    on<LoadQualityStandardsRequested>(_onLoadQualityStandardsRequested);
    on<CreateQualityStandardRequested>(_onCreateQualityStandardRequested);
    on<ApproveQualityStandardRequested>(_onApproveQualityStandardRequested);
    on<LoadQualityStatisticsRequested>(_onLoadQualityStatisticsRequested);
    on<LoadQualityTrendsRequested>(_onLoadQualityTrendsRequested);
    on<LoadDefectAnalysisRequested>(_onLoadDefectAnalysisRequested);
    on<SearchQualityInspectionsRequested>(_onSearchQualityInspectionsRequested);
    on<LoadPendingInspectionsRequested>(_onLoadPendingInspectionsRequested);
    on<LoadFailedInspectionsRequested>(_onLoadFailedInspectionsRequested);
    on<FilterQualityInspectionsRequested>(_onFilterQualityInspectionsRequested);
    on<ClearQualityState>(_onClearQualityState);
  }

  Future<void> _onLoadQualityInspectionsRequested(
    LoadQualityInspectionsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _getQualityInspectionsUseCase(GetQualityInspectionsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) => emit(QualityInspectionsLoaded(
        inspections: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onRefreshQualityInspectionsRequested(
    RefreshQualityInspectionsRequested event,
    Emitter<QualityState> emit,
  ) async {
    final currentState = state;
    if (currentState is QualityInspectionsLoaded) {
      emit(currentState.copyWith(isRefreshing: true));

      final result = await _getQualityInspectionsUseCase(GetQualityInspectionsParams(
        filter: currentState.filter,
        pagination: const PaginationParams(page: 1, perPage: 20),
      ));

      result.fold(
        (failure) => emit(currentState.copyWith(isRefreshing: false)),
        (response) => emit(currentState.copyWith(
          inspections: response.data ?? [],
          pagination: response.pagination,
          isRefreshing: false,
        )),
      );
    }
  }

  Future<void> _onLoadQualityInspectionDetailsRequested(
    LoadQualityInspectionDetailsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _getQualityInspectionByIdUseCase(IdParams(event.inspectionId));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityInspectionDetailsLoaded(response.data!));
        } else {
          emit(const QualityError('Quality inspection not found'));
        }
      },
    );
  }

  Future<void> _onCreateQualityInspectionRequested(
    CreateQualityInspectionRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _createQualityInspectionUseCase(event.request);

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityInspectionCreated(response.data!));
        } else {
          emit(const QualityError('Failed to create quality inspection'));
        }
      },
    );
  }

  Future<void> _onUpdateQualityInspectionRequested(
    UpdateQualityInspectionRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _updateQualityInspectionUseCase(event.request);

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityInspectionUpdated(response.data!));
        } else {
          emit(const QualityError('Failed to update quality inspection'));
        }
      },
    );
  }

  Future<void> _onStartQualityInspectionRequested(
    StartQualityInspectionRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _startQualityInspectionUseCase(IdParams(event.inspectionId));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityInspectionStarted(response.data!));
        } else {
          emit(const QualityError('Failed to start quality inspection'));
        }
      },
    );
  }

  Future<void> _onCompleteQualityInspectionRequested(
    CompleteQualityInspectionRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _completeQualityInspectionUseCase(CompleteQualityInspectionParams(
      event.inspectionId,
      event.request,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityInspectionCompleted(response.data!));
        } else {
          emit(const QualityError('Failed to complete quality inspection'));
        }
      },
    );
  }

  Future<void> _onUpdateCheckpointResultRequested(
    UpdateCheckpointResultRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _updateCheckpointResultUseCase(UpdateCheckpointResultParams(
      event.checkpointId,
      event.request,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(CheckpointResultUpdated(response.data! as QualityCheckpoint));
        } else {
          emit(const QualityError('Failed to update checkpoint result'));
        }
      },
    );
  }

  Future<void> _onAddQualityDefectRequested(
    AddQualityDefectRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _addQualityDefectUseCase(event.request);

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityDefectAdded(response.data!));
        } else {
          emit(const QualityError('Failed to add quality defect'));
        }
      },
    );
  }

  Future<void> _onUpdateQualityDefectRequested(
    UpdateQualityDefectRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _updateQualityDefectUseCase(event.request);

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityDefectUpdated(response.data!));
        } else {
          emit(const QualityError('Failed to update quality defect'));
        }
      },
    );
  }

  Future<void> _onResolveQualityDefectRequested(
    ResolveQualityDefectRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _resolveQualityDefectUseCase(ResolveQualityDefectParams(
      event.defectId,
      event.request,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityDefectResolved(response.data!));
        } else {
          emit(const QualityError('Failed to resolve quality defect'));
        }
      },
    );
  }

  Future<void> _onLoadQualityDefectsRequested(
    LoadQualityDefectsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _getQualityDefectsUseCase(GetQualityDefectsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) => emit(QualityDefectsLoaded(
        defects: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onLoadQualityStandardsRequested(
    LoadQualityStandardsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _getQualityStandardsUseCase(GetQualityStandardsParams(
      filter: event.filter != null
          ? QualityStandardFilter(
              status: event.filter?.status,
              type: event.filter?.type,
              category: event.filter?.category,
              fromDate: event.filter?.fromDate,
              toDate: event.filter?.toDate,
              searchQuery: event.filter?.searchQuery,
            )
          : null,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) => emit(QualityStandardsLoaded(
        standards: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onCreateQualityStandardRequested(
    CreateQualityStandardRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _createQualityStandardUseCase(event.request);

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityStandardCreated(response.data!));
        } else {
          emit(const QualityError('Failed to create quality standard'));
        }
      },
    );
  }

  Future<void> _onApproveQualityStandardRequested(
    ApproveQualityStandardRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _approveQualityStandardUseCase(ApproveQualityStandardParams(
      event.standardId,
      event.approverId,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(QualityStandardApproved(response.data!));
        } else {
          emit(const QualityError('Failed to approve quality standard'));
        }
      },
    );
  }

  Future<void> _onLoadQualityStatisticsRequested(
    LoadQualityStatisticsRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _getQualityStatisticsUseCase(GetQualityStatisticsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      departmentId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (statistics) => emit(QualityStatisticsLoaded(statistics)),
    );
  }

  Future<void> _onLoadQualityTrendsRequested(
    LoadQualityTrendsRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _getQualityTrendsUseCase(GetQualityTrendsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      productId: event.productId,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (trends) => emit(QualityTrendsLoaded(trends)),
    );
  }

  Future<void> _onLoadDefectAnalysisRequested(
    LoadDefectAnalysisRequested event,
    Emitter<QualityState> emit,
  ) async {
    final result = await _getDefectAnalysisUseCase(GetDefectAnalysisParams(
      startDate: event.startDate,
      endDate: event.endDate,
      categoryId: event.categoryId,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (analysis) => emit(DefectAnalysisLoaded(analysis)),
    );
  }

  Future<void> _onSearchQualityInspectionsRequested(
    SearchQualityInspectionsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _searchQualityInspectionsUseCase(SearchQualityInspectionsParams(
      event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) => emit(QualityInspectionsSearched(
        inspections: response.data ?? [],
        pagination: response.pagination,
        query: event.query,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onLoadPendingInspectionsRequested(
    LoadPendingInspectionsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _getPendingInspectionsUseCase(GetPendingInspectionsParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) => emit(PendingInspectionsLoaded(
        inspections: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadFailedInspectionsRequested(
    LoadFailedInspectionsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _getFailedInspectionsUseCase(GetFailedInspectionsParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) => emit(FailedInspectionsLoaded(
        inspections: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onFilterQualityInspectionsRequested(
    FilterQualityInspectionsRequested event,
    Emitter<QualityState> emit,
  ) async {
    emit(const QualityLoading());

    final result = await _getQualityInspectionsUseCase(GetQualityInspectionsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(QualityError(failure.message)),
      (response) => emit(QualityInspectionsFiltered(
        inspections: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  void _onClearQualityState(
    ClearQualityState event,
    Emitter<QualityState> emit,
  ) {
    emit(const QualityInitial());
  }
}
