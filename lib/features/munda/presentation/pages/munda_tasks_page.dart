import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../bloc/munda_bloc.dart';
import '../bloc/munda_event.dart';
import '../bloc/munda_state.dart';

/// Page for managing Munda tasks
class MundaTasksPage extends StatefulWidget {
  const MundaTasksPage({super.key});

  @override
  State<MundaTasksPage> createState() => _MundaTasksPageState();
}

class _MundaTasksPageState extends State<MundaTasksPage> {
  @override
  void initState() {
    super.initState();
    _loadTasks();
  }

  void _loadTasks() {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthAuthenticated) {
      context.read<MundaBloc>().add(
        LoadUserTasks(userId: authState.user.id),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Content
          Expanded(
            child: BlocBuilder<MundaBloc, MundaState>(
              builder: (context, state) {
                if (state is MundaLoading) {
                  return _buildLoadingState();
                } else if (state is TasksLoaded) {
                  return _buildTasksList(state.userTasks);
                } else if (state is MundaError) {
                  return _buildErrorState(state);
                } else {
                  return _buildEmptyState();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.task_alt,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'My Tasks',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: _loadTasks,
                icon: const Icon(
                  Icons.refresh,
                  color: AppColors.primary,
                ),
                tooltip: 'Refresh Tasks',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Manage your assigned tasks',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildTasksList(List<dynamic> tasks) {
    if (tasks.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return _buildTaskCard(task);
      },
    );
  }

  Widget _buildTaskCard(dynamic task) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 8,
                height: 40,
                decoration: BoxDecoration(
                  color: _getStatusColor(task.status),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task.title,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      task.description ?? 'No description',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusChip(task.status),
            ],
          ),
          const SizedBox(height: 12),
          
          // Task actions
          Row(
            children: [
              if (task.status == MundaTaskStatus.assigned)
                ElevatedButton.icon(
                  onPressed: () => _startTask(task.id),
                  icon: const Icon(Icons.play_arrow, size: 16),
                  label: const Text('Start'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              if (task.status == MundaTaskStatus.inProgress) ...[
                ElevatedButton.icon(
                  onPressed: () => _completeTask(task.id),
                  icon: const Icon(Icons.check, size: 16),
                  label: const Text('Complete'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  onPressed: () => _sendToBundling(task.id),
                  icon: const Icon(Icons.send, size: 16),
                  label: const Text('Send to Bundling'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.secondary,
                    side: const BorderSide(color: AppColors.secondary),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
              const Spacer(),
              Text(
                'Due: ${_formatDate(task.dueDate)}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(MundaTaskStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.displayName,
        style: AppTextStyles.bodySmall.copyWith(
          color: _getStatusColor(status),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.task_alt,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No tasks assigned',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You don\'t have any tasks assigned at the moment.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(MundaError error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load tasks',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.userMessage,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTasks,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(MundaTaskStatus status) {
    switch (status) {
      case MundaTaskStatus.pending:
        return AppColors.warning;
      case MundaTaskStatus.assigned:
        return AppColors.info;
      case MundaTaskStatus.inProgress:
        return AppColors.primary;
      case MundaTaskStatus.completed:
        return AppColors.success;
      case MundaTaskStatus.sentToBundling:
        return AppColors.secondary;
      case MundaTaskStatus.cancelled:
        return AppColors.error;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'No due date';
    return '${date.day}/${date.month}/${date.year}';
  }

  void _startTask(String taskId) {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthAuthenticated) {
      context.read<MundaBloc>().add(
        StartTask(taskId: taskId, userId: authState.user.id),
      );
    }
  }

  void _completeTask(String taskId) {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthAuthenticated) {
      context.read<MundaBloc>().add(
        CompleteTask(taskId: taskId, userId: authState.user.id),
      );
    }
  }

  void _sendToBundling(String taskId) {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthAuthenticated) {
      context.read<MundaBloc>().add(
        SendTaskToBundling(taskId: taskId, userId: authState.user.id),
      );
    }
  }
}
