import 'package:equatable/equatable.dart';

/// Financial metrics for analytics and reporting
class FinancialMetrics extends Equatable {
  /// Total revenue amount
  final double totalRevenue;
  
  /// Total expenses amount
  final double totalExpenses;
  
  /// Net profit (revenue - expenses)
  final double netProfit;
  
  /// Profit margin percentage (net profit / revenue * 100)
  final double profitMargin;
  
  /// Breakdown of expenses by category
  final Map<String, double> expenseBreakdown;
  
  /// Revenue by product/service category
  final Map<String, double> revenueByCategory;
  
  /// Budget variance percentage (actual vs planned)
  final double budgetVariance;
  
  /// Accounts receivable amount
  final double accountsReceivable;
  
  /// Accounts payable amount
  final double accountsPayable;
  
  /// Cash flow from operations
  final double operatingCashFlow;
  
  /// Current ratio (current assets / current liabilities)
  final double currentRatio;
  
  /// Return on investment percentage
  final double returnOnInvestment;

  const FinancialMetrics({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    required this.profitMargin,
    required this.expenseBreakdown,
    required this.revenueByCategory,
    required this.budgetVariance,
    required this.accountsReceivable,
    required this.accountsPayable,
    required this.operatingCashFlow,
    required this.currentRatio,
    required this.returnOnInvestment,
  });

  /// Creates a FinancialMetrics instance with default/zero values
  factory FinancialMetrics.zero() {
    return const FinancialMetrics(
      totalRevenue: 0.0,
      totalExpenses: 0.0,
      netProfit: 0.0,
      profitMargin: 0.0,
      expenseBreakdown: {},
      revenueByCategory: {},
      budgetVariance: 0.0,
      accountsReceivable: 0.0,
      accountsPayable: 0.0,
      operatingCashFlow: 0.0,
      currentRatio: 0.0,
      returnOnInvestment: 0.0,
    );
  }

  @override
  List<Object?> get props => [
        totalRevenue,
        totalExpenses,
        netProfit,
        profitMargin,
        expenseBreakdown,
        revenueByCategory,
        budgetVariance,
        accountsReceivable,
        accountsPayable,
        operatingCashFlow,
        currentRatio,
        returnOnInvestment,
      ];

  /// Creates a copy of this FinancialMetrics with the given fields replaced with the new values
  FinancialMetrics copyWith({
    double? totalRevenue,
    double? totalExpenses,
    double? netProfit,
    double? profitMargin,
    Map<String, double>? expenseBreakdown,
    Map<String, double>? revenueByCategory,
    double? budgetVariance,
    double? accountsReceivable,
    double? accountsPayable,
    double? operatingCashFlow,
    double? currentRatio,
    double? returnOnInvestment,
  }) {
    return FinancialMetrics(
      totalRevenue: totalRevenue ?? this.totalRevenue,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      netProfit: netProfit ?? this.netProfit,
      profitMargin: profitMargin ?? this.profitMargin,
      expenseBreakdown: expenseBreakdown ?? this.expenseBreakdown,
      revenueByCategory: revenueByCategory ?? this.revenueByCategory,
      budgetVariance: budgetVariance ?? this.budgetVariance,
      accountsReceivable: accountsReceivable ?? this.accountsReceivable,
      accountsPayable: accountsPayable ?? this.accountsPayable,
      operatingCashFlow: operatingCashFlow ?? this.operatingCashFlow,
      currentRatio: currentRatio ?? this.currentRatio,
      returnOnInvestment: returnOnInvestment ?? this.returnOnInvestment,
    );
  }
}
