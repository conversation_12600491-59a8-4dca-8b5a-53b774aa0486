part of 'bundling_bloc.dart';

/// Base bundling event
abstract class BundlingEvent extends Equatable {
  const BundlingEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load all bundles
class LoadBundles extends BundlingEvent {
  final BundleStatus? status;
  final String? designNo;
  final String? lotNo;
  final String? rollNo;
  final String? altNo;
  final GarmentSize? size;
  final String? assignedTo;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? limit;
  final int? offset;

  const LoadBundles({
    this.status,
    this.designNo,
    this.lotNo,
    this.rollNo,
    this.altNo,
    this.size,
    this.assignedTo,
    this.startDate,
    this.endDate,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [
        status,
        designNo,
        lotNo,
        rollNo,
        altNo,
        size,
        assignedTo,
        startDate,
        endDate,
        limit,
        offset,
      ];
}

/// Event to load a specific bundle by ID
class LoadBundleById extends BundlingEvent {
  final String bundleId;

  const LoadBundleById(this.bundleId);

  @override
  List<Object?> get props => [bundleId];
}

/// Event to create bundles from input
class CreateBundles extends BundlingEvent {
  final String designNo;
  final String lotNo;
  final String rollNo;
  final String altNo;
  final GarmentSize size;
  final int totalPiecesAvailable;
  final int piecesPerBundle;
  final String? color;
  final String? orderNo;
  final String? clientName;
  final String? remarks;

  const CreateBundles({
    required this.designNo,
    required this.lotNo,
    required this.rollNo,
    required this.altNo,
    required this.size,
    required this.totalPiecesAvailable,
    required this.piecesPerBundle,
    this.color,
    this.orderNo,
    this.clientName,
    this.remarks,
  });

  @override
  List<Object?> get props => [
        designNo,
        lotNo,
        rollNo,
        altNo,
        size,
        totalPiecesAvailable,
        piecesPerBundle,
        color,
        orderNo,
        clientName,
        remarks,
      ];
}

/// Event to update a bundle
class UpdateBundle extends BundlingEvent {
  final Bundle bundle;

  const UpdateBundle(this.bundle);

  @override
  List<Object?> get props => [bundle];
}

/// Event to delete a bundle
class DeleteBundle extends BundlingEvent {
  final String bundleId;

  const DeleteBundle(this.bundleId);

  @override
  List<Object?> get props => [bundleId];
}

/// Event to issue bundle to sewing
class IssueBundleToSewing extends BundlingEvent {
  final String bundleId;
  final String assignedTo;

  const IssueBundleToSewing({
    required this.bundleId,
    required this.assignedTo,
  });

  @override
  List<Object?> get props => [bundleId, assignedTo];
}

/// Event to start bundle work
class StartBundleWork extends BundlingEvent {
  final String bundleId;

  const StartBundleWork(this.bundleId);

  @override
  List<Object?> get props => [bundleId];
}

/// Event to complete bundle work
class CompleteBundleWork extends BundlingEvent {
  final String bundleId;
  final String? remarks;

  const CompleteBundleWork({
    required this.bundleId,
    this.remarks,
  });

  @override
  List<Object?> get props => [bundleId, remarks];
}

/// Event to cancel a bundle
class CancelBundle extends BundlingEvent {
  final String bundleId;
  final String reason;

  const CancelBundle({
    required this.bundleId,
    required this.reason,
  });

  @override
  List<Object?> get props => [bundleId, reason];
}

/// Event to search bundles
class SearchBundles extends BundlingEvent {
  final String query;

  const SearchBundles(this.query);

  @override
  List<Object?> get props => [query];
}

/// Event to load bundle statistics
class LoadBundleStatistics extends BundlingEvent {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? designNo;
  final GarmentSize? size;
  final BundleStatus? status;

  const LoadBundleStatistics({
    this.fromDate,
    this.toDate,
    this.designNo,
    this.size,
    this.status,
  });

  @override
  List<Object?> get props => [fromDate, toDate, designNo, size, status];
}

/// Event to load bundles assigned to current user
class LoadMyBundles extends BundlingEvent {
  const LoadMyBundles();
}

/// Event to load bundles by status
class LoadBundlesByStatus extends BundlingEvent {
  final BundleStatus status;

  const LoadBundlesByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

/// Event to refresh bundles
class RefreshBundles extends BundlingEvent {
  const RefreshBundles();
}

/// Event to sync offline bundles
class SyncOfflineBundles extends BundlingEvent {
  const SyncOfflineBundles();
}

/// Event to clear bundle filters
class ClearBundleFilters extends BundlingEvent {
  const ClearBundleFilters();
}

/// Event to apply bundle filters
class ApplyBundleFilters extends BundlingEvent {
  final BundleStatus? status;
  final String? designNo;
  final String? lotNo;
  final String? rollNo;
  final GarmentSize? size;
  final DateTime? startDate;
  final DateTime? endDate;

  const ApplyBundleFilters({
    this.status,
    this.designNo,
    this.lotNo,
    this.rollNo,
    this.size,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [
        status,
        designNo,
        lotNo,
        rollNo,
        size,
        startDate,
        endDate,
      ];
}

/// Event to validate bundle input
class ValidateBundleInput extends BundlingEvent {
  final String designNo;
  final String lotNo;
  final String rollNo;
  final String altNo;
  final GarmentSize size;
  final int totalPiecesAvailable;
  final int piecesPerBundle;

  const ValidateBundleInput({
    required this.designNo,
    required this.lotNo,
    required this.rollNo,
    required this.altNo,
    required this.size,
    required this.totalPiecesAvailable,
    required this.piecesPerBundle,
  });

  @override
  List<Object?> get props => [
        designNo,
        lotNo,
        rollNo,
        altNo,
        size,
        totalPiecesAvailable,
        piecesPerBundle,
      ];
}

/// Event to preview bundle creation
class PreviewBundleCreation extends BundlingEvent {
  final BundleInput input;

  const PreviewBundleCreation(this.input);

  @override
  List<Object?> get props => [input];
}

/// Event to export bundles
class ExportBundles extends BundlingEvent {
  final List<String>? bundleIds;
  final BundleStatus? status;
  final String? designNo;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String format; // 'excel' or 'pdf'

  const ExportBundles({
    this.bundleIds,
    this.status,
    this.designNo,
    this.fromDate,
    this.toDate,
    required this.format,
  });

  @override
  List<Object?> get props => [
        bundleIds,
        status,
        designNo,
        fromDate,
        toDate,
        format,
      ];
}

/// Event to reset bundling state
class ResetBundlingState extends BundlingEvent {
  const ResetBundlingState();
}
