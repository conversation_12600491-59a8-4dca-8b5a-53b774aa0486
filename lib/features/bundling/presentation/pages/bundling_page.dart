import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../domain/entities/bundle.dart';
import '../bloc/bundling_bloc.dart';
import '../widgets/bundle_creation_form.dart';
import '../widgets/bundle_list_widget.dart';
import '../widgets/bundle_statistics_card.dart';
import '../widgets/bundle_filter_bar.dart';

/// Main bundling page for managing bundles
class BundlingPage extends StatefulWidget {
  const BundlingPage({super.key});

  @override
  State<BundlingPage> createState() => _BundlingPageState();
}

class _BundlingPageState extends State<BundlingPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late BundlingBloc _bundlingBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _bundlingBloc = GetIt.instance<BundlingBloc>();
    
    // Load initial data
    _bundlingBloc.add(const LoadBundles());
    _bundlingBloc.add(const LoadBundleStatistics());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bundlingBloc,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Bundling Management'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(
                icon: Icon(Icons.dashboard),
                text: 'Dashboard',
              ),
              Tab(
                icon: Icon(Icons.add_box),
                text: 'Create Bundles',
              ),
              Tab(
                icon: Icon(Icons.list),
                text: 'All Bundles',
              ),
              Tab(
                icon: Icon(Icons.assignment),
                text: 'My Bundles',
              ),
            ],
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                _bundlingBloc.add(const RefreshBundles());
                _bundlingBloc.add(const LoadBundleStatistics());
              },
            ),
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: () => _showFilterDialog(context),
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => _showSearchDialog(context),
            ),
          ],
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildDashboardTab(),
            _buildCreateBundlesTab(),
            _buildAllBundlesTab(),
            _buildMyBundlesTab(),
          ],
        ),
        floatingActionButton: _tabController.index == 1
            ? null
            : FloatingActionButton(
                onPressed: () {
                  _tabController.animateTo(1); // Navigate to Create Bundles tab
                },
                backgroundColor: AppColors.primary,
                child: const Icon(Icons.add, color: Colors.white),
              ),
      ),
    );
  }

  /// Build dashboard tab
  Widget _buildDashboardTab() {
    return BlocBuilder<BundlingBloc, BundlingState>(
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: () async {
            _bundlingBloc.add(const LoadBundleStatistics());
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bundle Statistics',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                if (state is BundleStatisticsLoaded)
                  BundleStatisticsCard(statistics: state.statistics)
                else if (state is BundlingLoading)
                  const Center(child: LoadingWidget())
                else if (state is BundlingError)
                  Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading statistics',
                          style: AppTextStyles.bodyLarge.copyWith(
                            color: AppColors.error,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            _bundlingBloc.add(const LoadBundleStatistics());
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  )
                else
                  const Center(
                    child: Text('No statistics available'),
                  ),
                const SizedBox(height: 24),
                Text(
                  'Quick Actions',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildQuickActions(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build create bundles tab
  Widget _buildCreateBundlesTab() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: BundleCreationForm(),
    );
  }

  /// Build all bundles tab
  Widget _buildAllBundlesTab() {
    return Column(
      children: [
        const BundleFilterBar(),
        Expanded(
          child: BlocBuilder<BundlingBloc, BundlingState>(
            builder: (context, state) {
              if (state is BundlesLoaded) {
                return BundleListWidget(
                  bundles: state.bundles,
                  onBundleSelected: (bundle) => _showBundleDetails(context, bundle),
                  onRefresh: () async {
                    _bundlingBloc.add(const RefreshBundles());
                  },
                );
              } else if (state is BundlingLoading) {
                return const Center(child: LoadingWidget());
              } else if (state is BundlingError) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppColors.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading bundles',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.error,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        state.message,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          _bundlingBloc.add(const LoadBundles());
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              } else {
                return const Center(
                  child: Text('No bundles available'),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  /// Build my bundles tab
  Widget _buildMyBundlesTab() {
    return BlocBuilder<BundlingBloc, BundlingState>(
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: () async {
            _bundlingBloc.add(const LoadMyBundles());
          },
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                color: AppColors.surface,
                child: Row(
                  children: [
                    Icon(
                      Icons.assignment_ind,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Bundles Assigned to Me',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Builder(
                  builder: (context) {
                    if (state is BundlesLoaded) {
                      return BundleListWidget(
                        bundles: state.bundles,
                        onBundleSelected: (bundle) => _showBundleDetails(context, bundle),
                        onRefresh: () async {
                          _bundlingBloc.add(const LoadMyBundles());
                        },
                      );
                    } else if (state is BundlingLoading) {
                      return const Center(child: LoadingWidget());
                    } else if (state is BundlingError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 64,
                              color: AppColors.error,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Error loading my bundles',
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: AppColors.error,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              state.message,
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                _bundlingBloc.add(const LoadMyBundles());
                              },
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      );
                    } else {
                      // Load my bundles when tab is first accessed
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _bundlingBloc.add(const LoadMyBundles());
                      });
                      return const Center(child: LoadingWidget());
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build quick actions grid
  Widget _buildQuickActions() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildQuickActionCard(
          icon: Icons.add_box,
          title: 'Create Bundles',
          subtitle: 'Create new bundles',
          color: AppColors.primary,
          onTap: () => _tabController.animateTo(1),
        ),
        _buildQuickActionCard(
          icon: Icons.pending_actions,
          title: 'Pending Bundles',
          subtitle: 'View pending bundles',
          color: AppColors.warning,
          onTap: () {
            _bundlingBloc.add(const LoadBundlesByStatus(BundleStatus.pending));
            _tabController.animateTo(2);
          },
        ),
        _buildQuickActionCard(
          icon: Icons.work,
          title: 'In Progress',
          subtitle: 'View active bundles',
          color: AppColors.info,
          onTap: () {
            _bundlingBloc.add(const LoadBundlesByStatus(BundleStatus.inProgress));
            _tabController.animateTo(2);
          },
        ),
        _buildQuickActionCard(
          icon: Icons.check_circle,
          title: 'Completed',
          subtitle: 'View completed bundles',
          color: AppColors.success,
          onTap: () {
            _bundlingBloc.add(const LoadBundlesByStatus(BundleStatus.completed));
            _tabController.animateTo(2);
          },
        ),
      ],
    );
  }

  /// Build quick action card
  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTextStyles.titleSmall.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show filter dialog
  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Bundles'),
        content: const Text('Filter functionality will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Apply filters
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  /// Show search dialog
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Bundles'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'Enter bundle number, design, or lot...',
            prefixIcon: Icon(Icons.search),
          ),
          onSubmitted: (query) {
            Navigator.of(context).pop();
            if (query.isNotEmpty) {
              _bundlingBloc.add(SearchBundles(query));
              _tabController.animateTo(2);
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Show bundle details
  void _showBundleDetails(BuildContext context, Bundle bundle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Bundle ${bundle.bundleNo}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Design No:', bundle.designNo),
              _buildDetailRow('Lot No:', bundle.lotNo),
              _buildDetailRow('Roll No:', bundle.rollNo),
              _buildDetailRow('Alt No:', bundle.altNo),
              _buildDetailRow('Size:', bundle.size.displayName),
              _buildDetailRow('Piece Range:', bundle.pieceRange),
              _buildDetailRow('Total Pieces:', bundle.totalPieces.toString()),
              _buildDetailRow('Status:', bundle.status.displayName),
              if (bundle.assignedTo != null)
                _buildDetailRow('Assigned To:', bundle.assignedTo!),
              if (bundle.remarks != null)
                _buildDetailRow('Remarks:', bundle.remarks!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (bundle.canBeIssued || bundle.canBeStarted || bundle.canBeCompleted)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showBundleActions(context, bundle);
              },
              child: const Text('Actions'),
            ),
        ],
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show bundle actions
  void _showBundleActions(BuildContext context, Bundle bundle) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Bundle Actions',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (bundle.canBeIssued)
              ListTile(
                leading: const Icon(Icons.send),
                title: const Text('Issue to Sewing'),
                onTap: () {
                  Navigator.of(context).pop();
                  // Show assign dialog
                },
              ),
            if (bundle.canBeStarted)
              ListTile(
                leading: const Icon(Icons.play_arrow),
                title: const Text('Start Work'),
                onTap: () {
                  Navigator.of(context).pop();
                  _bundlingBloc.add(StartBundleWork(bundle.id));
                },
              ),
            if (bundle.canBeCompleted)
              ListTile(
                leading: const Icon(Icons.check),
                title: const Text('Complete Work'),
                onTap: () {
                  Navigator.of(context).pop();
                  _bundlingBloc.add(CompleteBundleWork(bundleId: bundle.id));
                },
              ),
            if (bundle.canBeCancelled)
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text('Cancel Bundle'),
                onTap: () {
                  Navigator.of(context).pop();
                  // Show cancel dialog
                },
              ),
          ],
        ),
      ),
    );
  }
}
