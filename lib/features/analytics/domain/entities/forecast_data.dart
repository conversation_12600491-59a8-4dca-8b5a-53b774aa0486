import 'package:equatable/equatable.dart';

/// Represents forecast data for analytics
class ForecastData extends Equatable {
  /// The list of forecasted values with their timestamps
  final List<ForecastPoint> values;
  
  /// The confidence interval for the forecast (lower and upper bounds)
  final List<ForecastPoint>? confidenceInterval;
  
  /// The accuracy metrics for the forecast
  final ForecastAccuracy? accuracy;
  
  /// The type of forecast (e.g., 'revenue', 'production', 'inventory')
  final String forecastType;
  
  /// The time period the forecast covers (e.g., 'daily', 'weekly', 'monthly')
  final String timePeriod;
  
  /// The timestamp when the forecast was generated
  final DateTime forecastGeneratedAt;

  const ForecastData({
    required this.values,
    this.confidenceInterval,
    this.accuracy,
    required this.forecastType,
    required this.timePeriod,
    required this.forecastGeneratedAt,
  });

  @override
  List<Object?> get props => [
        values,
        confidenceInterval,
        accuracy,
        forecastType,
        timePeriod,
        forecastGeneratedAt,
      ];
}

/// Represents a single point in a forecast
class ForecastPoint extends Equatable {
  /// The timestamp for this forecast point
  final DateTime timestamp;
  
  /// The forecasted value
  final double value;

  const ForecastPoint({
    required this.timestamp,
    required this.value,
  });

  @override
  List<Object> get props => [timestamp, value];
}

/// Represents the accuracy metrics of a forecast
class ForecastAccuracy extends Equatable {
  /// Mean Absolute Error
  final double? mae;
  
  /// Mean Absolute Percentage Error
  final double? mape;
  
  /// Root Mean Squared Error
  final double? rmse;
  
  /// R-squared value
  final double? rSquared;

  const ForecastAccuracy({
    this.mae,
    this.mape,
    this.rmse,
    this.rSquared,
  });

  @override
  List<Object?> get props => [mae, mape, rmse, rSquared];
}
