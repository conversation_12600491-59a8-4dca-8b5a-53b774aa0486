import 'package:flutter/material.dart';

import '../../domain/entities/inventory_entities.dart';
import '../bloc/inventory_bloc.dart';

/// List view for inventory items
class InventoryListView extends StatelessWidget {
  final List<InventoryItem> items;
  final InventoryViewMode? viewMode;
  final List<String> selectedItemIds;
  final Function(String)? onItemTap;
  final Function(String)? onItemLongPress;
  final Function(List<String>)? onSelectionChanged;
  final Function(String)? onStockAdjust;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  final bool isLoading;

  const InventoryListView({
    Key? key,
    required this.items,
    this.viewMode,
    this.selectedItemIds = const [],
    this.onItemTap,
    this.onItemLongPress,
    this.onSelectionChanged,
    this.onStockAdjust,
    this.onLoadMore,
    this.hasMore = false,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading && items.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No inventory items found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Inventory items will appear here when they are added',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: items.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == items.length) {
          // Load more indicator
          return Container(
            padding: const EdgeInsets.all(16),
            alignment: Alignment.center,
            child: const CircularProgressIndicator(),
          );
        }

        final item = items[index];
        final isSelected = selectedItemIds.contains(item.id);
        
        return InventoryItemListTile(
          item: item,
          isSelected: isSelected,
          onTap: () => onItemTap?.call(item.id),
          onLongPress: () => onItemLongPress?.call(item.id),
          onStockAdjust: () => onStockAdjust?.call(item.id),
        );
      },
    );
  }
}

/// Individual inventory item list tile
class InventoryItemListTile extends StatelessWidget {
  final InventoryItem item;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onStockAdjust;

  const InventoryItemListTile({
    Key? key,
    required this.item,
    this.isSelected = false,
    this.onTap,
    this.onLongPress,
    this.onStockAdjust,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
      child: ListTile(
        onTap: onTap,
        onLongPress: onLongPress,
        leading: CircleAvatar(
          backgroundColor: _getStockStatusColor(item.stockStatus),
          child: Text(
            item.itemCode.length >= 2 
                ? item.itemCode.substring(0, 2).toUpperCase()
                : item.itemCode.toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          item.itemName,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Code: ${item.itemCode}'),
            Text('Stock: ${item.currentStock.toStringAsFixed(0)} ${item.unit}'),
            Text('Category: ${item.category.displayName}'),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStockStatusColor(item.stockStatus).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    item.stockStatus.displayName,
                    style: TextStyle(
                      color: _getStockStatusColor(item.stockStatus),
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (item.isExpiringSoon) ...[
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'Expiring Soon',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: onStockAdjust != null 
            ? IconButton(
                icon: const Icon(Icons.tune),
                onPressed: onStockAdjust,
                tooltip: 'Adjust Stock',
              )
            : null,
        isThreeLine: true,
        selected: isSelected,
      ),
    );
  }

  Color _getStockStatusColor(StockStatus status) {
    switch (status) {
      case StockStatus.inStock:
        return Colors.green;
      case StockStatus.lowStock:
        return Colors.orange;
      case StockStatus.outOfStock:
        return Colors.red;
      case StockStatus.overstock:
        return Colors.blue;
    }
  }
}

/// Inventory grid view
class InventoryGridView extends StatelessWidget {
  final List<InventoryItem> items;
  final Function(InventoryItem)? onItemTap;
  final bool isLoading;

  const InventoryGridView({
    Key? key,
    required this.items,
    this.onItemTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No inventory items found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return InventoryItemCard(
          item: item,
          onTap: () => onItemTap?.call(item),
        );
      },
    );
  }
}

/// Individual inventory item card
class InventoryItemCard extends StatelessWidget {
  final InventoryItem item;
  final VoidCallback? onTap;

  const InventoryItemCard({
    Key? key,
    required this.item,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStockStatusColor(item.stockStatus),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item.itemCode,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                item.itemName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                item.category.displayName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${item.currentStock.toStringAsFixed(0)} ${item.unit}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getStockStatusColor(item.stockStatus),
                    ),
                  ),
                  if (item.isLowStock || item.isOutOfStock)
                    Icon(
                      Icons.warning,
                      color: _getStockStatusColor(item.stockStatus),
                      size: 20,
                    ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: item.stockPercentage / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getStockStatusColor(item.stockStatus),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStockStatusColor(StockStatus status) {
    switch (status) {
      case StockStatus.inStock:
        return Colors.green;
      case StockStatus.lowStock:
        return Colors.orange;
      case StockStatus.outOfStock:
        return Colors.red;
      case StockStatus.overstock:
        return Colors.blue;
    }
  }
}

