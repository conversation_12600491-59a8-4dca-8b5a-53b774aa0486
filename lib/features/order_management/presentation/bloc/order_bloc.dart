import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:hm_collection/shared/enums/common_enums.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/models/create_order_request.dart';
import '../../domain/entities/client_entities.dart';
import '../../domain/entities/order_entities.dart';
import '../../domain/usecases/order_usecases.dart';
import '../../domain/repositories/order_repository.dart' as repo;

part 'order_event.dart';
part 'order_state.dart';

/// Order Bloc
@injectable
class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final GetOrdersUseCase _getOrdersUseCase;
  final GetOrderByIdUseCase _getOrderByIdUseCase;
  final CreateOrderUseCase _createOrderUseCase;
  final UpdateOrderUseCase _updateOrderUseCase;
  final DeleteOrderUseCase _deleteOrderUseCase;
  final UpdateOrderStatusUseCase _updateOrderStatusUseCase;
  final SearchOrdersUseCase _searchOrdersUseCase;
  final GetOrdersByClientUseCase _getOrdersByClientUseCase;
  final GetOverdueOrdersUseCase _getOverdueOrdersUseCase;
  final GetUrgentOrdersUseCase _getUrgentOrdersUseCase;
  final CancelOrderUseCase _cancelOrderUseCase;
  final DuplicateOrderUseCase _duplicateOrderUseCase;
  final GetOrderStatisticsUseCase _getOrderStatisticsUseCase;
  final GetOrderTimelineUseCase _getOrderTimelineUseCase;
  final AddOrderNoteUseCase _addOrderNoteUseCase;

  OrderBloc(
    this._getOrdersUseCase,
    this._getOrderByIdUseCase,
    this._createOrderUseCase,
    this._updateOrderUseCase,
    this._deleteOrderUseCase,
    this._updateOrderStatusUseCase,
    this._searchOrdersUseCase,
    this._getOrdersByClientUseCase,
    this._getOverdueOrdersUseCase,
    this._getUrgentOrdersUseCase,
    this._cancelOrderUseCase,
    this._duplicateOrderUseCase,
    this._getOrderStatisticsUseCase,
    this._getOrderTimelineUseCase,
    this._addOrderNoteUseCase,
  ) : super(const OrderInitial()) {
    on<LoadOrdersRequested>(_onLoadOrdersRequested);
    on<RefreshOrdersRequested>(_onRefreshOrdersRequested);
    on<LoadOrderDetailsRequested>(_onLoadOrderDetailsRequested);
    on<CreateOrderRequested>(_onCreateOrderRequested);
    on<UpdateOrderRequested>(_onUpdateOrderRequested);
    on<DeleteOrderRequested>(_onDeleteOrderRequested);
    on<UpdateOrderStatusRequested>(_onUpdateOrderStatusRequested);
    on<SearchOrdersRequested>(_onSearchOrdersRequested);
    on<FilterOrdersRequested>(_onFilterOrdersRequested);
    on<LoadOrdersByClientRequested>(_onLoadOrdersByClientRequested);
    on<LoadOverdueOrdersRequested>(_onLoadOverdueOrdersRequested);
    on<LoadUrgentOrdersRequested>(_onLoadUrgentOrdersRequested);
    on<CancelOrderRequested>(_onCancelOrderRequested);
    on<DuplicateOrderRequested>(_onDuplicateOrderRequested);
    on<LoadOrderStatisticsRequested>(_onLoadOrderStatisticsRequested);
    on<LoadOrderTimelineRequested>(_onLoadOrderTimelineRequested);
    on<AddOrderNoteRequested>(_onAddOrderNoteRequested);
    on<ClearOrderState>(_onClearOrderState);
  }

  Future<void> _onLoadOrdersRequested(
    LoadOrdersRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _getOrdersUseCase(GetOrdersParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) => emit(OrdersLoaded(
        orders: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onRefreshOrdersRequested(
    RefreshOrdersRequested event,
    Emitter<OrderState> emit,
  ) async {
    final currentState = state;
    if (currentState is OrdersLoaded) {
      emit(currentState.copyWith(isRefreshing: true));

      final result = await _getOrdersUseCase(GetOrdersParams(
        filter: currentState.filter,
        pagination: const PaginationParams(page: 1, perPage: 20),
      ));

      result.fold(
        (failure) => emit(currentState.copyWith(isRefreshing: false)),
        (response) => emit(currentState.copyWith(
          orders: response.data ?? [],
          pagination: response.pagination,
          isRefreshing: false,
        )),
      );
    }
  }

  Future<void> _onLoadOrderDetailsRequested(
    LoadOrderDetailsRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _getOrderByIdUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(OrderDetailsLoaded(response.data!));
        } else {
          emit(const OrderError('Order not found'));
        }
      },
    );
  }

  Future<void> _onCreateOrderRequested(
    CreateOrderRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _createOrderUseCase(event.request);

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(OrderCreated(response.data!));
        } else {
          emit(const OrderError('Failed to create order'));
        }
      },
    );
  }

  Future<void> _onUpdateOrderRequested(
    UpdateOrderRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _updateOrderUseCase(event.request);

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(OrderUpdated(response.data!));
        } else {
          emit(const OrderError('Failed to update order'));
        }
      },
    );
  }

  Future<void> _onDeleteOrderRequested(
    DeleteOrderRequested event,
    Emitter<OrderState> emit,
  ) async {
    final result = await _deleteOrderUseCase(DeleteOrderParams(
      event.orderId,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (success) => emit(OrderDeleted(event.orderId)),
    );
  }

  Future<void> _onUpdateOrderStatusRequested(
    UpdateOrderStatusRequested event,
    Emitter<OrderState> emit,
  ) async {
    final result = await _updateOrderStatusUseCase(UpdateOrderStatusParams(
      event.orderId,
      event.status,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(OrderStatusUpdated(response.data!));
        } else {
          emit(const OrderError('Failed to update order status'));
        }
      },
    );
  }

  Future<void> _onSearchOrdersRequested(
    SearchOrdersRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _searchOrdersUseCase(SearchOrdersParams(
      event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) => emit(OrdersSearched(
        orders: response.data ?? [],
        pagination: response.pagination,
        query: event.query,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onFilterOrdersRequested(
    FilterOrdersRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _getOrdersUseCase(GetOrdersParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) => emit(OrdersFiltered(
        orders: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onLoadOrdersByClientRequested(
    LoadOrdersByClientRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _getOrdersByClientUseCase(GetOrdersByClientParams(
      event.clientId,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) => emit(OrdersByClientLoaded(
        orders: response.data ?? [],
        pagination: response.pagination,
        clientId: event.clientId,
      )),
    );
  }

  Future<void> _onLoadOverdueOrdersRequested(
    LoadOverdueOrdersRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _getOverdueOrdersUseCase(GetOverdueOrdersParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) => emit(OverdueOrdersLoaded(
        orders: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadUrgentOrdersRequested(
    LoadUrgentOrdersRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderLoading());

    final result = await _getUrgentOrdersUseCase(GetUrgentOrdersParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) => emit(UrgentOrdersLoaded(
        orders: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onCancelOrderRequested(
    CancelOrderRequested event,
    Emitter<OrderState> emit,
  ) async {
    final result = await _cancelOrderUseCase(CancelOrderParams(
      event.orderId,
      event.reason,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(OrderCancelled(response.data!));
        } else {
          emit(const OrderError('Failed to cancel order'));
        }
      },
    );
  }

  Future<void> _onDuplicateOrderRequested(
    DuplicateOrderRequested event,
    Emitter<OrderState> emit,
  ) async {
    final result = await _duplicateOrderUseCase(DuplicateOrderParams(
      event.orderId,
      event.request,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(OrderDuplicated(response.data!));
        } else {
          emit(const OrderError('Failed to duplicate order'));
        }
      },
    );
  }

  Future<void> _onLoadOrderStatisticsRequested(
    LoadOrderStatisticsRequested event,
    Emitter<OrderState> emit,
  ) async {
    final result = await _getOrderStatisticsUseCase(GetOrderStatisticsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      clientId: event.clientId,
    ));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (statistics) => emit(OrderStatisticsLoaded(statistics as OrderStatistics)),
    );
  }

  Future<void> _onLoadOrderTimelineRequested(
    LoadOrderTimelineRequested event,
    Emitter<OrderState> emit,
  ) async {
    final result = await _getOrderTimelineUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (timelineData) {
        try {
          // Convert the dynamic list to List<OrderTimelineEvent>
          final timelineEvents = (timelineData as List<dynamic>)
              .map((item) => OrderTimelineEvent.fromJson(item as Map<String, dynamic>))
              .toList();
          
          emit(OrderTimelineLoaded(event.orderId, timelineEvents));
        } catch (e) {
          emit(OrderError('Failed to load order timeline: $e'));
        }
      },
    );
  }

  Future<void> _onAddOrderNoteRequested(
    AddOrderNoteRequested event,
    Emitter<OrderState> emit,
  ) async {
    final result = await _addOrderNoteUseCase(event.request);

    result.fold(
      (failure) => emit(OrderError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(OrderNoteAdded(response.data!));
        } else {
          emit(const OrderError('Failed to add order note'));
        }
      },
    );
  }

  void _onClearOrderState(
    ClearOrderState event,
    Emitter<OrderState> emit,
  ) {
    emit(const OrderInitial());
  }
}
