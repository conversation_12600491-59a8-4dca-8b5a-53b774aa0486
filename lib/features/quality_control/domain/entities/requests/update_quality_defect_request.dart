import 'package:equatable/equatable.dart';

class UpdateQualityDefectRequest extends Equatable {
  final String defectId;
  final String? title;
  final String? description;
  final String? severity;
  final String? status;
  final String? assignedTo;
  final DateTime? dueDate;
  final String? resolutionNotes;
  final List<String>? attachmentUrls;

  const UpdateQualityDefectRequest({
    required this.defectId,
    this.title,
    this.description,
    this.severity,
    this.status,
    this.assignedTo,
    this.dueDate,
    this.resolutionNotes,
    this.attachmentUrls,
  });

  Map<String, dynamic> toJson() {
    return {
      'defectId': defectId,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (severity != null) 'severity': severity,
      if (status != null) 'status': status,
      if (assignedTo != null) 'assignedTo': assignedTo,
      if (dueDate != null) 'dueDate': dueDate?.toIso8601String(),
      if (resolutionNotes != null) 'resolutionNotes': resolutionNotes,
      if (attachmentUrls != null) 'attachmentUrls': attachmentUrls,
    };
  }

  @override
  List<Object?> get props => [
        defectId,
        title,
        description,
        severity,
        status,
        assignedTo,
        dueDate,
        resolutionNotes,
        attachmentUrls,
      ];
}
