import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../analytics/domain/repositories/analytics_repository.dart' hide ReportFilterCriteria, GenerateReportRequest;
import '../../domain/entities/financial_entities.dart';
import '../../domain/entities/financial_report_entities.dart' hide CostTrend;
import '../../domain/repositories/financial_repository.dart';

/// Financial data source interface
abstract class FinancialDataSource {
  // Cost Center Management
  Future<ApiListResponse<CostCenter>> getCostCenters({
    CostCenterFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<CostCenter>> getCostCenterById(String costCenterId);

  Future<ApiResponse<CostCenter>> createCostCenter(CreateCostCenterRequest request);

  Future<ApiResponse<CostCenter>> updateCostCenter(UpdateCostCenterRequest request);

  Future<ApiVoidResponse> deleteCostCenter(String costCenterId);

  // Financial Transaction Management
  Future<ApiListResponse<FinancialTransaction>> getTransactions({
    TransactionFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<FinancialTransaction>> getTransactionById(String transactionId);

  Future<ApiResponse<FinancialTransaction>> createTransaction(CreateTransactionRequest request);

  Future<ApiResponse<FinancialTransaction>> updateTransaction(UpdateTransactionRequest request);

  Future<ApiVoidResponse> deleteTransaction(String transactionId);

  Future<ApiResponse<FinancialTransaction>> approveTransaction(String transactionId, String approverId);

  Future<ApiResponse<FinancialTransaction>> rejectTransaction(String transactionId, String rejectedBy, String reason);

  // Budget Management
  Future<ApiListResponse<Budget>> getBudgets({
    BudgetFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<Budget>> getBudgetById(String budgetId);

  Future<ApiResponse<Budget>> createBudget(CreateBudgetRequest request);

  Future<ApiResponse<Budget>> updateBudget(UpdateBudgetRequest request);

  Future<ApiVoidResponse> deleteBudget(String budgetId);

  Future<ApiResponse<Budget>> approveBudget(String budgetId, String approverId);

  Future<ApiResponse<Budget>> rejectBudget(String budgetId, String rejectedBy, String reason);

  // Financial Reporting
  Future<ApiListResponse<FinancialReport>> getReports({
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<FinancialReport>> getReportById(String reportId);

  Future<ApiResponse<FinancialReport>> generateReport(GenerateReportRequest request);

  Future<ProfitLossStatement> getProfitLossStatement({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  });

  Future<CostAnalysisReport> getCostAnalysisReport({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  });

  Future<BudgetVarianceReport> getBudgetVarianceReport({
    required DateTime startDate,
    required DateTime endDate,
    String? budgetId,
    String? costCenterId,
  });

  // Financial Analytics
  Future<Map<String, double>> getFinancialSummary({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
  });

  Future<List<CostTrend>> getCostTrends({
    required DateTime startDate,
    required DateTime endDate,
    CostCategory? category,
    String? costCenterId,
  });

  Future<Map<String, double>> getRevenueBreakdown({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
  });

  Future<Map<String, double>> getExpenseBreakdown({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
  });

  // Search and Filtering
  Future<ApiListResponse<CostCenter>> searchCostCenters({
    required String query,
    CostCenterFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiListResponse<FinancialTransaction>> searchTransactions({
    required String query,
    TransactionFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiListResponse<Budget>> searchBudgets({
    required String query,
    BudgetFilterCriteria? filter,
    PaginationParams? pagination,
  });
}
