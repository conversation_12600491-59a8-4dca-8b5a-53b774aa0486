import 'package:flutter/material.dart';
import '../../domain/entities/inventory_entities.dart';
import '../../domain/repositories/inventory_repository.dart';

/// A card widget that displays inventory statistics
class InventoryStatisticsCard extends StatelessWidget {
  final InventoryStatistics statistics;
  final VoidCallback? onTap;

  const InventoryStatisticsCard({
    Key? key,
    required this.statistics,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Inventory Statistics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildStatisticRow(
                context,
                'Total Items',
                statistics.totalItems.toString(),
                Icons.inventory_2,
                Colors.blue,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Low Stock Items',
                statistics.lowStockItems.toString(),
                Icons.warning,
                Colors.orange,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Out of Stock',
                statistics.outOfStockItems.toString(),
                Icons.error,
                Colors.red,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Total Value',
                '\$${statistics.totalValue.toStringAsFixed(2)}',
                Icons.attach_money,
                Colors.green,
              ),
              const SizedBox(height: 16),
              _buildStockHealthIndicator(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStockHealthIndicator(BuildContext context) {
    final healthyItems = statistics.totalItems - statistics.lowStockItems - statistics.outOfStockItems;
    final healthPercentage = statistics.totalItems > 0 ? healthyItems / statistics.totalItems : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Stock Health',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${(healthPercentage * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: healthPercentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            healthPercentage >= 0.8 ? Colors.green : healthPercentage >= 0.6 ? Colors.orange : Colors.red,
          ),
        ),
      ],
    );
  }
}

/// A grid of inventory statistics cards
class InventoryStatisticsGrid extends StatelessWidget {
  final InventoryStatistics statistics;

  const InventoryStatisticsGrid({
    Key? key,
    required this.statistics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildStatCard(
          context,
          'Total Items',
          statistics.totalItems.toString(),
          Icons.inventory_2,
          Colors.blue,
        ),
        _buildStatCard(
          context,
          'Low Stock',
          statistics.lowStockItems.toString(),
          Icons.warning,
          Colors.orange,
        ),
        _buildStatCard(
          context,
          'Out of Stock',
          statistics.outOfStockItems.toString(),
          Icons.error,
          Colors.red,
        ),
        _buildStatCard(
          context,
          'Total Value',
          '\$${(statistics.totalValue / 1000).toStringAsFixed(1)}K',
          Icons.attach_money,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Inventory category breakdown chart
class InventoryCategoryChart extends StatelessWidget {
  final List<CategoryData> categoryData;
  final String title;

  const InventoryCategoryChart({
    Key? key,
    required this.categoryData,
    this.title = 'Inventory by Category',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...categoryData.map((data) => _buildCategoryRow(context, data)),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryRow(BuildContext context, CategoryData data) {
    final total = categoryData.fold<int>(0, (sum, item) => sum + item.count);
    final percentage = total > 0 ? data.count / total : 0.0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              data.category,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            data.count.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(percentage * 100).toStringAsFixed(1)}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

/// Stock alerts card
class StockAlertsCard extends StatelessWidget {
  final List<StockAlert> alerts;
  final VoidCallback? onViewAll;

  const StockAlertsCard({
    Key? key,
    required this.alerts,
    this.onViewAll,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Stock Alerts',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (alerts.length > 3)
                  TextButton(
                    onPressed: onViewAll,
                    child: const Text('View All'),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (alerts.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'No stock alerts',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              )
            else
              ...alerts.take(3).map((alert) => _buildAlertItem(context, alert)),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(BuildContext context, StockAlert alert) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            _getAlertIcon(alert.type),
            color: _getAlertColor(alert.type),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  alert.itemName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  alert.message,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getAlertIcon(StockAlertType type) {
    switch (type) {
      case StockAlertType.lowStock:
        return Icons.warning;
      case StockAlertType.outOfStock:
        return Icons.error;
      case StockAlertType.expiringSoon:
        return Icons.schedule;
    }
  }

  Color _getAlertColor(StockAlertType type) {
    switch (type) {
      case StockAlertType.lowStock:
        return Colors.orange;
      case StockAlertType.outOfStock:
        return Colors.red;
      case StockAlertType.expiringSoon:
        return Colors.blue;
    }
  }
}

class CategoryData {
  final String category;
  final int count;
  final double value;
  final double percentage;

  CategoryData(this.category, this.count, this.value, this.percentage);
}

class StockAlert {
  final String itemId;
  final String itemName;
  final StockAlertType type;
  final String message;
  final DateTime date;

  StockAlert(this.itemId, this.itemName, this.type, this.message, this.date);
}

enum StockAlertType { lowStock, outOfStock, expiringSoon }