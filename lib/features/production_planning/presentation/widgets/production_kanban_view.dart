import 'package:flutter/material.dart';

import '../../domain/entities/production_entities.dart';

/// Kanban view for production orders
class ProductionKanbanView extends StatelessWidget {
  final List<ProductionOrder> orders;
  final Function(ProductionOrder, ProductionStatus)? onStatusChanged;
  final Function(ProductionOrder)? onOrderTap;

  const ProductionKanbanView({
    Key? key,
    required this.orders,
    this.onStatusChanged,
    this.onOrderTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final plannedOrders = orders.where((o) => o.status == ProductionStatus.planned).toList();
    final inProgressOrders = orders.where((o) => o.status == ProductionStatus.inProgress).toList();
    final completedOrders = orders.where((o) => o.status == ProductionStatus.completed).toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildKanbanColumn(
            context,
            'Planned',
            plannedOrders,
            Colors.blue,
            ProductionStatus.planned,
          ),
          _buildKanbanColumn(
            context,
            'In Progress',
            inProgressOrders,
            Colors.orange,
            ProductionStatus.inProgress,
          ),
          _buildKanbanColumn(
            context,
            'Completed',
            completedOrders,
            Colors.green,
            ProductionStatus.completed,
          ),
        ],
      ),
    );
  }

  Widget _buildKanbanColumn(
    BuildContext context,
    String title,
    List<ProductionOrder> orders,
    Color color,
    ProductionStatus status,
  ) {
    return Container(
      width: 300,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    orders.length.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
              ),
              child: orders.isEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(32),
                        child: Text(
                          'No orders',
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 14,
                          ),
                        ),
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: orders.length,
                      itemBuilder: (context, index) {
                        return ProductionOrderCard(
                          order: orders[index],
                          onTap: () => onOrderTap?.call(orders[index]),
                          onStatusChanged: (newStatus) => onStatusChanged?.call(orders[index], newStatus),
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Individual production order card for kanban view
class ProductionOrderCard extends StatelessWidget {
  final ProductionOrder order;
  final VoidCallback? onTap;
  final Function(ProductionStatus)? onStatusChanged;

  const ProductionOrderCard({
    Key? key,
    required this.order,
    this.onTap,
    this.onStatusChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      order.orderNumber,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  PopupMenuButton<ProductionStatus>(
                    onSelected: onStatusChanged,
                    itemBuilder: (context) => ProductionStatus.values
                        .where((status) => status != order.status)
                        .map((status) => PopupMenuItem(
                              value: status,
                              child: Text(status.displayName),
                            ))
                        .toList(),
                    child: const Icon(Icons.more_vert, size: 16),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                order.clientName,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 4),
              Text(
                'Qty: ${order.totalQuantity}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 14,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(order.plannedEndDate),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: _getProgress(),
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor()),
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _getProgress() {
    switch (order.status) {
      case ProductionStatus.planned:
      case ProductionStatus.scheduled:
        return 0.0;
      case ProductionStatus.inProgress:
        return order.completionPercentage / 100;
      case ProductionStatus.completed:
        return 1.0;
      case ProductionStatus.cancelled:
      case ProductionStatus.paused:
      case ProductionStatus.onHold:
        return order.completionPercentage / 100;
    }
  }

  Color _getProgressColor() {
    switch (order.status) {
      case ProductionStatus.planned:
      case ProductionStatus.scheduled:
        return Colors.blue;
      case ProductionStatus.inProgress:
        return Colors.orange;
      case ProductionStatus.completed:
        return Colors.green;
      case ProductionStatus.cancelled:
        return Colors.red;
      case ProductionStatus.paused:
      case ProductionStatus.onHold:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}