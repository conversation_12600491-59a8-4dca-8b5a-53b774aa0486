import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/auth/widgets/permission_guard.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../bloc/collaboration_bloc.dart';
import '../widgets/workspace_grid_widget.dart';
import '../widgets/recent_activity_widget.dart';
import '../widgets/online_members_widget.dart';
import '../widgets/quick_actions_widget.dart' as quick_actions;

/// Collaboration main page
class CollaborationPage extends StatefulWidget {
  const CollaborationPage({super.key});

  @override
  State<CollaborationPage> createState() => _CollaborationPageState();
}

class _CollaborationPageState extends State<CollaborationPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late CollaborationBloc _collaborationBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _collaborationBloc = GetIt.instance<CollaborationBloc>();
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _collaborationBloc.close();
    super.dispose();
  }

  void _loadInitialData() {
    // Load workspaces
    _collaborationBloc.add(const GetWorkspacesRequested());

    // Load notifications
    _collaborationBloc.add(const GetNotificationsRequested());
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _collaborationBloc,
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'Team Collaboration',
          actions: [
            PermissionGuard(
              permission: 'collaboration.workspaces.create',
              child: IconButton(
                icon: const Icon(Icons.add_business),
                onPressed: () => _navigateToCreateWorkspace(),
                tooltip: 'Create Workspace',
              ),
            ),
            PermissionGuard(
              permission: 'collaboration.sessions.create',
              child: IconButton(
                icon: const Icon(Icons.video_call),
                onPressed: () => _startQuickSession(),
                tooltip: 'Start Session',
              ),
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => _showGlobalSearch(),
              tooltip: 'Search',
            ),
            IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: () => _showNotifications(),
              tooltip: 'Notifications',
            ),
          ],
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildWorkspacesTab(),
                  _buildMessagesTab(),
                  _buildSessionsTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: PermissionGuard(
          permission: 'collaboration.workspaces.create',
          child: FloatingActionButton(
            onPressed: () => _navigateToCreateWorkspace(),
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.workspaces),
            text: 'Workspaces',
          ),
          Tab(
            icon: Icon(Icons.chat),
            text: 'Messages',
          ),
          Tab(
            icon: Icon(Icons.video_call),
            text: 'Sessions',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Collaboration Overview',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Quick Actions
           quick_actions.QuickActionsWidget(
            onCreateWorkspace: _navigateToCreateWorkspace,
            onStartMeeting: _startQuickSession,
            onJoinWorkspace: _showJoinSessionDialog,
          ),
          
          const SizedBox(height: 24),
          
          // Stats Cards
          Row(
            children: [
              Expanded(
                child: _buildStatsCard(
                  'Active Workspaces',
                  '8',
                  Icons.workspaces,
                  AppColors.primary,
                  '+2 this week',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatsCard(
                  'Team Members',
                  '24',
                  Icons.people,
                  AppColors.success,
                  '3 online now',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatsCard(
                  'Messages Today',
                  '156',
                  Icons.chat,
                  AppColors.warning,
                  '+12% vs yesterday',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatsCard(
                  'Active Sessions',
                  '2',
                  Icons.video_call,
                  AppColors.error,
                  'Production meeting',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Recent Activity
              Expanded(
                flex: 2,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Recent Activity',
                              style: AppTextStyles.headlineSmall.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            TextButton(
                              onPressed: () => _showAllActivity(),
                              child: const Text('View All'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const RecentActivityWidget(),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Online Members
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Online Now',
                          style: AppTextStyles.headlineSmall.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                         OnlineMembersWidget(
                          onlineMembers: [], // TODO: Get actual online members from state
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Quick Access to Workspaces
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Quick Access',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => _tabController.animateTo(1),
                        child: const Text('View All Workspaces'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildQuickAccessWorkspaces(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkspacesTab() {
    return PermissionGuard(
      permission: 'collaboration.workspaces.read',
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Collaboration Workspaces',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.filter_list),
                      onPressed: () => _showWorkspaceFilters(),
                      tooltip: 'Filter Workspaces',
                    ),
                    IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: () => _showWorkspaceSearch(),
                      tooltip: 'Search Workspaces',
                    ),
                    PermissionGuard(
                      permission: 'collaboration.workspaces.create',
                      child: ElevatedButton.icon(
                        onPressed: () => _navigateToCreateWorkspace(),
                        icon: const Icon(Icons.add),
                        label: const Text('Create Workspace'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: BlocBuilder<CollaborationBloc, CollaborationState>(
                builder: (context, state) {
                  if (state is CollaborationWorkspacesLoaded) {
                    return WorkspaceGridWidget(
                      workspaces: state.workspaces,
                      onWorkspaceTap: (workspace) => _navigateToWorkspace(workspace.id),
                      onWorkspaceEdit: (workspace) => _editWorkspace(workspace.id),
                      onWorkspaceDelete: (workspace) => _deleteWorkspace(workspace.id),
                      onWorkspaceArchive: (workspace) => _archiveWorkspace(workspace.id),
                      onWorkspaceShare: (workspace) => _shareWorkspace(workspace.id),
                    );
                  } else if (state is CollaborationLoading) {
                    return const LoadingWidget();
                  } else if (state is CollaborationError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: AppColors.error,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading workspaces',
                            style: AppTextStyles.bodyLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => _loadInitialData(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }
                  return const Center(
                    child: Text('No workspaces available'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      fallback: const Center(
        child: Text('You do not have permission to view workspaces'),
      ),
    );
  }

  Widget _buildMessagesTab() {
    return PermissionGuard(
      permission: 'collaboration.messages.read',
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Messages & Channels',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: () => _showMessageSearch(),
                      tooltip: 'Search Messages',
                    ),
                    PermissionGuard(
                      permission: 'collaboration.channels.create',
                      child: ElevatedButton.icon(
                        onPressed: () => _navigateToCreateChannel(),
                        icon: const Icon(Icons.add),
                        label: const Text('Create Channel'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: _buildMessagesContent(),
            ),
          ],
        ),
      ),
      fallback: const Center(
        child: Text('You do not have permission to view messages'),
      ),
    );
  }

  Widget _buildSessionsTab() {
    return PermissionGuard(
      permission: 'collaboration.sessions.read',
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Collaboration Sessions',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.history),
                      onPressed: () => _showSessionHistory(),
                      tooltip: 'Session History',
                    ),
                    PermissionGuard(
                      permission: 'collaboration.sessions.create',
                      child: ElevatedButton.icon(
                        onPressed: () => _navigateToCreateSession(),
                        icon: const Icon(Icons.video_call),
                        label: const Text('Start Session'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: _buildSessionsContent(),
            ),
          ],
        ),
      ),
      fallback: const Center(
        child: Text('You do not have permission to view sessions'),
      ),
    );
  }

  Widget _buildStatsCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                Text(
                  value,
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAccessWorkspaces() {
    // Mock quick access workspaces
    return Row(
      children: [
        Expanded(
          child: _buildQuickWorkspaceCard(
            'Manufacturing Ops',
            'General workspace',
            Icons.precision_manufacturing,
            AppColors.primary,
            () => _navigateToWorkspace('workspace_1'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickWorkspaceCard(
            'Quality Control',
            'Department workspace',
            Icons.verified,
            AppColors.success,
            () => _navigateToWorkspace('workspace_2'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickWorkspaceCard(
            'Production Team',
            'Team workspace',
            Icons.groups,
            AppColors.warning,
            () => _navigateToWorkspace('workspace_3'),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickWorkspaceCard(
    String name,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                color: color,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                name,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessagesContent() {
    return const Center(
      child: Text('Messages content will be implemented here'),
    );
  }

  Widget _buildSessionsContent() {
    return const Center(
      child: Text('Sessions content will be implemented here'),
    );
  }

  void _navigateToCreateWorkspace() {
    // Navigate to create workspace page
  }

  void _startQuickSession() {
    // Start a quick collaboration session
  }

  void _showGlobalSearch() {
    // Show global search dialog
  }

  void _showNotifications() {
    // Show notifications panel
  }

  void _showJoinSessionDialog() {
    // Show join session dialog
  }

  void _showQuickMessageDialog() {
    // Show quick message dialog
  }

  void _showAllActivity() {
    // Show all activity page
  }

  void _showWorkspaceFilters() {
    // Show workspace filters dialog
  }

  void _showWorkspaceSearch() {
    // Show workspace search dialog
  }

  void _navigateToWorkspace(String workspaceId) {
    // Navigate to workspace details page
  }

  void _editWorkspace(String workspaceId) {
    // Navigate to edit workspace page
  }

  void _deleteWorkspace(String workspaceId) {
    // Show delete confirmation dialog
  }

  void _archiveWorkspace(String workspaceId) {
    // Archive workspace
  }

  void _shareWorkspace(String workspaceId) {
    // Show share workspace dialog
  }

  void _showMessageSearch() {
    // Show message search dialog
  }

  void _navigateToCreateChannel() {
    // Navigate to create channel page
  }

  void _showSessionHistory() {
    // Show session history page
  }

  void _navigateToCreateSession() {
    // Navigate to create session page
  }
}
