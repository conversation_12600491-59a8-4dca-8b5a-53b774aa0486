import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../domain/entities/bundle.dart';
import '../../domain/entities/bundle_input.dart';
import '../bloc/bundling_bloc.dart';

/// Form widget for creating bundles
class BundleCreationForm extends StatefulWidget {
  const BundleCreationForm({super.key});

  @override
  State<BundleCreationForm> createState() => _BundleCreationFormState();
}

class _BundleCreationFormState extends State<BundleCreationForm> {
  final _formKey = GlobalKey<FormState>();
  final _designNoController = TextEditingController();
  final _lotNoController = TextEditingController();
  final _rollNoController = TextEditingController();
  final _altNoController = TextEditingController();
  final _totalPiecesController = TextEditingController();
  final _piecesPerBundleController = TextEditingController();
  final _colorController = TextEditingController();
  final _orderNoController = TextEditingController();
  final _clientNameController = TextEditingController();
  final _remarksController = TextEditingController();

  GarmentSize _selectedSize = GarmentSize.m;
  List<BundlePreview>? _bundlePreviews;

  @override
  void dispose() {
    _designNoController.dispose();
    _lotNoController.dispose();
    _rollNoController.dispose();
    _altNoController.dispose();
    _totalPiecesController.dispose();
    _piecesPerBundleController.dispose();
    _colorController.dispose();
    _orderNoController.dispose();
    _clientNameController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<BundlingBloc, BundlingState>(
      listener: (context, state) {
        if (state is BundleCreationPreview) {
          setState(() {
            _bundlePreviews = state.previews;
          });
        } else if (state is BundlesCreated) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.success,
            ),
          );
          _clearForm();
        } else if (state is BundlingError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Create New Bundles',
              style: AppTextStyles.headlineMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            _buildInputSection(),
            const SizedBox(height: 24),
            if (_bundlePreviews != null) ...[
              _buildPreviewSection(),
              const SizedBox(height: 24),
            ],
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Build input section
  Widget _buildInputSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bundle Input Data',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _designNoController,
                    label: 'Design No.*',
                    hint: 'Enter design number',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Design number is required';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _lotNoController,
                    label: 'Lot No.*',
                    hint: 'Enter lot number',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Lot number is required';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _rollNoController,
                    label: 'Roll No.*',
                    hint: 'Enter roll number',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Roll number is required';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _altNoController,
                    label: 'Alt No.*',
                    hint: 'Enter alt number',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Alt number is required';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<GarmentSize>(
                    value: _selectedSize,
                    decoration: const InputDecoration(
                      labelText: 'Size*',
                      border: OutlineInputBorder(),
                    ),
                    items: GarmentSize.values.map((size) {
                      return DropdownMenuItem(
                        value: size,
                        child: Text(size.displayName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedSize = value;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _colorController,
                    label: 'Color',
                    hint: 'Enter color (optional)',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _totalPiecesController,
                    label: 'Total Pieces Available*',
                    hint: 'Enter total pieces',
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Total pieces is required';
                      }
                      final pieces = int.tryParse(value);
                      if (pieces == null || pieces <= 0) {
                        return 'Enter a valid number greater than 0';
                      }
                      return null;
                    },
                    onChanged: (value) => _updatePreview(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _piecesPerBundleController,
                    label: 'Pieces per Bundle*',
                    hint: 'Enter pieces per bundle',
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Pieces per bundle is required';
                      }
                      final pieces = int.tryParse(value);
                      if (pieces == null || pieces <= 0) {
                        return 'Enter a valid number greater than 0';
                      }
                      final totalPieces = int.tryParse(_totalPiecesController.text);
                      if (totalPieces != null && pieces > totalPieces) {
                        return 'Cannot exceed total pieces';
                      }
                      return null;
                    },
                    onChanged: (value) => _updatePreview(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _orderNoController,
                    label: 'Order No.',
                    hint: 'Enter order number (optional)',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _clientNameController,
                    label: 'Client Name',
                    hint: 'Enter client name (optional)',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _remarksController,
              label: 'Remarks',
              hint: 'Enter any remarks (optional)',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  /// Build preview section
  Widget _buildPreviewSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bundle Preview',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${_bundlePreviews!.length} bundles will be created',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.border),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _bundlePreviews!.length,
                itemBuilder: (context, index) {
                  final preview = _bundlePreviews![index];
                  return ListTile(
                    dense: true,
                    title: Text(
                      preview.bundleNo,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      'Pieces: ${preview.pieceRange} (${preview.totalPieces} pcs)',
                      style: AppTextStyles.bodySmall,
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${preview.totalPieces}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return BlocBuilder<BundlingBloc, BundlingState>(
      builder: (context, state) {
        final isLoading = state is BundlingOperationInProgress;
        
        return Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: isLoading ? null : _clearForm,
                child: const Text('Clear'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: isLoading ? null : _previewBundles,
                child: const Text('Preview'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: (isLoading || _bundlePreviews == null) ? null : _createBundles,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('Create Bundles'),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Update preview when input changes
  void _updatePreview() {
    if (_totalPiecesController.text.isNotEmpty &&
        _piecesPerBundleController.text.isNotEmpty) {
      final totalPieces = int.tryParse(_totalPiecesController.text);
      final piecesPerBundle = int.tryParse(_piecesPerBundleController.text);
      
      if (totalPieces != null && piecesPerBundle != null && 
          totalPieces > 0 && piecesPerBundle > 0 && piecesPerBundle <= totalPieces) {
        _previewBundles();
      }
    }
  }

  /// Preview bundles
  void _previewBundles() {
    if (!_formKey.currentState!.validate()) return;

    final input = _createBundleInput();
    if (input != null) {
      context.read<BundlingBloc>().add(PreviewBundleCreation(input));
    }
  }

  /// Create bundles
  void _createBundles() {
    if (!_formKey.currentState!.validate()) return;

    context.read<BundlingBloc>().add(CreateBundles(
      designNo: _designNoController.text.trim(),
      lotNo: _lotNoController.text.trim(),
      rollNo: _rollNoController.text.trim(),
      altNo: _altNoController.text.trim(),
      size: _selectedSize,
      totalPiecesAvailable: int.parse(_totalPiecesController.text),
      piecesPerBundle: int.parse(_piecesPerBundleController.text),
      color: _colorController.text.trim().isEmpty ? null : _colorController.text.trim(),
      orderNo: _orderNoController.text.trim().isEmpty ? null : _orderNoController.text.trim(),
      clientName: _clientNameController.text.trim().isEmpty ? null : _clientNameController.text.trim(),
      remarks: _remarksController.text.trim().isEmpty ? null : _remarksController.text.trim(),
    ));
  }

  /// Create bundle input from form data
  BundleInput? _createBundleInput() {
    final totalPieces = int.tryParse(_totalPiecesController.text);
    final piecesPerBundle = int.tryParse(_piecesPerBundleController.text);

    if (totalPieces == null || piecesPerBundle == null) return null;

    return BundleInput(
      designNo: _designNoController.text.trim(),
      lotNo: _lotNoController.text.trim(),
      rollNo: _rollNoController.text.trim(),
      altNo: _altNoController.text.trim(),
      size: _selectedSize,
      totalPiecesAvailable: totalPieces,
      piecesPerBundle: piecesPerBundle,
      color: _colorController.text.trim().isEmpty ? null : _colorController.text.trim(),
      orderNo: _orderNoController.text.trim().isEmpty ? null : _orderNoController.text.trim(),
      clientName: _clientNameController.text.trim().isEmpty ? null : _clientNameController.text.trim(),
      remarks: _remarksController.text.trim().isEmpty ? null : _remarksController.text.trim(),
    );
  }

  /// Clear form
  void _clearForm() {
    _formKey.currentState?.reset();
    _designNoController.clear();
    _lotNoController.clear();
    _rollNoController.clear();
    _altNoController.clear();
    _totalPiecesController.clear();
    _piecesPerBundleController.clear();
    _colorController.clear();
    _orderNoController.clear();
    _clientNameController.clear();
    _remarksController.clear();
    setState(() {
      _selectedSize = GarmentSize.m;
      _bundlePreviews = null;
    });
  }
}
