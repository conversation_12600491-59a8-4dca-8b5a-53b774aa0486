import 'package:flutter/material.dart';

import '../../core/theme/app_colors.dart';


enum ButtonVariant { filled, outlined, text }
enum ButtonSize { small, medium, large }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final ButtonVariant variant;
  final ButtonSize size;
  final IconData? icon;
  final Color? backgroundColor;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.variant = ButtonVariant.filled,
    this.size = ButtonSize.medium,
    this.icon,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle(context);
    final content = _buildChild();

    switch (variant) {
      case ButtonVariant.outlined:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: content,
        );
      case ButtonVariant.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: content,
        );
      case ButtonVariant.filled:
      default:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: content,
        );
    }
  }

  Widget _buildChild() {
    if (isLoading) {
      return SizedBox(
        width: _getSpinnerSize(),
        height: _getSpinnerSize(),
        child: const CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getFontSize()),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  double _getFontSize() {
    switch (size) {
      case ButtonSize.small:
        return 12;
      case ButtonSize.large:
        return 16;
      case ButtonSize.medium:
      default:
        return 14;
    }
  }

  double _getSpinnerSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.large:
        return 24;
      case ButtonSize.medium:
      default:
        return 20;
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
      case ButtonSize.medium:
      default:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
    }
  }

  ButtonStyle _getButtonStyle(BuildContext context) {
    final theme = Theme.of(context);
    final textStyle = TextStyle(fontSize: _getFontSize(), fontWeight: FontWeight.bold);
    final shape = RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    );

    switch (variant) {
      case ButtonVariant.outlined:
        return OutlinedButton.styleFrom(
          padding: _getPadding(),
          textStyle: textStyle,
          shape: shape,
          side: BorderSide(color: backgroundColor ?? theme.primaryColor),
        );
      case ButtonVariant.text:
        return TextButton.styleFrom(
          padding: _getPadding(),
          textStyle: textStyle,
          shape: shape,
        );
      case ButtonVariant.filled:
      default:
        return ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColors.primary,
          foregroundColor: Colors.white,
          padding: _getPadding(),
          textStyle: textStyle,
          shape: shape,
        );
    }
  }
}
