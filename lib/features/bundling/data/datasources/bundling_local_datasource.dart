import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/bundle.dart';
import '../models/bundle_model.dart';

/// Local data source for bundling operations using SharedPreferences
@injectable
class BundlingLocalDataSource {
  final SharedPreferences _prefs;
  static const String _bundlesKey = 'bundling_bundles';
  static const String _lastSyncKey = 'bundling_last_sync';
  static const String _offlineBundlesKey = 'bundling_offline_bundles';
  static const String _bundleCounterKey = 'bundling_bundle_counter';

  BundlingLocalDataSource(this._prefs);

  /// Get all cached bundles
  Future<List<BundleModel>> getAllBundles() async {
    try {
      final bundlesJson = _prefs.getString(_bundlesKey);
      if (bundlesJson == null) return [];

      final List<dynamic> bundlesList = json.decode(bundlesJson);
      return bundlesList
          .map((bundleJson) => BundleModel.fromJson(bundleJson))
          .toList();
    } catch (e) {
      throw CacheException('Failed to load cached bundles: $e');
    }
  }

  /// Get a specific bundle by ID from cache
  Future<BundleModel?> getBundleById(String id) async {
    try {
      final bundles = await getAllBundles();
      return bundles.where((bundle) => bundle.id == id).firstOrNull;
    } catch (e) {
      throw CacheException('Failed to get cached bundle: $e');
    }
  }

  /// Get bundle by bundle number from cache
  Future<BundleModel?> getBundleByNumber(String bundleNo) async {
    try {
      final bundles = await getAllBundles();
      return bundles.where((bundle) => bundle.bundleNo == bundleNo).firstOrNull;
    } catch (e) {
      throw CacheException('Failed to get cached bundle by number: $e');
    }
  }

  /// Cache a single bundle
  Future<void> cacheBundle(BundleModel bundle) async {
    try {
      final bundles = await getAllBundles();
      
      // Remove existing bundle with same ID if exists
      bundles.removeWhere((b) => b.id == bundle.id);
      
      // Add the new/updated bundle
      bundles.add(bundle);
      
      // Save back to cache
      await _cacheBundles(bundles);
    } catch (e) {
      throw CacheException('Failed to cache bundle: $e');
    }
  }

  /// Cache multiple bundles
  Future<void> cacheBundles(List<BundleModel> bundles) async {
    try {
      await _cacheBundles(bundles);
    } catch (e) {
      throw CacheException('Failed to cache bundles: $e');
    }
  }

  /// Internal method to cache bundles
  Future<void> _cacheBundles(List<BundleModel> bundles) async {
    final bundlesJson = json.encode(bundles.map((b) => b.toJson()).toList());
    await _prefs.setString(_bundlesKey, bundlesJson);
  }

  /// Remove a bundle from cache
  Future<void> removeBundleFromCache(String id) async {
    try {
      final bundles = await getAllBundles();
      bundles.removeWhere((bundle) => bundle.id == id);
      await _cacheBundles(bundles);
    } catch (e) {
      throw CacheException('Failed to remove bundle from cache: $e');
    }
  }

  /// Get bundles by status from cache
  Future<List<BundleModel>> getBundlesByStatus(BundleStatus status) async {
    try {
      final bundles = await getAllBundles();
      return bundles.where((bundle) => bundle.status == status).toList();
    } catch (e) {
      throw CacheException('Failed to get cached bundles by status: $e');
    }
  }

  /// Get bundles assigned to a user from cache
  Future<List<BundleModel>> getBundlesAssignedTo(String userId) async {
    try {
      final bundles = await getAllBundles();
      return bundles.where((bundle) => bundle.assignedTo == userId).toList();
    } catch (e) {
      throw CacheException('Failed to get cached assigned bundles: $e');
    }
  }

  /// Search bundles in cache
  Future<List<BundleModel>> searchBundles(String query) async {
    try {
      final bundles = await getAllBundles();
      final lowerQuery = query.toLowerCase();
      
      return bundles.where((bundle) =>
          bundle.bundleNo.toLowerCase().contains(lowerQuery) ||
          bundle.designNo.toLowerCase().contains(lowerQuery) ||
          bundle.lotNo.toLowerCase().contains(lowerQuery) ||
          bundle.rollNo.toLowerCase().contains(lowerQuery) ||
          (bundle.orderNo?.toLowerCase().contains(lowerQuery) ?? false) ||
          (bundle.clientName?.toLowerCase().contains(lowerQuery) ?? false)
      ).toList();
    } catch (e) {
      throw CacheException('Failed to search cached bundles: $e');
    }
  }

  /// Store offline bundle (for later sync)
  Future<void> storeOfflineBundle(BundleModel bundle) async {
    try {
      final offlineBundlesJson = _prefs.getString(_offlineBundlesKey) ?? '[]';
      final List<dynamic> offlineBundles = json.decode(offlineBundlesJson);
      
      // Remove existing bundle with same ID if exists
      offlineBundles.removeWhere((b) => b['id'] == bundle.id);
      
      // Add the bundle
      offlineBundles.add(bundle.toJson());
      
      await _prefs.setString(_offlineBundlesKey, json.encode(offlineBundles));
    } catch (e) {
      throw CacheException('Failed to store offline bundle: $e');
    }
  }

  /// Get offline bundles
  Future<List<BundleModel>> getOfflineBundles() async {
    try {
      final offlineBundlesJson = _prefs.getString(_offlineBundlesKey) ?? '[]';
      final List<dynamic> offlineBundles = json.decode(offlineBundlesJson);
      
      return offlineBundles
          .map((bundleJson) => BundleModel.fromJson(bundleJson))
          .toList();
    } catch (e) {
      throw CacheException('Failed to get offline bundles: $e');
    }
  }

  /// Clear offline bundles
  Future<void> clearOfflineBundles() async {
    try {
      await _prefs.remove(_offlineBundlesKey);
    } catch (e) {
      throw CacheException('Failed to clear offline bundles: $e');
    }
  }

  /// Get offline bundles count
  Future<int> getOfflineBundlesCount() async {
    try {
      final offlineBundles = await getOfflineBundles();
      return offlineBundles.length;
    } catch (e) {
      return 0;
    }
  }

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime() async {
    try {
      final syncTimeString = _prefs.getString(_lastSyncKey);
      if (syncTimeString == null) return null;
      return DateTime.parse(syncTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Update last sync timestamp
  Future<void> updateLastSyncTime(DateTime syncTime) async {
    try {
      await _prefs.setString(_lastSyncKey, syncTime.toIso8601String());
    } catch (e) {
      throw CacheException('Failed to update last sync time: $e');
    }
  }

  /// Get cached bundle counter
  Future<int> getBundleCounter() async {
    try {
      return _prefs.getInt(_bundleCounterKey) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Update bundle counter
  Future<void> updateBundleCounter(int counter) async {
    try {
      await _prefs.setInt(_bundleCounterKey, counter);
    } catch (e) {
      throw CacheException('Failed to update bundle counter: $e');
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      await Future.wait([
        _prefs.remove(_bundlesKey),
        _prefs.remove(_lastSyncKey),
        _prefs.remove(_offlineBundlesKey),
        _prefs.remove(_bundleCounterKey),
      ]);
    } catch (e) {
      throw CacheException('Failed to clear cache: $e');
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final bundles = await getAllBundles();
      final offlineBundles = await getOfflineBundles();
      final lastSync = await getLastSyncTime();
      final counter = await getBundleCounter();
      
      return {
        'total_bundles': bundles.length,
        'pending_bundles': bundles.where((b) => b.status == BundleStatus.pending).length,
        'issued_bundles': bundles.where((b) => b.status == BundleStatus.issuedToSewing).length,
        'in_progress_bundles': bundles.where((b) => b.status == BundleStatus.inProgress).length,
        'completed_bundles': bundles.where((b) => b.status == BundleStatus.completed).length,
        'cancelled_bundles': bundles.where((b) => b.status == BundleStatus.cancelled).length,
        'offline_bundles': offlineBundles.length,
        'last_sync': lastSync?.toIso8601String(),
        'bundle_counter': counter,
        'cache_size_kb': _calculateCacheSize(),
      };
    } catch (e) {
      return {'error': 'Failed to get cache stats: $e'};
    }
  }

  /// Calculate approximate cache size in KB
  int _calculateCacheSize() {
    try {
      final bundlesJson = _prefs.getString(_bundlesKey) ?? '';
      final offlineBundlesJson = _prefs.getString(_offlineBundlesKey) ?? '';
      
      final totalSize = bundlesJson.length + offlineBundlesJson.length;
      return (totalSize / 1024).round(); // Convert to KB
    } catch (e) {
      return 0;
    }
  }

  /// Check if bundle exists in cache
  Future<bool> bundleExistsInCache(String bundleId) async {
    try {
      final bundle = await getBundleById(bundleId);
      return bundle != null;
    } catch (e) {
      return false;
    }
  }

  /// Check if bundle number exists in cache
  Future<bool> bundleNumberExistsInCache(String bundleNo) async {
    try {
      final bundle = await getBundleByNumber(bundleNo);
      return bundle != null;
    } catch (e) {
      return false;
    }
  }
}
