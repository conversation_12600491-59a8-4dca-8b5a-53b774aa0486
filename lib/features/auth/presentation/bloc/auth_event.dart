part of 'auth_bloc.dart';


/// Base authentication event
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Check authentication status
class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

/// Login with credentials
class AuthLoginRequested extends AuthEvent {
  final LoginCredentials credentials;
  final bool saveCredentials;
  final bool enableBiometric;

  const AuthLoginRequested({
    required this.credentials,
    this.saveCredentials = false,
    this.enableBiometric = false,
  });

  @override
  List<Object?> get props => [credentials, saveCredentials, enableBiometric];
}

/// Login with biometric authentication
class AuthBiometricLoginRequested extends AuthEvent {
  final String reason;

  const AuthBiometricLoginRequested({
    this.reason = 'Please authenticate to login',
  });

  @override
  List<Object?> get props => [reason];
}

/// Auto login attempt
class AuthAutoLoginRequested extends AuthEvent {
  const AuthAutoLoginRequested();
}

/// Logout request
class AuthLogoutRequested extends AuthEvent {
  final LogoutParams params;

  const AuthLogoutRequested(this.params);

  @override
  List<Object?> get props => [params];
}

/// Logout all sessions
class AuthLogoutAllSessionsRequested extends AuthEvent {
  const AuthLogoutAllSessionsRequested();
}

/// User data updated
class AuthUserUpdated extends AuthEvent {
  final User user;

  const AuthUserUpdated(this.user);

  @override
  List<Object?> get props => [user];
}

/// Authentication tokens updated
class AuthTokensUpdated extends AuthEvent {
  final AuthTokens tokens;

  const AuthTokensUpdated(this.tokens);

  @override
  List<Object?> get props => [tokens];
}

/// Session expired
class AuthSessionExpired extends AuthEvent {
  final String? reason;

  const AuthSessionExpired({this.reason});

  @override
  List<Object?> get props => [reason];
}

/// Password change requested
class AuthPasswordChangeRequested extends AuthEvent {
  final ChangePasswordRequest request;

  const AuthPasswordChangeRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Password reset requested
class AuthPasswordResetRequested extends AuthEvent {
  final PasswordResetRequest request;

  const AuthPasswordResetRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Password reset confirmation
class AuthPasswordResetConfirmed extends AuthEvent {
  final PasswordResetConfirmRequest confirmation;

  const AuthPasswordResetConfirmed(this.confirmation);

  @override
  List<Object?> get props => [confirmation];
}

/// Email verification requested
class AuthEmailVerificationRequested extends AuthEvent {
  const AuthEmailVerificationRequested();
}

/// Email verification confirmed
class AuthEmailVerificationConfirmed extends AuthEvent {
  final String token;

  const AuthEmailVerificationConfirmed(this.token);

  @override
  List<Object?> get props => [token];
}

/// Two-factor authentication setup requested
class AuthTwoFactorSetupRequested extends AuthEvent {
  const AuthTwoFactorSetupRequested();
}

/// Two-factor authentication enabled
class AuthTwoFactorEnabled extends AuthEvent {
  const AuthTwoFactorEnabled();
}

/// Two-factor authentication disabled
class AuthTwoFactorDisabled extends AuthEvent {
  final String code;

  const AuthTwoFactorDisabled(this.code);

  @override
  List<Object?> get props => [code];
}

/// Two-factor authentication verification
class AuthTwoFactorVerified extends AuthEvent {
  final TwoFactorAuthData data;

  const AuthTwoFactorVerified(this.data);

  @override
  List<Object?> get props => [data];
}

/// Biometric authentication setup requested
class AuthBiometricSetupRequested extends AuthEvent {
  const AuthBiometricSetupRequested();
}

/// Biometric authentication enabled
class AuthBiometricEnabled extends AuthEvent {
  final BiometricAuthData data;

  const AuthBiometricEnabled(this.data);

  @override
  List<Object?> get props => [data];
}

/// Biometric authentication disabled
class AuthBiometricDisabled extends AuthEvent {
  final String biometricType;

  const AuthBiometricDisabled(this.biometricType);

  @override
  List<Object?> get props => [biometricType];
}

/// Profile update requested
class AuthProfileUpdateRequested extends AuthEvent {
  final User user;

  const AuthProfileUpdateRequested(this.user);

  @override
  List<Object?> get props => [user];
}

/// Preferences update requested
class AuthPreferencesUpdateRequested extends AuthEvent {
  final UserPreferences preferences;

  const AuthPreferencesUpdateRequested(this.preferences);

  @override
  List<Object?> get props => [preferences];
}

/// Session revoke requested
class AuthSessionRevokeRequested extends AuthEvent {
  final String sessionId;

  const AuthSessionRevokeRequested(this.sessionId);

  @override
  List<Object?> get props => [sessionId];
}

/// Refresh token requested
class AuthRefreshTokenRequested extends AuthEvent {
  const AuthRefreshTokenRequested();
}

/// Clear authentication cache
class AuthClearCacheRequested extends AuthEvent {
  final bool clearAll;

  const AuthClearCacheRequested({this.clearAll = false});

  @override
  List<Object?> get props => [clearAll];
}

/// Force logout (for security reasons)
class AuthForceLogoutRequested extends AuthEvent {
  final String reason;

  const AuthForceLogoutRequested(this.reason);

  @override
  List<Object?> get props => [reason];
}
