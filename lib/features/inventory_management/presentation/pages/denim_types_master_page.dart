import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../domain/entities/denim_inventory_entities.dart';
import '../../domain/requests/denim_inventory_requests.dart';

class DenimTypeService {
  final CollectionReference _denimTypesCollection = FirebaseFirestore.instance.collection('denimTypes');

  Stream<List<DenimType>> getDenimTypes() {
    return _denimTypesCollection.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        return DenimType.fromFirestore(doc);
      }).toList();
    });
  }

  Future<void> addDenimType(CreateDenimTypeRequest request) {
    return _denimTypesCollection.add({
      'denimCode': request.denimCode,
      'type': request.type,
      'description': request.description,
      'gsm': request.gsm,
      'width': request.width,
      'supplierName': request.supplierName,
      'isActive': true,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  Future<void> updateDenimType(UpdateDenimTypeRequest request) {
    return _denimTypesCollection.doc(request.id).update({
      'denimCode': request.denimCode,
      'type': request.type,
      'description': request.description,
      'gsm': request.gsm,
      'width': request.width,
      'supplierName': request.supplierName,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  Future<void> deleteDenimType(String id) {
    return _denimTypesCollection.doc(id).delete();
  }

  Future<void> toggleActiveStatus(String id, bool isActive) {
    return _denimTypesCollection.doc(id).update({'isActive': isActive});
  }
}

class DenimTypesMasterPage extends StatefulWidget {
  const DenimTypesMasterPage({super.key});

  @override
  State<DenimTypesMasterPage> createState() => _DenimTypesMasterPageState();
}

class _DenimTypesMasterPageState extends State<DenimTypesMasterPage> {
  final _searchController = TextEditingController();
  final _denimTypeService = DenimTypeService();
  bool _showInactiveTypes = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _showAddEditDialog({DenimType? denimType}) async {
    await showDialog(
      context: context,
      builder: (context) => _DenimTypeDialog(denimType: denimType),
    );
  }

  Future<void> _toggleActiveStatus(DenimType denimType) async {
    await _denimTypeService.toggleActiveStatus(denimType.id, !denimType.isActive);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            denimType.isActive
                ? 'Denim type deactivated successfully'
                : 'Denim type activated successfully',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _deleteDenimType(DenimType denimType) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Denim Type'),
        content: Text(
          'Are you sure you want to delete "${denimType.denimCode} - ${denimType.type}"?\n\n'
          'This action cannot be undone and may affect existing inventory records.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _denimTypeService.deleteDenimType(denimType.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Denim type deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Denim Types Master',
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddEditDialog(),
          ),
        ],
      ),
      body: StreamBuilder<List<DenimType>>(
        stream: _denimTypeService.getDenimTypes(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final denimTypes = snapshot.data ?? [];
          final filteredTypes = denimTypes.where((type) {
            if (!_showInactiveTypes && !type.isActive) {
              return false;
            }
            final searchQuery = _searchController.text.toLowerCase();
            if (searchQuery.isNotEmpty) {
              return type.denimCode.toLowerCase().contains(searchQuery) ||
                  type.type.toLowerCase().contains(searchQuery) ||
                  type.description.toLowerCase().contains(searchQuery) ||
                  (type.supplierName?.toLowerCase().contains(searchQuery) ?? false);
            }
            return true;
          }).toList();

          return SingleChildScrollView(
            child: Column(
              children: [
                _buildSearchAndFilters(),
                _buildSummaryCards(filteredTypes),
                _buildDenimTypesList(filteredTypes),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          CustomTextField(
            controller: _searchController,
            label: 'Search by code, type, description, or supplier',
            prefixIcon: const Icon(Icons.search),
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Show Inactive Types'),
                  value: _showInactiveTypes,
                  onChanged: (value) => setState(() => _showInactiveTypes = value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                    _showInactiveTypes = false;
                  });
                },
                icon: const Icon(Icons.clear),
                label: const Text('Clear'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(List<DenimType> types) {
    final activeTypes = types.where((t) => t.isActive).length;
    final inactiveTypes = types.where((t) => !t.isActive).length;
    final totalSuppliers = types.map((t) => t.supplierId).where((s) => s != null).toSet().length;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'Total Types',
              types.length.toString(),
              Icons.category,
              AppColors.primary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Active',
              activeTypes.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Inactive',
              inactiveTypes.toString(),
              Icons.cancel,
              Colors.red,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Suppliers',
              totalSuppliers.toString(),
              Icons.business,
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDenimTypesList(List<DenimType> types) {
    if (types.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.category_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No denim types found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Add a new denim type to get started',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: types.length,
      itemBuilder: (context, index) {
        final denimType = types[index];
        return _buildDenimTypeCard(denimType);
      },
    );
  }

  Widget _buildDenimTypeCard(DenimType denimType) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            denimType.denimCode,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: denimType.isActive
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: denimType.isActive
                                    ? Colors.green.withOpacity(0.3)
                                    : Colors.red.withOpacity(0.3),
                              ),
                            ),
                            child: Text(
                              denimType.isActive ? 'Active' : 'Inactive',
                              style: TextStyle(
                                color: denimType.isActive ? Colors.green : Colors.red,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        denimType.type,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        denimType.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _showAddEditDialog(denimType: denimType);
                        break;
                      case 'toggle':
                        _toggleActiveStatus(denimType);
                        break;
                      case 'delete':
                        _deleteDenimType(denimType);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 16),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'toggle',
                      child: Row(
                        children: [
                          Icon(
                            denimType.isActive ? Icons.visibility_off : Icons.visibility,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(denimType.isActive ? 'Deactivate' : 'Activate'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSpecItem(
                    Icons.fitness_center,
                    'GSM',
                    denimType.gsm.toString(),
                  ),
                ),
                Expanded(
                  child: _buildSpecItem(
                    Icons.straighten,
                    'Width',
                    '${denimType.width}"',
                  ),
                ),
                Expanded(
                  child: _buildSpecItem(
                    Icons.business,
                    'Supplier',
                    denimType.supplierName ?? 'N/A',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// Dialog for adding/editing denim types
class _DenimTypeDialog extends StatefulWidget {
  final DenimType? denimType;

  const _DenimTypeDialog({this.denimType});

  @override
  State<_DenimTypeDialog> createState() => _DenimTypeDialogState();
}

class _DenimTypeDialogState extends State<_DenimTypeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _denimCodeController = TextEditingController();
  final _typeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _gsmController = TextEditingController();
  final _widthController = TextEditingController();
  final _supplierNameController = TextEditingController();
  final _denimTypeService = DenimTypeService();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.denimType != null) {
      _denimCodeController.text = widget.denimType!.denimCode;
      _typeController.text = widget.denimType!.type;
      _descriptionController.text = widget.denimType!.description;
      _gsmController.text = widget.denimType!.gsm.toString();
      _widthController.text = widget.denimType!.width.toString();
      _supplierNameController.text = widget.denimType!.supplierName ?? '';
    }
  }

  @override
  void dispose() {
    _denimCodeController.dispose();
    _typeController.dispose();
    _descriptionController.dispose();
    _gsmController.dispose();
    _widthController.dispose();
    _supplierNameController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.denimType == null) {
        final request = CreateDenimTypeRequest(
          denimCode: _denimCodeController.text.trim(),
          type: _typeController.text.trim(),
          description: _descriptionController.text.trim(),
          gsm: double.parse(_gsmController.text),
          width: double.parse(_widthController.text),
          supplierName: _supplierNameController.text.trim().isEmpty
              ? null
              : _supplierNameController.text.trim(),
        );
        await _denimTypeService.addDenimType(request);
      } else {
        final request = UpdateDenimTypeRequest(
          id: widget.denimType!.id,
          denimCode: _denimCodeController.text.trim(),
          type: _typeController.text.trim(),
          description: _descriptionController.text.trim(),
          gsm: double.parse(_gsmController.text),
          width: double.parse(_widthController.text),
          supplierName: _supplierNameController.text.trim().isEmpty
              ? null
              : _supplierNameController.text.trim(),
        );
        await _denimTypeService.updateDenimType(request);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.denimType == null
                  ? 'Denim type created successfully'
                  : 'Denim type updated successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.denimType == null ? 'Add Denim Type' : 'Edit Denim Type'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTextField(
                  controller: _denimCodeController,
                  label: 'Denim Code',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter denim code';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                CustomTextField(
                  controller: _typeController,
                  label: 'Type',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter type';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                CustomTextField(
                  controller: _descriptionController,
                  label: 'Description',
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter description';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        controller: _gsmController,
                        label: 'GSM',
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter GSM';
                          }
                          final gsm = double.tryParse(value);
                          if (gsm == null || gsm <= 0) {
                            return 'Please enter valid GSM';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CustomTextField(
                        controller: _widthController,
                        label: 'Width (inches)',
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter width';
                          }
                          final width = double.tryParse(value);
                          if (width == null || width <= 0) {
                            return 'Please enter valid width';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                CustomTextField(
                  controller: _supplierNameController,
                  label: 'Supplier Name (Optional)',
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        CustomButton(
          text: widget.denimType == null ? 'Create' : 'Update',
          onPressed: _isLoading ? null : _submitForm,
          isLoading: _isLoading,
          size: ButtonSize.small,
        ),
      ],
    );
  }
}
