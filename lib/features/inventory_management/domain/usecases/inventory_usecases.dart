import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/inventory_entities.dart';
import '../entities/supplier_entities.dart';
import '../repositories/inventory_repository.dart';

/// Get inventory items use case
@injectable
class GetInventoryItemsUseCase implements UseCase<ApiListResponse<InventoryItem>, GetInventoryItemsParams> {
  final InventoryRepository _repository;

  const GetInventoryItemsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> call(GetInventoryItemsParams params) async {
    return await _repository.getInventoryItems(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get inventory item by ID use case
@injectable
class GetInventoryItemByIdUseCase implements UseCase<ApiResponse<InventoryItem>, IdParams> {
  final InventoryRepository _repository;

  const GetInventoryItemByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> call(IdParams params) async {
    return await _repository.getInventoryItemById(params.id);
  }
}

/// Create inventory item use case
@injectable
class CreateInventoryItemUseCase implements UseCase<ApiResponse<InventoryItem>, CreateInventoryItemRequest> {
  final InventoryRepository _repository;

  const CreateInventoryItemUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> call(CreateInventoryItemRequest params) async {
    return await _repository.createInventoryItem(params);
  }
}

/// Update inventory item use case
@injectable
class UpdateInventoryItemUseCase implements UseCase<ApiResponse<InventoryItem>, UpdateInventoryItemRequest> {
  final InventoryRepository _repository;

  const UpdateInventoryItemUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> call(UpdateInventoryItemRequest params) async {
    return await _repository.updateInventoryItem(params);
  }
}

/// Delete inventory item use case
@injectable
class DeleteInventoryItemUseCase implements UseCase<ApiVoidResponse, DeleteInventoryItemParams> {
  final InventoryRepository _repository;

  const DeleteInventoryItemUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(DeleteInventoryItemParams params) async {
    return await _repository.deleteInventoryItem(params.itemId, params.reason);
  }
}

/// Update stock level use case
@injectable
class UpdateStockLevelUseCase implements UseCase<ApiResponse<InventoryItem>, UpdateStockLevelParams> {
  final InventoryRepository _repository;

  const UpdateStockLevelUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> call(UpdateStockLevelParams params) async {
    return await _repository.updateStockLevel(
      params.itemId,
      params.newStock,
      params.reason,
    );
  }
}

/// Adjust stock use case
@injectable
class AdjustStockUseCase implements UseCase<ApiResponse<StockMovement>, AdjustStockRequest> {
  final InventoryRepository _repository;

  const AdjustStockUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<StockMovement>>> call(AdjustStockRequest params) async {
    return await _repository.adjustStock(params);
  }
}

/// Reserve stock use case
@injectable
class ReserveStockUseCase implements UseCase<ApiResponse<InventoryItem>, ReserveStockParams> {
  final InventoryRepository _repository;

  const ReserveStockUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> call(ReserveStockParams params) async {
    return await _repository.reserveStock(
      params.itemId,
      params.quantity,
      params.reason,
      params.orderId,
    );
  }
}

/// Release reserved stock use case
@injectable
class ReleaseReservedStockUseCase implements UseCase<ApiResponse<InventoryItem>, ReleaseReservedStockParams> {
  final InventoryRepository _repository;

  const ReleaseReservedStockUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> call(ReleaseReservedStockParams params) async {
    return await _repository.releaseReservedStock(
      params.itemId,
      params.quantity,
      params.reason,
    );
  }
}

/// Get stock movements use case
@injectable
class GetStockMovementsUseCase implements UseCase<ApiListResponse<StockMovement>, GetStockMovementsParams> {
  final InventoryRepository _repository;

  const GetStockMovementsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<StockMovement>>> call(GetStockMovementsParams params) async {
    return await _repository.getStockMovements(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get stock movements for item use case
@injectable
class GetStockMovementsForItemUseCase implements UseCase<ApiListResponse<StockMovement>, GetStockMovementsForItemParams> {
  final InventoryRepository _repository;

  const GetStockMovementsForItemUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<StockMovement>>> call(GetStockMovementsForItemParams params) async {
    return await _repository.getStockMovementsForItem(
      params.itemId,
      pagination: params.pagination,
    );
  }
}

/// Get low stock items use case
@injectable
class GetLowStockItemsUseCase implements UseCase<ApiListResponse<InventoryItem>, GetLowStockItemsParams> {
  final InventoryRepository _repository;

  const GetLowStockItemsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> call(GetLowStockItemsParams params) async {
    return await _repository.getLowStockItems(pagination: params.pagination);
  }
}

/// Get out of stock items use case
@injectable
class GetOutOfStockItemsUseCase implements UseCase<ApiListResponse<InventoryItem>, GetOutOfStockItemsParams> {
  final InventoryRepository _repository;

  const GetOutOfStockItemsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> call(GetOutOfStockItemsParams params) async {
    return await _repository.getOutOfStockItems(pagination: params.pagination);
  }
}

/// Get overstocked items use case
@injectable
class GetOverstockedItemsUseCase implements UseCase<ApiListResponse<InventoryItem>, GetOverstockedItemsParams> {
  final InventoryRepository _repository;

  const GetOverstockedItemsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> call(GetOverstockedItemsParams params) async {
    return await _repository.getOverstockedItems(pagination: params.pagination);
  }
}

/// Get expiring items use case
@injectable
class GetExpiringItemsUseCase implements UseCase<ApiListResponse<InventoryItem>, GetExpiringItemsParams> {
  final InventoryRepository _repository;

  const GetExpiringItemsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> call(GetExpiringItemsParams params) async {
    return await _repository.getExpiringItems(
      daysAhead: params.daysAhead,
      pagination: params.pagination,
    );
  }
}

/// Search inventory items use case
@injectable
class SearchInventoryItemsUseCase implements UseCase<ApiListResponse<InventoryItem>, SearchInventoryItemsParams> {
  final InventoryRepository _repository;

  const SearchInventoryItemsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> call(SearchInventoryItemsParams params) async {
    return await _repository.searchInventoryItems(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get inventory statistics use case
@injectable
class GetInventoryStatisticsUseCase implements UseCase<InventoryStatistics, GetInventoryStatisticsParams> {
  final InventoryRepository _repository;

  const GetInventoryStatisticsUseCase(this._repository);

  @override
  Future<Either<Failure, InventoryStatistics>> call(GetInventoryStatisticsParams params) async {
    return await _repository.getInventoryStatistics(
      startDate: params.startDate,
      endDate: params.endDate,
      categoryId: params.categoryId,
    );
  }
}

/// Get inventory valuation use case
@injectable
class GetInventoryValuationUseCase implements UseCase<InventoryValuation, GetInventoryValuationParams> {
  final InventoryRepository _repository;

  const GetInventoryValuationUseCase(this._repository);

  @override
  Future<Either<Failure, InventoryValuation>> call(GetInventoryValuationParams params) async {
    return await _repository.getInventoryValuation(
      asOfDate: params.asOfDate,
      categoryId: params.categoryId,
    );
  }
}

/// Generate reorder suggestions use case
@injectable
class GenerateReorderSuggestionsUseCase implements UseCase<List<ReorderSuggestion>, GenerateReorderSuggestionsParams> {
  final InventoryRepository _repository;

  const GenerateReorderSuggestionsUseCase(this._repository);

  @override
  Future<Either<Failure, List<ReorderSuggestion>>> call(GenerateReorderSuggestionsParams params) async {
    return await _repository.generateReorderSuggestions(
      categoryId: params.categoryId,
      daysAhead: params.daysAhead,
    );
  }
}

/// Create automatic reorder use case
@injectable
class CreateAutomaticReorderUseCase implements UseCase<ApiResponse<PurchaseOrder>, CreateAutomaticReorderParams> {
  final InventoryRepository _repository;

  const CreateAutomaticReorderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> call(CreateAutomaticReorderParams params) async {
    return await _repository.createAutomaticReorder(params.itemIds);
  }
}

// BOM use cases

/// Get BOMs use case
@injectable
class GetBOMsUseCase implements UseCase<ApiListResponse<BillOfMaterials>, GetBOMsParams> {
  final BOMRepository _repository;

  const GetBOMsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<BillOfMaterials>>> call(GetBOMsParams params) async {
    return await _repository.getBOMs(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get BOM by ID use case
@injectable
class GetBOMByIdUseCase implements UseCase<ApiResponse<BillOfMaterials>, IdParams> {
  final BOMRepository _repository;

  const GetBOMByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> call(IdParams params) async {
    return await _repository.getBOMById(params.id);
  }
}

/// Create BOM use case
@injectable
class CreateBOMUseCase implements UseCase<ApiResponse<BillOfMaterials>, CreateBOMRequest> {
  final BOMRepository _repository;

  const CreateBOMUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> call(CreateBOMRequest params) async {
    return await _repository.createBOM(params);
  }
}

/// Calculate material requirements use case
@injectable
class CalculateMaterialRequirementsUseCase implements UseCase<List<MaterialRequirement>, CalculateMaterialRequirementsParams> {
  final BOMRepository _repository;

  const CalculateMaterialRequirementsUseCase(this._repository);

  @override
  Future<Either<Failure, List<MaterialRequirement>>> call(CalculateMaterialRequirementsParams params) async {
    return await _repository.calculateMaterialRequirements(
      params.bomId,
      params.quantity,
    );
  }
}

/// Check material availability use case
@injectable
class CheckMaterialAvailabilityUseCase implements UseCase<MaterialAvailabilityReport, CheckMaterialAvailabilityParams> {
  final BOMRepository _repository;

  const CheckMaterialAvailabilityUseCase(this._repository);

  @override
  Future<Either<Failure, MaterialAvailabilityReport>> call(CheckMaterialAvailabilityParams params) async {
    return await _repository.checkMaterialAvailability(
      params.bomId,
      params.quantity,
    );
  }
}

// Purchase Order use cases

/// Get purchase orders use case
@injectable
class GetPurchaseOrdersUseCase implements UseCase<ApiListResponse<PurchaseOrder>, GetPurchaseOrdersParams> {
  final PurchaseOrderRepository _repository;

  const GetPurchaseOrdersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<PurchaseOrder>>> call(GetPurchaseOrdersParams params) async {
    return await _repository.getPurchaseOrders(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Create purchase order use case
@injectable
class CreatePurchaseOrderUseCase implements UseCase<ApiResponse<PurchaseOrder>, CreatePurchaseOrderRequest> {
  final PurchaseOrderRepository _repository;

  const CreatePurchaseOrderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> call(CreatePurchaseOrderRequest params) async {
    return await _repository.createPurchaseOrder(params);
  }
}

/// Approve purchase order use case
@injectable
class ApprovePurchaseOrderUseCase implements UseCase<ApiResponse<PurchaseOrder>, ApprovePurchaseOrderParams> {
  final PurchaseOrderRepository _repository;

  const ApprovePurchaseOrderUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> call(ApprovePurchaseOrderParams params) async {
    return await _repository.approvePurchaseOrder(params.poId, params.approverId);
  }
}

/// Receive purchase order items use case
@injectable
class ReceivePurchaseOrderItemsUseCase implements UseCase<ApiResponse<PurchaseOrder>, ReceivePurchaseOrderRequest> {
  final PurchaseOrderRepository _repository;

  const ReceivePurchaseOrderItemsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> call(ReceivePurchaseOrderRequest params) async {
    return await _repository.receivePurchaseOrderItems(params);
  }
}

// Supplier use cases

/// Get suppliers use case
@injectable
class GetSuppliersUseCase implements UseCase<ApiListResponse<Supplier>, GetSuppliersParams> {
  final SupplierRepository _repository;

  const GetSuppliersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<Supplier>>> call(GetSuppliersParams params) async {
    return await _repository.getSuppliers(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Create supplier use case
@injectable
class CreateSupplierUseCase implements UseCase<ApiResponse<Supplier>, CreateSupplierRequest> {
  final SupplierRepository _repository;

  const CreateSupplierUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<Supplier>>> call(CreateSupplierRequest params) async {
    return await _repository.createSupplier(params);
  }
}

/// Search suppliers use case
@injectable
class SearchSuppliersUseCase implements UseCase<ApiListResponse<Supplier>, SearchSuppliersParams> {
  final SupplierRepository _repository;

  const SearchSuppliersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<Supplier>>> call(SearchSuppliersParams params) async {
    return await _repository.searchSuppliers(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

// Parameter classes

class GetInventoryItemsParams {
  final InventoryFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetInventoryItemsParams({this.filter, this.pagination});
}

class DeleteInventoryItemParams {
  final String itemId;
  final String? reason;

  const DeleteInventoryItemParams(this.itemId, {this.reason});
}

class UpdateStockLevelParams {
  final String itemId;
  final double newStock;
  final String reason;

  const UpdateStockLevelParams(this.itemId, this.newStock, this.reason);
}

class ReserveStockParams {
  final String itemId;
  final double quantity;
  final String reason;
  final String? orderId;

  const ReserveStockParams(this.itemId, this.quantity, this.reason, {this.orderId});
}

class ReleaseReservedStockParams {
  final String itemId;
  final double quantity;
  final String reason;

  const ReleaseReservedStockParams(this.itemId, this.quantity, this.reason);
}

class GetStockMovementsParams {
  final StockMovementFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetStockMovementsParams({this.filter, this.pagination});
}

class GetStockMovementsForItemParams {
  final String itemId;
  final PaginationParams? pagination;

  const GetStockMovementsForItemParams(this.itemId, {this.pagination});
}

class GetLowStockItemsParams {
  final PaginationParams? pagination;

  const GetLowStockItemsParams({this.pagination});
}

class GetOutOfStockItemsParams {
  final PaginationParams? pagination;

  const GetOutOfStockItemsParams({this.pagination});
}

class GetOverstockedItemsParams {
  final PaginationParams? pagination;

  const GetOverstockedItemsParams({this.pagination});
}

class GetExpiringItemsParams {
  final int daysAhead;
  final PaginationParams? pagination;

  const GetExpiringItemsParams({this.daysAhead = 30, this.pagination});
}

class SearchInventoryItemsParams {
  final String query;
  final InventoryFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchInventoryItemsParams(this.query, {this.filter, this.pagination});
}

class GetInventoryStatisticsParams {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? categoryId;

  const GetInventoryStatisticsParams({this.startDate, this.endDate, this.categoryId});
}

class GetInventoryValuationParams {
  final DateTime? asOfDate;
  final String? categoryId;

  const GetInventoryValuationParams({this.asOfDate, this.categoryId});
}

class GenerateReorderSuggestionsParams {
  final String? categoryId;
  final int daysAhead;

  const GenerateReorderSuggestionsParams({this.categoryId, this.daysAhead = 30});
}

class CreateAutomaticReorderParams {
  final List<String> itemIds;

  const CreateAutomaticReorderParams(this.itemIds);
}

class GetBOMsParams {
  final BOMFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetBOMsParams({this.filter, this.pagination});
}

class CalculateMaterialRequirementsParams {
  final String bomId;
  final double quantity;

  const CalculateMaterialRequirementsParams(this.bomId, this.quantity);
}

class CheckMaterialAvailabilityParams {
  final String bomId;
  final double quantity;

  const CheckMaterialAvailabilityParams(this.bomId, this.quantity);
}

class GetPurchaseOrdersParams {
  final PurchaseOrderFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetPurchaseOrdersParams({this.filter, this.pagination});
}

class ApprovePurchaseOrderParams {
  final String poId;
  final String approverId;

  const ApprovePurchaseOrderParams(this.poId, this.approverId);
}

class GetSuppliersParams {
  final SupplierFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetSuppliersParams({this.filter, this.pagination});
}

class SearchSuppliersParams {
  final String query;
  final SupplierFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchSuppliersParams(this.query, {this.filter, this.pagination});
}
