import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../domain/entities/manufacturing_entities.dart';

/// Department card widget for displaying department information
class DepartmentCardWidget extends StatelessWidget {
  final ManufacturingDepartment department;
  final VoidCallback onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const DepartmentCardWidget({
    super.key,
    required this.department,
    required this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getDepartmentTypeColor(department.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getDepartmentTypeIcon(department.type),
                      color: _getDepartmentTypeColor(department.type),
                      size: 20,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: Row(
                          children: [
                            Icon(Icons.visibility, size: 16),
                            SizedBox(width: 8),
                            Text('View Details'),
                          ],
                        ),
                      ),
                      if (onEdit != null)
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 16),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'workers',
                        child: Row(
                          children: [
                            Icon(Icons.people, size: 16),
                            SizedBox(width: 8),
                            Text('View Workers'),
                          ],
                        ),
                      ),
                      if (onDelete != null)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 16, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Text(
                department.name,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 4),
              
              Text(
                department.description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Supervisor: ${department.supervisorName}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              
              const Spacer(),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.people,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${department.activeWorkersCount}/${department.capacity}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getStatusColor(department.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getStatusText(department.status),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: _getStatusColor(department.status),
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Capacity utilization bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Capacity',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${department.capacityUtilization.toStringAsFixed(1)}%',
                        style: AppTextStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: department.capacityUtilization / 100,
                    backgroundColor: AppColors.surface,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getCapacityColor(department.capacityUtilization),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getDepartmentTypeColor(DepartmentType type) {
    switch (type) {
      case DepartmentType.fabric:
        return Colors.blue;
      case DepartmentType.cutting:
        return Colors.orange;
      case DepartmentType.stitching:
        return Colors.green;
      case DepartmentType.quality:
        return Colors.purple;
      case DepartmentType.packing:
        return Colors.brown;
      case DepartmentType.finishing:
        return Colors.teal;
      default:
        return AppColors.primary;
    }
  }

  IconData _getDepartmentTypeIcon(DepartmentType type) {
    switch (type) {
      case DepartmentType.fabric:
        return Icons.texture;
      case DepartmentType.cutting:
        return Icons.content_cut;
      case DepartmentType.stitching:
        return Icons.construction;
      case DepartmentType.quality:
        return Icons.verified;
      case DepartmentType.packing:
        return Icons.inventory_2;
      case DepartmentType.finishing:
        return Icons.auto_fix_high;
      default:
        return Icons.business;
    }
  }

  Color _getStatusColor(DepartmentStatus status) {
    switch (status) {
      case DepartmentStatus.active:
        return AppColors.success;
      case DepartmentStatus.maintenance:
        return AppColors.warning;
      case DepartmentStatus.closed:
        return AppColors.error;
      default:
        return AppColors.textSecondary; // Fallback for any unexpected status
    }
  }

  String _getStatusText(DepartmentStatus status) {
    switch (status) {
      case DepartmentStatus.active:
        return 'ACTIVE';
      case DepartmentStatus.inactive:
        return 'INACTIVE';
      case DepartmentStatus.maintenance:
        return 'MAINTENANCE';
      case DepartmentStatus.closed:
        return 'CLOSED';
    }
  }

  Color _getCapacityColor(double utilization) {
    if (utilization >= 90) return AppColors.error;
    if (utilization >= 75) return AppColors.warning;
    if (utilization >= 50) return AppColors.success;
    return AppColors.primary;
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'view':
        onTap();
        break;
      case 'edit':
        onEdit?.call();
        break;
      case 'workers':
        // Navigate to workers view
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }
}

/// Department statistics widget
class DepartmentStatsWidget extends StatelessWidget {
  const DepartmentStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatsCard(
            'Total Departments',
            '12',
            Icons.business,
            AppColors.primary,
            '2 new this month',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatsCard(
            'Active Departments',
            '10',
            Icons.check_circle,
            AppColors.success,
            '83% operational',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatsCard(
            'Total Workers',
            '156',
            Icons.people,
            AppColors.warning,
            'Across all departments',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatsCard(
            'Avg Efficiency',
            '89.2%',
            Icons.trending_up,
            AppColors.error,
            '+3.1% vs last month',
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
