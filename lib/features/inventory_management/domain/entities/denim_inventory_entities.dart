import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../shared/enums/purchase_status_enum.dart';
import '../../../../shared/enums/return_status_enum.dart';

enum DenimRollStatus {
  inStock('In Stock'),
  sentToCutting('Sent to Cutting'),
  returned('Returned'),
  damaged('Damaged'),
  outOfStock('Out of Stock');

  const DenimRollStatus(this.displayName);
  final String displayName;

  String get value => displayName;
}

class DenimType {
  final String id;
  final String denimCode;
  final String type;
  final String description;
  final double gsm;
  final double width;
  final String? supplierId;
  final String? supplierName;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final String? createdBy;
  final String? updatedBy;

  const DenimType({
    required this.id,
    required this.denimCode,
    required this.type,
    required this.description,
    required this.gsm,
    required this.width,
    this.supplierId,
    this.supplierName,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
  });

  factory DenimType.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return DenimType(
      id: doc.id,
      denimCode: data['denimCode'] ?? '',
      type: data['type'] ?? '',
      description: data['description'] ?? '',
      gsm: (data['gsm'] as num?)?.toDouble() ?? 0.0,
      width: (data['width'] as num?)?.toDouble() ?? 0.0,
      supplierId: data['supplierId'] as String?,
      supplierName: data['supplierName'] as String?,
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'] as String?,
      updatedBy: data['updatedBy'] as String?,
    );
  }

  DenimType copyWith({
    String? id,
    String? denimCode,
    String? type,
    String? description,
    double? gsm,
    double? width,
    String? supplierId,
    String? supplierName,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return DenimType(
      id: id ?? this.id,
      denimCode: denimCode ?? this.denimCode,
      type: type ?? this.type,
      description: description ?? this.description,
      gsm: gsm ?? this.gsm,
      width: width ?? this.width,
      supplierId: supplierId ?? this.supplierId,
      supplierName: supplierName ?? this.supplierName,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'denimCode': denimCode,
      'type': type,
      'description': description,
      'gsm': gsm,
      'width': width,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'isActive': isActive,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is DenimType &&
      other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class DenimRoll {
  final String id;
  final String rollId;
  final String denimTypeId;
  final String denimCode;
  final String denimType;
  final String color;
  final double width;
  final double totalPurchasedQty;
  final double returnedQty;
  final double usedQty;
  final double balanceQty;
  final String? location;
  final DenimRollStatus status;
  final double gsm;
  final String? supplierId;
  final String? supplierName;
  final String unit;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final String? createdBy;
  final String? updatedBy;
  final Map<String, dynamic> metadata;

  DenimRoll({
    required this.id,
    required this.rollId,
    required this.denimTypeId,
    required this.denimCode,
    required this.denimType,
    required this.color,
    required this.width,
    required this.totalPurchasedQty,
    required this.returnedQty,
    required this.usedQty,
    required this.balanceQty,
    this.location,
    required this.status,
    required this.gsm,
    this.supplierId,
    this.supplierName,
    this.unit = 'yards',
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'rollId': rollId,
      'denimTypeId': denimTypeId,
      'denimCode': denimCode,
      'denimType': denimType,
      'color': color,
      'width': width,
      'totalPurchasedQty': totalPurchasedQty,
      'returnedQty': returnedQty,
      'usedQty': usedQty,
      'balanceQty': balanceQty,
      'location': location,
      'status': status.displayName,
      'gsm': gsm,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'unit': unit,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
      'deletedAt': deletedAt,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'metadata': metadata,
    };
  }
}

class DenimPurchaseRecord {
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final String? createdBy;
  final String? updatedBy;
  final int srNo;
  final DateTime purchaseDate;
  final String supplierName;
  final String? supplierId;
  final String rollId;
  final String denimTypeId;
  final String denimType;
  final String color;
  final double width;
  final double length;
  final String unit;
  final double gsm;
  final double ratePerUnit;
  final double totalCost;
  final String invoiceNo;
  final String? remarks;
  final PurchaseStatus status;
  final String? driverName;
  final String? vehicleNumber;

  const DenimPurchaseRecord({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    required this.srNo,
    required this.purchaseDate,
    required this.supplierName,
    this.supplierId,
    required this.rollId,
    required this.denimTypeId,
    required this.denimType,
    required this.color,
    required this.width,
    required this.length,
    this.unit = 'yards',
    required this.gsm,
    required this.ratePerUnit,
    required this.totalCost,
    required this.invoiceNo,
    this.remarks,
    this.status = PurchaseStatus.completed,
    this.driverName,
    this.vehicleNumber,
  });
}

class DenimReturnRecord {
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final String? createdBy;
  final String? updatedBy;
  final int srNo;
  final DateTime returnDate;
  final String rollId;
  final String supplierName;
  final String? supplierId;
  final String reasonForReturn;
  final double quantityReturned;
  final String unit;
  final String debitNoteNo;
  final String? remarks;
  final ReturnStatus status;
  final double? refundAmount;

  const DenimReturnRecord({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    required this.srNo,
    required this.returnDate,
    required this.rollId,
    required this.supplierName,
    this.supplierId,
    required this.reasonForReturn,
    required this.quantityReturned,
    this.unit = 'yards',
    required this.debitNoteNo,
    this.remarks,
    this.status = ReturnStatus.pending,
    this.refundAmount,
  });
}
