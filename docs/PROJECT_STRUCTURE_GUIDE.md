# Project Structure Guide - Cloth Manufacturing App

## Overview

This guide outlines the recommended project structure for the Cloth Manufacturing App using Flutter with Clean Architecture principles and Bloc state management.

## Table of Contents

1. [Directory Structure](#directory-structure)
2. [Feature-based Architecture](#feature-based-architecture)
3. [Core Module Organization](#core-module-organization)
4. [Shared Components](#shared-components)
5. [Asset Management](#asset-management)
6. [Configuration Files](#configuration-files)
7. [Testing Structure](#testing-structure)

---

## Directory Structure

```
hm_collection_app/
├── android/                          # Android-specific files
├── ios/                             # iOS-specific files
├── web/                             # Web-specific files
├── linux/                           # Linux-specific files
├── macos/                           # macOS-specific files
├── windows/                         # Windows-specific files
├── assets/                          # App assets
│   ├── images/
│   │   ├── icons/
│   │   ├── logos/
│   │   └── illustrations/
│   ├── fonts/
│   ├── config/
│   │   ├── dev.json
│   │   ├── staging.json
│   │   └── prod.json
│   └── translations/
│       ├── en.json
│       └── es.json
├── lib/                             # Main source code
│   ├── core/                        # Core functionality
│   │   ├── constants/
│   │   ├── errors/
│   │   ├── network/
│   │   ├── utils/
│   │   ├── widgets/
│   │   ├── theme/
│   │   └── injection/
│   ├── features/                    # Feature modules
│   │   ├── authentication/
│   │   ├── dashboard/
│   │   ├── orders/
│   │   ├── inventory/
│   │   ├── production/
│   │   ├── quality/
│   │   ├── reports/
│   │   └── settings/
│   ├── shared/                      # Shared components
│   │   ├── models/
│   │   ├── repositories/
│   │   ├── services/
│   │   └── widgets/
│   └── main.dart                    # App entry point
├── test/                            # Test files
│   ├── features/
│   ├── shared/
│   ├── core/
│   └── widget_test.dart
├── integration_test/                # Integration tests
├── docs/                           # Documentation
├── pubspec.yaml                    # Dependencies
├── analysis_options.yaml          # Linting rules
└── README.md                       # Project documentation
```

---

## Feature-based Architecture

Each feature follows the Clean Architecture pattern with three layers:

### Feature Structure Template

```
features/[feature_name]/
├── data/                           # Data Layer
│   ├── datasources/
│   │   ├── [feature]_local_datasource.dart
│   │   └── [feature]_remote_datasource.dart
│   ├── models/
│   │   ├── [model]_model.dart
│   │   └── [model]_model.g.dart
│   └── repositories/
│       └── [feature]_repository_impl.dart
├── domain/                         # Domain Layer
│   ├── entities/
│   │   └── [entity].dart
│   ├── repositories/
│   │   └── [feature]_repository.dart
│   └── usecases/
│       ├── get_[feature].dart
│       ├── create_[feature].dart
│       └── update_[feature].dart
└── presentation/                   # Presentation Layer
    ├── bloc/
    │   ├── [feature]_bloc.dart
    │   ├── [feature]_event.dart
    │   └── [feature]_state.dart
    ├── pages/
    │   ├── [feature]_page.dart
    │   └── [feature]_detail_page.dart
    └── widgets/
        ├── [feature]_card.dart
        ├── [feature]_form.dart
        └── [feature]_list.dart
```

### Example: Authentication Feature

```
features/authentication/
├── data/
│   ├── datasources/
│   │   ├── auth_local_datasource.dart
│   │   └── auth_remote_datasource.dart
│   ├── models/
│   │   ├── user_model.dart
│   │   ├── user_model.g.dart
│   │   ├── auth_tokens_model.dart
│   │   └── auth_tokens_model.g.dart
│   └── repositories/
│       └── auth_repository_impl.dart
├── domain/
│   ├── entities/
│   │   ├── user.dart
│   │   └── auth_tokens.dart
│   ├── repositories/
│   │   └── auth_repository.dart
│   └── usecases/
│       ├── login_user.dart
│       ├── logout_user.dart
│       ├── refresh_token.dart
│       └── check_auth_status.dart
└── presentation/
    ├── bloc/
    │   ├── auth_bloc.dart
    │   ├── auth_event.dart
    │   └── auth_state.dart
    ├── pages/
    │   ├── login_page.dart
    │   ├── register_page.dart
    │   └── forgot_password_page.dart
    └── widgets/
        ├── login_form.dart
        ├── register_form.dart
        └── auth_button.dart
```

### Example: Orders Feature

```
features/orders/
├── data/
│   ├── datasources/
│   │   ├── order_local_datasource.dart
│   │   └── order_remote_datasource.dart
│   ├── models/
│   │   ├── order_model.dart
│   │   ├── order_item_model.dart
│   │   └── customer_model.dart
│   └── repositories/
│       └── order_repository_impl.dart
├── domain/
│   ├── entities/
│   │   ├── order.dart
│   │   ├── order_item.dart
│   │   └── customer.dart
│   ├── repositories/
│   │   └── order_repository.dart
│   └── usecases/
│       ├── get_orders.dart
│       ├── create_order.dart
│       ├── update_order.dart
│       ├── delete_order.dart
│       └── get_order_details.dart
└── presentation/
    ├── bloc/
    │   ├── order_bloc.dart
    │   ├── order_event.dart
    │   ├── order_state.dart
    │   ├── order_form_bloc.dart
    │   └── order_detail_bloc.dart
    ├── pages/
    │   ├── orders_page.dart
    │   ├── order_detail_page.dart
    │   ├── create_order_page.dart
    │   └── edit_order_page.dart
    └── widgets/
        ├── order_card.dart
        ├── order_list.dart
        ├── order_form.dart
        ├── order_status_badge.dart
        └── customer_selector.dart
```

---

## Core Module Organization

### Core Directory Structure

```
core/
├── constants/
│   ├── app_constants.dart
│   ├── api_constants.dart
│   ├── storage_constants.dart
│   └── route_constants.dart
├── errors/
│   ├── failures.dart
│   ├── exceptions.dart
│   └── error_handler.dart
├── network/
│   ├── api_client.dart
│   ├── network_info.dart
│   ├── interceptors/
│   │   ├── auth_interceptor.dart
│   │   ├── logging_interceptor.dart
│   │   └── error_interceptor.dart
│   └── endpoints.dart
├── utils/
│   ├── date_utils.dart
│   ├── validation_utils.dart
│   ├── format_utils.dart
│   ├── permission_utils.dart
│   └── device_utils.dart
├── widgets/
│   ├── custom_app_bar.dart
│   ├── custom_button.dart
│   ├── custom_text_field.dart
│   ├── loading_widget.dart
│   ├── error_widget.dart
│   ├── empty_state_widget.dart
│   └── role_based_widget.dart
├── theme/
│   ├── app_theme.dart
│   ├── app_colors.dart
│   ├── app_text_styles.dart
│   └── app_dimensions.dart
└── injection/
    ├── injection_container.dart
    └── injection_container.config.dart
```

### Key Core Files

#### app_constants.dart
```dart
class AppConstants {
  static const String appName = 'HM Collection';
  static const String appVersion = '1.0.0';
  static const int pageSize = 20;
  static const int timeoutDuration = 30; // seconds
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'MMM dd, yyyy';
}
```

#### api_constants.dart
```dart
class ApiConstants {
  static const String baseUrl = 'https://api.hmcollection.com';
  static const String apiVersion = '/v1';
  
  // Auth Endpoints
  static const String login = '/auth/login';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh';
  
  // Order Endpoints
  static const String orders = '/orders';
  static const String createOrder = '/orders';
  
  // Production Endpoints
  static const String production = '/production';
  static const String tasks = '/production/tasks';
  
  // Inventory Endpoints
  static const String inventory = '/inventory';
  static const String materials = '/inventory/materials';
}
```

#### failures.dart
```dart
abstract class Failure extends Equatable {
  final String message;
  
  const Failure(this.message);
  
  @override
  List<Object> get props => [message];
}

class ServerFailure extends Failure {
  const ServerFailure(String message) : super(message);
}

class CacheFailure extends Failure {
  const CacheFailure(String message) : super(message);
}

class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message);
}

class AuthFailure extends Failure {
  const AuthFailure(String message) : super(message);
}

class ValidationFailure extends Failure {
  const ValidationFailure(String message) : super(message);
}
```

---

## Shared Components

### Shared Directory Structure

```
shared/
├── models/
│   ├── api_response.dart
│   ├── pagination.dart
│   ├── filter_criteria.dart
│   └── sort_criteria.dart
├── repositories/
│   ├── base_repository.dart
│   └── cache_repository.dart
├── services/
│   ├── notification_service.dart
│   ├── storage_service.dart
│   ├── permission_service.dart
│   ├── biometric_service.dart
│   └── analytics_service.dart
└── widgets/
    ├── custom_scaffold.dart
    ├── search_bar.dart
    ├── filter_bottom_sheet.dart
    ├── sort_bottom_sheet.dart
    ├── pagination_widget.dart
    └── refresh_indicator_widget.dart
```

### Key Shared Components

#### api_response.dart
```dart
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;
  final Pagination? pagination;
  
  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.pagination,
  });
  
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      data: json['data'] != null ? fromJsonT(json['data']) : null,
      message: json['message'],
      error: json['error'],
      pagination: json['pagination'] != null
          ? Pagination.fromJson(json['pagination'])
          : null,
    );
  }
}
```

#### base_repository.dart
```dart
abstract class BaseRepository {
  final NetworkInfo networkInfo;
  final StorageService storageService;
  
  BaseRepository({
    required this.networkInfo,
    required this.storageService,
  });
  
  Future<Either<Failure, T>> handleApiCall<T>(
    Future<T> Function() apiCall,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final result = await apiCall();
        return Right(result);
      } else {
        return Left(NetworkFailure('No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }
}
```

---

## Asset Management

### Assets Directory Structure

```
assets/
├── images/
│   ├── icons/
│   │   ├── app_icon.png
│   │   ├── department_icons/
│   │   │   ├── cutting.svg
│   │   │   ├── sewing.svg
│   │   │   ├── quality.svg
│   │   │   └── finishing.svg
│   │   └── status_icons/
│   │       ├── pending.svg
│   │       ├── in_progress.svg
│   │       ├── completed.svg
│   │       └── cancelled.svg
│   ├── logos/
│   │   ├── app_logo.png
│   │   ├── company_logo.png
│   │   └── splash_logo.png
│   └── illustrations/
│       ├── empty_orders.svg
│       ├── no_internet.svg
│       └── error_illustration.svg
├── fonts/
│   ├── Roboto-Regular.ttf
│   ├── Roboto-Medium.ttf
│   └── Roboto-Bold.ttf
├── config/
│   ├── dev.json
│   ├── staging.json
│   └── prod.json
└── translations/
    ├── en.json
    ├── es.json
    └── hi.json
```

### pubspec.yaml Asset Configuration

```yaml
flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/images/icons/
    - assets/images/icons/department_icons/
    - assets/images/icons/status_icons/
    - assets/images/logos/
    - assets/images/illustrations/
    - assets/config/
    - assets/translations/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
```

---

## Configuration Files

### Environment Configuration

#### dev.json
```json
{
  "apiBaseUrl": "https://dev-api.hmcollection.com",
  "enableLogging": true,
  "enableAnalytics": false,
  "cacheTimeout": 300,
  "features": {
    "biometricAuth": true,
    "offlineMode": true,
    "realTimeUpdates": true
  }
}
```

#### staging.json
```json
{
  "apiBaseUrl": "https://staging-api.hmcollection.com",
  "enableLogging": true,
  "enableAnalytics": true,
  "cacheTimeout": 600,
  "features": {
    "biometricAuth": true,
    "offlineMode": true,
    "realTimeUpdates": true
  }
}
```

#### prod.json
```json
{
  "apiBaseUrl": "https://api.hmcollection.com",
  "enableLogging": false,
  "enableAnalytics": true,
  "cacheTimeout": 3600,
  "features": {
    "biometricAuth": true,
    "offlineMode": true,
    "realTimeUpdates": true
  }
}
```

---

## Testing Structure

### Test Directory Organization

```
test/
├── features/
│   ├── authentication/
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   ├── models/
│   │   │   └── repositories/
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   └── usecases/
│   │   └── presentation/
│   │       ├── bloc/
│   │       └── widgets/
│   ├── orders/
│   └── production/
├── shared/
│   ├── models/
│   ├── repositories/
│   ├── services/
│   └── widgets/
├── core/
│   ├── network/
│   ├── utils/
│   └── widgets/
├── fixtures/
│   ├── auth_fixtures.dart
│   ├── order_fixtures.dart
│   └── production_fixtures.dart
├── helpers/
│   ├── test_helper.dart
│   ├── mock_helper.dart
│   └── pump_app.dart
└── widget_test.dart
```

### Test Helper Example

```dart
// test/helpers/pump_app.dart
class TestApp extends StatelessWidget {
  final Widget child;
  final List<BlocProvider> providers;
  
  const TestApp({
    Key? key,
    required this.child,
    this.providers = const [],
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: providers,
      child: MaterialApp(
        home: child,
      ),
    );
  }
}

extension WidgetTesterExtension on WidgetTester {
  Future<void> pumpApp(
    Widget widget, {
    List<BlocProvider> providers = const [],
  }) {
    return pumpWidget(
      TestApp(
        child: widget,
        providers: providers,
      ),
    );
  }
}
```

---

*This project structure guide provides a comprehensive foundation for organizing the Cloth Manufacturing App codebase. Follow this structure for maintainable, scalable, and testable code.*
