import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../auth/domain/entities/user.dart';
import '../entities/user_management.dart';

/// User management repository interface
abstract class UserManagementRepository {
  /// Get all users with filtering and pagination
  Future<Either<Failure, ApiListResponse<User>>> getUsers({
    UserFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get user by ID
  Future<Either<Failure, ApiResponse<User>>> getUserById(String userId);

  /// Create new user
  Future<Either<Failure, ApiResponse<User>>> createUser(
    CreateUserRequest request,
  );

  /// Update user
  Future<Either<Failure, ApiResponse<User>>> updateUser(
    UpdateUserRequest request,
  );

  /// Delete user (soft delete)
  Future<Either<Failure, ApiVoidResponse>> deleteUser(
    String userId,
    String? reason,
  );

  /// Activate user
  Future<Either<Failure, ApiVoidResponse>> activateUser(String userId);

  /// Deactivate user
  Future<Either<Failure, ApiVoidResponse>> deactivateUser(
    String userId,
    String? reason,
  );

  /// Assign role to user
  Future<Either<Failure, ApiVoidResponse>> assignRole(
    RoleAssignmentRequest request,
  );

  /// Grant permissions to user
  Future<Either<Failure, ApiVoidResponse>> grantPermissions(
    PermissionAssignmentRequest request,
  );

  /// Revoke permissions from user
  Future<Either<Failure, ApiVoidResponse>> revokePermissions(
    PermissionAssignmentRequest request,
  );

  /// Get user permissions
  Future<Either<Failure, List<String>>> getUserPermissions(String userId);

  /// Get user activity logs
  Future<Either<Failure, ApiListResponse<UserActivityLog>>> getUserActivityLogs(
    String userId, {
    PaginationParams? pagination,
  });

  /// Get user statistics
  Future<Either<Failure, UserStatistics>> getUserStatistics();

  /// Search users
  Future<Either<Failure, ApiListResponse<User>>> searchUsers({
    required String query,
    UserFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get users by role
  Future<Either<Failure, ApiListResponse<User>>> getUsersByRole(
    UserRole role, {
    PaginationParams? pagination,
  });

  /// Get users by department
  Future<Either<Failure, ApiListResponse<User>>> getUsersByDepartment(
    Department department, {
    PaginationParams? pagination,
  });

  /// Reset user password
  Future<Either<Failure, ApiVoidResponse>> resetUserPassword(
    String userId,
    String newPassword,
  );

  /// Force password change
  Future<Either<Failure, ApiVoidResponse>> forcePasswordChange(String userId);

  /// Lock user account
  Future<Either<Failure, ApiVoidResponse>> lockUser(
    String userId,
    String reason,
  );

  /// Unlock user account
  Future<Either<Failure, ApiVoidResponse>> unlockUser(String userId);

  /// Get available roles
  Future<Either<Failure, List<RoleDefinition>>> getAvailableRoles();

  /// Get available permissions
  Future<Either<Failure, List<Permission>>> getAvailablePermissions();

  /// Get permissions by category
  Future<Either<Failure, Map<String, List<Permission>>>> getPermissionsByCategory();

  /// Bulk user operations
  Future<Either<Failure, ApiVoidResponse>> bulkUpdateUsers(
    List<UpdateUserRequest> requests,
  );

  /// Bulk delete users
  Future<Either<Failure, ApiVoidResponse>> bulkDeleteUsers(
    List<String> userIds,
    String? reason,
  );

  /// Export users
  Future<Either<Failure, String>> exportUsers({
    UserFilterCriteria? filter,
    String format = 'csv', // csv, excel, pdf
  });

  /// Import users
  Future<Either<Failure, ApiResponse<Map<String, dynamic>>>> importUsers(
    String filePath, {
    bool validateOnly = false,
  });
}

/// Role management repository interface
abstract class RoleManagementRepository {
  /// Get all role definitions
  Future<Either<Failure, List<RoleDefinition>>> getAllRoles();

  /// Get role definition by role
  Future<Either<Failure, RoleDefinition?>> getRoleDefinition(UserRole role);

  /// Create custom role
  Future<Either<Failure, RoleDefinition>> createCustomRole(
    RoleDefinition roleDefinition,
  );

  /// Update role definition
  Future<Either<Failure, RoleDefinition>> updateRoleDefinition(
    RoleDefinition roleDefinition,
  );

  /// Delete custom role
  Future<Either<Failure, ApiVoidResponse>> deleteCustomRole(UserRole role);

  /// Get role hierarchy
  Future<Either<Failure, Map<UserRole, List<UserRole>>>> getRoleHierarchy();

  /// Check if role can manage another role
  Future<Either<Failure, bool>> canRoleManage(
    UserRole managerRole,
    UserRole targetRole,
  );
}

/// Permission management repository interface
abstract class PermissionManagementRepository {
  /// Get all permissions
  Future<Either<Failure, List<Permission>>> getAllPermissions();

  /// Get permissions by category
  Future<Either<Failure, Map<String, List<Permission>>>> getPermissionsByCategory();

  /// Create custom permission
  Future<Either<Failure, Permission>> createCustomPermission(
    Permission permission,
  );

  /// Update permission
  Future<Either<Failure, Permission>> updatePermission(
    Permission permission,
  );

  /// Delete custom permission
  Future<Either<Failure, ApiVoidResponse>> deleteCustomPermission(
    String permissionName,
  );

  /// Check permission dependencies
  Future<Either<Failure, List<String>>> getPermissionDependencies(
    String permissionName,
  );

  /// Validate permission assignment
  Future<Either<Failure, bool>> validatePermissionAssignment(
    UserRole role,
    List<String> permissions,
  );
}

/// Audit repository interface
abstract class UserAuditRepository {
  /// Log user activity
  Future<Either<Failure, void>> logActivity(UserActivityLog activity);

  /// Get activity logs
  Future<Either<Failure, ApiListResponse<UserActivityLog>>> getActivityLogs({
    String? userId,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    PaginationParams? pagination,
  });

  /// Get user login history
  Future<Either<Failure, ApiListResponse<UserActivityLog>>> getLoginHistory(
    String userId, {
    PaginationParams? pagination,
  });

  /// Get system activity summary
  Future<Either<Failure, Map<String, dynamic>>> getActivitySummary({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Export activity logs
  Future<Either<Failure, String>> exportActivityLogs({
    String? userId,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    String format = 'csv',
  });
}
