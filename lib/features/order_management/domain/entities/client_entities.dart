import 'package:equatable/equatable.dart';
import 'package:hm_collection/features/order_management/domain/entities/order_entities.dart' as order_entities;

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Client entity
class Client extends BaseEntity {
  final String clientCode;
  final String companyName;
  final String? displayName;
  final ClientType type;
  final CommonStatus status;
  final ClientContact primaryContact;
  final List<ClientContact> contacts;
  final ClientAddress billingAddress;
  final ClientAddress? shippingAddress;
  final ClientBusinessInfo businessInfo;
  final ClientPreferences preferences;
  final List<String> tags;
  final Map<String, dynamic> metadata;
  final double creditLimit;
  final double currentBalance;
  final ClientRating rating;
  final String? assignedMerchandiser;

  const Client({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.clientCode,
    required this.companyName,
    this.displayName,
    required this.type,
    required this.status,
    required this.primaryContact,
    this.contacts = const [],
    required this.billingAddress,
    this.shippingAddress,
    required this.businessInfo,
    required this.preferences,
    this.tags = const [],
    this.metadata = const {},
    this.creditLimit = 0.0,
    this.currentBalance = 0.0,
    required this.rating,
    this.assignedMerchandiser,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        clientCode,
        companyName,
        displayName,
        type,
        status,
        primaryContact,
        contacts,
        billingAddress,
        shippingAddress,
        businessInfo,
        preferences,
        tags,
        metadata,
        creditLimit,
        currentBalance,
        rating,
        assignedMerchandiser,
      ];

  /// Get effective display name
  String get effectiveDisplayName => displayName ?? companyName;

  /// Check if client is active
  bool get isActive => status == CommonStatus.active;

  /// Get available credit
  double get availableCredit => creditLimit - currentBalance;

  /// Check if client has credit available
  bool get hasCreditAvailable => availableCredit > 0;

  /// Get all contact emails
  List<String> get allEmails {
    final emails = <String>[primaryContact.email];
    emails.addAll(contacts.map((c) => c.email));
    return emails.where((email) => email.isNotEmpty).toList();
  }

  /// Get all contact phones
  List<String> get allPhones {
    final phones = <String>[primaryContact.phone];
    phones.addAll(contacts.map((c) => c.phone));
    return phones.where((phone) => phone.isNotEmpty).toList();
  }
}

/// Client contact information
class ClientContact extends Equatable {
  final String name;
  final String title;
  final String email;
  final String phone;
  final String? mobile;
  final bool isPrimary;
  final List<String> roles;

  const ClientContact({
    required this.name,
    required this.title,
    required this.email,
    required this.phone,
    this.mobile,
    this.isPrimary = false,
    this.roles = const [],
  });

  @override
  List<Object?> get props => [
        name,
        title,
        email,
        phone,
        mobile,
        isPrimary,
        roles,
      ];
}

/// Client address information
class ClientAddress extends Equatable {
  final String street;
  final String city;
  final String state;
  final String country;
  final String postalCode;
  final String? landmark;
  final double? latitude;
  final double? longitude;

  const ClientAddress({
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    this.landmark,
    this.latitude,
    this.longitude,
  });

  @override
  List<Object?> get props => [
        street,
        city,
        state,
        country,
        postalCode,
        landmark,
        latitude,
        longitude,
      ];

  /// Get formatted address
  String get formattedAddress {
    final parts = <String>[
      street,
      city,
      state,
      country,
      postalCode,
    ];
    return parts.where((part) => part.isNotEmpty).join(', ');
  }
}

/// Client business information
class ClientBusinessInfo extends Equatable {
  final String? taxId;
  final String? registrationNumber;
  final String industry;
  final String? website;
  final int? employeeCount;
  final double? annualRevenue;
  final DateTime? establishedDate;
  final List<String> certifications;

  const ClientBusinessInfo({
    this.taxId,
    this.registrationNumber,
    required this.industry,
    this.website,
    this.employeeCount,
    this.annualRevenue,
    this.establishedDate,
    this.certifications = const [],
  });

  @override
  List<Object?> get props => [
        taxId,
        registrationNumber,
        industry,
        website,
        employeeCount,
        annualRevenue,
        establishedDate,
        certifications,
      ];
}

/// Client preferences
class ClientPreferences extends Equatable {
  final String preferredCurrency;
  final String preferredLanguage;
  final List<String> preferredPaymentMethods;
  final int paymentTerms; // days
  final String? preferredShippingMethod;
  final bool allowPartialShipments;
  final bool requireQualityReports;
  final Map<String, dynamic> customPreferences;

  const ClientPreferences({
    required this.preferredCurrency,
    required this.preferredLanguage,
    this.preferredPaymentMethods = const [],
    this.paymentTerms = 30,
    this.preferredShippingMethod,
    this.allowPartialShipments = true,
    this.requireQualityReports = false,
    this.customPreferences = const {},
  });

  @override
  List<Object?> get props => [
        preferredCurrency,
        preferredLanguage,
        preferredPaymentMethods,
        paymentTerms,
        preferredShippingMethod,
        allowPartialShipments,
        requireQualityReports,
        customPreferences,
      ];
}

/// Client rating information
class ClientRating extends Equatable {
  final double overallRating;
  final double paymentRating;
  final double communicationRating;
  final double volumeRating;
  final int totalOrders;
  final double totalValue;
  final DateTime? lastOrderDate;

  const ClientRating({
    required this.overallRating,
    required this.paymentRating,
    required this.communicationRating,
    required this.volumeRating,
    this.totalOrders = 0,
    this.totalValue = 0.0,
    this.lastOrderDate,
  });

  @override
  List<Object?> get props => [
        overallRating,
        paymentRating,
        communicationRating,
        volumeRating,
        totalOrders,
        totalValue,
        lastOrderDate,
      ];

  /// Get rating category
  ClientRatingCategory get category {
    if (overallRating >= 4.5) return ClientRatingCategory.excellent;
    if (overallRating >= 4.0) return ClientRatingCategory.good;
    if (overallRating >= 3.0) return ClientRatingCategory.average;
    if (overallRating >= 2.0) return ClientRatingCategory.poor;
    return ClientRatingCategory.bad;
  }
}

/// Client type enum
enum ClientType {
  individual,
  smallBusiness,
  corporation,
  retailer,
  wholesaler,
  manufacturer,
  distributor,
}

/// Client type extension
extension ClientTypeExtension on ClientType {
  String get displayName {
    switch (this) {
      case ClientType.individual:
        return 'Individual';
      case ClientType.smallBusiness:
        return 'Small Business';
      case ClientType.corporation:
        return 'Corporation';
      case ClientType.retailer:
        return 'Retailer';
      case ClientType.wholesaler:
        return 'Wholesaler';
      case ClientType.manufacturer:
        return 'Manufacturer';
      case ClientType.distributor:
        return 'Distributor';
    }
  }

  String get value {
    return name;
  }
}

/// Client rating category enum
enum ClientRatingCategory {
  excellent,
  good,
  average,
  poor,
  bad,
}

/// Client rating category extension
extension ClientRatingCategoryExtension on ClientRatingCategory {
  String get displayName {
    switch (this) {
      case ClientRatingCategory.excellent:
        return 'Excellent';
      case ClientRatingCategory.good:
        return 'Good';
      case ClientRatingCategory.average:
        return 'Average';
      case ClientRatingCategory.poor:
        return 'Poor';
      case ClientRatingCategory.bad:
        return 'Bad';
    }
  }

  String get value {
    return name;
  }
}

/// Order filter criteria
class OrderFilterCriteria extends Equatable {
  final String? clientId;
  final OrderStatus? status;
  final OrderPriority? priority;
  final OrderType? type;
  final Department? department;
  final String? assignedMerchandiser;
  final DateTime? orderDateFrom;
  final DateTime? orderDateTo;
  final DateTime? requiredDateFrom;
  final DateTime? requiredDateTo;
  final double? minValue;
  final double? maxValue;
  final bool? isOverdue;
  final bool? isUrgent;
  final List<String>? tags;

  const OrderFilterCriteria({
    this.clientId,
    this.status,
    this.priority,
    this.type,
    this.department,
    this.assignedMerchandiser,
    this.orderDateFrom,
    this.orderDateTo,
    this.requiredDateFrom,
    this.requiredDateTo,
    this.minValue,
    this.maxValue,
    this.isOverdue,
    this.isUrgent,
    this.tags,
  });

  @override
  List<Object?> get props => [
        clientId,
        status,
        priority,
        type,
        department,
        assignedMerchandiser,
        orderDateFrom,
        orderDateTo,
        requiredDateFrom,
        requiredDateTo,
        minValue,
        maxValue,
        isOverdue,
        isUrgent,
        tags,
      ];

  /// Convert to query parameters
  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (clientId != null) params['client_id'] = clientId;
    if (status != null) params['status'] = status!.value;
    if (priority != null) params['priority'] = priority!.value;
    if (type != null) params['type'] = type!.value;
    if (department != null) params['department'] = department!.value;
    if (assignedMerchandiser != null) params['assigned_merchandiser'] = assignedMerchandiser;
    if (orderDateFrom != null) params['order_date_from'] = orderDateFrom!.toIso8601String();
    if (orderDateTo != null) params['order_date_to'] = orderDateTo!.toIso8601String();
    if (requiredDateFrom != null) params['required_date_from'] = requiredDateFrom!.toIso8601String();
    if (requiredDateTo != null) params['required_date_to'] = requiredDateTo!.toIso8601String();
    if (minValue != null) params['min_value'] = minValue;
    if (maxValue != null) params['max_value'] = maxValue;
    if (isOverdue != null) params['is_overdue'] = isOverdue;
    if (isUrgent != null) params['is_urgent'] = isUrgent;
    if (tags != null && tags!.isNotEmpty) params['tags'] = tags!.join(',');
    
    return params;
  }
}

/// Update order request
class UpdateOrderRequest extends Equatable {
  final String orderId;
  final String? orderNumber;
  final OrderType? type;
  final OrderPriority? priority;
  final DateTime? requiredDate;
  final List<order_entities.OrderItem>? items;
  final String? notes;
  final Map<String, dynamic>? metadata;

  const UpdateOrderRequest({
    required this.orderId,
    this.orderNumber,
    this.type,
    this.priority,
    this.requiredDate,
    this.items,
    this.notes,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        orderId,
        orderNumber,
        type,
        priority,
        requiredDate,
        items,
        notes,
        metadata,
      ];
}

/// Client order item
class ClientOrderItem extends Equatable {
  final String id;
  final String productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final String? specifications;

  const ClientOrderItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    this.specifications,
  });

  @override
  List<Object?> get props => [
        id,
        productId,
        productName,
        quantity,
        unitPrice,
        specifications,
      ];
}

/// Order statistics
class OrderStatistics extends Equatable {
  final int totalOrders;
  final int completedOrders;
  final int inProgressOrders;
  final int overdueOrders;
  final double totalValue;
  final double averageOrderValue;
  final double onTimeDeliveryRate;

  const OrderStatistics({
    required this.totalOrders,
    required this.completedOrders,
    required this.inProgressOrders,
    required this.overdueOrders,
    required this.totalValue,
    required this.averageOrderValue,
    required this.onTimeDeliveryRate,
  });

  @override
  List<Object?> get props => [
        totalOrders,
        completedOrders,
        inProgressOrders,
        overdueOrders,
        totalValue,
        averageOrderValue,
        onTimeDeliveryRate,
      ];
}

/// Order timeline event
class OrderTimelineEvent extends Equatable {
  final String id;
  final String orderId;
  final String eventType;
  final String description;
  final DateTime timestamp;
  final String? userId;
  final String? userName;

  const OrderTimelineEvent({
    required this.id,
    required this.orderId,
    required this.eventType,
    required this.description,
    required this.timestamp,
    this.userId,
    this.userName,
  });

  factory OrderTimelineEvent.fromJson(Map<String, dynamic> json) {
    return OrderTimelineEvent(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      eventType: json['eventType'] as String,
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['userId'] as String?,
      userName: json['userName'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        orderId,
        eventType,
        description,
        timestamp,
        userId,
        userName,
      ];
}

/// Order production schedule
class OrderProductionSchedule extends Equatable {
  final String id;
  final String orderId;
  final Department department;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final TaskStatus status;

  const OrderProductionSchedule({
    required this.id,
    required this.orderId,
    required this.department,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    required this.status,
  });

  @override
  List<Object?> get props => [
        id,
        orderId,
        department,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        status,
      ];
}
