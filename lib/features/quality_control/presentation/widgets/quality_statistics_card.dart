import 'package:flutter/material.dart';

/// A card widget that displays quality control statistics
class QualityStatisticsCard extends StatelessWidget {
  final QualityStatistics statistics;
  final VoidCallback? onTap;

  const QualityStatisticsCard({
    Key? key,
    required this.statistics,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Quality Statistics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildStatisticRow(
                context,
                'Total Inspections',
                statistics.totalInspections.toString(),
                Icons.verified,
                Colors.blue,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Passed',
                statistics.passedInspections.toString(),
                Icons.check_circle,
                Colors.green,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Failed',
                statistics.failedInspections.toString(),
                Icons.error,
                Colors.red,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Average Score',
                '${statistics.averageScore.toStringAsFixed(1)}%',
                Icons.trending_up,
                Colors.purple,
              ),
              const SizedBox(height: 16),
              _buildQualityIndicator(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQualityIndicator(BuildContext context) {
    final passRate = statistics.totalInspections > 0
        ? statistics.passedInspections / statistics.totalInspections
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Pass Rate',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${(passRate * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: passRate,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            passRate >= 0.9 ? Colors.green : passRate >= 0.7 ? Colors.orange : Colors.red,
          ),
        ),
      ],
    );
  }
}

/// A grid of quality statistics cards
class QualityStatisticsGrid extends StatelessWidget {
  final QualityStatistics statistics;

  const QualityStatisticsGrid({
    Key? key,
    required this.statistics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildStatCard(
          context,
          'Total Inspections',
          statistics.totalInspections.toString(),
          Icons.verified,
          Colors.blue,
        ),
        _buildStatCard(
          context,
          'Passed',
          statistics.passedInspections.toString(),
          Icons.check_circle,
          Colors.green,
        ),
        _buildStatCard(
          context,
          'Failed',
          statistics.failedInspections.toString(),
          Icons.error,
          Colors.red,
        ),
        _buildStatCard(
          context,
          'Avg Score',
          '${statistics.averageScore.toStringAsFixed(1)}%',
          Icons.trending_up,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Quality trend chart card
class QualityTrendCard extends StatelessWidget {
  final List<QualityTrendData> trendData;
  final String title;

  const QualityTrendCard({
    Key? key,
    required this.trendData,
    this.title = 'Quality Trend',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: _buildTrendChart(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendChart(BuildContext context) {
    if (trendData.isEmpty) {
      return Center(
        child: Text(
          'No trend data available',
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: trendData.length,
      itemBuilder: (context, index) {
        final data = trendData[index];
        final maxScore = trendData.map((d) => d.score).reduce((a, b) => a > b ? a : b);
        final height = (data.score / maxScore) * 150;

        return Container(
          width: 40,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                data.score.toStringAsFixed(0),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 4),
              Container(
                width: 30,
                height: height,
                decoration: BoxDecoration(
                  color: _getScoreColor(data.score),
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${data.date.day}/${data.date.month}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.orange;
    return Colors.red;
  }
}

/// Quality KPI cards
class QualityKPICards extends StatelessWidget {
  final List<QualityKPI> kpis;

  const QualityKPICards({
    Key? key,
    required this.kpis,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: kpis.length,
        itemBuilder: (context, index) {
          final kpi = kpis[index];
          return Container(
            width: 160,
            margin: EdgeInsets.only(
              left: index == 0 ? 16 : 8,
              right: index == kpis.length - 1 ? 16 : 8,
            ),
            child: Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      kpi.value,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: kpi.color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      kpi.title,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (kpi.trend != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            kpi.trend! > 0 ? Icons.trending_up : Icons.trending_down,
                            size: 16,
                            color: kpi.trend! > 0 ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${kpi.trend!.abs().toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 12,
                              color: kpi.trend! > 0 ? Colors.green : Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Placeholder classes - these should be defined in quality_entities.dart
class QualityStatistics {
  final int totalInspections;
  final int passedInspections;
  final int failedInspections;
  final double averageScore;
  final double firstPassYield;
  final int criticalDefects;
  final int majorDefects;
  final int minorDefects;
  final Map<String, int> inspectionsByStage;

  const QualityStatistics({
    required this.totalInspections,
    required this.passedInspections,
    required this.failedInspections,
    required this.averageScore,
    required this.firstPassYield,
    this.criticalDefects = 0,
    this.majorDefects = 0,
    this.minorDefects = 0,
    this.inspectionsByStage = const {}, // Initialize with empty map
  });

  double get passRate => totalInspections > 0 
      ? (passedInspections / totalInspections) * 100 
      : 0.0;
      
  double get defectRate => totalInspections > 0
      ? (failedInspections / totalInspections) * 100
      : 0.0;
      
  int get totalDefects => criticalDefects + majorDefects + minorDefects;
}

class QualityTrendData {
  final DateTime date;
  final double score;

  const QualityTrendData({
    required this.date,
    required this.score,
  });
}

class QualityKPI {
  final String title;
  final String value;
  final Color color;
  final double? trend;

  const QualityKPI({
    required this.title,
    required this.value,
    required this.color,
    this.trend,
  });
}