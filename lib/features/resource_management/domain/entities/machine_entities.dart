import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Machine entity
class Machine extends BaseEntity {
  final String machineCode;
  final String machineName;
  final String description;
  final MachineType type;
  final MachineCategory category;
  final String manufacturer;
  final String model;
  final String serialNumber;
  final DateTime purchaseDate;
  final DateTime? warrantyExpiry;
  final MachineStatus status;
  final String departmentId;
  final String departmentName;
  final String? locationId;
  final String? locationName;
  final MachineSpecifications specifications;
  final MachineCapacity capacity;
  final List<String> operatorIds;
  final List<String> skillsRequired;
  final double utilizationRate;
  final double efficiency;
  final DateTime? lastMaintenanceDate;
  final DateTime? nextMaintenanceDate;
  final int totalOperatingHours;
  final List<String> attachments;
  final Map<String, dynamic> metadata;

  const Machine({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.machineCode,
    required this.machineName,
    required this.description,
    required this.type,
    required this.category,
    required this.manufacturer,
    required this.model,
    required this.serialNumber,
    required this.purchaseDate,
    this.warrantyExpiry,
    required this.status,
    required this.departmentId,
    required this.departmentName,
    this.locationId,
    this.locationName,
    required this.specifications,
    required this.capacity,
    this.operatorIds = const [],
    this.skillsRequired = const [],
    this.utilizationRate = 0.0,
    this.efficiency = 0.0,
    this.lastMaintenanceDate,
    this.nextMaintenanceDate,
    this.totalOperatingHours = 0,
    this.attachments = const [],
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        machineCode,
        machineName,
        description,
        type,
        category,
        manufacturer,
        model,
        serialNumber,
        purchaseDate,
        warrantyExpiry,
        status,
        departmentId,
        departmentName,
        locationId,
        locationName,
        specifications,
        capacity,
        operatorIds,
        skillsRequired,
        utilizationRate,
        efficiency,
        lastMaintenanceDate,
        nextMaintenanceDate,
        totalOperatingHours,
        attachments,
        metadata,
      ];

  /// Check if machine is available
  bool get isAvailable => status == MachineStatus.available;

  /// Check if machine is in use
  bool get isInUse => status == MachineStatus.inUse;

  /// Check if machine is under maintenance
  bool get isUnderMaintenance => status == MachineStatus.maintenance;

  /// Check if machine is out of order
  bool get isOutOfOrder => status == MachineStatus.outOfOrder;

  /// Check if maintenance is due
  bool get isMaintenanceDue {
    if (nextMaintenanceDate == null) return false;
    return DateTime.now().isAfter(nextMaintenanceDate!);
  }

  /// Check if maintenance is due soon
  bool get isMaintenanceDueSoon {
    if (nextMaintenanceDate == null) return false;
    final daysUntilMaintenance = nextMaintenanceDate!.difference(DateTime.now()).inDays;
    return daysUntilMaintenance <= 7 && daysUntilMaintenance > 0;
  }

  /// Check if warranty is expired
  bool get isWarrantyExpired {
    if (warrantyExpiry == null) return false;
    return DateTime.now().isAfter(warrantyExpiry!);
  }

  /// Get machine age in years
  double get ageInYears {
    final now = DateTime.now();
    final ageInDays = now.difference(purchaseDate).inDays;
    return ageInDays / 365.25;
  }

  /// Get days until next maintenance
  int? get daysUntilMaintenance {
    if (nextMaintenanceDate == null) return null;
    return nextMaintenanceDate!.difference(DateTime.now()).inDays;
  }
}

/// Machine specifications entity
class MachineSpecifications extends Equatable {
  final double? power; // in kW
  final double? voltage; // in V
  final double? weight; // in kg
  final double? length; // in cm
  final double? width; // in cm
  final double? height; // in cm
  final String? powerSource;
  final String? controlSystem;
  final Map<String, dynamic> technicalSpecs;

  const MachineSpecifications({
    this.power,
    this.voltage,
    this.weight,
    this.length,
    this.width,
    this.height,
    this.powerSource,
    this.controlSystem,
    this.technicalSpecs = const {},
  });

  @override
  List<Object?> get props => [
        power,
        voltage,
        weight,
        length,
        width,
        height,
        powerSource,
        controlSystem,
        technicalSpecs,
      ];
}

/// Machine capacity entity
class MachineCapacity extends Equatable {
  final double maxProductionRate; // units per hour
  final double optimalProductionRate;
  final int maxOperatingHours; // per day
  final double maxLoad; // in kg or units
  final String unit;
  final Map<String, double> capacityByProduct;

  const MachineCapacity({
    required this.maxProductionRate,
    required this.optimalProductionRate,
    this.maxOperatingHours = 24,
    required this.maxLoad,
    required this.unit,
    this.capacityByProduct = const {},
  });

  @override
  List<Object?> get props => [
        maxProductionRate,
        optimalProductionRate,
        maxOperatingHours,
        maxLoad,
        unit,
        capacityByProduct,
      ];
}

/// Machine schedule entity
class MachineSchedule extends BaseEntity {
  final String machineId;
  final String machineCode;
  final String machineName;
  final DateTime startTime;
  final DateTime endTime;
  final ScheduleType type;
  final ScheduleStatus status;
  final String? orderId;
  final String? orderNumber;
  final String? productionOrderId;
  final String? taskId;
  final String? operatorId;
  final String? operatorName;
  final String? notes;
  final double? plannedOutput;
  final double? actualOutput;
  final String? outputUnit;

  const MachineSchedule({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.machineId,
    required this.machineCode,
    required this.machineName,
    required this.startTime,
    required this.endTime,
    required this.type,
    required this.status,
    this.orderId,
    this.orderNumber,
    this.productionOrderId,
    this.taskId,
    this.operatorId,
    this.operatorName,
    this.notes,
    this.plannedOutput,
    this.actualOutput,
    this.outputUnit,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        machineId,
        machineCode,
        machineName,
        startTime,
        endTime,
        type,
        status,
        orderId,
        orderNumber,
        productionOrderId,
        taskId,
        operatorId,
        operatorName,
        notes,
        plannedOutput,
        actualOutput,
        outputUnit,
      ];

  /// Get schedule duration
  Duration get duration => endTime.difference(startTime);

  /// Get schedule duration in hours
  double get durationInHours => duration.inMinutes / 60.0;

  /// Check if schedule is active
  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startTime) && now.isBefore(endTime);
  }

  /// Check if schedule is upcoming
  bool get isUpcoming => DateTime.now().isBefore(startTime);

  /// Check if schedule is completed
  bool get isCompleted => status == ScheduleStatus.completed;

  /// Get efficiency percentage
  double? get efficiency {
    if (plannedOutput == null || actualOutput == null || plannedOutput == 0) {
      return null;
    }
    return (actualOutput! / plannedOutput!) * 100;
  }
}

/// Machine maintenance entity
class MaintenanceRecord extends BaseEntity {
  final String machineId;
  final String machineCode;
  final String machineName;
  final MaintenanceType type;
  final MaintenanceStatus status;
  final MaintenancePriority priority;
  final DateTime scheduledDate;
  final DateTime? startDate;
  final DateTime? completedDate;
  final String title;
  final String description;
  final List<MaintenanceTask> tasks;
  final String? assignedTo;
  final String? assignedToName;
  final double estimatedCost;
  final double actualCost;
  final int estimatedDuration; // in hours
  final int? actualDuration;
  final List<String> partsUsed;
  final List<String> attachments;
  final String? notes;
  final String? completionNotes;

  const MaintenanceRecord({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.machineId,
    required this.machineCode,
    required this.machineName,
    required this.type,
    required this.status,
    required this.priority,
    required this.scheduledDate,
    this.startDate,
    this.completedDate,
    required this.title,
    required this.description,
    this.tasks = const [],
    this.assignedTo,
    this.assignedToName,
    this.estimatedCost = 0.0,
    this.actualCost = 0.0,
    this.estimatedDuration = 0,
    this.actualDuration,
    this.partsUsed = const [],
    this.attachments = const [],
    this.notes,
    this.completionNotes,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        machineId,
        machineCode,
        machineName,
        type,
        status,
        priority,
        scheduledDate,
        startDate,
        completedDate,
        title,
        description,
        tasks,
        assignedTo,
        assignedToName,
        estimatedCost,
        actualCost,
        estimatedDuration,
        actualDuration,
        partsUsed,
        attachments,
        notes,
        completionNotes,
      ];

  /// Check if maintenance is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(scheduledDate) && !isCompleted;
  }

  /// Check if maintenance is completed
  bool get isCompleted => status == MaintenanceStatus.completed;

  /// Check if maintenance is in progress
  bool get isInProgress => status == MaintenanceStatus.inProgress;

  /// Get maintenance duration
  Duration? get maintenanceDuration {
    if (startDate == null || completedDate == null) return null;
    return completedDate!.difference(startDate!);
  }

  /// Get cost variance
  double get costVariance => actualCost - estimatedCost;

  /// Get duration variance in hours
  int? get durationVariance {
    if (actualDuration == null) return null;
    return actualDuration! - estimatedDuration;
  }
}

/// Maintenance task entity
class MaintenanceTask extends Equatable {
  final String taskId;
  final String taskName;
  final String description;
  final TaskStatus status;
  final int estimatedDuration; // in minutes
  final int? actualDuration;
  final String? assignedTo;
  final String? notes;
  final List<String> attachments;

  const MaintenanceTask({
    required this.taskId,
    required this.taskName,
    required this.description,
    required this.status,
    this.estimatedDuration = 0,
    this.actualDuration,
    this.assignedTo,
    this.notes,
    this.attachments = const [],
  });

  @override
  List<Object?> get props => [
        taskId,
        taskName,
        description,
        status,
        estimatedDuration,
        actualDuration,
        assignedTo,
        notes,
        attachments,
      ];

  /// Check if task is completed
  bool get isCompleted => status == TaskStatus.completed;

  /// Get duration variance in minutes
  int? get durationVariance {
    if (actualDuration == null) return null;
    return actualDuration! - estimatedDuration;
  }
}

// Enums

/// Machine type enum
enum MachineType {
  cutting,
  sewing,
  embroidery,
  pressing,
  finishing,
  packaging,
  inspection,
  utility,
}

/// Machine type extension
extension MachineTypeExtension on MachineType {
  String get displayName {
    switch (this) {
      case MachineType.cutting:
        return 'Cutting Machine';
      case MachineType.sewing:
        return 'Sewing Machine';
      case MachineType.embroidery:
        return 'Embroidery Machine';
      case MachineType.pressing:
        return 'Pressing Machine';
      case MachineType.finishing:
        return 'Finishing Machine';
      case MachineType.packaging:
        return 'Packaging Machine';
      case MachineType.inspection:
        return 'Inspection Equipment';
      case MachineType.utility:
        return 'Utility Equipment';
    }
  }

  String get value => name;
}

/// Machine category enum
enum MachineCategory {
  production,
  support,
  quality,
  maintenance,
}

/// Machine status enum
enum MachineStatus {
  available,
  inUse,
  maintenance,
  outOfOrder,
  retired,
}

/// Machine status extension
extension MachineStatusExtension on MachineStatus {
  String get displayName {
    switch (this) {
      case MachineStatus.available:
        return 'Available';
      case MachineStatus.inUse:
        return 'In Use';
      case MachineStatus.maintenance:
        return 'Under Maintenance';
      case MachineStatus.outOfOrder:
        return 'Out of Order';
      case MachineStatus.retired:
        return 'Retired';
    }
  }

  String get value => name;

  bool get isOperational => this == MachineStatus.available || this == MachineStatus.inUse;
}

/// Schedule type enum
enum ScheduleType {
  production,
  maintenance,
  setup,
  cleaning,
  training,
}

/// Schedule status enum
enum ScheduleStatus {
  scheduled,
  inProgress,
  completed,
  cancelled,
  postponed,
}

/// Maintenance type enum
enum MaintenanceType {
  preventive,
  corrective,
  predictive,
  emergency,
  overhaul,
}

/// Maintenance type extension
extension MaintenanceTypeExtension on MaintenanceType {
  String get displayName {
    switch (this) {
      case MaintenanceType.preventive:
        return 'Preventive';
      case MaintenanceType.corrective:
        return 'Corrective';
      case MaintenanceType.predictive:
        return 'Predictive';
      case MaintenanceType.emergency:
        return 'Emergency';
      case MaintenanceType.overhaul:
        return 'Overhaul';
    }
  }

  String get value => name;
}

/// Maintenance status enum
enum MaintenanceStatus {
  scheduled,
  inProgress,
  completed,
  cancelled,
  postponed,
}

/// Maintenance priority enum
enum MaintenancePriority {
  low,
  medium,
  high,
  critical,
}

/// Maintenance priority extension
extension MaintenancePriorityExtension on MaintenancePriority {
  String get displayName {
    switch (this) {
      case MaintenancePriority.low:
        return 'Low';
      case MaintenancePriority.medium:
        return 'Medium';
      case MaintenancePriority.high:
        return 'High';
      case MaintenancePriority.critical:
        return 'Critical';
    }
  }

  String get value => name;

  int get priority {
    switch (this) {
      case MaintenancePriority.critical:
        return 4;
      case MaintenancePriority.high:
        return 3;
      case MaintenancePriority.medium:
        return 2;
      case MaintenancePriority.low:
        return 1;
    }
  }
}

/// Task status enum
enum TaskStatus {
  pending,
  inProgress,
  completed,
  cancelled,
}

// Request classes

class CreateMachineRequest extends Equatable {
  final String machineCode;
  final String machineName;
  final String description;
  final MachineType type;
  final MachineCategory category;
  final String manufacturer;
  final String model;
  final String serialNumber;
  final DateTime purchaseDate;
  final String departmentId;

  const CreateMachineRequest({
    required this.machineCode,
    required this.machineName,
    required this.description,
    required this.type,
    required this.category,
    required this.manufacturer,
    required this.model,
    required this.serialNumber,
    required this.purchaseDate,
    required this.departmentId,
  });

  @override
  List<Object?> get props => [
        machineCode,
        machineName,
        description,
        type,
        category,
        manufacturer,
        model,
        serialNumber,
        purchaseDate,
        departmentId,
      ];
}

class UpdateMachineRequest extends Equatable {
  final String id;
  final String? machineCode;
  final String? machineName;
  final String? description;
  final MachineType? type;
  final MachineCategory? category;
  final String? manufacturer;
  final String? model;
  final String? serialNumber;
  final DateTime? purchaseDate;
  final DateTime? warrantyExpiry;
  final MachineStatus? status;
  final String? departmentId;

  const UpdateMachineRequest({
    required this.id,
    this.machineCode,
    this.machineName,
    this.description,
    this.type,
    this.category,
    this.manufacturer,
    this.model,
    this.serialNumber,
    this.purchaseDate,
    this.warrantyExpiry,
    this.status,
    this.departmentId,
  });

  @override
  List<Object?> get props => [
        id,
        machineCode,
        machineName,
        description,
        type,
        category,
        manufacturer,
        model,
        serialNumber,
        purchaseDate,
        warrantyExpiry,
        status,
        departmentId,
      ];
}

class CreateMachineScheduleRequest extends Equatable {
  final String machineId;
  final DateTime startTime;
  final DateTime endTime;
  final ScheduleType type;

  const CreateMachineScheduleRequest({
    required this.machineId,
    required this.startTime,
    required this.endTime,
    required this.type,
  });

  @override
  List<Object?> get props => [machineId, startTime, endTime, type];
}

class CreateMaintenanceRecordRequest extends Equatable {
  final String machineId;
  final MaintenanceType type;
  final MaintenancePriority priority;
  final DateTime scheduledDate;
  final String title;
  final String description;

  const CreateMaintenanceRecordRequest({
    required this.machineId,
    required this.type,
    required this.priority,
    required this.scheduledDate,
    required this.title,
    required this.description,
  });

  @override
  List<Object?> get props =>
      [machineId, type, priority, scheduledDate, title, description];
}

// Report classes

class MachineUtilizationReport extends Equatable {
  final String machineId;
  final String machineName;
  final DateTime startDate;
  final DateTime endDate;
  final double totalHours;
  final double operatingHours;
  final double idleHours;
  final double maintenanceHours;
  final double utilizationPercentage;

  const MachineUtilizationReport({
    required this.machineId,
    required this.machineName,
    required this.startDate,
    required this.endDate,
    required this.totalHours,
    required this.operatingHours,
    required this.idleHours,
    required this.maintenanceHours,
    required this.utilizationPercentage,
  });

  @override
  List<Object?> get props => [
        machineId,
        machineName,
        startDate,
        endDate,
        totalHours,
        operatingHours,
        idleHours,
        maintenanceHours,
        utilizationPercentage,
      ];
}
