import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';  // Temporarily commented out
import 'package:firebase_remote_config/firebase_remote_config.dart';
// import 'package:firebase_storage/firebase_storage.dart';  // Temporarily commented out
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../firebase_options.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:injectable/injectable.dart';

/// Firebase configuration and initialization
@singleton
class FirebaseConfig {
  static FirebaseConfig? _instance;

  FirebaseConfig._();

  static FirebaseConfig get instance {
    _instance ??= FirebaseConfig._();
    return _instance!;
  }

  /// Initialize Firebase
  static Future<void> initialize() async {
    try {
      final options = _getFirebaseOptions();
      if (options != null) {
        await Firebase.initializeApp(
          options: options,
        );

        // Configure Firebase services with better error handling
        // Defer Firestore configuration to run shortly after first frame
        Future.delayed(const Duration(milliseconds: 50), () async {
          try {
            await _configureFirestore();
          } catch (e) {
            print('Firestore configuration failed: $e');
          }
        });

        // Defer Auth configuration to reduce startup blocking
        Future.microtask(() async {
          try {
            await _configureFirebaseAuth();
          } catch (e) {
            print('Auth configuration failed: $e');
          }
        });

        // Defer Crashlytics configuration to reduce startup jank
        Future.microtask(() async {
          try {
            await _configureFirebaseCrashlytics();
          } catch (e) {
            print('Crashlytics configuration failed: $e');
          }
        });

        // Defer Analytics configuration
        Future.delayed(const Duration(milliseconds: 200), () async {
          try {
            await _configureFirebaseAnalytics();
          } catch (e) {
            print('Analytics configuration failed: $e');
          }
        });

        // Defer Remote Config configuration
        Future.delayed(const Duration(milliseconds: 400), () async {
          try {
            await _configureRemoteConfig();
          } catch (e) {
            print('Remote Config configuration failed: $e');
          }
        });

        print('Firebase initialized successfully');
      } else {
        print(
            'Firebase options not available, skipping Firebase initialization');
      }
    } catch (e) {
      print('Firebase initialization failed: $e');
      // Don't rethrow to allow app to continue without Firebase
      print('Continuing app initialization without Firebase...');
    }
  }

  /// Get Firebase options for different platforms
  static FirebaseOptions? _getFirebaseOptions() {
    try {
      return DefaultFirebaseOptions.currentPlatform;
    } catch (e) {
      print('Error getting Firebase options: $e');
      // Return null to skip Firebase initialization if options are not available
      return null;
    }
  }

  /// Configure Firebase Authentication
  static Future<void> _configureFirebaseAuth() async {
    final auth = FirebaseAuth.instance;

    // Disable all security features for development
    if (kDebugMode) {
      try {
        // Configure auth settings first
        await auth.setSettings(
          appVerificationDisabledForTesting: true,  // Disable reCAPTCHA
          forceRecaptchaFlow: false,  // Disable reCAPTCHA flow
          userAccessGroup: null,
        );
        
        // Set default language to prevent null locale
        await auth.setLanguageCode('en');
        
        // Configure Firestore settings
        FirebaseFirestore.instance.settings = const Settings(
          persistenceEnabled: true,
          cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
        );
        
        debugPrint('Firebase security features disabled for development');
      } catch (e) {
        debugPrint('Error configuring Firebase Auth: $e');
      }
    }

    // Set language code from device locale, with a fallback to 'en'
    try {
      final locale = WidgetsBinding.instance.platformDispatcher.locale;
      final languageCode = locale.languageCode;
      
      // Use 'en' as a fallback if the language code is empty, null, or 'und'
      final codeToSet = (languageCode.isEmpty || languageCode == 'und') ? 'en' : languageCode;
      
      await auth.setLanguageCode(codeToSet);
      
    } catch (e) {
      // If there's any error, default to 'en'
      await auth.setLanguageCode('en');
      debugPrint('Error setting language code, defaulted to en: $e');
    }

    print('Firebase Auth configured');
  }

  /// Configure Firestore
  static Future<void> _configureFirestore() async {
    final firestore = FirebaseFirestore.instance;

    // Configure Firestore settings
    firestore.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );

    // Enable offline persistence
    if (!kIsWeb) {
      try {
        await firestore.enablePersistence();
      } catch (e) {
        print('Firestore persistence error: $e');
      }
    }

    print('Firestore configured');
  }

  /// Configure Firebase Messaging
  // static Future<void> _configureFirebaseMessaging() async {
  //   final messaging = FirebaseMessaging.instance;
  //
  //   // Request permission for notifications
  //   final settings = await messaging.requestPermission(
  //     alert: true,
  //     announcement: false,
  //     badge: true,
  //     carPlay: false,
  //     criticalAlert: false,
  //     provisional: false,
  //     sound: true,
  //   );
  //
  //   print('Firebase Messaging permission: ${settings.authorizationStatus}');
  //
  //   // Configure foreground message handling
  //   // FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  //   //   print('Received foreground message: ${message.messageId}');
  //   //   // Handle foreground messages
  //   // });  // Temporarily commented out
  //
  //   // Configure background message handling
  //   // FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);  // Temporarily commented out
  //
  //   // Configure message opened app handling
  //   // FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
  //   //   print('Message opened app: ${message.messageId}');
  //   //   // Handle message opened app
  //   // });  // Temporarily commented out
  //
  //   print('Firebase Messaging configured');
  // }  // Temporarily commented out

  /// Configure Firebase Crashlytics
  static Future<void> _configureFirebaseCrashlytics() async {
    final crashlytics = FirebaseCrashlytics.instance;

    // Enable crashlytics collection in release mode
    await crashlytics.setCrashlyticsCollectionEnabled(!kDebugMode);

    // Set user identifier for crash reports
    if (kDebugMode) {
      await crashlytics.setUserIdentifier('debug-user');
    }

    // Pass all uncaught errors to Crashlytics
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };

    // Pass all uncaught asynchronous errors to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };

    print('Firebase Crashlytics configured');
  }

  /// Configure Firebase Analytics
  static Future<void> _configureFirebaseAnalytics() async {
    final analytics = FirebaseAnalytics.instance;

    // Enable analytics collection
    await analytics.setAnalyticsCollectionEnabled(!kDebugMode);

    // Set default event parameters
    await analytics.setDefaultEventParameters({
      'app_version': '1.0.0',
      'platform': defaultTargetPlatform.name,
    });

    print('Firebase Analytics configured');
  }

  /// Configure Remote Config
  static Future<void> _configureRemoteConfig() async {
    final remoteConfig = FirebaseRemoteConfig.instance;

    // Set config settings
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval:
          kDebugMode ? const Duration(minutes: 1) : const Duration(hours: 1),
    ));

    // Set default parameters
    await remoteConfig.setDefaults({
      'maintenance_mode': false,
      'min_app_version': '1.0.0',
      'feature_flags': '{}',
      'api_base_url': 'https://api.example.com',
    });

    try {
      // Fetch and activate config
      await remoteConfig.fetchAndActivate();
      print('Remote Config fetched and activated');
    } catch (e) {
      print('Remote Config fetch failed: $e');
    }

    print('Firebase Remote Config configured');
  }

  /// Get Firebase Auth instance
  // FirebaseAuth get auth => FirebaseAuth.instance;  // Temporarily commented out

  /// Get Firestore instance
  FirebaseFirestore get firestore => FirebaseFirestore.instance;

  /// Get Firebase Messaging instance
  // FirebaseMessaging get messaging => FirebaseMessaging.instance;  // Temporarily commented out

  /// Get Firebase Storage instance
  // FirebaseStorage get storage => FirebaseStorage.instance;  // Temporarily commented out

  /// Get Firebase Analytics instance
  FirebaseAnalytics get analytics => FirebaseAnalytics.instance;

  /// Get Firebase Crashlytics instance
  FirebaseCrashlytics get crashlytics => FirebaseCrashlytics.instance;

  /// Get Remote Config instance
  FirebaseRemoteConfig get remoteConfig => FirebaseRemoteConfig.instance;

  /// Check if Firebase is initialized
  bool get isInitialized => Firebase.apps.isNotEmpty;

  /// Check Google Play Services availability (Android only)
  static Future<bool> checkGooglePlayServices() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        // On Android, we'll assume Google Play Services is available
        // since we're using Firebase which requires it
        return true;
      }
      return true;
    } catch (e) {
      print('Google Play Services check failed: $e');
      return false;
    }
  }

  /// Get current user
  // User? get currentUser => auth.currentUser;  // Temporarily commented out

  /// Check if user is signed in
  // bool get isSignedIn => currentUser != null;  // Temporarily commented out

  /// Sign out current user
  // Future<void> signOut() async {
  //   await auth.signOut();
  // }  // Temporarily commented out

  /// Delete current user
  // Future<void> deleteUser() async {
  //   final user = currentUser;
  //   if (user != null) {
  //     await user.delete();
  //   }
  // }  // Temporarily commented out

  /// Send crash report
  Future<void> recordError(
    dynamic exception,
    StackTrace? stack, {
    String? reason,
    bool fatal = false,
  }) async {
    await crashlytics.recordError(
      exception,
      stack,
      reason: reason,
      fatal: fatal,
    );
  }

  /// Log custom event
  Future<void> logEvent(
    String name, {
    Map<String, Object?>? parameters,
  }) async {
    final nonNullParams = parameters
        ?.map((key, value) => MapEntry(key, value ?? ''))
      ?..removeWhere((_, value) => value == null);

    await analytics.logEvent(
      name: name,
      parameters: nonNullParams,
    );
  }

  /// Set user properties
  Future<void> setUserProperties({
    String? userId,
    Map<String, String?>? properties,
  }) async {
    if (userId != null) {
      await analytics.setUserId(id: userId);
      await crashlytics.setUserIdentifier(userId);
    }

    if (properties != null) {
      for (final entry in properties.entries) {
        await analytics.setUserProperty(
          name: entry.key,
          value: entry.value,
        );
      }
    }
  }

  /// Get remote config value
  T getRemoteConfigValue<T>(String key, T defaultValue) {
    try {
      final value = remoteConfig.getValue(key);

      if (T == bool) {
        return value.asBool() as T;
      } else if (T == int) {
        return value.asInt() as T;
      } else if (T == double) {
        return value.asDouble() as T;
      } else if (T == String) {
        return value.asString() as T;
      } else {
        return defaultValue;
      }
    } catch (e) {
      print('Error getting remote config value for $key: $e');
      return defaultValue;
    }
  }

  /// Check if app is in maintenance mode
  bool get isMaintenanceMode => getRemoteConfigValue('maintenance_mode', false);

  /// Get minimum app version
  String get minAppVersion => getRemoteConfigValue('min_app_version', '1.0.0');

  /// Get API base URL
  String get apiBaseUrl =>
      getRemoteConfigValue('api_base_url', 'https://api.example.com');

  /// Get feature flags
  Map<String, dynamic> get featureFlags {
    try {
      final flagsJson = getRemoteConfigValue('feature_flags', '{}');
      return Map<String, dynamic>.from(
        // You would use a JSON decoder here
        <String, dynamic>{},
      );
    } catch (e) {
      print('Error parsing feature flags: $e');
      return <String, dynamic>{};
    }
  }

  /// Check if feature is enabled
  bool isFeatureEnabled(String featureName) {
    return featureFlags[featureName] == true;
  }
}

/// Background message handler
// @pragma('vm:entry-point')
// Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   await Firebase.initializeApp();
//   print('Handling background message: ${message.messageId}');
//   // Handle background messages
// }  // Temporarily commented out

/// Firebase service locator registration
@module
abstract class FirebaseModule {
  // @singleton
  // FirebaseAuth get firebaseAuth => FirebaseAuth.instance;  // Temporarily commented out

  @singleton
  FirebaseFirestore get firebaseFirestore => FirebaseFirestore.instance;

  // @singleton
  // FirebaseMessaging get firebaseMessaging => FirebaseMessaging.instance;  // Temporarily commented out

  // @singleton
  // FirebaseStorage get firebaseStorage => FirebaseStorage.instance;  // Temporarily commented out

  @singleton
  FirebaseAnalytics get firebaseAnalytics => FirebaseAnalytics.instance;

  @singleton
  FirebaseCrashlytics get firebaseCrashlytics => FirebaseCrashlytics.instance;

  @singleton
  FirebaseRemoteConfig get firebaseRemoteConfig =>
      FirebaseRemoteConfig.instance;
}
