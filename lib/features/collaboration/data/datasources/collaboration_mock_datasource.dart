import 'dart:async';
import 'dart:math';

import 'package:injectable/injectable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/collaboration_entities.dart';
import '../../domain/repositories/collaboration_repository.dart';
import 'collaboration_datasource.dart';

/// Mock collaboration data source implementation
@LazySingleton(as: CollaborationDataSource)
class CollaborationMockDataSource implements CollaborationDataSource {
  // Mock data
  final List<CollaborationWorkspace> _workspaces = _generateMockWorkspaces();
  final List<CollaborationChannel> _channels = _generateMockChannels();
  final List<CollaborationMessage> _messages = _generateMockMessages();
  final List<CollaborationSession> _sessions = _generateMockSessions();
  final List<RealTimeActivity> _activities = _generateMockActivities();
  final List<MessageAttachment> _attachments = _generateMockAttachments();
  final Random _random = Random();

  // Real-time streams
  final Map<String, StreamController<CollaborationMessage>> _messageStreams = {};
  final Map<String, StreamController<RealTimeActivity>> _activityStreams = {};
  final Map<String, StreamController<List<WorkspaceMember>>> _memberStreams = {};

  @override
  Future<ApiListResponse<CollaborationWorkspace>> getWorkspaces({
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredWorkspaces = _workspaces.where((workspace) {
      if (filter?.type != null && workspace.type != filter!.type) return false;
      if (filter?.ownerId != null && workspace.ownerId != filter!.ownerId) return false;
      if (filter?.isPublic != null && workspace.isPublic != filter!.isPublic) return false;
      if (filter?.isArchived != null && workspace.isArchived != filter!.isArchived) return false;
      if (filter?.tags != null && filter!.tags!.isNotEmpty) {
        final hasMatchingTag = filter.tags!.any((tag) => workspace.tags.contains(tag));
        if (!hasMatchingTag) return false;
      }
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedWorkspaces = filteredWorkspaces.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      success: true,
      data: paginatedWorkspaces,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        perPage: pagination?.perPage ?? 20,
        total: filteredWorkspaces.length,
        totalPages: (filteredWorkspaces.length / (pagination?.perPage ?? 20)).ceil(),
        hasNextPage: (pagination?.page ?? 0) < ((filteredWorkspaces.length / (pagination?.perPage ?? 20)).ceil() - 1),
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ),
    );
  }

  @override
  Future<ApiResponse<CollaborationWorkspace>> getWorkspaceById(String workspaceId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final workspace = _workspaces.firstWhere(
      (w) => w.id == workspaceId,
      orElse: () => throw Exception('Workspace not found'),
    );

    return ApiResponse(data: workspace, success: true);
  }

  @override
  Future<ApiResponse<CollaborationWorkspace>> createWorkspace(CreateWorkspaceRequest request) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final newWorkspace = CollaborationWorkspace(
      id: 'workspace_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      workspaceCode: 'WS-${DateTime.now().millisecondsSinceEpoch}',
      workspaceName: request.workspaceName,
      description: request.description,
      type: request.type,
      status: CommonStatus.active,
      ownerId: 'current_user', // Mock
      ownerName: 'Current User', // Mock
      members: [
        WorkspaceMember(
          userId: 'current_user',
          userName: 'Current User',
          userEmail: '<EMAIL>',
          role: WorkspaceRole.owner,
          joinedAt: DateTime.now(),
          isActive: true,
          isOnline: true,
        ),
      ],
      settings: request.settings,
      tags: request.tags,
      isPublic: request.isPublic,
    );

    _workspaces.add(newWorkspace);
    _broadcastActivity(newWorkspace.id, ActivityType.workspaceUpdated, 'Workspace created');

    return ApiResponse(data: newWorkspace, success: true);
  }

  @override
  Future<ApiResponse<CollaborationWorkspace>> updateWorkspace(UpdateWorkspaceRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final index = _workspaces.indexWhere((w) => w.id == request.workspaceId);
    if (index == -1) throw Exception('Workspace not found');

    final existingWorkspace = _workspaces[index];
    final updatedWorkspace = CollaborationWorkspace(
      id: existingWorkspace.id,
      createdAt: existingWorkspace.createdAt,
      updatedAt: DateTime.now(),
      workspaceCode: existingWorkspace.workspaceCode,
      workspaceName: request.workspaceName ?? existingWorkspace.workspaceName,
      description: request.description ?? existingWorkspace.description,
      type: existingWorkspace.type,
      status: existingWorkspace.status,
      ownerId: existingWorkspace.ownerId,
      ownerName: existingWorkspace.ownerName,
      members: existingWorkspace.members,
      settings: request.settings ?? existingWorkspace.settings,
      tags: request.tags ?? existingWorkspace.tags,
      lastActivityAt: DateTime.now(),
      isPublic: request.isPublic ?? existingWorkspace.isPublic,
      isArchived: existingWorkspace.isArchived,
    );

    _workspaces[index] = updatedWorkspace;
    _broadcastActivity(updatedWorkspace.id, ActivityType.workspaceUpdated, 'Workspace updated');

    return ApiResponse(data: updatedWorkspace, success: true);
  }

  @override
  Future<ApiVoidResponse> deleteWorkspace(String workspaceId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    _workspaces.removeWhere((w) => w.id == workspaceId);
    _channels.removeWhere((c) => c.workspaceId == workspaceId);
    _messages.removeWhere((m) => m.workspaceId == workspaceId);

    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiResponse<CollaborationWorkspace>> archiveWorkspace(String workspaceId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _workspaces.indexWhere((w) => w.id == workspaceId);
    if (index == -1) throw Exception('Workspace not found');

    final workspace = _workspaces[index];
    final archivedWorkspace = CollaborationWorkspace(
      id: workspace.id,
      createdAt: workspace.createdAt,
      updatedAt: DateTime.now(),
      workspaceCode: workspace.workspaceCode,
      workspaceName: workspace.workspaceName,
      description: workspace.description,
      type: workspace.type,
      status: workspace.status,
      ownerId: workspace.ownerId,
      ownerName: workspace.ownerName,
      members: workspace.members,
      settings: workspace.settings,
      tags: workspace.tags,
      lastActivityAt: DateTime.now(),
      isPublic: workspace.isPublic,
      isArchived: true,
    );

    _workspaces[index] = archivedWorkspace;
    return ApiResponse(data: archivedWorkspace, success: true);
  }

  @override
  Future<ApiResponse<CollaborationWorkspace>> restoreWorkspace(String workspaceId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _workspaces.indexWhere((w) => w.id == workspaceId);
    if (index == -1) throw Exception('Workspace not found');

    final workspace = _workspaces[index];
    final restoredWorkspace = CollaborationWorkspace(
      id: workspace.id,
      createdAt: workspace.createdAt,
      updatedAt: DateTime.now(),
      workspaceCode: workspace.workspaceCode,
      workspaceName: workspace.workspaceName,
      description: workspace.description,
      type: workspace.type,
      status: workspace.status,
      ownerId: workspace.ownerId,
      ownerName: workspace.ownerName,
      members: workspace.members,
      settings: workspace.settings,
      tags: workspace.tags,
      lastActivityAt: DateTime.now(),
      isPublic: workspace.isPublic,
      isArchived: false,
    );

    _workspaces[index] = restoredWorkspace;
    return ApiResponse(data: restoredWorkspace, success: true);
  }

  @override
  Future<ApiResponse<WorkspaceMember>> addMember(AddMemberRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final workspaceIndex = _workspaces.indexWhere((w) => w.id == request.workspaceId);
    if (workspaceIndex == -1) throw Exception('Workspace not found');

    final newMember = WorkspaceMember(
      userId: request.userId,
      userName: 'User ${request.userId}', // Mock
      userEmail: '${request.userId}@example.com', // Mock
      role: request.role,
      joinedAt: DateTime.now(),
      isActive: true,
      isOnline: _random.nextBool(),
      permissions: request.permissions,
    );

    final workspace = _workspaces[workspaceIndex];
    final updatedMembers = [...workspace.members, newMember];

    final updatedWorkspace = CollaborationWorkspace(
      id: workspace.id,
      createdAt: workspace.createdAt,
      updatedAt: DateTime.now(),
      workspaceCode: workspace.workspaceCode,
      workspaceName: workspace.workspaceName,
      description: workspace.description,
      type: workspace.type,
      status: workspace.status,
      ownerId: workspace.ownerId,
      ownerName: workspace.ownerName,
      members: updatedMembers,
      settings: workspace.settings,
      tags: workspace.tags,
      lastActivityAt: DateTime.now(),
      isPublic: workspace.isPublic,
      isArchived: workspace.isArchived,
    );

    _workspaces[workspaceIndex] = updatedWorkspace;
    _broadcastActivity(workspace.id, ActivityType.userJoined, '${newMember.userName} joined the workspace');

    return ApiResponse(data: newMember, success: true);
  }

  @override
  Future<ApiResponse<WorkspaceMember>> updateMember(UpdateMemberRequest request) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final workspaceIndex = _workspaces.indexWhere((w) => w.id == request.workspaceId);
    if (workspaceIndex == -1) throw Exception('Workspace not found');

    final workspace = _workspaces[workspaceIndex];
    final memberIndex = workspace.members.indexWhere((m) => m.userId == request.userId);
    if (memberIndex == -1) throw Exception('Member not found');

    final existingMember = workspace.members[memberIndex];
    final updatedMember = WorkspaceMember(
      userId: existingMember.userId,
      userName: existingMember.userName,
      userEmail: existingMember.userEmail,
      userAvatar: existingMember.userAvatar,
      role: request.role ?? existingMember.role,
      joinedAt: existingMember.joinedAt,
      lastSeenAt: existingMember.lastSeenAt,
      isActive: existingMember.isActive,
      isOnline: existingMember.isOnline,
      permissions: request.permissions ?? existingMember.permissions,
    );

    final updatedMembers = List<WorkspaceMember>.from(workspace.members);
    updatedMembers[memberIndex] = updatedMember;

    final updatedWorkspace = CollaborationWorkspace(
      id: workspace.id,
      createdAt: workspace.createdAt,
      updatedAt: DateTime.now(),
      workspaceCode: workspace.workspaceCode,
      workspaceName: workspace.workspaceName,
      description: workspace.description,
      type: workspace.type,
      status: workspace.status,
      ownerId: workspace.ownerId,
      ownerName: workspace.ownerName,
      members: updatedMembers,
      settings: workspace.settings,
      tags: workspace.tags,
      lastActivityAt: DateTime.now(),
      isPublic: workspace.isPublic,
      isArchived: workspace.isArchived,
    );

    _workspaces[workspaceIndex] = updatedWorkspace;
    return ApiResponse(data: updatedMember, success: true);
  }

  @override
  Future<ApiVoidResponse> removeMember(String workspaceId, String userId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    final workspaceIndex = _workspaces.indexWhere((w) => w.id == workspaceId);
    if (workspaceIndex == -1) throw Exception('Workspace not found');

    final workspace = _workspaces[workspaceIndex];
    final member = workspace.members.firstWhere(
      (m) => m.userId == userId,
      orElse: () => throw Exception('Member not found'),
    );

    final updatedMembers = workspace.members.where((m) => m.userId != userId).toList();

    final updatedWorkspace = CollaborationWorkspace(
      id: workspace.id,
      createdAt: workspace.createdAt,
      updatedAt: DateTime.now(),
      workspaceCode: workspace.workspaceCode,
      workspaceName: workspace.workspaceName,
      description: workspace.description,
      type: workspace.type,
      status: workspace.status,
      ownerId: workspace.ownerId,
      ownerName: workspace.ownerName,
      members: updatedMembers,
      settings: workspace.settings,
      tags: workspace.tags,
      lastActivityAt: DateTime.now(),
      isPublic: workspace.isPublic,
      isArchived: workspace.isArchived,
    );

    _workspaces[workspaceIndex] = updatedWorkspace;
    _broadcastActivity(workspaceId, ActivityType.userLeft, '${member.userName} left the workspace');

    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiListResponse<WorkspaceMember>> getWorkspaceMembers({
    required String workspaceId,
    MemberFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));

    final workspace = _workspaces.firstWhere(
      (w) => w.id == workspaceId,
      orElse: () => throw Exception('Workspace not found'),
    );

    var filteredMembers = workspace.members.where((member) {
      if (filter?.role != null && member.role != filter!.role) return false;
      if (filter?.isActive != null && member.isActive != filter!.isActive) return false;
      if (filter?.isOnline != null && member.isOnline != filter!.isOnline) return false;
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedMembers = filteredMembers.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedMembers,
      pagination: Pagination(
        currentPage: (pagination?.page ?? 0) + 1, // Adding 1 because Pagination class uses 1-based page numbers
        perPage: pagination?.perPage ?? 20,
        total: filteredMembers.length,
        totalPages: (filteredMembers.length / (pagination?.perPage ?? 20)).ceil(),
        from: startIndex + 1,
        to: startIndex + paginatedMembers.length,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredMembers.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<ApiResponse<WorkspaceMember>> inviteMember(InviteMemberRequest request) async {
    await Future.delayed(const Duration(milliseconds: 700));

    // Mock invitation - in real implementation, this would send an email
    final newMember = WorkspaceMember(
      userId: 'invited_${DateTime.now().millisecondsSinceEpoch}',
      userName: request.email.split('@')[0],
      userEmail: request.email,
      role: request.role,
      joinedAt: DateTime.now(),
      isActive: false, // Pending invitation
      isOnline: false,
    );

    return ApiResponse(data: newMember, success: true);
  }

  @override
  Future<ApiVoidResponse> acceptInvitation(String invitationId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // Mock acceptance
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiVoidResponse> declineInvitation(String invitationId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    // Mock decline
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiListResponse<CollaborationChannel>> getChannels({
    required String workspaceId,
    ChannelFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));

    var filteredChannels = _channels.where((channel) {
      if (channel.workspaceId != workspaceId) return false;
      if (filter?.type != null && channel.type != filter!.type) return false;
      if (filter?.isArchived != null && channel.isArchived != filter!.isArchived) return false;
      if (filter?.hasUnreadMessages != null && channel.hasUnreadMessages != filter!.hasUnreadMessages) return false;
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedChannels = filteredChannels.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedChannels,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        totalPages: (filteredChannels.length / (pagination?.perPage ?? 20)).ceil(),
        total: filteredChannels.length,
        perPage: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredChannels.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<ApiResponse<CollaborationChannel>> getChannelById(String channelId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final channel = _channels.firstWhere(
      (c) => c.id == channelId,
      orElse: () => throw Exception('Channel not found'),
    );

    return ApiResponse(data: channel, success: true);
  }

  @override
  Future<ApiResponse<CollaborationChannel>> createChannel(CreateChannelRequest request) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final newChannel = CollaborationChannel(
      id: 'channel_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      channelCode: 'CH-${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: request.workspaceId,
      channelName: request.channelName,
      description: request.description,
      type: request.type,
      status: CommonStatus.active,
      createdBy: 'current_user', // Mock
      memberIds: ['current_user', ...request.memberIds],
      settings: request.settings,
    );

    _channels.add(newChannel);
    _broadcastActivity(request.workspaceId, ActivityType.channelCreated, 'Channel "${newChannel.channelName}" created');

    return ApiResponse(data: newChannel, success: true);
  }

  @override
  Future<ApiResponse<CollaborationChannel>> updateChannel(UpdateChannelRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final index = _channels.indexWhere((c) => c.id == request.channelId);
    if (index == -1) throw Exception('Channel not found');

    final existingChannel = _channels[index];
    final updatedChannel = CollaborationChannel(
      id: existingChannel.id,
      createdAt: existingChannel.createdAt,
      updatedAt: DateTime.now(),
      channelCode: existingChannel.channelCode,
      workspaceId: existingChannel.workspaceId,
      channelName: request.channelName ?? existingChannel.channelName,
      description: request.description ?? existingChannel.description,
      type: existingChannel.type,
      status: existingChannel.status,
      createdBy: existingChannel.createdBy,
      memberIds: existingChannel.memberIds,
      settings: request.settings ?? existingChannel.settings,
      lastMessageAt: existingChannel.lastMessageAt,
      messageCount: existingChannel.messageCount,
      unreadCount: existingChannel.unreadCount,
      isArchived: existingChannel.isArchived,
    );

    _channels[index] = updatedChannel;
    _broadcastActivity(updatedChannel.workspaceId, ActivityType.channelUpdated, 'Channel "${updatedChannel.channelName}" updated');

    return ApiResponse(data: updatedChannel, success: true);
  }

  @override
  Future<ApiVoidResponse> deleteChannel(String channelId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    final channel = _channels.firstWhere(
      (c) => c.id == channelId,
      orElse: () => throw Exception('Channel not found'),
    );

    _channels.removeWhere((c) => c.id == channelId);
    _messages.removeWhere((m) => m.channelId == channelId);

    return  ApiVoidResponse(success: true);
  }

  @override
  Future<ApiResponse<CollaborationChannel>> archiveChannel(String channelId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _channels.indexWhere((c) => c.id == channelId);
    if (index == -1) throw Exception('Channel not found');

    final channel = _channels[index];
    final archivedChannel = CollaborationChannel(
      id: channel.id,
      createdAt: channel.createdAt,
      updatedAt: DateTime.now(),
      channelCode: channel.channelCode,
      workspaceId: channel.workspaceId,
      channelName: channel.channelName,
      description: channel.description,
      type: channel.type,
      status: channel.status,
      createdBy: channel.createdBy,
      memberIds: channel.memberIds,
      settings: channel.settings,
      lastMessageAt: channel.lastMessageAt,
      messageCount: channel.messageCount,
      unreadCount: channel.unreadCount,
      isArchived: true,
    );

    _channels[index] = archivedChannel;
    return ApiResponse(data: archivedChannel, success: true);
  }

  @override
  Future<ApiResponse<CollaborationChannel>> joinChannel(String channelId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    final index = _channels.indexWhere((c) => c.id == channelId);
    if (index == -1) throw Exception('Channel not found');

    final channel = _channels[index];
    final updatedMemberIds = [...channel.memberIds];
    if (!updatedMemberIds.contains('current_user')) {
      updatedMemberIds.add('current_user');
    }

    final updatedChannel = CollaborationChannel(
      id: channel.id,
      createdAt: channel.createdAt,
      updatedAt: DateTime.now(),
      channelCode: channel.channelCode,
      workspaceId: channel.workspaceId,
      channelName: channel.channelName,
      description: channel.description,
      type: channel.type,
      status: channel.status,
      createdBy: channel.createdBy,
      memberIds: updatedMemberIds,
      settings: channel.settings,
      lastMessageAt: channel.lastMessageAt,
      messageCount: channel.messageCount,
      unreadCount: channel.unreadCount,
      isArchived: channel.isArchived,
    );

    _channels[index] = updatedChannel;
    return ApiResponse(data: updatedChannel, success: true);
  }

  @override
  Future<ApiVoidResponse> leaveChannel(String channelId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final index = _channels.indexWhere((c) => c.id == channelId);
    if (index == -1) throw Exception('Channel not found');

    final channel = _channels[index];
    final updatedMemberIds = channel.memberIds.where((id) => id != 'current_user').toList();

    final updatedChannel = CollaborationChannel(
      id: channel.id,
      createdAt: channel.createdAt,
      updatedAt: DateTime.now(),
      channelCode: channel.channelCode,
      workspaceId: channel.workspaceId,
      channelName: channel.channelName,
      description: channel.description,
      type: channel.type,
      status: channel.status,
      createdBy: channel.createdBy,
      memberIds: updatedMemberIds,
      settings: channel.settings,
      lastMessageAt: channel.lastMessageAt,
      messageCount: channel.messageCount,
      unreadCount: channel.unreadCount,
      isArchived: channel.isArchived,
    );

    _channels[index] = updatedChannel;
    return ApiVoidResponse(success: true);
  }

  // Helper methods for broadcasting real-time updates
  void _broadcastActivity(String workspaceId, ActivityType type, String description) {
    final activity = RealTimeActivity(
      id: 'activity_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      activityCode: 'ACT-${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: workspaceId,
      userId: 'current_user',
      userName: 'Current User',
      type: type,
      description: description,
      priority: ActivityPriority.normal,
    );

    _activities.add(activity);

    // Broadcast to activity stream
    if (_activityStreams.containsKey(workspaceId)) {
      _activityStreams[workspaceId]!.add(activity);
    }
  }

  void _broadcastMessage(CollaborationMessage message) {
    // Broadcast to message stream
    if (_messageStreams.containsKey(message.channelId)) {
      _messageStreams[message.channelId]!.add(message);
    }

    // Update channel last message time
    final channelIndex = _channels.indexWhere((c) => c.id == message.channelId);
    if (channelIndex != -1) {
      final channel = _channels[channelIndex];
      final updatedChannel = CollaborationChannel(
        id: channel.id,
        createdAt: channel.createdAt,
        updatedAt: DateTime.now(),
        channelCode: channel.channelCode,
        workspaceId: channel.workspaceId,
        channelName: channel.channelName,
        description: channel.description,
        type: channel.type,
        status: channel.status,
        createdBy: channel.createdBy,
        memberIds: channel.memberIds,
        settings: channel.settings,
        lastMessageAt: message.createdAt,
        messageCount: channel.messageCount + 1,
        unreadCount: channel.unreadCount + 1,
        isArchived: channel.isArchived,
      );
      _channels[channelIndex] = updatedChannel;
    }

    // Broadcast activity
    _broadcastActivity(message.workspaceId, ActivityType.messagePosted, 'New message in ${_getChannelName(message.channelId)}');
  }

  String _getChannelName(String channelId) {
    try {
      return _channels.firstWhere((c) => c.id == channelId).channelName;
    } catch (e) {
      return 'Unknown Channel';
    }
  }

  // Implement remaining abstract methods with basic implementations
  @override
  Future<ApiListResponse<CollaborationMessage>> getMessages({
    required String channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));

    var filteredMessages = _messages.where((m) => m.channelId == channelId).toList();
    filteredMessages.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedMessages = filteredMessages.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedMessages,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        total: (filteredMessages.length / (pagination?.perPage ?? 20)).ceil(),
        perPage: filteredMessages.length,
        totalPages: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredMessages.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  // Implement remaining methods with basic mock implementations
  @override
  Future<ApiResponse<CollaborationMessage>> getMessageById(String messageId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final message = _messages.firstWhere((m) => m.id == messageId, orElse: () => throw Exception('Message not found'));
    return ApiResponse(data: message, success: true);
  }

  @override
  Future<ApiResponse<CollaborationMessage>> sendMessage(SendMessageRequest request) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final newMessage = CollaborationMessage(
      id: 'message_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      messageCode: 'MSG-${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: _getWorkspaceIdForChannel(request.channelId),
      channelId: request.channelId,
      senderId: 'current_user',
      senderName: 'Current User',
      type: request.type,
      content: request.content,
      mentions: request.mentions,
      replyToMessageId: request.replyToMessageId,
    );

    _messages.add(newMessage);
    _broadcastMessage(newMessage);
    return ApiResponse(data: newMessage, success: true);
  }

  @override
  Future<ApiResponse<CollaborationMessage>> editMessage(EditMessageRequest request) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final index = _messages.indexWhere((m) => m.id == request.messageId);
    if (index == -1) throw Exception('Message not found');

    final message = _messages[index];
    final editedMessage = CollaborationMessage(
      id: message.id,
      createdAt: message.createdAt,
      updatedAt: DateTime.now(),
      messageCode: message.messageCode,
      workspaceId: message.workspaceId,
      channelId: message.channelId,
      senderId: message.senderId,
      senderName: message.senderName,
      type: message.type,
      content: request.content,
      attachments: message.attachments,
      replyToMessageId: message.replyToMessageId,
      reactions: message.reactions,
      mentions: message.mentions,
      status: message.status,
      editedAt: DateTime.now(),
      isPinned: message.isPinned,
      isThreadStarter: message.isThreadStarter,
      threadCount: message.threadCount,
    );

    _messages[index] = editedMessage;
    return ApiResponse(data: editedMessage,success: true);
  }

  @override
  Future<ApiVoidResponse> deleteMessage(String messageId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    _messages.removeWhere((m) => m.id == messageId);
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiResponse<CollaborationMessage>> pinMessage(String messageId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index == -1) throw Exception('Message not found');

    final message = _messages[index];
    final pinnedMessage = CollaborationMessage(
      id: message.id,
      createdAt: message.createdAt,
      updatedAt: DateTime.now(),
      messageCode: message.messageCode,
      workspaceId: message.workspaceId,
      channelId: message.channelId,
      senderId: message.senderId,
      senderName: message.senderName,
      type: message.type,
      content: message.content,
      attachments: message.attachments,
      replyToMessageId: message.replyToMessageId,
      reactions: message.reactions,
      mentions: message.mentions,
      status: message.status,
      editedAt: message.editedAt,
      isPinned: true,
      isThreadStarter: message.isThreadStarter,
      threadCount: message.threadCount,
    );

    _messages[index] = pinnedMessage;
    return ApiResponse(data: pinnedMessage, success: true);
  }

  @override
  Future<ApiVoidResponse> unpinMessage(String messageId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index != -1) {
      final message = _messages[index];
      final unpinnedMessage = CollaborationMessage(
        id: message.id,
        createdAt: message.createdAt,
        updatedAt: DateTime.now(),
        messageCode: message.messageCode,
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        senderId: message.senderId,
        senderName: message.senderName,
        type: message.type,
        content: message.content,
        attachments: message.attachments,
        replyToMessageId: message.replyToMessageId,
        reactions: message.reactions,
        mentions: message.mentions,
        status: message.status,
        editedAt: message.editedAt,
        isPinned: false,
        isThreadStarter: message.isThreadStarter,
        threadCount: message.threadCount,
      );
      _messages[index] = unpinnedMessage;
    }

    return  ApiVoidResponse(success: true);
  }

  @override
  Future<ApiResponse<CollaborationMessage>> addReaction(AddReactionRequest request) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final index = _messages.indexWhere((m) => m.id == request.messageId);
    if (index == -1) throw Exception('Message not found');

    final message = _messages[index];
    final reactions = List<MessageReaction>.from(message.reactions);

    final existingReactionIndex = reactions.indexWhere((r) => r.emoji == request.emoji);
    if (existingReactionIndex != -1) {
      final existingReaction = reactions[existingReactionIndex];
      if (!existingReaction.userIds.contains('current_user')) {
        reactions[existingReactionIndex] = MessageReaction(
          emoji: existingReaction.emoji,
          userIds: [...existingReaction.userIds, 'current_user'],
          count: existingReaction.count + 1,
        );
      }
    } else {
      reactions.add(MessageReaction(
        emoji: request.emoji,
        userIds: ['current_user'],
        count: 1,
      ));
    }

    final updatedMessage = CollaborationMessage(
      id: message.id,
      createdAt: message.createdAt,
      updatedAt: DateTime.now(),
      messageCode: message.messageCode,
      workspaceId: message.workspaceId,
      channelId: message.channelId,
      senderId: message.senderId,
      senderName: message.senderName,
      type: message.type,
      content: message.content,
      attachments: message.attachments,
      replyToMessageId: message.replyToMessageId,
      reactions: reactions,
      mentions: message.mentions,
      status: message.status,
      editedAt: message.editedAt,
      isPinned: message.isPinned,
      isThreadStarter: message.isThreadStarter,
      threadCount: message.threadCount,
    );

    _messages[index] = updatedMessage;
    return ApiResponse(data: updatedMessage, success: true);
  }

  String _getWorkspaceIdForChannel(String channelId) {
    try {
      return _channels.firstWhere((c) => c.id == channelId).workspaceId;
    } catch (e) {
      return 'unknown_workspace';
    }
  }

  // Implement remaining abstract methods with basic stubs
  @override
  Future<ApiVoidResponse> removeReaction(RemoveReactionRequest request) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiVoidResponse> markMessageAsRead(String messageId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiVoidResponse> markChannelAsRead(String channelId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiListResponse<CollaborationMessage>> getThreadMessages({
    required String parentMessageId,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return ApiListResponse(data: [], pagination: Pagination.empty(),success: true);
  }

  @override
  Future<ApiResponse<CollaborationMessage>> replyToThread(ReplyToThreadRequest request) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final reply = CollaborationMessage(
      id: 'reply_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      messageCode: 'RPL-${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: 'workspace_1',
      channelId: 'channel_1',
      senderId: 'current_user',
      senderName: 'Current User',
      type: MessageType.text,
      content: request.content,
      replyToMessageId: request.parentMessageId,
    );
    return ApiResponse(data: reply , success: true);
  }

  @override
  Future<ApiResponse<MessageAttachment>> uploadFile(UploadFileRequest request) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    final attachment = MessageAttachment(
      attachmentId: 'file_${DateTime.now().millisecondsSinceEpoch}',
      fileName: request.fileName,
      fileType: request.fileType,
      fileSize: request.fileSize,
      fileUrl: 'https://example.com/files/${request.fileName}',
    );
    return ApiResponse(data: attachment, success: true);
  }

  @override
  Future<String> getFileDownloadUrl(String attachmentId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return 'https://example.com/download/$attachmentId';
  }

  @override
  Future<ApiVoidResponse> deleteFile(String attachmentId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiListResponse<MessageAttachment>> getChannelFiles({
    required String channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return ApiListResponse(data: _attachments, pagination: Pagination.empty(),success: true);
  }

  @override
  Stream<CollaborationMessage> getMessageStream(String channelId) {
    if (!_messageStreams.containsKey(channelId)) {
      _messageStreams[channelId] = StreamController<CollaborationMessage>.broadcast();
    }
    return _messageStreams[channelId]!.stream;
  }

  @override
  Stream<RealTimeActivity> getActivityStream(String workspaceId) {
    if (!_activityStreams.containsKey(workspaceId)) {
      _activityStreams[workspaceId] = StreamController<RealTimeActivity>.broadcast();
    }
    return _activityStreams[workspaceId]!.stream;
  }

  @override
  Stream<List<WorkspaceMember>> getOnlineMembersStream(String workspaceId) {
    if (!_memberStreams.containsKey(workspaceId)) {
      _memberStreams[workspaceId] = StreamController<List<WorkspaceMember>>.broadcast();
    }
    return _memberStreams[workspaceId]!.stream;
  }

  @override
  Future<ApiVoidResponse> updateUserPresence(UpdatePresenceRequest request) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiVoidResponse> sendTypingIndicator(TypingIndicatorRequest request) async {
    await Future.delayed(const Duration(milliseconds: 50));
    return ApiVoidResponse(success: true);
  }

  // Session management stubs
  @override
  Future<ApiListResponse<CollaborationSession>> getSessions({
    required String workspaceId,
    SessionFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final workspaceSessions = _sessions.where((s) => s.workspaceId == workspaceId).toList();
    return ApiListResponse(data: workspaceSessions, pagination: Pagination.empty(), success: true);
  }

  @override
  Future<ApiResponse<CollaborationSession>> getSessionById(String sessionId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final session = _sessions.firstWhere((s) => s.id == sessionId, orElse: () => throw Exception('Session not found'));
    return ApiResponse(data: session, success: true);
  }

  @override
  Future<ApiResponse<CollaborationSession>> createSession(CreateSessionRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));
    final session = CollaborationSession(
      id: 'session_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sessionCode: 'SES-${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: request.workspaceId,
      channelId: request.channelId,
      hostId: 'current_user',
      hostName: 'Current User',
      type: request.type,
      title: request.title,
      description: request.description,
      status: SessionStatus.scheduled,
      settings: request.settings,
    );
    _sessions.add(session);
    return ApiResponse(data: session, success: true);
  }

  @override
  Future<ApiResponse<CollaborationSession>> updateSession(UpdateSessionRequest request) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final session = _sessions.firstWhere((s) => s.id == request.sessionId, orElse: () => throw Exception('Session not found'));
    return ApiResponse(data: session, success: true);
  }

  @override
  Future<ApiVoidResponse> deleteSession(String sessionId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    _sessions.removeWhere((s) => s.id == sessionId);
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiResponse<CollaborationSession>> startSession(String sessionId) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final session = _sessions.firstWhere((s) => s.id == sessionId, orElse: () => throw Exception('Session not found'));
    return ApiResponse(data: session, success: true);
  }

  @override
  Future<ApiResponse<CollaborationSession>> endSession(String sessionId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final session = _sessions.firstWhere((s) => s.id == sessionId, orElse: () => throw Exception('Session not found'));
    return ApiResponse(data: session, success: true);
  }

  @override
  Future<ApiResponse<SessionParticipant>> joinSession(JoinSessionRequest request) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final participant = SessionParticipant(
      userId: 'current_user',
      userName: 'Current User',
      joinedAt: DateTime.now(),
    );
    return ApiResponse(data: participant, success: true);
  }

  @override
  Future<ApiVoidResponse> leaveSession(String sessionId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiResponse<SessionParticipant>> updateParticipant(UpdateParticipantRequest request) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final participant = SessionParticipant(
      userId: request.userId,
      userName: 'User ${request.userId}',
      joinedAt: DateTime.now(),
      isMuted: request.isMuted ?? false,
      isScreenSharing: request.isScreenSharing ?? false,
      role: request.role ?? ParticipantRole.participant,
    );
    return ApiResponse(data: participant, success: true);
  }

  // Search and analytics stubs
  @override
  Future<ApiListResponse<CollaborationWorkspace>> searchWorkspaces({
    required String query,
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final results = _workspaces.where((w) => w.workspaceName.toLowerCase().contains(query.toLowerCase())).toList();
    return ApiListResponse(data: results, pagination: Pagination.empty(), success: true);
  }

  @override
  Future<ApiListResponse<CollaborationMessage>> searchMessages({
    required String query,
    required String workspaceId,
    String? channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));
    final results = _messages.where((m) =>
      m.workspaceId == workspaceId &&
      m.content.toLowerCase().contains(query.toLowerCase())
    ).toList();
    return ApiListResponse(data: results, pagination: Pagination.empty(), success: true);
  }

  @override
  Future<ApiListResponse<MessageAttachment>> searchFiles({
    required String query,
    required String workspaceId,
    String? channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final results = _attachments.where((a) => a.fileName.toLowerCase().contains(query.toLowerCase())).toList();
    return ApiListResponse(data: results, pagination: Pagination.empty(), success: true);
  }

  @override
  Future<Map<String, dynamic>> getWorkspaceAnalytics({
    required String workspaceId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));
    return {
      'total_messages': 1250 + _random.nextInt(500),
      'active_users': 25 + _random.nextInt(15),
      'channels_created': 5 + _random.nextInt(10),
      'files_shared': 85 + _random.nextInt(50),
      'sessions_held': 12 + _random.nextInt(8),
    };
  }

  @override
  Future<Map<String, dynamic>> getChannelAnalytics({
    required String channelId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));
    return {
      'message_count': 150 + _random.nextInt(100),
      'active_members': 8 + _random.nextInt(12),
      'files_shared': 15 + _random.nextInt(20),
      'peak_activity_hour': _random.nextInt(24),
    };
  }

  @override
  Future<Map<String, dynamic>> getUserActivityAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'messages_sent': 45 + _random.nextInt(50),
      'channels_joined': 3 + _random.nextInt(5),
      'files_uploaded': 8 + _random.nextInt(12),
      'sessions_attended': 5 + _random.nextInt(8),
      'online_hours': 35 + _random.nextInt(25),
    };
  }

  @override
  Future<ApiListResponse<RealTimeActivity>> getNotifications({
    NotificationFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return ApiListResponse(data: _activities.take(20).toList(), pagination: Pagination.empty(), success: true);
  }

  @override
  Future<ApiVoidResponse> markNotificationAsRead(String activityId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiVoidResponse> markAllNotificationsAsRead() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return ApiVoidResponse(success: true);
  }

  @override
  Future<ApiVoidResponse> updateNotificationSettings(UpdateNotificationSettingsRequest request) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return ApiVoidResponse(success: true);
  }
}

// Mock data generators
List<CollaborationWorkspace> _generateMockWorkspaces() {
  return [
    CollaborationWorkspace(
      id: 'workspace_1',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      workspaceCode: 'WS-MAIN-001',
      workspaceName: 'Manufacturing Operations',
      description: 'Main workspace for manufacturing operations and coordination',
      type: WorkspaceType.general,
      status: CommonStatus.active,
      ownerId: 'user_001',
      ownerName: 'John Smith',
      members: [
        WorkspaceMember(
          userId: 'user_001',
          userName: 'John Smith',
          userEmail: '<EMAIL>',
          role: WorkspaceRole.owner,
          joinedAt: DateTime.now().subtract(const Duration(days: 30)),
          isActive: true,
          isOnline: true,
        ),
        WorkspaceMember(
          userId: 'user_002',
          userName: 'Sarah Johnson',
          userEmail: '<EMAIL>',
          role: WorkspaceRole.admin,
          joinedAt: DateTime.now().subtract(const Duration(days: 25)),
          isActive: true,
          isOnline: false,
        ),
        WorkspaceMember(
          userId: 'user_003',
          userName: 'Mike Wilson',
          userEmail: '<EMAIL>',
          role: WorkspaceRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 20)),
          isActive: true,
          isOnline: true,
        ),
      ],
      settings: const WorkspaceSettings(
        allowGuestAccess: false,
        requireApprovalToJoin: true,
        allowFileSharing: true,
        allowScreenSharing: true,
        enableNotifications: true,
        enableRealTimeUpdates: true,
        maxMembers: 100,
      ),
      tags: const ['manufacturing', 'operations', 'main'],
      lastActivityAt: DateTime.now().subtract(const Duration(minutes: 15)),
      isPublic: false,
    ),
    CollaborationWorkspace(
      id: 'workspace_2',
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
      workspaceCode: 'WS-QC-002',
      workspaceName: 'Quality Control Team',
      description: 'Dedicated workspace for quality control processes and discussions',
      type: WorkspaceType.department,
      status: CommonStatus.active,
      ownerId: 'user_004',
      ownerName: 'Lisa Chen',
      members: [
        WorkspaceMember(
          userId: 'user_004',
          userName: 'Lisa Chen',
          userEmail: '<EMAIL>',
          role: WorkspaceRole.owner,
          joinedAt: DateTime.now().subtract(const Duration(days: 15)),
          isActive: true,
          isOnline: true,
        ),
        WorkspaceMember(
          userId: 'user_005',
          userName: 'David Brown',
          userEmail: '<EMAIL>',
          role: WorkspaceRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 12)),
          isActive: true,
          isOnline: false,
        ),
      ],
      settings: const WorkspaceSettings(
        allowGuestAccess: true,
        requireApprovalToJoin: false,
        allowFileSharing: true,
        allowScreenSharing: false,
        enableNotifications: true,
        enableRealTimeUpdates: true,
        maxMembers: 50,
      ),
      tags: const ['quality', 'control', 'inspection'],
      lastActivityAt: DateTime.now().subtract(const Duration(minutes: 30)),
      isPublic: true,
    ),
  ];
}

List<CollaborationChannel> _generateMockChannels() {
  return [
    CollaborationChannel(
      id: 'channel_1',
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 10)),
      channelCode: 'CH-GENERAL-001',
      workspaceId: 'workspace_1',
      channelName: 'general',
      description: 'General discussions and announcements',
      type: ChannelType.general,
      status: CommonStatus.active,
      createdBy: 'user_001',
      memberIds: ['user_001', 'user_002', 'user_003'],
      settings: const ChannelSettings(
        isPrivate: false,
        allowThreads: true,
        allowReactions: true,
        allowFileUploads: true,
        muteNotifications: false,
      ),
      lastMessageAt: DateTime.now().subtract(const Duration(minutes: 10)),
      messageCount: 156,
      unreadCount: 3,
    ),
    CollaborationChannel(
      id: 'channel_2',
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      channelCode: 'CH-PROD-002',
      workspaceId: 'workspace_1',
      channelName: 'production-updates',
      description: 'Production line updates and status reports',
      type: ChannelType.project,
      status: CommonStatus.active,
      createdBy: 'user_002',
      memberIds: ['user_001', 'user_002', 'user_003'],
      settings: const ChannelSettings(
        isPrivate: false,
        allowThreads: true,
        allowReactions: true,
        allowFileUploads: true,
        muteNotifications: false,
      ),
      lastMessageAt: DateTime.now().subtract(const Duration(hours: 2)),
      messageCount: 89,
      unreadCount: 0,
    ),
    CollaborationChannel(
      id: 'channel_3',
      createdAt: DateTime.now().subtract(const Duration(days: 12)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 45)),
      channelCode: 'CH-QC-003',
      workspaceId: 'workspace_2',
      channelName: 'quality-alerts',
      description: 'Quality control alerts and issues',
      type: ChannelType.announcement,
      status: CommonStatus.active,
      createdBy: 'user_004',
      memberIds: ['user_004', 'user_005'],
      settings: const ChannelSettings(
        isPrivate: true,
        allowThreads: false,
        allowReactions: true,
        allowFileUploads: true,
        muteNotifications: false,
      ),
      lastMessageAt: DateTime.now().subtract(const Duration(minutes: 45)),
      messageCount: 34,
      unreadCount: 1,
    ),
  ];
}

List<CollaborationMessage> _generateMockMessages() {
  return [
    CollaborationMessage(
      id: 'message_1',
      createdAt: DateTime.now().subtract(const Duration(minutes: 10)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 10)),
      messageCode: 'MSG-001',
      workspaceId: 'workspace_1',
      channelId: 'channel_1',
      senderId: 'user_002',
      senderName: 'Sarah Johnson',
      type: MessageType.text,
      content: 'Good morning team! Production line 2 is running smoothly today. Quality metrics are looking great.',
      reactions: [
        const MessageReaction(emoji: '👍', userIds: ['user_001', 'user_003'], count: 2),
        const MessageReaction(emoji: '✅', userIds: ['user_001'], count: 1),
      ],
      status: MessageStatus.delivered,
    ),
    CollaborationMessage(
      id: 'message_2',
      createdAt: DateTime.now().subtract(const Duration(minutes: 25)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 25)),
      messageCode: 'MSG-002',
      workspaceId: 'workspace_1',
      channelId: 'channel_1',
      senderId: 'user_001',
      senderName: 'John Smith',
      type: MessageType.text,
      content: 'Thanks for the update Sarah. Can you share the latest efficiency numbers?',
      replyToMessageId: 'message_1',
      status: MessageStatus.read,
    ),
    CollaborationMessage(
      id: 'message_3',
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      messageCode: 'MSG-003',
      workspaceId: 'workspace_1',
      channelId: 'channel_2',
      senderId: 'user_003',
      senderName: 'Mike Wilson',
      type: MessageType.file,
      content: 'Daily production report attached',
      attachments: [
        const MessageAttachment(
          attachmentId: 'file_1',
          fileName: 'daily_production_report.pdf',
          fileType: 'pdf',
          fileSize: 2048576,
          fileUrl: 'https://example.com/files/daily_production_report.pdf',
        ),
      ],
      status: MessageStatus.delivered,
      isPinned: true,
    ),
  ];
}

List<CollaborationSession> _generateMockSessions() {
  return [
    CollaborationSession(
      id: 'session_1',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      sessionCode: 'SES-STANDUP-001',
      workspaceId: 'workspace_1',
      channelId: 'channel_1',
      hostId: 'user_001',
      hostName: 'John Smith',
      type: SessionType.standup,
      title: 'Daily Production Standup',
      description: 'Daily standup meeting for production team',
      status: SessionStatus.ended,
      startedAt: DateTime.now().subtract(const Duration(days: 2, hours: 1)),
      endedAt: DateTime.now().subtract(const Duration(days: 2, minutes: 30)),
      participants: [
         SessionParticipant(
          userId: 'user_001',
          userName: 'John Smith',
          joinedAt: DateTime(2024, 1, 1, 9, 0),
          role: ParticipantRole.host,
        ),
         SessionParticipant(
          userId: 'user_002',
          userName: 'Sarah Johnson',
          joinedAt: DateTime(2024, 1, 1, 9, 2),
          role: ParticipantRole.participant,
        ),
      ],
      settings: const SessionSettings(
        allowScreenSharing: true,
        allowFileSharing: true,
        allowRecording: false,
        maxParticipants: 20,
      ),
    ),
  ];
}

List<RealTimeActivity> _generateMockActivities() {
  return [
    RealTimeActivity(
      id: 'activity_1',
      createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 5)),
      activityCode: 'ACT-001',
      workspaceId: 'workspace_1',
      channelId: 'channel_1',
      userId: 'user_002',
      userName: 'Sarah Johnson',
      type: ActivityType.messagePosted,
      description: 'Posted a message in #general',
      priority: ActivityPriority.normal,
    ),
    RealTimeActivity(
      id: 'activity_2',
      createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 15)),
      activityCode: 'ACT-002',
      workspaceId: 'workspace_1',
      userId: 'user_003',
      userName: 'Mike Wilson',
      type: ActivityType.fileShared,
      description: 'Shared a file in #production-updates',
      priority: ActivityPriority.normal,
    ),
  ];
}

List<MessageAttachment> _generateMockAttachments() {
  return [
    const MessageAttachment(
      attachmentId: 'file_1',
      fileName: 'daily_production_report.pdf',
      fileType: 'pdf',
      fileSize: 2048576,
      fileUrl: 'https://example.com/files/daily_production_report.pdf',
      thumbnailUrl: 'https://example.com/thumbnails/daily_production_report.jpg',
    ),
    const MessageAttachment(
      attachmentId: 'file_2',
      fileName: 'quality_metrics.xlsx',
      fileType: 'xlsx',
      fileSize: 1024000,
      fileUrl: 'https://example.com/files/quality_metrics.xlsx',
    ),
    const MessageAttachment(
      attachmentId: 'file_3',
      fileName: 'production_line_photo.jpg',
      fileType: 'jpg',
      fileSize: 3145728,
      fileUrl: 'https://example.com/files/production_line_photo.jpg',
      thumbnailUrl: 'https://example.com/thumbnails/production_line_photo_thumb.jpg',
    ),
  ];
}