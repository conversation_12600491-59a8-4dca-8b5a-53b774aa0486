import 'package:equatable/equatable.dart';

class QualityInspectionFilter extends Equatable {
  final String? status;
  final String? inspectorId;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? referenceType;
  final String? referenceId;
  final String? qualityStandardId;

  const QualityInspectionFilter({
    this.status,
    this.inspectorId,
    this.fromDate,
    this.toDate,
    this.referenceType,
    this.referenceId,
    this.qualityStandardId,
  });

  @override
  List<Object?> get props => [
        status,
        inspectorId,
        fromDate,
        toDate,
        referenceType,
        referenceId,
        qualityStandardId,
      ];

  Map<String, dynamic> toJson() {
    return {
      if (status != null) 'status': status,
      if (inspectorId != null) 'inspectorId': inspectorId,
      if (fromDate != null) 'fromDate': fromDate?.toIso8601String(),
      if (toDate != null) 'toDate': toDate?.toIso8601String(),
      if (referenceType != null) 'referenceType': referenceType,
      if (referenceId != null) 'referenceId': referenceId,
      if (qualityStandardId != null) 'qualityStandardId': qualityStandardId,
    }..removeWhere((key, value) => value == null);
  }
}
