import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/bundle.dart';
import '../../domain/entities/bundle_input.dart';
import '../models/bundle_model.dart';

/// Remote data source for bundling operations using Firebase Firestore
@injectable
class BundlingRemoteDataSource {
  final FirebaseFirestore _firestore;
  static const String _collection = 'bundles';
  static const String _countersCollection = 'counters';

  BundlingRemoteDataSource(this._firestore);

  /// Get all bundles with optional filtering
  Future<List<BundleModel>> getAllBundles({
    BundleStatus? status,
    String? designNo,
    String? lotNo,
    String? rollNo,
    String? altNo,
    GarmentSize? size,
    String? assignedTo,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply filters
      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }
      if (designNo != null) {
        query = query.where('design_no', isEqualTo: designNo);
      }
      if (lotNo != null) {
        query = query.where('lot_no', isEqualTo: lotNo);
      }
      if (rollNo != null) {
        query = query.where('roll_no', isEqualTo: rollNo);
      }
      if (altNo != null) {
        query = query.where('alt_no', isEqualTo: altNo);
      }
      if (size != null) {
        query = query.where('size', isEqualTo: size.value);
      }
      if (assignedTo != null) {
        query = query.where('assigned_to', isEqualTo: assignedTo);
      }
      if (startDate != null) {
        query = query.where('created_at', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        query = query.where('created_at', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      // Apply soft delete filter
      query = query.where('deleted_at', isNull: true);

      // Apply ordering
      query = query.orderBy('created_at', descending: true);

      // Apply pagination
      if (offset != null && offset > 0) {
        query = query.offset(offset);
      }
      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => BundleModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch bundles: $e');
    }
  }

  /// Get a specific bundle by ID
  Future<BundleModel> getBundleById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (!doc.exists) {
        throw NotFoundException('Bundle not found');
      }
      return BundleModel.fromFirestore(doc);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw ServerException('Failed to fetch bundle: $e');
    }
  }

  /// Get bundle by bundle number
  Future<BundleModel> getBundleByNumber(String bundleNo) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('bundle_no', isEqualTo: bundleNo)
          .where('deleted_at', isNull: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        throw NotFoundException('Bundle not found');
      }

      return BundleModel.fromFirestore(querySnapshot.docs.first);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw ServerException('Failed to fetch bundle by number: $e');
    }
  }

  /// Create bundles from input data
  Future<List<BundleModel>> createBundles(
    BundleInput input,
    String createdBy,
  ) async {
    try {
      final batch = _firestore.batch();
      final bundles = <BundleModel>[];
      final now = DateTime.now();

      // Generate bundle previews
      final previews = input.generateBundlePreviews();

      for (final preview in previews) {
        final docRef = _firestore.collection(_collection).doc();
        
        final bundle = BundleModel(
          id: docRef.id,
          bundleNo: preview.bundleNo,
          designNo: input.designNo,
          lotNo: input.lotNo,
          rollNo: input.rollNo,
          altNo: input.altNo,
          size: input.size,
          pieceStartNo: preview.pieceStartNo,
          pieceEndNo: preview.pieceEndNo,
          totalPieces: preview.totalPieces,
          status: BundleStatus.pending,
          supervisorId: createdBy,
          color: input.color,
          orderNo: input.orderNo,
          clientName: input.clientName,
          remarks: input.remarks,
          createdAt: now,
          updatedAt: now,
          createdBy: createdBy,
          updatedBy: createdBy,
        );

        batch.set(docRef, bundle.toFirestore());
        bundles.add(bundle);
      }

      await batch.commit();
      return bundles;
    } catch (e) {
      throw ServerException('Failed to create bundles: $e');
    }
  }

  /// Update an existing bundle
  Future<BundleModel> updateBundle(BundleModel bundle) async {
    try {
      final updatedBundle = bundle.copyWith(
        updatedAt: DateTime.now(),
        version: bundle.version + 1,
      );

      await _firestore
          .collection(_collection)
          .doc(bundle.id)
          .update(updatedBundle.toFirestore());

      return updatedBundle;
    } catch (e) {
      throw ServerException('Failed to update bundle: $e');
    }
  }

  /// Delete a bundle (soft delete)
  Future<void> deleteBundle(String id, String deletedBy) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'deleted_at': Timestamp.fromDate(DateTime.now()),
        'deleted_by': deletedBy,
        'updated_at': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw ServerException('Failed to delete bundle: $e');
    }
  }

  /// Issue bundle to sewing
  Future<BundleModel> issueBundleToSewing(
    String bundleId,
    String assignedTo,
    String issuedBy,
  ) async {
    try {
      final now = DateTime.now();
      final updateData = {
        'status': BundleStatus.issuedToSewing.value,
        'assigned_to': assignedTo,
        'issued_at': Timestamp.fromDate(now),
        'updated_at': Timestamp.fromDate(now),
        'updated_by': issuedBy,
      };

      await _firestore.collection(_collection).doc(bundleId).update(updateData);
      
      // Return updated bundle
      return await getBundleById(bundleId);
    } catch (e) {
      throw ServerException('Failed to issue bundle to sewing: $e');
    }
  }

  /// Start bundle work
  Future<BundleModel> startBundleWork(String bundleId, String startedBy) async {
    try {
      final now = DateTime.now();
      final updateData = {
        'status': BundleStatus.inProgress.value,
        'started_at': Timestamp.fromDate(now),
        'updated_at': Timestamp.fromDate(now),
        'updated_by': startedBy,
      };

      await _firestore.collection(_collection).doc(bundleId).update(updateData);
      
      return await getBundleById(bundleId);
    } catch (e) {
      throw ServerException('Failed to start bundle work: $e');
    }
  }

  /// Complete bundle work
  Future<BundleModel> completeBundleWork(
    String bundleId,
    String completedBy,
    String? remarks,
  ) async {
    try {
      final now = DateTime.now();
      final updateData = {
        'status': BundleStatus.completed.value,
        'completed_at': Timestamp.fromDate(now),
        'updated_at': Timestamp.fromDate(now),
        'updated_by': completedBy,
      };

      if (remarks != null) {
        updateData['remarks'] = remarks;
      }

      await _firestore.collection(_collection).doc(bundleId).update(updateData);
      
      return await getBundleById(bundleId);
    } catch (e) {
      throw ServerException('Failed to complete bundle work: $e');
    }
  }

  /// Cancel bundle
  Future<BundleModel> cancelBundle(
    String bundleId,
    String cancelledBy,
    String reason,
  ) async {
    try {
      final now = DateTime.now();
      final updateData = {
        'status': BundleStatus.cancelled.value,
        'updated_at': Timestamp.fromDate(now),
        'updated_by': cancelledBy,
        'remarks': reason,
      };

      await _firestore.collection(_collection).doc(bundleId).update(updateData);
      
      return await getBundleById(bundleId);
    } catch (e) {
      throw ServerException('Failed to cancel bundle: $e');
    }
  }

  /// Get bundles assigned to a specific user
  Future<List<BundleModel>> getBundlesAssignedTo(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('assigned_to', isEqualTo: userId)
          .where('deleted_at', isNull: true)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BundleModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch assigned bundles: $e');
    }
  }

  /// Get bundles by status
  Future<List<BundleModel>> getBundlesByStatus(BundleStatus status) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: status.value)
          .where('deleted_at', isNull: true)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BundleModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch bundles by status: $e');
    }
  }

  /// Search bundles
  Future<List<BundleModel>> searchBundles(String query) async {
    try {
      // For now, search by bundle number, design number, or lot number
      // In a real implementation, you might want to use Algolia or similar
      final futures = <Future<QuerySnapshot>>[];

      // Search by bundle number
      futures.add(_firestore
          .collection(_collection)
          .where('bundle_no', isGreaterThanOrEqualTo: query.toUpperCase())
          .where('bundle_no', isLessThan: '${query.toUpperCase()}z')
          .where('deleted_at', isNull: true)
          .get());

      // Search by design number
      futures.add(_firestore
          .collection(_collection)
          .where('design_no', isGreaterThanOrEqualTo: query.toUpperCase())
          .where('design_no', isLessThan: '${query.toUpperCase()}z')
          .where('deleted_at', isNull: true)
          .get());

      final results = await Future.wait(futures);
      final bundles = <BundleModel>[];
      final seenIds = <String>{};

      for (final result in results) {
        for (final doc in result.docs) {
          if (!seenIds.contains(doc.id)) {
            seenIds.add(doc.id);
            bundles.add(BundleModel.fromFirestore(doc));
          }
        }
      }

      return bundles;
    } catch (e) {
      throw ServerException('Failed to search bundles: $e');
    }
  }

  /// Get next bundle number
  Future<String> getNextBundleNumber() async {
    try {
      final counterRef = _firestore.collection(_countersCollection).doc('bundle_counter');
      
      return await _firestore.runTransaction<String>((transaction) async {
        final counterDoc = await transaction.get(counterRef);
        
        int nextNumber = 1;
        if (counterDoc.exists) {
          nextNumber = (counterDoc.data()?['count'] ?? 0) + 1;
        }
        
        transaction.set(counterRef, {'count': nextNumber}, SetOptions(merge: true));
        
        return 'B${nextNumber.toString().padLeft(3, '0')}';
      });
    } catch (e) {
      throw ServerException('Failed to get next bundle number: $e');
    }
  }

  /// Check if bundle number exists
  Future<bool> bundleNumberExists(String bundleNo) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('bundle_no', isEqualTo: bundleNo)
          .where('deleted_at', isNull: true)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      throw ServerException('Failed to check bundle number existence: $e');
    }
  }

  /// Get bundles stream for real-time updates
  Stream<List<BundleModel>> getBundlesStream({
    BundleStatus? status,
    String? assignedTo,
  }) {
    try {
      Query query = _firestore.collection(_collection);

      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }
      if (assignedTo != null) {
        query = query.where('assigned_to', isEqualTo: assignedTo);
      }

      query = query
          .where('deleted_at', isNull: true)
          .orderBy('created_at', descending: true);

      return query.snapshots().map((snapshot) =>
          snapshot.docs.map((doc) => BundleModel.fromFirestore(doc)).toList());
    } catch (e) {
      throw ServerException('Failed to get bundles stream: $e');
    }
  }
}
