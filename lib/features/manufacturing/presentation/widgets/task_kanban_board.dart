import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/manufacturing_entities.dart';


class TaskKanbanBoard extends StatelessWidget {
  final List<ManufacturingTask> tasks;
  final Function(ManufacturingTask, TaskStatus)? onStatusChanged;
  final Function(ManufacturingTask)? onTaskTap;

  const TaskKanbanBoard({
    Key? key,
    required this.tasks,
    this.onStatusChanged,
    this.onTaskTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final todoTasks = tasks.where((t) => t.status == TaskStatus.assigned).toList();
    final inProgressTasks = tasks.where((t) => t.status == TaskStatus.inProgress).toList();
    final doneTasks = tasks.where((t) => t.status == TaskStatus.completed).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildKanbanColumn(
            context,
            'To Do',
            Colors.blue,
            todoTasks,
            TaskStatus.assigned,
            Icons.assignment_outlined,
          ),
          const SizedBox(width: 16),
          _buildKanbanColumn(
            context,
            'In Progress',
            Colors.orange,
            inProgressTasks,
            TaskStatus.inProgress,
            Icons.hourglass_empty,
          ),
          const SizedBox(width: 16),
          _buildKanbanColumn(
            context,
            'Done',
            Colors.green,
            doneTasks,
            TaskStatus.completed,
            Icons.check_circle_outline,
          ),
        ],
      ),
    );
  }

  Widget _buildKanbanColumn(
    BuildContext context,
    String title,
    Color color,
    List<ManufacturingTask> tasks,
    TaskStatus status,
    IconData icon,
  ) {
    return Expanded(
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '$title (${tasks.length})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: color,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              tasks.isEmpty
                  ? Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Text(
                        'No tasks here',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                              fontStyle: FontStyle.italic,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: tasks.length,
                      itemBuilder: (context, index) {
                        final task = tasks[index];
                        return _buildTaskCard(context, task, color);
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTaskCard(
      BuildContext context, ManufacturingTask task, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 1,
      child: InkWell(
        onTap: () => onTaskTap?.call(task),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                task.title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (task.deadline != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Due: ${_formatDate(task.deadline!)}, ${_formatTime(task.deadline!)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ],
              if (task.assignedToName.isNotEmpty) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      task.assignedToName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 8),
              if (task.priority != null) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: task.priority!.backgroundColor,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: task.priority!.borderColor,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    task.priority!.displayName,
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: task.priority!.color,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
