import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hm_collection/features/production_planning/domain/models/create_production_order_request.dart';
import 'package:hm_collection/features/production_planning/presentation/bloc/production_bloc.dart';

class CreateProductionOrderPage extends StatefulWidget {
  const CreateProductionOrderPage({super.key});

  @override
  State<CreateProductionOrderPage> createState() =>
      _CreateProductionOrderPageState();
}

class _CreateProductionOrderPageState extends State<CreateProductionOrderPage> {
  final _formKey = GlobalKey<FormState>();
  final _manufacturingOrderIdController = TextEditingController();
  final _orderNumberController = TextEditingController();
  final _quantityController = TextEditingController();
  final _notesController = TextEditingController();
  String? _selectedProductId;
  DateTime? _targetDate;

  @override
  void dispose() {
    _manufacturingOrderIdController.dispose();
    _orderNumberController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Production Order'),
      ),
      body: BlocConsumer<ProductionBloc, ProductionState>(
        listener: (context, state) {
          if (state is ProductionOrderCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Production Order Created!')),
            );
            Navigator.of(context).pop();
          } else if (state is ProductionError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${state.message}')),
            );
          }
        },
        builder: (context, state) {
          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextFormField(
                    controller: _manufacturingOrderIdController,
                    decoration: const InputDecoration(
                      labelText: 'Manufacturing Order ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter the manufacturing order ID';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _orderNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Order Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter an order number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'Quantity',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty || int.tryParse(value) == null) {
                        return 'Please enter a valid quantity';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  // Placeholder for Product ID dropdown
                  DropdownButtonFormField<String>(
                    value: _selectedProductId,
                    hint: const Text('Select Product'),
                    onChanged: (value) {
                      setState(() {
                        _selectedProductId = value;
                      });
                    },
                    items: const [
                      DropdownMenuItem(value: 'prod_123', child: Text('Product A')),
                      DropdownMenuItem(value: 'prod_456', child: Text('Product B')),
                    ],
                     validator: (value) {
                      if (value == null) {
                        return 'Please select a product';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: Text(_targetDate == null
                        ? 'Select Target Date'
                        : 'Target Date: ${MaterialLocalizations.of(context).formatShortDate(_targetDate!)}'),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: _pickTargetDate,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: state is ProductionLoading ? null : _submitForm,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: state is ProductionLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text('Create Order'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _pickTargetDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _targetDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (pickedDate != null && pickedDate != _targetDate) {
      setState(() {
        _targetDate = pickedDate;
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate() && _selectedProductId != null && _targetDate != null) {
      final request = CreateProductionOrderRequest(
        manufacturingOrderId: _manufacturingOrderIdController.text,
        orderNumber: _orderNumberController.text,
        productId: _selectedProductId!,
        quantity: int.parse(_quantityController.text),
        targetDate: _targetDate!,
        notes: _notesController.text,
      );
      context.read<ProductionBloc>().add(CreateProductionOrderRequested(request));
    } else {
       ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please complete the form')),
      );
    }
  }
}
