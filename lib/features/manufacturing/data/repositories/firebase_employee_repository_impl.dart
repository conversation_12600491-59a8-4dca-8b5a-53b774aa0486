import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../domain/entities/employee.dart';
import '../../domain/repositories/employee_repository.dart';
import '../models/employee_model.dart';

/// Firebase Firestore implementation of EmployeeRepository
class FirebaseEmployeeRepositoryImpl implements EmployeeRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  
  static const String _collectionName = 'employees';

  FirebaseEmployeeRepositoryImpl({
    required FirebaseFirestore firestore,
    required FirebaseAuth auth,
  }) : _firestore = firestore,
       _auth = auth;

  @override
  Future<Either<Failure, ApiResponse<Employee>>> createEmployee(CreateEmployeeRequest request) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      // Convert string values to enums
      final role = UserRole.values.firstWhere(
        (r) => r.value == request.role,
        orElse: () => UserRole.sewingOperator,
      );
      final department = Department.values.firstWhere(
        (d) => d.value == request.department,
        orElse: () => Department.sewing,
      );

      // Generate employee ID
      final employeeId = await _generateEmployeeId(department);
      
      // Create employee document
      final docRef = _firestore.collection(_collectionName).doc();
      final now = DateTime.now();

      final employee = Employee(
        id: docRef.id,
        employeeId: employeeId,
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        phoneNumber: request.phoneNumber,
        role: role,
        department: department,
        status: CommonStatus.active,
        hireDate: request.hireDate,
        isActive: true,
        skills: request.skills,
        hourlyRate: request.hourlyRate,
        emergencyContact: request.emergencyContact,
        address: request.address,
        metadata: request.metadata,
        createdAt: now,
        updatedAt: now,
        createdBy: currentUser.uid,
        updatedBy: currentUser.uid,
      );

      final employeeModel = EmployeeModel.fromEntity(employee);
      await docRef.set(employeeModel.toFirestore());

      return Right(ApiResponse.success(
        data: employee,
        message: 'Employee created successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to create employee: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> updateEmployee(UpdateEmployeeRequest request) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(request.id);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Employee not found'));
      }

      final existingEmployee = EmployeeModel.fromFirestore(doc).toEntity();

      // Convert string values to enums if provided
      UserRole? role;
      if (request.role != null) {
        role = UserRole.values.firstWhere(
          (r) => r.value == request.role,
          orElse: () => existingEmployee.role,
        );
      }

      Department? department;
      if (request.department != null) {
        department = Department.values.firstWhere(
          (d) => d.value == request.department,
          orElse: () => existingEmployee.department,
        );
      }

      CommonStatus? status;
      if (request.status != null) {
        status = CommonStatus.values.firstWhere(
          (s) => s.value == request.status,
          orElse: () => existingEmployee.status,
        );
      }

      // Update only provided fields
      final updatedEmployee = existingEmployee.copyWith(
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        phoneNumber: request.phoneNumber,
        role: role,
        department: department,
        status: status,
        isActive: request.isActive,
        skills: request.skills,
        hourlyRate: request.hourlyRate,
        emergencyContact: request.emergencyContact,
        address: request.address,
        updatedAt: DateTime.now(),
        updatedBy: currentUser.uid,
      );

      final employeeModel = EmployeeModel.fromEntity(updatedEmployee);
      await docRef.update(employeeModel.toFirestore());

      return Right(ApiResponse.success(
        data: updatedEmployee,
        message: 'Employee updated successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update employee: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<void>>> deleteEmployee(String id, {String? reason}) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(id);
      
      // Soft delete - mark as deleted
      await docRef.update({
        'deletedAt': FieldValue.serverTimestamp(),
        'deletedBy': currentUser.uid,
        'deletionReason': reason ?? 'No reason provided',
        'isActive': false,
        'status': CommonStatus.inactive.value,
      });

      return Right(ApiResponse.success(
        data: null,
        message: 'Employee deleted successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to delete employee: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployees({
    EmployeeFilterCriteria? filter,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore.collection(_collectionName)
          .where('deletedAt', isNull: true)
          .limit(limit);

      // Apply filters
      if (filter != null) {
        if (filter.department != null) {
          query = query.where('department', isEqualTo: filter.department!.value);
        }
        if (filter.role != null) {
          query = query.where('role', isEqualTo: filter.role!.value);
        }
        if (filter.status != null) {
          query = query.where('status', isEqualTo: filter.status!.value);
        }
        if (filter.isActive != null) {
          query = query.where('isActive', isEqualTo: filter.isActive);
        }
      }

      final querySnapshot = await query.get();
      final employees = querySnapshot.docs
          .map((doc) => EmployeeModel.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>).toEntity())
          .toList();

      return Right(ApiResponse.success(
        data: employees,
        message: 'Employees retrieved successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get employees: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> getEmployeeById(String id) async {
    try {
      final doc = await _firestore.collection(_collectionName).doc(id).get();
      
      if (!doc.exists) {
        return const Left(NotFoundFailure('Employee not found'));
      }

      final employee = EmployeeModel.fromFirestore(doc).toEntity();
      
      return Right(ApiResponse.success(
        data: employee,
        message: 'Employee retrieved successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get employee: ${e.toString()}'));
    }
  }

  @override
  Stream<List<Employee>> getEmployeesStream({
    EmployeeFilterCriteria? filter,
    int limit = 50,
  }) {
    try {
      Query query = _firestore.collection(_collectionName)
          .where('deletedAt', isNull: true)
          .limit(limit);

      // Apply filters
      if (filter != null) {
        if (filter.department != null) {
          query = query.where('department', isEqualTo: filter.department!.value);
        }
        if (filter.role != null) {
          query = query.where('role', isEqualTo: filter.role!.value);
        }
        if (filter.status != null) {
          query = query.where('status', isEqualTo: filter.status!.value);
        }
        if (filter.isActive != null) {
          query = query.where('isActive', isEqualTo: filter.isActive);
        }
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs
            .map((doc) => EmployeeModel.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>).toEntity())
            .toList();
      });
    } catch (e) {
      throw ServerFailure('Failed to get employees stream: ${e.toString()}');
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> searchEmployees(
    String query, {
    EmployeeFilterCriteria? filter,
    int limit = 20,
  }) async {
    try {
      // Firestore doesn't support full-text search, so we'll search by name and email
      final nameQuery = _firestore.collection(_collectionName)
          .where('deletedAt', isNull: true)
          .where('firstName', isGreaterThanOrEqualTo: query)
          .where('firstName', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(limit);

      final emailQuery = _firestore.collection(_collectionName)
          .where('deletedAt', isNull: true)
          .where('email', isGreaterThanOrEqualTo: query)
          .where('email', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(limit);

      final nameResults = await nameQuery.get();
      final emailResults = await emailQuery.get();

      final employees = <Employee>[];
      final addedIds = <String>{};

      // Add name search results
      for (final doc in nameResults.docs) {
        if (!addedIds.contains(doc.id)) {
          employees.add(EmployeeModel.fromFirestore(doc).toEntity());
          addedIds.add(doc.id);
        }
      }

      // Add email search results
      for (final doc in emailResults.docs) {
        if (!addedIds.contains(doc.id)) {
          employees.add(EmployeeModel.fromFirestore(doc).toEntity());
          addedIds.add(doc.id);
        }
      }

      return Right(ApiResponse.success(
        data: employees,
        message: 'Search completed successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to search employees: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<EmployeeStatistics>>> getEmployeeStatistics() async {
    try {
      final allEmployeesQuery = await _firestore.collection(_collectionName)
          .where('deletedAt', isNull: true)
          .get();

      final allEmployees = allEmployeesQuery.docs.map((doc) => EmployeeModel.fromFirestore(doc).toEntity()).toList();

      final now = DateTime.now();
      final firstDayOfMonth = DateTime(now.year, now.month, 1);
      final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

      final terminationsQuery = await _firestore.collection(_collectionName)
          .where('terminationDate', isGreaterThanOrEqualTo: Timestamp.fromDate(firstDayOfMonth))
          .where('terminationDate', isLessThanOrEqualTo: Timestamp.fromDate(lastDayOfMonth))
          .get();

      final statistics = EmployeeStatistics(
        totalEmployees: allEmployees.length,
        activeEmployees: allEmployees.where((e) => e.isActive).length,
        inactiveEmployees: allEmployees.where((e) => !e.isActive).length,
        newHiresThisMonth: allEmployees.where((e) => e.hireDate.isAfter(firstDayOfMonth)).length,
        terminationsThisMonth: terminationsQuery.docs.length,
        averageYearsOfService: _calculateAverageYearsOfService(allEmployees),
        employeesByDepartment: _groupEmployeesBy(allEmployees, (e) => e.department.value),
        employeesByRole: _groupEmployeesBy(allEmployees, (e) => e.role.value),
        employeesByStatus: _groupEmployeesBy(allEmployees, (e) => e.status.value),
      );

      return Right(ApiResponse.success(
        data: statistics,
        message: 'Statistics retrieved successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get statistics: ${e.toString()}'));
    }
  }

  /// Generate unique employee ID based on department
  Future<String> _generateEmployeeId(Department department) async {
    final prefix = _getDepartmentPrefix(department);
    final year = DateTime.now().year.toString().substring(2);
    
    // Get count of employees in this department
    final departmentEmployeesQuery = await _firestore.collection(_collectionName)
        .where('department', isEqualTo: department.value)
        .get();
    
    final sequence = (departmentEmployeesQuery.docs.length + 1).toString().padLeft(3, '0');
    return '$prefix$year$sequence';
  }

  /// Get department prefix for employee ID
  String _getDepartmentPrefix(Department department) {
    switch (department) {
      case Department.cutting:
        return 'CUT';
      case Department.sewing:
        return 'SEW';
      case Department.finishing:
        return 'FIN';
      case Department.warehouse:
        return 'WH';
      case Department.quality:
        return 'QC';
      default:
        return 'EMP';
    }
  }

  /// Calculate average years of service
  double _calculateAverageYearsOfService(List<Employee> employees) {
    if (employees.isEmpty) return 0.0;
    
    final now = DateTime.now();
    double totalYears = 0.0;
    
    for (final employee in employees) {
      final hireDate = employee.hireDate;
      final years = now.difference(hireDate).inDays / 365.25;
      totalYears += years;
    }
    
    return totalYears / employees.length;
  }

  Map<String, int> _groupEmployeesBy(List<Employee> employees, String Function(Employee) getKey) {
    final map = <String, int>{};
    for (final employee in employees) {
      final key = getKey(employee);
      map[key] = (map[key] ?? 0) + 1;
    }
    return map;
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> getEmployeeByEmployeeId(String employeeId) async {
    try {
      final query = await _firestore.collection(_collectionName)
          .where('employeeId', isEqualTo: employeeId)
          .where('deletedAt', isNull: true)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return const Left(NotFoundFailure('Employee not found'));
      }

      final employee = EmployeeModel.fromFirestore(query.docs.first).toEntity();

      return Right(ApiResponse.success(
        data: employee,
        message: 'Employee retrieved successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get employee: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> activateEmployee(String id) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(id);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Employee not found'));
      }

      await docRef.update({
        'isActive': true,
        'status': CommonStatus.active.value,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': currentUser.uid,
      });

      final updatedDoc = await docRef.get();
      final employee = EmployeeModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse.success(
        data: employee,
        message: 'Employee activated successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to activate employee: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> deactivateEmployee(
    String id, {
    String? reason,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(id);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Employee not found'));
      }

      await docRef.update({
        'isActive': false,
        'status': CommonStatus.inactive.value,
        'deactivationReason': reason ?? 'No reason provided',
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': currentUser.uid,
      });

      final updatedDoc = await docRef.get();
      final employee = EmployeeModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse.success(
        data: employee,
        message: 'Employee deactivated successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to deactivate employee: ${e.toString()}'));
    }
  }

  @override
  Stream<Employee?> getEmployeeStream(String id) {
    try {
      return _firestore.collection(_collectionName).doc(id).snapshots().map((doc) {
        if (!doc.exists) {
          return null;
        }
        return EmployeeModel.fromFirestore(doc).toEntity();
      });
    } catch (e) {
      throw ServerFailure('Failed to get employee stream: ${e.toString()}');
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesByDepartment(
    String departmentId, {
    int limit = 50,
  }) async {
    try {
      final query = await _firestore.collection(_collectionName)
          .where('department', isEqualTo: departmentId)
          .where('deletedAt', isNull: true)
          .limit(limit)
          .get();

      final employees = query.docs
          .map((doc) => EmployeeModel.fromFirestore(doc).toEntity())
          .toList();

      return Right(ApiResponse.success(
        data: employees,
        message: 'Employees retrieved successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get employees by department: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesByRole(
    String roleId, {
    int limit = 50,
  }) async {
    try {
      final query = await _firestore.collection(_collectionName)
          .where('role', isEqualTo: roleId)
          .where('deletedAt', isNull: true)
          .limit(limit)
          .get();

      final employees = query.docs
          .map((doc) => EmployeeModel.fromFirestore(doc).toEntity())
          .toList();

      return Right(ApiResponse.success(
        data: employees,
        message: 'Employees retrieved successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get employees by role: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesBySupervisor(
    String supervisorId, {
    int limit = 50,
  }) async {
    try {
      final query = await _firestore.collection(_collectionName)
          .where('supervisorId', isEqualTo: supervisorId)
          .where('deletedAt', isNull: true)
          .limit(limit)
          .get();

      final employees = query.docs
          .map((doc) => EmployeeModel.fromFirestore(doc).toEntity())
          .toList();

      return Right(ApiResponse.success(
        data: employees,
        message: 'Employees retrieved successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get employees by supervisor: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> transferEmployee(
    String id,
    String newDepartmentId, {
    String? newSupervisorId,
    String? reason,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(id);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Employee not found'));
      }

      final updateData = <String, dynamic>{
        'department': newDepartmentId,
        'transferReason': reason ?? 'Department transfer',
        'transferDate': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': currentUser.uid,
      };

      if (newSupervisorId != null) {
        updateData['supervisorId'] = newSupervisorId;
      }

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      final employee = EmployeeModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse.success(
        data: employee,
        message: 'Employee transferred successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to transfer employee: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> promoteEmployee(
    String id,
    String newRoleId, {
    String? reason,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(id);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Employee not found'));
      }

      // Validate the new role
      final newRole = UserRole.values.firstWhere(
        (r) => r.value == newRoleId,
        orElse: () => throw ArgumentError('Invalid role ID: $newRoleId'),
      );

      final updateData = <String, dynamic>{
        'role': newRoleId,
        'promotionReason': reason ?? 'Role promotion',
        'promotionDate': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': currentUser.uid,
      };

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      final employee = EmployeeModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse.success(
        data: employee,
        message: 'Employee promoted successfully',
      ));
    } catch (e) {
      if (e is ArgumentError) {
        return Left(ValidationFailure(e.message));
      }
      return Left(ServerFailure('Failed to promote employee: ${e.toString()}'));
    }
  }
}
