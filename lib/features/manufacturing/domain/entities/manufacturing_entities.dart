import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../production_planning/domain/entities/production_entities.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../../resource_management/domain/entities/worker_entities.dart';


/// Represents the different work shifts for manufacturing workers
enum WorkerShift {
  /// Morning shift (6:00 AM - 2:00 PM)
  morning,
  
  /// Afternoon shift (2:00 PM - 10:00 PM)
  afternoon,
  
  /// Night shift (10:00 PM - 6:00 AM)
  night,
  
  /// Custom or flexible shift
  flexible,
  
  /// No shift assigned
  notScheduled,
}

/// Manufacturing department entity
class ManufacturingDepartment extends Equatable {
  final String id;
  final String name;
  final String description;
  final String supervisorId;
  final String supervisorName;
  final DepartmentType type;
  final DepartmentStatus status;
  final List<String> workerIds;
  final List<String> machineIds;
  final int capacity;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ManufacturingDepartment({
    required this.id,
    required this.name,
    required this.description,
    required this.supervisorId,
    required this.supervisorName,
    required this.type,
    required this.status,
    required this.workerIds,
    required this.machineIds,
    required this.capacity,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        supervisorId,
        supervisorName,
        type,
        status,
        workerIds,
        machineIds,
        capacity,
        createdAt,
        updatedAt,
      ];

  /// Get active workers count
  int get activeWorkersCount => workerIds.length;

  /// Check if department is operational
  bool get isOperational => status == DepartmentStatus.active;

  /// Get capacity utilization percentage
  double get capacityUtilization => activeWorkersCount / capacity * 100;
}

/// Manufacturing task entity
class ManufacturingTask extends Equatable {
  final String id;
  final String title;
  final String description;
  final String departmentId;
  final String departmentName;
  final String assignedToId;
  final String assignedToName;
  final String assignedById;
  final String assignedByName;
  final TaskType type;
  final TaskPriority priority;
  final TaskStatus status;
  final DateTime deadline;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<TaskAttachment> attachments;
  final List<TaskComment> comments;
  final TaskMetrics? metrics;
  final String? qualityFeedback;
  final double? qualityScore;

  const ManufacturingTask({
    required this.id,
    required this.title,
    required this.description,
    required this.departmentId,
    required this.departmentName,
    required this.assignedToId,
    required this.assignedToName,
    required this.assignedById,
    required this.assignedByName,
    required this.type,
    required this.priority,
    required this.status,
    required this.deadline,
    this.startedAt,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
    required this.attachments,
    required this.comments,
    this.metrics,
    this.qualityFeedback,
    this.qualityScore,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        departmentId,
        departmentName,
        assignedToId,
        assignedToName,
        assignedById,
        assignedByName,
        type,
        priority,
        status,
        deadline,
        startedAt,
        completedAt,
        createdAt,
        updatedAt,
        attachments,
        comments,
        metrics,
        qualityFeedback,
        qualityScore,
      ];

  /// Check if task is overdue
  bool get isOverdue => DateTime.now().isAfter(deadline) && status != TaskStatus.completed;

  /// Get task duration if completed
  Duration? get duration {
    if (startedAt != null && completedAt != null) {
      return completedAt!.difference(startedAt!);
    }
    return null;
  }

  /// Check if task is in progress
  bool get isInProgress => status == TaskStatus.inProgress;

  /// Check if task is completed
  bool get isCompleted => status == TaskStatus.completed;
}

/// Manufacturing worker entity
class ManufacturingWorker extends Equatable {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String employeeId;
  final WorkerRole role;
  final String departmentId;
  final String departmentName;
  final String position;
  final WorkerStatus status;
  final List<String> skills;
  final double experienceYears;
  final double performanceScore;
  final DateTime joinedAt;
  final DateTime? lastActiveAt;
  final WorkerShift currentShift;
  final bool isPresent;

  const ManufacturingWorker({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.employeeId,
    required this.role,
    required this.departmentId,
    required this.departmentName,
    required this.position,
    required this.status,
    required this.skills,
    required this.experienceYears,
    required this.performanceScore,
    required this.joinedAt,
    this.lastActiveAt,
    required this.currentShift,
    required this.isPresent,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        employeeId,
        role,
        departmentId,
        departmentName,
        position,
        status,
        skills,
        experienceYears,
        performanceScore,
        joinedAt,
        lastActiveAt,
        currentShift,
        isPresent,
      ];

  /// Check if worker is available for tasks
  bool get isAvailable => status == WorkerStatus.active && isPresent;

  /// Get worker experience level
  ExperienceLevel get experienceLevel {
    if (experienceYears < 1) return ExperienceLevel.beginner;
    if (experienceYears < 3) return ExperienceLevel.intermediate;
    if (experienceYears < 5) return ExperienceLevel.experienced;
    return ExperienceLevel.expert;
  }
}

/// Task attachment entity
class TaskAttachment extends Equatable {
  final String id;
  final String fileName;
  final String fileType;
  final String fileUrl;
  final int fileSize;
  final DateTime uploadedAt;
  final String uploadedBy;

  const TaskAttachment({
    required this.id,
    required this.fileName,
    required this.fileType,
    required this.fileUrl,
    required this.fileSize,
    required this.uploadedAt,
    required this.uploadedBy,
  });

  @override
  List<Object?> get props => [id, fileName, fileType, fileUrl, fileSize, uploadedAt, uploadedBy];

  factory TaskAttachment.fromJson(Map<String, dynamic> json) {
    return TaskAttachment(
      id: json['id'] ?? '',
      fileName: json['fileName'] ?? '',
      fileType: json['fileType'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
      fileSize: json['fileSize'] ?? 0,
      uploadedAt: (json['uploadedAt'] as Timestamp).toDate(),
      uploadedBy: json['uploadedBy'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'fileType': fileType,
      'fileUrl': fileUrl,
      'fileSize': fileSize,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'uploadedBy': uploadedBy,
    };
  }
}

/// Task comment entity
class TaskComment extends Equatable {
  final String id;
  final String content;
  final String authorId;
  final String authorName;
  final DateTime createdAt;

  const TaskComment({
    required this.id,
    required this.content,
    required this.authorId,
    required this.authorName,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [id, content, authorId, authorName, createdAt];

  factory TaskComment.fromJson(Map<String, dynamic> json) {
    return TaskComment(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      authorId: json['authorId'] ?? '',
      authorName: json['authorName'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'authorId': authorId,
      'authorName': authorName,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

/// Task metrics entity
class TaskMetrics extends Equatable {
  final int piecesProduced;
  final int defectiveItems;
  final double qualityRate;
  final Duration actualTime;
  final Duration estimatedTime;
  final double efficiency;

  const TaskMetrics({
    required this.piecesProduced,
    required this.defectiveItems,
    required this.qualityRate,
    required this.actualTime,
    required this.estimatedTime,
    required this.efficiency,
  });

  @override
  List<Object?> get props => [piecesProduced, defectiveItems, qualityRate, actualTime, estimatedTime, efficiency];

  factory TaskMetrics.fromJson(Map<String, dynamic> json) {
    return TaskMetrics(
      piecesProduced: json['piecesProduced'] ?? 0,
      defectiveItems: json['defectiveItems'] ?? 0,
      qualityRate: (json['qualityRate'] as num?)?.toDouble() ?? 0.0,
      actualTime: Duration(seconds: json['actualTime'] ?? 0),
      estimatedTime: Duration(seconds: json['estimatedTime'] ?? 0),
      efficiency: (json['efficiency'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'piecesProduced': piecesProduced,
      'defectiveItems': defectiveItems,
      'qualityRate': qualityRate,
      'actualTime': actualTime.inSeconds,
      'estimatedTime': estimatedTime.inSeconds,
      'efficiency': efficiency,
    };
  }
}
