import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../../domain/entities/munda_requests.dart';
import '../bloc/munda_bloc.dart';
import '../bloc/munda_event.dart';
import '../bloc/munda_state.dart';
import '../widgets/munda_summary_card.dart';

/// Main data entry screen for Munda operations
class MundaDataEntryPage extends StatefulWidget {
  const MundaDataEntryPage({super.key});

  @override
  State<MundaDataEntryPage> createState() => _MundaDataEntryPageState();
}

class _MundaDataEntryPageState extends State<MundaDataEntryPage> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  
  // Form controllers
  final _designNumberController = TextEditingController();
  final _lotNumberController = TextEditingController();
  final _rollNumberController = TextEditingController();
  final _piecesController = TextEditingController();
  final _rateController = TextEditingController();
  final _checkedByController = TextEditingController();
  final _remarksController = TextEditingController();
  
  // Form state
  DateTime _selectedDate = DateTime.now();
  ThreadType _selectedThread = ThreadType.cotton;
  bool _isLoading = false;
  double _calculatedAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _setupCalculationListeners();
    _loadUserInfo();
  }

  @override
  void dispose() {
    _designNumberController.dispose();
    _lotNumberController.dispose();
    _rollNumberController.dispose();
    _piecesController.dispose();
    _rateController.dispose();
    _checkedByController.dispose();
    _remarksController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupCalculationListeners() {
    _piecesController.addListener(_calculateAmount);
    _rateController.addListener(_calculateAmount);
  }

  void _calculateAmount() {
    final pieces = double.tryParse(_piecesController.text) ?? 0.0;
    final rate = double.tryParse(_rateController.text) ?? 0.0;
    setState(() {
      _calculatedAmount = pieces * rate;
    });
  }

  void _loadUserInfo() {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthAuthenticated) {
      _checkedByController.text = authState.user.displayName;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocListener<MundaBloc, MundaState>(
        listener: (context, state) {
          if (state is MundaOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
              ),
            );
            _clearForm();
          } else if (state is MundaError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.userMessage),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Form content
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Data entry form
                    _buildDataEntryForm(),
                    const SizedBox(height: 24),
                    
                    // Summary card
                    const MundaSummaryCard(),
                    const SizedBox(height: 100), // Space for floating button
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildSaveButton(),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.add_circle_outline,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'New Data Entry',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Enter new Munda record details',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataEntryForm() {
    return Form(
      key: _formKey,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Form title
            Text(
              'Record Details',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            
            // Date field
            _buildDateField(),
            const SizedBox(height: 16),
            
            // Design Number
            CustomTextField(
              controller: _designNumberController,
              label: 'Design Number *',
              hintText: 'Enter design number',
              prefixIcon: Icons.design_services,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Design number is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // Lot Number and Roll Number (Row)
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _lotNumberController,
                    label: 'Lot Number *',
                    hintText: 'Enter lot number',
                    prefixIcon: Icons.inventory,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Lot number is required';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _rollNumberController,
                    label: 'Roll Number *',
                    hintText: 'Enter roll number',
                    prefixIcon: Icons.straighten,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Roll number is required';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Thread Type
            _buildThreadTypeField(),
            const SizedBox(height: 16),
            
            // Pieces and Rate (Row)
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _piecesController,
                    label: 'Pieces *',
                    hintText: 'Enter pieces count',
                    prefixIcon: Icons.format_list_numbered,
                    keyboardType: TextInputType.number,
                    // inputFormatters: [
                    //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    // ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Pieces count is required';
                      }
                      final pieces = double.tryParse(value);
                      if (pieces == null || pieces <= 0) {
                        return 'Enter valid pieces count';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _rateController,
                    label: 'Rate (₹) *',
                    hintText: 'Enter rate per piece',
                    prefixIcon: Icons.currency_rupee,
                    keyboardType: TextInputType.number,
                    // inputFormatters: [
                    //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    // ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Rate is required';
                      }
                      final rate = double.tryParse(value);
                      if (rate == null || rate <= 0) {
                        return 'Enter valid rate';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Calculated Amount (Read-only)
            _buildAmountField(),
            const SizedBox(height: 16),
            
            // Checked By
            CustomTextField(
              controller: _checkedByController,
              label: 'Checked By',
              hintText: 'Enter checker name',
              prefixIcon: Icons.person,
              readOnly: true,
            ),
            const SizedBox(height: 16),
            
            // Remarks
            CustomTextField(
              controller: _remarksController,
              label: 'Remarks',
              hintText: 'Enter any remarks (optional)',
              prefixIcon: Icons.note,
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField() {
    return GestureDetector(
      onTap: _selectDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withAlpha(50)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.calendar_today,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date *',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_drop_down,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreadTypeField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withAlpha(50)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thread Type *',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonHideUnderline(
            child: DropdownButton<ThreadType>(
              value: _selectedThread,
              isExpanded: true,
              onChanged: (ThreadType? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedThread = newValue;
                  });
                }
              },
              items: ThreadType.values.map<DropdownMenuItem<ThreadType>>((ThreadType value) {
                return DropdownMenuItem<ThreadType>(
                  value: value,
                  child: Row(
                    children: [
                      Icon(
                        Icons.fiber_manual_record,
                        color: _getThreadColor(value),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        value.displayName,
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        border: Border.all(color: AppColors.success.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.calculate,
            color: AppColors.success,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Calculated Amount',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.success,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '₹${_calculatedAmount.toStringAsFixed(2)}',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.success,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              'Auto-calculated',
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return BlocBuilder<MundaBloc, MundaState>(
      builder: (context, state) {
        final isLoading = state is MundaLoading;
        
        return FloatingActionButton.extended(
          onPressed: isLoading ? null : _saveRecord,
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          icon: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Icon(Icons.save),
          label: Text(
            isLoading ? 'Saving...' : 'Save Record',
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        );
      },
    );
  }

  Color _getThreadColor(ThreadType threadType) {
    switch (threadType) {
      case ThreadType.cotton:
        return Colors.brown;
      case ThreadType.polyester:
        return Colors.blue;
      case ThreadType.silk:
        return Colors.purple;
      case ThreadType.wool:
        return Colors.orange;
      case ThreadType.linen:
        return Colors.green;
      case ThreadType.nylon:
        return Colors.grey;
      case ThreadType.rayon:
        return Colors.pink;
        throw UnimplementedError();
      case ThreadType.blended:
        return Colors.indigo;
        throw UnimplementedError();
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _saveRecord() {
    if (_formKey.currentState?.validate() ?? false) {
      final authState = context.read<FirebaseAuthBloc>().state;
      if (authState is! FirebaseAuthAuthenticated) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Authentication required'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final request = CreateMundaRecordRequest(
        entryDate: _selectedDate,
        designNumber: _designNumberController.text.trim(),
        lotNumber: _lotNumberController.text.trim(),
        rollNumber: _rollNumberController.text.trim(),
        threadType: _selectedThread,
        pieces: int.parse(_piecesController.text),
        rate: double.parse(_rateController.text),
        checkedBy: _checkedByController.text.trim(),
        remarks: _remarksController.text.trim(),
      );

      context.read<MundaBloc>().add(
        CreateRecord(
          request: request,
          userId: authState.user.id,
        ),
      );
    }
  }

  void _clearForm() {
    _designNumberController.clear();
    _lotNumberController.clear();
    _rollNumberController.clear();
    _piecesController.clear();
    _rateController.clear();
    _remarksController.clear();
    setState(() {
      _selectedDate = DateTime.now();
      _selectedThread = ThreadType.cotton;
      _calculatedAmount = 0.0;
    });
    
    // Scroll to top
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}
