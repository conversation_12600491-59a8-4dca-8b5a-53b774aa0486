import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/cutting_master_entities.dart';

/// Marker model for Firestore
class MarkerModel {
  final String id;
  final String markerNumber;
  final String productId;
  final String productName;
  final String fabricType;
  final double fabricWidth;
  final double markerLength;
  final int piecesPerMarker;
  final List<MarkerPieceModel> pieces;
  final String status;
  final String createdBy;
  final String createdByName;
  final String? approvedBy;
  final String? approvedByName;
  final Timestamp? approvedAt;
  final double efficiency;
  final String? notes;
  final Map<String, dynamic> specifications;
  final Timestamp createdAt;
  final Timestamp updatedAt;
  final Timestamp? deletedAt;

  const MarkerModel({
    required this.id,
    required this.markerNumber,
    required this.productId,
    required this.productName,
    required this.fabricType,
    required this.fabricWidth,
    required this.markerLength,
    required this.piecesPerMarker,
    required this.pieces,
    required this.status,
    required this.createdBy,
    required this.createdByName,
    this.approvedBy,
    this.approvedByName,
    this.approvedAt,
    required this.efficiency,
    this.notes,
    this.specifications = const {},
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Convert from Firestore document
  factory MarkerModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return MarkerModel(
      id: doc.id,
      markerNumber: data['markerNumber'] ?? '',
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      fabricType: data['fabricType'] ?? '',
      fabricWidth: (data['fabricWidth'] ?? 0.0).toDouble(),
      markerLength: (data['markerLength'] ?? 0.0).toDouble(),
      piecesPerMarker: data['piecesPerMarker'] ?? 0,
      pieces: (data['pieces'] as List<dynamic>?)
              ?.map((piece) => MarkerPieceModel.fromMap(piece))
              .toList() ??
          [],
      status: data['status'] ?? 'draft',
      createdBy: data['createdBy'] ?? '',
      createdByName: data['createdByName'] ?? '',
      approvedBy: data['approvedBy'],
      approvedByName: data['approvedByName'],
      approvedAt: data['approvedAt'],
      efficiency: (data['efficiency'] ?? 0.0).toDouble(),
      notes: data['notes'],
      specifications: data['specifications'] ?? {},
      createdAt: data['createdAt'] ?? Timestamp.now(),
      updatedAt: data['updatedAt'] ?? Timestamp.now(),
      deletedAt: data['deletedAt'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'markerNumber': markerNumber,
      'productId': productId,
      'productName': productName,
      'fabricType': fabricType,
      'fabricWidth': fabricWidth,
      'markerLength': markerLength,
      'piecesPerMarker': piecesPerMarker,
      'pieces': pieces.map((piece) => piece.toMap()).toList(),
      'status': status,
      'createdBy': createdBy,
      'createdByName': createdByName,
      'approvedBy': approvedBy,
      'approvedByName': approvedByName,
      'approvedAt': approvedAt,
      'efficiency': efficiency,
      'notes': notes,
      'specifications': specifications,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
    };
  }

  /// Convert to entity
  Marker toEntity() {
    return Marker(
      id: id,
      markerNumber: markerNumber,
      productId: productId,
      productName: productName,
      fabricType: fabricType,
      fabricWidth: fabricWidth,
      markerLength: markerLength,
      piecesPerMarker: piecesPerMarker,
      pieces: pieces.map((piece) => piece.toEntity()).toList(),
      status: _parseMarkerStatus(status),
      createdBy: createdBy,
      createdByName: createdByName,
      approvedBy: approvedBy,
      approvedByName: approvedByName,
      approvedAt: approvedAt?.toDate(),
      efficiency: efficiency,
      notes: notes,
      specifications: specifications,
      createdAt: createdAt.toDate(),
      updatedAt: updatedAt.toDate(),
      deletedAt: deletedAt?.toDate(),
    );
  }

  /// Convert from entity
  factory MarkerModel.fromEntity(Marker entity) {
    return MarkerModel(
      id: entity.id,
      markerNumber: entity.markerNumber,
      productId: entity.productId,
      productName: entity.productName,
      fabricType: entity.fabricType,
      fabricWidth: entity.fabricWidth,
      markerLength: entity.markerLength,
      piecesPerMarker: entity.piecesPerMarker,
      pieces: entity.pieces.map((piece) => MarkerPieceModel.fromEntity(piece)).toList(),
      status: entity.status.name,
      createdBy: entity.createdBy,
      createdByName: entity.createdByName,
      approvedBy: entity.approvedBy,
      approvedByName: entity.approvedByName,
      approvedAt: entity.approvedAt != null ? Timestamp.fromDate(entity.approvedAt!) : null,
      efficiency: entity.efficiency,
      notes: entity.notes,
      specifications: entity.specifications,
      createdAt: Timestamp.fromDate(entity.createdAt),
      updatedAt: Timestamp.fromDate(entity.updatedAt),
      deletedAt: entity.deletedAt != null ? Timestamp.fromDate(entity.deletedAt!) : null,
    );
  }

  static MarkerStatus _parseMarkerStatus(String status) {
    switch (status) {
      case 'draft':
        return MarkerStatus.draft;
      case 'pending':
        return MarkerStatus.pending;
      case 'approved':
        return MarkerStatus.approved;
      case 'rejected':
        return MarkerStatus.rejected;
      case 'archived':
        return MarkerStatus.archived;
      default:
        return MarkerStatus.draft;
    }
  }
}

/// Marker piece model
class MarkerPieceModel {
  final String pieceId;
  final String pieceName;
  final String size;
  final int quantity;
  final double xPosition;
  final double yPosition;
  final double width;
  final double height;
  final double rotation;

  const MarkerPieceModel({
    required this.pieceId,
    required this.pieceName,
    required this.size,
    required this.quantity,
    required this.xPosition,
    required this.yPosition,
    required this.width,
    required this.height,
    this.rotation = 0.0,
  });

  /// Convert from map
  factory MarkerPieceModel.fromMap(Map<String, dynamic> map) {
    return MarkerPieceModel(
      pieceId: map['pieceId'] ?? '',
      pieceName: map['pieceName'] ?? '',
      size: map['size'] ?? '',
      quantity: map['quantity'] ?? 0,
      xPosition: (map['xPosition'] ?? 0.0).toDouble(),
      yPosition: (map['yPosition'] ?? 0.0).toDouble(),
      width: (map['width'] ?? 0.0).toDouble(),
      height: (map['height'] ?? 0.0).toDouble(),
      rotation: (map['rotation'] ?? 0.0).toDouble(),
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'pieceId': pieceId,
      'pieceName': pieceName,
      'size': size,
      'quantity': quantity,
      'xPosition': xPosition,
      'yPosition': yPosition,
      'width': width,
      'height': height,
      'rotation': rotation,
    };
  }

  /// Convert to entity
  MarkerPiece toEntity() {
    return MarkerPiece(
      pieceId: pieceId,
      pieceName: pieceName,
      size: size,
      quantity: quantity,
      xPosition: xPosition,
      yPosition: yPosition,
      width: width,
      height: height,
      rotation: rotation,
    );
  }

  /// Convert from entity
  factory MarkerPieceModel.fromEntity(MarkerPiece entity) {
    return MarkerPieceModel(
      pieceId: entity.pieceId,
      pieceName: entity.pieceName,
      size: entity.size,
      quantity: entity.quantity,
      xPosition: entity.xPosition,
      yPosition: entity.yPosition,
      width: entity.width,
      height: entity.height,
      rotation: entity.rotation,
    );
  }
}

/// Cutting task model for Firestore
class CuttingTaskModel {
  final String id;
  final String taskNumber;
  final String markerId;
  final String markerNumber;
  final String productId;
  final String productName;
  final String fabricType;
  final int plannedQuantity;
  final int actualQuantity;
  final String status;
  final String assignedTo;
  final String assignedToName;
  final String assignedBy;
  final String assignedByName;
  final Timestamp plannedStartDate;
  final Timestamp plannedEndDate;
  final Timestamp? actualStartDate;
  final Timestamp? actualEndDate;
  final List<CuttingPieceModel> cutPieces;
  final List<QualityCheckModel> qualityChecks;
  final String? notes;
  final Map<String, dynamic> metadata;
  final Timestamp createdAt;
  final Timestamp updatedAt;
  final Timestamp? deletedAt;

  const CuttingTaskModel({
    required this.id,
    required this.taskNumber,
    required this.markerId,
    required this.markerNumber,
    required this.productId,
    required this.productName,
    required this.fabricType,
    required this.plannedQuantity,
    this.actualQuantity = 0,
    required this.status,
    required this.assignedTo,
    required this.assignedToName,
    required this.assignedBy,
    required this.assignedByName,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.cutPieces = const [],
    this.qualityChecks = const [],
    this.notes,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Convert from Firestore document
  factory CuttingTaskModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CuttingTaskModel(
      id: doc.id,
      taskNumber: data['taskNumber'] ?? '',
      markerId: data['markerId'] ?? '',
      markerNumber: data['markerNumber'] ?? '',
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      fabricType: data['fabricType'] ?? '',
      plannedQuantity: data['plannedQuantity'] ?? 0,
      actualQuantity: data['actualQuantity'] ?? 0,
      status: data['status'] ?? 'pending',
      assignedTo: data['assignedTo'] ?? '',
      assignedToName: data['assignedToName'] ?? '',
      assignedBy: data['assignedBy'] ?? '',
      assignedByName: data['assignedByName'] ?? '',
      plannedStartDate: data['plannedStartDate'] ?? Timestamp.now(),
      plannedEndDate: data['plannedEndDate'] ?? Timestamp.now(),
      actualStartDate: data['actualStartDate'],
      actualEndDate: data['actualEndDate'],
      cutPieces: (data['cutPieces'] as List<dynamic>?)
              ?.map((piece) => CuttingPieceModel.fromMap(piece))
              .toList() ??
          [],
      qualityChecks: (data['qualityChecks'] as List<dynamic>?)
              ?.map((check) => QualityCheckModel.fromMap(check))
              .toList() ??
          [],
      notes: data['notes'],
      metadata: data['metadata'] ?? {},
      createdAt: data['createdAt'] ?? Timestamp.now(),
      updatedAt: data['updatedAt'] ?? Timestamp.now(),
      deletedAt: data['deletedAt'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'taskNumber': taskNumber,
      'markerId': markerId,
      'markerNumber': markerNumber,
      'productId': productId,
      'productName': productName,
      'fabricType': fabricType,
      'plannedQuantity': plannedQuantity,
      'actualQuantity': actualQuantity,
      'status': status,
      'assignedTo': assignedTo,
      'assignedToName': assignedToName,
      'assignedBy': assignedBy,
      'assignedByName': assignedByName,
      'plannedStartDate': plannedStartDate,
      'plannedEndDate': plannedEndDate,
      'actualStartDate': actualStartDate,
      'actualEndDate': actualEndDate,
      'cutPieces': cutPieces.map((piece) => piece.toMap()).toList(),
      'qualityChecks': qualityChecks.map((check) => check.toMap()).toList(),
      'notes': notes,
      'metadata': metadata,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
    };
  }

  /// Convert to entity
  CuttingTask toEntity() {
    return CuttingTask(
      id: id,
      taskNumber: taskNumber,
      markerId: markerId,
      markerNumber: markerNumber,
      productId: productId,
      productName: productName,
      fabricType: fabricType,
      plannedQuantity: plannedQuantity,
      actualQuantity: actualQuantity,
      status: _parseCuttingTaskStatus(status),
      assignedTo: assignedTo,
      assignedToName: assignedToName,
      assignedBy: assignedBy,
      assignedByName: assignedByName,
      plannedStartDate: plannedStartDate.toDate(),
      plannedEndDate: plannedEndDate.toDate(),
      actualStartDate: actualStartDate?.toDate(),
      actualEndDate: actualEndDate?.toDate(),
      cutPieces: cutPieces.map((piece) => piece.toEntity()).toList(),
      qualityChecks: qualityChecks.map((check) => check.toEntity()).toList(),
      notes: notes,
      metadata: metadata,
      createdAt: createdAt.toDate(),
      updatedAt: updatedAt.toDate(),
      deletedAt: deletedAt?.toDate(),
    );
  }

  /// Convert from entity
  factory CuttingTaskModel.fromEntity(CuttingTask entity) {
    return CuttingTaskModel(
      id: entity.id,
      taskNumber: entity.taskNumber,
      markerId: entity.markerId,
      markerNumber: entity.markerNumber,
      productId: entity.productId,
      productName: entity.productName,
      fabricType: entity.fabricType,
      plannedQuantity: entity.plannedQuantity,
      actualQuantity: entity.actualQuantity,
      status: entity.status.name,
      assignedTo: entity.assignedTo,
      assignedToName: entity.assignedToName,
      assignedBy: entity.assignedBy,
      assignedByName: entity.assignedByName,
      plannedStartDate: Timestamp.fromDate(entity.plannedStartDate),
      plannedEndDate: Timestamp.fromDate(entity.plannedEndDate),
      actualStartDate: entity.actualStartDate != null ? Timestamp.fromDate(entity.actualStartDate!) : null,
      actualEndDate: entity.actualEndDate != null ? Timestamp.fromDate(entity.actualEndDate!) : null,
      cutPieces: entity.cutPieces.map((piece) => CuttingPieceModel.fromEntity(piece)).toList(),
      qualityChecks: entity.qualityChecks.map((check) => QualityCheckModel.fromEntity(check)).toList(),
      notes: entity.notes,
      metadata: entity.metadata,
      createdAt: Timestamp.fromDate(entity.createdAt),
      updatedAt: Timestamp.fromDate(entity.updatedAt),
      deletedAt: entity.deletedAt != null ? Timestamp.fromDate(entity.deletedAt!) : null,
    );
  }

  static CuttingTaskStatus _parseCuttingTaskStatus(String status) {
    switch (status) {
      case 'pending':
        return CuttingTaskStatus.pending;
      case 'inProgress':
        return CuttingTaskStatus.inProgress;
      case 'completed':
        return CuttingTaskStatus.completed;
      case 'onHold':
        return CuttingTaskStatus.onHold;
      case 'cancelled':
        return CuttingTaskStatus.cancelled;
      default:
        return CuttingTaskStatus.pending;
    }
  }
}

/// Cutting piece model
class CuttingPieceModel {
  final String pieceId;
  final String pieceName;
  final String size;
  final int quantity;
  final String status;
  final double? qualityScore;
  final String? qualityNotes;
  final Timestamp cutAt;
  final String cutBy;

  const CuttingPieceModel({
    required this.pieceId,
    required this.pieceName,
    required this.size,
    required this.quantity,
    required this.status,
    this.qualityScore,
    this.qualityNotes,
    required this.cutAt,
    required this.cutBy,
  });

  /// Convert from map
  factory CuttingPieceModel.fromMap(Map<String, dynamic> map) {
    return CuttingPieceModel(
      pieceId: map['pieceId'] ?? '',
      pieceName: map['pieceName'] ?? '',
      size: map['size'] ?? '',
      quantity: map['quantity'] ?? 0,
      status: map['status'] ?? 'pending',
      qualityScore: map['qualityScore']?.toDouble(),
      qualityNotes: map['qualityNotes'],
      cutAt: map['cutAt'] ?? Timestamp.now(),
      cutBy: map['cutBy'] ?? '',
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'pieceId': pieceId,
      'pieceName': pieceName,
      'size': size,
      'quantity': quantity,
      'status': status,
      'qualityScore': qualityScore,
      'qualityNotes': qualityNotes,
      'cutAt': cutAt,
      'cutBy': cutBy,
    };
  }

  /// Convert to entity
  CuttingPiece toEntity() {
    return CuttingPiece(
      pieceId: pieceId,
      pieceName: pieceName,
      size: size,
      quantity: quantity,
      status: _parseCuttingPieceStatus(status),
      qualityScore: qualityScore,
      qualityNotes: qualityNotes,
      cutAt: cutAt.toDate(),
      cutBy: cutBy,
    );
  }

  /// Convert from entity
  factory CuttingPieceModel.fromEntity(CuttingPiece entity) {
    return CuttingPieceModel(
      pieceId: entity.pieceId,
      pieceName: entity.pieceName,
      size: entity.size,
      quantity: entity.quantity,
      status: entity.status.name,
      qualityScore: entity.qualityScore,
      qualityNotes: entity.qualityNotes,
      cutAt: Timestamp.fromDate(entity.cutAt),
      cutBy: entity.cutBy,
    );
  }

  static CuttingPieceStatus _parseCuttingPieceStatus(String status) {
    switch (status) {
      case 'pending':
        return CuttingPieceStatus.pending;
      case 'cut':
        return CuttingPieceStatus.cut;
      case 'approved':
        return CuttingPieceStatus.approved;
      case 'rejected':
        return CuttingPieceStatus.rejected;
      case 'rework':
        return CuttingPieceStatus.rework;
      default:
        return CuttingPieceStatus.pending;
    }
  }
}

/// Quality check model
class QualityCheckModel {
  final String checkId;
  final String checkType;
  final String status;
  final double score;
  final String checkedBy;
  final String checkedByName;
  final Timestamp checkedAt;
  final String? notes;
  final List<String> defects;

  const QualityCheckModel({
    required this.checkId,
    required this.checkType,
    required this.status,
    required this.score,
    required this.checkedBy,
    required this.checkedByName,
    required this.checkedAt,
    this.notes,
    this.defects = const [],
  });

  /// Convert from map
  factory QualityCheckModel.fromMap(Map<String, dynamic> map) {
    return QualityCheckModel(
      checkId: map['checkId'] ?? '',
      checkType: map['checkType'] ?? '',
      status: map['status'] ?? 'pending',
      score: (map['score'] ?? 0.0).toDouble(),
      checkedBy: map['checkedBy'] ?? '',
      checkedByName: map['checkedByName'] ?? '',
      checkedAt: map['checkedAt'] ?? Timestamp.now(),
      notes: map['notes'],
      defects: List<String>.from(map['defects'] ?? []),
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'checkId': checkId,
      'checkType': checkType,
      'status': status,
      'score': score,
      'checkedBy': checkedBy,
      'checkedByName': checkedByName,
      'checkedAt': checkedAt,
      'notes': notes,
      'defects': defects,
    };
  }

  /// Convert to entity
  QualityCheck toEntity() {
    return QualityCheck(
      checkId: checkId,
      checkType: checkType,
      status: _parseQualityCheckStatus(status),
      score: score,
      checkedBy: checkedBy,
      checkedByName: checkedByName,
      checkedAt: checkedAt.toDate(),
      notes: notes,
      defects: defects,
    );
  }

  /// Convert from entity
  factory QualityCheckModel.fromEntity(QualityCheck entity) {
    return QualityCheckModel(
      checkId: entity.checkId,
      checkType: entity.checkType,
      status: entity.status.name,
      score: entity.score,
      checkedBy: entity.checkedBy,
      checkedByName: entity.checkedByName,
      checkedAt: Timestamp.fromDate(entity.checkedAt),
      notes: entity.notes,
      defects: entity.defects,
    );
  }

  static QualityCheckStatus _parseQualityCheckStatus(String status) {
    switch (status) {
      case 'pending':
        return QualityCheckStatus.pending;
      case 'passed':
        return QualityCheckStatus.passed;
      case 'failed':
        return QualityCheckStatus.failed;
      case 'rework':
        return QualityCheckStatus.rework;
      default:
        return QualityCheckStatus.pending;
    }
  }
}

/// Fabric calculation model
class FabricCalculationModel {
  final String id;
  final String calculationNumber;
  final String productId;
  final String productName;
  final String fabricType;
  final double fabricWidth;
  final Map<String, int> sizeQuantities;
  final double totalFabricRequired;
  final double wastagePercentage;
  final double totalFabricWithWastage;
  final String calculatedBy;
  final String calculatedByName;
  final String? approvedBy;
  final String? approvedByName;
  final Timestamp? approvedAt;
  final String status;
  final String? notes;
  final Timestamp createdAt;
  final Timestamp updatedAt;
  final Timestamp? deletedAt;

  const FabricCalculationModel({
    required this.id,
    required this.calculationNumber,
    required this.productId,
    required this.productName,
    required this.fabricType,
    required this.fabricWidth,
    required this.sizeQuantities,
    required this.totalFabricRequired,
    required this.wastagePercentage,
    required this.totalFabricWithWastage,
    required this.calculatedBy,
    required this.calculatedByName,
    this.approvedBy,
    this.approvedByName,
    this.approvedAt,
    required this.status,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Convert from Firestore document
  factory FabricCalculationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return FabricCalculationModel(
      id: doc.id,
      calculationNumber: data['calculationNumber'] ?? '',
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      fabricType: data['fabricType'] ?? '',
      fabricWidth: (data['fabricWidth'] ?? 0.0).toDouble(),
      sizeQuantities: Map<String, int>.from(data['sizeQuantities'] ?? {}),
      totalFabricRequired: (data['totalFabricRequired'] ?? 0.0).toDouble(),
      wastagePercentage: (data['wastagePercentage'] ?? 0.0).toDouble(),
      totalFabricWithWastage: (data['totalFabricWithWastage'] ?? 0.0).toDouble(),
      calculatedBy: data['calculatedBy'] ?? '',
      calculatedByName: data['calculatedByName'] ?? '',
      approvedBy: data['approvedBy'],
      approvedByName: data['approvedByName'],
      approvedAt: data['approvedAt'],
      status: data['status'] ?? 'draft',
      notes: data['notes'],
      createdAt: data['createdAt'] ?? Timestamp.now(),
      updatedAt: data['updatedAt'] ?? Timestamp.now(),
      deletedAt: data['deletedAt'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'calculationNumber': calculationNumber,
      'productId': productId,
      'productName': productName,
      'fabricType': fabricType,
      'fabricWidth': fabricWidth,
      'sizeQuantities': sizeQuantities,
      'totalFabricRequired': totalFabricRequired,
      'wastagePercentage': wastagePercentage,
      'totalFabricWithWastage': totalFabricWithWastage,
      'calculatedBy': calculatedBy,
      'calculatedByName': calculatedByName,
      'approvedBy': approvedBy,
      'approvedByName': approvedByName,
      'approvedAt': approvedAt,
      'status': status,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
    };
  }

  /// Convert to entity
  FabricCalculation toEntity() {
    return FabricCalculation(
      id: id,
      calculationNumber: calculationNumber,
      productId: productId,
      productName: productName,
      fabricType: fabricType,
      fabricWidth: fabricWidth,
      sizeQuantities: sizeQuantities,
      totalFabricRequired: totalFabricRequired,
      wastagePercentage: wastagePercentage,
      totalFabricWithWastage: totalFabricWithWastage,
      calculatedBy: calculatedBy,
      calculatedByName: calculatedByName,
      approvedBy: approvedBy,
      approvedByName: approvedByName,
      approvedAt: approvedAt?.toDate(),
      status: _parseFabricCalculationStatus(status),
      notes: notes,
      createdAt: createdAt.toDate(),
      updatedAt: updatedAt.toDate(),
      deletedAt: deletedAt?.toDate(),
    );
  }

  /// Convert from entity
  factory FabricCalculationModel.fromEntity(FabricCalculation entity) {
    return FabricCalculationModel(
      id: entity.id,
      calculationNumber: entity.calculationNumber,
      productId: entity.productId,
      productName: entity.productName,
      fabricType: entity.fabricType,
      fabricWidth: entity.fabricWidth,
      sizeQuantities: entity.sizeQuantities,
      totalFabricRequired: entity.totalFabricRequired,
      wastagePercentage: entity.wastagePercentage,
      totalFabricWithWastage: entity.totalFabricWithWastage,
      calculatedBy: entity.calculatedBy,
      calculatedByName: entity.calculatedByName,
      approvedBy: entity.approvedBy,
      approvedByName: entity.approvedByName,
      approvedAt: entity.approvedAt != null ? Timestamp.fromDate(entity.approvedAt!) : null,
      status: entity.status.name,
      notes: entity.notes,
      createdAt: Timestamp.fromDate(entity.createdAt),
      updatedAt: Timestamp.fromDate(entity.updatedAt),
      deletedAt: entity.deletedAt != null ? Timestamp.fromDate(entity.deletedAt!) : null,
    );
  }

  static FabricCalculationStatus _parseFabricCalculationStatus(String status) {
    switch (status) {
      case 'draft':
        return FabricCalculationStatus.draft;
      case 'pending':
        return FabricCalculationStatus.pending;
      case 'approved':
        return FabricCalculationStatus.approved;
      case 'rejected':
        return FabricCalculationStatus.rejected;
      default:
        return FabricCalculationStatus.draft;
    }
  }
}

/// Production log entry model
class ProductionLogEntryModel {
  final String id;
  final String logNumber;
  final String taskId;
  final String taskType;
  final String operatorId;
  final String operatorName;
  final Timestamp startTime;
  final Timestamp? endTime;
  final int quantityProduced;
  final double? qualityScore;
  final String? notes;
  final Map<String, dynamic> metrics;
  final Timestamp createdAt;
  final Timestamp updatedAt;
  final Timestamp? deletedAt;

  const ProductionLogEntryModel({
    required this.id,
    required this.logNumber,
    required this.taskId,
    required this.taskType,
    required this.operatorId,
    required this.operatorName,
    required this.startTime,
    this.endTime,
    required this.quantityProduced,
    this.qualityScore,
    this.notes,
    this.metrics = const {},
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Convert from Firestore document
  factory ProductionLogEntryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ProductionLogEntryModel(
      id: doc.id,
      logNumber: data['logNumber'] ?? '',
      taskId: data['taskId'] ?? '',
      taskType: data['taskType'] ?? '',
      operatorId: data['operatorId'] ?? '',
      operatorName: data['operatorName'] ?? '',
      startTime: data['startTime'] ?? Timestamp.now(),
      endTime: data['endTime'],
      quantityProduced: data['quantityProduced'] ?? 0,
      qualityScore: data['qualityScore']?.toDouble(),
      notes: data['notes'],
      metrics: data['metrics'] ?? {},
      createdAt: data['createdAt'] ?? Timestamp.now(),
      updatedAt: data['updatedAt'] ?? Timestamp.now(),
      deletedAt: data['deletedAt'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'logNumber': logNumber,
      'taskId': taskId,
      'taskType': taskType,
      'operatorId': operatorId,
      'operatorName': operatorName,
      'startTime': startTime,
      'endTime': endTime,
      'quantityProduced': quantityProduced,
      'qualityScore': qualityScore,
      'notes': notes,
      'metrics': metrics,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
    };
  }

  /// Convert to entity
  ProductionLogEntry toEntity() {
    return ProductionLogEntry(
      id: id,
      logNumber: logNumber,
      taskId: taskId,
      taskType: taskType,
      operatorId: operatorId,
      operatorName: operatorName,
      startTime: startTime.toDate(),
      endTime: endTime?.toDate(),
      quantityProduced: quantityProduced,
      qualityScore: qualityScore,
      notes: notes,
      metrics: metrics,
      createdAt: createdAt.toDate(),
      updatedAt: updatedAt.toDate(),
      deletedAt: deletedAt?.toDate(),
    );
  }

  /// Convert from entity
  factory ProductionLogEntryModel.fromEntity(ProductionLogEntry entity) {
    return ProductionLogEntryModel(
      id: entity.id,
      logNumber: entity.logNumber,
      taskId: entity.taskId,
      taskType: entity.taskType,
      operatorId: entity.operatorId,
      operatorName: entity.operatorName,
      startTime: Timestamp.fromDate(entity.startTime),
      endTime: entity.endTime != null ? Timestamp.fromDate(entity.endTime!) : null,
      quantityProduced: entity.quantityProduced,
      qualityScore: entity.qualityScore,
      notes: entity.notes,
      metrics: entity.metrics,
      createdAt: Timestamp.fromDate(entity.createdAt),
      updatedAt: Timestamp.fromDate(entity.updatedAt),
      deletedAt: entity.deletedAt != null ? Timestamp.fromDate(entity.deletedAt!) : null,
    );
  }
}
