import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hm_collection/features/production_planning/domain/entities/production_entities.dart';
import 'package:hm_collection/shared/enums/common_enums.dart';

import '../../../manufacturing/domain/entities/manufacturing_entities.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';

/// Production Order Model for Firestore serialization
class ProductionOrderModel {
  final String id;
  final String productionOrderNumber;
  final String manufacturingOrderId;
  final String orderNumber;
  final String clientName;
  final String status;
  final String priority;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<Map<String, dynamic>> tasks;
  final List<Map<String, dynamic>> resourceAllocations;
  final Map<String, dynamic> metrics;
  final List<Map<String, dynamic>> notes;
  final Map<String, dynamic> metadata;
  final String? assignedSupervisor;
  final String currentDepartment;
  final double completionPercentage;
  final int totalQuantity;
  final int completedQuantity;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final String? createdBy;
  final String? updatedBy;

  const ProductionOrderModel({
    required this.id,
    required this.productionOrderNumber,
    required this.manufacturingOrderId,
    required this.orderNumber,
    required this.clientName,
    required this.status,
    required this.priority,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    required this.tasks,
    required this.resourceAllocations,
    required this.metrics,
    this.notes = const [],
    this.metadata = const {},
    this.assignedSupervisor,
    required this.currentDepartment,
    this.completionPercentage = 0.0,
    required this.totalQuantity,
    this.completedQuantity = 0,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
  });

  /// Convert from Firestore document
  factory ProductionOrderModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ProductionOrderModel.fromMap(data, doc.id);
  }

  /// Convert from Map
  factory ProductionOrderModel.fromMap(Map<String, dynamic> map, String id) {
    return ProductionOrderModel(
      id: id,
      productionOrderNumber: map['productionOrderNumber'] ?? '',
      manufacturingOrderId: map['manufacturingOrderId'] ?? '',
      orderNumber: map['orderNumber'] ?? '',
      clientName: map['clientName'] ?? '',
      status: map['status'] ?? ProductionStatus.planned.name,
      priority: map['priority'] ?? ProductionPriority.normal.name,
      plannedStartDate: (map['plannedStartDate'] as Timestamp).toDate(),
      plannedEndDate: (map['plannedEndDate'] as Timestamp).toDate(),
      actualStartDate: map['actualStartDate'] != null 
          ? (map['actualStartDate'] as Timestamp).toDate() 
          : null,
      actualEndDate: map['actualEndDate'] != null 
          ? (map['actualEndDate'] as Timestamp).toDate() 
          : null,
      tasks: List<Map<String, dynamic>>.from(map['tasks'] ?? []),
      resourceAllocations: List<Map<String, dynamic>>.from(map['resourceAllocations'] ?? []),
      metrics: Map<String, dynamic>.from(map['metrics'] ?? {}),
      notes: List<Map<String, dynamic>>.from(map['notes'] ?? []),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      assignedSupervisor: map['assignedSupervisor'],
      currentDepartment: map['currentDepartment'] ?? DepartmentType.cutting.name,
      completionPercentage: (map['completionPercentage'] ?? 0.0).toDouble(),
      totalQuantity: map['totalQuantity'] ?? 0,
      completedQuantity: map['completedQuantity'] ?? 0,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      deletedAt: map['deletedAt'] != null 
          ? (map['deletedAt'] as Timestamp).toDate() 
          : null,
      createdBy: map['createdBy'],
      updatedBy: map['updatedBy'],
    );
  }

  /// Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'productionOrderNumber': productionOrderNumber,
      'manufacturingOrderId': manufacturingOrderId,
      'orderNumber': orderNumber,
      'clientName': clientName,
      'status': status,
      'priority': priority,
      'plannedStartDate': Timestamp.fromDate(plannedStartDate),
      'plannedEndDate': Timestamp.fromDate(plannedEndDate),
      'actualStartDate': actualStartDate != null 
          ? Timestamp.fromDate(actualStartDate!) 
          : null,
      'actualEndDate': actualEndDate != null 
          ? Timestamp.fromDate(actualEndDate!) 
          : null,
      'tasks': tasks,
      'resourceAllocations': resourceAllocations,
      'metrics': metrics,
      'notes': notes,
      'metadata': metadata,
      'assignedSupervisor': assignedSupervisor,
      'currentDepartment': currentDepartment,
      'completionPercentage': completionPercentage,
      'totalQuantity': totalQuantity,
      'completedQuantity': completedQuantity,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  /// Convert to domain entity
  ProductionOrder toEntity() {
    return ProductionOrder(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
      productionOrderNumber: productionOrderNumber,
      manufacturingOrderId: manufacturingOrderId,
      orderNumber: orderNumber,
      clientName: clientName,
      status: ProductionStatus.values.byName(status),
      priority: ProductionPriority.values.byName(priority),
      plannedStartDate: plannedStartDate,
      plannedEndDate: plannedEndDate,
      actualStartDate: actualStartDate,
      actualEndDate: actualEndDate,
      tasks: _convertTasks(tasks),
      resourceAllocations: _convertResourceAllocations(resourceAllocations),
      metrics: _convertMetrics(metrics),
      notes: _convertNotes(notes),
      metadata: metadata,
      assignedSupervisor: assignedSupervisor,
      currentDepartment: DepartmentType.values.byName(currentDepartment),
      completionPercentage: completionPercentage,
      totalQuantity: totalQuantity,
      completedQuantity: completedQuantity,
    );
  }

  /// Convert from domain entity
  factory ProductionOrderModel.fromEntity(ProductionOrder entity) {
    return ProductionOrderModel(
      id: entity.id,
      productionOrderNumber: entity.productionOrderNumber,
      manufacturingOrderId: entity.manufacturingOrderId,
      orderNumber: entity.orderNumber,
      clientName: entity.clientName,
      status: entity.status.name,
      priority: entity.priority.name,
      plannedStartDate: entity.plannedStartDate,
      plannedEndDate: entity.plannedEndDate,
      actualStartDate: entity.actualStartDate,
      actualEndDate: entity.actualEndDate,
      tasks: _convertTasksToMap(entity.tasks),
      resourceAllocations: _convertResourceAllocationsToMap(entity.resourceAllocations),
      metrics: _convertMetricsToMap(entity.metrics),
      notes: _convertNotesToMap(entity.notes),
      metadata: entity.metadata,
      assignedSupervisor: entity.assignedSupervisor,
      currentDepartment: entity.currentDepartment.name,
      completionPercentage: entity.completionPercentage,
      totalQuantity: entity.totalQuantity,
      completedQuantity: entity.completedQuantity,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    );
  }

  // Helper methods for conversion
  static List<ProductionTask> _convertTasks(List<Map<String, dynamic>> tasks) {
    // Simplified conversion - implement full conversion based on your needs
    return [];
  }

  static List<ResourceAllocation> _convertResourceAllocations(List<Map<String, dynamic>> allocations) {
    // Simplified conversion - implement full conversion based on your needs
    return [];
  }

  static ProductionMetrics _convertMetrics(Map<String, dynamic> metrics) {
    // Simplified conversion - implement full conversion based on your needs
    return const ProductionMetrics(
      efficiency: 0.0,
      qualityScore: 0.0,
      onTimeDelivery: 0.0,
      resourceUtilization: 0.0,
      costVariance: 0.0,
      scheduleVariance: 0.0,
      departmentEfficiency: {},
      plannedEfficiency: 0.0,
      actualEfficiency: 0.0,
      defectCount: 0,
      reworkCount: 0,
    );
  }

  static List<ProductionNote> _convertNotes(List<Map<String, dynamic>> notes) {
    // Simplified conversion - implement full conversion based on your needs
    return [];
  }

  static List<Map<String, dynamic>> _convertTasksToMap(List<ProductionTask> tasks) {
    // Simplified conversion - implement full conversion based on your needs
    return [];
  }

  static List<Map<String, dynamic>> _convertResourceAllocationsToMap(List<ResourceAllocation> allocations) {
    // Simplified conversion - implement full conversion based on your needs
    return [];
  }

  static Map<String, dynamic> _convertMetricsToMap(ProductionMetrics metrics) {
    return {
      'efficiency': metrics.efficiency,
      'qualityScore': metrics.qualityScore,
      'onTimeDelivery': metrics.onTimeDelivery,
      'resourceUtilization': metrics.resourceUtilization,
      'costVariance': metrics.costVariance,
      'scheduleVariance': metrics.scheduleVariance,
    };
  }

  static List<Map<String, dynamic>> _convertNotesToMap(List<ProductionNote> notes) {
    // Simplified conversion - implement full conversion based on your needs
    return [];
  }
}
