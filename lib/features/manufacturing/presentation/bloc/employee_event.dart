part of 'employee_bloc.dart';

/// Base class for all employee events
abstract class EmployeeEvent extends Equatable {
  const EmployeeEvent();

  @override
  List<Object?> get props => [];
}

/// Load employees event
class LoadEmployeesRequested extends EmployeeEvent {
  final EmployeeFilterCriteria? filter;
  final int limit;

  const LoadEmployeesRequested({
    this.filter,
    this.limit = 50,
  });

  @override
  List<Object?> get props => [filter, limit];
}

/// Refresh employees event
class RefreshEmployeesRequested extends EmployeeEvent {
  final EmployeeFilterCriteria? filter;
  final int limit;

  const RefreshEmployeesRequested({
    this.filter,
    this.limit = 50,
  });

  @override
  List<Object?> get props => [filter, limit];
}

/// Load employee details event
class LoadEmployeeDetailsRequested extends EmployeeEvent {
  final String employeeId;

  const LoadEmployeeDetailsRequested(this.employeeId);

  @override
  List<Object?> get props => [employeeId];
}

/// Create employee event
class CreateEmployeeRequested extends EmployeeEvent {
  final CreateEmployeeRequest request;

  const CreateEmployeeRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update employee event
class UpdateEmployeeRequested extends EmployeeEvent {
  final UpdateEmployeeRequest request;

  const UpdateEmployeeRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete employee event
class DeleteEmployeeRequested extends EmployeeEvent {
  final String employeeId;
  final String? reason;

  const DeleteEmployeeRequested(this.employeeId, {this.reason});

  @override
  List<Object?> get props => [employeeId, reason];
}

/// Search employees event
class SearchEmployeesRequested extends EmployeeEvent {
  final String query;
  final EmployeeFilterCriteria? filter;
  final int limit;

  const SearchEmployeesRequested(
    this.query, {
    this.filter,
    this.limit = 20,
  });

  @override
  List<Object?> get props => [query, filter, limit];
}

/// Filter employees event
class FilterEmployeesRequested extends EmployeeEvent {
  final EmployeeFilterCriteria filter;
  final int limit;

  const FilterEmployeesRequested(
    this.filter, {
    this.limit = 50,
  });

  @override
  List<Object?> get props => [filter, limit];
}

/// Load employees by department event
class LoadEmployeesByDepartmentRequested extends EmployeeEvent {
  final Department department;
  final int limit;

  const LoadEmployeesByDepartmentRequested(
    this.department, {
    this.limit = 50,
  });

  @override
  List<Object?> get props => [department, limit];
}

/// Load employees by role event
class LoadEmployeesByRoleRequested extends EmployeeEvent {
  final UserRole role;
  final int limit;

  const LoadEmployeesByRoleRequested(
    this.role, {
    this.limit = 50,
  });

  @override
  List<Object?> get props => [role, limit];
}

/// Load employees by supervisor event
class LoadEmployeesBySupervisorRequested extends EmployeeEvent {
  final String supervisorId;
  final int limit;

  const LoadEmployeesBySupervisorRequested(
    this.supervisorId, {
    this.limit = 50,
  });

  @override
  List<Object?> get props => [supervisorId, limit];
}

/// Activate employee event
class ActivateEmployeeRequested extends EmployeeEvent {
  final String employeeId;

  const ActivateEmployeeRequested(this.employeeId);

  @override
  List<Object?> get props => [employeeId];
}

/// Deactivate employee event
class DeactivateEmployeeRequested extends EmployeeEvent {
  final String employeeId;
  final String? reason;

  const DeactivateEmployeeRequested(this.employeeId, {this.reason});

  @override
  List<Object?> get props => [employeeId, reason];
}

/// Transfer employee event
class TransferEmployeeRequested extends EmployeeEvent {
  final String employeeId;
  final Department newDepartment;
  final String? newSupervisorId;
  final String? reason;

  const TransferEmployeeRequested(
    this.employeeId,
    this.newDepartment, {
    this.newSupervisorId,
    this.reason,
  });

  @override
  List<Object?> get props => [employeeId, newDepartment, newSupervisorId, reason];
}

/// Promote employee event
class PromoteEmployeeRequested extends EmployeeEvent {
  final String employeeId;
  final UserRole newRole;
  final String? reason;

  const PromoteEmployeeRequested(
    this.employeeId,
    this.newRole, {
    this.reason,
  });

  @override
  List<Object?> get props => [employeeId, newRole, reason];
}

/// Load employee statistics event
class LoadEmployeeStatisticsRequested extends EmployeeEvent {
  const LoadEmployeeStatisticsRequested();
}

/// Start real-time employee listening event
class StartEmployeeListeningRequested extends EmployeeEvent {
  final EmployeeFilterCriteria? filter;
  final int limit;

  const StartEmployeeListeningRequested({
    this.filter,
    this.limit = 50,
  });

  @override
  List<Object?> get props => [filter, limit];
}

/// Stop real-time employee listening event
class StopEmployeeListeningRequested extends EmployeeEvent {
  const StopEmployeeListeningRequested();
}

/// Real-time employees updated event
class EmployeesUpdatedFromStream extends EmployeeEvent {
  final List<Employee> employees;

  const EmployeesUpdatedFromStream(this.employees);

  @override
  List<Object?> get props => [employees];
}

/// Clear employee state event
class ClearEmployeeState extends EmployeeEvent {
  const ClearEmployeeState();
}

/// Navigate to create employee event
class NavigateToCreateEmployeeRequested extends EmployeeEvent {
  const NavigateToCreateEmployeeRequested();
}

/// Navigate to employee details event
class NavigateToEmployeeDetailsRequested extends EmployeeEvent {
  final Employee employee;

  const NavigateToEmployeeDetailsRequested(this.employee);

  @override
  List<Object?> get props => [employee];
}

/// Navigate to edit employee event
class NavigateToEditEmployeeRequested extends EmployeeEvent {
  final Employee employee;

  const NavigateToEditEmployeeRequested(this.employee);

  @override
  List<Object?> get props => [employee];
}

/// Export employees event
class ExportEmployeesRequested extends EmployeeEvent {
  final EmployeeFilterCriteria? filter;
  final String format; // 'csv', 'excel', 'pdf'

  const ExportEmployeesRequested({
    this.filter,
    this.format = 'csv',
  });

  @override
  List<Object?> get props => [filter, format];
}

/// Import employees event
class ImportEmployeesRequested extends EmployeeEvent {
  final String filePath;
  final String format; // 'csv', 'excel'

  const ImportEmployeesRequested(this.filePath, {this.format = 'csv'});

  @override
  List<Object?> get props => [filePath, format];
}

/// Bulk update employees event
class BulkUpdateEmployeesRequested extends EmployeeEvent {
  final List<String> employeeIds;
  final Map<String, dynamic> updates;

  const BulkUpdateEmployeesRequested(this.employeeIds, this.updates);

  @override
  List<Object?> get props => [employeeIds, updates];
}

/// Bulk delete employees event
class BulkDeleteEmployeesRequested extends EmployeeEvent {
  final List<String> employeeIds;
  final String? reason;

  const BulkDeleteEmployeesRequested(this.employeeIds, {this.reason});

  @override
  List<Object?> get props => [employeeIds, reason];
}
