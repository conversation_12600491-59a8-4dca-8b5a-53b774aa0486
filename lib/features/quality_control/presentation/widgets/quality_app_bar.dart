import 'package:flutter/material.dart';

import '../bloc/quality_bloc.dart';

/// Custom app bar for quality control pages
class QualityAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onAddPressed;
  final bool showSearch;
  final bool showFilter;
  final bool showAdd;

  const QualityAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.onSearchPressed,
    this.onFilterPressed,
    this.onAddPressed,
    this.showSearch = true,
    this.showFilter = true,
    this.showAdd = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (showSearch)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: onSearchPressed,
            tooltip: 'Search',
          ),
        if (showFilter)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: onFilterPressed,
            tooltip: 'Filter',
          ),
        if (showAdd)
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: onAddPressed,
            tooltip: 'Add Inspection',
          ),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Quality app bar with search functionality
class QualitySearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchClear;
  final VoidCallback? onFilterPressed;
  final ValueChanged<QualityViewMode>? onViewModeChanged;
  final QualityViewMode? currentViewMode;
  final bool isSearching;
  final TextEditingController? searchController;
  final VoidCallback? onAddPressed;

  const QualitySearchAppBar({
    Key? key,
    required this.title,
    this.hintText = 'Search quality inspections...',
    this.onSearchChanged,
    this.onSearchClear,
    this.onFilterPressed,
    this.onViewModeChanged,
    this.currentViewMode,
    this.isSearching = false,
    this.searchController,
    this.onAddPressed,
  }) : super(key: key);

  @override
  State<QualitySearchAppBar> createState() => _QualitySearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _QualitySearchAppBarState extends State<QualitySearchAppBar> {
  late bool _isSearching;
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _isSearching = widget.isSearching;
    _searchController = widget.searchController ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.searchController == null) {
      _searchController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: _isSearching
          ? TextField(
              controller: _searchController,
              onChanged: widget.onSearchChanged,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
                border: InputBorder.none,
              ),
              autofocus: true,
            )
          : Text(
              widget.title,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (_isSearching)
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              widget.onSearchClear?.call();
              setState(() => _isSearching = false);
            },
            tooltip: 'Clear Search',
          )
        else
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => setState(() => _isSearching = true),
            tooltip: 'Search',
          ),
        if (widget.onViewModeChanged != null)
          PopupMenuButton<QualityViewMode>(
            icon: const Icon(Icons.view_module),
            tooltip: 'View Mode',
            onSelected: widget.onViewModeChanged,
            itemBuilder: (context) => QualityViewMode.values.map((mode) {
              return PopupMenuItem<QualityViewMode>(
                value: mode,
                child: Row(
                  children: [
                    Icon(
                      mode.icon,
                      color: widget.currentViewMode == mode 
                          ? Theme.of(context).primaryColor 
                          : null,
                    ),
                    const SizedBox(width: 8),
                    Text(mode.displayName),
                    if (widget.currentViewMode == mode)
                      const Icon(Icons.check, color: Colors.green),
                  ],
                ),
              );
            }).toList(),
          ),
        if (widget.onFilterPressed != null)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: widget.onFilterPressed,
            tooltip: 'Filter',
          ),
          if (widget.onAddPressed != null)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: widget.onAddPressed,
              tooltip: 'Add New Inspection',
            ),
      ],
    );
  }
}

/// Quality app bar with tabs
class QualityTabAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Tab> tabs;
  final TabController? controller;
  final List<Widget>? actions;

  const QualityTabAppBar({
    Key? key,
    required this.title,
    required this.tabs,
    this.controller,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: actions,
      bottom: TabBar(
        controller: controller,
        tabs: tabs,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withOpacity(0.7),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + kTextTabBarHeight,
  );
}