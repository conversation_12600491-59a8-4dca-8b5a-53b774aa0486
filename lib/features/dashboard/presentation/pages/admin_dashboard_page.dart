// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
//
// import '../../../auth/domain/entities/user.dart';
// // import '../../../../core/auth/widgets/permission_guard.dart';
// import '../../../../core/constants/app_constants.dart';
// import '../../../../core/theme/app_colors.dart';
// import '../../../../core/theme/app_text_styles.dart';
// import '../../../../features/auth/presentation/bloc/firebase_auth_bloc.dart';
// import '../../../../shared/widgets/dashboard_card.dart';
// import '../../../../shared/widgets/quick_stats_widget.dart';
// import '../widgets/dashboard_drawer.dart';
//
// /// Admin Dashboard Page
// class AdminDashboardPage extends StatelessWidget {
//   const AdminDashboardPage({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
//       builder: (context, state) {
//         if (state is! FirebaseAuthAuthenticated) {
//           return const Scaffold(
//             body: Center(child: CircularProgressIndicator()),
//           );
//         }
//         final user = state.user;
//
//         // Responsive layout: Drawer on narrow screens, permanent rail/drawer on wide
//         return LayoutBuilder(
//           builder: (context, constraints) {
//             final isWide = constraints.maxWidth >= 1000; // web/desktop-like
//
//             return Scaffold(
//               appBar: AppBar(
//                 title: const Text('HM Collection Admin Dashboard'),
//                 backgroundColor: AppColors.primary,
//                 foregroundColor: Colors.white,
//                 leading: isWide ? null : Builder(
//                   builder: (context) => IconButton(
//                     icon: const Icon(Icons.menu),
//                     onPressed: () => Scaffold.of(context).openDrawer(),
//                   ),
//                 ),
//                 actions: [
//                   IconButton(
//                     icon: const Icon(Icons.notifications),
//                     onPressed: () => _navigateTo(context, '/notifications'),
//                   ),
//                   IconButton(
//                     icon: const Icon(Icons.settings),
//                     onPressed: () => _navigateTo(context, '/settings'),
//                   ),
//                 ],
//               ),
//               drawer: isWide ? null : DashboardDrawer(
//                 user: user,
//                 onNavigate: (route) => _handleNavigate(context, route),
//               ),
//               body: Row(
//                 children: [
//                   if (isWide)
//                     SizedBox(
//                       width: 280,
//                       child: Material(
//                         elevation: 1,
//                         color: Theme.of(context).drawerTheme.backgroundColor ?? Theme.of(context).colorScheme.surface,
//                         child: DashboardDrawer(
//                           user: user,
//                           onNavigate: (route) => _handleNavigate(context, route),
//                         ),
//                       ),
//                     ),
//                   Expanded(
//                     child: _buildAdminDashboard(context, user),
//                   ),
//                 ],
//               ),
//             );
//           },
//         );
//       },
//     );
//   }
//
//   Widget _buildAdminDashboard(BuildContext context, User user) {
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(AppConstants.defaultPadding),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           _buildWelcomeSection(user),
//           const SizedBox(height: 24),
//           _buildQuickStats(),
//           const SizedBox(height: 24),
//           _buildAdminActions(context),
//           const SizedBox(height: 24),
//           _buildSystemOverview(),
//           const SizedBox(height: 24),
//           _buildRecentActivities(),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildWelcomeSection(User user) {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Row(
//           children: [
//             CircleAvatar(
//               radius: 30,
//               backgroundColor: AppColors.primary,
//               child: Text(
//                 user.initials,
//                 style: AppTextStyles.titleLarge.copyWith(
//                   color: Colors.white,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//             ),
//             const SizedBox(width: 16),
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'Welcome back, ${user.fullName}!',
//                     style: AppTextStyles.titleLarge.copyWith(
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   const SizedBox(height: 4),
//                   Text(
//                     'Administrator',
//                     style: AppTextStyles.bodyMedium.copyWith(
//                       color: AppColors.primary,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   const SizedBox(height: 4),
//                   Text(
//                     'Last login: ${_formatLastLogin(user.lastLoginAt)}',
//                     style: AppTextStyles.bodySmall.copyWith(
//                       color: AppColors.textSecondary,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildQuickStats() {
//     return const QuickStatsWidget(
//       stats: [
//         QuickStat(
//           title: 'Total Users',
//           value: '156',
//           icon: Icons.people,
//           color: Colors.blue,
//           trend: '+12%',
//         ),
//         QuickStat(
//           title: 'Active Orders',
//           value: '89',
//           icon: Icons.shopping_cart,
//           color: Colors.green,
//           trend: '+5%',
//         ),
//         QuickStat(
//           title: 'Production Lines',
//           value: '12',
//           icon: Icons.factory,
//           color: Colors.orange,
//           trend: '0%',
//         ),
//         QuickStat(
//           title: 'System Health',
//           value: '98%',
//           icon: Icons.health_and_safety,
//           color: Colors.purple,
//           trend: '+2%',
//         ),
//       ],
//     );
//   }
//
//   Widget _buildAdminActions(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Admin Actions',
//           style: AppTextStyles.titleMedium.copyWith(
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         const SizedBox(height: 16),
//         GridView.count(
//           shrinkWrap: true,
//           physics: const NeverScrollableScrollPhysics(),
//           crossAxisCount: 2,
//           crossAxisSpacing: 16,
//           mainAxisSpacing: 16,
//           childAspectRatio: 1.5,
//           children: [
//             DashboardCard(
//               title: 'User Management',
//               subtitle: 'Manage users and roles',
//               icon: Icons.people_alt,
//               color: Colors.blue,
//               onTap: () {
//                 // Navigate to user management
//               },
//             ),
//             DashboardCard(
//               title: 'System Settings',
//               subtitle: 'Configure system settings',
//               icon: Icons.settings,
//               color: Colors.grey,
//               onTap: () {
//                 // Navigate to system settings
//               },
//             ),
//             DashboardCard(
//               title: 'Audit Logs',
//               subtitle: 'View system audit logs',
//               icon: Icons.history,
//               color: Colors.purple,
//               onTap: () {
//                 // Navigate to audit logs
//               },
//             ),
//             DashboardCard(
//               title: 'Backup & Restore',
//               subtitle: 'Manage data backups',
//               icon: Icons.backup,
//               color: Colors.orange,
//               onTap: () {
//                 // Navigate to backup management
//               },
//             ),
//           ],
//         ),
//       ],
//     );
//   }
//
//   Widget _buildSystemOverview() {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'System Overview',
//               style: AppTextStyles.titleMedium.copyWith(
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             const SizedBox(height: 16),
//             _buildSystemMetric('Server Status', 'Online', Colors.green),
//             const SizedBox(height: 8),
//             _buildSystemMetric('Database', 'Healthy', Colors.green),
//             const SizedBox(height: 8),
//             _buildSystemMetric('Active Sessions', '45', Colors.blue),
//             const SizedBox(height: 8),
//             _buildSystemMetric('Storage Used', '67%', Colors.orange),
//           ],
//         ),
//       ),
//     );
//   }
//
//   // Simple route handler. Replace with Navigator.of(context).pushNamed when routes are ready
//   void _handleNavigate(BuildContext context, String route) {
//     // TODO: Integrate with your real routing once available
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(content: Text('Navigate to $route')),
//     );
//   }
//
//   void _navigateTo(BuildContext context, String route) {
//     _handleNavigate(context, route);
//   }
//
//   Widget _buildSystemMetric(String label, String value, Color color) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Text(
//           label,
//           style: AppTextStyles.bodyMedium,
//         ),
//         Container(
//           padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//           decoration: BoxDecoration(
//             color: color.withValues(alpha: 0.1),
//             borderRadius: BorderRadius.circular(12),
//           ),
//           child: Text(
//             value,
//             style: AppTextStyles.bodySmall.copyWith(
//               color: color,
//               fontWeight: FontWeight.w600,
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildRecentActivities() {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'Recent Activities',
//               style: AppTextStyles.titleMedium.copyWith(
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             const SizedBox(height: 16),
//             _buildActivityItem(
//               'New user registered',
//               'John Doe joined as Operator',
//               '2 minutes ago',
//               Icons.person_add,
//               Colors.green,
//             ),
//             _buildActivityItem(
//               'System backup completed',
//               'Daily backup finished successfully',
//               '1 hour ago',
//               Icons.backup,
//               Colors.blue,
//             ),
//             _buildActivityItem(
//               'Security alert',
//               'Failed login attempts detected',
//               '3 hours ago',
//               Icons.security,
//               Colors.red,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildActivityItem(
//     String title,
//     String subtitle,
//     String time,
//     IconData icon,
//     Color color,
//   ) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 12),
//       child: Row(
//         children: [
//           Container(
//             padding: const EdgeInsets.all(8),
//             decoration: BoxDecoration(
//               color: color.withValues(alpha: 0.1),
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: Icon(
//               icon,
//               size: 20,
//               color: color,
//             ),
//           ),
//           const SizedBox(width: 12),
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   title,
//                   style: AppTextStyles.bodyMedium.copyWith(
//                     fontWeight: FontWeight.w500,
//                   ),
//                 ),
//                 Text(
//                   subtitle,
//                   style: AppTextStyles.bodySmall.copyWith(
//                     color: AppColors.textSecondary,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           Text(
//             time,
//             style: AppTextStyles.bodySmall.copyWith(
//               color: AppColors.textSecondary,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   String _formatLastLogin(DateTime? lastLogin) {
//     if (lastLogin == null) return 'Never';
//
//     final now = DateTime.now();
//     final difference = now.difference(lastLogin);
//
//     if (difference.inDays > 0) {
//       return '${difference.inDays} days ago';
//     } else if (difference.inHours > 0) {
//       return '${difference.inHours} hours ago';
//     } else if (difference.inMinutes > 0) {
//       return '${difference.inMinutes} minutes ago';
//     } else {
//       return 'Just now';
//     }
//   }
// }
