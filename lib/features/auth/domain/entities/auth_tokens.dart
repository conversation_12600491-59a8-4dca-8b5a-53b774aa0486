import 'package:equatable/equatable.dart';

/// Authentication tokens
class AuthTokens extends Equatable {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final DateTime issuedAt;
  final DateTime expiresAt;
  final List<String> scopes;

  const AuthTokens({
    required this.accessToken,
    required this.refreshToken,
    this.tokenType = 'Bearer',
    required this.expiresIn,
    required this.issuedAt,
    required this.expiresAt,
    this.scopes = const [],
  });

  /// Create from API response
  factory AuthTokens.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? json;
    final issuedAt = data['issued_at'] is String
        ? DateTime.parse(data['issued_at'] as String)
        : DateTime.now();
    final expiresIn = (data['expires_in'] as num?)?.toInt() ?? 3600;

    return AuthTokens(
      accessToken: data['access_token'] as String,
      refreshToken: data['refresh_token'] as String,
      tokenType: data['token_type'] as String? ?? 'Bearer',
      expiresIn: expiresIn,
      issuedAt: issuedAt,
      expiresAt: data['expires_at'] is String
          ? DateTime.parse(data['expires_at'] as String)
          : issuedAt.add(Duration(seconds: expiresIn)),
      scopes: data['scopes'] != null
          ? List<String>.from(data['scopes'] as List)
          : const [],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
      'issued_at': issuedAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'scopes': scopes,
    };
  }



  /// Check if access token is expired
  bool get isExpired {
    return DateTime.now().isAfter(expiresAt);
  }

  /// Check if access token will expire soon (within 5 minutes)
  bool get willExpireSoon {
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt);
  }

  /// Check if tokens are valid
  bool get isValid {
    return accessToken.isNotEmpty && 
           refreshToken.isNotEmpty && 
           !isExpired;
  }

  /// Get time remaining until expiry
  Duration get timeUntilExpiry {
    return expiresAt.difference(DateTime.now());
  }

  /// Get formatted authorization header
  String get authorizationHeader {
    return '$tokenType $accessToken';
  }

  /// Check if token has specific scope
  bool hasScope(String scope) {
    return scopes.contains(scope);
  }

  /// Check if token has any of the specified scopes
  bool hasAnyScope(List<String> scopeList) {
    return scopeList.any((scope) => scopes.contains(scope));
  }

  /// Check if token has all specified scopes
  bool hasAllScopes(List<String> scopeList) {
    return scopeList.every((scope) => scopes.contains(scope));
  }

  /// Copy with new values
  AuthTokens copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    DateTime? issuedAt,
    DateTime? expiresAt,
    List<String>? scopes,
  }) {
    return AuthTokens(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      issuedAt: issuedAt ?? this.issuedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      scopes: scopes ?? this.scopes,
    );
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        tokenType,
        expiresIn,
        issuedAt,
        expiresAt,
        scopes,
      ];

  @override
  String toString() {
    return 'AuthTokens(tokenType: $tokenType, expiresIn: $expiresIn, issuedAt: $issuedAt, expiresAt: $expiresAt, scopes: $scopes)';
  }
}


