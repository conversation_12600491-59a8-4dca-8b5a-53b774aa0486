part of 'collaboration_bloc.dart';

/// Base collaboration state
abstract class CollaborationState extends Equatable {
  const CollaborationState();

  @override
  List<Object?> get props => [];
}

/// Initial collaboration state
class CollaborationInitial extends CollaborationState {
  const CollaborationInitial();
}

/// Collaboration loading state
class CollaborationLoading extends CollaborationState {
  const CollaborationLoading();
}

/// Collaboration error state
class CollaborationError extends CollaborationState {
  final String message;

  const CollaborationError(this.message);

  @override
  List<Object?> get props => [message];
}

// Workspace States

/// Workspaces loaded state
class CollaborationWorkspacesLoaded extends CollaborationState {
  final ApiListResponse<CollaborationWorkspace> response;

  const CollaborationWorkspacesLoaded(this.response);

  @override
  List<Object?> get props => [response];

  /// Get workspaces list
  List<CollaborationWorkspace> get workspaces => response.data ?? [];

  /// Get pagination metadata
  Pagination? get pagination => response.pagination;

  /// Check if has more pages
  bool get hasNextPage => pagination?.hasNextPage ?? false;

  /// Check if has previous pages
  bool get hasPreviousPage => pagination?.hasPreviousPage ?? false;

  /// Get workspaces by type
  Map<WorkspaceType, List<CollaborationWorkspace>> get workspacesByType {
    final Map<WorkspaceType, List<CollaborationWorkspace>> grouped = {};
    for (final workspace in workspaces) {
      grouped.putIfAbsent(workspace.type, () => []).add(workspace);
    }
    return grouped;
  }

  /// Get active workspaces count
  int get activeWorkspacesCount {
    return workspaces.where((w) => w.isActive).length;
  }

  /// Get public workspaces
  List<CollaborationWorkspace> get publicWorkspaces {
    return workspaces.where((w) => w.isPublic).toList();
  }

  /// Get private workspaces
  List<CollaborationWorkspace> get privateWorkspaces {
    return workspaces.where((w) => !w.isPublic).toList();
  }
}

/// Workspace loaded state
class CollaborationWorkspaceLoaded extends CollaborationState {
  final CollaborationWorkspace workspace;

  const CollaborationWorkspaceLoaded(this.workspace);

  @override
  List<Object?> get props => [workspace];

  /// Get member count
  int get memberCount => workspace.memberCount;

  /// Get active member count
  int get activeMemberCount => workspace.activeMemberCount;

  /// Check if workspace is active
  bool get isActive => workspace.isActive;

  /// Get online members
  List<WorkspaceMember> get onlineMembers {
    return workspace.members.where((m) => m.isOnline).toList();
  }

  /// Get offline members
  List<WorkspaceMember> get offlineMembers {
    return workspace.members.where((m) => !m.isOnline).toList();
  }

  /// Get members by role
  Map<WorkspaceRole, List<WorkspaceMember>> get membersByRole {
    final Map<WorkspaceRole, List<WorkspaceMember>> grouped = {};
    for (final member in workspace.members) {
      grouped.putIfAbsent(member.role, () => []).add(member);
    }
    return grouped;
  }
}

/// Workspace created state
class CollaborationWorkspaceCreated extends CollaborationState {
  final CollaborationWorkspace workspace;

  const CollaborationWorkspaceCreated(this.workspace);

  @override
  List<Object?> get props => [workspace];
}

/// Workspace updated state
class CollaborationWorkspaceUpdated extends CollaborationState {
  final CollaborationWorkspace workspace;

  const CollaborationWorkspaceUpdated(this.workspace);

  @override
  List<Object?> get props => [workspace];
}

/// Workspace deleted state
class CollaborationWorkspaceDeleted extends CollaborationState {
  final String workspaceId;

  const CollaborationWorkspaceDeleted(this.workspaceId);

  @override
  List<Object?> get props => [workspaceId];
}

/// Workspace archived state
class CollaborationWorkspaceArchived extends CollaborationState {
  final CollaborationWorkspace workspace;

  const CollaborationWorkspaceArchived(this.workspace);

  @override
  List<Object?> get props => [workspace];
}

/// Workspace restored state
class CollaborationWorkspaceRestored extends CollaborationState {
  final CollaborationWorkspace workspace;

  const CollaborationWorkspaceRestored(this.workspace);

  @override
  List<Object?> get props => [workspace];
}

// Member States

/// Members loaded state
class CollaborationMembersLoaded extends CollaborationState {
  final ApiListResponse<WorkspaceMember> response;

  const CollaborationMembersLoaded(this.response);

  @override
  List<Object?> get props => [response];

  /// Get members list
  List<WorkspaceMember> get members => response.data ?? [];

  /// Get pagination metadata
  Pagination? get pagination => response.pagination;

  /// Get online members count
  int get onlineMembersCount {
    return members.where((m) => m.isOnline).length;
  }

  /// Get offline members count
  int get offlineMembersCount {
    return members.where((m) => !m.isOnline).length;
  }

  /// Get members by role
  Map<WorkspaceRole, List<WorkspaceMember>> get membersByRole {
    final Map<WorkspaceRole, List<WorkspaceMember>> grouped = {};
    for (final member in members) {
      grouped.putIfAbsent(member.role, () => []).add(member);
    }
    return grouped;
  }

  /// Get active members
  List<WorkspaceMember> get activeMembers {
    return members.where((m) => m.isActive).toList();
  }

  /// Get inactive members
  List<WorkspaceMember> get inactiveMembers {
    return members.where((m) => !m.isActive).toList();
  }
}

/// Member added state
class CollaborationMemberAdded extends CollaborationState {
  final WorkspaceMember member;

  const CollaborationMemberAdded(this.member);

  @override
  List<Object?> get props => [member];
}

/// Member updated state
class CollaborationMemberUpdated extends CollaborationState {
  final WorkspaceMember member;

  const CollaborationMemberUpdated(this.member);

  @override
  List<Object?> get props => [member];
}

/// Member removed state
class CollaborationMemberRemoved extends CollaborationState {
  final String workspaceId;
  final String userId;

  const CollaborationMemberRemoved(this.workspaceId, this.userId);

  @override
  List<Object?> get props => [workspaceId, userId];
}

/// Member invited state
class CollaborationMemberInvited extends CollaborationState {
  final WorkspaceMember member;

  const CollaborationMemberInvited(this.member);

  @override
  List<Object?> get props => [member];
}

/// Invitation accepted state
class CollaborationInvitationAccepted extends CollaborationState {
  final String invitationId;

  const CollaborationInvitationAccepted(this.invitationId);

  @override
  List<Object?> get props => [invitationId];
}

/// Invitation declined state
class CollaborationInvitationDeclined extends CollaborationState {
  final String invitationId;

  const CollaborationInvitationDeclined(this.invitationId);

  @override
  List<Object?> get props => [invitationId];
}

// Channel States

/// Channels loaded state
class CollaborationChannelsLoaded extends CollaborationState {
  final ApiListResponse<CollaborationChannel> response;

  const CollaborationChannelsLoaded(this.response);

  @override
  List<Object?> get props => [response];

  /// Get channels list
  List<CollaborationChannel> get channels => response.data ?? [];

  /// Get pagination metadata
  Pagination? get pagination => response.pagination;

  /// Get channels by type
  Map<ChannelType, List<CollaborationChannel>> get channelsByType {
    final Map<ChannelType, List<CollaborationChannel>> grouped = {};
    for (final channel in channels) {
      grouped.putIfAbsent(channel.type, () => []).add(channel);
    }
    return grouped;
  }

  /// Get channels with unread messages
  List<CollaborationChannel> get channelsWithUnread {
    return channels.where((c) => c.hasUnreadMessages).toList();
  }

  /// Get total unread count
  int get totalUnreadCount {
    return channels.fold(0, (sum, channel) => sum + channel.unreadCount);
  }

  /// Get active channels
  List<CollaborationChannel> get activeChannels {
    return channels.where((c) => c.isActive).toList();
  }

  /// Get archived channels
  List<CollaborationChannel> get archivedChannels {
    return channels.where((c) => c.isArchived).toList();
  }
}

/// Channel loaded state
class CollaborationChannelLoaded extends CollaborationState {
  final CollaborationChannel channel;

  const CollaborationChannelLoaded(this.channel);

  @override
  List<Object?> get props => [channel];

  /// Get member count
  int get memberCount => channel.memberCount;

  /// Check if channel is active
  bool get isActive => channel.isActive;

  /// Check if has unread messages
  bool get hasUnreadMessages => channel.hasUnreadMessages;

  /// Get unread count
  int get unreadCount => channel.unreadCount;
}

/// Channel created state
class CollaborationChannelCreated extends CollaborationState {
  final CollaborationChannel channel;

  const CollaborationChannelCreated(this.channel);

  @override
  List<Object?> get props => [channel];
}

/// Channel updated state
class CollaborationChannelUpdated extends CollaborationState {
  final CollaborationChannel channel;

  const CollaborationChannelUpdated(this.channel);

  @override
  List<Object?> get props => [channel];
}

/// Channel deleted state
class CollaborationChannelDeleted extends CollaborationState {
  final String channelId;

  const CollaborationChannelDeleted(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

/// Channel archived state
class CollaborationChannelArchived extends CollaborationState {
  final CollaborationChannel channel;

  const CollaborationChannelArchived(this.channel);

  @override
  List<Object?> get props => [channel];
}

/// Channel joined state
class CollaborationChannelJoined extends CollaborationState {
  final CollaborationChannel channel;

  const CollaborationChannelJoined(this.channel);

  @override
  List<Object?> get props => [channel];
}

/// Channel left state
class CollaborationChannelLeft extends CollaborationState {
  final String channelId;

  const CollaborationChannelLeft(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

// Message States

/// Messages loaded state
class CollaborationMessagesLoaded extends CollaborationState {
  final ApiListResponse<CollaborationMessage> response;

  const CollaborationMessagesLoaded(this.response);

  @override
  List<Object?> get props => [response];

  /// Get messages list
  List<CollaborationMessage> get messages => response.data ?? [];

  /// Get pagination metadata
  Pagination? get pagination => response.pagination;

  /// Get messages by type
  Map<MessageType, List<CollaborationMessage>> get messagesByType {
    final Map<MessageType, List<CollaborationMessage>> grouped = {};
    for (final message in messages) {
      grouped.putIfAbsent(message.type, () => []).add(message);
    }
    return grouped;
  }

  /// Get pinned messages
  List<CollaborationMessage> get pinnedMessages {
    return messages.where((m) => m.isPinned).toList();
  }

  /// Get messages with attachments
  List<CollaborationMessage> get messagesWithAttachments {
    return messages.where((m) => m.attachments.isNotEmpty).toList();
  }

  /// Get messages with reactions
  List<CollaborationMessage> get messagesWithReactions {
    return messages.where((m) => m.reactions.isNotEmpty).toList();
  }

  /// Get thread starter messages
  List<CollaborationMessage> get threadStarters {
    return messages.where((m) => m.isThreadStarter).toList();
  }
}

/// Message loaded state
class CollaborationMessageLoaded extends CollaborationState {
  final CollaborationMessage message;

  const CollaborationMessageLoaded(this.message);

  @override
  List<Object?> get props => [message];

  /// Check if message is edited
  bool get isEdited => message.isEdited;

  /// Check if message is reply
  bool get isReply => message.isReply;

  /// Check if message has thread
  bool get hasThread => message.hasThread;

  /// Get reaction count
  int get reactionCount => message.reactionCount;

  /// Get attachment count
  int get attachmentCount => message.attachments.length;
}

/// Message sent state
class CollaborationMessageSent extends CollaborationState {
  final CollaborationMessage message;

  const CollaborationMessageSent(this.message);

  @override
  List<Object?> get props => [message];
}

/// Message edited state
class CollaborationMessageEdited extends CollaborationState {
  final CollaborationMessage message;

  const CollaborationMessageEdited(this.message);

  @override
  List<Object?> get props => [message];
}

/// Message deleted state
class CollaborationMessageDeleted extends CollaborationState {
  final String messageId;

  const CollaborationMessageDeleted(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Message pinned state
class CollaborationMessagePinned extends CollaborationState {
  final CollaborationMessage message;

  const CollaborationMessagePinned(this.message);

  @override
  List<Object?> get props => [message];
}

/// Message unpinned state
class CollaborationMessageUnpinned extends CollaborationState {
  final String messageId;

  const CollaborationMessageUnpinned(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Reaction added state
class CollaborationReactionAdded extends CollaborationState {
  final CollaborationMessage message;

  const CollaborationReactionAdded(this.message);

  @override
  List<Object?> get props => [message];
}

/// Reaction removed state
class CollaborationReactionRemoved extends CollaborationState {
  final String messageId;
  final String emoji;

  const CollaborationReactionRemoved(this.messageId, this.emoji);

  @override
  List<Object?> get props => [messageId, emoji];
}

/// Message marked as read state
class CollaborationMessageMarkedAsRead extends CollaborationState {
  final String messageId;

  const CollaborationMessageMarkedAsRead(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Channel marked as read state
class CollaborationChannelMarkedAsRead extends CollaborationState {
  final String channelId;

  const CollaborationChannelMarkedAsRead(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

// Thread States

/// Thread messages loaded state
class CollaborationThreadMessagesLoaded extends CollaborationState {
  final ApiListResponse<CollaborationMessage> response;
  final String parentMessageId;

  const CollaborationThreadMessagesLoaded(this.response, this.parentMessageId);

  @override
  List<Object?> get props => [response, parentMessageId];

  /// Get thread messages list
  List<CollaborationMessage> get messages => response.data ?? [];

  /// Get pagination metadata
  Pagination? get pagination => response.pagination;

  /// Get thread messages count
  int get threadMessagesCount => messages.length;
}

/// Thread reply sent state
class CollaborationThreadReplySent extends CollaborationState {
  final CollaborationMessage message;

  const CollaborationThreadReplySent(this.message);

  @override
  List<Object?> get props => [message];
}

// File States

/// File uploaded state
class CollaborationFileUploaded extends CollaborationState {
  final MessageAttachment attachment;

  const CollaborationFileUploaded(this.attachment);

  @override
  List<Object?> get props => [attachment];
}

/// File download URL loaded state
class CollaborationFileDownloadUrlLoaded extends CollaborationState {
  final String attachmentId;
  final String downloadUrl;

  const CollaborationFileDownloadUrlLoaded(this.attachmentId, this.downloadUrl);

  @override
  List<Object?> get props => [attachmentId, downloadUrl];
}

/// File deleted state
class CollaborationFileDeleted extends CollaborationState {
  final String attachmentId;

  const CollaborationFileDeleted(this.attachmentId);

  @override
  List<Object?> get props => [attachmentId];
}

/// Channel files loaded state
class CollaborationChannelFilesLoaded extends CollaborationState {
  final ApiListResponse<MessageAttachment> response;

  const CollaborationChannelFilesLoaded(this.response);

  @override
  List<Object?> get props => [response];

  /// Get files list
  List<MessageAttachment> get files => response.data ?? [];

  /// Get pagination metadata
  Pagination? get pagination => response.pagination;

  /// Get files by type
  Map<String, List<MessageAttachment>> get filesByType {
    final Map<String, List<MessageAttachment>> grouped = {};
    for (final file in files) {
      grouped.putIfAbsent(file.fileType, () => []).add(file);
    }
    return grouped;
  }

  /// Get image files
  List<MessageAttachment> get imageFiles {
    return files.where((f) => f.isImage).toList();
  }

  /// Get document files
  List<MessageAttachment> get documentFiles {
    return files.where((f) => f.isDocument).toList();
  }

  /// Get total file size
  int get totalFileSize {
    return files.fold(0, (sum, file) => sum + file.fileSize);
  }
}
