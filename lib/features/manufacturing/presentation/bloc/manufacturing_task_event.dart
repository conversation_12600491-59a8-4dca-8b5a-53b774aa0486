part of 'manufacturing_task_bloc.dart';

/// Base manufacturing task event
abstract class ManufacturingTaskEvent extends Equatable {
  const ManufacturingTaskEvent();

  @override
  List<Object?> get props => [];
}

// Manufacturing Task Order Events

/// Load manufacturing task orders
class LoadManufacturingTaskOrdersRequested extends ManufacturingTaskEvent {
  final ManufacturingTaskOrderFilter? filter;
  final PaginationParams? pagination;

  const LoadManufacturingTaskOrdersRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load manufacturing task order by ID
class LoadManufacturingTaskOrderByIdRequested extends ManufacturingTaskEvent {
  final String orderId;

  const LoadManufacturingTaskOrderByIdRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Create manufacturing task order
class CreateManufacturingTaskOrderRequested extends ManufacturingTaskEvent {
  final CreateManufacturingTaskOrderRequest request;

  const CreateManufacturingTaskOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update manufacturing task order
class UpdateManufacturingTaskOrderRequested extends ManufacturingTaskEvent {
  final UpdateManufacturingTaskOrderRequest request;

  const UpdateManufacturingTaskOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete manufacturing task order
class DeleteManufacturingTaskOrderRequested extends ManufacturingTaskEvent {
  final String orderId;

  const DeleteManufacturingTaskOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Start manufacturing task order
class StartManufacturingTaskOrderRequested extends ManufacturingTaskEvent {
  final String orderId;

  const StartManufacturingTaskOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Pause manufacturing task order
class PauseManufacturingTaskOrderRequested extends ManufacturingTaskEvent {
  final PauseManufacturingTaskOrderRequest request;

  const PauseManufacturingTaskOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Resume manufacturing task order
class ResumeManufacturingTaskOrderRequested extends ManufacturingTaskEvent {
  final String orderId;

  const ResumeManufacturingTaskOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Complete manufacturing task order
class CompleteManufacturingTaskOrderRequested extends ManufacturingTaskEvent {
  final String orderId;

  const CompleteManufacturingTaskOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

// Stage Task Events

/// Load stage tasks for order
class LoadStageTasksForOrderRequested extends ManufacturingTaskEvent {
  final GetStageTasksRequest request;

  const LoadStageTasksForOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load stage tasks by department
class LoadStageTasksByDepartmentRequested extends ManufacturingTaskEvent {
  final GetStageTasksByDepartmentRequest request;

  const LoadStageTasksByDepartmentRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load stage tasks by stage
class LoadStageTasksByStageRequested extends ManufacturingTaskEvent {
  final GetStageTasksByStageRequest request;

  const LoadStageTasksByStageRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load stage task by ID
class LoadStageTaskByIdRequested extends ManufacturingTaskEvent {
  final String taskId;

  const LoadStageTaskByIdRequested(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Create stage task
class CreateStageTaskRequested extends ManufacturingTaskEvent {
  final CreateStageTaskRequest request;

  const CreateStageTaskRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update stage task
class UpdateStageTaskRequested extends ManufacturingTaskEvent {
  final UpdateStageTaskRequest request;

  const UpdateStageTaskRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Start stage task
class StartStageTaskRequested extends ManufacturingTaskEvent {
  final String taskId;

  const StartStageTaskRequested(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Complete stage task
class CompleteStageTaskRequested extends ManufacturingTaskEvent {
  final CompleteStageTaskRequest request;

  const CompleteStageTaskRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Assign workers to stage task
class AssignWorkersToStageTaskRequested extends ManufacturingTaskEvent {
  final AssignWorkersToStageTaskRequest request;

  const AssignWorkersToStageTaskRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update stage task progress
class UpdateStageTaskProgressRequested extends ManufacturingTaskEvent {
  final UpdateStageTaskProgressRequest request;

  const UpdateStageTaskProgressRequested(this.request);

  @override
  List<Object?> get props => [request];
}

// Stage Transfer Events

/// Create stage transfer
class CreateStageTransferRequested extends ManufacturingTaskEvent {
  final CreateStageTransferRequest request;

  const CreateStageTransferRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load transfers for order
class LoadTransfersForOrderRequested extends ManufacturingTaskEvent {
  final String orderId;

  const LoadTransfersForOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Load pending transfers for department
class LoadPendingTransfersForDepartmentRequested extends ManufacturingTaskEvent {
  final GetPendingTransfersRequest request;

  const LoadPendingTransfersForDepartmentRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Accept stage transfer
class AcceptStageTransferRequested extends ManufacturingTaskEvent {
  final AcceptStageTransferRequest request;

  const AcceptStageTransferRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Reject stage transfer
class RejectStageTransferRequested extends ManufacturingTaskEvent {
  final RejectStageTransferRequest request;

  const RejectStageTransferRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Complete stage transfer
class CompleteStageTransferRequested extends ManufacturingTaskEvent {
  final String transferId;

  const CompleteStageTransferRequested(this.transferId);

  @override
  List<Object?> get props => [transferId];
}

// Notes Events

/// Add note to order
class AddNoteToOrderRequested extends ManufacturingTaskEvent {
  final AddNoteToOrderRequest request;

  const AddNoteToOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Add note to stage task
class AddNoteToStageTaskRequested extends ManufacturingTaskEvent {
  final AddNoteToStageTaskRequest request;

  const AddNoteToStageTaskRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load notes for order
class LoadNotesForOrderRequested extends ManufacturingTaskEvent {
  final String orderId;

  const LoadNotesForOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Load notes for stage task
class LoadNotesForStageTaskRequested extends ManufacturingTaskEvent {
  final String taskId;

  const LoadNotesForStageTaskRequested(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Update note
class UpdateNoteRequested extends ManufacturingTaskEvent {
  final UpdateNoteRequest request;

  const UpdateNoteRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete note
class DeleteNoteRequested extends ManufacturingTaskEvent {
  final String noteId;

  const DeleteNoteRequested(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

// Analytics Events

/// Load manufacturing task analytics
class LoadManufacturingTaskAnalyticsRequested extends ManufacturingTaskEvent {
  final GetManufacturingTaskAnalyticsRequest request;

  const LoadManufacturingTaskAnalyticsRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load stage performance metrics
class LoadStagePerformanceMetricsRequested extends ManufacturingTaskEvent {
  final GetStagePerformanceMetricsRequest request;

  const LoadStagePerformanceMetricsRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load bottleneck analysis
class LoadBottleneckAnalysisRequested extends ManufacturingTaskEvent {
  final Map<String, dynamic> params;

  const LoadBottleneckAnalysisRequested({required this.params});

  @override
  List<Object?> get props => [params];
}

/// Load efficiency metrics
class LoadEfficiencyMetricsRequested extends ManufacturingTaskEvent {
  final GetEfficiencyMetricsRequest request;

  const LoadEfficiencyMetricsRequested(this.request);

  @override
  List<Object?> get props => [request];
}

// Search and Filter Events

/// Search manufacturing task orders
class SearchManufacturingTaskOrdersRequested extends ManufacturingTaskEvent {
  final SearchManufacturingTaskOrdersRequest request;

  const SearchManufacturingTaskOrdersRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load overdue manufacturing task orders
class LoadOverdueManufacturingTaskOrdersRequested extends ManufacturingTaskEvent {
  final ManufacturingTaskOrderFilter? filter;
  final PaginationParams? pagination;

  const LoadOverdueManufacturingTaskOrdersRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load urgent manufacturing task orders
class LoadUrgentManufacturingTaskOrdersRequested extends ManufacturingTaskEvent {
  final List<Department>? departments;
  final PaginationParams? pagination;

  const LoadUrgentManufacturingTaskOrdersRequested({
    this.departments,
    this.pagination,
  });

  @override
  List<Object?> get props => [departments, pagination];
}

/// Load manufacturing task orders by status
class LoadManufacturingTaskOrdersByStatusRequested extends ManufacturingTaskEvent {
  final GetManufacturingTaskOrdersByStatusRequest request;

  const LoadManufacturingTaskOrdersByStatusRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load stage tasks by worker
class LoadStageTasksByWorkerRequested extends ManufacturingTaskEvent {
  final GetStageTasksByWorkerRequest request;

  const LoadStageTasksByWorkerRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load stage tasks by status
class LoadStageTasksByStatusRequested extends ManufacturingTaskEvent {
  final GetStageTasksByStatusRequest request;

  const LoadStageTasksByStatusRequested(this.request);

  @override
  List<Object?> get props => [request];
}

// Utility Events

/// Refresh manufacturing task data
class RefreshManufacturingTaskDataRequested extends ManufacturingTaskEvent {
  const RefreshManufacturingTaskDataRequested();
}

/// Clear manufacturing task state
class ClearManufacturingTaskStateRequested extends ManufacturingTaskEvent {
  const ClearManufacturingTaskStateRequested();
}
