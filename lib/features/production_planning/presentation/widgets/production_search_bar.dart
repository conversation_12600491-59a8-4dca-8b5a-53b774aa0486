import 'package:flutter/material.dart';

/// Search bar widget for production planning
class ProductionSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged; // Original callback
  final ValueChanged<String>? onSearch; // Alias for compatibility with callers
  final VoidCallback? onFilterPressed;
  final VoidCallback? onClear; // Optional clear callback
  final TextEditingController? controller;
  final bool showFilter;
  final String? initialQuery; // Optional initial text

  const ProductionSearchBar({
    Key? key,
    this.hintText = 'Search production orders...',
    this.onSearchChanged,
    this.onSearch,
    this.onFilterPressed,
    this.onClear,
    this.controller,
    this.showFilter = true,
    this.initialQuery,
  }) : super(key: key);

  @override
  State<ProductionSearchBar> createState() => _ProductionSearchBarState();
}

class _ProductionSearchBarState extends State<ProductionSearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      _controller.text = widget.initialQuery!;
    }
    // Rebuild to reflect suffix icon visibility when text changes
    _controller.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _controller,
              onChanged: (value) {
                // Support either onSearchChanged or onSearch
                widget.onSearchChanged?.call(value);
                widget.onSearch?.call(value);
              },
              decoration: InputDecoration(
                hintText: widget.hintText,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _controller.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          widget.onSearchChanged?.call('');
                          widget.onSearch?.call('');
                          widget.onClear?.call();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).primaryColor),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          if (widget.showFilter) ...[
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: widget.onFilterPressed,
                tooltip: 'Filter',
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Advanced search bar with quick filters
class ProductionAdvancedSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<ProductionOrderStatus?>? onStatusFilterChanged;
  final VoidCallback? onDateFilterPressed;
  final TextEditingController? controller;
  final ProductionOrderStatus? selectedStatus;

  const ProductionAdvancedSearchBar({
    Key? key,
    this.hintText = 'Search production orders...',
    this.onSearchChanged,
    this.onStatusFilterChanged,
    this.onDateFilterPressed,
    this.controller,
    this.selectedStatus,
  }) : super(key: key);

  @override
  State<ProductionAdvancedSearchBar> createState() => _ProductionAdvancedSearchBarState();
}

class _ProductionAdvancedSearchBarState extends State<ProductionAdvancedSearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search field
          TextField(
            controller: _controller,
            onChanged: widget.onSearchChanged,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _controller.clear();
                        widget.onSearchChanged?.call('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Theme.of(context).primaryColor),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),
          
          // Quick filters
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<ProductionOrderStatus?>(
                  value: widget.selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<ProductionOrderStatus?>(
                      value: null,
                      child: Text('All Statuses'),
                    ),
                    ...ProductionOrderStatus.values.map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(status.displayName),
                    )),
                  ],
                  onChanged: widget.onStatusFilterChanged,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: const Icon(Icons.date_range),
                  onPressed: widget.onDateFilterPressed,
                  tooltip: 'Date Filter',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Placeholder enum - this should be defined in production_entities.dart
enum ProductionOrderStatus { planned, inProgress, completed, cancelled }

extension ProductionOrderStatusExtension on ProductionOrderStatus {
  String get displayName {
    switch (this) {
      case ProductionOrderStatus.planned:
        return 'Planned';
      case ProductionOrderStatus.inProgress:
        return 'In Progress';
      case ProductionOrderStatus.completed:
        return 'Completed';
      case ProductionOrderStatus.cancelled:
        return 'Cancelled';
    }
  }
}