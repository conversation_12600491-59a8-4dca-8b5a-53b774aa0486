import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/shared/models/pagination.dart';
import 'package:hm_collection/features/quality_control/domain/entities/quality_entities.dart';
import 'package:hm_collection/features/quality_control/domain/usecases/quality_usecases.dart';

import '../../presentation/widgets/quality_statistics_card.dart';
import '../entities/defect_analysis_entity.dart';
import '../entities/quality_trends_entity.dart';
import '../entities/requests/create_quality_standard_request.dart';
import '../entities/requests/update_quality_defect_request.dart';
import '../entities/requests/update_quality_inspection_request.dart';
import '../models/requests/add_quality_defect_request.dart';
import '../models/requests/create_quality_inspection_request.dart';

abstract class QualityRepository {
  Future<Either<Failure, ApiListResponse<QualityInspection>>> getQualityInspections(
      GetQualityInspectionsParams params);

  Future<Either<Failure, ApiResponse<QualityInspection>>> getQualityInspectionById(
      String inspectionId);

  Future<Either<Failure, ApiResponse<QualityInspection>>> createQualityInspection(
      CreateQualityInspectionRequest request);

  Future<Either<Failure, ApiResponse<QualityInspection>>> updateQualityInspection(
      UpdateQualityInspectionRequest request);

  Future<Either<Failure, ApiResponse<QualityInspection>>> startQualityInspection(
      String inspectionId);

  Future<Either<Failure, ApiResponse<QualityInspection>>> completeQualityInspection(
      CompleteQualityInspectionParams params);

  Future<Either<Failure, ApiResponse<CheckpointResult>>> updateCheckpointResult(
      UpdateCheckpointResultParams params);

  Future<Either<Failure, ApiResponse<QualityDefect>>> addQualityDefect(
      AddQualityDefectRequest request);

  Future<Either<Failure, ApiResponse<QualityDefect>>> updateQualityDefect(
      UpdateQualityDefectRequest request);

  Future<Either<Failure, ApiResponse<QualityDefect>>> resolveQualityDefect(
      ResolveQualityDefectParams params);

  Future<Either<Failure, ApiListResponse<QualityDefect>>> getQualityDefects(
      GetQualityDefectsParams params);

  Future<Either<Failure, ApiListResponse<QualityStandard>>> getQualityStandards(
      GetQualityStandardsParams params);

  Future<Either<Failure, ApiResponse<QualityStandard>>> createQualityStandard(
      CreateQualityStandardRequest request);

  Future<Either<Failure, ApiResponse<QualityStandard>>> approveQualityStandard(
      ApproveQualityStandardParams params);

  Future<Either<Failure, QualityStatistics>> getQualityStatistics(
      GetQualityStatisticsParams params);

  Future<Either<Failure, QualityTrends>> getQualityTrends(
      GetQualityTrendsParams params);

  Future<Either<Failure, DefectAnalysis>> getDefectAnalysis(
      GetDefectAnalysisParams params);

  Future<Either<Failure, ApiListResponse<QualityInspection>>>
      searchQualityInspections(SearchQualityInspectionsParams params);

  Future<Either<Failure, ApiListResponse<QualityInspection>>>
      getPendingInspections(GetPendingInspectionsParams params);

  Future<Either<Failure, ApiListResponse<QualityInspection>>>
      getFailedInspections(GetFailedInspectionsParams params);
}
