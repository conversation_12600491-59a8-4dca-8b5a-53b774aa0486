import 'package:equatable/equatable.dart';

/// Password reset confirmation request
class PasswordResetConfirmRequest extends Equatable {
  final String token;
  final String email;
  final String newPassword;
  final String confirmPassword;

  const PasswordResetConfirmRequest({
    required this.token,
    required this.email,
    required this.newPassword,
    required this.confirmPassword,
  });

  /// Validate passwords match
  bool get isValid =>
      token.isNotEmpty &&
      email.isNotEmpty &&
      newPassword.isNotEmpty &&
      newPassword == confirmPassword &&
      newPassword.length >= 6;

  @override
  List<Object> get props => [token, email, newPassword, confirmPassword];

  @override
  bool get stringify => true;
}
