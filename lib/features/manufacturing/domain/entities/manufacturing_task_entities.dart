import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';
import '../../../production_planning/domain/entities/production_entities.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import 'manufacturing_entities.dart';

/// Manufacturing Task Order entity - represents a production order that flows through manufacturing stages
class ManufacturingTaskOrder extends BaseEntity {
  final String orderNumber;
  final String productionOrderId;
  final String productId;
  final String productName;
  final String clientName;
  final int totalQuantity;
  final int completedQuantity;
  final ManufacturingOrderStatus status;
  final ManufacturingOrderPriority priority;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<ManufacturingStageTask> stageTasks;
  final ManufacturingStage currentStage;
  final String? assignedSupervisor;
  final Map<String, dynamic> specifications;
  final List<ManufacturingTaskNote> notes;
  final ManufacturingTaskMetrics metrics;
  final Map<String, dynamic> metadata;

  const ManufacturingTaskOrder({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.orderNumber,
    required this.productionOrderId,
    required this.productId,
    required this.productName,
    required this.clientName,
    required this.totalQuantity,
    this.completedQuantity = 0,
    required this.status,
    required this.priority,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.stageTasks = const [],
    required this.currentStage,
    this.assignedSupervisor,
    this.specifications = const {},
    this.notes = const [],
    required this.metrics,
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        orderNumber,
        productionOrderId,
        productId,
        productName,
        clientName,
        totalQuantity,
        completedQuantity,
        status,
        priority,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        stageTasks,
        currentStage,
        assignedSupervisor,
        specifications,
        notes,
        metrics,
        metadata,
      ];

  /// Calculate completion percentage
  double get completionPercentage {
    if (totalQuantity == 0) return 0.0;
    return (completedQuantity / totalQuantity) * 100;
  }

  /// Check if order is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(plannedEndDate) && !status.isCompleted;
  }

  /// Get current stage task
  ManufacturingStageTask? get currentStageTask {
    return stageTasks.where((task) => task.stage == currentStage).firstOrNull;
  }

  /// Get completed stages
  List<ManufacturingStage> get completedStages {
    return stageTasks
        .where((task) => task.status == TaskStatus.completed)
        .map((task) => task.stage)
        .toList();
  }

  /// Get pending stages
  List<ManufacturingStage> get pendingStages {
    return stageTasks
        .where((task) => task.status != TaskStatus.completed)
        .map((task) => task.stage)
        .toList();
  }
}

/// Manufacturing Stage Task entity - represents a specific task within a manufacturing stage
class ManufacturingStageTask extends BaseEntity {
  final String manufacturingTaskOrderId;
  final ManufacturingStage stage;
  final TaskType taskType;
  final String taskName;
  final String description;
  final TaskStatus status;
  final TaskPriority priority;
  final DepartmentType department;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<String> assignedWorkers;
  final List<String> requiredSkills;
  final int estimatedHours;
  final int actualHours;
  final int quantityToProcess;
  final int quantityProcessed;
  final double qualityScore;
  final String? qualityFeedback;
  final List<TaskAttachment> attachments;
  final List<TaskComment> comments;
  final Map<String, dynamic> specifications;
  final Map<String, dynamic> metadata;

  const ManufacturingStageTask({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.manufacturingTaskOrderId,
    required this.stage,
    required this.taskType,
    required this.taskName,
    required this.description,
    required this.status,
    required this.priority,
    required this.department,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.assignedWorkers = const [],
    this.requiredSkills = const [],
    required this.estimatedHours,
    this.actualHours = 0,
    required this.quantityToProcess,
    this.quantityProcessed = 0,
    this.qualityScore = 0.0,
    this.qualityFeedback,
    this.attachments = const [],
    this.comments = const [],
    this.specifications = const {},
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        manufacturingTaskOrderId,
        stage,
        taskType,
        taskName,
        description,
        status,
        priority,
        department,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        assignedWorkers,
        requiredSkills,
        estimatedHours,
        actualHours,
        quantityToProcess,
        quantityProcessed,
        qualityScore,
        qualityFeedback,
        attachments,
        comments,
        specifications,
        metadata,
      ];

  /// Calculate completion percentage
  double get completionPercentage {
    if (quantityToProcess == 0) return 0.0;
    return (quantityProcessed / quantityToProcess) * 100;
  }

  /// Check if task is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(plannedEndDate) && !status.isCompleted;
  }

  /// Check if task can start
  bool get canStart {
    return status == TaskStatus.pending || status == TaskStatus.ready;
  }

  /// Get efficiency percentage
  double get efficiency {
    if (actualHours == 0 || estimatedHours == 0) return 0.0;
    return (estimatedHours / actualHours) * 100;
  }

  /// Check if task is in progress
  bool get isInProgress {
    return status == TaskStatus.inProgress;
  }

  /// Check if task is completed
  bool get isCompleted {
    return status == TaskStatus.completed;
  }
}

/// Manufacturing Stage Transfer entity - represents transfer of tasks between stages
class ManufacturingStageTransfer extends BaseEntity {
  final String manufacturingTaskOrderId;
  final String stageTaskId;
  final ManufacturingStage fromStage;
  final ManufacturingStage toStage;
  final String transferredBy;
  final String transferredByName;
  final String? receivedBy;
  final String? receivedByName;
  final DateTime transferredAt;
  final DateTime? receivedAt;
  final TransferStatus status;
  final int quantityTransferred;
  final String? notes;
  final List<TransferAttachment> attachments;
  final Map<String, dynamic> metadata;

  const ManufacturingStageTransfer({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.manufacturingTaskOrderId,
    required this.stageTaskId,
    required this.fromStage,
    required this.toStage,
    required this.transferredBy,
    required this.transferredByName,
    this.receivedBy,
    this.receivedByName,
    required this.transferredAt,
    this.receivedAt,
    required this.status,
    required this.quantityTransferred,
    this.notes,
    this.attachments = const [],
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        manufacturingTaskOrderId,
        stageTaskId,
        fromStage,
        toStage,
        transferredBy,
        transferredByName,
        receivedBy,
        receivedByName,
        transferredAt,
        receivedAt,
        status,
        quantityTransferred,
        notes,
        attachments,
        metadata,
      ];

  /// Check if transfer is pending
  bool get isPending {
    return status == TransferStatus.pending;
  }

  /// Check if transfer is completed
  bool get isCompleted {
    return status == TransferStatus.completed;
  }

  /// Get transfer duration in hours
  int? get transferDurationHours {
    if (receivedAt == null) return null;
    return receivedAt!.difference(transferredAt).inHours;
  }
}

/// Manufacturing Task Note entity
class ManufacturingTaskNote extends BaseEntity {
  final String manufacturingTaskOrderId;
  final String? stageTaskId;
  final String userId;
  final String userName;
  final String content;
  final NoteType type;
  final NotePriority priority;
  final bool isInternal;
  final List<NoteAttachment> attachments;

  const ManufacturingTaskNote({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.manufacturingTaskOrderId,
    this.stageTaskId,
    required this.userId,
    required this.userName,
    required this.content,
    required this.type,
    required this.priority,
    this.isInternal = false,
    this.attachments = const [],
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        manufacturingTaskOrderId,
        stageTaskId,
        userId,
        userName,
        content,
        type,
        priority,
        isInternal,
        attachments,
      ];
}

/// Manufacturing Task Metrics entity
class ManufacturingTaskMetrics extends Equatable {
  final double plannedEfficiency;
  final double actualEfficiency;
  final double qualityScore;
  final double onTimeDelivery;
  final double resourceUtilization;
  final int totalStages;
  final int completedStages;
  final double averageStageTime;
  final double costVariance;
  final double scheduleVariance;

  const ManufacturingTaskMetrics({
    required this.plannedEfficiency,
    required this.actualEfficiency,
    required this.qualityScore,
    required this.onTimeDelivery,
    required this.resourceUtilization,
    required this.totalStages,
    required this.completedStages,
    required this.averageStageTime,
    required this.costVariance,
    required this.scheduleVariance,
  });

  @override
  List<Object?> get props => [
        plannedEfficiency,
        actualEfficiency,
        qualityScore,
        onTimeDelivery,
        resourceUtilization,
        totalStages,
        completedStages,
        averageStageTime,
        costVariance,
        scheduleVariance,
      ];

  /// Calculate overall performance score
  double get overallPerformance {
    return (actualEfficiency + qualityScore + onTimeDelivery + resourceUtilization) / 4;
  }

  /// Get stage completion percentage
  double get stageCompletionPercentage {
    if (totalStages == 0) return 0.0;
    return (completedStages / totalStages) * 100;
  }
}

/// Transfer Attachment entity
class TransferAttachment extends Equatable {
  final String id;
  final String fileName;
  final String fileUrl;
  final String fileType;
  final int fileSize;
  final DateTime uploadedAt;
  final String uploadedBy;

  const TransferAttachment({
    required this.id,
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
    required this.uploadedAt,
    required this.uploadedBy,
  });

  @override
  List<Object?> get props => [
        id,
        fileName,
        fileUrl,
        fileType,
        fileSize,
        uploadedAt,
        uploadedBy,
      ];

  /// Convert from JSON
  factory TransferAttachment.fromJson(Map<String, dynamic> json) {
    return TransferAttachment(
      id: json['id'] ?? '',
      fileName: json['fileName'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
      fileType: json['fileType'] ?? '',
      fileSize: json['fileSize'] ?? 0,
      uploadedAt: json['uploadedAt'] is String
          ? DateTime.parse(json['uploadedAt'])
          : (json['uploadedAt'] as Timestamp).toDate(),
      uploadedBy: json['uploadedBy'] ?? '',
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'fileUrl': fileUrl,
      'fileType': fileType,
      'fileSize': fileSize,
      'uploadedAt': uploadedAt.toIso8601String(),
      'uploadedBy': uploadedBy,
    };
  }
}

/// Note Attachment entity
class NoteAttachment extends Equatable {
  final String id;
  final String fileName;
  final String fileUrl;
  final String fileType;
  final int fileSize;
  final DateTime uploadedAt;

  const NoteAttachment({
    required this.id,
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
    required this.uploadedAt,
  });

  @override
  List<Object?> get props => [
        id,
        fileName,
        fileUrl,
        fileType,
        fileSize,
        uploadedAt,
      ];

  /// Convert from JSON
  factory NoteAttachment.fromJson(Map<String, dynamic> json) {
    return NoteAttachment(
      id: json['id'] ?? '',
      fileName: json['fileName'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
      fileType: json['fileType'] ?? '',
      fileSize: json['fileSize'] ?? 0,
      uploadedAt: json['uploadedAt'] is String
          ? DateTime.parse(json['uploadedAt'])
          : (json['uploadedAt'] as Timestamp).toDate(),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'fileUrl': fileUrl,
      'fileType': fileType,
      'fileSize': fileSize,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }
}

// Enums

/// Manufacturing Order Status enum
enum ManufacturingOrderStatus {
  created,
  planned,
  scheduled,
  inProgress,
  paused,
  completed,
  cancelled,
  onHold,
}

/// Extension for ManufacturingOrderStatus
extension ManufacturingOrderStatusExtension on ManufacturingOrderStatus {
  String get displayName {
    switch (this) {
      case ManufacturingOrderStatus.created:
        return 'Created';
      case ManufacturingOrderStatus.planned:
        return 'Planned';
      case ManufacturingOrderStatus.scheduled:
        return 'Scheduled';
      case ManufacturingOrderStatus.inProgress:
        return 'In Progress';
      case ManufacturingOrderStatus.paused:
        return 'Paused';
      case ManufacturingOrderStatus.completed:
        return 'Completed';
      case ManufacturingOrderStatus.cancelled:
        return 'Cancelled';
      case ManufacturingOrderStatus.onHold:
        return 'On Hold';
    }
  }

  String get colorCode {
    switch (this) {
      case ManufacturingOrderStatus.created:
        return '#6B7280'; // Gray
      case ManufacturingOrderStatus.planned:
        return '#8B5CF6'; // Purple
      case ManufacturingOrderStatus.scheduled:
        return '#3B82F6'; // Blue
      case ManufacturingOrderStatus.inProgress:
        return '#10B981'; // Green
      case ManufacturingOrderStatus.paused:
        return '#F59E0B'; // Amber
      case ManufacturingOrderStatus.completed:
        return '#059669'; // Emerald
      case ManufacturingOrderStatus.cancelled:
        return '#EF4444'; // Red
      case ManufacturingOrderStatus.onHold:
        return '#F97316'; // Orange
    }
  }

  bool get isCompleted => this == ManufacturingOrderStatus.completed;
  bool get isActive => [
        ManufacturingOrderStatus.planned,
        ManufacturingOrderStatus.scheduled,
        ManufacturingOrderStatus.inProgress,
      ].contains(this);
}

/// Manufacturing Order Priority enum
enum ManufacturingOrderPriority {
  low,
  normal,
  high,
  urgent,
  critical,
}

/// Extension for ManufacturingOrderPriority
extension ManufacturingOrderPriorityExtension on ManufacturingOrderPriority {
  String get displayName {
    switch (this) {
      case ManufacturingOrderPriority.low:
        return 'Low';
      case ManufacturingOrderPriority.normal:
        return 'Normal';
      case ManufacturingOrderPriority.high:
        return 'High';
      case ManufacturingOrderPriority.urgent:
        return 'Urgent';
      case ManufacturingOrderPriority.critical:
        return 'Critical';
    }
  }

  String get colorCode {
    switch (this) {
      case ManufacturingOrderPriority.low:
        return '#6B7280'; // Gray
      case ManufacturingOrderPriority.normal:
        return '#3B82F6'; // Blue
      case ManufacturingOrderPriority.high:
        return '#F59E0B'; // Amber
      case ManufacturingOrderPriority.urgent:
        return '#EF4444'; // Red
      case ManufacturingOrderPriority.critical:
        return '#DC2626'; // Dark Red
    }
  }

  int get priorityLevel {
    switch (this) {
      case ManufacturingOrderPriority.low:
        return 1;
      case ManufacturingOrderPriority.normal:
        return 2;
      case ManufacturingOrderPriority.high:
        return 3;
      case ManufacturingOrderPriority.urgent:
        return 4;
      case ManufacturingOrderPriority.critical:
        return 5;
    }
  }
}

/// Manufacturing Stage enum - represents the sequential stages in manufacturing
enum ManufacturingStage {
  // Cutting & Preparation
  cuttingMaster,
  lupCutting,
  munda,

  // Stitching & Assembly
  singer,
  overlock,
  fayThread,
  bottomHemming,
  iKaaj,

  // Hardware & Accessories
  belt,
  elasticBelt,
  beltPacking,
  luppi,
  baltec,
  iLet,
  buckle,

  // Finishing Operations
  shouting,
  threadCutting,
  pressing,

  // Quality & Documentation
  image,
  numbering,

  // Processing & Packaging
  washing,
  labeling,
  packing,
  bundling,
  pocting,
  plasticBag,

  // Logistics
  receiveGoods,
  dispatch,
}

/// Extension for ManufacturingStage
extension ManufacturingStageExtension on ManufacturingStage {
  String get displayName {
    switch (this) {
      // Cutting & Preparation
      case ManufacturingStage.cuttingMaster:
        return 'Cutting Master';
      case ManufacturingStage.lupCutting:
        return 'Lup Cutting';
      case ManufacturingStage.munda:
        return 'Munda (Fabric Laying)';

      // Stitching & Assembly
      case ManufacturingStage.singer:
        return 'Singer (Sewing Machine)';
      case ManufacturingStage.overlock:
        return 'Overlock (Edge Finishing)';
      case ManufacturingStage.fayThread:
        return 'Fay-thread (Thread Work)';
      case ManufacturingStage.bottomHemming:
        return 'Bottom Hemming';
      case ManufacturingStage.iKaaj:
        return 'I-Kaaj (Stitching Technique)';

      // Hardware & Accessories
      case ManufacturingStage.belt:
        return 'Belt Operations';
      case ManufacturingStage.elasticBelt:
        return 'Elastic Belt';
      case ManufacturingStage.beltPacking:
        return 'Belt Packing';
      case ManufacturingStage.luppi:
        return 'Luppi (Loop Attachment)';
      case ManufacturingStage.baltec:
        return 'Baltec (Belt Technology)';
      case ManufacturingStage.iLet:
        return 'I-Let (Button Attachment)';
      case ManufacturingStage.buckle:
        return 'Buckle (Adjuster)';

      // Finishing Operations
      case ManufacturingStage.shouting:
        return 'Shouting (Quality Inspection)';
      case ManufacturingStage.threadCutting:
        return 'Thread Cutting';
      case ManufacturingStage.pressing:
        return 'Pressing (Ironing)';

      // Quality & Documentation
      case ManufacturingStage.image:
        return 'Image (Sample Documentation)';
      case ManufacturingStage.numbering:
        return 'Numbering (Product Tagging)';

      // Processing & Packaging
      case ManufacturingStage.washing:
        return 'Washing';
      case ManufacturingStage.labeling:
        return 'Labeling';
      case ManufacturingStage.packing:
        return 'Packing';
      case ManufacturingStage.bundling:
        return 'Bundling';
      case ManufacturingStage.pocting:
        return 'Pocting (Pocket Operations)';
      case ManufacturingStage.plasticBag:
        return 'Plastic Bag Packaging';

      // Logistics
      case ManufacturingStage.receiveGoods:
        return 'Receive Goods';
      case ManufacturingStage.dispatch:
        return 'Dispatch';
    }
  }

  String get description {
    switch (this) {
      // Cutting & Preparation
      case ManufacturingStage.cuttingMaster:
        return 'Master cutting operations and pattern management';
      case ManufacturingStage.lupCutting:
        return 'Specialized lup cutting technique';
      case ManufacturingStage.munda:
        return 'Fabric laying and spreading operations';

      // Stitching & Assembly
      case ManufacturingStage.singer:
        return 'Sewing machine operations and garment assembly';
      case ManufacturingStage.overlock:
        return 'Edge finishing and seam reinforcement';
      case ManufacturingStage.fayThread:
        return 'Specialized thread work and detailing';
      case ManufacturingStage.bottomHemming:
        return 'Bottom edge hemming and finishing';
      case ManufacturingStage.iKaaj:
        return 'Specialized stitching technique for garment construction';

      // Hardware & Accessories
      case ManufacturingStage.belt:
        return 'Belt attachment and processing operations';
      case ManufacturingStage.elasticBelt:
        return 'Elastic belt installation and adjustment';
      case ManufacturingStage.beltPacking:
        return 'Belt packaging and preparation';
      case ManufacturingStage.luppi:
        return 'Loop attachment and belt loop operations';
      case ManufacturingStage.baltec:
        return 'Advanced belt technology and hardware installation';
      case ManufacturingStage.iLet:
        return 'Button attachment and fastening operations';
      case ManufacturingStage.buckle:
        return 'Buckle and adjuster installation';

      // Finishing Operations
      case ManufacturingStage.shouting:
        return 'Quality inspection call-outs and defect identification';
      case ManufacturingStage.threadCutting:
        return 'Thread trimming and cleanup operations';
      case ManufacturingStage.pressing:
        return 'Ironing and garment pressing operations';

      // Quality & Documentation
      case ManufacturingStage.image:
        return 'Sample documentation and photography';
      case ManufacturingStage.numbering:
        return 'Product numbering and tagging operations';

      // Processing & Packaging
      case ManufacturingStage.washing:
        return 'Garment washing and treatment processes';
      case ManufacturingStage.labeling:
        return 'Label attachment and branding operations';
      case ManufacturingStage.packing:
        return 'Final packing and preparation for shipment';
      case ManufacturingStage.bundling:
        return 'Product bundling and grouping operations';
      case ManufacturingStage.pocting:
        return 'Pocket operations and pocket attachment';
      case ManufacturingStage.plasticBag:
        return 'Plastic bag packaging and protection';

      // Logistics
      case ManufacturingStage.receiveGoods:
        return 'Goods receiving and inventory management';
      case ManufacturingStage.dispatch:
        return 'Final dispatch and shipping operations';
    }
  }

  /// Get the department type that typically handles this stage
  DepartmentType get department {
    switch (this) {
      case ManufacturingStage.cuttingMaster:
      case ManufacturingStage.lupCutting:
      case ManufacturingStage.munda:
        return DepartmentType.cutting;

      case ManufacturingStage.singer:
      case ManufacturingStage.overlock:
      case ManufacturingStage.fayThread:
      case ManufacturingStage.bottomHemming:
      case ManufacturingStage.iKaaj:
      case ManufacturingStage.belt:
      case ManufacturingStage.elasticBelt:
      case ManufacturingStage.beltPacking:
      case ManufacturingStage.luppi:
      case ManufacturingStage.baltec:
      case ManufacturingStage.iLet:
      case ManufacturingStage.buckle:
        return DepartmentType.stitching;

      case ManufacturingStage.shouting:
      case ManufacturingStage.threadCutting:
      case ManufacturingStage.pressing:
      case ManufacturingStage.washing:
        return DepartmentType.finishing;

      case ManufacturingStage.image:
        return DepartmentType.quality;

      case ManufacturingStage.numbering:
        return DepartmentType.production;

      case ManufacturingStage.labeling:
      case ManufacturingStage.packing:
      case ManufacturingStage.bundling:
      case ManufacturingStage.pocting:
      case ManufacturingStage.plasticBag:
        return DepartmentType.packing;

      case ManufacturingStage.receiveGoods:
      case ManufacturingStage.dispatch:
        return DepartmentType.warehouse;
    }
  }

  /// Get the corresponding task type
  TaskType get taskType {
    switch (this) {
      case ManufacturingStage.cuttingMaster:
        return TaskType.cuttingMaster;
      case ManufacturingStage.lupCutting:
        return TaskType.lupCutting;
      case ManufacturingStage.munda:
        return TaskType.munda;
      case ManufacturingStage.singer:
        return TaskType.singer;
      case ManufacturingStage.overlock:
        return TaskType.overlock;
      case ManufacturingStage.fayThread:
        return TaskType.fayThread;
      case ManufacturingStage.bottomHemming:
        return TaskType.bottomHemming;
      case ManufacturingStage.iKaaj:
        return TaskType.iKaaj;
      case ManufacturingStage.belt:
        return TaskType.belt;
      case ManufacturingStage.elasticBelt:
        return TaskType.elasticBelt;
      case ManufacturingStage.beltPacking:
        return TaskType.beltPacking;
      case ManufacturingStage.luppi:
        return TaskType.luppi;
      case ManufacturingStage.baltec:
        return TaskType.baltec;
      case ManufacturingStage.iLet:
        return TaskType.iLet;
      case ManufacturingStage.buckle:
        return TaskType.buckle;
      case ManufacturingStage.shouting:
        return TaskType.shouting;
      case ManufacturingStage.threadCutting:
        return TaskType.threadCutting;
      case ManufacturingStage.pressing:
        return TaskType.pressing;
      case ManufacturingStage.image:
        return TaskType.image;
      case ManufacturingStage.numbering:
        return TaskType.numbering;
      case ManufacturingStage.washing:
        return TaskType.washing;
      case ManufacturingStage.labeling:
        return TaskType.labeling;
      case ManufacturingStage.packing:
        return TaskType.packing;
      case ManufacturingStage.bundling:
        return TaskType.bundling;
      case ManufacturingStage.pocting:
        return TaskType.pocting;
      case ManufacturingStage.plasticBag:
        return TaskType.plasticBag;
      case ManufacturingStage.receiveGoods:
        return TaskType.receiveGoods;
      case ManufacturingStage.dispatch:
        return TaskType.dispatch;
    }
  }

  /// Get estimated hours for this stage
  int get estimatedHours {
    return taskType.estimatedHours;
  }

  /// Get the next stage in the workflow
  ManufacturingStage? get nextStage {
    const stages = ManufacturingStage.values;
    final currentIndex = stages.indexOf(this);
    if (currentIndex < stages.length - 1) {
      return stages[currentIndex + 1];
    }
    return null; // Last stage
  }

  /// Get the previous stage in the workflow
  ManufacturingStage? get previousStage {
    const stages = ManufacturingStage.values;
    final currentIndex = stages.indexOf(this);
    if (currentIndex > 0) {
      return stages[currentIndex - 1];
    }
    return null; // First stage
  }

  /// Check if this is the first stage
  bool get isFirstStage => this == ManufacturingStage.values.first;

  /// Check if this is the last stage
  bool get isLastStage => this == ManufacturingStage.values.last;
}

/// Transfer Status enum
enum TransferStatus {
  pending,
  inTransit,
  completed,
  rejected,
  cancelled,
}

/// Extension for TransferStatus
extension TransferStatusExtension on TransferStatus {
  String get displayName {
    switch (this) {
      case TransferStatus.pending:
        return 'Pending';
      case TransferStatus.inTransit:
        return 'In Transit';
      case TransferStatus.completed:
        return 'Completed';
      case TransferStatus.rejected:
        return 'Rejected';
      case TransferStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get colorCode {
    switch (this) {
      case TransferStatus.pending:
        return '#6B7280'; // Gray
      case TransferStatus.inTransit:
        return '#3B82F6'; // Blue
      case TransferStatus.completed:
        return '#10B981'; // Green
      case TransferStatus.rejected:
        return '#EF4444'; // Red
      case TransferStatus.cancelled:
        return '#F97316'; // Orange
    }
  }

  bool get isCompleted => this == TransferStatus.completed;
  bool get isActive => [
        TransferStatus.pending,
        TransferStatus.inTransit,
      ].contains(this);
}
