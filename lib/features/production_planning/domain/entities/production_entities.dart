import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../../../shared/enums/common_enums.dart' show AllocationStatus, TaskStatus, DepartmentType, TaskType, TaskPriority, ResourceType, NoteType, NotePriority;
import '../../../../shared/models/base_entity.dart';
import '../../../manufacturing/domain/entities/manufacturing_entities.dart' hide TaskStatus;
import '../../../manufacturing/domain/entities/employee.dart'; // Added import for EmployeeFilterCriteria
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../../resource_management/presentation/bloc/resource_bloc.dart';
import '../../data/models/production_order_filter.dart';
import '../../presentation/widgets/production_search_bar.dart';

/// Production order entity
class ProductionOrder extends BaseEntity {
  final String productionOrderNumber;
  final String manufacturingOrderId;
  final String orderNumber;
  final String clientName;
  final ProductionStatus status;
  final ProductionPriority priority;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<ProductionTask> tasks;
  final List<ResourceAllocation> resourceAllocations;
  final ProductionMetrics metrics;
  final List<ProductionNote> notes;
  final Map<String, dynamic> metadata;
  final String? assignedSupervisor;
  final DepartmentType currentDepartment;
  final double completionPercentage;
  final int totalQuantity;
  final int completedQuantity;

  const ProductionOrder({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.productionOrderNumber,
    required this.manufacturingOrderId,
    required this.orderNumber,
    required this.clientName,
    required this.status,
    required this.priority,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    required this.tasks,
    required this.resourceAllocations,
    required this.metrics,
    this.notes = const [],
    this.metadata = const {},
    this.assignedSupervisor,
    required this.currentDepartment,
    this.completionPercentage = 0.0,
    required this.totalQuantity,
    this.completedQuantity = 0,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        productionOrderNumber,
        manufacturingOrderId,
        orderNumber,
        clientName,
        status,
        priority,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        tasks,
        resourceAllocations,
        metrics,
        notes,
        metadata,
        assignedSupervisor,
        currentDepartment,
        completionPercentage,
        totalQuantity,
        completedQuantity,
      ];

  /// Check if production order is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(plannedEndDate) && !status.isCompleted;
  }

  /// Get remaining quantity
  int get remainingQuantity => totalQuantity - completedQuantity;

  /// Check if production order is urgent
  bool get isUrgent {
    return priority == ProductionPriority.urgent || 
           priority == ProductionPriority.critical ||
           (plannedEndDate.difference(DateTime.now()).inDays <= 2);
  }

  /// Get estimated completion date
  DateTime? get estimatedCompletionDate {
    if (completionPercentage == 0) return plannedEndDate;
    
    final elapsed = actualStartDate != null 
        ? DateTime.now().difference(actualStartDate!)
        : Duration.zero;
    
    if (completionPercentage > 0) {
      final totalEstimated = elapsed.inMilliseconds / (completionPercentage / 100);
      return actualStartDate?.add(Duration(milliseconds: totalEstimated.round()));
    }
    
    return plannedEndDate;
  }

  /// Get production efficiency
  double get efficiency {
    if (actualStartDate == null || completionPercentage == 0) return 0.0;
    
    final plannedDuration = plannedEndDate.difference(plannedStartDate).inHours;
    final actualDuration = DateTime.now().difference(actualStartDate!).inHours;
    
    if (actualDuration == 0) return 0.0;
    
    final expectedProgress = (actualDuration / plannedDuration) * 100;
    return completionPercentage / expectedProgress;
  }
}

/// Production task entity
class ProductionTask extends BaseEntity {
  /// Creates a ProductionTask from JSON map
  factory ProductionTask.fromJson(Map<String, dynamic> json) {
    return ProductionTask(
      id: json['id'] as String,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
      deletedAt: json['deletedAt'] != null ? (json['deletedAt'] as Timestamp).toDate() : null,
      productionOrderId: json['productionOrderId'] as String,
      taskName: json['taskName'] as String,
      description: json['description'] as String,
      department: DepartmentType.values.byName(json['department'] ?? 'other'),
      type: TaskType.values.byName(
        json['type'] ?? 'other',
      ),
      status: TaskStatus.values.byName(
        json['status'] ?? 'pending',
      ),
      priority: TaskPriority.values.byName(
        json['priority'] ?? 'normal',
      ),
      plannedStartDate: (json['plannedStartDate'] as Timestamp).toDate(),
      plannedEndDate: (json['plannedEndDate'] as Timestamp).toDate(),
      actualStartDate: json['actualStartDate'] != null ? (json['actualStartDate'] as Timestamp).toDate() : null,
      actualEndDate: json['actualEndDate'] != null ? (json['actualEndDate'] as Timestamp).toDate() : null,
      assignedWorkers: List<String>.from(json['assignedWorkers'] as List? ?? []),
      requiredSkills: List<String>.from(json['requiredSkills'] as List? ?? []),
      resourceRequirements: (json['resourceRequirements'] as List<dynamic>? ?? [])
          .map((e) => ResourceRequirement.fromJson(e as Map<String, dynamic>))
          .toList(),
      metrics: TaskMetrics.fromJson(json['metrics'] as Map<String, dynamic>),
      dependencies: (json['dependencies'] as List<dynamic>? ?? [])
          .map((e) => TaskDependency.fromJson(e as Map<String, dynamic>))
          .toList(),
      specifications: Map<String, dynamic>.from(json['specifications'] as Map? ?? {}),
      completionPercentage: (json['completionPercentage'] as num?)?.toDouble() ?? 0.0,
      estimatedHours: json['estimatedHours'] as int,
      actualHours: json['actualHours'] as int? ?? 0,
    );
  }

  final String productionOrderId;
  final String taskName;
  final String description;
  final DepartmentType department;
  final TaskType type;
  final TaskStatus status;
  final TaskPriority priority;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<String> assignedWorkers;
  final List<String> requiredSkills;
  final List<ResourceRequirement> resourceRequirements;
  final TaskMetrics metrics;
  final List<TaskDependency> dependencies;
  final Map<String, dynamic> specifications;
  final double completionPercentage;
  final int estimatedHours;
  final int actualHours;

  const ProductionTask({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.productionOrderId,
    required this.taskName,
    required this.description,
    required this.department,
    required this.type,
    required this.status,
    required this.priority,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.assignedWorkers = const [],
    this.requiredSkills = const [],
    this.resourceRequirements = const [],
    required this.metrics,
    this.dependencies = const [],
    this.specifications = const {},
    this.completionPercentage = 0.0,
    required this.estimatedHours,
    this.actualHours = 0,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        productionOrderId,
        taskName,
        description,
        department,
        type,
        status,
        priority,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        assignedWorkers,
        requiredSkills,
        resourceRequirements,
        metrics,
        dependencies,
        specifications,
        completionPercentage,
        estimatedHours,
        actualHours,
      ];

  /// Check if task is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(plannedEndDate) && !status.isCompleted;
  }

  /// Check if task can start
  bool get canStart {
    return dependencies.every((dep) => dep.isCompleted) && 
           status == TaskStatus.pending;
  }

  /// Get task efficiency
  double get efficiency {
    if (actualHours == 0 || estimatedHours == 0) return 0.0;
    return (estimatedHours / actualHours) * 100;
  }
}

/// Resource allocation entity
class ResourceAllocation extends BaseEntity {
  final String productionOrderId;
  final String? taskId;
  final ResourceType resourceType;
  final String resourceId;
  final String resourceName;
  final int allocatedQuantity;
  final int usedQuantity;
  final DateTime allocationDate;
  final DateTime? releaseDate;
  final AllocationStatus status;
  final double costPerUnit;
  final double totalCost;
  final Map<String, dynamic> specifications;

  const ResourceAllocation({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.productionOrderId,
    this.taskId,
    required this.resourceType,
    required this.resourceId,
    required this.resourceName,
    required this.allocatedQuantity,
    this.usedQuantity = 0,
    required this.allocationDate,
    this.releaseDate,
    required this.status,
    required this.costPerUnit,
    required this.totalCost,
    this.specifications = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        productionOrderId,
        taskId,
        resourceType,
        resourceId,
        resourceName,
        allocatedQuantity,
        usedQuantity,
        allocationDate,
        releaseDate,
        status,
        costPerUnit,
        totalCost,
        specifications,
      ];

  /// Get remaining quantity
  int get remainingQuantity => allocatedQuantity - usedQuantity;

  /// Get utilization percentage
  double get utilizationPercentage {
    if (allocatedQuantity == 0) return 0.0;
    return (usedQuantity / allocatedQuantity) * 100;
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
      'productionOrderId': productionOrderId,
      'taskId': taskId,
      'resourceType': resourceType.toString().split('.').last,
      'resourceId': resourceId,
      'resourceName': resourceName,
      'allocatedQuantity': allocatedQuantity,
      'usedQuantity': usedQuantity,
      'allocationDate': allocationDate.toIso8601String(),
      'releaseDate': releaseDate?.toIso8601String(),
      'status': status.toString().split('.').last,
      'costPerUnit': costPerUnit,
      'totalCost': totalCost,
      'specifications': Map<String, dynamic>.from(specifications),
    };
  }
}

/// Production metrics
class ProductionMetrics extends Equatable {
  final double plannedEfficiency;
  final double actualEfficiency;
  final double qualityScore;
  final int defectCount;
  final int reworkCount;
  final double costVariance;
  final double scheduleVariance;
  final Map<String, double> departmentEfficiency;
  final double efficiency;
  final double onTimeDelivery;
  final double resourceUtilization;

  const ProductionMetrics({
    required this.plannedEfficiency,
    required this.actualEfficiency,
    required this.qualityScore,
    this.defectCount = 0,
    this.reworkCount = 0,
    required this.costVariance,
    required this.scheduleVariance,
    this.departmentEfficiency = const {},
    required this.efficiency,
    required this.onTimeDelivery,
    required this.resourceUtilization,
  });

  @override
  List<Object?> get props => [
        plannedEfficiency,
        actualEfficiency,
        qualityScore,
        defectCount,
        reworkCount,
        costVariance,
        scheduleVariance,
        departmentEfficiency,
        efficiency,
        onTimeDelivery,
        resourceUtilization,
      ];
}

/// Task metrics
class TaskMetrics extends Equatable {
  final double efficiency;
  final double qualityScore;
  final int defectCount;
  final double costActual;
  final double costPlanned;
  final int hoursActual;
  final int hoursPlanned;

  const TaskMetrics({
    required this.efficiency,
    required this.qualityScore,
    this.defectCount = 0,
    required this.costActual,
    required this.costPlanned,
    required this.hoursActual,
    required this.hoursPlanned,
  });

  @override
  List<Object?> get props => [
        efficiency,
        qualityScore,
        defectCount,
        costActual,
        costPlanned,
        hoursActual,
        hoursPlanned,
      ];

  /// Get cost variance
  double get costVariance => costActual - costPlanned;

  /// Get schedule variance
  double get scheduleVariance => hoursActual.toDouble() - hoursPlanned.toDouble();

  /// Create a TaskMetrics instance from JSON
  factory TaskMetrics.fromJson(Map<String, dynamic> json) {
    return TaskMetrics(
      efficiency: (json['efficiency'] as num?)?.toDouble() ?? 0.0,
      qualityScore: (json['qualityScore'] as num?)?.toDouble() ?? 0.0,
      defectCount: (json['defectCount'] as int?) ?? 0,
      costActual: (json['costActual'] as num?)?.toDouble() ?? 0.0,
      costPlanned: (json['costPlanned'] as num?)?.toDouble() ?? 0.0,
      hoursActual: (json['hoursActual'] as int?) ?? 0,
      hoursPlanned: (json['hoursPlanned'] as int?) ?? 0,
    );
  }
}

/// Resource requirement
class ResourceRequirement extends Equatable {
  final ResourceType type;
  final String resourceId;
  final String resourceName;
  final int requiredQuantity;
  final String unit;
  final DateTime requiredDate;
  final bool isCritical;

  const ResourceRequirement({
    required this.type,
    required this.resourceId,
    required this.resourceName,
    required this.requiredQuantity,
    required this.unit,
    required this.requiredDate,
    this.isCritical = false,
  });

  @override
  List<Object?> get props => [
        type,
        resourceId,
        resourceName,
        requiredQuantity,
        unit,
        requiredDate,
        isCritical,
      ];

  factory ResourceRequirement.fromJson(Map<String, dynamic> json) {
    return ResourceRequirement(
      type: ResourceType.values.byName(json['type'] ?? 'machine'),
      resourceId: json['resourceId'] as String,
      resourceName: json['resourceName'] as String,
      requiredQuantity: json['requiredQuantity'] as int,
      unit: json['unit'] as String,
      requiredDate: (json['requiredDate'] as Timestamp).toDate(),
      isCritical: json['isCritical'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'resourceId': resourceId,
      'resourceName': resourceName,
      'requiredQuantity': requiredQuantity,
      'unit': unit,
      'requiredDate': Timestamp.fromDate(requiredDate),
      'isCritical': isCritical,
    };
  }
}

/// Extension to convert int to DependencyType
extension IntToDependencyType on int {
  DependencyType toDependencyType() {
    return DependencyType.values[this];
  }
}

/// Extension to convert DependencyType to int
extension DependencyTypeToInt on DependencyType {
  int toInt() {
    return index;
  }
}

/// Dependency type enum
enum DependencyType {
  finishToStart,
  startToStart,
  finishToFinish,
  startToFinish,
  milestone,
}

/// Task dependency
class TaskDependency extends Equatable {
  final String dependentTaskId;
  final String dependsOnTaskId;
  final DependencyType type;
  final bool isCompleted;

  const TaskDependency({
    required this.dependentTaskId,
    required this.dependsOnTaskId,
    required this.type,
    this.isCompleted = false,
  });

  @override
  List<Object?> get props => [dependentTaskId, dependsOnTaskId, type, isCompleted];

  factory TaskDependency.fromJson(Map<String, dynamic> json) {
    return TaskDependency(
      dependentTaskId: json['dependentTaskId'] as String,
      dependsOnTaskId: json['dependsOnTaskId'] as String,
      type: DependencyType.values.byName(json['type'] ?? 'finishToStart'),
      isCompleted: json['isCompleted'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dependentTaskId': dependentTaskId,
      'dependsOnTaskId': dependsOnTaskId,
      'type': type.name,
      'isCompleted': isCompleted,
    };
  }
}



/// Update resource allocation request
class UpdateResourceAllocationRequest extends Equatable {
  final String allocationId;
  final String? taskId;
  final String? resourceId;
  final ResourceType? resourceType;
  final DateTime? startTime;
  final DateTime? endTime;
  final double? quantity;
  final String? notes;
  final bool? isReleased;
  final String? releaseReason;

  const UpdateResourceAllocationRequest({
    required this.allocationId,
    this.taskId,
    this.resourceId,
    this.resourceType,
    this.startTime,
    this.endTime,
    this.quantity,
    this.notes,
    this.isReleased,
    this.releaseReason,
  });

  Map<String, dynamic> toJson() {
    return {
      'allocationId': allocationId,
      if (taskId != null) 'taskId': taskId,
      if (resourceId != null) 'resourceId': resourceId,
      if (resourceType != null) 'resourceType': resourceType?.name,
      if (startTime != null) 'startTime': startTime?.toIso8601String(),
      if (endTime != null) 'endTime': endTime?.toIso8601String(),
      if (quantity != null) 'quantity': quantity,
      if (notes != null) 'notes': notes,
      if (isReleased != null) 'isReleased': isReleased,
      if (releaseReason != null) 'reason': releaseReason,
    };
  }

  @override
  List<Object?> get props => [
        allocationId,
        taskId,
        resourceId,
        resourceType,
        startTime,
        endTime,
        quantity,
        notes,
        isReleased,
        releaseReason,
      ];
}

/// Production note
class ProductionNote extends BaseEntity {
  final String productionOrderId;
  final String? taskId;
  final String userId;
  final String userName;
  final String content;
  final NoteType type;
  final NotePriority priority;
  final bool isInternal;

  const ProductionNote({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.productionOrderId,
    this.taskId,
    required this.userId,
    required this.userName,
    required this.content,
    required this.type,
    required this.priority,
    this.isInternal = false,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        productionOrderId,
        taskId,
        userId,
        userName,
        content,
        type,
        priority,
        isInternal,
      ];
}

// Enums

/// Note type enum
enum NoteType {
  general,
  quality,
  safety,
  maintenance,
  productionIssue,
  materialIssue,
  other,
}

/// Production status enum
enum ProductionStatus {
  planned,
  scheduled,
  inProgress,
  paused,
  completed,
  cancelled,
  onHold,
}

/// Production status extension
extension ProductionStatusExtension on ProductionStatus {
  String get displayName {
    switch (this) {
      case ProductionStatus.planned:
        return 'Planned';
      case ProductionStatus.scheduled:
        return 'Scheduled';
      case ProductionStatus.inProgress:
        return 'In Progress';
      case ProductionStatus.paused:
        return 'Paused';
      case ProductionStatus.completed:
        return 'Completed';
      case ProductionStatus.cancelled:
        return 'Cancelled';
      case ProductionStatus.onHold:
        return 'On Hold';
    }
  }

  Color get color {
    switch (this) {
      case ProductionStatus.planned:
        return Colors.blue;
      case ProductionStatus.scheduled:
        return Colors.lightBlue;
      case ProductionStatus.inProgress:
        return Colors.green;
      case ProductionStatus.paused:
        return Colors.orange;
      case ProductionStatus.completed:
        return Colors.green.shade700;
      case ProductionStatus.cancelled:
        return Colors.red;
      case ProductionStatus.onHold:
        return Colors.amber;
    }
  }

  IconData get icon {
    switch (this) {
      case ProductionStatus.planned:
        return Icons.edit_calendar_outlined;
      case ProductionStatus.scheduled:
        return Icons.calendar_today_outlined;
      case ProductionStatus.inProgress:
        return Icons.sync;
      case ProductionStatus.paused:
        return Icons.pause_circle_outline;
      case ProductionStatus.completed:
        return Icons.check_circle_outline;
      case ProductionStatus.cancelled:
        return Icons.cancel_outlined;
      case ProductionStatus.onHold:
        return Icons.pan_tool_outlined;
    }
  }

  String get value => name;

  bool get isCompleted => this == ProductionStatus.completed;
  bool get isActive => this == ProductionStatus.inProgress;
  bool get canStart => this == ProductionStatus.scheduled;
  bool get canPause => this == ProductionStatus.inProgress;
  bool get canResume => this == ProductionStatus.paused || this == ProductionStatus.onHold;
}

/// Production priority enum
enum ProductionPriority {
  low,
  normal,
  high,
  urgent,
  critical,
}

/// Production priority extension
extension ProductionPriorityExtension on ProductionPriority {
  String get displayName {
    switch (this) {
      case ProductionPriority.low:
        return 'Low';
      case ProductionPriority.normal:
        return 'Normal';
      case ProductionPriority.high:
        return 'High';
      case ProductionPriority.urgent:
        return 'Urgent';
      case ProductionPriority.critical:
        return 'Critical';
    }
  }

  String get value => name;

  int get level {
    switch (this) {
      case ProductionPriority.low:
        return 1;
      case ProductionPriority.normal:
        return 2;
      case ProductionPriority.high:
        return 3;
      case ProductionPriority.urgent:
        return 4;
      case ProductionPriority.critical:
        return 5;
    }
  }
}

/// Task type enum
enum TaskType {
  // Existing types
  cutting(estimatedHours: 4),
  sewing(estimatedHours: 8),
  finishing(estimatedHours: 2),
  quality(estimatedHours: 1),
  packaging(estimatedHours: 2),
  setup(estimatedHours: 1),
  maintenance(estimatedHours: 4),
  inspection(estimatedHours: 1),
  other(estimatedHours: 2),

  // Cutting & Preparation
  cuttingMaster(estimatedHours: 4),
  lupCutting(estimatedHours: 3),
  munda(estimatedHours: 2),

  // Stitching & Assembly
  singer(estimatedHours: 6),
  overlock(estimatedHours: 5),
  fayThread(estimatedHours: 4),
  bottomHemming(estimatedHours: 3),
  iKaaj(estimatedHours: 5),

  // Hardware & Accessories
  belt(estimatedHours: 2),
  elasticBelt(estimatedHours: 2),
  beltPacking(estimatedHours: 1),
  luppi(estimatedHours: 3),
  baltec(estimatedHours: 2),
  iLet(estimatedHours: 2),
  buckle(estimatedHours: 1),

  // Finishing Operations
  shouting(estimatedHours: 1),
  threadCutting(estimatedHours: 1),
  pressing(estimatedHours: 2),

  // Quality & Documentation
  image(estimatedHours: 1),
  numbering(estimatedHours: 1),

  // Processing & Packaging
  washing(estimatedHours: 2),
  labeling(estimatedHours: 1),
  packing(estimatedHours: 2),
  bundling(estimatedHours: 1),
  pocting(estimatedHours: 1),
  plasticBag(estimatedHours: 1),

  // Logistics
  receiveGoods(estimatedHours: 2),
  dispatch(estimatedHours: 2);

  final int estimatedHours;
  const TaskType({required this.estimatedHours});
}

/// Extension for TaskType to provide display names and descriptions
extension TaskTypeExtension on TaskType {
  String get displayName {
    switch (this) {
      case TaskType.cutting:
        return 'Cutting';
      case TaskType.sewing:
        return 'Sewing';
      case TaskType.finishing:
        return 'Finishing';
      case TaskType.quality:
        return 'Quality Control';
      case TaskType.packaging:
        return 'Packaging';
      case TaskType.setup:
        return 'Setup';
      case TaskType.maintenance:
        return 'Maintenance';
      case TaskType.inspection:
        return 'Inspection';
      case TaskType.other:
        return 'Other';

      // Cutting & Preparation
      case TaskType.cuttingMaster:
        return 'Cutting Master';
      case TaskType.lupCutting:
        return 'Lup Cutting';
      case TaskType.munda:
        return 'Munda (Fabric Laying)';

      // Stitching & Assembly
      case TaskType.singer:
        return 'Singer (Sewing Machine)';
      case TaskType.overlock:
        return 'Overlock (Edge Finishing)';
      case TaskType.fayThread:
        return 'Fay-thread (Thread Work)';
      case TaskType.bottomHemming:
        return 'Bottom Hemming';
      case TaskType.iKaaj:
        return 'I-Kaaj (Stitching Technique)';

      // Hardware & Accessories
      case TaskType.belt:
        return 'Belt Operations';
      case TaskType.elasticBelt:
        return 'Elastic Belt';
      case TaskType.beltPacking:
        return 'Belt Packing';
      case TaskType.luppi:
        return 'Luppi (Loop Attachment)';
      case TaskType.baltec:
        return 'Baltec (Belt Technology)';
      case TaskType.iLet:
        return 'I-Let (Button Attachment)';
      case TaskType.buckle:
        return 'Buckle (Adjuster)';

      // Finishing Operations
      case TaskType.shouting:
        return 'Shouting (Quality Inspection)';
      case TaskType.threadCutting:
        return 'Thread Cutting';
      case TaskType.pressing:
        return 'Pressing (Ironing)';

      // Quality & Documentation
      case TaskType.image:
        return 'Image (Sample Documentation)';
      case TaskType.numbering:
        return 'Numbering (Product Tagging)';

      // Processing & Packaging
      case TaskType.washing:
        return 'Washing';
      case TaskType.labeling:
        return 'Labeling';
      case TaskType.packing:
        return 'Packing';
      case TaskType.bundling:
        return 'Bundling';
      case TaskType.pocting:
        return 'Pocting (Pocket Operations)';
      case TaskType.plasticBag:
        return 'Plastic Bag Packaging';

      // Logistics
      case TaskType.receiveGoods:
        return 'Receive Goods';
      case TaskType.dispatch:
        return 'Dispatch';
    }
  }

  String get description {
    switch (this) {
      case TaskType.cutting:
        return 'General fabric cutting operations';
      case TaskType.sewing:
        return 'General sewing and stitching operations';
      case TaskType.finishing:
        return 'Final finishing operations';
      case TaskType.quality:
        return 'Quality control and inspection';
      case TaskType.packaging:
        return 'General packaging operations';
      case TaskType.setup:
        return 'Machine and workspace setup';
      case TaskType.maintenance:
        return 'Equipment maintenance and repair';
      case TaskType.inspection:
        return 'Product inspection and verification';
      case TaskType.other:
        return 'Other miscellaneous tasks';

      // Cutting & Preparation
      case TaskType.cuttingMaster:
        return 'Master cutting operations and pattern management';
      case TaskType.lupCutting:
        return 'Specialized lup cutting technique';
      case TaskType.munda:
        return 'Fabric laying and spreading operations';

      // Stitching & Assembly
      case TaskType.singer:
        return 'Sewing machine operations and garment assembly';
      case TaskType.overlock:
        return 'Edge finishing and seam reinforcement';
      case TaskType.fayThread:
        return 'Specialized thread work and detailing';
      case TaskType.bottomHemming:
        return 'Bottom edge hemming and finishing';
      case TaskType.iKaaj:
        return 'Specialized stitching technique for garment construction';

      // Hardware & Accessories
      case TaskType.belt:
        return 'Belt attachment and processing operations';
      case TaskType.elasticBelt:
        return 'Elastic belt installation and adjustment';
      case TaskType.beltPacking:
        return 'Belt packaging and preparation';
      case TaskType.luppi:
        return 'Loop attachment and hardware installation';
      case TaskType.baltec:
        return 'Advanced belt technology operations';
      case TaskType.iLet:
        return 'Button attachment and fastening operations';
      case TaskType.buckle:
        return 'Buckle and adjuster installation';

      // Finishing Operations
      case TaskType.shouting:
        return 'Quality inspection call-outs and defect identification';
      case TaskType.threadCutting:
        return 'Thread trimming and cleanup operations';
      case TaskType.pressing:
        return 'Ironing and garment pressing operations';

      // Quality & Documentation
      case TaskType.image:
        return 'Sample documentation and photography';
      case TaskType.numbering:
        return 'Product numbering and identification tagging';

      // Processing & Packaging
      case TaskType.washing:
        return 'Garment washing and treatment processes';
      case TaskType.labeling:
        return 'Label attachment and product identification';
      case TaskType.packing:
        return 'Final product packing and preparation';
      case TaskType.bundling:
        return 'Product bundling and grouping operations';
      case TaskType.pocting:
        return 'Pocket operations and specialized processing';
      case TaskType.plasticBag:
        return 'Plastic bag packaging and sealing';

      // Logistics
      case TaskType.receiveGoods:
        return 'Goods receiving and inventory management';
      case TaskType.dispatch:
        return 'Product dispatch and shipping operations';
    }
  }

  /// Get the department type that typically handles this task type
  DepartmentType get primaryDepartment {
    switch (this) {
      case TaskType.cutting:
      case TaskType.cuttingMaster:
      case TaskType.lupCutting:
      case TaskType.munda:
        return DepartmentType.cutting;

      case TaskType.sewing:
      case TaskType.singer:
      case TaskType.overlock:
      case TaskType.fayThread:
      case TaskType.bottomHemming:
      case TaskType.iKaaj:
        return DepartmentType.stitching;

      case TaskType.belt:
      case TaskType.elasticBelt:
      case TaskType.beltPacking:
      case TaskType.luppi:
      case TaskType.baltec:
      case TaskType.iLet:
      case TaskType.buckle:
        return DepartmentType.production;

      case TaskType.finishing:
      case TaskType.shouting:
      case TaskType.threadCutting:
      case TaskType.pressing:
        return DepartmentType.finishing;

      case TaskType.quality:
      case TaskType.inspection:
      case TaskType.image:
        return DepartmentType.quality;

      case TaskType.packaging:
      case TaskType.packing:
      case TaskType.bundling:
      case TaskType.plasticBag:
        return DepartmentType.packing;

      case TaskType.washing:
      case TaskType.labeling:
      case TaskType.numbering:
      case TaskType.pocting:
        return DepartmentType.production;

      case TaskType.receiveGoods:
      case TaskType.dispatch:
        return DepartmentType.warehouse;

      case TaskType.setup:
      case TaskType.maintenance:
        return DepartmentType.maintenance;

      case TaskType.other:
        return DepartmentType.other;
    }
  }
}

// TaskStatus is now imported from shared/enums/common_enums.dart

/// Task priority enum
enum TaskPriority {
  low(
    'Low',
    Color(0xFF4CAF50),  // Green
    Color(0xFFE8F5E9),  // Light Green 50
    Color(0xFF1B5E20),  // Dark Green 900
  ),
  normal(
    'Normal',
    Color(0xFF2196F3),  // Blue
    Color(0xFFE3F2FD),  // Light Blue 50
    Color(0xFF0D47A1),  // Blue 900
  ),
  high(
    'High',
    Color(0xFFFF9800),  // Orange
    Color(0xFFFFF3E0),  // Deep Orange 50
    Color(0xFFE65100),  // Orange 900
  ),
  urgent(
    'Urgent',
    Color(0xFFF44336),  // Red
    Color(0xFFFFEBEE),  // Red 50
    Color(0xFFB71C1C),  // Red 900
  ),
  critical(
    'Critical',
    Color(0xFF9C27B0),  // Purple
    Color(0xFFF3E5F5),  // Purple 50
    Color(0xFF4A148C),  // Deep Purple 900
  );

  const TaskPriority(this.displayName, this.color, this.backgroundColor, this.borderColor);
  
  final String displayName;
  final Color color;
  final Color backgroundColor;
  final Color borderColor;
  
  static TaskPriority fromString(String value) {
    return TaskPriority.values.firstWhere(
      (priority) => priority.toString().split('.').last == value,
      orElse: () => TaskPriority.normal,
    );
  }
  
  IconData get icon {
    switch (this) {
      case TaskPriority.low:
        return Icons.low_priority;
      case TaskPriority.normal:
        return Icons.horizontal_rule;
      case TaskPriority.high:
        return Icons.priority_high;
      case TaskPriority.urgent:
        return Icons.warning_amber_rounded;
      case TaskPriority.critical:
        return Icons.error_outline;
    }
  }
  
  Color get statusColor {
    switch (this) {
      case ProductionStatus.planned:
        return Colors.blue;
      case ProductionStatus.scheduled:
        return Colors.orange;
      case ProductionStatus.inProgress:
        return Colors.green;
      case ProductionStatus.paused:
        return Colors.amber;
      case ProductionStatus.completed:
        return Colors.green.shade700;
      case ProductionStatus.cancelled:
        return Colors.red;
      case ProductionStatus.onHold:
        return Colors.grey;
      case TaskPriority.low:
        // TODO: Handle this case.
        throw UnimplementedError();
      case TaskPriority.normal:
        // TODO: Handle this case.
        throw UnimplementedError();
      case TaskPriority.high:
        // TODO: Handle this case.
        throw UnimplementedError();
      case TaskPriority.urgent:
        // TODO: Handle this case.
        throw UnimplementedError();
      case TaskPriority.critical:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }
}

/// Resource type enum
enum ResourceType {
  machine,
  worker,
  material,
  tool,
  space,
}

/// Priority levels for production notes
enum NotePriority {
  /// Low priority note
  low,
  
  /// Normal priority note
  normal,
  
  /// High priority note
  high,
  
  /// Critical priority note
  critical,
}

/// Production filter criteria
class ProductionFilterCriteria extends Equatable {
  final ProductionStatus? status;
  final ProductionPriority? priority;
  final DepartmentType? department;
  final String? assignedSupervisor;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  const ProductionFilterCriteria({
    this.status,
    this.priority,
    this.department,
    this.assignedSupervisor,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        status,
        priority,
        department,
        assignedSupervisor,
        startDate,
        endDate,
        searchQuery,
      ];

  ProductionFilterCriteria copyWith({
    ProductionStatus? status,
    ProductionPriority? priority,
    DepartmentType? department,
    String? assignedSupervisor,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
  }) {
    return ProductionFilterCriteria(
      status: status ?? this.status,
      priority: priority ?? this.priority,
      department: department ?? this.department,
      assignedSupervisor: assignedSupervisor ?? this.assignedSupervisor,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

/// Extension to convert ProductionFilterCriteria to ProductionOrderFilter
extension ProductionFilterCriteriaX on ProductionFilterCriteria {
  /// Converts a [ProductionFilterCriteria] to a [ProductionOrderFilter]
  ProductionOrderFilter toOrderFilter() {
    return ProductionOrderFilter(
      // Map domain ProductionStatus to UI/DTO ProductionOrderStatus enum
      statuses: status != null
          ? [
              switch (status!) {
                ProductionStatus.planned => ProductionOrderStatus.planned,
                ProductionStatus.inProgress => ProductionOrderStatus.inProgress,
                ProductionStatus.completed => ProductionOrderStatus.completed,
                ProductionStatus.cancelled => ProductionOrderStatus.cancelled,
                // Map other statuses to closest equivalents
                ProductionStatus.scheduled => ProductionOrderStatus.planned,
                ProductionStatus.paused => ProductionOrderStatus.inProgress,
                ProductionStatus.onHold => ProductionOrderStatus.inProgress,
              }
            ]
          : null,
      startDate: startDate,
      endDate: endDate,
      department: department,
      searchQuery: searchQuery,
    );
  }
}

/// Task filter criteria
class TaskFilterCriteria extends Equatable {
  final TaskStatus? status;
  final TaskType? type;
  final TaskPriority? priority;
  final String? assignedWorker;
  final DepartmentType? department;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  const TaskFilterCriteria({
    this.status,
    this.type,
    this.priority,
    this.assignedWorker,
    this.department,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        status,
        type,
        priority,
        assignedWorker,
        department,
        startDate,
        endDate,
        searchQuery,
      ];
}

/// Resource allocation filter criteria
class ResourceAllocationFilterCriteria extends Equatable {
  final ResourceType? resourceType;
  final AllocationStatus? status;
  final String? resourceId;
  final String? taskId;
  final DateTime? startDate;
  final DateTime? endDate;

  const ResourceAllocationFilterCriteria({
    this.resourceType,
    this.status,
    this.resourceId,
    this.taskId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [
        resourceType,
        status,
        resourceId,
        taskId,
        startDate,
        endDate,
      ];
}

/// Allocate resource request
class AllocateResourceRequest extends Equatable {
  final String taskId;
  final String resourceId;
  final ResourceType resourceType;
  final DateTime startTime;
  final DateTime endTime;
  final double quantity;
  final String? notes;

  const AllocateResourceRequest({
    required this.taskId,
    required this.resourceId,
    required this.resourceType,
    required this.startTime,
    required this.endTime,
    required this.quantity,
    this.notes,
  });

  @override
  List<Object?> get props => [
        taskId,
        resourceId,
        resourceType,
        startTime,
        endTime,
        quantity,
        notes,
      ];
}

/// Production statistics
class ProductionStatistics extends Equatable {
  final int totalOrders;
  final int completedOrders;
  final int inProgressOrders;
  final int delayedOrders;
  final double averageCompletionTime;
  final double onTimeDeliveryRate;
  final double resourceUtilization;

  const ProductionStatistics({
    required this.totalOrders,
    required this.completedOrders,
    required this.inProgressOrders,
    required this.delayedOrders,
    required this.averageCompletionTime,
    required this.onTimeDeliveryRate,
    required this.resourceUtilization,
  });

  @override
  List<Object?> get props => [
        totalOrders,
        completedOrders,
        inProgressOrders,
        delayedOrders,
        averageCompletionTime,
        onTimeDeliveryRate,
        resourceUtilization,
      ];
}

/// Production capacity
class ProductionCapacity extends Equatable {
  final double totalCapacity;
  final double usedCapacity;
  final double availableCapacity;
  final Map<DepartmentType, double> departmentCapacity;

  const ProductionCapacity({
    required this.totalCapacity,
    required this.usedCapacity,
    required this.availableCapacity,
    required this.departmentCapacity,
  });

  @override
  List<Object?> get props => [
        totalCapacity,
        usedCapacity,
        availableCapacity,
        departmentCapacity,
      ];
}

/// Production bottleneck
class ProductionBottleneck extends Equatable {
  final String id;
  final String name;
  final DepartmentType department;
  final String description;
  final double impactLevel;
  final DateTime identifiedAt;
  final String? resolution;

  const ProductionBottleneck({
    required this.id,
    required this.name,
    required this.department,
    required this.description,
    required this.impactLevel,
    required this.identifiedAt,
    this.resolution,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        department,
        description,
        impactLevel,
        identifiedAt,
        resolution,
      ];
}

/// Production KPI (Key Performance Indicator)
class ProductionKPI extends Equatable {
  final String title;
  final String value;
  final String unit;
  final double? trend;
  final String? description;
  final String? colorCode; // Hex color code for UI display

  const ProductionKPI({
    required this.title,
    required this.value,
    required this.unit,
    this.trend,
    this.description,
    this.colorCode,
  });

  @override
  List<Object?> get props => [
        title,
        value,
        unit,
        trend,
        description,
        colorCode,
      ];
}
