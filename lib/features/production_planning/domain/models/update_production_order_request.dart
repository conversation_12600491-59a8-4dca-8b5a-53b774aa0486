import 'package:equatable/equatable.dart';
import 'package:hm_collection/shared/enums/common_enums.dart';

import '../../../resource_management/domain/entities/facility_entities.dart';
import '../entities/production_entities.dart';

class UpdateProductionOrderRequest extends Equatable {
  final String id;
  final String? clientName;
  final ProductionStatus? status;
  final ProductionPriority? priority;
  final DateTime? plannedStartDate;
  final DateTime? plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final String? assignedSupervisor;
  final DepartmentType? currentDepartment;
  final double? completionPercentage;
  final int? completedQuantity;
  final Map<String, dynamic>? metadata;

  const UpdateProductionOrderRequest({
    required this.id,
    this.clientName,
    this.status,
    this.priority,
    this.plannedStartDate,
    this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.assignedSupervisor,
    this.currentDepartment,
    this.completionPercentage,
    this.completedQuantity,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        clientName,
        status,
        priority,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        assignedSupervisor,
        currentDepartment,
        completionPercentage,
        completedQuantity,
        metadata,
      ];
}
