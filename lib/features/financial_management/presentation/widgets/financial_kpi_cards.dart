import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/animated_counter.dart';
import '../../domain/entities/financial_entities.dart';
import 'financial_summary_card.dart';

/// Financial KPI cards widget
class FinancialKpiCards extends StatelessWidget {
  final FinancialSummary summary;

  const FinancialKpiCards({
    super.key,
    required this.summary,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildKpiCard(
            'Total Revenue',
            summary.totalRevenue,
            Icons.trending_up,
            AppColors.success,
            '\$',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildKpiCard(
            'Total Expenses',
            summary.totalExpenses,
            Icons.trending_down,
            AppColors.error,
            '\$',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildKpiCard(
            'Net Income',
            summary.netProfit,
            Icons.account_balance,
            _getNetIncomeColor(summary.netProfit),
            '\$',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildKpiCard(
            'Profit Margin',
            summary.profitMargin,
            Icons.percent,
            _getProfitMarginColor(summary.profitMargin),
            '',
            suffix: '%',
          ),
        ),
      ],
    );
  }

  Widget _buildKpiCard(
    String title,
    double value,
    IconData icon,
    Color color,
    String prefix, {
    String suffix = '',
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    _getTrendIcon(value),
                    color: color,
                    size: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            AnimatedCounter(
              value: value,
              duration: const Duration(milliseconds: 1500),
              builder: (context, animatedValue) {
                return Text(
                  '$prefix${_formatValue(animatedValue)}$suffix',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Color _getNetIncomeColor(double netIncome) {
    if (netIncome > 0) return AppColors.success;
    if (netIncome < 0) return AppColors.error;
    return AppColors.textSecondary;
  }

  Color _getProfitMarginColor(double margin) {
    if (margin >= 15) return AppColors.success;
    if (margin >= 10) return AppColors.warning;
    if (margin >= 5) return AppColors.error;
    return AppColors.error;
  }

  IconData _getTrendIcon(double value) {
    if (value > 0) return Icons.arrow_upward;
    if (value < 0) return Icons.arrow_downward;
    return Icons.remove;
  }

  String _formatValue(double value) {
    if (value.abs() >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value.abs() >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }
}

/// Financial summary card widget
class FinancialSummaryCard extends StatelessWidget {
  final FinancialSummary summary;

  const FinancialSummaryCard({
    super.key,
    required this.summary,
  });

  @override
  Widget build(BuildContext context) {
    final totalRevenue = summary.totalRevenue;
    final totalExpenses = summary.totalExpenses;
    final netIncome = summary.netProfit;
    // Calculate margins based on available data
    final grossMargin = totalRevenue > 0 ? ((totalRevenue - totalExpenses) / totalRevenue) * 100 : 0.0;
    final operatingMargin = summary.profitMargin; // Use profit margin as operating margin
    final netMargin = summary.profitMargin;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Financial Summary',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getProfitabilityColor(netIncome).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getProfitabilityStatus(netIncome),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: _getProfitabilityColor(netIncome),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Revenue vs Expenses
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Revenue',
                    totalRevenue,
                    AppColors.success,
                    Icons.arrow_upward,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: _buildSummaryItem(
                    'Expenses',
                    totalExpenses,
                    AppColors.error,
                    Icons.arrow_downward,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: _buildSummaryItem(
                    'Net Income',
                    netIncome,
                    _getNetIncomeColor(netIncome),
                    netIncome >= 0 ? Icons.trending_up : Icons.trending_down,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Margin Analysis
            Text(
              'Margin Analysis',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildMarginItem('Gross Margin', grossMargin),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildMarginItem('Operating Margin', operatingMargin),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildMarginItem('Net Margin', netMargin),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Performance Indicator
            _buildPerformanceIndicator(netMargin),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    double value,
    Color color,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        AnimatedCounter(
          value: value,
          duration: const Duration(milliseconds: 1200),
          builder: (context, animatedValue) {
            return Text(
              '\$${_formatCurrency(animatedValue)}',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildMarginItem(String label, double percentage) {
    final color = _getMarginColor(percentage);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            AnimatedCounter(
              value: percentage,
              duration: const Duration(milliseconds: 1000),
              builder: (context, animatedValue) {
                return Text(
                  '${animatedValue.toStringAsFixed(1)}%',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                );
              },
            ),
            const SizedBox(width: 4),
            Icon(
              _getMarginTrendIcon(percentage),
              color: color,
              size: 16,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            widthFactor: (percentage / 100).clamp(0.0, 1.0),
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceIndicator(double netMargin) {
    final performance = _getPerformanceLevel(netMargin);
    final color = _getPerformanceColor(netMargin);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            _getPerformanceIcon(netMargin),
            color: color,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Performance Level',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  performance,
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${netMargin.toStringAsFixed(1)}%',
            style: AppTextStyles.headlineSmall.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getNetIncomeColor(double netIncome) {
    if (netIncome > 0) return AppColors.success;
    if (netIncome < 0) return AppColors.error;
    return AppColors.textSecondary;
  }

  Color _getProfitabilityColor(double netIncome) {
    if (netIncome > 0) return AppColors.success;
    if (netIncome < 0) return AppColors.error;
    return AppColors.warning;
  }

  String _getProfitabilityStatus(double netIncome) {
    if (netIncome > 0) return 'Profitable';
    if (netIncome < 0) return 'Loss';
    return 'Break Even';
  }

  Color _getMarginColor(double margin) {
    if (margin >= 20) return AppColors.success;
    if (margin >= 15) return AppColors.success.withOpacity(0.8);
    if (margin >= 10) return AppColors.warning;
    if (margin >= 5) return AppColors.error.withOpacity(0.8);
    return AppColors.error;
  }

  IconData _getMarginTrendIcon(double margin) {
    if (margin >= 15) return Icons.trending_up;
    if (margin >= 10) return Icons.trending_flat;
    return Icons.trending_down;
  }

  String _getPerformanceLevel(double netMargin) {
    if (netMargin >= 20) return 'Excellent';
    if (netMargin >= 15) return 'Good';
    if (netMargin >= 10) return 'Average';
    if (netMargin >= 5) return 'Below Average';
    if (netMargin >= 0) return 'Poor';
    return 'Loss Making';
  }

  Color _getPerformanceColor(double netMargin) {
    if (netMargin >= 15) return AppColors.success;
    if (netMargin >= 10) return AppColors.warning;
    if (netMargin >= 0) return AppColors.error.withOpacity(0.8);
    return AppColors.error;
  }

  IconData _getPerformanceIcon(double netMargin) {
    if (netMargin >= 15) return Icons.star;
    if (netMargin >= 10) return Icons.thumb_up;
    if (netMargin >= 5) return Icons.trending_flat;
    if (netMargin >= 0) return Icons.thumb_down;
    return Icons.warning;
  }

  String _formatCurrency(double value) {
    if (value.abs() >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value.abs() >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }
}
