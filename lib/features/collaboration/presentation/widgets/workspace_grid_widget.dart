import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/entities/collaboration_entities.dart';

/// Workspace grid widget
class WorkspaceGridWidget extends StatelessWidget {
  final List<CollaborationWorkspace> workspaces;
  final Function(CollaborationWorkspace) onWorkspaceTap;
  final Function(CollaborationWorkspace)? onWorkspaceEdit;
  final Function(CollaborationWorkspace)? onWorkspaceDelete;
  final Function(CollaborationWorkspace)? onWorkspaceArchive;
  final Function(CollaborationWorkspace)? onWorkspaceShare;

  const WorkspaceGridWidget({
    super.key,
    required this.workspaces,
    required this.onWorkspaceTap,
    this.onWorkspaceEdit,
    this.onWorkspaceDelete,
    this.onWorkspaceArchive,
    this.onWorkspaceShare,
  });

  @override
  Widget build(BuildContext context) {
    if (workspaces.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      // Ensure a concrete size when used inside Columns/Expanded in tabs
      shrinkWrap: true,
      primary: false,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: workspaces.length,
      itemBuilder: (context, index) {
        final workspace = workspaces[index];
        return _buildWorkspaceCard(workspace);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.workspaces_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Workspaces Available',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first workspace to start collaborating',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () => _createWorkspace(),
            icon: const Icon(Icons.add),
            label: const Text('Create Workspace'),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkspaceCard(CollaborationWorkspace workspace) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => onWorkspaceTap(workspace),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getWorkspaceTypeColor(workspace.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getWorkspaceTypeIcon(workspace.type),
                      color: _getWorkspaceTypeColor(workspace.type),
                      size: 20,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value, workspace),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share, size: 16),
                            SizedBox(width: 8),
                            Text('Share'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'archive',
                        child: Row(
                          children: [
                            Icon(Icons.archive, size: 16),
                            SizedBox(width: 8),
                            Text('Archive'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Text(
                workspace.workspaceName,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 4),
              
              Text(
                workspace.description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const Spacer(),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.people,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${workspace.memberCount} members',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      if (workspace.isPublic)
                        Icon(
                          Icons.public,
                          size: 14,
                          color: AppColors.success,
                        ),
                      if (workspace.isArchived) ...[
                        if (workspace.isPublic) const SizedBox(width: 4),
                        Icon(
                          Icons.archive,
                          size: 14,
                          color: AppColors.warning,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              Row(
                children: [
                  // Online members indicator
                  Row(
                    children: workspace.members
                        .where((m) => m.isOnline)
                        .take(3)
                        .map((member) => Container(
                              margin: const EdgeInsets.only(right: 4),
                              child: CircleAvatar(
                                radius: 8,
                                backgroundColor: AppColors.success,
                                child: Text(
                                  member.userName.substring(0, 1).toUpperCase(),
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Colors.white,
                                    fontSize: 8,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                  if (workspace.activeMemberCount > 3) ...[
                    const SizedBox(width: 4),
                    Text(
                      '+${workspace.activeMemberCount - 3}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                        fontSize: 10,
                      ),
                    ),
                  ],
                  const Spacer(),
                  Text(
                    _formatDate(workspace.lastActivityAt ?? workspace.updatedAt),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getWorkspaceTypeColor(WorkspaceType type) {
    switch (type) {
      case WorkspaceType.general:
        return AppColors.primary;
      case WorkspaceType.project:
        return AppColors.success;
      case WorkspaceType.department:
        return AppColors.warning;
      case WorkspaceType.team:
        return AppColors.error;
      case WorkspaceType.temporary:
        return Colors.purple;
    }
  }

  IconData _getWorkspaceTypeIcon(WorkspaceType type) {
    switch (type) {
      case WorkspaceType.general:
        return Icons.workspaces;
      case WorkspaceType.project:
        return Icons.folder_open;
      case WorkspaceType.department:
        return Icons.business;
      case WorkspaceType.team:
        return Icons.groups;
      case WorkspaceType.temporary:
        return Icons.schedule;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) return 'Today';
    if (difference == 1) return 'Yesterday';
    if (difference < 7) return '${difference}d ago';
    if (difference < 30) return '${(difference / 7).floor()}w ago';
    
    return '${date.day}/${date.month}/${date.year}';
  }

  void _handleMenuAction(String action, CollaborationWorkspace workspace) {
    switch (action) {
      case 'edit':
        onWorkspaceEdit?.call(workspace);
        break;
      case 'share':
        onWorkspaceShare?.call(workspace);
        break;
      case 'archive':
        onWorkspaceArchive?.call(workspace);
        break;
      case 'delete':
        onWorkspaceDelete?.call(workspace);
        break;
    }
  }

  void _createWorkspace() {
    // Navigate to create workspace page
  }
}

/// Quick actions widget
class QuickActionsWidget extends StatelessWidget {
  final VoidCallback? onCreateWorkspace;
  final VoidCallback? onStartSession;
  final VoidCallback? onJoinSession;
  final VoidCallback? onSendMessage;

  const QuickActionsWidget({
    super.key,
    this.onCreateWorkspace,
    this.onStartSession,
    this.onJoinSession,
    this.onSendMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    'Create Workspace',
                    'Start a new collaboration space',
                    Icons.add_business,
                    AppColors.primary,
                    onCreateWorkspace,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    'Start Session',
                    'Begin a video collaboration',
                    Icons.video_call,
                    AppColors.success,
                    onStartSession,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    'Join Session',
                    'Join an existing session',
                    Icons.meeting_room,
                    AppColors.warning,
                    onJoinSession,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    'Send Message',
                    'Quick message to team',
                    Icons.send,
                    AppColors.error,
                    onSendMessage,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
