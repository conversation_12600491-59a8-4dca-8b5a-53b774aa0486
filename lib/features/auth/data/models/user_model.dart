import 'package:json_annotation/json_annotation.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/user.dart';

part 'user_model.g.dart';

/// User data model for API communication
@JsonSerializable()
class UserModel extends User {
  const UserModel({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    String? deletedBy,
    int version = 1,
    required String username,
    required String email,
    required String firstName,
    required String lastName,
    String? phoneNumber,
    String? profileImageUrl,
    required UserRole role,
    required Department department,
    required CommonStatus status,
    DateTime? lastLoginAt,
    DateTime? emailVerifiedAt,
    DateTime? phoneVerifiedAt,
    required bool isActive,
    required bool isEmailVerified,
    required bool isPhoneVerified,
    Map<String, dynamic>? preferences,
    required List<String> permissions,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          deletedBy: deletedBy,
          version: version,
          username: username,
          email: email,
          firstName: firstName,
          lastName: lastName,
          phoneNumber: phoneNumber,
          profileImageUrl: profileImageUrl,
          role: role,
          department: department,
          status: status,
          lastLoginAt: lastLoginAt,
          emailVerifiedAt: emailVerifiedAt,
          phoneVerifiedAt: phoneVerifiedAt,
          isActive: isActive,
          isEmailVerified: isEmailVerified,
          isPhoneVerified: isPhoneVerified,
          preferences: preferences,
          permissions: permissions,
        );

  /// Factory constructor for JSON deserialization
  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// Create from domain entity
  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      deletedAt: user.deletedAt,
      createdBy: user.createdBy,
      updatedBy: user.updatedBy,
      deletedBy: user.deletedBy,
      version: user.version,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phoneNumber: user.phoneNumber,
      profileImageUrl: user.profileImageUrl,
      role: user.role,
      department: user.department,
      status: user.status,
      lastLoginAt: user.lastLoginAt,
      emailVerifiedAt: user.emailVerifiedAt,
      phoneVerifiedAt: user.phoneVerifiedAt,
      isActive: user.isActive,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      preferences: user.preferences,
      permissions: user.permissions,
    );
  }

  /// Convert to domain entity
  User toEntity() {
    return User(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy,
      deletedBy: deletedBy,
      version: version,
      username: username,
      email: email,
      firstName: firstName,
      lastName: lastName,
      phoneNumber: phoneNumber,
      profileImageUrl: profileImageUrl,
      role: role,
      department: department,
      status: status,
      lastLoginAt: lastLoginAt,
      emailVerifiedAt: emailVerifiedAt,
      phoneVerifiedAt: phoneVerifiedAt,
      isActive: isActive,
      isEmailVerified: isEmailVerified,
      isPhoneVerified: isPhoneVerified,
      preferences: preferences,
      permissions: permissions,
    );
  }

  /// Copy with new values
  @override
  UserModel copyWith({
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
    UserRole? role,
    Department? department,
    CommonStatus? status,
    DateTime? lastLoginAt,
    DateTime? emailVerifiedAt,
    DateTime? phoneVerifiedAt,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    Map<String, dynamic>? preferences,
    List<String>? permissions,
    DateTime? updatedAt,
    String? updatedBy,
    int? version,
  }) {
    return UserModel(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy,
      version: version ?? this.version + 1,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      role: role ?? this.role,
      department: department ?? this.department,
      status: status ?? this.status,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      preferences: preferences ?? this.preferences,
      permissions: permissions ?? this.permissions,
    );
  }
}

/// User session data model
@JsonSerializable()
class UserSessionModel extends UserSession {
  const UserSessionModel({
    required String sessionId,
    required String userId,
    required String deviceId,
    required String deviceName,
    required String ipAddress,
    required String userAgent,
    required DateTime createdAt,
    required DateTime lastActiveAt,
    required DateTime expiresAt,
    required bool isActive,
  }) : super(
          sessionId: sessionId,
          userId: userId,
          deviceId: deviceId,
          deviceName: deviceName,
          ipAddress: ipAddress,
          userAgent: userAgent,
          createdAt: createdAt,
          lastActiveAt: lastActiveAt,
          expiresAt: expiresAt,
          isActive: isActive,
        );

  /// Factory constructor for JSON deserialization
  factory UserSessionModel.fromJson(Map<String, dynamic> json) => _$UserSessionModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$UserSessionModelToJson(this);

  /// Create from domain entity
  factory UserSessionModel.fromEntity(UserSession session) {
    return UserSessionModel(
      sessionId: session.sessionId,
      userId: session.userId,
      deviceId: session.deviceId,
      deviceName: session.deviceName,
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      createdAt: session.createdAt,
      lastActiveAt: session.lastActiveAt,
      expiresAt: session.expiresAt,
      isActive: session.isActive,
    );
  }

  /// Convert to domain entity
  UserSession toEntity() {
    return UserSession(
      sessionId: sessionId,
      userId: userId,
      deviceId: deviceId,
      deviceName: deviceName,
      ipAddress: ipAddress,
      userAgent: userAgent,
      createdAt: createdAt,
      lastActiveAt: lastActiveAt,
      expiresAt: expiresAt,
      isActive: isActive,
    );
  }
}

/// User preferences data model
@JsonSerializable()
class UserPreferencesModel extends UserPreferences {
  const UserPreferencesModel({
    String theme = 'system',
    String language = 'en',
    String dateFormat = 'MMM dd, yyyy',
    String timeFormat = '12h',
    bool enableNotifications = true,
    bool enableEmailNotifications = true,
    bool enablePushNotifications = true,
    Map<String, bool> notificationChannels = const {},
    Map<String, dynamic> dashboardLayout = const {},
    Map<String, dynamic> customSettings = const {},
  }) : super(
          theme: theme,
          language: language,
          dateFormat: dateFormat,
          timeFormat: timeFormat,
          enableNotifications: enableNotifications,
          enableEmailNotifications: enableEmailNotifications,
          enablePushNotifications: enablePushNotifications,
          notificationChannels: notificationChannels,
          dashboardLayout: dashboardLayout,
          customSettings: customSettings,
        );

  /// Factory constructor for JSON deserialization
  factory UserPreferencesModel.fromJson(Map<String, dynamic> json) => _$UserPreferencesModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$UserPreferencesModelToJson(this);

  /// Create from domain entity
  factory UserPreferencesModel.fromEntity(UserPreferences preferences) {
    return UserPreferencesModel(
      theme: preferences.theme,
      language: preferences.language,
      dateFormat: preferences.dateFormat,
      timeFormat: preferences.timeFormat,
      enableNotifications: preferences.enableNotifications,
      enableEmailNotifications: preferences.enableEmailNotifications,
      enablePushNotifications: preferences.enablePushNotifications,
      notificationChannels: preferences.notificationChannels,
      dashboardLayout: preferences.dashboardLayout,
      customSettings: preferences.customSettings,
    );
  }

  /// Convert to domain entity
  UserPreferences toEntity() {
    return UserPreferences(
      theme: theme,
      language: language,
      dateFormat: dateFormat,
      timeFormat: timeFormat,
      enableNotifications: enableNotifications,
      enableEmailNotifications: enableEmailNotifications,
      enablePushNotifications: enablePushNotifications,
      notificationChannels: notificationChannels,
      dashboardLayout: dashboardLayout,
      customSettings: customSettings,
    );
  }

  /// Copy with new values
  @override
  UserPreferencesModel copyWith({
    String? theme,
    String? language,
    String? dateFormat,
    String? timeFormat,
    bool? enableNotifications,
    bool? enableEmailNotifications,
    bool? enablePushNotifications,
    Map<String, bool>? notificationChannels,
    Map<String, dynamic>? dashboardLayout,
    Map<String, dynamic>? customSettings,
  }) {
    return UserPreferencesModel(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableEmailNotifications: enableEmailNotifications ?? this.enableEmailNotifications,
      enablePushNotifications: enablePushNotifications ?? this.enablePushNotifications,
      notificationChannels: notificationChannels ?? this.notificationChannels,
      dashboardLayout: dashboardLayout ?? this.dashboardLayout,
      customSettings: customSettings ?? this.customSettings,
    );
  }
}
