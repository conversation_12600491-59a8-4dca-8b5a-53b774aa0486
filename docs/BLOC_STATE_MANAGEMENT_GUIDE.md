# Bloc State Management Guide for Cloth Manufacturing App

## Overview

This guide provides detailed implementation patterns for using Bloc state management in the Cloth Manufacturing App. It covers best practices, code examples, and architectural decisions specific to our manufacturing domain.

## Table of Contents

1. [Bloc Architecture Overview](#bloc-architecture-overview)
2. [Core Blocs Implementation](#core-blocs-implementation)
3. [State Management Patterns](#state-management-patterns)
4. [Event Handling Strategies](#event-handling-strategies)
5. [Repository Pattern Integration](#repository-pattern-integration)
6. [Testing Blocs](#testing-blocs)
7. [Performance Optimization](#performance-optimization)

---

## Bloc Architecture Overview

### Clean Architecture Layers

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   Pages     │  │   Bloc/Cubit    │   │
│  │   Widgets   │  │   (UI Logic)    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│            Domain Layer                 │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  Use Cases  │  │   Entities      │   │
│  │(Bus. Logic) │  │  (Core Models)  │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│             Data Layer                  │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │Repositories │  │  Data Sources   │   │
│  │(Contracts)  │  │  (API/Local)    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

### Key Principles

1. **Single Responsibility**: Each Bloc handles one specific domain
2. **Unidirectional Data Flow**: Events → Business Logic → States
3. **Immutable States**: All states are immutable for predictable behavior
4. **Separation of Concerns**: UI logic separated from business logic
5. **Testability**: Easy to unit test business logic

---

## Core Blocs Implementation

### 1. Authentication Bloc

```dart
// authentication/presentation/bloc/auth_bloc.dart

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  final UserRepository _userRepository;

  AuthBloc({
    required AuthRepository authRepository,
    required UserRepository userRepository,
  })  : _authRepository = authRepository,
        _userRepository = userRepository,
        super(AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthTokenRefreshRequested>(_onTokenRefreshRequested);
    on<AuthStatusChecked>(_onAuthStatusChecked);
  }

  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    
    try {
      final result = await _authRepository.login(
        username: event.username,
        password: event.password,
      );
      
      result.fold(
        (failure) => emit(AuthError(failure.message)),
        (authData) async {
          await _userRepository.saveUserData(authData.user);
          await _authRepository.saveTokens(authData.tokens);
          emit(AuthAuthenticated(authData.user));
        },
      );
    } catch (e) {
      emit(AuthError('An unexpected error occurred'));
    }
  }

  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    
    try {
      await _authRepository.logout();
      await _userRepository.clearUserData();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError('Logout failed'));
    }
  }

  Future<void> _onTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final result = await _authRepository.refreshToken();
      
      result.fold(
        (failure) {
          emit(AuthUnauthenticated());
        },
        (tokens) async {
          await _authRepository.saveTokens(tokens);
          // Keep current authenticated state
        },
      );
    } catch (e) {
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onAuthStatusChecked(
    AuthStatusChecked event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    
    try {
      final isAuthenticated = await _authRepository.isAuthenticated();
      
      if (isAuthenticated) {
        final user = await _userRepository.getCurrentUser();
        if (user != null) {
          emit(AuthAuthenticated(user));
        } else {
          emit(AuthUnauthenticated());
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthUnauthenticated());
    }
  }
}
```

### 2. Order Management Bloc

```dart
// orders/presentation/bloc/order_bloc.dart

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final OrderRepository _orderRepository;
  final ProductionRepository _productionRepository;

  OrderBloc({
    required OrderRepository orderRepository,
    required ProductionRepository productionRepository,
  })  : _orderRepository = orderRepository,
        _productionRepository = productionRepository,
        super(OrderInitial()) {
    on<OrdersLoadRequested>(_onOrdersLoadRequested);
    on<OrderCreateRequested>(_onOrderCreateRequested);
    on<OrderUpdateRequested>(_onOrderUpdateRequested);
    on<OrderStatusUpdateRequested>(_onOrderStatusUpdateRequested);
    on<OrderFilterChanged>(_onOrderFilterChanged);
  }

  Future<void> _onOrdersLoadRequested(
    OrdersLoadRequested event,
    Emitter<OrderState> emit,
  ) async {
    emit(OrderLoading());
    
    try {
      final result = await _orderRepository.getOrders(
        page: event.page,
        limit: event.limit,
        filters: event.filters,
      );
      
      result.fold(
        (failure) => emit(OrderError(failure.message)),
        (orders) => emit(OrderLoaded(
          orders: orders,
          hasReachedMax: orders.length < event.limit,
        )),
      );
    } catch (e) {
      emit(OrderError('Failed to load orders'));
    }
  }

  Future<void> _onOrderCreateRequested(
    OrderCreateRequested event,
    Emitter<OrderState> emit,
  ) async {
    if (state is OrderLoaded) {
      emit(OrderCreating());
      
      try {
        final result = await _orderRepository.createOrder(event.orderData);
        
        result.fold(
          (failure) => emit(OrderError(failure.message)),
          (newOrder) async {
            // Create production order automatically
            await _productionRepository.createProductionOrder(newOrder.id);
            
            final currentState = state as OrderLoaded;
            emit(OrderLoaded(
              orders: [newOrder, ...currentState.orders],
              hasReachedMax: currentState.hasReachedMax,
            ));
          },
        );
      } catch (e) {
        emit(OrderError('Failed to create order'));
      }
    }
  }

  Future<void> _onOrderStatusUpdateRequested(
    OrderStatusUpdateRequested event,
    Emitter<OrderState> emit,
  ) async {
    if (state is OrderLoaded) {
      try {
        final result = await _orderRepository.updateOrderStatus(
          event.orderId,
          event.newStatus,
        );
        
        result.fold(
          (failure) => emit(OrderError(failure.message)),
          (updatedOrder) {
            final currentState = state as OrderLoaded;
            final updatedOrders = currentState.orders.map((order) {
              return order.id == event.orderId ? updatedOrder : order;
            }).toList();
            
            emit(OrderLoaded(
              orders: updatedOrders,
              hasReachedMax: currentState.hasReachedMax,
            ));
          },
        );
      } catch (e) {
        emit(OrderError('Failed to update order status'));
      }
    }
  }
}
```

### 3. Production Management Bloc

```dart
// production/presentation/bloc/production_bloc.dart

class ProductionBloc extends Bloc<ProductionEvent, ProductionState> {
  final ProductionRepository _productionRepository;
  final TaskRepository _taskRepository;
  final WorkstationRepository _workstationRepository;
  
  StreamSubscription<List<Task>>? _taskSubscription;

  ProductionBloc({
    required ProductionRepository productionRepository,
    required TaskRepository taskRepository,
    required WorkstationRepository workstationRepository,
  })  : _productionRepository = productionRepository,
        _taskRepository = taskRepository,
        _workstationRepository = workstationRepository,
        super(ProductionInitial()) {
    on<ProductionDashboardLoadRequested>(_onDashboardLoadRequested);
    on<ProductionTasksLoadRequested>(_onTasksLoadRequested);
    on<ProductionTaskStatusUpdated>(_onTaskStatusUpdated);
    on<ProductionTaskAssigned>(_onTaskAssigned);
    on<ProductionRealTimeUpdatesStarted>(_onRealTimeUpdatesStarted);
    on<ProductionRealTimeUpdatesStopped>(_onRealTimeUpdatesStopped);
  }

  Future<void> _onDashboardLoadRequested(
    ProductionDashboardLoadRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(ProductionLoading());
    
    try {
      final results = await Future.wait([
        _productionRepository.getProductionSummary(),
        _workstationRepository.getWorkstationStatus(),
        _taskRepository.getActiveTasks(),
      ]);
      
      final summary = results[0] as ProductionSummary;
      final workstations = results[1] as List<Workstation>;
      final activeTasks = results[2] as List<Task>;
      
      emit(ProductionDashboardLoaded(
        summary: summary,
        workstations: workstations,
        activeTasks: activeTasks,
      ));
    } catch (e) {
      emit(ProductionError('Failed to load production dashboard'));
    }
  }

  Future<void> _onTaskStatusUpdated(
    ProductionTaskStatusUpdated event,
    Emitter<ProductionState> emit,
  ) async {
    try {
      final result = await _taskRepository.updateTaskStatus(
        event.taskId,
        event.newStatus,
        event.notes,
      );
      
      result.fold(
        (failure) => emit(ProductionError(failure.message)),
        (updatedTask) {
          // Update the current state with the new task status
          if (state is ProductionDashboardLoaded) {
            final currentState = state as ProductionDashboardLoaded;
            final updatedTasks = currentState.activeTasks.map((task) {
              return task.id == event.taskId ? updatedTask : task;
            }).toList();
            
            emit(currentState.copyWith(activeTasks: updatedTasks));
          }
        },
      );
    } catch (e) {
      emit(ProductionError('Failed to update task status'));
    }
  }

  Future<void> _onRealTimeUpdatesStarted(
    ProductionRealTimeUpdatesStarted event,
    Emitter<ProductionState> emit,
  ) async {
    await _taskSubscription?.cancel();
    
    _taskSubscription = _taskRepository.getTaskUpdatesStream().listen(
      (tasks) {
        if (state is ProductionDashboardLoaded) {
          final currentState = state as ProductionDashboardLoaded;
          emit(currentState.copyWith(activeTasks: tasks));
        }
      },
    );
  }

  Future<void> _onRealTimeUpdatesStopped(
    ProductionRealTimeUpdatesStopped event,
    Emitter<ProductionState> emit,
  ) async {
    await _taskSubscription?.cancel();
    _taskSubscription = null;
  }

  @override
  Future<void> close() {
    _taskSubscription?.cancel();
    return super.close();
  }
}
```

---

## State Management Patterns

### 1. Loading States Pattern

```dart
abstract class BaseState extends Equatable {
  const BaseState();
}

class InitialState extends BaseState {
  @override
  List<Object> get props => [];
}

class LoadingState extends BaseState {
  @override
  List<Object> get props => [];
}

class LoadedState<T> extends BaseState {
  final T data;
  
  const LoadedState(this.data);
  
  @override
  List<Object> get props => [data];
}

class ErrorState extends BaseState {
  final String message;
  
  const ErrorState(this.message);
  
  @override
  List<Object> get props => [message];
}
```

### 2. Pagination State Pattern

```dart
class PaginatedState<T> extends BaseState {
  final List<T> items;
  final bool hasReachedMax;
  final bool isLoading;
  final String? error;
  
  const PaginatedState({
    this.items = const [],
    this.hasReachedMax = false,
    this.isLoading = false,
    this.error,
  });
  
  PaginatedState<T> copyWith({
    List<T>? items,
    bool? hasReachedMax,
    bool? isLoading,
    String? error,
  }) {
    return PaginatedState<T>(
      items: items ?? this.items,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
  
  @override
  List<Object?> get props => [items, hasReachedMax, isLoading, error];
}
```

### 3. Form State Pattern

```dart
class FormState extends BaseState {
  final Map<String, String> fieldValues;
  final Map<String, String?> fieldErrors;
  final bool isValid;
  final bool isSubmitting;
  final String? submitError;
  
  const FormState({
    this.fieldValues = const {},
    this.fieldErrors = const {},
    this.isValid = false,
    this.isSubmitting = false,
    this.submitError,
  });
  
  FormState copyWith({
    Map<String, String>? fieldValues,
    Map<String, String?>? fieldErrors,
    bool? isValid,
    bool? isSubmitting,
    String? submitError,
  }) {
    return FormState(
      fieldValues: fieldValues ?? this.fieldValues,
      fieldErrors: fieldErrors ?? this.fieldErrors,
      isValid: isValid ?? this.isValid,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      submitError: submitError ?? this.submitError,
    );
  }
  
  @override
  List<Object?> get props => [
    fieldValues,
    fieldErrors,
    isValid,
    isSubmitting,
    submitError,
  ];
}
```

---

## Event Handling Strategies

### 1. Event Debouncing

```dart
class SearchBloc extends Bloc<SearchEvent, SearchState> {
  Timer? _debounceTimer;
  
  SearchBloc() : super(SearchInitial()) {
    on<SearchQueryChanged>(
      _onSearchQueryChanged,
      transformer: debounce(const Duration(milliseconds: 300)),
    );
  }
  
  EventTransformer<T> debounce<T>(Duration duration) {
    return (events, mapper) => events.debounceTime(duration).flatMap(mapper);
  }
}
```

### 2. Event Throttling

```dart
on<ButtonPressed>(
  _onButtonPressed,
  transformer: throttle(const Duration(seconds: 1)),
);

EventTransformer<T> throttle<T>(Duration duration) {
  return (events, mapper) => events.throttleTime(duration).flatMap(mapper);
}
```

### 3. Sequential Event Processing

```dart
on<CriticalEvent>(
  _onCriticalEvent,
  transformer: sequential(),
);
```

---

## Repository Pattern Integration

### Repository Interface

```dart
abstract class OrderRepository {
  Future<Either<Failure, List<Order>>> getOrders({
    int page = 1,
    int limit = 20,
    OrderFilters? filters,
  });
  
  Future<Either<Failure, Order>> createOrder(CreateOrderRequest request);
  Future<Either<Failure, Order>> updateOrder(String id, UpdateOrderRequest request);
  Future<Either<Failure, void>> deleteOrder(String id);
  Stream<Order> getOrderUpdates(String id);
}
```

### Repository Implementation

```dart
class OrderRepositoryImpl implements OrderRepository {
  final OrderRemoteDataSource _remoteDataSource;
  final OrderLocalDataSource _localDataSource;
  final NetworkInfo _networkInfo;
  
  OrderRepositoryImpl({
    required OrderRemoteDataSource remoteDataSource,
    required OrderLocalDataSource localDataSource,
    required NetworkInfo networkInfo,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource,
        _networkInfo = networkInfo;
  
  @override
  Future<Either<Failure, List<Order>>> getOrders({
    int page = 1,
    int limit = 20,
    OrderFilters? filters,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final orders = await _remoteDataSource.getOrders(
          page: page,
          limit: limit,
          filters: filters,
        );
        
        await _localDataSource.cacheOrders(orders);
        return Right(orders);
      } catch (e) {
        return Left(ServerFailure('Failed to fetch orders'));
      }
    } else {
      try {
        final cachedOrders = await _localDataSource.getCachedOrders();
        return Right(cachedOrders);
      } catch (e) {
        return Left(CacheFailure('No cached orders available'));
      }
    }
  }
}
```

---

## Testing Blocs

### Unit Testing Example

```dart
// test/features/auth/presentation/bloc/auth_bloc_test.dart

void main() {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockAuthRepository mockAuthRepository;
    late MockUserRepository mockUserRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      mockUserRepository = MockUserRepository();
      authBloc = AuthBloc(
        authRepository: mockAuthRepository,
        userRepository: mockUserRepository,
      );
    });

    tearDown(() {
      authBloc.close();
    });

    test('initial state is AuthInitial', () {
      expect(authBloc.state, AuthInitial());
    });

    group('AuthLoginRequested', () {
      const username = 'testuser';
      const password = 'testpass';
      const user = User(id: '1', username: username, role: UserRole.operator);
      const tokens = AuthTokens(accessToken: 'access', refreshToken: 'refresh');
      const authData = AuthData(user: user, tokens: tokens);

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthAuthenticated] when login succeeds',
        build: () {
          when(() => mockAuthRepository.login(
                username: username,
                password: password,
              )).thenAnswer((_) async => Right(authData));
          when(() => mockUserRepository.saveUserData(user))
              .thenAnswer((_) async {});
          when(() => mockAuthRepository.saveTokens(tokens))
              .thenAnswer((_) async {});
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLoginRequested(username, password)),
        expect: () => [
          AuthLoading(),
          AuthAuthenticated(user),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.login(
                username: username,
                password: password,
              )).called(1);
          verify(() => mockUserRepository.saveUserData(user)).called(1);
          verify(() => mockAuthRepository.saveTokens(tokens)).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthError] when login fails',
        build: () {
          when(() => mockAuthRepository.login(
                username: username,
                password: password,
              )).thenAnswer((_) async => Left(AuthFailure('Invalid credentials')));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLoginRequested(username, password)),
        expect: () => [
          AuthLoading(),
          AuthError('Invalid credentials'),
        ],
      );
    });
  });
}
```

---

## Performance Optimization

### 1. State Equality

```dart
class OrderState extends Equatable {
  final List<Order> orders;
  final bool isLoading;
  final String? error;
  
  const OrderState({
    required this.orders,
    required this.isLoading,
    this.error,
  });
  
  @override
  List<Object?> get props => [orders, isLoading, error];
}
```

### 2. BlocSelector for Specific State Listening

```dart
BlocSelector<OrderBloc, OrderState, bool>(
  selector: (state) => state.isLoading,
  builder: (context, isLoading) {
    return isLoading
        ? CircularProgressIndicator()
        : OrderList();
  },
)
```

### 3. Bloc Listener for Side Effects

```dart
BlocListener<AuthBloc, AuthState>(
  listener: (context, state) {
    if (state is AuthError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(state.message)),
      );
    } else if (state is AuthAuthenticated) {
      Navigator.of(context).pushReplacementNamed('/dashboard');
    }
  },
  child: LoginForm(),
)
```

---

*This guide provides comprehensive patterns and examples for implementing Bloc state management in the Cloth Manufacturing App. Follow these patterns for consistent, maintainable, and testable code.*
