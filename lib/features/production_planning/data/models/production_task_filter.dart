import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../domain/entities/production_entities.dart' show TaskFilterCriteria;

/// Filter criteria for querying production tasks at the repository level
class ProductionTaskFilter extends Equatable {
  /// List of task statuses to filter by
  final List<TaskStatus>? statuses;

  /// List of user IDs to filter tasks assigned to specific users
  final List<String>? assignedToUserIds;

  /// Start date range for filtering tasks
  final DateTime? startDate;

  /// End date range for filtering tasks
  final DateTime? endDate;

  /// Department to filter tasks by
  final DepartmentType? department;

  /// Search query to filter tasks by name or description
  final String? searchQuery;

  /// Production order ID to filter tasks by
  final String? productionOrderId;

  const ProductionTaskFilter({
    this.statuses,
    this.assignedToUserIds,
    this.startDate,
    this.endDate,
    this.department,
    this.searchQuery,
    this.productionOrderId,
  });

  /// Creates a copy of this filter with the given fields replaced with the new values
  ProductionTaskFilter copyWith({
    List<TaskStatus>? statuses,
    List<String>? assignedToUserIds,
    DateTime? startDate,
    DateTime? endDate,
    DepartmentType? department,
    String? searchQuery,
    String? productionOrderId,
  }) {
    return ProductionTaskFilter(
      statuses: statuses ?? this.statuses,
      assignedToUserIds: assignedToUserIds ?? this.assignedToUserIds,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      department: department ?? this.department,
      productionOrderId: productionOrderId ?? this.productionOrderId,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  /// Creates a filter from the presentation-level TaskFilterCriteria
  factory ProductionTaskFilter.fromTaskFilterCriteria(criteria) {
    return ProductionTaskFilter(
      statuses: criteria.status != null ? [criteria.status!] : null,
      assignedToUserIds: criteria.assignedWorker != null ? [criteria.assignedWorker!] : null,
      startDate: criteria.startDate,
      endDate: criteria.endDate,
      department: criteria.department,
      searchQuery: criteria.searchQuery,
    );
  }

  /// Returns true if this filter has no filtering conditions set
  bool get isEmpty {
    return statuses == null &&
        assignedToUserIds == null &&
        startDate == null &&
        endDate == null &&
        department == null &&
        (searchQuery == null || searchQuery!.isEmpty);
  }

  @override
  List<Object?> get props => [
        statuses,
        assignedToUserIds,
        startDate,
        endDate,
        department,
        searchQuery,
      ];
}
