import 'package:flutter/material.dart';

/// A chart widget that displays profit and loss data
class ProfitLossChart extends StatelessWidget {
  final List<ProfitLossData> data;
  final String title;
  final String period;

  const ProfitLossChart({
    Key? key,
    required this.data,
    this.title = 'Profit & Loss',
    this.period = 'Monthly', required void Function() onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    period,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildLegend(context),
            const SizedBox(height: 16),
            Si<PERSON><PERSON>ox(
              height: 200,
              child: _buildChart(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegend(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildLegendItem(context, 'Revenue', Colors.green),
        _buildLegendItem(context, 'Expenses', Colors.red),
        _buildLegendItem(context, 'Profit', Colors.blue),
      ],
    );
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildChart(BuildContext context) {
    if (data.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    final maxValue = data.fold<double>(0, (max, item) {
      final itemMax = [item.revenue, item.expenses, item.profit.abs()].reduce((a, b) => a > b ? a : b);
      return itemMax > max ? itemMax : max;
    });

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: data.map((item) => _buildChartBar(context, item, maxValue)).toList(),
      ),
    );
  }

  Widget _buildChartBar(BuildContext context, ProfitLossData item, double maxValue) {
    final revenueHeight = maxValue > 0 ? (item.revenue / maxValue) * 150 : 0.0;
    final expenseHeight = maxValue > 0 ? (item.expenses / maxValue) * 150 : 0.0;
    final profitHeight = maxValue > 0 ? (item.profit.abs() / maxValue) * 150 : 0.0;

    return Container(
      width: 80,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Values on top
          Column(
            children: [
              Text(
                '\$${(item.revenue / 1000).toStringAsFixed(1)}K',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '\$${(item.expenses / 1000).toStringAsFixed(1)}K',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '\$${(item.profit / 1000).toStringAsFixed(1)}K',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: item.profit >= 0 ? Colors.blue : Colors.orange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Bars
          Row(
            children: [
              // Revenue bar
              Container(
                width: 20,
                height: revenueHeight,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(2)),
                ),
              ),
              const SizedBox(width: 4),
              
              // Expense bar
              Container(
                width: 20,
                height: expenseHeight,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(2)),
                ),
              ),
              const SizedBox(width: 4),
              
              // Profit bar
              Container(
                width: 20,
                height: profitHeight,
                decoration: BoxDecoration(
                  color: item.profit >= 0 ? Colors.blue : Colors.orange,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(2)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Period label
          Text(
            item.period,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Simple line chart for profit trend
class ProfitTrendChart extends StatelessWidget {
  final List<ProfitLossData> data;
  final String title;

  const ProfitTrendChart({
    Key? key,
    required this.data,
    this.title = 'Profit Trend',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 150,
              child: _buildTrendChart(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendChart(BuildContext context) {
    if (data.isEmpty) {
      return Center(
        child: Text(
          'No trend data available',
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    final maxProfit = data.map((d) => d.profit).reduce((a, b) => a > b ? a : b);
    final minProfit = data.map((d) => d.profit).reduce((a, b) => a < b ? a : b);
    final range = maxProfit - minProfit;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: CustomPaint(
        size: Size(data.length * 60.0, 150),
        painter: ProfitTrendPainter(
          data: data,
          maxProfit: maxProfit,
          minProfit: minProfit,
          range: range,
        ),
      ),
    );
  }
}

/// Custom painter for profit trend line
class ProfitTrendPainter extends CustomPainter {
  final List<ProfitLossData> data;
  final double maxProfit;
  final double minProfit;
  final double range;

  ProfitTrendPainter({
    required this.data,
    required this.maxProfit,
    required this.minProfit,
    required this.range,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty || range == 0) return;

    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final pointPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    final path = Path();
    final points = <Offset>[];

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final normalizedProfit = (data[i].profit - minProfit) / range;
      final y = size.height - (normalizedProfit * size.height);

      points.add(Offset(x, y));

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    // Draw line
    canvas.drawPath(path, paint);

    // Draw points
    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Revenue vs Expenses comparison chart
class RevenueExpenseChart extends StatelessWidget {
  final List<ProfitLossData> data;
  final String title;

  const RevenueExpenseChart({
    Key? key,
    required this.data,
    this.title = 'Revenue vs Expenses',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildLegendItem(context, 'Revenue', Colors.green),
                const SizedBox(width: 16),
                _buildLegendItem(context, 'Expenses', Colors.red),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: _buildComparisonChart(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildComparisonChart(BuildContext context) {
    if (data.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    final maxValue = data.fold<double>(0, (max, item) {
      final itemMax = item.revenue > item.expenses ? item.revenue : item.expenses;
      return itemMax > max ? itemMax : max;
    });

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: data.map((item) => _buildComparisonBar(context, item, maxValue)).toList(),
      ),
    );
  }

  Widget _buildComparisonBar(BuildContext context, ProfitLossData item, double maxValue) {
    final revenueHeight = maxValue > 0 ? (item.revenue / maxValue) * 150 : 0.0;
    final expenseHeight = maxValue > 0 ? (item.expenses / maxValue) * 150 : 0.0;

    return Container(
      width: 60,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            children: [
              // Revenue bar
              Expanded(
                child: Container(
                  height: revenueHeight,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
              ),
              const SizedBox(width: 2),
              
              // Expense bar
              Expanded(
                child: Container(
                  height: expenseHeight,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            item.period,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// Placeholder class - this should be defined in financial_entities.dart
class ProfitLossData {
  final String period;
  final double revenue;
  final double expenses;
  final double profit;

  const ProfitLossData({
    required this.period,
    required this.revenue,
    required this.expenses,
    required this.profit,
  });
}