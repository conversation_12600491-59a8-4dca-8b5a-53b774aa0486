
import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/shared/models/pagination.dart';
import 'package:hm_collection/features/production_planning/domain/entities/production_entities.dart';
import 'package:hm_collection/features/production_planning/domain/usecases/production_usecases.dart';

import '../../data/models/production_order_filter.dart';
import '../models/create_production_order_request.dart';
import '../models/update_production_order_request.dart';

abstract class ProductionRepository {
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> getProductionOrders({
    ProductionOrderFilter? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<ProductionOrder>>> getProductionOrderById(
    String orderId,
  );

  Future<Either<Failure, ApiResponse<ProductionOrder>>> createProductionOrder(
    CreateProductionOrderRequest request,
  );

  Future<Either<Failure, ApiResponse<ProductionOrder>>> updateProductionOrder(
    UpdateProductionOrderRequest request,
  );

  Future<Either<Failure, ApiResponse<ProductionOrder>>> updateProductionOrderStatus(
      UpdateProductionOrderStatusParams params);

  Future<Either<Failure, ApiResponse<ProductionOrder>>> startProductionOrder(
      String orderId);

  Future<Either<Failure, ApiResponse<ProductionOrder>>> pauseProductionOrder(
      PauseProductionOrderParams params);

  Future<Either<Failure, ApiResponse<ProductionOrder>>> resumeProductionOrder(
      String orderId);

  Future<Either<Failure, ApiResponse<ProductionOrder>>> completeProductionOrder(
      String orderId);

  Future<Either<Failure, ApiListResponse<ProductionTask>>> getProductionTasks(
      GetProductionTasksParams params);

  Future<Either<Failure, ApiResponse<ProductionTask>>> updateTaskStatus(
      UpdateTaskStatusParams params);

  Future<Either<Failure, ApiResponse<ProductionTask>>> assignTaskToWorkers(
      AssignTaskToWorkersParams params);

  Future<Either<Failure, ApiResponse<ProductionTask>>> updateTaskProgress(
      UpdateTaskProgressParams params);

  Future<Either<Failure, ApiListResponse<ResourceAllocation>>>
      getResourceAllocations(GetResourceAllocationsParams params);

  Future<Either<Failure, ApiResponse<ResourceAllocation>>> allocateResource(
      AllocateResourceRequest request);

  Future<Either<Failure, ApiVoidResponse>> releaseResource(
      ReleaseResourceParams params);

  Future<Either<Failure, ProductionStatistics>> getProductionStatistics(
      GetProductionStatisticsParams params);

  Future<Either<Failure, ProductionCapacity>> getProductionCapacity(
      GetProductionCapacityParams params);

  Future<Either<Failure, List<ProductionBottleneck>>> getProductionBottlenecks(
      GetProductionBottlenecksParams params);

  Future<Either<Failure, ApiListResponse<ProductionOrder>>>
      getOverdueProductionOrders(GetOverdueProductionOrdersParams params);

  Future<Either<Failure, ApiListResponse<ProductionOrder>>>
      getUrgentProductionOrders(GetUrgentProductionOrdersParams params);

  Future<Either<Failure, ApiListResponse<ProductionOrder>>>
      searchProductionOrders(SearchProductionOrdersParams params);
}
