import 'package:equatable/equatable.dart';

class CreateQualityInspectionRequest extends Equatable {
  final String productId;
  final String batchNumber;
  final int quantity;
  final String inspectorId;
  final DateTime? scheduledDate;
  final String? notes;
  final Map<String, dynamic>? customFields;

  const CreateQualityInspectionRequest({
    required this.productId,
    required this.batchNumber,
    required this.quantity,
    required this.inspectorId,
    this.scheduledDate,
    this.notes,
    this.customFields,
  });

  Map<String, dynamic> toJson() => {
        'productId': productId,
        'batchNumber': batchNumber,
        'quantity': quantity,
        'inspectorId': inspectorId,
        if (scheduledDate != null) 'scheduledDate': scheduledDate!.toIso8601String(),
        if (notes != null) 'notes': notes,
        if (customFields != null) ...customFields!,
      };

  @override
  List<Object?> get props => [
        productId,
        batchNumber,
        quantity,
        inspectorId,
        scheduledDate,
        notes,
        customFields,
      ];
}
