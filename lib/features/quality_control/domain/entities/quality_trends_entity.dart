import 'package:equatable/equatable.dart';

/// Represents quality trends data over time with comprehensive quality metrics
class QualityTrends extends Equatable {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final TimeGranularity granularity;
  final List<QualityTrendData> dataPoints;
  
  // Core quality metrics
  final double averageDefectRate;       // Average defects per unit
  final double firstPassYield;          // Percentage of units passing first inspection
  final double reworkRate;              // Percentage of units requiring rework
  final double scrapRate;               // Percentage of units scrapped
  final double customerReturnRate;      // Percentage of units returned by customers
  final double overallEquipmentEffectiveness; // OEE metric (0-1 scale)
  
  // Detailed metrics
  final Map<DefectSeverity, int> defectCountsBySeverity;
  final Map<String, int> defectCountsByType;  // Defect type to count mapping
  final Map<String, int> defectCountsByLocation; // Location/line to defect count
  
  // Trend metrics (compared to previous period)
  final double defectRateChange;        // Percentage change in defect rate
  final double firstPassYieldChange;    // Percentage point change in first pass yield
  final double reworkRateChange;        // Percentage point change in rework rate
  
  // Additional context
  final int totalUnitsInspected;
  final int totalDefectsFound;
  final double averageInspectionTime;   // In minutes
  final double averageTimeToRepair;     // Average time to fix defects in minutes
  
  // Comparisons with targets/benchmarks
  final Map<String, dynamic> comparisons;
  final Map<String, dynamic> targets;   // Target values for KPIs

  const QualityTrends({
    required this.id,
    required this.startDate,
    required this.endDate,
    this.granularity = TimeGranularity.daily,
    required this.dataPoints,
    this.averageDefectRate = 0.0,
    this.firstPassYield = 0.0,
    this.reworkRate = 0.0,
    this.scrapRate = 0.0,
    this.customerReturnRate = 0.0,
    this.overallEquipmentEffectiveness = 0.0,
    this.defectCountsBySeverity = const {},
    this.defectCountsByType = const {},
    this.defectCountsByLocation = const {},
    this.defectRateChange = 0.0,
    this.firstPassYieldChange = 0.0,
    this.reworkRateChange = 0.0,
    this.totalUnitsInspected = 0,
    this.totalDefectsFound = 0,
    this.averageInspectionTime = 0.0,
    this.averageTimeToRepair = 0.0,
    this.comparisons = const {},
    this.targets = const {},
  });

  /// Creates a QualityTrends instance from JSON
  factory QualityTrends.fromJson(Map<String, dynamic> json) {
    return QualityTrends(
      id: json['id'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      granularity: TimeGranularity.values.firstWhere(
        (e) => e.toString() == 'TimeGranularity.${json['granularity']}',
        orElse: () => TimeGranularity.daily,
      ),
      dataPoints: (json['dataPoints'] as List<dynamic>? ?? [])
          .map((e) => QualityTrendData.fromJson(e as Map<String, dynamic>))
          .toList(),
      averageDefectRate: (json['averageDefectRate'] as num?)?.toDouble() ?? 0.0,
      firstPassYield: (json['firstPassYield'] as num?)?.toDouble() ?? 0.0,
      reworkRate: (json['reworkRate'] as num?)?.toDouble() ?? 0.0,
      scrapRate: (json['scrapRate'] as num?)?.toDouble() ?? 0.0,
      customerReturnRate: (json['customerReturnRate'] as num?)?.toDouble() ?? 0.0,
      overallEquipmentEffectiveness: 
          (json['overallEquipmentEffectiveness'] as num?)?.toDouble() ?? 0.0,
      defectCountsBySeverity: (json['defectCountsBySeverity'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(
          DefectSeverity.values.firstWhere(
            (e) => e.toString() == 'DefectSeverity.$key',
            orElse: () => DefectSeverity.minor,
          ),
          value as int,
        ),
      ) ?? const {},
      defectCountsByType: Map<String, int>.from(
          json['defectCountsByType'] as Map<String, dynamic>? ?? {}),
      defectCountsByLocation: Map<String, int>.from(
          json['defectCountsByLocation'] as Map<String, dynamic>? ?? {}),
      defectRateChange: (json['defectRateChange'] as num?)?.toDouble() ?? 0.0,
      firstPassYieldChange: (json['firstPassYieldChange'] as num?)?.toDouble() ?? 0.0,
      reworkRateChange: (json['reworkRateChange'] as num?)?.toDouble() ?? 0.0,
      totalUnitsInspected: (json['totalUnitsInspected'] as num?)?.toInt() ?? 0,
      totalDefectsFound: (json['totalDefectsFound'] as num?)?.toInt() ?? 0,
      averageInspectionTime: (json['averageInspectionTime'] as num?)?.toDouble() ?? 0.0,
      averageTimeToRepair: (json['averageTimeToRepair'] as num?)?.toDouble() ?? 0.0,
      comparisons: Map<String, dynamic>.from(json['comparisons'] as Map<String, dynamic>? ?? {}),
      targets: Map<String, dynamic>.from(json['targets'] as Map<String, dynamic>? ?? {}),
    );
  }

  /// Converts the QualityTrends instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'granularity': granularity.toString().split('.').last,
      'dataPoints': dataPoints.map((e) => e.toJson()).toList(),
      'averageDefectRate': averageDefectRate,
      'firstPassYield': firstPassYield,
      'reworkRate': reworkRate,
      'scrapRate': scrapRate,
      'customerReturnRate': customerReturnRate,
      'overallEquipmentEffectiveness': overallEquipmentEffectiveness,
      'defectCountsBySeverity': defectCountsBySeverity.map(
        (key, value) => MapEntry(key.toString().split('.').last, value),
      ),
      'defectCountsByType': defectCountsByType,
      'defectCountsByLocation': defectCountsByLocation,
      'defectRateChange': defectRateChange,
      'firstPassYieldChange': firstPassYieldChange,
      'reworkRateChange': reworkRateChange,
      'totalUnitsInspected': totalUnitsInspected,
      'totalDefectsFound': totalDefectsFound,
      'averageInspectionTime': averageInspectionTime,
      'averageTimeToRepair': averageTimeToRepair,
      'comparisons': comparisons,
      'targets': targets,
    };
  }

  /// Creates a copy of this QualityTrends with the given fields replaced
  QualityTrends copyWith({
    String? id,
    DateTime? startDate,
    DateTime? endDate,
    TimeGranularity? granularity,
    List<QualityTrendData>? dataPoints,
    double? averageDefectRate,
    double? firstPassYield,
    double? reworkRate,
    double? scrapRate,
    double? customerReturnRate,
    double? overallEquipmentEffectiveness,
    Map<DefectSeverity, int>? defectCountsBySeverity,
    Map<String, int>? defectCountsByType,
    Map<String, int>? defectCountsByLocation,
    double? defectRateChange,
    double? firstPassYieldChange,
    double? reworkRateChange,
    int? totalUnitsInspected,
    int? totalDefectsFound,
    double? averageInspectionTime,
    double? averageTimeToRepair,
    Map<String, dynamic>? comparisons,
    Map<String, dynamic>? targets,
  }) {
    return QualityTrends(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      granularity: granularity ?? this.granularity,
      dataPoints: dataPoints ?? this.dataPoints,
      averageDefectRate: averageDefectRate ?? this.averageDefectRate,
      firstPassYield: firstPassYield ?? this.firstPassYield,
      reworkRate: reworkRate ?? this.reworkRate,
      scrapRate: scrapRate ?? this.scrapRate,
      customerReturnRate: customerReturnRate ?? this.customerReturnRate,
      overallEquipmentEffectiveness: 
          overallEquipmentEffectiveness ?? this.overallEquipmentEffectiveness,
      defectCountsBySeverity: defectCountsBySeverity ?? this.defectCountsBySeverity,
      defectCountsByType: defectCountsByType ?? this.defectCountsByType,
      defectCountsByLocation: defectCountsByLocation ?? this.defectCountsByLocation,
      defectRateChange: defectRateChange ?? this.defectRateChange,
      firstPassYieldChange: firstPassYieldChange ?? this.firstPassYieldChange,
      reworkRateChange: reworkRateChange ?? this.reworkRateChange,
      totalUnitsInspected: totalUnitsInspected ?? this.totalUnitsInspected,
      totalDefectsFound: totalDefectsFound ?? this.totalDefectsFound,
      averageInspectionTime: averageInspectionTime ?? this.averageInspectionTime,
      averageTimeToRepair: averageTimeToRepair ?? this.averageTimeToRepair,
      comparisons: comparisons ?? this.comparisons,
      targets: targets ?? this.targets,
    );
  }

  @override
  List<Object?> get props => [
        id,
        startDate,
        endDate,
        granularity,
        dataPoints,
        averageDefectRate,
        firstPassYield,
        reworkRate,
        scrapRate,
        customerReturnRate,
        overallEquipmentEffectiveness,
        defectCountsBySeverity,
        defectCountsByType,
        defectCountsByLocation,
        defectRateChange,
        firstPassYieldChange,
        reworkRateChange,
        totalUnitsInspected,
        totalDefectsFound,
        averageInspectionTime,
        averageTimeToRepair,
        comparisons,
        targets,
      ];
      
  /// Gets the overall quality score (0-100) based on multiple metrics
  double get qualityScore {
    // Simple weighted average of key metrics
    // Adjust weights based on your business priorities
    return (
      (firstPassYield * 0.4) + 
      ((1 - reworkRate) * 0.3) + 
      ((1 - scrapRate) * 0.2) + 
      (overallEquipmentEffectiveness * 0.1)
    ) * 100;
  }
  
  /// Checks if the current metrics meet all quality targets
  bool get meetsAllTargets {
    if (targets.isEmpty) return true;
    
    return (
      (!targets.containsKey('firstPassYield') || 
        firstPassYield >= (targets['firstPassYield'] as num).toDouble()) &&
      (!targets.containsKey('reworkRate') || 
        reworkRate <= (targets['reworkRate'] as num).toDouble()) &&
      (!targets.containsKey('scrapRate') || 
        scrapRate <= (targets['scrapRate'] as num).toDouble())
    );
  }
}

/// Time granularity for trend data
enum TimeGranularity {
  hourly,
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
}

/// Defect severity levels
enum DefectSeverity {
  critical,
  major,
  minor,
  cosmetic,
}

/// Represents a single data point in the quality trend
class QualityTrendData extends Equatable {
  final DateTime date;
  final double value;          // Primary metric value
  final String label;          // Human-readable label for this data point
  final String? category;      // Optional category for grouping
  final double? targetValue;   // Target or benchmark value
  final double? previousValue; // Value from previous period for comparison
  final Map<String, dynamic>? additionalData; // Additional context or metrics

  const QualityTrendData({
    required this.date,
    required this.value,
    required this.label,
    this.category,
    this.targetValue,
    this.previousValue,
    this.additionalData,
  });

  /// Creates a QualityTrendData instance from JSON
  factory QualityTrendData.fromJson(Map<String, dynamic> json) {
    return QualityTrendData(
      date: DateTime.parse(json['date'] as String),
      value: (json['value'] as num).toDouble(),
      label: json['label'] as String,
      category: json['category'] as String?,
      targetValue: (json['targetValue'] as num?)?.toDouble(),
      previousValue: (json['previousValue'] as num?)?.toDouble(),
      additionalData: json['additionalData'] as Map<String, dynamic>?,
    );
  }

  /// Converts the QualityTrendData instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'value': value,
      'label': label,
      if (category != null) 'category': category,
      if (targetValue != null) 'targetValue': targetValue,
      if (previousValue != null) 'previousValue': previousValue,
      if (additionalData != null) 'additionalData': additionalData,
    };
  }

  /// Creates a copy of this QualityTrendData with the given fields replaced
  QualityTrendData copyWith({
    DateTime? date,
    double? value,
    String? label,
    String? category,
    double? targetValue,
    double? previousValue,
    Map<String, dynamic>? additionalData,
  }) {
    return QualityTrendData(
      date: date ?? this.date,
      value: value ?? this.value,
      label: label ?? this.label,
      category: category ?? this.category,
      targetValue: targetValue ?? this.targetValue,
      previousValue: previousValue ?? this.previousValue,
      additionalData: additionalData ?? this.additionalData,
    );
  }
  
  /// Calculates the variance from target as a percentage
  double? get varianceFromTarget {
    if (targetValue == null || targetValue == 0) return null;
    return ((value - targetValue!) / targetValue!) * 100;
  }
  
  /// Gets the trend direction compared to previous value
  int get trendDirection {
    if (previousValue == null) return 0;
    return value.compareTo(previousValue!);
  }
  
  /// Checks if the value meets the target
  bool get meetsTarget {
    if (targetValue == null) return true;
    return value >= targetValue!;
  }

  @override
  List<Object?> get props => [
        date, 
        value, 
        label, 
        category, 
        targetValue, 
        previousValue, 
        additionalData,
      ];
}
