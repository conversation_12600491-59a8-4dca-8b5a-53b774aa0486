/*
  Safe Admin Creation Script

  Usage:
    node functions/scripts/create_admin_user.js \
      --email="<EMAIL>" \
      --password="StrongP@ssw0rd!" \
      --firstName="Admin" \
      --lastName="User"

  Requirements:
  - Set GOOGLE_APPLICATION_CREDENTIALS to your Firebase service account JSON path, or
  - Place a serviceAccountKey.json in the project root and set SERVICE_ACCOUNT_PATH to it.

  Behavior:
  - Ensures an admin account exists (auth + Firestore users/{uid})
  - If a user with the email exists in Auth but Firestore doc is missing, it will create the user doc
  - If a Firestore admin already exists, it won't create duplicates
  - Validates password length >= 8 (basic check)
*/

const admin = require('firebase-admin');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');

async function initFirebaseAdmin() {
  if (admin.apps.length) return;

  try {
    // Prefer GOOGLE_APPLICATION_CREDENTIALS
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
    });
  } catch (e) {
    const svcPath = process.env.SERVICE_ACCOUNT_PATH || path.resolve(process.cwd(), 'serviceAccountKey.json');
    try {
      const serviceAccount = require(svcPath);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
    } catch (inner) {
      throw new Error(
        'Failed to initialize Firebase Admin SDK. Provide GOOGLE_APPLICATION_CREDENTIALS or SERVICE_ACCOUNT_PATH.\n' +
        `Original: ${e.message}\nFallback: ${inner.message}`
      );
    }
  }
}

function validateArgs(argv) {
  const { email, password, firstName, lastName } = argv;
  if (!email) throw new Error('--email is required');
  if (!password) throw new Error('--password is required');
  if (String(password).length < 8) throw new Error('Password must be at least 8 characters long');
  if (!firstName) throw new Error('--firstName is required');
  if (!lastName) throw new Error('--lastName is required');
}

function toUserDoc({ uid, email, firstName, lastName }) {
  const now = admin.firestore.FieldValue.serverTimestamp();
  return {
    uid,
    email,
    firstName,
    lastName,
    displayName: `${firstName} ${lastName}`.trim(),
    username: email.split('@')[0],
    role: 'administrator',
    status: 'active',
    department: 'administration',
    isActive: true,
    isEmailVerified: true,
    permissions: [
      'users.create','users.read','users.update','users.delete',
      'orders.create','orders.read','orders.update','orders.delete',
      'production.create','production.read','production.update','production.delete',
      'inventory.create','inventory.read','inventory.update','inventory.delete',
    ],
    createdAt: now,
    updatedAt: now,
    lastLoginAt: null,
    lastActiveAt: null,
    metadata: {
      registrationSource: 'admin_seed_script',
      appVersion: 'seed',
    },
  };
}

async function ensureAdmin({ email, password, firstName, lastName }) {
  const auth = admin.auth();
  const db = admin.firestore();

  // 1) If an admin already exists in Firestore, short-circuit
  const adminQuery = await db
    .collection('users')
    .where('role', '==', 'administrator')
    .limit(1)
    .get();

  if (!adminQuery.empty) {
    const doc = adminQuery.docs[0];
    const data = doc.data();
    return { status: 'exists', message: `Admin already exists: ${data.email} (uid: ${doc.id})` };
  }

  // 2) Try to find existing auth user by email
  let userRecord;
  try {
    userRecord = await auth.getUserByEmail(email);
  } catch (e) {
    if (e.code !== 'auth/user-not-found') throw e;
  }

  if (!userRecord) {
    // Create auth user
    userRecord = await auth.createUser({
      email,
      password,
      emailVerified: true, // assume verified for seeded admin
      displayName: `${firstName} ${lastName}`.trim(),
      disabled: false,
    });
  } else {
    // Ensure not disabled and name set
    if (userRecord.disabled || !userRecord.displayName) {
      await auth.updateUser(userRecord.uid, {
        emailVerified: true,
        displayName: userRecord.displayName || `${firstName} ${lastName}`.trim(),
        disabled: false,
      });
    }
  }

  // 3) Ensure Firestore user doc exists with admin role
  const userRef = db.collection('users').doc(userRecord.uid);
  const docSnap = await userRef.get();

  if (!docSnap.exists) {
    await userRef.set(toUserDoc({ uid: userRecord.uid, email, firstName, lastName }));
    return { status: 'created', message: `Admin created: ${email} (uid: ${userRecord.uid})` };
  } else {
    // If exists but not admin, upgrade safely
    const data = docSnap.data() || {};
    if (data.role !== 'administrator') {
      await userRef.update({
        role: 'administrator',
        status: 'active',
        department: 'administration',
        isActive: true,
        permissions: toUserDoc({ uid: userRecord.uid, email, firstName, lastName }).permissions,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        metadata: { ...(data.metadata || {}), upgradedBy: 'admin_seed_script' },
      });
      return { status: 'upgraded', message: `User upgraded to admin: ${email} (uid: ${userRecord.uid})` };
    }
    return { status: 'exists-auth', message: `Auth user exists and Firestore doc already present for: ${email}` };
  }
}

async function main() {
  const argv = yargs(hideBin(process.argv))
    .options({
      email: { type: 'string', demandOption: true, describe: 'Admin email' },
      password: { type: 'string', demandOption: true, describe: 'Admin password (min 8 chars)' },
      firstName: { type: 'string', demandOption: true, describe: 'Admin first name' },
      lastName: { type: 'string', demandOption: true, describe: 'Admin last name' },
    })
    .help()
    .argv;

  try {
    validateArgs(argv);
    await initFirebaseAdmin();
    const result = await ensureAdmin(argv);
    console.log(`[${result.status}] ${result.message}`);
    process.exit(0);
  } catch (e) {
    console.error('Failed to create admin:', e.message || e);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}