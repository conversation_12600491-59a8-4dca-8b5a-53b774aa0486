import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

@injectable
class CheckEmailVerifiedUseCase implements UseCase<bool, NoParams> {
  final AuthRepository _authRepository;

  const CheckEmailVerifiedUseCase(this._authRepository);

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    return await _authRepository.isEmailVerified();
  }
}
