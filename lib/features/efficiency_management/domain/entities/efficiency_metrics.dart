import 'package:equatable/equatable.dart';

/// Represents efficiency metrics for production processes
class EfficiencyMetrics extends Equatable {
  /// Overall Equipment Effectiveness (OEE) percentage
  final double oee;
  
  /// Production efficiency percentage
  final double productionEfficiency;
  
  /// Labor efficiency percentage
  final double laborEfficiency;
  
  /// Machine utilization percentage
  final double machineUtilization;
  
  /// Downtime in minutes
  final int downtimeMinutes;
  
  /// Number of changeovers
  final int changeovers;
  
  /// Average changeover time in minutes
  final double avgChangeoverTime;
  
  /// Timestamp when these metrics were recorded
  final DateTime timestamp;

  const EfficiencyMetrics({
    required this.oee,
    required this.productionEfficiency,
    required this.laborEfficiency,
    required this.machineUtilization,
    required this.downtimeMinutes,
    required this.changeovers,
    required this.avgChangeoverTime,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [
        oee,
        productionEfficiency,
        laborEfficiency,
        machineUtilization,
        downtimeMinutes,
        changeovers,
        avgChangeoverTime,
        timestamp,
      ];

  /// Creates a copy of this efficiency metrics with the given fields replaced with the new values
  EfficiencyMetrics copyWith({
    double? oee,
    double? productionEfficiency,
    double? laborEfficiency,
    double? machineUtilization,
    int? downtimeMinutes,
    int? changeovers,
    double? avgChangeoverTime,
    DateTime? timestamp,
  }) {
    return EfficiencyMetrics(
      oee: oee ?? this.oee,
      productionEfficiency: productionEfficiency ?? this.productionEfficiency,
      laborEfficiency: laborEfficiency ?? this.laborEfficiency,
      machineUtilization: machineUtilization ?? this.machineUtilization,
      downtimeMinutes: downtimeMinutes ?? this.downtimeMinutes,
      changeovers: changeovers ?? this.changeovers,
      avgChangeoverTime: avgChangeoverTime ?? this.avgChangeoverTime,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  /// Creates an empty efficiency metrics object
  factory EfficiencyMetrics.empty() {
    return  EfficiencyMetrics(
      oee: 0.0,
      productionEfficiency: 0.0,
      laborEfficiency: 0.0,
      machineUtilization: 0.0,
      downtimeMinutes: 0,
      changeovers: 0,
      avgChangeoverTime: 0.0,
      timestamp: DateTime.now(),
    );
  }
}
