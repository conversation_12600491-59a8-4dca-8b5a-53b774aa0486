import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/entities/bundle.dart';
import '../bloc/bundling_bloc.dart';

/// Widget for filtering bundles
class BundleFilterBar extends StatefulWidget {
  const BundleFilterBar({super.key});

  @override
  State<BundleFilterBar> createState() => _BundleFilterBarState();
}

class _BundleFilterBarState extends State<BundleFilterBar> {
  BundleStatus? _selectedStatus;
  GarmentSize? _selectedSize;
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<BundlingBloc, BundlingState>(
      listener: (context, state) {
        if (state is BundleFiltersCleared) {
          setState(() {
            _selectedStatus = null;
            _selectedSize = null;
            _isExpanded = false;
          });
        }
      },
      child: Container(
        color: AppColors.surface,
        child: Column(
          children: [
            _buildQuickFilters(),
            if (_isExpanded) _buildAdvancedFilters(),
          ],
        ),
      ),
    );
  }

  /// Build quick filters
  Widget _buildQuickFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(
            Icons.filter_list,
            color: AppColors.textSecondary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Filters:',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildStatusFilter(),
                  const SizedBox(width: 8),
                  _buildSizeFilter(),
                  const SizedBox(width: 8),
                  _buildClearFiltersButton(),
                ],
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              _isExpanded ? Icons.expand_less : Icons.expand_more,
              color: AppColors.textSecondary,
            ),
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),
        ],
      ),
    );
  }

  /// Build advanced filters
  Widget _buildAdvancedFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        border: Border(
          top: BorderSide(color: AppColors.border),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Advanced Filters',
            style: AppTextStyles.titleSmall.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDateRangeFilter(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildAssigneeFilter(),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _applyFilters,
                  child: const Text('Apply Filters'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: _clearAllFilters,
                  child: const Text('Clear All'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build status filter
  Widget _buildStatusFilter() {
    return DropdownButton<BundleStatus?>(
      value: _selectedStatus,
      hint: Text(
        'Status',
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      underline: const SizedBox(),
      items: [
        const DropdownMenuItem<BundleStatus?>(
          value: null,
          child: Text('All Status'),
        ),
        ...BundleStatus.values.map((status) {
          return DropdownMenuItem<BundleStatus?>(
            value: status,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Color(int.parse(status.colorCode.substring(1), radix: 16) + 0xFF000000),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(status.displayName),
              ],
            ),
          );
        }).toList(),
      ],
      onChanged: (value) {
        setState(() {
          _selectedStatus = value;
        });
        _applyQuickFilter();
      },
    );
  }

  /// Build size filter
  Widget _buildSizeFilter() {
    return DropdownButton<GarmentSize?>(
      value: _selectedSize,
      hint: Text(
        'Size',
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      underline: const SizedBox(),
      items: [
        const DropdownMenuItem<GarmentSize?>(
          value: null,
          child: Text('All Sizes'),
        ),
        ...GarmentSize.values.map((size) {
          return DropdownMenuItem<GarmentSize?>(
            value: size,
            child: Text(size.displayName),
          );
        }).toList(),
      ],
      onChanged: (value) {
        setState(() {
          _selectedSize = value;
        });
        _applyQuickFilter();
      },
    );
  }

  /// Build clear filters button
  Widget _buildClearFiltersButton() {
    final hasFilters = _selectedStatus != null || _selectedSize != null;
    
    if (!hasFilters) return const SizedBox();

    return TextButton.icon(
      onPressed: _clearAllFilters,
      icon: const Icon(Icons.clear, size: 16),
      label: const Text('Clear'),
      style: TextButton.styleFrom(
        foregroundColor: AppColors.error,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  /// Build date range filter
  Widget _buildDateRangeFilter() {
    return OutlinedButton.icon(
      onPressed: () {
        // Show date range picker
        _showDateRangePicker();
      },
      icon: const Icon(Icons.date_range, size: 16),
      label: const Text('Date Range'),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  /// Build assignee filter
  Widget _buildAssigneeFilter() {
    return OutlinedButton.icon(
      onPressed: () {
        // Show assignee picker
        _showAssigneePicker();
      },
      icon: const Icon(Icons.person, size: 16),
      label: const Text('Assignee'),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  /// Apply quick filter
  void _applyQuickFilter() {
    context.read<BundlingBloc>().add(LoadBundles(
      status: _selectedStatus,
      size: _selectedSize,
    ));
  }

  /// Apply all filters
  void _applyFilters() {
    context.read<BundlingBloc>().add(ApplyBundleFilters(
      status: _selectedStatus,
      size: _selectedSize,
      // Add other filter parameters as needed
    ));
  }

  /// Clear all filters
  void _clearAllFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedSize = null;
      _isExpanded = false;
    });
    context.read<BundlingBloc>().add(const ClearBundleFilters());
  }

  /// Show date range picker
  void _showDateRangePicker() {
    showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    ).then((dateRange) {
      if (dateRange != null) {
        // Apply date range filter
        context.read<BundlingBloc>().add(ApplyBundleFilters(
          status: _selectedStatus,
          size: _selectedSize,
          startDate: dateRange.start,
          endDate: dateRange.end,
        ));
      }
    });
  }

  /// Show assignee picker
  void _showAssigneePicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Assignee'),
        content: const Text('Assignee picker will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Apply assignee filter
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }
}
