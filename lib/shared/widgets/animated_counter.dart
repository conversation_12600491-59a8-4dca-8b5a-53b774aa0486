import 'package:flutter/material.dart';

/// An animated counter widget that animates number changes
class AnimatedCounter extends StatefulWidget {
  final double value;
  final Duration duration;
  final TextStyle? textStyle;
  final String prefix;
  final String suffix;
  final int decimalPlaces;
  final Widget Function(BuildContext context, double animatedValue)? builder;

  const AnimatedCounter({
    Key? key,
    required this.value,
    this.duration = const Duration(milliseconds: 1000),
    this.textStyle,
    this.prefix = '',
    this.suffix = '',
    this.decimalPlaces = 0,
    this.builder,
  }) : super(key: key);

  @override
  State<AnimatedCounter> createState() => _AnimatedCounterState();
}

class _AnimatedCounterState extends State<AnimatedCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _previousValue = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: _previousValue,
      end: widget.value,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _previousValue = oldWidget.value;
      _animation = Tween<double>(
        begin: _previousValue,
        end: widget.value,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        if (widget.builder != null) {
          return widget.builder!(context, _animation.value);
        }
        return Text(
          '${widget.prefix}${_animation.value.toStringAsFixed(widget.decimalPlaces)}${widget.suffix}',
          style: widget.textStyle ?? Theme.of(context).textTheme.headlineMedium,
        );
      },
    );
  }
}

/// An animated percentage counter
class AnimatedPercentageCounter extends StatelessWidget {
  final double percentage;
  final Duration duration;
  final TextStyle? textStyle;

  const AnimatedPercentageCounter({
    Key? key,
    required this.percentage,
    this.duration = const Duration(milliseconds: 1000),
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedCounter(
      value: percentage,
      duration: duration,
      textStyle: textStyle,
      suffix: '%',
      decimalPlaces: 1,
    );
  }
}

/// An animated currency counter
class AnimatedCurrencyCounter extends StatelessWidget {
  final double amount;
  final Duration duration;
  final TextStyle? textStyle;
  final String currency;

  const AnimatedCurrencyCounter({
    Key? key,
    required this.amount,
    this.duration = const Duration(milliseconds: 1000),
    this.textStyle,
    this.currency = '\$',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedCounter(
      value: amount,
      duration: duration,
      textStyle: textStyle,
      prefix: currency,
      decimalPlaces: 2,
    );
  }
}

/// An animated integer counter
class AnimatedIntegerCounter extends StatelessWidget {
  final int value;
  final Duration duration;
  final TextStyle? textStyle;
  final String prefix;
  final String suffix;

  const AnimatedIntegerCounter({
    Key? key,
    required this.value,
    this.duration = const Duration(milliseconds: 1000),
    this.textStyle,
    this.prefix = '',
    this.suffix = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedCounter(
      value: value.toDouble(),
      duration: duration,
      textStyle: textStyle,
      prefix: prefix,
      suffix: suffix,
      decimalPlaces: 0,
    );
  }
}