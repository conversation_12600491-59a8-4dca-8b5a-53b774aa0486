import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  final String? code;
  final dynamic details;
  
  const Failure(
    this.message, {
    this.code,
    this.details,
  });
  
  @override
  List<Object?> get props => [message, code, details];
  
  @override
  String toString() => 'Failure(message: $message, code: $code)';
}

/// Server-related failures
class ServerFailure extends Failure {
  const ServerFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Cache-related failures
class CacheFailure extends Failure {
  const CacheFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Authentication-related failures
class AuthFailure extends Failure {
  const AuthFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Authorization-related failures
class AuthorizationFailure extends Failure {
  const AuthorizationFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Validation-related failures
class ValidationFailure extends Failure {
  final Map<String, List<String>>? fieldErrors;

  const ValidationFailure(
    super.message, {
    super.code,
    this.fieldErrors,
    super.details,
  });

  @override
  List<Object?> get props => [super.message, code, details, fieldErrors];
}

/// Not found failures
class NotFoundFailure extends Failure {
  const NotFoundFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// File operation failures
class FileFailure extends Failure {
  const FileFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Permission-related failures
class PermissionFailure extends Failure {
  const PermissionFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Database-related failures
class DatabaseFailure extends Failure {
  const DatabaseFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Timeout-related failures
class TimeoutFailure extends Failure {
  const TimeoutFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Format/Parsing failures
class FormatFailure extends Failure {
  const FormatFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Business logic failures
class BusinessLogicFailure extends Failure {
  const BusinessLogicFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// External service failures
class ExternalServiceFailure extends Failure {
  const ExternalServiceFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Configuration failures
class ConfigurationFailure extends Failure {
  const ConfigurationFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Unknown/Unexpected failures
class UnknownFailure extends Failure {
  const UnknownFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Specific manufacturing domain failures

/// Order-related failures
class OrderFailure extends Failure {
  const OrderFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Production-related failures
class ProductionFailure extends Failure {
  const ProductionFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Inventory-related failures
class InventoryFailure extends Failure {
  const InventoryFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Quality control failures
class QualityFailure extends Failure {
  const QualityFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Task management failures
class TaskFailure extends Failure {
  const TaskFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Workstation-related failures
class WorkstationFailure extends Failure {
  const WorkstationFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Department-related failures
class DepartmentFailure extends Failure {
  const DepartmentFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// User management failures
class UserManagementFailure extends Failure {
  const UserManagementFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Reporting failures
class ReportingFailure extends Failure {
  const ReportingFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Notification failures
class NotificationFailure extends Failure {
  const NotificationFailure(
    super.message, {
    super.code,
    super.details,
  });
}

/// Unimplemented feature failure
class UnimplementedFailure extends Failure {
  const UnimplementedFailure(
    super.message, {
    super.code,
    super.details,
  });
}


/// Operation not supported failure
class UnsupportedFailure extends Failure {
  const UnsupportedFailure(
    super.message, {
    super.code = 'UNSUPPORTED_OPERATION',
    super.details,
  });
}
