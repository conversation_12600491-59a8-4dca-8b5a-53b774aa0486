import 'package:equatable/equatable.dart';

import '../../../../shared/models/base_entity.dart';
import 'financial_entities.dart';

/// Financial report entity
class FinancialReport extends BaseEntity {
  final String reportCode;
  final String reportName;
  final String description;
  final FinancialReportType type;
  final ReportPeriod period;
  final DateTime periodStart;
  final DateTime periodEnd;
  final String currency;
  final ReportStatus status;
  final String generatedBy;
  final DateTime? generatedAt;
  final Map<String, dynamic> parameters;
  final FinancialReportData data;
  final List<String> tags;

  const FinancialReport({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.reportCode,
    required this.reportName,
    required this.description,
    required this.type,
    required this.period,
    required this.periodStart,
    required this.periodEnd,
    required this.currency,
    required this.status,
    required this.generatedBy,
    this.generatedAt,
    this.parameters = const {},
    required this.data,
    this.tags = const [],
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        reportCode,
        reportName,
        description,
        type,
        period,
        periodStart,
        periodEnd,
        currency,
        status,
        generatedBy,
        generatedAt,
        parameters,
        data,
        tags,
      ];

  /// Check if report is ready
  bool get isReady => status == ReportStatus.completed;

  /// Get report duration in days
  int get durationInDays => periodEnd.difference(periodStart).inDays;
}

/// Financial report data
class FinancialReportData extends Equatable {
  final Map<String, double> summary;
  final List<FinancialReportSection> sections;
  final List<FinancialChart> charts;
  final Map<String, dynamic> metadata;

  const FinancialReportData({
    this.summary = const {},
    this.sections = const [],
    this.charts = const [],
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [summary, sections, charts, metadata];
}

/// Financial report section
class FinancialReportSection extends Equatable {
  final String title;
  final String description;
  final List<FinancialReportItem> items;
  final Map<String, double> totals;

  const FinancialReportSection({
    required this.title,
    required this.description,
    this.items = const [],
    this.totals = const {},
  });

  @override
  List<Object?> get props => [title, description, items, totals];
}

/// Financial report item
class FinancialReportItem extends Equatable {
  final String label;
  final double amount;
  final double? previousAmount;
  final double? variance;
  final double? variancePercentage;
  final String? unit;
  final Map<String, dynamic> attributes;

  const FinancialReportItem({
    required this.label,
    required this.amount,
    this.previousAmount,
    this.variance,
    this.variancePercentage,
    this.unit,
    this.attributes = const {},
  });

  @override
  List<Object?> get props => [
        label,
        amount,
        previousAmount,
        variance,
        variancePercentage,
        unit,
        attributes,
      ];
}

/// Financial chart
class FinancialChart extends Equatable {
  final String title;
  final ChartType type;
  final List<ChartDataPoint> dataPoints;
  final Map<String, dynamic> configuration;

  const FinancialChart({
    required this.title,
    required this.type,
    this.dataPoints = const [],
    this.configuration = const {},
  });

  @override
  List<Object?> get props => [title, type, dataPoints, configuration];
}

/// Chart data point
class ChartDataPoint extends Equatable {
  final String label;
  final double value;
  final String? category;
  final DateTime? date;
  final Map<String, dynamic> metadata;

  const ChartDataPoint({
    required this.label,
    required this.value,
    this.category,
    this.date,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [label, value, category, date, metadata];
}

/// Profit and loss statement
class ProfitLossStatement extends Equatable {
  final DateTime periodStart;
  final DateTime periodEnd;
  final String currency;
  final double totalRevenue;
  final double totalCostOfGoodsSold;
  final double grossProfit;
  final double totalOperatingExpenses;
  final double operatingIncome;
  final double totalOtherIncome;
  final double totalOtherExpenses;
  final double netIncome;
  final double grossProfitMargin;
  final double operatingMargin;
  final double netProfitMargin;
  final Map<String, double> revenueBreakdown;
  final Map<String, double> expenseBreakdown;

  const ProfitLossStatement({
    required this.periodStart,
    required this.periodEnd,
    required this.currency,
    required this.totalRevenue,
    required this.totalCostOfGoodsSold,
    required this.grossProfit,
    required this.totalOperatingExpenses,
    required this.operatingIncome,
    required this.totalOtherIncome,
    required this.totalOtherExpenses,
    required this.netIncome,
    required this.grossProfitMargin,
    required this.operatingMargin,
    required this.netProfitMargin,
    this.revenueBreakdown = const {},
    this.expenseBreakdown = const {},
  });

  @override
  List<Object?> get props => [
        periodStart,
        periodEnd,
        currency,
        totalRevenue,
        totalCostOfGoodsSold,
        grossProfit,
        totalOperatingExpenses,
        operatingIncome,
        totalOtherIncome,
        totalOtherExpenses,
        netIncome,
        grossProfitMargin,
        operatingMargin,
        netProfitMargin,
        revenueBreakdown,
        expenseBreakdown,
      ];
}

/// Cost analysis report
class CostAnalysisReport extends Equatable {
  final DateTime periodStart;
  final DateTime periodEnd;
  final String currency;
  final double totalCosts;
  final double directCosts;
  final double indirectCosts;
  final double fixedCosts;
  final double variableCosts;
  final Map<CostCategory, double> costsByCategory;
  final Map<String, double> costsByCostCenter;
  final Map<String, double> costsByDepartment;
  final Map<String, double> costsByProduct;
  final List<CostTrend> trends;

  const CostAnalysisReport({
    required this.periodStart,
    required this.periodEnd,
    required this.currency,
    required this.totalCosts,
    required this.directCosts,
    required this.indirectCosts,
    required this.fixedCosts,
    required this.variableCosts,
    this.costsByCategory = const {},
    this.costsByCostCenter = const {},
    this.costsByDepartment = const {},
    this.costsByProduct = const {},
    this.trends = const [],
  });

  @override
  List<Object?> get props => [
        periodStart,
        periodEnd,
        currency,
        totalCosts,
        directCosts,
        indirectCosts,
        fixedCosts,
        variableCosts,
        costsByCategory,
        costsByCostCenter,
        costsByDepartment,
        costsByProduct,
        trends,
      ];

  /// Get cost breakdown percentages
  Map<String, double> get costBreakdownPercentages {
    if (totalCosts <= 0) return {};
    return {
      'Direct': (directCosts / totalCosts) * 100,
      'Indirect': (indirectCosts / totalCosts) * 100,
      'Fixed': (fixedCosts / totalCosts) * 100,
      'Variable': (variableCosts / totalCosts) * 100,
    };
  }
}

/// Cost trend
class CostTrend extends Equatable {
  final DateTime date;
  final double amount;
  final CostCategory category;
  final String? costCenterId;
  final String? departmentId;

  const CostTrend({
    required this.date,
    required this.amount,
    required this.category,
    this.costCenterId,
    this.departmentId,
  });

  @override
  List<Object?> get props => [date, amount, category, costCenterId, departmentId];
}

/// Budget variance report
class BudgetVarianceReport extends Equatable {
  final DateTime periodStart;
  final DateTime periodEnd;
  final String currency;
  final double totalBudget;
  final double totalActual;
  final double totalVariance;
  final double variancePercentage;
  final List<BudgetVarianceItem> items;
  final Map<String, BudgetVarianceItem> varianceByCategory;
  final Map<String, BudgetVarianceItem> varianceByCostCenter;

  const BudgetVarianceReport({
    required this.periodStart,
    required this.periodEnd,
    required this.currency,
    required this.totalBudget,
    required this.totalActual,
    required this.totalVariance,
    required this.variancePercentage,
    this.items = const [],
    this.varianceByCategory = const {},
    this.varianceByCostCenter = const {},
  });

  @override
  List<Object?> get props => [
        periodStart,
        periodEnd,
        currency,
        totalBudget,
        totalActual,
        totalVariance,
        variancePercentage,
        items,
        varianceByCategory,
        varianceByCostCenter,
      ];

  /// Check if budget is exceeded
  bool get isBudgetExceeded => totalActual > totalBudget;

  /// Get favorable variances
  List<BudgetVarianceItem> get favorableVariances {
    return items.where((item) => item.isFavorable).toList();
  }

  /// Get unfavorable variances
  List<BudgetVarianceItem> get unfavorableVariances {
    return items.where((item) => item.isUnfavorable).toList();
  }
}

/// Budget variance item
class BudgetVarianceItem extends Equatable {
  final String description;
  final double budgetAmount;
  final double actualAmount;
  final double variance;
  final double variancePercentage;
  final CostCategory category;
  final String? costCenterId;
  final String? departmentId;

  const BudgetVarianceItem({
    required this.description,
    required this.budgetAmount,
    required this.actualAmount,
    required this.variance,
    required this.variancePercentage,
    required this.category,
    this.costCenterId,
    this.departmentId,
  });

  @override
  List<Object?> get props => [
        description,
        budgetAmount,
        actualAmount,
        variance,
        variancePercentage,
        category,
        costCenterId,
        departmentId,
      ];

  /// Check if variance is favorable (under budget)
  bool get isFavorable => variance < 0;

  /// Check if variance is unfavorable (over budget)
  bool get isUnfavorable => variance > 0;

  /// Get absolute variance
  double get absoluteVariance => variance.abs();

  /// Get absolute variance percentage
  double get absoluteVariancePercentage => variancePercentage.abs();
}

/// Financial summary
class FinancialSummary extends Equatable {
  final double totalRevenue;
  final double totalExpenses;
  final double netIncome;
  final double profitMargin;

  const FinancialSummary({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netIncome,
    required this.profitMargin,
  });

  @override
  List<Object?> get props =>
      [totalRevenue, totalExpenses, netIncome, profitMargin];
}

/// Cost trends
class CostTrends extends Equatable {
  final List<CostTrend> trends;

  const CostTrends({required this.trends});

  @override
  List<Object?> get props => [trends];
}

// Enums

/// Financial report type
enum FinancialReportType {
  profitLoss,
  balanceSheet,
  cashFlow,
  costAnalysis,
  budgetVariance,
  departmentPerformance,
  productProfitability,
  custom,
}

/// Financial report type extension
extension FinancialReportTypeExtension on FinancialReportType {
  String get displayName {
    switch (this) {
      case FinancialReportType.profitLoss:
        return 'Profit & Loss Statement';
      case FinancialReportType.balanceSheet:
        return 'Balance Sheet';
      case FinancialReportType.cashFlow:
        return 'Cash Flow Statement';
      case FinancialReportType.costAnalysis:
        return 'Cost Analysis Report';
      case FinancialReportType.budgetVariance:
        return 'Budget Variance Report';
      case FinancialReportType.departmentPerformance:
        return 'Department Performance Report';
      case FinancialReportType.productProfitability:
        return 'Product Profitability Report';
      case FinancialReportType.custom:
        return 'Custom Report';
    }
  }

  String get value => name;
}

/// Report period
enum ReportPeriod {
  daily,
  weekly,
  monthly,
  quarterly,
  semiAnnual,
  annual,
  custom,
}

/// Report period extension
extension ReportPeriodExtension on ReportPeriod {
  String get displayName {
    switch (this) {
      case ReportPeriod.daily:
        return 'Daily';
      case ReportPeriod.weekly:
        return 'Weekly';
      case ReportPeriod.monthly:
        return 'Monthly';
      case ReportPeriod.quarterly:
        return 'Quarterly';
      case ReportPeriod.semiAnnual:
        return 'Semi-Annual';
      case ReportPeriod.annual:
        return 'Annual';
      case ReportPeriod.custom:
        return 'Custom Period';
    }
  }

  String get value => name;
}

/// Report status
enum ReportStatus {
  pending,
  generating,
  completed,
  failed,
  cancelled,
}

/// Report status extension
extension ReportStatusExtension on ReportStatus {
  String get displayName {
    switch (this) {
      case ReportStatus.pending:
        return 'Pending';
      case ReportStatus.generating:
        return 'Generating';
      case ReportStatus.completed:
        return 'Completed';
      case ReportStatus.failed:
        return 'Failed';
      case ReportStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value => name;
}

/// Chart type
enum ChartType {
  line,
  bar,
  pie,
  area,
  scatter,
  donut,
}

/// Chart type extension
extension ChartTypeExtension on ChartType {
  String get displayName {
    switch (this) {
      case ChartType.line:
        return 'Line Chart';
      case ChartType.bar:
        return 'Bar Chart';
      case ChartType.pie:
        return 'Pie Chart';
      case ChartType.area:
        return 'Area Chart';
      case ChartType.scatter:
        return 'Scatter Plot';
      case ChartType.donut:
        return 'Donut Chart';
    }
  }

  String get value => name;
}
