import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/munda_record.dart';
import '../entities/munda_task.dart';
import '../entities/munda_requests.dart';

/// Repository interface for Munda operations
abstract class MundaRepository {
  // ==================== MUNDA RECORDS ====================
  
  /// Create a new Munda record
  Future<Either<Failure, MundaRecord>> createRecord(
    CreateMundaRecordRequest request,
    String userId,
  );

  /// Get a Munda record by ID
  Future<Either<Failure, MundaRecord>> getRecord(String recordId);

  /// Update a Munda record
  Future<Either<Failure, MundaRecord>> updateRecord(
    UpdateMundaRecordRequest request,
    String userId,
  );

  /// Delete a Munda record
  Future<Either<Failure, void>> deleteRecord(String recordId, String userId);

  /// Get paginated list of Munda records
  Future<Either<Failure, PaginatedResult<MundaRecord>>> getRecords({
    MundaRecordFilterCriteria? filter,
    PaginationParams? pagination,
    String? userId,
  });

  /// Get records for a specific task
  Future<Either<Failure, List<MundaRecord>>> getRecordsByTask(String taskId);

  /// Get records for a specific user
  Future<Either<Failure, List<MundaRecord>>> getRecordsByUser(
    String userId, {
    MundaRecordFilterCriteria? filter,
  });

  /// Get summary of records
  Future<Either<Failure, MundaRecordSummary>> getRecordsSummary({
    MundaRecordFilterCriteria? filter,
    String? userId,
  });

  /// Get next serial number for a user
  Future<Either<Failure, int>> getNextSerialNumber(String userId);

  /// Bulk create records
  Future<Either<Failure, List<MundaRecord>>> bulkCreateRecords(
    List<CreateMundaRecordRequest> requests,
    String userId,
  );

  /// Export records to various formats
  Future<Either<Failure, String>> exportRecords({
    MundaRecordFilterCriteria? filter,
    String? userId,
    String format = 'csv', // csv, excel, pdf
  });

  // ==================== MUNDA TASKS ====================

  /// Create a new Munda task
  Future<Either<Failure, MundaTask>> createTask(
    CreateMundaTaskRequest request,
    String createdBy,
  );

  /// Get a Munda task by ID
  Future<Either<Failure, MundaTask>> getTask(String taskId);

  /// Update a Munda task
  Future<Either<Failure, MundaTask>> updateTask(
    UpdateMundaTaskRequest request,
    String updatedBy,
  );

  /// Delete a Munda task
  Future<Either<Failure, void>> deleteTask(String taskId, String deletedBy);

  /// Get paginated list of Munda tasks
  Future<Either<Failure, PaginatedResult<MundaTask>>> getTasks({
    MundaTaskFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get tasks assigned to a specific user
  Future<Either<Failure, List<MundaTask>>> getTasksByUser(
    String userId, {
    MundaTaskFilterCriteria? filter,
  });

  /// Assign a task to a user
  Future<Either<Failure, MundaTask>> assignTask(
    String taskId,
    String assignedTo,
    String assignedBy,
  );

  /// Start a task
  Future<Either<Failure, MundaTask>> startTask(String taskId, String userId);

  /// Complete a task
  Future<Either<Failure, MundaTask>> completeTask(
    String taskId,
    String userId, {
    String? notes,
  });

  /// Send task to bundling
  Future<Either<Failure, MundaTask>> sendTaskToBundling(
    String taskId,
    String userId, {
    String? notes,
  });

  /// Cancel a task
  Future<Either<Failure, MundaTask>> cancelTask(
    String taskId,
    String userId,
    String reason,
  );

  /// Update task progress (called when records are added/updated)
  Future<Either<Failure, MundaTask>> updateTaskProgress(String taskId);

  /// Get task statistics
  Future<Either<Failure, Map<String, dynamic>>> getTaskStatistics({
    String? userId,
    DateTime? fromDate,
    DateTime? toDate,
  });

  // ==================== REAL-TIME UPDATES ====================

  /// Stream of record updates for a user
  Stream<List<MundaRecord>> watchUserRecords(String userId);

  /// Stream of task updates for a user
  Stream<List<MundaTask>> watchUserTasks(String userId);

  /// Stream of record summary updates
  Stream<MundaRecordSummary> watchRecordsSummary({
    String? userId,
    MundaRecordFilterCriteria? filter,
  });

  /// Stream of task updates for a specific task
  Stream<MundaTask> watchTask(String taskId);

  // ==================== OFFLINE SUPPORT ====================

  /// Sync offline data with server
  Future<Either<Failure, void>> syncOfflineData(String userId);

  /// Get offline records count
  Future<Either<Failure, int>> getOfflineRecordsCount(String userId);

  /// Clear offline data
  Future<Either<Failure, void>> clearOfflineData(String userId);

  // ==================== VALIDATION ====================

  /// Validate record data
  Future<Either<Failure, bool>> validateRecord(
    CreateMundaRecordRequest request,
  );

  /// Check for duplicate records
  Future<Either<Failure, bool>> checkDuplicateRecord(
    String designNumber,
    String lotNumber,
    String rollNumber,
    String userId,
  );

  /// Validate task data
  Future<Either<Failure, bool>> validateTask(CreateMundaTaskRequest request);

  // ==================== ANALYTICS ====================

  /// Get productivity analytics for a user
  Future<Either<Failure, Map<String, dynamic>>> getUserProductivityAnalytics(
    String userId, {
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get department analytics
  Future<Either<Failure, Map<String, dynamic>>> getDepartmentAnalytics({
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get quality metrics
  Future<Either<Failure, Map<String, dynamic>>> getQualityMetrics({
    String? userId,
    DateTime? fromDate,
    DateTime? toDate,
  });
}
