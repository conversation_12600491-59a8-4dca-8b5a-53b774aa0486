import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:hm_collection/features/auth/presentation/pages/admin_login_page.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/quick_stats_widget.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../../../auth/presentation/pages/firebase_login_page.dart';
import '../bloc/dashboard_bloc.dart';
import '../widgets/dashboard_app_bar.dart';
import '../widgets/dashboard_drawer.dart';
import '../widgets/dashboard_grid.dart';
import '../widgets/dashboard_loading.dart' as widgets;
import '../widgets/quick_actions_section.dart';
import '../widgets/statistics_section.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<FirebaseAuthBloc, FirebaseAuthState>(
      listener: (context, state) {
        if (state is FirebaseAuthUnauthenticated) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const FirebaseLoginPage()),
            (route) => false,
          );
        }
      },
      child: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, authState) {
          if (authState is FirebaseAuthAuthenticated) {
            return BlocProvider(
              create: (context) => GetIt.instance<DashboardBloc>()
                ..add(LoadDashboardRequested(authState.user)),
              child: const DashboardView(),
            );
          }
          // If not authenticated, show a login prompt or navigate away.
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        },
      ),
    );
  }
}

class DashboardView extends StatefulWidget {
  const DashboardView({super.key});

  @override
  State<DashboardView> createState() => _DashboardViewState();
}

class _DashboardViewState extends State<DashboardView> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    // Access the user from the authenticated state.
    final user = (context.watch<FirebaseAuthBloc>().state as FirebaseAuthAuthenticated).user;

    return Scaffold(
      key: _scaffoldKey,
      appBar: DashboardAppBar(
        user: user,
        onMenuPressed: () => _scaffoldKey.currentState?.openDrawer(),
        onRefresh: () => _refreshDashboard(user),
      ),
      drawer: DashboardDrawer(user: user),
      body: BlocBuilder<DashboardBloc, DashboardState>(
        builder: (context, state) {
          if (state is DashboardLoading) {
            return const widgets.DashboardLoading();
          }

          if (state is DashboardError) {
            return _buildErrorWidget(state.message, user);
          }

          if (state is DashboardLoaded) {
            return _buildDashboardContent(state, user);
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildErrorWidget(String message, User user) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading dashboard',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _refreshDashboard(user),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent(DashboardLoaded state, User user) {
    return RefreshIndicator(
      onRefresh: () async => _refreshDashboard(user),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(user),
            const SizedBox(height: 24),
            _buildQuickStats(),
            const SizedBox(height: 24),
            StatisticsSection(statistics: state.statistics),
            const SizedBox(height: 24),
            QuickActionsSection(
              quickActions: state.quickActions,
              onActionTap: _handleQuickAction,
            ),
            const SizedBox(height: 24),
            DashboardGrid(
              layout: state.layout,
              statistics: state.statistics,
              charts: state.charts,
              recentActivities: state.recentActivities,
              notifications: state.notifications,
              onWidgetTap: _handleWidgetTap,
              onNotificationTap: _handleNotificationTap,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    final greeting = _getGreeting();
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$greeting, ${user.firstName}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      user.role.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '•',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      user.department.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _getRoleBasedMessage(user.role),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white.withOpacity(0.2),
            backgroundImage: user.profileImageUrl != null
                ? NetworkImage(user.profileImageUrl!)
                : null,
            child: user.profileImageUrl == null
                ? Text(
                    user.initials,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )
                : null,
          ),
        ],
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }

  String _getRoleBasedMessage(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return 'Monitor system performance and manage operations';
      case UserRole.merchandiser:
        return 'Track orders and manage client relationships';
      case UserRole.inventoryManager:
        return 'Monitor inventory levels and material flow';
      case UserRole.cuttingHead:
        return 'Oversee cutting operations and quality';
      case UserRole.sewingHead:
        return 'Manage sewing production and team performance';
      case UserRole.finishingHead:
        return 'Ensure quality finishing and timely delivery';
      case UserRole.qualityController:
        return 'Maintain quality standards and inspections';
      case UserRole.sewingSupervisor:
        return 'Supervise sewing operations and productivity';
      default:
        return 'Welcome to your dashboard';
    }
  }

  void _refreshDashboard(User user) {
    context.read<DashboardBloc>().add(RefreshDashboardRequested(user));
  }

  void _handleQuickAction(String actionId) {
    // Handle quick action navigation
    switch (actionId) {
      case 'create_order':
        _navigateToCreateOrder();
        break;
      case 'view_production':
        _navigateToProduction();
        break;
      case 'check_inventory':
        _navigateToInventory();
        break;
      case 'quality_report':
        _navigateToQuality();
        break;
      case 'user_management':
        _navigateToUserManagement();
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Action $actionId not implemented yet')),
        );
    }
  }

  void _handleWidgetTap(String widgetId) {
    // Handle widget tap for detailed view
    switch (widgetId) {
      case 'statistics':
        _navigateToAnalytics();
        break;
      case 'production_status':
        _navigateToProduction();
        break;
      case 'order_summary':
        _navigateToOrders();
        break;
      case 'quality_metrics':
        _navigateToQuality();
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Widget $widgetId details not available')),
        );
    }
  }

  void _handleNotificationTap(String notificationId) {
    // Mark notification as read and handle action
    context.read<DashboardBloc>().add(
      MarkNotificationAsReadRequested(notificationId),
    );

    // TODO: Navigate to notification details or action
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification marked as read')),
    );
  }

  // Navigation methods
  void _navigateToCreateOrder() {
    // TODO: Navigate to create order page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create Order feature coming soon')),
    );
  }

  void _navigateToProduction() {
    // TODO: Navigate to production page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Production Management feature coming soon')),
    );
  }

  void _navigateToInventory() {
    // TODO: Navigate to inventory page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Inventory Management feature coming soon')),
    );
  }

  void _navigateToQuality() {
    // TODO: Navigate to quality page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Quality Control feature coming soon')),
    );
  }

  void _navigateToUserManagement() {
    // TODO: Navigate to user management page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('User Management feature coming soon')),
    );
  }

  void _navigateToAnalytics() {
    // TODO: Navigate to analytics page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Analytics feature coming soon')),
    );
  }

  void _navigateToOrders() {
    // TODO: Navigate to orders page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Order Management feature coming soon')),
    );
  }
}

Widget _buildQuickStats() {
    return const QuickStatsWidget(
      stats: [
        QuickStat(
          title: 'Total Users',
          value: '156',
          icon: Icons.people,
          color: Colors.blue,
          trend: '+12%',
        ),
        QuickStat(
          title: 'Active Orders',
          value: '89',
          icon: Icons.shopping_cart,
          color: Colors.green,
          trend: '+5%',
        ),
        QuickStat(
          title: 'Production Lines',
          value: '12',
          icon: Icons.factory,
          color: Colors.orange,
          trend: '0%',
        ),
        QuickStat(
          title: 'System Health',
          value: '98%',
          icon: Icons.health_and_safety,
          color: Colors.purple,
          trend: '+2%',
        ),
      ],
    );
  }
