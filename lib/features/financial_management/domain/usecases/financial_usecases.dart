import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/core/usecases/usecase.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/shared/models/pagination.dart';
import '../entities/financial_entities.dart';
import '../entities/financial_report_entities.dart' hide FinancialSummary, CostTrends;
import '../repositories/financial_repository.dart';

///* Use Cases

class GetCostCentersUseCase
    implements UseCase<ApiListResponse<CostCenter>, GetCostCentersParams> {
  final FinancialRepository repository;
  GetCostCentersUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<CostCenter>>> call(
      GetCostCentersParams params) async {
    return await repository.getCostCenters(
        filter: params.filter, pagination: params.pagination);
  }
}

class GetCostCenterByIdUseCase
    implements UseCase<ApiResponse<CostCenter>, IdParams> {
  final FinancialRepository repository;
  GetCostCenterByIdUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<CostCenter>>> call(IdParams params) async {
    return await repository.getCostCenterById(params.id);
  }
}

class CreateCostCenterUseCase
    implements UseCase<ApiResponse<CostCenter>, CreateCostCenterRequest> {
  final FinancialRepository repository;
  CreateCostCenterUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<CostCenter>>> call(
      CreateCostCenterRequest params) async {
    return await repository.createCostCenter(params);
  }
}

class UpdateCostCenterUseCase
    implements UseCase<ApiResponse<CostCenter>, UpdateCostCenterRequest> {
  final FinancialRepository repository;
  UpdateCostCenterUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<CostCenter>>> call(
      UpdateCostCenterRequest params) async {
    return await repository.updateCostCenter(params);
  }
}

class DeleteCostCenterUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final FinancialRepository repository;
  DeleteCostCenterUseCase(this.repository);
  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await repository.deleteCostCenter(params.id);
  }
}

class GetTransactionsUseCase
    implements
        UseCase<ApiListResponse<FinancialTransaction>, GetTransactionsParams> {
  final FinancialRepository repository;
  GetTransactionsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<FinancialTransaction>>> call(
      GetTransactionsParams params) async {
    return await repository.getTransactions(
        filter: params.filter, pagination: params.pagination);
  }
}

class GetTransactionByIdUseCase
    implements UseCase<ApiResponse<FinancialTransaction>, IdParams> {
  final FinancialRepository repository;
  GetTransactionByIdUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<FinancialTransaction>>> call(
      IdParams params) async {
    return await repository.getTransactionById(params.id);
  }
}

class CreateTransactionUseCase
    implements
        UseCase<ApiResponse<FinancialTransaction>, CreateTransactionRequest> {
  final FinancialRepository repository;
  CreateTransactionUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<FinancialTransaction>>> call(
      CreateTransactionRequest params) async {
    return await repository.createTransaction(params);
  }
}

class UpdateTransactionUseCase
    implements
        UseCase<ApiResponse<FinancialTransaction>, UpdateTransactionRequest> {
  final FinancialRepository repository;
  UpdateTransactionUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<FinancialTransaction>>> call(
      UpdateTransactionRequest params) async {
    return await repository.updateTransaction(params);
  }
}

class DeleteTransactionUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final FinancialRepository repository;
  DeleteTransactionUseCase(this.repository);
  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await repository.deleteTransaction(params.id);
  }
}

class ApproveTransactionUseCase
    implements
        UseCase<ApiResponse<FinancialTransaction>, ApproveTransactionParams> {
  final FinancialRepository repository;
  ApproveTransactionUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<FinancialTransaction>>> call(
      ApproveTransactionParams params) async {
    return await repository.approveTransaction(
        params.transactionId, params.approverId);
  }
}

class RejectTransactionUseCase
    implements
        UseCase<ApiResponse<FinancialTransaction>, RejectTransactionParams> {
  final FinancialRepository repository;
  RejectTransactionUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<FinancialTransaction>>> call(
      RejectTransactionParams params) async {
    return await repository.rejectTransaction(
        params.transactionId, params.rejectedBy, params.reason);
  }
}

class GetBudgetsUseCase
    implements UseCase<ApiListResponse<Budget>, GetBudgetsParams> {
  final FinancialRepository repository;
  GetBudgetsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Budget>>> call(
      GetBudgetsParams params) async {
    return await repository.getBudgets(
        filter: params.filter, pagination: params.pagination);
  }
}

class GetBudgetByIdUseCase implements UseCase<ApiResponse<Budget>, IdParams> {
  final FinancialRepository repository;
  GetBudgetByIdUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Budget>>> call(IdParams params) async {
    return await repository.getBudgetById(params.id);
  }
}

class CreateBudgetUseCase
    implements UseCase<ApiResponse<Budget>, CreateBudgetRequest> {
  final FinancialRepository repository;
  CreateBudgetUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Budget>>> call(
      CreateBudgetRequest params) async {
    return await repository.createBudget(params);
  }
}

class UpdateBudgetUseCase
    implements UseCase<ApiResponse<Budget>, UpdateBudgetRequest> {
  final FinancialRepository repository;
  UpdateBudgetUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Budget>>> call(
      UpdateBudgetRequest params) async {
    return await repository.updateBudget(params);
  }
}

class DeleteBudgetUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final FinancialRepository repository;
  DeleteBudgetUseCase(this.repository);
  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await repository.deleteBudget(params.id);
  }
}

class ApproveBudgetUseCase
    implements UseCase<ApiResponse<Budget>, ApproveBudgetParams> {
  final FinancialRepository repository;
  ApproveBudgetUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Budget>>> call(
      ApproveBudgetParams params) async {
    return await repository.approveBudget(params.budgetId, params.approverId);
  }
}

class RejectBudgetUseCase
    implements UseCase<ApiResponse<Budget>, RejectBudgetParams> {
  final FinancialRepository repository;
  RejectBudgetUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Budget>>> call(
      RejectBudgetParams params) async {
    return await repository.rejectBudget(
        params.budgetId, params.rejectedBy, params.reason);
  }
}

class GetReportsUseCase
    implements UseCase<ApiListResponse<FinancialReport>, GetReportsParams> {
  final FinancialRepository repository;
  GetReportsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<FinancialReport>>> call(
      GetReportsParams params) async {
    return await repository.getReports(
        filter: params.filter, pagination: params.pagination);
  }
}

class GenerateReportUseCase
    implements UseCase<ApiResponse<FinancialReport>, GenerateReportRequest> {
  final FinancialRepository repository;
  GenerateReportUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<FinancialReport>>> call(
      GenerateReportRequest params) async {
    return await repository.generateReport(params);
  }
}

class GetProfitLossStatementUseCase
    implements UseCase<ProfitLossStatement, GetProfitLossStatementParams> {
  final FinancialRepository repository;
  GetProfitLossStatementUseCase(this.repository);
  @override
  Future<Either<Failure, ProfitLossStatement>> call(
      GetProfitLossStatementParams params) async {
    return await repository.getProfitLossStatement(
      startDate: params.startDate,
      endDate: params.endDate,
      costCenterId: params.costCenterId,
      departmentId: params.departmentId,
    );
  }
}

class GetCostAnalysisReportUseCase
    implements UseCase<CostAnalysisReport, GetCostAnalysisReportParams> {
  final FinancialRepository repository;
  GetCostAnalysisReportUseCase(this.repository);
  @override
  Future<Either<Failure, CostAnalysisReport>> call(
      GetCostAnalysisReportParams params) async {
    return await repository.getCostAnalysisReport(
      startDate: params.startDate,
      endDate: params.endDate,
      costCenterId: params.costCenterId,
      departmentId: params.departmentId,
    );
  }
}

class GetBudgetVarianceReportUseCase
    implements UseCase<BudgetVarianceReport, GetBudgetVarianceReportParams> {
  final FinancialRepository repository;
  GetBudgetVarianceReportUseCase(this.repository);
  @override
  Future<Either<Failure, BudgetVarianceReport>> call(
      GetBudgetVarianceReportParams params) async {
    return await repository.getBudgetVarianceReport(
      startDate: params.startDate,
      endDate: params.endDate,
      budgetId: params.budgetId,
      costCenterId: params.costCenterId,
    );
  }
}

class GetFinancialSummaryUseCase
    implements UseCase<FinancialSummary, GetFinancialSummaryParams> {
  final FinancialRepository repository;
  GetFinancialSummaryUseCase(this.repository);
  @override
  Future<Either<Failure, FinancialSummary>> call(
      GetFinancialSummaryParams params) async {
    return await repository.getFinancialSummary(
      startDate: params.startDate,
      endDate: params.endDate,
      costCenterId: params.costCenterId,
    );
  }
}

class GetCostTrendsUseCase
    implements UseCase<CostTrends, GetCostTrendsParams> {
  final FinancialRepository repository;
  GetCostTrendsUseCase(this.repository);
  @override
  Future<Either<Failure, CostTrends>> call(GetCostTrendsParams params) async {
    return await repository.getCostTrends(
      startDate: params.startDate,
      endDate: params.endDate,
      category: params.category,
      costCenterId: params.costCenterId,
    );
  }
}

class SearchCostCentersUseCase
    implements UseCase<ApiListResponse<CostCenter>, SearchCostCentersParams> {
  final FinancialRepository repository;
  SearchCostCentersUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<CostCenter>>> call(
      SearchCostCentersParams params) async {
    return await repository.searchCostCenters(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

class SearchTransactionsUseCase
    implements
        UseCase<ApiListResponse<FinancialTransaction>,
            SearchTransactionsParams> {
  final FinancialRepository repository;
  SearchTransactionsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<FinancialTransaction>>> call(
      SearchTransactionsParams params) async {
    return await repository.searchTransactions(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

class SearchBudgetsUseCase
    implements UseCase<ApiListResponse<Budget>, SearchBudgetsParams> {
  final FinancialRepository repository;
  SearchBudgetsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Budget>>> call(
      SearchBudgetsParams params) async {
    return await repository.searchBudgets(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

///* Parameter Classes

class GetCostCentersParams extends Equatable {
  final CostCenterFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetCostCentersParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class GetTransactionsParams extends Equatable {
  final TransactionFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetTransactionsParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class GetBudgetsParams extends Equatable {
  final BudgetFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetBudgetsParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class GetReportsParams extends Equatable {
  final ReportFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetReportsParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class ApproveTransactionParams extends Equatable {
  final String transactionId;
  final String approverId;

  const ApproveTransactionParams({required this.transactionId, required this.approverId});

  @override
  List<Object?> get props => [transactionId, approverId];
}

class RejectTransactionParams extends Equatable {
  final String transactionId;
  final String rejectedBy;
  final String reason;

  const RejectTransactionParams(
      {required this.transactionId, required this.rejectedBy, required this.reason});

  @override
  List<Object?> get props => [transactionId, rejectedBy, reason];
}

class ApproveBudgetParams extends Equatable {
  final String budgetId;
  final String approverId;

  const ApproveBudgetParams({required this.budgetId, required this.approverId});

  @override
  List<Object?> get props => [budgetId, approverId];
}

class RejectBudgetParams extends Equatable {
  final String budgetId;
  final String rejectedBy;
  final String reason;

  const RejectBudgetParams(
      {required this.budgetId, required this.rejectedBy, required this.reason});

  @override
  List<Object?> get props => [budgetId, rejectedBy, reason];
}

class GetProfitLossStatementParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  final String? departmentId;

  const GetProfitLossStatementParams(
      {required this.startDate, required this.endDate, this.costCenterId, this.departmentId});

  @override
  List<Object?> get props => [startDate, endDate, costCenterId, departmentId];
}

class GetCostAnalysisReportParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  final String? departmentId;

  const GetCostAnalysisReportParams(
      {required this.startDate, required this.endDate, this.costCenterId, this.departmentId});

  @override
  List<Object?> get props => [startDate, endDate, costCenterId, departmentId];
}

class GetBudgetVarianceReportParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? budgetId;
  final String? costCenterId;

  const GetBudgetVarianceReportParams(
      {required this.startDate, required this.endDate, this.budgetId, this.costCenterId});

  @override
  List<Object?> get props => [startDate, endDate, budgetId, costCenterId];
}

class GetFinancialSummaryParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;

  const GetFinancialSummaryParams(
      {required this.startDate, required this.endDate, this.costCenterId});

  @override
  List<Object?> get props => [startDate, endDate, costCenterId];
}

class GetCostTrendsParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final CostCategory? category;
  final String? costCenterId;

  const GetCostTrendsParams(
      {required this.startDate, required this.endDate, this.category, this.costCenterId});

  @override
  List<Object?> get props => [startDate, endDate, category, costCenterId];
}

class SearchCostCentersParams extends Equatable {
  final String query;
  final CostCenterFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchCostCentersParams({required this.query, this.filter, this.pagination});

  @override
  List<Object?> get props => [query, filter, pagination];
}

class SearchTransactionsParams extends Equatable {
  final String query;
  final TransactionFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchTransactionsParams({required this.query, this.filter, this.pagination});

  @override
  List<Object?> get props => [query, filter, pagination];
}

class SearchBudgetsParams extends Equatable {
  final String query;
  final BudgetFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchBudgetsParams({required this.query, this.filter, this.pagination});

  @override
  List<Object?> get props => [query, filter, pagination];
}
