import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/bundle.dart';

part 'bundle_model.g.dart';

/// Data model for Bundle entity
@JsonSerializable()
class BundleModel {
  final String id;
  
  @Json<PERSON>ey(name: 'bundle_no')
  final String bundleNo;
  
  @Json<PERSON><PERSON>(name: 'design_no')
  final String designNo;
  
  @Json<PERSON>ey(name: 'lot_no')
  final String lotNo;
  
  @<PERSON><PERSON><PERSON>ey(name: 'roll_no')
  final String rollNo;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'alt_no')
  final String altNo;
  
  @J<PERSON><PERSON><PERSON>(name: 'size', fromJson: _sizeFromJson, toJson: _sizeToJson)
  final GarmentSize size;
  
  @Json<PERSON><PERSON>(name: 'piece_start_no')
  final int pieceStartNo;
  
  @Json<PERSON>ey(name: 'piece_end_no')
  final int pieceEndNo;
  
  @<PERSON>son<PERSON>ey(name: 'total_pieces')
  final int totalPieces;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
  final BundleStatus status;
  
  @JsonKey(name: 'supervisor_id')
  final String? supervisorId;
  
  @JsonKey(name: 'assigned_to')
  final String? assignedTo;
  
  @JsonKey(name: 'issued_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime? issuedAt;
  
  @JsonKey(name: 'started_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime? startedAt;
  
  @JsonKey(name: 'completed_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime? completedAt;
  
  final String? remarks;
  final String? color;
  
  @JsonKey(name: 'order_no')
  final String? orderNo;
  
  @JsonKey(name: 'client_name')
  final String? clientName;
  
  @JsonKey(name: 'created_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime createdAt;
  
  @JsonKey(name: 'updated_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime updatedAt;
  
  @JsonKey(name: 'deleted_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime? deletedAt;
  
  @JsonKey(name: 'created_by')
  final String? createdBy;
  
  @JsonKey(name: 'updated_by')
  final String? updatedBy;
  
  @JsonKey(name: 'deleted_by')
  final String? deletedBy;
  
  final int version;

  const BundleModel({
    required this.id,
    required this.bundleNo,
    required this.designNo,
    required this.lotNo,
    required this.rollNo,
    required this.altNo,
    required this.size,
    required this.pieceStartNo,
    required this.pieceEndNo,
    required this.totalPieces,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.supervisorId,
    this.assignedTo,
    this.issuedAt,
    this.startedAt,
    this.completedAt,
    this.remarks,
    this.color,
    this.orderNo,
    this.clientName,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.deletedBy,
    this.version = 1,
  });

  /// Convert to domain entity
  Bundle toEntity() {
    return Bundle(
      id: id,
      bundleNo: bundleNo,
      designNo: designNo,
      lotNo: lotNo,
      rollNo: rollNo,
      altNo: altNo,
      size: size,
      pieceStartNo: pieceStartNo,
      pieceEndNo: pieceEndNo,
      totalPieces: totalPieces,
      status: status,
      supervisorId: supervisorId,
      assignedTo: assignedTo,
      issuedAt: issuedAt,
      startedAt: startedAt,
      completedAt: completedAt,
      remarks: remarks,
      color: color,
      orderNo: orderNo,
      clientName: clientName,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy,
      deletedBy: deletedBy,
      version: version,
    );
  }

  /// Create from domain entity
  factory BundleModel.fromEntity(Bundle bundle) {
    return BundleModel(
      id: bundle.id,
      bundleNo: bundle.bundleNo,
      designNo: bundle.designNo,
      lotNo: bundle.lotNo,
      rollNo: bundle.rollNo,
      altNo: bundle.altNo,
      size: bundle.size,
      pieceStartNo: bundle.pieceStartNo,
      pieceEndNo: bundle.pieceEndNo,
      totalPieces: bundle.totalPieces,
      status: bundle.status,
      supervisorId: bundle.supervisorId,
      assignedTo: bundle.assignedTo,
      issuedAt: bundle.issuedAt,
      startedAt: bundle.startedAt,
      completedAt: bundle.completedAt,
      remarks: bundle.remarks,
      color: bundle.color,
      orderNo: bundle.orderNo,
      clientName: bundle.clientName,
      createdAt: bundle.createdAt,
      updatedAt: bundle.updatedAt,
      deletedAt: bundle.deletedAt,
      createdBy: bundle.createdBy,
      updatedBy: bundle.updatedBy,
      deletedBy: bundle.deletedBy,
      version: bundle.version,
    );
  }

  /// Create from JSON
  factory BundleModel.fromJson(Map<String, dynamic> json) =>
      _$BundleModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$BundleModelToJson(this);

  /// Create from Firestore document
  factory BundleModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = doc.id;
    return BundleModel.fromJson(data);
  }

  /// Convert to Firestore data
  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id'); // Remove ID as it's handled by Firestore
    return json;
  }

  /// Create a copy with updated fields
  BundleModel copyWith({
    String? id,
    String? bundleNo,
    String? designNo,
    String? lotNo,
    String? rollNo,
    String? altNo,
    GarmentSize? size,
    int? pieceStartNo,
    int? pieceEndNo,
    int? totalPieces,
    BundleStatus? status,
    String? supervisorId,
    String? assignedTo,
    DateTime? issuedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? remarks,
    String? color,
    String? orderNo,
    String? clientName,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    String? deletedBy,
    int? version,
  }) {
    return BundleModel(
      id: id ?? this.id,
      bundleNo: bundleNo ?? this.bundleNo,
      designNo: designNo ?? this.designNo,
      lotNo: lotNo ?? this.lotNo,
      rollNo: rollNo ?? this.rollNo,
      altNo: altNo ?? this.altNo,
      size: size ?? this.size,
      pieceStartNo: pieceStartNo ?? this.pieceStartNo,
      pieceEndNo: pieceEndNo ?? this.pieceEndNo,
      totalPieces: totalPieces ?? this.totalPieces,
      status: status ?? this.status,
      supervisorId: supervisorId ?? this.supervisorId,
      assignedTo: assignedTo ?? this.assignedTo,
      issuedAt: issuedAt ?? this.issuedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      remarks: remarks ?? this.remarks,
      color: color ?? this.color,
      orderNo: orderNo ?? this.orderNo,
      clientName: clientName ?? this.clientName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy ?? this.deletedBy,
      version: version ?? this.version,
    );
  }
}

// Helper functions for JSON serialization
static GarmentSize _sizeFromJson(String value) => GarmentSize.fromString(value);
static String _sizeToJson(GarmentSize size) => size.value;

static BundleStatus _statusFromJson(String value) => BundleStatus.fromString(value);
static String _statusToJson(BundleStatus status) => status.value;

static DateTime? _dateTimeFromJson(dynamic value) {
  if (value == null) return null;
  if (value is Timestamp) return value.toDate();
  if (value is String) return DateTime.parse(value);
  return null;
}

static dynamic _dateTimeToJson(DateTime? dateTime) {
  if (dateTime == null) return null;
  return Timestamp.fromDate(dateTime);
}
