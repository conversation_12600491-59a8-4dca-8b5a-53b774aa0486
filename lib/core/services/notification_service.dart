import 'package:flutter/services.dart';

class NotificationService {
  static const MethodChannel _channel = 
      MethodChannel('com.example.hm_collection/notifications');

  /// Request notification permission from the user
  /// Returns 'granted' if permission is already granted
  /// Returns 'requested' if permission was requested
  /// Returns 'denied' if permission was denied
  static Future<String> requestNotificationPermission() async {
    try {
      final String result = await _channel.invokeMethod('requestNotificationPermission');
      return result;
    } on PlatformException catch (e) {
      print("Failed to request notification permission: ${e.message}");
      return "error";
    }
  }

  /// Check if notification permission is granted
  static Future<bool> isNotificationPermissionGranted() async {
    try {
      final String result = await _channel.invokeMethod('requestNotificationPermission');
      return result == 'granted';
    } on PlatformException {
      return false;
    }
  }
}
