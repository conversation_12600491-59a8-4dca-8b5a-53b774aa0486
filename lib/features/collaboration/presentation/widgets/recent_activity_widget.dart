import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/entities/collaboration_entities.dart';

/// Recent activity widget
class RecentActivityWidget extends StatelessWidget {
  final List<RealTimeActivity>? activities;
  final Function(RealTimeActivity)? onActivityTap;

  const RecentActivityWidget({
    super.key,
    this.activities,
    this.onActivityTap,
  });

  @override
  Widget build(BuildContext context) {
    // Mock activities if none provided
    final activityList = activities ?? _getMockActivities();

    if (activityList.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: activityList
          .take(5)
          .map((activity) => _buildActivityItem(activity))
          .toList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.history,
            size: 48,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 8),
          Text(
            'No recent activity',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(RealTimeActivity activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => onActivityTap?.call(activity),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getActivityTypeColor(activity.type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  _getActivityTypeIcon(activity.type),
                  color: _getActivityTypeColor(activity.type),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      activity.description,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Text(
                          activity.userName,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          ' • ${_formatTime(activity.createdAt)}',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (activity.priority == ActivityPriority.high ||
                  activity.priority == ActivityPriority.urgent)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getPriorityColor(activity.priority).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    activity.priority.displayName.toUpperCase(),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: _getPriorityColor(activity.priority),
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getActivityTypeColor(ActivityType type) {
    switch (type) {
      case ActivityType.userJoined:
        return AppColors.success;
      case ActivityType.userLeft:
        return AppColors.warning;
      case ActivityType.messagePosted:
        return AppColors.primary;
      case ActivityType.fileShared:
        return Colors.purple;
      case ActivityType.channelCreated:
        return AppColors.success;
      case ActivityType.channelUpdated:
        return AppColors.warning;
      case ActivityType.workspaceUpdated:
        return AppColors.primary;
      case ActivityType.sessionStarted:
        return AppColors.success;
      case ActivityType.sessionEnded:
        return AppColors.error;
      case ActivityType.taskAssigned:
        return Colors.orange;
      case ActivityType.taskCompleted:
        return AppColors.success;
      case ActivityType.systemUpdate:
        return AppColors.textSecondary;
    }
  }

  IconData _getActivityTypeIcon(ActivityType type) {
    switch (type) {
      case ActivityType.userJoined:
        return Icons.person_add;
      case ActivityType.userLeft:
        return Icons.person_remove;
      case ActivityType.messagePosted:
        return Icons.chat;
      case ActivityType.fileShared:
        return Icons.attach_file;
      case ActivityType.channelCreated:
        return Icons.add_circle;
      case ActivityType.channelUpdated:
        return Icons.edit;
      case ActivityType.workspaceUpdated:
        return Icons.update;
      case ActivityType.sessionStarted:
        return Icons.play_circle;
      case ActivityType.sessionEnded:
        return Icons.stop_circle;
      case ActivityType.taskAssigned:
        return Icons.assignment;
      case ActivityType.taskCompleted:
        return Icons.check_circle;
      case ActivityType.systemUpdate:
        return Icons.system_update;
    }
  }

  Color _getPriorityColor(ActivityPriority priority) {
    switch (priority) {
      case ActivityPriority.low:
        return AppColors.textSecondary;
      case ActivityPriority.normal:
        return AppColors.primary;
      case ActivityPriority.high:
        return AppColors.warning;
      case ActivityPriority.urgent:
        return AppColors.error;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  List<RealTimeActivity> _getMockActivities() {
    return [
      RealTimeActivity(
        id: 'activity_1',
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 5)),
        activityCode: 'ACT-001',
        workspaceId: 'workspace_1',
        channelId: 'channel_1',
        userId: 'user_002',
        userName: 'Sarah Johnson',
        type: ActivityType.messagePosted,
        description: 'Posted a message in #general',
        priority: ActivityPriority.normal,
      ),
      RealTimeActivity(
        id: 'activity_2',
        createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 15)),
        activityCode: 'ACT-002',
        workspaceId: 'workspace_1',
        userId: 'user_003',
        userName: 'Mike Wilson',
        type: ActivityType.fileShared,
        description: 'Shared production report in #production-updates',
        priority: ActivityPriority.high,
      ),
      RealTimeActivity(
        id: 'activity_3',
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        activityCode: 'ACT-003',
        workspaceId: 'workspace_1',
        userId: 'user_001',
        userName: 'John Smith',
        type: ActivityType.sessionStarted,
        description: 'Started daily standup meeting',
        priority: ActivityPriority.normal,
      ),
      RealTimeActivity(
        id: 'activity_4',
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        activityCode: 'ACT-004',
        workspaceId: 'workspace_2',
        userId: 'user_004',
        userName: 'Lisa Chen',
        type: ActivityType.channelCreated,
        description: 'Created #quality-alerts channel',
        priority: ActivityPriority.normal,
      ),
      RealTimeActivity(
        id: 'activity_5',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        activityCode: 'ACT-005',
        workspaceId: 'workspace_1',
        userId: 'user_005',
        userName: 'David Brown',
        type: ActivityType.userJoined,
        description: 'Joined Manufacturing Operations workspace',
        priority: ActivityPriority.normal,
      ),
    ];
  }
}

