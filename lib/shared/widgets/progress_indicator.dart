import 'package:flutter/material.dart';

/// A custom progress indicator with label and percentage
class CustomProgressIndicator extends StatelessWidget {
  final double progress;
  final String? label;
  final Color? color;
  final Color? backgroundColor;
  final double height;
  final bool showPercentage;
  final TextStyle? labelStyle;
  final TextStyle? percentageStyle;

  const CustomProgressIndicator({
    Key? key,
    required this.progress,
    this.label,
    this.color,
    this.backgroundColor,
    this.height = 8.0,
    this.showPercentage = true,
    this.labelStyle,
    this.percentageStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.primaryColor;
    final bgColor = backgroundColor ?? Colors.grey[300];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null || showPercentage)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (label != null)
                Text(
                  label!,
                  style: labelStyle ?? theme.textTheme.bodyMedium,
                ),
              if (showPercentage)
                Text(
                  '${(progress * 100).toStringAsFixed(1)}%',
                  style: percentageStyle ?? theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
        if (label != null || showPercentage) const SizedBox(height: 4),
        Container(
          height: height,
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(height / 2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress.clamp(0.0, 1.0),
            child: Container(
              decoration: BoxDecoration(
                color: progressColor,
                borderRadius: BorderRadius.circular(height / 2),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// A circular progress indicator with percentage in the center
class CircularProgressWithPercentage extends StatelessWidget {
  final double progress;
  final double size;
  final double strokeWidth;
  final Color? color;
  final Color? backgroundColor;
  final TextStyle? textStyle;

  const CircularProgressWithPercentage({
    Key? key,
    required this.progress,
    this.size = 100.0,
    this.strokeWidth = 8.0,
    this.color,
    this.backgroundColor,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.primaryColor;
    final bgColor = backgroundColor ?? Colors.grey[300];

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              value: progress,
              strokeWidth: strokeWidth,
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              backgroundColor: bgColor,
            ),
          ),
          Positioned.fill(
            child: Center(
              child: Text(
                '${(progress * 100).toStringAsFixed(0)}%',
                style: textStyle ?? theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A stepped progress indicator
class SteppedProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final Color? activeColor;
  final Color? inactiveColor;
  final double stepSize;
  final List<String>? stepLabels;

  const SteppedProgressIndicator({
    Key? key,
    required this.currentStep,
    required this.totalSteps,
    this.activeColor,
    this.inactiveColor,
    this.stepSize = 24.0,
    this.stepLabels,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final activeStepColor = activeColor ?? theme.primaryColor;
    final inactiveStepColor = inactiveColor ?? Colors.grey[300];

    return Column(
      children: [
        Row(
          children: List.generate(totalSteps, (index) {
            final isActive = index < currentStep;
            final isLast = index == totalSteps - 1;

            return Expanded(
              child: Row(
                children: [
                  Container(
                    width: stepSize,
                    height: stepSize,
                    decoration: BoxDecoration(
                      color: isActive ? activeStepColor : inactiveStepColor,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: isActive ? Colors.white : Colors.grey[600],
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  if (!isLast)
                    Expanded(
                      child: Container(
                        height: 2,
                        color: isActive ? activeStepColor : inactiveStepColor,
                      ),
                    ),
                ],
              ),
            );
          }),
        ),
        if (stepLabels != null) ...[
          const SizedBox(height: 8),
          Row(
            children: List.generate(totalSteps, (index) {
              final isActive = index < currentStep;
              return Expanded(
                child: Text(
                  stepLabels![index],
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isActive ? activeStepColor : Colors.grey[600],
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }),
          ),
        ],
      ],
    );
  }
}

/// A multi-colored progress bar
class MultiColorProgressBar extends StatelessWidget {
  final List<ProgressSegment> segments;
  final double height;
  final BorderRadius? borderRadius;

  const MultiColorProgressBar({
    Key? key,
    required this.segments,
    this.height = 8.0,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final radius = borderRadius ?? BorderRadius.circular(height / 2);

    return Container(
      height: height,
      decoration: BoxDecoration(
        borderRadius: radius,
        color: Colors.grey[300],
      ),
      child: ClipRRect(
        borderRadius: radius,
        child: Row(
          children: segments.map((segment) {
            return Expanded(
              flex: (segment.value * 100).round(),
              child: Container(
                color: segment.color,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

/// A segment for the multi-colored progress bar
class ProgressSegment {
  final double value;
  final Color color;
  final String? label;

  const ProgressSegment({
    required this.value,
    required this.color,
    this.label,
  });
}