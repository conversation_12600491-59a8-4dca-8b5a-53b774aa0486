import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/auth/widgets/permission_guard.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../domain/entities/analytics_entities.dart';
import '../bloc/analytics_bloc.dart';
import '../widgets/analytics_kpi_cards.dart';
import '../widgets/dashboard_grid_widget.dart';

/// Analytics dashboard page
class AnalyticsDashboardPage extends StatefulWidget {
  const AnalyticsDashboardPage({super.key});

  @override
  State<AnalyticsDashboardPage> createState() => _AnalyticsDashboardPageState();
}

class _AnalyticsDashboardPageState extends State<AnalyticsDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late AnalyticsBloc _analyticsBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _analyticsBloc = GetIt.instance<AnalyticsBloc>();
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _analyticsBloc.close();
    super.dispose();
  }

  void _loadInitialData() {
    // Load dashboards
    _analyticsBloc.add(const GetDashboardsRequested());

    // Load KPI data
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    _analyticsBloc.add(GetKpiDataRequested(
      metrics: [
        'production_volume',
        'quality_score',
        'efficiency_rate',
        'revenue',
        'profit_margin',
        'cost_per_unit',
      ],
      startDate: startOfMonth,
      endDate: endOfMonth,
    ));

    // Load real-time metrics
    _analyticsBloc.add(const GetRealTimeMetricsRequested(
      metrics: [
        'production_volume',
        'quality_score',
        'efficiency_rate',
        'machine_utilization',
      ],
    ));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _analyticsBloc,
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'Analytics & Reporting',
          actions: [
            PermissionGuard(
              permission: 'analytics.dashboards.create',
              child: IconButton(
                icon: const Icon(Icons.dashboard_customize),
                onPressed: () => _navigateToCreateDashboard(),
                tooltip: 'Create Dashboard',
              ),
            ),
            PermissionGuard(
              permission: 'analytics.reports.generate',
              child: IconButton(
                icon: const Icon(Icons.assessment),
                onPressed: () => _navigateToGenerateReport(),
                tooltip: 'Generate Report',
              ),
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => _refreshData(),
              tooltip: 'Refresh Data',
            ),
          ],
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildDashboardsTab(),
                  _buildReportsTab(),
                  _buildAnalyticsTab(),
                  _buildRealTimeTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: PermissionGuard(
          permission: 'analytics.dashboards.create',
          child: FloatingActionButton(
            onPressed: () => _navigateToCreateDashboard(),
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        isScrollable: true,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.view_quilt),
            text: 'Dashboards',
          ),
          Tab(
            icon: Icon(Icons.assessment),
            text: 'Reports',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'Analytics',
          ),
          Tab(
            icon: Icon(Icons.speed),
            text: 'Real-time',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
     // padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analytics Overview',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // KPI Cards
          BlocBuilder<AnalyticsBloc, AnalyticsState>(
            builder: (context, state) {
              if (state is AnalyticsKpiDataLoaded) {
                return AnalyticsKpiCards(data: state.data);
              } else if (state is AnalyticsLoading) {
                return const LoadingWidget();
              }
              return const SizedBox.shrink();
            },
          ),
          
          const SizedBox(height: 24),
          
          // Quick Analytics
          Row(
            children: [
              Expanded(
                child: _buildQuickAnalyticsCard(
                  'Production Trends',
                  Icons.trending_up,
                  AppColors.primary,
                  () => _loadProductionTrends(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickAnalyticsCard(
                  'Quality Analysis',
                  Icons.verified,
                  AppColors.success,
                  () => _loadQualityAnalysis(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickAnalyticsCard(
                  'Financial Metrics',
                  Icons.attach_money,
                  AppColors.warning,
                  () => _loadFinancialMetrics(),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Recent Activity
          _buildRecentActivity(),
          
          const SizedBox(height: 24),
          
          // Performance Summary
          _buildPerformanceSummary(),
        ],
      ),
    );
  }

  Widget _buildDashboardsTab() {
    return PermissionGuard(
      permission: 'analytics.dashboards.read',
      fallback: const Center(
        child: Text('You do not have permission to view dashboards'),
      ),
      child: Container(
        //padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Analytics Dashboards',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: () => _showDashboardSearch(),
                      tooltip: 'Search Dashboards',
                    ),
                    PermissionGuard(
                      permission: 'analytics.dashboards.create',
                      child: ElevatedButton.icon(
                        onPressed: () => _navigateToCreateDashboard(),
                        icon: const Icon(Icons.add),
                        label: const Text('Create Dashboard'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: BlocBuilder<AnalyticsBloc, AnalyticsState>(
                builder: (context, state) {
                  if (state is AnalyticsDashboardsLoaded) {
                    return DashboardGridWidget(
                      dashboards: state.dashboards,
                      onDashboardTap: (dashboard) => _navigateToDashboard(dashboard.id),
                      onDashboardEdit: (dashboard) => _editDashboard(dashboard.id),
                      onDashboardDelete: (dashboard) => _deleteDashboard(dashboard.id),
                      onDashboardClone: (dashboard) => _cloneDashboard(dashboard.id),
                      onDashboardShare: (dashboard) => _shareDashboard(dashboard.id),
                    );
                  } else if (state is AnalyticsLoading) {
                    return const LoadingWidget();
                  } else if (state is AnalyticsError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: AppColors.error,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading dashboards',
                            style: AppTextStyles.bodyLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => _loadInitialData(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }
                  return const Center(
                    child: Text('No dashboards available'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab() {
    return PermissionGuard(
      permission: 'analytics.reports.read',
      fallback: const Center(
        child: Text('You do not have permission to view reports'),
      ),
      child: Container(
       // padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Analytics Reports',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: () => _showReportSearch(),
                      tooltip: 'Search Reports',
                    ),
                    PermissionGuard(
                      permission: 'analytics.reports.generate',
                      child: ElevatedButton.icon(
                        onPressed: () => _navigateToGenerateReport(),
                        icon: const Icon(Icons.add),
                        label: const Text('Generate Report'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Quick Report Actions
            _buildQuickReportActions(),

            const SizedBox(height: 24),

            Expanded(
              child: BlocBuilder<AnalyticsBloc, AnalyticsState>(
                builder: (context, state) {
                  if (state is AnalyticsReportsLoaded) {
                    return _buildReportsList(state);
                  } else if (state is AnalyticsLoading) {
                    return const LoadingWidget();
                  }
                  return const Center(
                    child: Text('No reports available'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return PermissionGuard(
      permission: 'analytics.data.read',
      fallback: const Center(
        child: Text('You do not have permission to view analytics'),
      ),
      child: Container(
      //  padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Advanced Analytics',
              style: AppTextStyles.headlineMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Analytics Tools
            Row(
              children: [
                Expanded(
                  child: _buildAnalyticsToolCard(
                    'Trend Analysis',
                    Icons.show_chart,
                    'Analyze trends over time',
                    () => _openTrendAnalysis(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildAnalyticsToolCard(
                    'Comparative Analysis',
                    Icons.compare,
                    'Compare periods and metrics',
                    () => _openComparativeAnalysis(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildAnalyticsToolCard(
                    'Forecasting',
                    Icons.timeline,
                    'Predict future trends',
                    () => _openForecasting(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildAnalyticsToolCard(
                    'Anomaly Detection',
                    Icons.warning,
                    'Detect unusual patterns',
                    () => _openAnomalyDetection(),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            Expanded(
              child: _buildAnalyticsContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRealTimeTab() {
    return PermissionGuard(
      permission: 'analytics.realtime.read',
      fallback: const Center(
        child: Text('You do not have permission to view real-time metrics'),
      ),
      child: Container(
        //padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Real-time Metrics',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    Container(
                    //  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.success.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: AppColors.success,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Live',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.success,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: () => _refreshRealTimeData(),
                      tooltip: 'Refresh',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: BlocBuilder<AnalyticsBloc, AnalyticsState>(
                builder: (context, state) {
                  if (state is AnalyticsRealTimeMetricsLoaded) {
                    return RealTimeMetricsWidget(
                      metrics: state.data.metrics,
                      onMetricTap: (metric) => _showMetricDetails(metric),
                    );
                  } else if (state is AnalyticsLoading) {
                    return const LoadingWidget();
                  }
                  return const Center(
                    child: Text('No real-time data available'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAnalyticsCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Mock recent activities
            _buildActivityItem(
              'Production Report Generated',
              'Monthly production analysis completed',
              Icons.assessment,
              AppColors.primary,
              '2 hours ago',
            ),
            const SizedBox(height: 12),
            _buildActivityItem(
              'Quality Dashboard Updated',
              'New quality metrics added to dashboard',
              Icons.dashboard,
              AppColors.success,
              '4 hours ago',
            ),
            const SizedBox(height: 12),
            _buildActivityItem(
              'Anomaly Detected',
              'Unusual pattern in production line 3',
              Icons.warning,
              AppColors.warning,
              '6 hours ago',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String description,
    IconData icon,
    Color color,
    String time,
  ) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Text(
          time,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Summary',
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryMetric(
                    'Overall Efficiency',
                    '87.5%',
                    '+2.3%',
                    true,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: _buildSummaryMetric(
                    'Quality Score',
                    '94.2%',
                    '+1.8%',
                    true,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: _buildSummaryMetric(
                    'Cost per Unit',
                    '\$12.45',
                    '-3.2%',
                    true,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: _buildSummaryMetric(
                    'On-time Delivery',
                    '96.8%',
                    '-0.5%',
                    false,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryMetric(
    String label,
    String value,
    String change,
    bool isPositive,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(
              isPositive ? Icons.arrow_upward : Icons.arrow_downward,
              size: 16,
              color: isPositive ? AppColors.success : AppColors.error,
            ),
            const SizedBox(width: 4),
            Text(
              change,
              style: AppTextStyles.bodySmall.copyWith(
                color: isPositive ? AppColors.success : AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickReportActions() {
    return Row(
      children: [
        Expanded(
          child: _buildQuickReportCard(
            'Production Report',
            Icons.precision_manufacturing,
            () => _generateProductionReport(),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickReportCard(
            'Quality Report',
            Icons.verified,
            () => _generateQualityReport(),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickReportCard(
            'Financial Report',
            Icons.attach_money,
            () => _generateFinancialReport(),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickReportCard(
            'Efficiency Report',
            Icons.speed,
            () => _generateEfficiencyReport(),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickReportCard(String title, IconData icon, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: AppColors.primary,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReportsList(AnalyticsReportsLoaded state) {
    return ListView.builder(
      itemCount: state.reports.length,
      itemBuilder: (context, index) {
        final report = state.reports[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primary.withOpacity(0.1),
              child: Icon(
                Icons.assessment,
                color: AppColors.primary,
              ),
            ),
            title: Text(
              report.reportName,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              '${report.type.displayName} • ${report.category.displayName}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getReportStatusColor(report.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                report.status.displayName,
                style: AppTextStyles.bodySmall.copyWith(
                  color: _getReportStatusColor(report.status),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            onTap: () => _navigateToReportDetails(report.id),
          ),
        );
      },
    );
  }

  Widget _buildAnalyticsToolCard(
    String title,
    IconData icon,
    String description,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: AppColors.primary,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalyticsContent() {
    return const Center(
      child: Text('Select an analytics tool to get started'),
    );
  }

  Color _getReportStatusColor(ReportStatus status) {
    return switch (status) {
      ReportStatus.completed => AppColors.success,
      ReportStatus.generating => AppColors.warning,
      ReportStatus.failed || ReportStatus.cancelled => AppColors.error,
      ReportStatus.pending || ReportStatus.scheduled => AppColors.textSecondary,
    };
  }

  void _refreshData() {
    _loadInitialData();
  }

  void _refreshRealTimeData() {
    _analyticsBloc.add(const GetRealTimeMetricsRequested(
      metrics: [
        'production_volume',
        'quality_score',
        'efficiency_rate',
        'machine_utilization',
      ],
    ));
  }

  void _loadProductionTrends() {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month - 3, 1);
    
    _analyticsBloc.add(GetProductionMetricsRequested(
      startDate: startDate,
      endDate: now,
    ));
  }

  void _loadQualityAnalysis() {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month - 1, 1);
    
    _analyticsBloc.add(GetQualityMetricsRequested(
      startDate: startDate,
      endDate: now,
    ));
  }

  void _loadFinancialMetrics() {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, 1);
    
    _analyticsBloc.add(GetFinancialMetricsRequested(
      startDate: startDate,
      endDate: now,
    ));
  }

  void _navigateToCreateDashboard() {
    // Navigate to create dashboard page
  }

  void _navigateToGenerateReport() {
    // Navigate to generate report page
  }

  void _navigateToDashboard(String dashboardId) {
    // Navigate to dashboard details page
  }

  void _navigateToReportDetails(String reportId) {
    // Navigate to report details page
  }

  void _editDashboard(String dashboardId) {
    // Navigate to edit dashboard page
  }

  void _deleteDashboard(String dashboardId) {
    // Show delete confirmation dialog
  }

  void _cloneDashboard(String dashboardId) {
    // Show clone dashboard dialog
  }

  void _shareDashboard(String dashboardId) {
    // Show share dashboard dialog
  }

  void _showDashboardSearch() {
    // Show dashboard search dialog
  }

  void _showReportSearch() {
    // Show report search dialog
  }

  void _generateProductionReport() {
    // Generate production report
  }

  void _generateQualityReport() {
    // Generate quality report
  }

  void _generateFinancialReport() {
    // Generate financial report
  }

  void _generateEfficiencyReport() {
    // Generate efficiency report
  }

  void _openTrendAnalysis() {
    // Open trend analysis tool
  }

  void _openComparativeAnalysis() {
    // Open comparative analysis tool
  }

  void _openForecasting() {
    // Open forecasting tool
  }

  void _openAnomalyDetection() {
    // Open anomaly detection tool
  }

  void _showMetricDetails(String metric) {
    // Show metric details dialog
  }
}
