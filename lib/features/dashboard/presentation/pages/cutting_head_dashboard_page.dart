import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Cutting Head Dashboard Page - Cutting department management
class CuttingHeadDashboardPage extends StatelessWidget {
  const CuttingHeadDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cutting Head Dashboard'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildCuttingHeadDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildCuttingHeadDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildCuttingOverview(),
          const SizedBox(height: 24),
          _buildCuttingActions(context),
          const SizedBox(height: 24),
          _buildTeamPerformance(),
          const SizedBox(height: 24),
          _buildActiveTasks(),
          const SizedBox(height: 24),
          _buildMachineStatus(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple,
            Colors.purple.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Cutting Head'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Cutting department management',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.content_cut,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildCuttingOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cutting Department Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: const [
            DashboardMetricCard(
              title: 'Active Tasks',
              value: '12',
              icon: Icons.assignment,
              color: Colors.blue,
              trend: '+3',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Team Members',
              value: '8',
              icon: Icons.people,
              color: Colors.green,
            ),
            DashboardMetricCard(
              title: 'Completed Today',
              value: '15',
              icon: Icons.check_circle,
              color: Colors.purple,
              trend: '+25%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Efficiency',
              value: '92%',
              icon: Icons.trending_up,
              color: Colors.orange,
              trend: '+5%',
              isPositiveTrend: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCuttingActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Department Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Assign Tasks',
              subtitle: 'Assign cutting tasks to team',
              icon: Icons.assignment_ind,
              color: Colors.blue,
              onTap: () {
                // Navigate to task assignment
              },
            ),
            DashboardCard(
              title: 'Team Management',
              subtitle: 'Manage cutting team',
              icon: Icons.people,
              color: Colors.green,
              onTap: () {
                // Navigate to team management
              },
            ),
            DashboardCard(
              title: 'Production Planning',
              subtitle: 'Plan cutting schedules',
              icon: Icons.calendar_today,
              color: Colors.purple,
              onTap: () {
                // Navigate to production planning
              },
            ),
            DashboardCard(
              title: 'Quality Control',
              subtitle: 'Monitor cutting quality',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () {
                // Navigate to quality control
              },
            ),
            DashboardCard(
              title: 'Material Requests',
              subtitle: 'Request materials',
              icon: Icons.inventory,
              color: Colors.teal,
              onTap: () {
                // Navigate to material requests
              },
            ),
            DashboardCard(
              title: 'Reports',
              subtitle: 'View department reports',
              icon: Icons.analytics,
              color: Colors.indigo,
              onTap: () {
                // Navigate to reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTeamPerformance() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.people,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Team Performance',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to detailed performance
                  },
                  child: const Text('View Details'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPerformanceItem('John Smith (Master)', 0.95, Colors.green),
            const SizedBox(height: 8),
            _buildPerformanceItem('Sarah Johnson (Master)', 0.88, Colors.blue),
            const SizedBox(height: 8),
            _buildPerformanceItem('Mike Wilson (Helper)', 0.82, Colors.orange),
            const SizedBox(height: 8),
            _buildPerformanceItem('Lisa Brown (Helper)', 0.76, Colors.purple),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceItem(String name, double performance, Color color) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            name,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 2,
          child: LinearProgressIndicator(
            value: performance,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${(performance * 100).toInt()}%',
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildActiveTasks() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.assignment,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Active Cutting Tasks',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to all tasks
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTaskItem(
              'CUT-001',
              'Order #1234 - Shirt cutting',
              'John Smith',
              'In Progress',
              Colors.blue,
            ),
            _buildTaskItem(
              'CUT-002',
              'Order #1235 - Trouser cutting',
              'Sarah Johnson',
              'Pending',
              Colors.orange,
            ),
            _buildTaskItem(
              'CUT-003',
              'Order #1236 - Dress cutting',
              'Mike Wilson',
              'Completed',
              Colors.green,
            ),
            _buildTaskItem(
              'CUT-004',
              'Order #1237 - Jacket cutting',
              'Lisa Brown',
              'In Progress',
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskItem(String taskId, String description, String assignee, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  taskId,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
                Text(
                  'Assigned to: $assignee',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMachineStatus() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.precision_manufacturing,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Cutting Machine Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '4/5 Active',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMachineCard('Machine 1', 'Active', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMachineCard('Machine 2', 'Active', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMachineCard('Machine 3', 'Maintenance', Colors.orange),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMachineCard('Machine 4', 'Active', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMachineCard('Machine 5', 'Active', Colors.green),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMachineCard(String machine, String status, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.precision_manufacturing,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            machine,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            status,
            style: TextStyle(
              fontSize: 9,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
