import 'package:equatable/equatable.dart';

// Params classes
class GetMachinesParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetMachinesParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class UpdateMachineStatusParams extends Equatable {
  final String machineId;
  final dynamic status;
  final String? reason;
  const UpdateMachineStatusParams(this.machineId, this.status, this.reason);
  @override
  List<Object?> get props => [machineId, status, reason];
}

class GetMachineSchedulesParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetMachineSchedulesParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class GetMachineMaintenanceRecordsParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetMachineMaintenanceRecordsParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class CompleteMaintenanceParams extends Equatable {
  final String maintenanceId;
  final dynamic request;
  const CompleteMaintenanceParams(this.maintenanceId, this.request);
  @override
  List<Object?> get props => [maintenanceId, request];
}

class GetAvailableMachinesParams extends Equatable {
  final DateTime startTime;
  final DateTime endTime;
  final dynamic pagination;
  const GetAvailableMachinesParams(
      {required this.startTime, required this.endTime, this.pagination});
  @override
  List<Object?> get props => [startTime, endTime, pagination];
}

class GetMachinesDueForMaintenanceParams extends Equatable {
  final int daysAhead;
  final dynamic pagination;
  const GetMachinesDueForMaintenanceParams({required this.daysAhead, this.pagination});
  @override
  List<Object?> get props => [daysAhead, pagination];
}

class GetMachineUtilizationReportParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  const GetMachineUtilizationReportParams(
      {required this.startDate, required this.endDate, this.departmentId});
  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

class SearchMachinesParams extends Equatable {
  final String query;
  final dynamic filter;
  final dynamic pagination;
  const SearchMachinesParams(this.query, {this.filter, this.pagination});
  @override
  List<Object?> get props => [query, filter, pagination];
}

class GetWorkersParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetWorkersParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class UpdateWorkerStatusParams extends Equatable {
  final String workerId;
  final dynamic status;
  final String? reason;
  const UpdateWorkerStatusParams(this.workerId, this.status, this.reason);
  @override
  List<Object?> get props => [workerId, status, reason];
}

class GetWorkerSchedulesParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetWorkerSchedulesParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class GetWorkerAttendanceParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetWorkerAttendanceParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class GetWorkerLeaveRequestsParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetWorkerLeaveRequestsParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class ApproveLeaveRequestParams extends Equatable {
  final String leaveRequestId;
  final String approverId;
  const ApproveLeaveRequestParams({required this.leaveRequestId, required this.approverId});
  @override
  List<Object?> get props => [leaveRequestId, approverId];
}

class GetAvailableWorkersParams extends Equatable {
  final DateTime startTime;
  final DateTime endTime;
  final dynamic pagination;
  const GetAvailableWorkersParams(
      {required this.startTime, required this.endTime, this.pagination});
  @override
  List<Object?> get props => [startTime, endTime, pagination];
}

class GetWorkerPerformanceReportParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  const GetWorkerPerformanceReportParams(
      {required this.startDate, required this.endDate, this.departmentId});
  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

class SearchWorkersParams extends Equatable {
  final String query;
  final dynamic filter;
  final dynamic pagination;
  const SearchWorkersParams(this.query, {this.filter, this.pagination});
  @override
  List<Object?> get props => [query, filter, pagination];
}

class GetFacilitiesParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetFacilitiesParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class GetDepartmentsParams extends Equatable {
  final dynamic filter;
  final dynamic pagination;
  const GetDepartmentsParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class GetFacilityCapacityReportParams extends Equatable {
  final DateTime date;
  final String? departmentId;
  const GetFacilityCapacityReportParams({required this.date, this.departmentId});
  @override
  List<Object?> get props => [date, departmentId];
}

class GetFacilityUtilizationReportParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  const GetFacilityUtilizationReportParams(
      {required this.startDate, required this.endDate, this.departmentId});
  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}
