import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';
import 'machine_entities.dart';

/// Worker entity
class Worker extends BaseEntity {
  final String employeeId;
  final String firstName;
  final String lastName;
  final String email;
  final String? phone;
  final String? address;
  final DateTime dateOfBirth;
  final DateTime hireDate;
  final DateTime? terminationDate;
  final WorkerStatus status;
  final String departmentId;
  final String departmentName;
  final String? supervisorId;
  final String? supervisorName;
  final WorkerRole role;
  final EmploymentType employmentType;
  final ShiftType shiftType;
  final List<WorkerSkill> skills;
  final List<String> certifications;
  final WorkerPerformance performance;
  final double hourlyRate;
  final double overtimeRate;
  final int maxHoursPerDay;
  final int maxHoursPerWeek;
  final List<String> attachments;
  final Map<String, dynamic> metadata;

  const Worker({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.employeeId,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phone,
    this.address,
    required this.dateOfBirth,
    required this.hireDate,
    this.terminationDate,
    required this.status,
    required this.departmentId,
    required this.departmentName,
    this.supervisorId,
    this.supervisorName,
    required this.role,
    required this.employmentType,
    required this.shiftType,
    this.skills = const [],
    this.certifications = const [],
    required this.performance,
    this.hourlyRate = 0.0,
    this.overtimeRate = 0.0,
    this.maxHoursPerDay = 8,
    this.maxHoursPerWeek = 40,
    this.attachments = const [],
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        employeeId,
        firstName,
        lastName,
        email,
        phone,
        address,
        dateOfBirth,
        hireDate,
        terminationDate,
        status,
        departmentId,
        departmentName,
        supervisorId,
        supervisorName,
        role,
        employmentType,
        shiftType,
        skills,
        certifications,
        performance,
        hourlyRate,
        overtimeRate,
        maxHoursPerDay,
        maxHoursPerWeek,
        attachments,
        metadata,
      ];

  /// Get full name
  String get fullName => '$firstName $lastName';

  /// Check if worker is active
  bool get isActive => status == WorkerStatus.active;

  /// Check if worker is available
  bool get isAvailable => status == WorkerStatus.available;

  /// Check if worker is on leave
  bool get isOnLeave => status == WorkerStatus.onLeave;

  /// Get years of experience
  double get yearsOfExperience {
    final endDate = terminationDate ?? DateTime.now();
    final experienceInDays = endDate.difference(hireDate).inDays;
    return experienceInDays / 365.25;
  }

  /// Get age in years
  int get ageInYears {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  /// Check if worker has skill
  bool hasSkill(String skillName) {
    return skills.any((skill) => skill.skillName.toLowerCase() == skillName.toLowerCase());
  }

  /// Get skill level for specific skill
  SkillLevel? getSkillLevel(String skillName) {
    final skill = skills.firstWhere(
      (skill) => skill.skillName.toLowerCase() == skillName.toLowerCase(),
      orElse: () => const WorkerSkill(skillName: '', level: SkillLevel.beginner, yearsOfExperience: 0),
    );
    return skill.skillName.isNotEmpty ? skill.level : null;
  }

  /// Check if worker has certification
  bool hasCertification(String certification) {
    return certifications.contains(certification);
  }
}

/// Worker skill entity
class WorkerSkill extends Equatable {
  final String skillName;
  final SkillLevel level;
  final double yearsOfExperience;
  final DateTime? lastAssessed;
  final String? certificationId;
  final DateTime? certificationExpiry;

  const WorkerSkill({
    required this.skillName,
    required this.level,
    required this.yearsOfExperience,
    this.lastAssessed,
    this.certificationId,
    this.certificationExpiry,
  });

  @override
  List<Object?> get props => [
        skillName,
        level,
        yearsOfExperience,
        lastAssessed,
        certificationId,
        certificationExpiry,
      ];

  /// Check if certification is expired
  bool get isCertificationExpired {
    if (certificationExpiry == null) return false;
    return DateTime.now().isAfter(certificationExpiry!);
  }
}

/// Worker performance entity
class WorkerPerformance extends Equatable {
  final double overallRating;
  final double productivityRating;
  final double qualityRating;
  final double attendanceRating;
  final double safetyRating;
  final int totalTasksCompleted;
  final int tasksCompletedOnTime;
  final double averageTaskCompletionTime;
  final int defectsReported;
  final int safetyIncidents;
  final DateTime? lastReviewDate;
  final String? reviewNotes;

  const WorkerPerformance({
    required this.overallRating,
    required this.productivityRating,
    required this.qualityRating,
    required this.attendanceRating,
    required this.safetyRating,
    this.totalTasksCompleted = 0,
    this.tasksCompletedOnTime = 0,
    this.averageTaskCompletionTime = 0.0,
    this.defectsReported = 0,
    this.safetyIncidents = 0,
    this.lastReviewDate,
    this.reviewNotes,
  });

  @override
  List<Object?> get props => [
        overallRating,
        productivityRating,
        qualityRating,
        attendanceRating,
        safetyRating,
        totalTasksCompleted,
        tasksCompletedOnTime,
        averageTaskCompletionTime,
        defectsReported,
        safetyIncidents,
        lastReviewDate,
        reviewNotes,
      ];

  /// Get on-time completion rate
  double get onTimeCompletionRate {
    if (totalTasksCompleted == 0) return 0.0;
    return (tasksCompletedOnTime / totalTasksCompleted) * 100;
  }

  /// Get defect rate
  double get defectRate {
    if (totalTasksCompleted == 0) return 0.0;
    return (defectsReported / totalTasksCompleted) * 100;
  }
}

/// Worker schedule entity
class WorkerSchedule extends BaseEntity {
  final String workerId;
  final String employeeId;
  final String workerName;
  final DateTime startTime;
  final DateTime endTime;
  final ScheduleType type;
  final ScheduleStatus status;
  final String? orderId;
  final String? orderNumber;
  final String? productionOrderId;
  final String? taskId;
  final String? machineId;
  final String? machineCode;
  final String? notes;
  final double? plannedOutput;
  final double? actualOutput;
  final String? outputUnit;
  final bool isOvertime;

  const WorkerSchedule({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.workerId,
    required this.employeeId,
    required this.workerName,
    required this.startTime,
    required this.endTime,
    required this.type,
    required this.status,
    this.orderId,
    this.orderNumber,
    this.productionOrderId,
    this.taskId,
    this.machineId,
    this.machineCode,
    this.notes,
    this.plannedOutput,
    this.actualOutput,
    this.outputUnit,
    this.isOvertime = false,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        workerId,
        employeeId,
        workerName,
        startTime,
        endTime,
        type,
        status,
        orderId,
        orderNumber,
        productionOrderId,
        taskId,
        machineId,
        machineCode,
        notes,
        plannedOutput,
        actualOutput,
        outputUnit,
        isOvertime,
      ];

  /// Get schedule duration
  Duration get duration => endTime.difference(startTime);

  /// Get schedule duration in hours
  double get durationInHours => duration.inMinutes / 60.0;

  /// Check if schedule is active
  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startTime) && now.isBefore(endTime);
  }

  /// Check if schedule is upcoming
  bool get isUpcoming => DateTime.now().isBefore(startTime);

  /// Check if schedule is completed
  bool get isCompleted => status == ScheduleStatus.completed;

  /// Get efficiency percentage
  double? get efficiency {
    if (plannedOutput == null || actualOutput == null || plannedOutput == 0) {
      return null;
    }
    return (actualOutput! / plannedOutput!) * 100;
  }
}

/// Worker attendance entity
class WorkerAttendance extends BaseEntity {
  final String workerId;
  final String employeeId;
  final String workerName;
  final DateTime date;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final AttendanceStatus status;
  final double hoursWorked;
  final double overtimeHours;
  final double breakTime;
  final String? notes;
  final String? approvedBy;
  final DateTime? approvedAt;

  const WorkerAttendance({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.workerId,
    required this.employeeId,
    required this.workerName,
    required this.date,
    this.checkInTime,
    this.checkOutTime,
    required this.status,
    this.hoursWorked = 0.0,
    this.overtimeHours = 0.0,
    this.breakTime = 0.0,
    this.notes,
    this.approvedBy,
    this.approvedAt,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        workerId,
        employeeId,
        workerName,
        date,
        checkInTime,
        checkOutTime,
        status,
        hoursWorked,
        overtimeHours,
        breakTime,
        notes,
        approvedBy,
        approvedAt,
      ];

  /// Check if worker is present
  bool get isPresent => status == AttendanceStatus.present;

  /// Check if worker is absent
  bool get isAbsent => status == AttendanceStatus.absent;

  /// Check if worker is on leave
  bool get isOnLeave => status == AttendanceStatus.onLeave;

  /// Check if worker is late
  bool get isLate => status == AttendanceStatus.late;

  /// Get total hours including overtime
  double get totalHours => hoursWorked + overtimeHours;

  /// Get effective work hours (excluding break time)
  double get effectiveWorkHours => hoursWorked - breakTime;
}

/// Worker leave entity
class WorkerLeave extends BaseEntity {
  final String workerId;
  final String employeeId;
  final String workerName;
  final LeaveType type;
  final DateTime startDate;
  final DateTime endDate;
  final int totalDays;
  final LeaveStatus status;
  final String reason;
  final String? approvedBy;
  final DateTime? approvedAt;
  final String? rejectionReason;
  final List<String> attachments;

  const WorkerLeave({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.workerId,
    required this.employeeId,
    required this.workerName,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.totalDays,
    required this.status,
    required this.reason,
    this.approvedBy,
    this.approvedAt,
    this.rejectionReason,
    this.attachments = const [],
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        workerId,
        employeeId,
        workerName,
        type,
        startDate,
        endDate,
        totalDays,
        status,
        reason,
        approvedBy,
        approvedAt,
        rejectionReason,
        attachments,
      ];

  /// Check if leave is approved
  bool get isApproved => status == LeaveStatus.approved;

  /// Check if leave is pending
  bool get isPending => status == LeaveStatus.pending;

  /// Check if leave is rejected
  bool get isRejected => status == LeaveStatus.rejected;

  /// Check if leave is active
  bool get isActive {
    final now = DateTime.now();
    return isApproved && now.isAfter(startDate) && now.isBefore(endDate.add(const Duration(days: 1)));
  }

  /// Get leave duration
  Duration get duration => endDate.difference(startDate);
}

// Enums

/// Worker status enum
enum WorkerStatus {
  active,
  available,
  busy,
  onLeave,
  inactive,
  terminated,
}

/// Worker status extension
extension WorkerStatusExtension on WorkerStatus {
  String get displayName {
    switch (this) {
      case WorkerStatus.active:
        return 'Active';
      case WorkerStatus.available:
        return 'Available';
      case WorkerStatus.busy:
        return 'Busy';
      case WorkerStatus.onLeave:
        return 'On Leave';
      case WorkerStatus.inactive:
        return 'Inactive';
      case WorkerStatus.terminated:
        return 'Terminated';
    }
  }

  String get value => name;

  bool get isWorking => this == WorkerStatus.active || this == WorkerStatus.busy;
}

/// Worker role enum
enum WorkerRole {
  operator,
  supervisor,
  manager,
  technician,
  inspector,
  helper,
}

/// Worker role extension
extension WorkerRoleExtension on WorkerRole {
  String get displayName {
    switch (this) {
      case WorkerRole.operator:
        return 'Machine Operator';
      case WorkerRole.supervisor:
        return 'Supervisor';
      case WorkerRole.manager:
        return 'Manager';
      case WorkerRole.technician:
        return 'Technician';
      case WorkerRole.inspector:
        return 'Quality Inspector';
      case WorkerRole.helper:
        return 'Helper';
    }
  }

  String get value => name;
}

/// Employment type enum
enum EmploymentType {
  fullTime,
  partTime,
  contract,
  temporary,
  intern,
}

/// Employment type extension
extension EmploymentTypeExtension on EmploymentType {
  String get displayName {
    switch (this) {
      case EmploymentType.fullTime:
        return 'Full Time';
      case EmploymentType.partTime:
        return 'Part Time';
      case EmploymentType.contract:
        return 'Contract';
      case EmploymentType.temporary:
        return 'Temporary';
      case EmploymentType.intern:
        return 'Intern';
    }
  }

  String get value => name;
}

/// Shift type enum
enum ShiftType {
  morning,
  afternoon,
  night,
  rotating,
  flexible,
}

/// Shift type extension
extension ShiftTypeExtension on ShiftType {
  String get displayName {
    switch (this) {
      case ShiftType.morning:
        return 'Morning Shift';
      case ShiftType.afternoon:
        return 'Afternoon Shift';
      case ShiftType.night:
        return 'Night Shift';
      case ShiftType.rotating:
        return 'Rotating Shift';
      case ShiftType.flexible:
        return 'Flexible Hours';
    }
  }

  String get value => name;
}

/// Skill level enum
enum SkillLevel {
  beginner,
  intermediate,
  advanced,
  expert,
}

/// Skill level extension
extension SkillLevelExtension on SkillLevel {
  String get displayName {
    switch (this) {
      case SkillLevel.beginner:
        return 'Beginner';
      case SkillLevel.intermediate:
        return 'Intermediate';
      case SkillLevel.advanced:
        return 'Advanced';
      case SkillLevel.expert:
        return 'Expert';
    }
  }

  String get value => name;

  int get level {
    switch (this) {
      case SkillLevel.beginner:
        return 1;
      case SkillLevel.intermediate:
        return 2;
      case SkillLevel.advanced:
        return 3;
      case SkillLevel.expert:
        return 4;
    }
  }
}

/// Attendance status enum
enum AttendanceStatus {
  present,
  absent,
  late,
  onLeave,
  halfDay,
}

/// Attendance status extension
extension AttendanceStatusExtension on AttendanceStatus {
  String get displayName {
    switch (this) {
      case AttendanceStatus.present:
        return 'Present';
      case AttendanceStatus.absent:
        return 'Absent';
      case AttendanceStatus.late:
        return 'Late';
      case AttendanceStatus.onLeave:
        return 'On Leave';
      case AttendanceStatus.halfDay:
        return 'Half Day';
    }
  }

  String get value => name;
}

/// Leave type enum
enum LeaveType {
  annual,
  sick,
  personal,
  maternity,
  paternity,
  emergency,
  unpaid,
}

/// Leave type extension
extension LeaveTypeExtension on LeaveType {
  String get displayName {
    switch (this) {
      case LeaveType.annual:
        return 'Annual Leave';
      case LeaveType.sick:
        return 'Sick Leave';
      case LeaveType.personal:
        return 'Personal Leave';
      case LeaveType.maternity:
        return 'Maternity Leave';
      case LeaveType.paternity:
        return 'Paternity Leave';
      case LeaveType.emergency:
        return 'Emergency Leave';
      case LeaveType.unpaid:
        return 'Unpaid Leave';
    }
  }

  String get value => name;
}

/// Leave status enum
enum LeaveStatus {
  pending,
  approved,
  rejected,
  cancelled,
}

/// Leave status extension
extension LeaveStatusExtension on LeaveStatus {
  String get displayName {
    switch (this) {
      case LeaveStatus.pending:
        return 'Pending';
      case LeaveStatus.approved:
        return 'Approved';
      case LeaveStatus.rejected:
        return 'Rejected';
      case LeaveStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value => name;
}

// Request classes

class CreateWorkerRequest extends Equatable {
  final String employeeId;
  final String firstName;
  final String lastName;
  final String email;
  final DateTime dateOfBirth;
  final DateTime hireDate;
  final String departmentId;
  final WorkerRole role;
  final EmploymentType employmentType;
  final ShiftType shiftType;

  const CreateWorkerRequest({
    required this.employeeId,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.dateOfBirth,
    required this.hireDate,
    required this.departmentId,
    required this.role,
    required this.employmentType,
    required this.shiftType,
  });

  @override
  List<Object?> get props => [
        employeeId,
        firstName,
        lastName,
        email,
        dateOfBirth,
        hireDate,
        departmentId,
        role,
        employmentType,
        shiftType,
      ];
}

class UpdateWorkerRequest extends Equatable {
  final String id;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? address;
  final DateTime? dateOfBirth;
  final DateTime? hireDate;
  final DateTime? terminationDate;
  final WorkerStatus? status;
  final String? departmentId;
  final String? supervisorId;
  final WorkerRole? role;
  final EmploymentType? employmentType;
  final ShiftType? shiftType;

  const UpdateWorkerRequest({
    required this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.address,
    this.dateOfBirth,
    this.hireDate,
    this.terminationDate,
    this.status,
    this.departmentId,
    this.supervisorId,
    this.role,
    this.employmentType,
    this.shiftType,
  });

  @override
  List<Object?> get props => [
        id,
        firstName,
        lastName,
        email,
        phone,
        address,
        dateOfBirth,
        hireDate,
        terminationDate,
        status,
        departmentId,
        supervisorId,
        role,
        employmentType,
        shiftType,
      ];
}

class CreateWorkerScheduleRequest extends Equatable {
  final String workerId;
  final DateTime startTime;
  final DateTime endTime;
  final ScheduleType type;

  const CreateWorkerScheduleRequest({
    required this.workerId,
    required this.startTime,
    required this.endTime,
    required this.type,
  });

  @override
  List<Object?> get props => [workerId, startTime, endTime, type];
}

class RecordAttendanceRequest extends Equatable {
  final String workerId;
  final DateTime date;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final AttendanceStatus status;

  const RecordAttendanceRequest({
    required this.workerId,
    required this.date,
    this.checkInTime,
    this.checkOutTime,
    required this.status,
  });

  @override
  List<Object?> get props => [workerId, date, checkInTime, checkOutTime, status];
}

class CreateLeaveRequestRequest extends Equatable {
  final String workerId;
  final LeaveType type;
  final DateTime startDate;
  final DateTime endDate;
  final String reason;

  const CreateLeaveRequestRequest({
    required this.workerId,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.reason,
  });

  @override
  List<Object?> get props => [workerId, type, startDate, endDate, reason];
}

// Report classes

class WorkerPerformanceReport extends Equatable {
  final String workerId;
  final String workerName;
  final DateTime startDate;
  final DateTime endDate;
  final double averageProductivity;
  final double averageQuality;
  final double attendancePercentage;
  final int tasksCompleted;

  const WorkerPerformanceReport({
    required this.workerId,
    required this.workerName,
    required this.startDate,
    required this.endDate,
    required this.averageProductivity,
    required this.averageQuality,
    required this.attendancePercentage,
    required this.tasksCompleted,
  });

  @override
  List<Object?> get props => [
        workerId,
        workerName,
        startDate,
        endDate,
        averageProductivity,
        averageQuality,
        attendancePercentage,
        tasksCompleted,
      ];
}
