import 'package:equatable/equatable.dart';
import '../../domain/entities/numbering_task.dart';

/// Base class for all numbering events
abstract class NumberingEvent extends Equatable {
  const NumberingEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load all numbering tasks
class LoadNumberingTasks extends NumberingEvent {
  final NumberingTaskStatus? status;
  final String? orderNo;
  final String? clientName;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? limit;
  final int? offset;
  final bool forceRefresh;

  const LoadNumberingTasks({
    this.status,
    this.orderNo,
    this.clientName,
    this.startDate,
    this.endDate,
    this.limit,
    this.offset,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [
        status,
        orderNo,
        clientName,
        startDate,
        endDate,
        limit,
        offset,
        forceRefresh,
      ];
}

/// Event to load a specific task by ID
class LoadNumberingTaskById extends NumberingEvent {
  final String taskId;

  const LoadNumberingTaskById(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Event to create a new numbering task
class CreateNumberingTask extends NumberingEvent {
  final DateTime date;
  final String orderNo;
  final String clientName;
  final String designNo;
  final GarmentSize size;
  final String color;
  final String bundleNo;
  final int pieceStartNo;
  final int pieceEndNo;
  final double? ratePerPiece;
  final String checkedBy;
  final String remarks;

  const CreateNumberingTask({
    required this.date,
    required this.orderNo,
    required this.clientName,
    required this.designNo,
    required this.size,
    required this.color,
    required this.bundleNo,
    required this.pieceStartNo,
    required this.pieceEndNo,
    this.ratePerPiece,
    required this.checkedBy,
    required this.remarks,
  });

  @override
  List<Object?> get props => [
        date,
        orderNo,
        clientName,
        designNo,
        size,
        color,
        bundleNo,
        pieceStartNo,
        pieceEndNo,
        ratePerPiece,
        checkedBy,
        remarks,
      ];
}

/// Event to update an existing numbering task
class UpdateNumberingTask extends NumberingEvent {
  final String taskId;
  final DateTime? date;
  final String? orderNo;
  final String? clientName;
  final String? designNo;
  final GarmentSize? size;
  final String? color;
  final String? bundleNo;
  final int? pieceStartNo;
  final int? pieceEndNo;
  final double? ratePerPiece;
  final String? checkedBy;
  final String? remarks;
  final NumberingTaskStatus? status;

  const UpdateNumberingTask({
    required this.taskId,
    this.date,
    this.orderNo,
    this.clientName,
    this.designNo,
    this.size,
    this.color,
    this.bundleNo,
    this.pieceStartNo,
    this.pieceEndNo,
    this.ratePerPiece,
    this.checkedBy,
    this.remarks,
    this.status,
  });

  @override
  List<Object?> get props => [
        taskId,
        date,
        orderNo,
        clientName,
        designNo,
        size,
        color,
        bundleNo,
        pieceStartNo,
        pieceEndNo,
        ratePerPiece,
        checkedBy,
        remarks,
        status,
      ];
}

/// Event to delete a numbering task
class DeleteNumberingTask extends NumberingEvent {
  final String taskId;

  const DeleteNumberingTask(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Event to get a new task (assign to current user)
class GetNewNumberingTask extends NumberingEvent {
  const GetNewNumberingTask();
}

/// Event to complete a numbering task
class CompleteNumberingTask extends NumberingEvent {
  final String taskId;

  const CompleteNumberingTask(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Event to send task to Munda department
class SendTaskToMunda extends NumberingEvent {
  final String taskId;

  const SendTaskToMunda(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Event to search numbering tasks
class SearchNumberingTasks extends NumberingEvent {
  final String? searchQuery;
  final NumberingTaskStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? limit;
  final int? offset;

  const SearchNumberingTasks({
    this.searchQuery,
    this.status,
    this.startDate,
    this.endDate,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [
        searchQuery,
        status,
        startDate,
        endDate,
        limit,
        offset,
      ];
}

/// Event to load tasks assigned to current user
class LoadMyNumberingTasks extends NumberingEvent {
  const LoadMyNumberingTasks();
}

/// Event to load tasks by status
class LoadTasksByStatus extends NumberingEvent {
  final NumberingTaskStatus status;

  const LoadTasksByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

/// Event to load task statistics
class LoadTaskStatistics extends NumberingEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? userId;

  const LoadTaskStatistics({
    this.startDate,
    this.endDate,
    this.userId,
  });

  @override
  List<Object?> get props => [startDate, endDate, userId];
}

/// Event to refresh tasks (pull to refresh)
class RefreshNumberingTasks extends NumberingEvent {
  const RefreshNumberingTasks();
}

/// Event to clear task selection
class ClearTaskSelection extends NumberingEvent {
  const ClearTaskSelection();
}

/// Event to select/deselect a task
class ToggleTaskSelection extends NumberingEvent {
  final String taskId;

  const ToggleTaskSelection(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

/// Event to select all tasks
class SelectAllTasks extends NumberingEvent {
  const SelectAllTasks();
}

/// Event to deselect all tasks
class DeselectAllTasks extends NumberingEvent {
  const DeselectAllTasks();
}

/// Event to bulk update task status
class BulkUpdateTaskStatus extends NumberingEvent {
  final List<String> taskIds;
  final NumberingTaskStatus status;

  const BulkUpdateTaskStatus({
    required this.taskIds,
    required this.status,
  });

  @override
  List<Object?> get props => [taskIds, status];
}

/// Event to load default rate per piece
class LoadDefaultRatePerPiece extends NumberingEvent {
  const LoadDefaultRatePerPiece();
}

/// Event to update default rate per piece (admin only)
class UpdateDefaultRatePerPiece extends NumberingEvent {
  final double rate;

  const UpdateDefaultRatePerPiece(this.rate);

  @override
  List<Object?> get props => [rate];
}

/// Event to export tasks
class ExportNumberingTasks extends NumberingEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final NumberingTaskStatus? status;
  final String format;

  const ExportNumberingTasks({
    this.startDate,
    this.endDate,
    this.status,
    this.format = 'csv',
  });

  @override
  List<Object?> get props => [startDate, endDate, status, format];
}

/// Event to start watching tasks (real-time updates)
class StartWatchingTasks extends NumberingEvent {
  final NumberingTaskStatus? status;
  final String? userId;

  const StartWatchingTasks({
    this.status,
    this.userId,
  });

  @override
  List<Object?> get props => [status, userId];
}

/// Event to stop watching tasks
class StopWatchingTasks extends NumberingEvent {
  const StopWatchingTasks();
}

/// Event to handle real-time task updates
class TasksUpdatedFromStream extends NumberingEvent {
  final List<NumberingTask> tasks;

  const TasksUpdatedFromStream(this.tasks);

  @override
  List<Object?> get props => [tasks];
}

/// Event to reset numbering state
class ResetNumberingState extends NumberingEvent {
  const ResetNumberingState();
}
