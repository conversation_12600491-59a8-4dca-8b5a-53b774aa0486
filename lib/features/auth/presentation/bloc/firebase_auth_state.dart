part of 'firebase_auth_bloc.dart';

/// Base Firebase authentication state
abstract class FirebaseAuthState extends Equatable {
  const FirebaseAuthState();

  @override
  List<Object?> get props => [];
}

/// Initial Firebase authentication state
class FirebaseAuthInitial extends FirebaseAuthState {
  const FirebaseAuthInitial();
}

/// Firebase authentication loading state
class FirebaseAuthLoading extends FirebaseAuthState {
  const FirebaseAuthLoading();
}

/// Firebase authentication authenticated state
class FirebaseAuthAuthenticated extends FirebaseAuthState {
  final User user;

  const FirebaseAuthAuthenticated(this.user);

  @override
  List<Object?> get props => [user];

  /// Check if user is admin
  bool get isAdmin => user.role == UserRole.administrator;

  /// Check if user is merchandiser
  bool get isMerchandiser => user.role == UserRole.merchandiser;

  /// Check if user is sewing supervisor
  bool get isSewingSupervisor => user.role == UserRole.sewingSupervisor;

  /// Check if user is sewing operator
  bool get isSewingOperator => user.role == UserRole.sewingOperator;

  /// Check if user is quality controller
  bool get isQualityController => user.role == UserRole.qualityController;

  /// Check if user is viewer
  bool get isViewer => user.role == UserRole.viewer;

  /// Check if user is active
  bool get isActive => user.status == CommonStatus.active;

  /// Check if email is verified
  bool get isEmailVerified => user.isEmailVerified;

  /// Get user's full name
  String get fullName => user.fullName;

  /// Get user's initials
  String get initials => user.initials;

  /// Check if user has permission
  bool hasPermission(String permission) => user.hasPermission(permission);

  /// Check if user can access department
  bool canAccessDepartment(String departmentId) => user.canAccessDepartment(departmentId);
}

/// Firebase authentication unauthenticated state
class FirebaseAuthUnauthenticated extends FirebaseAuthState {
  const FirebaseAuthUnauthenticated();
}

/// Firebase authentication error state
class FirebaseAuthError extends FirebaseAuthState {
  final String message;

  const FirebaseAuthError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Firebase sign up success state
class FirebaseAuthSignUpSuccess extends FirebaseAuthState {
  final AppUser user;

  const FirebaseAuthSignUpSuccess(this.user);

  @override
  List<Object?> get props => [user];
}

/// Firebase password reset email sent state
class FirebaseAuthPasswordResetEmailSent extends FirebaseAuthState {
  const FirebaseAuthPasswordResetEmailSent();
}

/// Firebase password updated state
class FirebaseAuthPasswordUpdated extends FirebaseAuthState {
  const FirebaseAuthPasswordUpdated();
}

/// Firebase user profile updated state
class FirebaseAuthUserProfileUpdated extends FirebaseAuthState {
  final AppUser user;

  const FirebaseAuthUserProfileUpdated(this.user);

  @override
  List<Object?> get props => [user];
}

/// Firebase email verification sent state
class FirebaseAuthEmailVerificationSent extends FirebaseAuthState {
  const FirebaseAuthEmailVerificationSent();
}

/// Firebase email verified state
class FirebaseAuthEmailVerified extends FirebaseAuthState {
  const FirebaseAuthEmailVerified();
}

/// Firebase user data refreshed state
class FirebaseAuthUserDataRefreshed extends FirebaseAuthState {
  final AppUser user;

  const FirebaseAuthUserDataRefreshed(this.user);

  @override
  List<Object?> get props => [user];
}

/// Firebase authentication session expired state
class FirebaseAuthSessionExpired extends FirebaseAuthState {
  final String reason;

  const FirebaseAuthSessionExpired({
    this.reason = 'Session expired',
  });

  @override
  List<Object?> get props => [reason];
}

/// Firebase authentication validation error state
class FirebaseAuthValidationError extends FirebaseAuthState {
  final Map<String, String> errors;

  const FirebaseAuthValidationError(this.errors);

  @override
  List<Object?> get props => [errors];
}

/// Firebase authentication network error state
class FirebaseAuthNetworkError extends FirebaseAuthState {
  final String message;

  const FirebaseAuthNetworkError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Firebase authentication permission denied state
class FirebaseAuthPermissionDenied extends FirebaseAuthState {
  final String message;
  final String? requiredPermission;

  const FirebaseAuthPermissionDenied(
    this.message, {
    this.requiredPermission,
  });

  @override
  List<Object?> get props => [message, requiredPermission];
}

/// Firebase authentication account disabled state
class FirebaseAuthAccountDisabled extends FirebaseAuthState {
  final String message;
  final CommonStatus status;

  const FirebaseAuthAccountDisabled(
    this.message,
    this.status,
  );

  @override
  List<Object?> get props => [message, status];
}

/// Firebase email not verified state
class FirebaseAuthEmailNotVerified extends FirebaseAuthState {
  final AppUser? user;

  const FirebaseAuthEmailNotVerified([this.user]);

  @override
  List<Object?> get props => [user];
}

/// Firebase email verified state

