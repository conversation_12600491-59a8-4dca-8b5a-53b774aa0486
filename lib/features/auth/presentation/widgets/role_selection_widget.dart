import 'package:flutter/material.dart';

import '../../../../core/auth/entities/user_entities.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';
// import '../../../../shared/enums/common_enums.dart';


/// Role selection widget for user registration
class RoleSelectionWidget extends StatelessWidget {
  final UserRole selectedRole;
  final ValueChanged<UserRole> onRoleChanged;
  final bool enabled;
  final bool showAdminRole;
  final List<UserRole>? allowedRoles;

  const RoleSelectionWidget({
    super.key,
    required this.selectedRole,
    required this.onRoleChanged,
    this.enabled = true,
    this.showAdminRole = true,
    this.allowedRoles,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Role',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: _getAvailableRoles().map((role) {
              return _buildRoleOption(role);
            }).toList(),
          ),
        ),
      ],
    );
  }

  List<UserRole> _getAvailableRoles() {
    if (allowedRoles != null) {
      return allowedRoles!;
    }

    List<UserRole> roles = UserRole.values.toList();

    // Remove admin role if not allowed
    if (!showAdminRole) {
      roles.removeWhere((role) => role == UserRole.administrator);
    }

    return roles;
  }

  Widget _buildRoleOption(UserRole role) {
    final isSelected = selectedRole == role;
    
    return InkWell(
      onTap: enabled ? () => onRoleChanged(role) : null,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : null,
          border: Border(
            bottom: BorderSide(
              color: AppColors.border.withOpacity(0.5),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getRoleIcon(role),
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    role.displayName,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isSelected ? AppColors.primary : AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _getRoleDescription(role),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Radio<UserRole>(
              value: role,
              groupValue: selectedRole,
              onChanged: enabled ? (value) {
                if (value != null) {
                  onRoleChanged(value);
                }
              } : null,
              activeColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return Icons.admin_panel_settings;
      case UserRole.merchandiser:
        return Icons.manage_accounts;
      case UserRole.sewingSupervisor:
        return Icons.supervisor_account;
      case UserRole.sewingOperator:
        return Icons.engineering;
      case UserRole.qualityController:
        return Icons.verified_user;
      case UserRole.viewer:
        return Icons.visibility;
      default:
        return Icons.person;
    }
  }

  String _getRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return 'Full system access and user management';
      case UserRole.merchandiser:
        return 'Department management and reporting';
      case UserRole.sewingSupervisor:
        return 'Team supervision and task coordination';
      case UserRole.sewingOperator:
        return 'Machine operation and production tasks';
      case UserRole.qualityController:
        return 'Quality control and inspection duties';
      case UserRole.viewer:
        return 'Read-only access to system data';
      default:
        return 'Standard user access';
    }
  }
}

/// Role badge widget
class RoleBadge extends StatelessWidget {
  final UserRole role;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;

  const RoleBadge({
    super.key,
    required this.role,
    this.fontSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getRoleColor(role).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getRoleColor(role).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getRoleIcon(role),
            size: fontSize != null ? fontSize! + 2 : 14,
            color: _getRoleColor(role),
          ),
          const SizedBox(width: 4),
          Text(
            role.displayName,
            style: AppTextStyles.bodySmall.copyWith(
              fontSize: fontSize,
              color: _getRoleColor(role),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return Icons.admin_panel_settings;
      case UserRole.merchandiser:
        return Icons.manage_accounts;
      case UserRole.sewingSupervisor:
        return Icons.supervisor_account;
      case UserRole.sewingOperator:
        return Icons.engineering;
      case UserRole.qualityController:
        return Icons.verified_user;
      case UserRole.viewer:
        return Icons.visibility;
      default:
        return Icons.person;
    }
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return Colors.red;
      case UserRole.merchandiser:
        return Colors.purple;
      case UserRole.sewingSupervisor:
        return Colors.blue;
      case UserRole.sewingOperator:
        return Colors.green;
      case UserRole.qualityController:
        return Colors.orange;
      case UserRole.viewer:
        return Colors.grey;
      default:
        return Colors.blueGrey;
    }
  }
}

/// Permission chip widget
class PermissionChip extends StatelessWidget {
  final String permission;
  final bool granted;
  final VoidCallback? onTap;

  const PermissionChip({
    super.key,
    required this.permission,
    required this.granted,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: granted 
              ? AppColors.success.withOpacity(0.1)
              : AppColors.error.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: granted 
                ? AppColors.success.withOpacity(0.3)
                : AppColors.error.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              granted ? Icons.check_circle : Icons.cancel,
              size: 16,
              color: granted ? AppColors.success : AppColors.error,
            ),
            const SizedBox(width: 4),
            Text(
              _formatPermissionName(permission),
              style: AppTextStyles.bodySmall.copyWith(
                color: granted ? AppColors.success : AppColors.error,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatPermissionName(String permission) {
    // Convert permission like 'orders.create' to 'Create Orders'
    final parts = permission.split('.');
    if (parts.length == 2) {
      final action = parts[1];
      final resource = parts[0];
      
      final actionFormatted = action[0].toUpperCase() + action.substring(1);
      final resourceFormatted = resource[0].toUpperCase() + resource.substring(1);
      
      return '$actionFormatted $resourceFormatted';
    }
    
    return permission;
  }
}

/// User status chip widget
class UserStatusChip extends StatelessWidget {
  final CommonStatus status;
  final double? fontSize;

  const UserStatusChip({
    super.key,
    required this.status,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(status).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getStatusColor(status),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            status.displayName,
            style: AppTextStyles.bodySmall.copyWith(
              fontSize: fontSize,
              color: _getStatusColor(status),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(CommonStatus status) {
    switch (status) {
      case CommonStatus.active:
        return AppColors.success;
      case CommonStatus.inactive:
        return AppColors.textSecondary;
      case CommonStatus.suspended:
        return AppColors.error;
      case CommonStatus.pending:
        return AppColors.warning;
      case CommonStatus.archived:
        return AppColors.textSecondary;
    }
  }
}
