import 'package:equatable/equatable.dart';

class CreateProductionOrderRequest extends Equatable {
  final String manufacturingOrderId;
  final String orderNumber;
  final String productId;
  final int quantity;
  final DateTime targetDate;
  final String? notes;
  final Map<String, dynamic>? customFields;

  const CreateProductionOrderRequest({
    required this.manufacturingOrderId,
    required this.orderNumber,
    required this.productId,
    required this.quantity,
    required this.targetDate,
    this.notes,
    this.customFields,
  });

  Map<String, dynamic> toJson() => {
        'manufacturingOrderId': manufacturingOrderId,
        'orderNumber': orderNumber,
        'productId': productId,
        'quantity': quantity,
        'targetDate': targetDate.toIso8601String(),
        if (notes != null) 'notes': notes,
        if (customFields != null) ...customFields!,
      };

  @override
  List<Object?> get props => [
        manufacturingOrderId,
        orderNumber,
        productId,
        quantity,
        targetDate,
        notes,
        customFields,
      ];
}
