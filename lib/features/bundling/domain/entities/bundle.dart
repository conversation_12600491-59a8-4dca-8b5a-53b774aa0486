import '../../../../shared/models/base_entity.dart';
import '../../../../shared/enums/common_enums.dart';

/// Bundle status enumeration
enum BundleStatus {
  pending('pending'),
  issuedToSewing('issued_to_sewing'),
  inProgress('in_progress'),
  completed('completed'),
  cancelled('cancelled');

  const BundleStatus(this.value);
  final String value;

  String get displayName {
    switch (this) {
      case BundleStatus.pending:
        return 'Pending';
      case BundleStatus.issuedToSewing:
        return 'Issued to Sewing';
      case BundleStatus.inProgress:
        return 'In Progress';
      case BundleStatus.completed:
        return 'Completed';
      case BundleStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get colorCode {
    switch (this) {
      case BundleStatus.pending:
        return '#FFA500'; // Orange
      case BundleStatus.issuedToSewing:
        return '#2196F3'; // Blue
      case BundleStatus.inProgress:
        return '#FF9800'; // Amber
      case BundleStatus.completed:
        return '#4CAF50'; // Green
      case BundleStatus.cancelled:
        return '#F44336'; // Red
    }
  }

  bool get isActive {
    switch (this) {
      case BundleStatus.pending:
      case BundleStatus.issuedToSewing:
      case BundleStatus.inProgress:
        return true;
      case BundleStatus.completed:
      case BundleStatus.cancelled:
        return false;
    }
  }

  static BundleStatus fromString(String value) {
    return BundleStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => BundleStatus.pending,
    );
  }
}

/// Garment size enumeration for bundling
enum GarmentSize {
  xs('XS'),
  s('S'),
  m('M'),
  l('L'),
  xl('XL'),
  xxl('XXL'),
  xxxl('XXXL'),
  size26('26'),
  size28('28'),
  size30('30'),
  size32('32'),
  size34('34'),
  size36('36'),
  size38('38'),
  size40('40'),
  size42('42'),
  size44('44');

  const GarmentSize(this.value);
  final String value;

  String get displayName => value;

  bool get isAlphabetic => ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'].contains(value);
  bool get isNumeric => !isAlphabetic;

  static GarmentSize fromString(String value) {
    return GarmentSize.values.firstWhere(
      (size) => size.value.toLowerCase() == value.toLowerCase(),
      orElse: () => GarmentSize.m,
    );
  }

  static List<String> get allSizes => GarmentSize.values.map((e) => e.value).toList();
  static List<GarmentSize> get alphabeticSizes => 
      GarmentSize.values.where((size) => size.isAlphabetic).toList();
  static List<GarmentSize> get numericSizes => 
      GarmentSize.values.where((size) => size.isNumeric).toList();
}

/// Bundle entity representing a collection of pieces for production
class Bundle extends AuditableEntity {
  /// Bundle number (auto-generated, e.g., B001, B002)
  final String bundleNo;
  
  /// Design number/code
  final String designNo;
  
  /// Lot number (fabric lot/batch)
  final String lotNo;
  
  /// Roll number (from which roll cut is done)
  final String rollNo;
  
  /// Alternative number (for shade/variation tracking)
  final String altNo;
  
  /// Garment size
  final GarmentSize size;
  
  /// Starting piece number in this bundle
  final int pieceStartNo;
  
  /// Ending piece number in this bundle
  final int pieceEndNo;
  
  /// Total pieces in this bundle
  final int totalPieces;
  
  /// Bundle status
  final BundleStatus status;
  
  /// User ID who created the bundle
  final String? supervisorId;
  
  /// User ID assigned to work on this bundle
  final String? assignedTo;
  
  /// Date when bundle was issued to sewing
  final DateTime? issuedAt;
  
  /// Date when bundle work started
  final DateTime? startedAt;
  
  /// Date when bundle was completed
  final DateTime? completedAt;
  
  /// Additional notes or remarks
  final String? remarks;
  
  /// Color information
  final String? color;
  
  /// Order number this bundle belongs to
  final String? orderNo;
  
  /// Client name
  final String? clientName;

  const Bundle({
    required String id,
    required this.bundleNo,
    required this.designNo,
    required this.lotNo,
    required this.rollNo,
    required this.altNo,
    required this.size,
    required this.pieceStartNo,
    required this.pieceEndNo,
    required this.totalPieces,
    required this.status,
    required DateTime createdAt,
    required DateTime updatedAt,
    this.supervisorId,
    this.assignedTo,
    this.issuedAt,
    this.startedAt,
    this.completedAt,
    this.remarks,
    this.color,
    this.orderNo,
    this.clientName,
    String? createdBy,
    String? updatedBy,
    String? deletedBy,
    DateTime? deletedAt,
    int version = 1,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          deletedBy: deletedBy,
          version: version,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        bundleNo,
        designNo,
        lotNo,
        rollNo,
        altNo,
        size,
        pieceStartNo,
        pieceEndNo,
        totalPieces,
        status,
        supervisorId,
        assignedTo,
        issuedAt,
        startedAt,
        completedAt,
        remarks,
        color,
        orderNo,
        clientName,
      ];

  /// Create a copy with updated fields
  Bundle copyWith({
    String? id,
    String? bundleNo,
    String? designNo,
    String? lotNo,
    String? rollNo,
    String? altNo,
    GarmentSize? size,
    int? pieceStartNo,
    int? pieceEndNo,
    int? totalPieces,
    BundleStatus? status,
    String? supervisorId,
    String? assignedTo,
    DateTime? issuedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? remarks,
    String? color,
    String? orderNo,
    String? clientName,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? deletedBy,
    DateTime? deletedAt,
    int? version,
  }) {
    return Bundle(
      id: id ?? this.id,
      bundleNo: bundleNo ?? this.bundleNo,
      designNo: designNo ?? this.designNo,
      lotNo: lotNo ?? this.lotNo,
      rollNo: rollNo ?? this.rollNo,
      altNo: altNo ?? this.altNo,
      size: size ?? this.size,
      pieceStartNo: pieceStartNo ?? this.pieceStartNo,
      pieceEndNo: pieceEndNo ?? this.pieceEndNo,
      totalPieces: totalPieces ?? this.totalPieces,
      status: status ?? this.status,
      supervisorId: supervisorId ?? this.supervisorId,
      assignedTo: assignedTo ?? this.assignedTo,
      issuedAt: issuedAt ?? this.issuedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      remarks: remarks ?? this.remarks,
      color: color ?? this.color,
      orderNo: orderNo ?? this.orderNo,
      clientName: clientName ?? this.clientName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy ?? this.deletedBy,
      deletedAt: deletedAt ?? this.deletedAt,
      version: version ?? this.version,
    );
  }

  /// Check if bundle can be issued to sewing
  bool get canBeIssued => status == BundleStatus.pending;

  /// Check if bundle can be started
  bool get canBeStarted => status == BundleStatus.issuedToSewing;

  /// Check if bundle can be completed
  bool get canBeCompleted => status == BundleStatus.inProgress;

  /// Check if bundle can be cancelled
  bool get canBeCancelled => status != BundleStatus.completed && status != BundleStatus.cancelled;

  /// Get bundle identifier for display
  String get displayId => '$bundleNo ($designNo-$lotNo-$rollNo)';

  /// Get piece range as string
  String get pieceRange => '$pieceStartNo-$pieceEndNo';

  /// Validate bundle data
  bool get isValid {
    return bundleNo.isNotEmpty &&
           designNo.isNotEmpty &&
           lotNo.isNotEmpty &&
           rollNo.isNotEmpty &&
           altNo.isNotEmpty &&
           pieceStartNo > 0 &&
           pieceEndNo >= pieceStartNo &&
           totalPieces > 0 &&
           totalPieces == (pieceEndNo - pieceStartNo + 1);
  }
}
