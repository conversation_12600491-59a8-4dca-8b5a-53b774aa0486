import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:hm_collection/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:zxcvbn/zxcvbn.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/validation_utils.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/loading_screen.dart';
import '../bloc/firebase_auth_bloc.dart';
import '../widgets/loading_button.dart';
import '../widgets/role_selection_widget.dart';
import '../widgets/password_strength_indicator.dart';

/// Firebase Sign Up Page
class FirebaseSignUpPage extends StatefulWidget {
  const FirebaseSignUpPage({super.key});

  @override
  State<FirebaseSignUpPage> createState() => _FirebaseSignUpPageState();
}

class _FirebaseSignUpPageState extends State<FirebaseSignUpPage> {
  late final FirebaseAuthBloc _signUpBloc;
  final _formKey = GlobalKey<FormState>();
  
  // Form field keys
  final _firstNameKey = GlobalKey<FormFieldState>();
  final _lastNameKey = GlobalKey<FormFieldState>();
  final _emailKey = GlobalKey<FormFieldState>();
  final _passwordKey = GlobalKey<FormFieldState>();
  // Add more keys for other form fields as needed
  
  // Controllers
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _employeeIdController = TextEditingController();
  final _zxcvbn = Zxcvbn();
  double _passwordStrength = 0.0;
  String _passwordStrengthText = '';
  Color _passwordStrengthColor = Colors.grey;

  UserRole _selectedRole = UserRole.sewingOperator;
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;

  @override
  void initState() {
    super.initState();
    _passwordController.addListener(_updatePasswordStrength);
    _signUpBloc = GetIt.instance<FirebaseAuthBloc>();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.removeListener(_updatePasswordStrength);
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    _employeeIdController.dispose();
    super.dispose();
  }

  void _updatePasswordStrength() {
    if (_passwordController.text.isEmpty) {
      setState(() {
        _passwordStrength = 0.0;
        _passwordStrengthText = '';
        _passwordStrengthColor = Colors.grey;
      });
      return;
    }

    final result = _zxcvbn.evaluate(_passwordController.text);
    final strength = (result?.score ?? 0) / 4.0; // Convert 0-4 score to 0.0-1.0 range with null safety
    
    setState(() {
      _passwordStrength = strength;
      
      if (strength < 0.3) {
        _passwordStrengthText = 'Weak';
        _passwordStrengthColor = Colors.red;
      } else if (strength < 0.7) {
        _passwordStrengthText = 'Fair';
        _passwordStrengthColor = Colors.orange;
      } else if (strength < 0.9) {
        _passwordStrengthText = 'Good';
        _passwordStrengthColor = Colors.lightGreen;
      } else {
        _passwordStrengthText = 'Strong';
        _passwordStrengthColor = Colors.green;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _signUpBloc,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Create Account'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
        ),
        body: BlocListener<FirebaseAuthBloc, FirebaseAuthState>(
          listener: _handleAuthStateChanges,
          child: LoadingOverlay(
            isLoading: _isLoading,
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(),
                      const SizedBox(height: 32),
                      _buildPersonalInfoSection(),
                      const SizedBox(height: 24),
                      _buildAccountInfoSection(),
                      const SizedBox(height: 24),
                      _buildRoleSelectionSection(),
                      const SizedBox(height: 24),
                      _buildTermsAndConditions(),
                      const SizedBox(height: 32),
                      _buildSignUpButton(),
                      const SizedBox(height: 16),
                      _buildLoginLink(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Join ${AppConstants.appName}',
          style: AppTextStyles.headlineLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Create your account to get started with our manufacturing management system',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _firstNameController,
                label: 'First Name',
                hintText: 'Enter your first name',
                prefixIcon: Icons.person_outline,
                //  validator: ValidationUtils.validateName,
                textInputAction: TextInputAction.next,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _lastNameController,
                label: 'Last Name',
                hintText: 'Enter your last name',
                prefixIcon: Icons.person_outline,
                // validator: ValidationUtils.validateName,
                textInputAction: TextInputAction.next,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _phoneController,
          label: 'Phone Number (Optional)',
          hintText: 'Enter your phone number',
          prefixIcon: Icons.phone_outlined,
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (value.length < 10) {
                return 'Phone number must be at least 10 digits';
              }
            }
            return null;
          },
          textInputAction: TextInputAction.next,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _employeeIdController,
          label: 'Employee ID (Optional)',
          hintText: 'Enter your employee ID',
          prefixIcon: Icons.badge_outlined,
          textInputAction: TextInputAction.next,
        ),
      ],
    );
  }

  Widget _buildAccountInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Information',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _emailController,
          label: 'Email',
          hintText: 'Enter your email',
          keyboardType: TextInputType.emailAddress,
          prefixIcon: Icons.email_outlined,
          validator: ValidationUtils.validateEmail,
          onChanged: (value) {
            // Update email in real-time for better UX
            setState(() {});
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _passwordController,
          label: 'Password',
          hintText: 'Enter a strong password',
          obscureText: _obscurePassword,
          prefixIcon: Icons.lock_outline,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility_off : Icons.visibility,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
          onChanged: (value) {
            // Password strength is updated via the listener
          },
          validator: ValidationUtils.validatePassword,
        ),
        if (_passwordController.text.isNotEmpty) ...[
          const SizedBox(height: 8),
          PasswordStrengthIndicator(
            strength: _passwordStrength,
            strengthText: _passwordStrengthText,
            strengthColor: _passwordStrengthColor,
          ),
          const SizedBox(height: 8),
          Text(
            'Password should contain at least 8 characters, including uppercase, lowercase, numbers, and special characters.',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
        const SizedBox(height: 16),
        CustomTextField(
          controller: _confirmPasswordController,
          label: 'Confirm Password',
          hintText: 'Confirm your password',
          prefixIcon: Icons.lock_outline,
          obscureText: _obscureConfirmPassword,
          suffixIcon: IconButton(
            icon: Icon(
              _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () {
              setState(() {
                _obscureConfirmPassword = !_obscureConfirmPassword;
              });
            },
          ),
          validator: (value) {
            if (value != _passwordController.text) {
              return 'Passwords do not match';
            }
            return null;
          },
          textInputAction: TextInputAction.done,
        ),
      ],
    );
  }

  Widget _buildRoleSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Role Selection',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Select your role in the organization. This will determine your access permissions.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 16),
        RoleSelectionWidget(
          selectedRole: _selectedRole,
          onRoleChanged: (role) {
            setState(() {
              _selectedRole = role;
            });
          },
          showAdminRole: false,
        ),
      ],
    );
  }

  Widget _buildTermsAndConditions() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _agreeToTerms,
          onChanged: (value) {
            setState(() {
              _agreeToTerms = value ?? false;
            });
          },
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _agreeToTerms = !_agreeToTerms;
              });
            },
            child: Text.rich(
              TextSpan(
                text: 'I agree to the ',
                style: AppTextStyles.bodyMedium,
                children: [
                  TextSpan(
                    text: 'Terms of Service',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                  const TextSpan(text: ' and '),
                  TextSpan(
                    text: 'Privacy Policy',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpButton() {
    return BlocConsumer<FirebaseAuthBloc, FirebaseAuthState>(
      listener: (context, state) {
        _handleAuthStateChanges(context, state);
      },
      builder: (context, state) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          child: LoadingButton(
            onPressed: _isLoading ? null : _handleSignUp,
            isLoading: _isLoading,
           child: const Text('Login'),
          ),
        );
      },
    );
  }

  Widget _buildLoginLink() {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Already have an account?',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          TextButton(
            onPressed: _isLoading
                ? null
                : () {
                    // Clear form when navigating to login
                    _formKey.currentState?.reset();
                    context.go('/firebase-login');
                  },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              'Log In',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleSignUp() {
    // Close the keyboard if it's open
    FocusScope.of(context).unfocus();
    
    if (!_formKey.currentState!.validate()) {
      // Trigger validation to show error messages
      _formKey.currentState!.validate();
      return;
    }
    
    if (!_agreeToTerms) {
      _showErrorSnackBar('Please agree to the terms and conditions');
      // Scroll to terms checkbox
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    _signUpBloc.add(
      FirebaseSignUpRequested(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        role: _selectedRole,
      ),
    );
  }

  void _handleAuthStateChanges(BuildContext context, FirebaseAuthState state) {
    setState(() {
      _isLoading = state is FirebaseAuthLoading;
    });

    if (state is FirebaseAuthSignUpSuccess) {
      // Automatically log in after successful signup
      _signUpBloc.add(FirebaseSignInRequested(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      ));
    } else if (state is FirebaseAuthAuthenticated) {
      _showSuccessSnackBar('Registration successful! Welcome!');
      _navigateToDashboard(context);
    } else if (state is FirebaseAuthError) {
      _showErrorSnackBar(state.message);
    }
  }

  void _navigateToDashboard(BuildContext context) {
    // Add a small delay to ensure the UI has time to update
    Future.delayed(const Duration(milliseconds: 500), () {
      // Clear form fields after successful signup
      _firstNameController.clear();
      _lastNameController.clear();
      _emailController.clear();
      _passwordController.clear();
      _confirmPasswordController.clear();
      _phoneController.clear();
      _employeeIdController.clear();
      
      // Navigate to single dashboard regardless of role
      context.go('/dashboard');
    });
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showEmailVerificationDialog(String email) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Verify Your Email'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'We\'ve sent a verification email to:',
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              email,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Please check your email and click the verification link to activate your account.',
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              context.pop();
              context.pop();
            },
            child: const Text('Back to Login'),
          ),
          ElevatedButton(
            onPressed: () {
              _signUpBloc.add(const AuthEmailVerificationRequested() as FirebaseAuthEvent);
              context.pop();
            },
            child: const Text('Resend Email'),
          ),
        ],
      ),
    );
  }
}
