import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Manufacturing order entity
class ManufacturingOrder extends BaseEntity {
  final String orderNumber;
  final String clientId;
  final String clientName;
  final OrderStatus status;
  final OrderPriority priority;
  final OrderType type;
  final DateTime orderDate;
  final DateTime? requiredDate;
  final DateTime? deliveryDate;
  final List<OrderItem> items;
  final OrderPricing pricing;
  final OrderShipping? shipping;
  final List<OrderNote> notes;
  final List<OrderAttachment> attachments;
  final Map<String, dynamic> metadata;
  final String? assignedMerchandiser;
  final Department currentDepartment;
  final double completionPercentage;

  const ManufacturingOrder({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.orderNumber,
    required this.clientId,
    required this.clientName,
    required this.status,
    required this.priority,
    required this.type,
    required this.orderDate,
    this.requiredDate,
    this.deliveryDate,
    required this.items,
    required this.pricing,
    this.shipping,
    this.notes = const [],
    this.attachments = const [],
    this.metadata = const {},
    this.assignedMerchandiser,
    required this.currentDepartment,
    this.completionPercentage = 0.0,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        orderNumber,
        clientId,
        clientName,
        status,
        priority,
        type,
        orderDate,
        requiredDate,
        deliveryDate,
        items,
        pricing,
        shipping,
        notes,
        attachments,
        metadata,
        assignedMerchandiser,
        currentDepartment,
        completionPercentage,
      ];

  /// Get total quantity of all items
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);

  /// Get total value of the order
  double get totalValue => pricing.totalAmount;

  /// Check if order is overdue
  bool get isOverdue {
    if (requiredDate == null) return false;
    return DateTime.now().isAfter(requiredDate!) && !status.isCompleted;
  }

  /// Get days until delivery
  int? get daysUntilDelivery {
    if (requiredDate == null) return null;
    return requiredDate!.difference(DateTime.now()).inDays;
  }

  /// Check if order is urgent
  bool get isUrgent {
    return priority == OrderPriority.urgent || 
           priority == OrderPriority.critical ||
           (daysUntilDelivery != null && daysUntilDelivery! <= 3);
  }
}

/// Order item entity
class OrderItem extends Equatable {
  final String id;
  final String productId;
  final String productName;
  final String? productCode;
  final String? description;
  final int quantity;
  final String unit;
  final double unitPrice;
  final double totalPrice;
  final GarmentSize? size;
  final String? color;
  final String? fabric;
  final Map<String, dynamic> specifications;
  final List<String> attachments;
  final OrderItemStatus status;
  final int completedQuantity;

  const OrderItem({
    required this.id,
    required this.productId,
    required this.productName,
    this.productCode,
    this.description,
    required this.quantity,
    required this.unit,
    required this.unitPrice,
    required this.totalPrice,
    this.size,
    this.color,
    this.fabric,
    this.specifications = const {},
    this.attachments = const [],
    this.status = OrderItemStatus.pending,
    this.completedQuantity = 0,
  });

  @override
  List<Object?> get props => [
        id,
        productId,
        productName,
        productCode,
        description,
        quantity,
        unit,
        unitPrice,
        totalPrice,
        size,
        color,
        fabric,
        specifications,
        attachments,
        status,
        completedQuantity,
      ];

  /// Get completion percentage for this item
  double get completionPercentage {
    if (quantity == 0) return 0.0;
    return (completedQuantity / quantity) * 100;
  }

  /// Get remaining quantity
  int get remainingQuantity => quantity - completedQuantity;

  /// Check if item is completed
  bool get isCompleted => completedQuantity >= quantity;

  /// Validate required fields for the order item
  bool get isValid {
    return productId.isNotEmpty && 
           productName.isNotEmpty && 
           quantity > 0 && 
           unit.isNotEmpty &&
           unitPrice >= 0;
  }
}

/// Order pricing information
class OrderPricing extends Equatable {
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double shippingCost;
  final double totalAmount;
  final String currency;
  final List<OrderPricingBreakdown> breakdown;

  const OrderPricing({
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.shippingCost,
    required this.totalAmount,
    required this.currency,
    this.breakdown = const [],
  });

  @override
  List<Object?> get props => [
        subtotal,
        taxAmount,
        discountAmount,
        shippingCost,
        totalAmount,
        currency,
        breakdown,
      ];
}

/// Order pricing breakdown
class OrderPricingBreakdown extends Equatable {
  final String description;
  final double amount;
  final String type; // material, labor, overhead, etc.

  const OrderPricingBreakdown({
    required this.description,
    required this.amount,
    required this.type,
  });

  @override
  List<Object?> get props => [description, amount, type];
}

/// Order shipping information
class OrderShipping extends Equatable {
  final String method;
  final String? trackingNumber;
  final String address;
  final String city;
  final String state;
  final String country;
  final String postalCode;
  final String? contactPerson;
  final String? contactPhone;
  final DateTime? estimatedDelivery;
  final DateTime? actualDelivery;

  const OrderShipping({
    required this.method,
    this.trackingNumber,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    this.contactPerson,
    this.contactPhone,
    this.estimatedDelivery,
    this.actualDelivery,
  });

  @override
  List<Object?> get props => [
        method,
        trackingNumber,
        address,
        city,
        state,
        country,
        postalCode,
        contactPerson,
        contactPhone,
        estimatedDelivery,
        actualDelivery,
      ];
}

/// Order note
class OrderNote extends BaseEntity {
  final String orderId;
  final String userId;
  final String userName;
  final String content;
  final OrderNoteType type;
  final bool isInternal;

  const OrderNote({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.orderId,
    required this.userId,
    required this.userName,
    required this.content,
    required this.type,
    this.isInternal = false,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        orderId,
        userId,
        userName,
        content,
        type,
        isInternal,
      ];
}

/// Order attachment
class OrderAttachment extends BaseEntity {
  final String orderId;
  final String fileName;
  final String fileUrl;
  final String fileType;
  final int fileSize;
  final String uploadedBy;
  final OrderAttachmentType type;

  const OrderAttachment({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.orderId,
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
    required this.uploadedBy,
    required this.type,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        orderId,
        fileName,
        fileUrl,
        fileType,
        fileSize,
        uploadedBy,
        type,
      ];
}

/// Import the common enums


/// Order status is now imported from common_enums.dart
/// Order status extension
extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.draft:
        return 'Draft';
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProduction:
        return 'In Production';
      case OrderStatus.qualityCheck:
        return 'Quality Check';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.onHold:
        return 'On Hold';
    }
  }

  String get value {
    return name;
  }

  bool get isCompleted {
    return this == OrderStatus.completed ||
           this == OrderStatus.shipped ||
           this == OrderStatus.delivered;
  }

  bool get isActive {
    return this != OrderStatus.cancelled &&
           this != OrderStatus.completed &&
           this != OrderStatus.shipped &&
           this != OrderStatus.delivered;
  }

  bool get canEdit {
    return this == OrderStatus.draft ||
           this == OrderStatus.pending;
  }

  bool get canCancel {
    return this != OrderStatus.cancelled &&
           this != OrderStatus.completed &&
           this != OrderStatus.shipped &&
           this != OrderStatus.delivered;
  }
}

/// Order type is now imported from common_enums.dart
/// OrderPriority is imported from common_enums.dart

/// Order item status enum
enum OrderItemStatus {
  pending,
  inProgress,
  completed,
  cancelled,
  onHold,
}

/// Order note type enum
enum OrderNoteType {
  general,
  production,
  quality,
  shipping,
  client,
  internal,
}

/// Order attachment type enum
enum OrderAttachmentType {
  design,
  specification,
  sample,
  contract,
  invoice,
  other,
}

/// Garment size enum
enum GarmentSize {
  xs,
  s,
  m,
  l,
  xl,
  xxl,
  xxxl,
  custom,
}

/// Garment size extension
extension GarmentSizeExtension on GarmentSize {
  String get displayName {
    switch (this) {
      case GarmentSize.xs:
        return 'XS';
      case GarmentSize.s:
        return 'S';
      case GarmentSize.m:
        return 'M';
      case GarmentSize.l:
        return 'L';
      case GarmentSize.xl:
        return 'XL';
      case GarmentSize.xxl:
        return 'XXL';
      case GarmentSize.xxxl:
        return 'XXXL';
      case GarmentSize.custom:
        return 'Custom';
    }
  }

  String get value {
    return name;
  }
}
