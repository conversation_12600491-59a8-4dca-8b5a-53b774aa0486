import 'package:equatable/equatable.dart';
import 'bundle.dart';

/// Bundle input data from cutting department
class BundleInput extends Equatable {
  /// Design number/code
  final String designNo;
  
  /// Lot number (fabric lot/batch)
  final String lotNo;
  
  /// Roll number (from which roll cut is done)
  final String rollNo;
  
  /// Alternative number (for shade/variation tracking)
  final String altNo;
  
  /// Garment size
  final GarmentSize size;
  
  /// Total pieces available for bundling
  final int totalPiecesAvailable;
  
  /// Number of pieces per bundle (supervisor decides)
  final int piecesPerBundle;
  
  /// Color information
  final String? color;
  
  /// Order number this bundle belongs to
  final String? orderNo;
  
  /// Client name
  final String? clientName;
  
  /// Additional notes or remarks
  final String? remarks;

  const BundleInput({
    required this.designNo,
    required this.lotNo,
    required this.rollNo,
    required this.altNo,
    required this.size,
    required this.totalPiecesAvailable,
    required this.piecesPerBundle,
    this.color,
    this.orderNo,
    this.clientName,
    this.remarks,
  });

  @override
  List<Object?> get props => [
        designNo,
        lotNo,
        rollNo,
        altNo,
        size,
        totalPiecesAvailable,
        piecesPerBundle,
        color,
        orderNo,
        clientName,
        remarks,
      ];

  /// Calculate number of bundles that will be created
  int get numberOfBundles {
    return (totalPiecesAvailable / piecesPerBundle).ceil();
  }

  /// Calculate pieces in the last bundle
  int get piecesInLastBundle {
    final remainder = totalPiecesAvailable % piecesPerBundle;
    return remainder == 0 ? piecesPerBundle : remainder;
  }

  /// Check if there will be a remainder in the last bundle
  bool get hasRemainder {
    return totalPiecesAvailable % piecesPerBundle != 0;
  }

  /// Validate input data
  bool get isValid {
    return designNo.isNotEmpty &&
           lotNo.isNotEmpty &&
           rollNo.isNotEmpty &&
           altNo.isNotEmpty &&
           totalPiecesAvailable > 0 &&
           piecesPerBundle > 0 &&
           piecesPerBundle <= totalPiecesAvailable;
  }

  /// Get validation errors
  List<String> get validationErrors {
    final errors = <String>[];
    
    if (designNo.isEmpty) {
      errors.add('Design number is required');
    }
    
    if (lotNo.isEmpty) {
      errors.add('Lot number is required');
    }
    
    if (rollNo.isEmpty) {
      errors.add('Roll number is required');
    }
    
    if (altNo.isEmpty) {
      errors.add('Alt number is required');
    }
    
    if (totalPiecesAvailable <= 0) {
      errors.add('Total pieces must be greater than 0');
    }
    
    if (piecesPerBundle <= 0) {
      errors.add('Pieces per bundle must be greater than 0');
    }
    
    if (piecesPerBundle > totalPiecesAvailable) {
      errors.add('Pieces per bundle cannot exceed total pieces available');
    }
    
    return errors;
  }

  /// Create a copy with updated fields
  BundleInput copyWith({
    String? designNo,
    String? lotNo,
    String? rollNo,
    String? altNo,
    GarmentSize? size,
    int? totalPiecesAvailable,
    int? piecesPerBundle,
    String? color,
    String? orderNo,
    String? clientName,
    String? remarks,
  }) {
    return BundleInput(
      designNo: designNo ?? this.designNo,
      lotNo: lotNo ?? this.lotNo,
      rollNo: rollNo ?? this.rollNo,
      altNo: altNo ?? this.altNo,
      size: size ?? this.size,
      totalPiecesAvailable: totalPiecesAvailable ?? this.totalPiecesAvailable,
      piecesPerBundle: piecesPerBundle ?? this.piecesPerBundle,
      color: color ?? this.color,
      orderNo: orderNo ?? this.orderNo,
      clientName: clientName ?? this.clientName,
      remarks: remarks ?? this.remarks,
    );
  }

  /// Generate bundle preview information
  List<BundlePreview> generateBundlePreviews() {
    final previews = <BundlePreview>[];
    int currentPieceStart = 1;
    
    for (int i = 0; i < numberOfBundles; i++) {
      final bundleNumber = i + 1;
      final bundleNo = 'B${bundleNumber.toString().padLeft(3, '0')}';
      
      int piecesInThisBundle;
      if (i == numberOfBundles - 1 && hasRemainder) {
        piecesInThisBundle = piecesInLastBundle;
      } else {
        piecesInThisBundle = piecesPerBundle;
      }
      
      final pieceEndNo = currentPieceStart + piecesInThisBundle - 1;
      
      previews.add(BundlePreview(
        bundleNo: bundleNo,
        pieceStartNo: currentPieceStart,
        pieceEndNo: pieceEndNo,
        totalPieces: piecesInThisBundle,
      ));
      
      currentPieceStart = pieceEndNo + 1;
    }
    
    return previews;
  }

  @override
  String toString() {
    return 'BundleInput(designNo: $designNo, lotNo: $lotNo, rollNo: $rollNo, '
           'altNo: $altNo, size: ${size.value}, totalPieces: $totalPiecesAvailable, '
           'piecesPerBundle: $piecesPerBundle)';
  }
}

/// Bundle preview for showing bundle breakdown before creation
class BundlePreview extends Equatable {
  final String bundleNo;
  final int pieceStartNo;
  final int pieceEndNo;
  final int totalPieces;

  const BundlePreview({
    required this.bundleNo,
    required this.pieceStartNo,
    required this.pieceEndNo,
    required this.totalPieces,
  });

  @override
  List<Object?> get props => [bundleNo, pieceStartNo, pieceEndNo, totalPieces];

  String get pieceRange => '$pieceStartNo-$pieceEndNo';

  @override
  String toString() {
    return 'BundlePreview(bundleNo: $bundleNo, range: $pieceRange, total: $totalPieces)';
  }
}
