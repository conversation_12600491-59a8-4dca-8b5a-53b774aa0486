import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/budget_overview.dart';
import '../../domain/entities/expense_breakdown.dart';
import '../../domain/entities/financial_entities.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/financial_report_entities.dart' hide FinancialSummary, CostTrend;
import '../../domain/entities/revenue_analysis.dart';
import '../../domain/repositories/financial_repository.dart';
import '../../domain/usecases/financial_usecases.dart';
import '../widgets/financial_summary_card.dart' hide FinancialSummary;
import '../widgets/profit_loss_chart.dart';

part 'financial_event.dart';
part 'financial_state.dart';

/// Financial management BLoC
@injectable
class FinancialBloc extends Bloc<FinancialEvent, FinancialState> {
  final GetCostCentersUseCase _getCostCentersUseCase;
  final GetCostCenterByIdUseCase _getCostCenterByIdUseCase;
  final CreateCostCenterUseCase _createCostCenterUseCase;
  final UpdateCostCenterUseCase _updateCostCenterUseCase;
  final DeleteCostCenterUseCase _deleteCostCenterUseCase;
  final GetTransactionsUseCase _getTransactionsUseCase;
  final GetTransactionByIdUseCase _getTransactionByIdUseCase;
  final CreateTransactionUseCase _createTransactionUseCase;
  final UpdateTransactionUseCase _updateTransactionUseCase;
  final DeleteTransactionUseCase _deleteTransactionUseCase;
  final ApproveTransactionUseCase _approveTransactionUseCase;
  final RejectTransactionUseCase _rejectTransactionUseCase;
  final GetBudgetsUseCase _getBudgetsUseCase;
  final GetBudgetByIdUseCase _getBudgetByIdUseCase;
  final CreateBudgetUseCase _createBudgetUseCase;
  final UpdateBudgetUseCase _updateBudgetUseCase;
  final DeleteBudgetUseCase _deleteBudgetUseCase;
  final ApproveBudgetUseCase _approveBudgetUseCase;
  final RejectBudgetUseCase _rejectBudgetUseCase;
  final GetReportsUseCase _getReportsUseCase;
  final GenerateReportUseCase _generateReportUseCase;
  final GetProfitLossStatementUseCase _getProfitLossStatementUseCase;
  final GetCostAnalysisReportUseCase _getCostAnalysisReportUseCase;
  final GetBudgetVarianceReportUseCase _getBudgetVarianceReportUseCase;
  final GetFinancialSummaryUseCase _getFinancialSummaryUseCase;
  final GetCostTrendsUseCase _getCostTrendsUseCase;
  final SearchCostCentersUseCase _searchCostCentersUseCase;
  final SearchTransactionsUseCase _searchTransactionsUseCase;
  final SearchBudgetsUseCase _searchBudgetsUseCase;

  FinancialBloc(
    this._getCostCentersUseCase,
    this._getCostCenterByIdUseCase,
    this._createCostCenterUseCase,
    this._updateCostCenterUseCase,
    this._deleteCostCenterUseCase,
    this._getTransactionsUseCase,
    this._getTransactionByIdUseCase,
    this._createTransactionUseCase,
    this._updateTransactionUseCase,
    this._deleteTransactionUseCase,
    this._approveTransactionUseCase,
    this._rejectTransactionUseCase,
    this._getBudgetsUseCase,
    this._getBudgetByIdUseCase,
    this._createBudgetUseCase,
    this._updateBudgetUseCase,
    this._deleteBudgetUseCase,
    this._approveBudgetUseCase,
    this._rejectBudgetUseCase,
    this._getReportsUseCase,
    this._generateReportUseCase,
    this._getProfitLossStatementUseCase,
    this._getCostAnalysisReportUseCase,
    this._getBudgetVarianceReportUseCase,
    this._getFinancialSummaryUseCase,
    this._getCostTrendsUseCase,
    this._searchCostCentersUseCase,
    this._searchTransactionsUseCase,
    this._searchBudgetsUseCase,
  ) : super(const FinancialInitial()) {
    // Cost Center Events
    on<GetCostCentersRequested>(_onGetCostCentersRequested);
    on<GetCostCenterByIdRequested>(_onGetCostCenterByIdRequested);
    on<CreateCostCenterRequested>(_onCreateCostCenterRequested);
    on<UpdateCostCenterRequested>(_onUpdateCostCenterRequested);
    on<DeleteCostCenterRequested>(_onDeleteCostCenterRequested);
    on<SearchCostCentersRequested>(_onSearchCostCentersRequested);

    // Transaction Events
    on<GetTransactionsRequested>(_onGetTransactionsRequested);
    on<GetTransactionByIdRequested>(_onGetTransactionByIdRequested);
    on<CreateTransactionRequested>(_onCreateTransactionRequested);
    on<UpdateTransactionRequested>(_onUpdateTransactionRequested);
    on<DeleteTransactionRequested>(_onDeleteTransactionRequested);
    on<ApproveTransactionRequested>(_onApproveTransactionRequested);
    on<RejectTransactionRequested>(_onRejectTransactionRequested);
    on<SearchTransactionsRequested>(_onSearchTransactionsRequested);

    // Budget Events
    on<GetBudgetsRequested>(_onGetBudgetsRequested);
    on<GetBudgetByIdRequested>(_onGetBudgetByIdRequested);
    on<CreateBudgetRequested>(_onCreateBudgetRequested);
    on<UpdateBudgetRequested>(_onUpdateBudgetRequested);
    on<DeleteBudgetRequested>(_onDeleteBudgetRequested);
    on<ApproveBudgetRequested>(_onApproveBudgetRequested);
    on<RejectBudgetRequested>(_onRejectBudgetRequested);
    on<SearchBudgetsRequested>(_onSearchBudgetsRequested);

    // Report Events
    on<GetFinancialReportsRequested>(_onGetFinancialReportsRequested);
    on<GenerateReportRequested>(_onGenerateReportRequested);
    on<GetProfitLossStatementRequested>(_onGetProfitLossStatementRequested);
    on<GetCostAnalysisReportRequested>(_onGetCostAnalysisReportRequested);
    on<GetBudgetVarianceReportRequested>(_onGetBudgetVarianceReportRequested);

    // Analytics Events
    on<GetFinancialSummaryRequested>(_onGetFinancialSummaryRequested);
    on<GetCostTrendsRequested>(_onGetCostTrendsRequested);
  }

  // Cost Center Event Handlers
  Future<void> _onGetCostCentersRequested(
    GetCostCentersRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getCostCentersUseCase(GetCostCentersParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) => emit(FinancialCostCentersLoaded(response.data ?? [])),
    );
  }

  Future<void> _onGetCostCenterByIdRequested(
    GetCostCenterByIdRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getCostCenterByIdUseCase(IdParams(event.costCenterId));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialCostCenterLoaded(response.data!));
        } else {
          emit(const FinancialError('Cost center not found'));
        }
      },
    );
  }

  Future<void> _onCreateCostCenterRequested(
    CreateCostCenterRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _createCostCenterUseCase(event.request);

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialCostCenterLoaded(response.data!));
        } else {
          emit(const FinancialError('Failed to load cost center data'));
        }
      },
    );
  }

  Future<void> _onUpdateCostCenterRequested(
    UpdateCostCenterRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _updateCostCenterUseCase(event.request as UpdateCostCenterRequest);

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialCostCenterLoaded(response.data!));
        } else {
          emit(const FinancialError('No cost center data received'));
        }
      },
    );
  }

  Future<void> _onDeleteCostCenterRequested(
    DeleteCostCenterRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _deleteCostCenterUseCase(IdParams(event.costCenterId));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (_) => emit(FinancialCostCenterDeleted(event.costCenterId)),
    );
  }

  Future<void> _onSearchCostCentersRequested(
    SearchCostCentersRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _searchCostCentersUseCase(SearchCostCentersParams(
      query: event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) => emit(FinancialCostCentersLoaded(response.data ?? [])),
    );
  }

  // Transaction Event Handlers
  Future<void> _onGetTransactionsRequested(
    GetTransactionsRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getTransactionsUseCase(GetTransactionsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) => emit(FinancialTransactionsLoaded(response.data ?? [])),
    );
  }

  Future<void> _onGetTransactionByIdRequested(
    GetTransactionByIdRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getTransactionByIdUseCase(IdParams(event.transactionId));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialTransactionLoaded(response.data!));
        } else {
          emit(const FinancialError('Transaction not found'));
        }
      },
    );
  }

  Future<void> _onCreateTransactionRequested(
    CreateTransactionRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _createTransactionUseCase(event.request);

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data == null) {
          emit(const FinancialError('Transaction data is null'));
          return;
        }
        emit(FinancialTransactionLoaded(response.data!));
      },
    );
  }

  Future<void> _onUpdateTransactionRequested(
    UpdateTransactionRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _updateTransactionUseCase(event.request);

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialTransactionUpdated(response.data!));
        } else {
          emit(const FinancialError('Transaction data is null'));
        }
      },
    );
  }

  Future<void> _onDeleteTransactionRequested(
    DeleteTransactionRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _deleteTransactionUseCase(IdParams(event.transactionId));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (_) => emit(FinancialTransactionDeleted(event.transactionId)),
    );
  }

  Future<void> _onApproveTransactionRequested(
    ApproveTransactionRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _approveTransactionUseCase(ApproveTransactionParams(
      transactionId: event.transactionId,
      approverId: event.approverId,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialTransactionApproved(response.data!));
        } else {
          emit(const FinancialError('Transaction data is null'));
        }
      },
    );
  }

  Future<void> _onRejectTransactionRequested(
    RejectTransactionRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _rejectTransactionUseCase(RejectTransactionParams(
      transactionId: event.transactionId,
      rejectedBy: event.rejectedBy,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialTransactionRejected(response.data!));
        } else {
          emit(const FinancialError('Transaction data is null'));
        }
      },
    );
  }

  Future<void> _onSearchTransactionsRequested(
    SearchTransactionsRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _searchTransactionsUseCase(SearchTransactionsParams(
      query: event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) => emit(FinancialTransactionsSearched(response.data ?? [], event.query)),
    );
  }

  // Budget Event Handlers
  Future<void> _onGetBudgetsRequested(
    GetBudgetsRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getBudgetsUseCase(GetBudgetsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) => emit(FinancialBudgetsLoaded(response.data ?? [])),
    );
  }

  Future<void> _onGetBudgetByIdRequested(
    GetBudgetByIdRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getBudgetByIdUseCase(IdParams(event.id));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialBudgetLoaded(response.data!));
        } else {
          emit(const FinancialError('Budget not found'));
        }
      },
    );
  }

  Future<void> _onCreateBudgetRequested(
    CreateBudgetRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _createBudgetUseCase(event.request);

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialBudgetCreated(response.data!));
        } else {
          emit(const FinancialError('Budget data is null'));
        }
      },
    );
  }

  Future<void> _onUpdateBudgetRequested(
    UpdateBudgetRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _updateBudgetUseCase(event.request);

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) { 
          emit(FinancialBudgetUpdated(response.data!));
        } else {
          emit(const FinancialError('Budget data is null'));
        }
      },
    );
  }

  Future<void> _onDeleteBudgetRequested(
    DeleteBudgetRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _deleteBudgetUseCase(IdParams(event.budgetId));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (_) => emit(FinancialBudgetDeleted(event.budgetId)),
    );
  }

  Future<void> _onApproveBudgetRequested(
    ApproveBudgetRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _approveBudgetUseCase(ApproveBudgetParams(
      budgetId: event.budgetId,
      approverId: event.approverId,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialBudgetApproved(response.data!));
        } else {
          emit(const FinancialError('Budget data is null'));
        }
      },
    );
  }

  Future<void> _onRejectBudgetRequested(
    RejectBudgetRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _rejectBudgetUseCase(RejectBudgetParams(
      budgetId: event.budgetId,
      rejectedBy: event.rejectedBy ?? 'Unknown',
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialBudgetRejected(response.data!));
        } else {
          emit(const FinancialError('Budget data is null'));
        }
      },
    );
  }

  Future<void> _onSearchBudgetsRequested(
    SearchBudgetsRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _searchBudgetsUseCase(SearchBudgetsParams(
      query: event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) => emit(FinancialBudgetsSearched(response.data ?? [], event.query)),
    );
  }

  // Report Event Handlers
  Future<void> _onGetFinancialReportsRequested(
    GetFinancialReportsRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getReportsUseCase(GetReportsParams(
      filter: event.filters,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) => emit(FinancialReportsLoaded(response.data ?? [])),
    );
  }

  Future<void> _onGenerateReportRequested(
    GenerateReportRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _generateReportUseCase(event.request);

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(FinancialReportGenerated(response.data!));
        } else {
          emit(const FinancialError('Report data is null'));
        }
      },
    );
  }

  Future<void> _onGetProfitLossStatementRequested(
    GetProfitLossStatementRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getProfitLossStatementUseCase(GetProfitLossStatementParams(
      startDate: event.startDate,
      endDate: event.endDate,
      costCenterId: event.costCenterId,
      departmentId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (statement) => emit(FinancialProfitLossStatementLoaded(statement)),
    );
  }

  Future<void> _onGetCostAnalysisReportRequested(
    GetCostAnalysisReportRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getCostAnalysisReportUseCase(GetCostAnalysisReportParams(
      startDate: event.dateRange.startDate,
      endDate: event.dateRange.endDate,
      costCenterId: event.costCenterId,
      departmentId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (report) => emit(FinancialCostAnalysisReportLoaded(report)),
    );
  }

  Future<void> _onGetBudgetVarianceReportRequested(
    GetBudgetVarianceReportRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getBudgetVarianceReportUseCase(GetBudgetVarianceReportParams(
      startDate: event.startDate,
      endDate: event.endDate,
      budgetId: event.budgetId,
      costCenterId: event.costCenterId,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (report) => emit(FinancialBudgetVarianceReportLoaded(report)),
    );
  }

  // Analytics Event Handlers
  Future<void> _onGetFinancialSummaryRequested(
    GetFinancialSummaryRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getFinancialSummaryUseCase(GetFinancialSummaryParams(
      startDate: event.dateRange?.startDate ?? DateTime.now().subtract(const Duration(days: 30)),
      endDate: event.dateRange?.endDate ?? DateTime.now(),
      costCenterId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (summary) => emit(FinancialSummaryLoaded(summary)),
    );
  }

  Future<void> _onGetCostTrendsRequested(
    GetCostTrendsRequested event,
    Emitter<FinancialState> emit,
  ) async {
    emit(const FinancialLoading());

    final result = await _getCostTrendsUseCase(GetCostTrendsParams(
      startDate: event.dateRange.startDate,
      endDate: event.dateRange.endDate,
      category: event.category,
      costCenterId: event.costCenterId,
    ));

    result.fold(
      (failure) => emit(FinancialError(failure.message)),
      (trends) => emit(FinancialCostTrendsLoaded(trends.trends)),
    );
  }
}
