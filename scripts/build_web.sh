#!/bin/bash

# HM Collection Web Build Script
# This script builds the Flutter web application with optimizations

set -e

echo "🚀 Starting HM Collection Web Build Process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_status "Flutter version:"
flutter --version

# Clean previous builds
print_status "Cleaning previous builds..."
flutter clean

# Get dependencies
print_status "Getting dependencies..."
flutter pub get

# Run code generation
print_status "Running code generation..."
if command -v dart &> /dev/null; then
    dart run build_runner build --delete-conflicting-outputs
else
    print_warning "build_runner not available, skipping code generation"
fi

# Check for web support
print_status "Checking web support..."
if ! flutter devices | grep -q "Chrome"; then
    print_warning "Chrome not detected, but continuing with web build"
fi

# Build for web with optimizations
print_status "Building web application..."

# Production build with optimizations
flutter build web \
    --release \
    --web-renderer canvaskit \
    --base-href "/" \
    --source-maps \
    --tree-shake-icons \
    --dart-define=FLUTTER_WEB_USE_SKIA=true \
    --dart-define=FLUTTER_WEB_AUTO_DETECT=true

if [ $? -eq 0 ]; then
    print_success "Web build completed successfully!"
else
    print_error "Web build failed!"
    exit 1
fi

# Build directory
BUILD_DIR="build/web"

if [ -d "$BUILD_DIR" ]; then
    print_status "Build output directory: $BUILD_DIR"
    
    # Show build size
    BUILD_SIZE=$(du -sh "$BUILD_DIR" | cut -f1)
    print_status "Build size: $BUILD_SIZE"
    
    # List main files
    print_status "Main build files:"
    ls -la "$BUILD_DIR" | head -10
    
    # Check for critical files
    CRITICAL_FILES=("index.html" "main.dart.js" "flutter_service_worker.js" "manifest.json")
    
    for file in "${CRITICAL_FILES[@]}"; do
        if [ -f "$BUILD_DIR/$file" ]; then
            print_success "✓ $file found"
        else
            print_warning "✗ $file missing"
        fi
    done
    
    # Optimize build (optional)
    print_status "Optimizing build..."
    
    # Compress JavaScript files if gzip is available
    if command -v gzip &> /dev/null; then
        find "$BUILD_DIR" -name "*.js" -exec gzip -k {} \;
        print_success "JavaScript files compressed"
    fi
    
    # Create deployment info
    cat > "$BUILD_DIR/build-info.json" << EOF
{
    "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "version": "1.0.0",
    "buildType": "production",
    "renderer": "canvaskit",
    "flutter": "$(flutter --version | head -n 1)",
    "buildSize": "$BUILD_SIZE"
}
EOF
    
    print_success "Build info created"
    
else
    print_error "Build directory not found!"
    exit 1
fi

# Performance recommendations
print_status "Performance Recommendations:"
echo "  • Enable gzip compression on your web server"
echo "  • Set proper cache headers for static assets"
echo "  • Use HTTPS for better performance"
echo "  • Consider using a CDN for global distribution"
echo "  • Monitor Core Web Vitals"

# Deployment instructions
print_status "Deployment Instructions:"
echo "  1. Upload the contents of '$BUILD_DIR' to your web server"
echo "  2. Configure your server to serve index.html for all routes"
echo "  3. Set up proper MIME types for .wasm files"
echo "  4. Enable compression for .js, .css, and .html files"
echo "  5. Test the deployment thoroughly"

# Local testing
print_status "Local Testing:"
echo "  To test locally, run:"
echo "  cd $BUILD_DIR && python3 -m http.server 8000"
echo "  Then open http://localhost:8000 in your browser"

print_success "🎉 HM Collection web build process completed!"
print_status "Ready for deployment to production!"

# Optional: Start local server for testing
read -p "Would you like to start a local server for testing? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting local server on port 8000..."
    cd "$BUILD_DIR"
    
    if command -v python3 &> /dev/null; then
        python3 -m http.server 8000
    elif command -v python &> /dev/null; then
        python -m http.server 8000
    else
        print_warning "Python not found. Please serve the files manually."
        print_status "You can use any static file server to serve the '$BUILD_DIR' directory"
    fi
fi
