import 'package:flutter/material.dart';

/// Search bar widget for quality control
class QualitySearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onFilterPressed;
  final TextEditingController? controller;
  final bool showFilter;

  const QualitySearchBar({
    Key? key,
    this.hintText = 'Search quality inspections...',
    this.onSearchChanged,
    this.onFilterPressed,
    this.controller,
    this.showFilter = true,
  }) : super(key: key);

  @override
  State<QualitySearchBar> createState() => _QualitySearchBarState();
}

class _QualitySearchBarState extends State<QualitySearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _controller,
              onChanged: widget.onSearchChanged,
              decoration: InputDecoration(
                hintText: widget.hintText,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _controller.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          widget.onSearchChanged?.call('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).primaryColor),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          if (widget.showFilter) ...[
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: widget.onFilterPressed,
                tooltip: 'Filter',
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Advanced search bar with quick filters for quality control
class QualityAdvancedSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<QualityInspectionStatus?>? onStatusFilterChanged;
  final ValueChanged<String?>? onInspectorFilterChanged;
  final VoidCallback? onDateFilterPressed;
  final TextEditingController? controller;
  final QualityInspectionStatus? selectedStatus;
  final String? selectedInspector;
  final List<String> inspectors;

  const QualityAdvancedSearchBar({
    Key? key,
    this.hintText = 'Search quality inspections...',
    this.onSearchChanged,
    this.onStatusFilterChanged,
    this.onInspectorFilterChanged,
    this.onDateFilterPressed,
    this.controller,
    this.selectedStatus,
    this.selectedInspector,
    this.inspectors = const [],
  }) : super(key: key);

  @override
  State<QualityAdvancedSearchBar> createState() => _QualityAdvancedSearchBarState();
}

class _QualityAdvancedSearchBarState extends State<QualityAdvancedSearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search field
          TextField(
            controller: _controller,
            onChanged: widget.onSearchChanged,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _controller.clear();
                        widget.onSearchChanged?.call('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Theme.of(context).primaryColor),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),
          
          // Quick filters
          Row(
            children: [
              // Status filter
              Expanded(
                child: DropdownButtonFormField<QualityInspectionStatus?>(
                  value: widget.selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<QualityInspectionStatus?>(
                      value: null,
                      child: Text('All Statuses'),
                    ),
                    ...QualityInspectionStatus.values.map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(status.displayName),
                    )),
                  ],
                  onChanged: widget.onStatusFilterChanged,
                ),
              ),
              const SizedBox(width: 12),
              
              // Inspector filter
              if (widget.inspectors.isNotEmpty)
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: widget.selectedInspector,
                    decoration: InputDecoration(
                      labelText: 'Inspector',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Inspectors'),
                      ),
                      ...widget.inspectors.map((inspector) => DropdownMenuItem(
                        value: inspector,
                        child: Text(inspector),
                      )),
                    ],
                    onChanged: widget.onInspectorFilterChanged,
                  ),
                ),
              
              const SizedBox(width: 12),
              
              // Date filter button
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: const Icon(Icons.date_range),
                  onPressed: widget.onDateFilterPressed,
                  tooltip: 'Date Filter',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Quality search bar with score filter
class QualityScoreSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<RangeValues>? onScoreRangeChanged;
  final TextEditingController? controller;
  final RangeValues scoreRange;

  const QualityScoreSearchBar({
    Key? key,
    this.hintText = 'Search by product, inspector...',
    this.onSearchChanged,
    this.onScoreRangeChanged,
    this.controller,
    this.scoreRange = const RangeValues(0, 100),
  }) : super(key: key);

  @override
  State<QualityScoreSearchBar> createState() => _QualityScoreSearchBarState();
}

class _QualityScoreSearchBarState extends State<QualityScoreSearchBar> {
  late TextEditingController _controller;
  late RangeValues _scoreRange;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _scoreRange = widget.scoreRange;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search field
          TextField(
            controller: _controller,
            onChanged: widget.onSearchChanged,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _controller.clear();
                        widget.onSearchChanged?.call('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Theme.of(context).primaryColor),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Score range filter
          Text(
            'Quality Score Range: ${_scoreRange.start.round()}% - ${_scoreRange.end.round()}%',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          RangeSlider(
            values: _scoreRange,
            min: 0,
            max: 100,
            divisions: 20,
            labels: RangeLabels(
              '${_scoreRange.start.round()}%',
              '${_scoreRange.end.round()}%',
            ),
            onChanged: (values) {
              setState(() => _scoreRange = values);
              widget.onScoreRangeChanged?.call(values);
            },
          ),
        ],
      ),
    );
  }
}

// Placeholder enum - this should be defined in quality_entities.dart
enum QualityInspectionStatus { pending, inProgress, completed, failed }

extension QualityInspectionStatusExtension on QualityInspectionStatus {
  String get displayName {
    switch (this) {
      case QualityInspectionStatus.pending:
        return 'Pending';
      case QualityInspectionStatus.inProgress:
        return 'In Progress';
      case QualityInspectionStatus.completed:
        return 'Completed';
      case QualityInspectionStatus.failed:
        return 'Failed';
    }
  }
}