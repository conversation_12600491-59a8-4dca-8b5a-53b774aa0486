part of 'department_bloc.dart';

abstract class DepartmentState extends Equatable {
  const DepartmentState();

  @override
  List<Object> get props => [];
}

class DepartmentInitial extends DepartmentState {}

class DepartmentLoading extends DepartmentState {}

class DepartmentLoaded extends DepartmentState {
  final List<Department> departments;

  const DepartmentLoaded({required this.departments});

  @override
  List<Object> get props => [departments];
}

class DepartmentError extends DepartmentState {
  final String message;

  const DepartmentError({required this.message});

  @override
  List<Object> get props => [message];
}
