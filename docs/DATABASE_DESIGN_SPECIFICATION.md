# Database Design Specification - Cloth Manufacturing App

## Overview

This document outlines the database schema design for the Cloth Manufacturing App, including tables, relationships, indexes, and constraints.

## Table of Contents

1. [Database Overview](#database-overview)
2. [Entity Relationship Diagram](#entity-relationship-diagram)
3. [Core Tables](#core-tables)
4. [Relationships](#relationships)
5. [Indexes and Performance](#indexes-and-performance)
6. [Data Types and Constraints](#data-types-and-constraints)
7. [Sample Data](#sample-data)

---

## Database Overview

### Database Technology
- **Primary Database**: PostgreSQL 14+
- **Cache Layer**: Redis 6+
- **Search Engine**: Elasticsearch 8+ (optional for advanced search)

### Design Principles
- **Normalization**: 3NF (Third Normal Form) with selective denormalization for performance
- **ACID Compliance**: Full ACID properties for data integrity
- **Scalability**: Designed for horizontal scaling with partitioning
- **Audit Trail**: Complete audit logging for all critical operations
- **Soft Deletes**: Logical deletion for data recovery

---

## Entity Relationship Diagram

```mermaid
erDiagram
    USERS ||--o{ USER_SESSIONS : has
    USERS }o--|| ROLES : belongs_to
    USERS }o--|| DEPARTMENTS : belongs_to
    
    CUSTOMERS ||--o{ ORDERS : places
    ORDERS ||--o{ ORDER_ITEMS : contains
    ORDER_ITEMS }o--|| PRODUCTS : references
    
    ORDERS ||--o{ PRODUCTION_ORDERS : generates
    PRODUCTION_ORDERS ||--o{ TASKS : contains
    TASKS }o--|| USERS : assigned_to
    TASKS }o--|| WORKSTATIONS : performed_at
    
    PRODUCTS ||--o{ BILL_OF_MATERIALS : has
    BILL_OF_MATERIALS }o--|| MATERIALS : uses
    MATERIALS ||--o{ INVENTORY_TRANSACTIONS : tracked_by
    
    QUALITY_CHECKPOINTS ||--o{ QUALITY_INSPECTIONS : defines
    QUALITY_INSPECTIONS }o--|| USERS : conducted_by
    QUALITY_INSPECTIONS ||--o{ DEFECTS : may_have
    
    DEPARTMENTS ||--o{ WORKSTATIONS : contains
    WORKSTATIONS ||--o{ TASKS : hosts
```

---

## Core Tables

### User Management

#### users
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_image_url TEXT,
    role_id UUID NOT NULL REFERENCES roles(id),
    department_id UUID NOT NULL REFERENCES departments(id),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);
```

#### roles
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### departments
```sql
CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    head_user_id UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### user_sessions
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    access_token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255) NOT NULL,
    device_id VARCHAR(255),
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Order Management

#### customers
```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    address JSONB,
    contact_person VARCHAR(255),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    payment_terms INTEGER DEFAULT 30, -- days
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### orders
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID NOT NULL REFERENCES customers(id),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    order_date DATE NOT NULL DEFAULT CURRENT_DATE,
    delivery_date DATE NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);
```

#### order_items
```sql
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    specifications JSONB DEFAULT '{}',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Product Management

#### products
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    specifications JSONB DEFAULT '{}',
    base_price DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### materials
```sql
CREATE TABLE materials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    current_stock DECIMAL(15,3) DEFAULT 0,
    minimum_stock DECIMAL(15,3) DEFAULT 0,
    maximum_stock DECIMAL(15,3),
    unit_cost DECIMAL(10,2),
    supplier_id UUID REFERENCES suppliers(id),
    specifications JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### bill_of_materials
```sql
CREATE TABLE bill_of_materials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id),
    material_id UUID NOT NULL REFERENCES materials(id),
    quantity_required DECIMAL(15,3) NOT NULL CHECK (quantity_required > 0),
    unit_of_measure VARCHAR(20) NOT NULL,
    wastage_percentage DECIMAL(5,2) DEFAULT 0,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, material_id)
);
```

### Production Management

#### production_orders
```sql
CREATE TABLE production_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id),
    production_number VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'planned',
    priority VARCHAR(20) DEFAULT 'medium',
    planned_start_date DATE NOT NULL,
    planned_end_date DATE NOT NULL,
    actual_start_date DATE,
    actual_end_date DATE,
    total_quantity INTEGER NOT NULL CHECK (total_quantity > 0),
    completed_quantity INTEGER DEFAULT 0 CHECK (completed_quantity >= 0),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);
```

#### workstations
```sql
CREATE TABLE workstations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(id),
    workstation_type VARCHAR(100) NOT NULL,
    capacity INTEGER DEFAULT 1,
    current_load INTEGER DEFAULT 0,
    specifications JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### tasks
```sql
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    production_order_id UUID NOT NULL REFERENCES production_orders(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    task_type VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'not_started',
    priority VARCHAR(20) DEFAULT 'medium',
    assigned_to UUID REFERENCES users(id),
    workstation_id UUID REFERENCES workstations(id),
    estimated_duration INTEGER, -- minutes
    actual_duration INTEGER, -- minutes
    planned_start_time TIMESTAMP WITH TIME ZONE,
    planned_end_time TIMESTAMP WITH TIME ZONE,
    actual_start_time TIMESTAMP WITH TIME ZONE,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    dependencies JSONB DEFAULT '[]', -- array of task IDs
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);
```

#### task_logs
```sql
CREATE TABLE task_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    old_status VARCHAR(50),
    new_status VARCHAR(50),
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Inventory Management

#### inventory_transactions
```sql
CREATE TABLE inventory_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    material_id UUID NOT NULL REFERENCES materials(id),
    transaction_type VARCHAR(50) NOT NULL, -- 'received', 'issued', 'adjusted', 'returned'
    quantity DECIMAL(15,3) NOT NULL,
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
    reference_type VARCHAR(50), -- 'purchase_order', 'production_order', 'adjustment'
    reference_id UUID,
    batch_number VARCHAR(100),
    expiry_date DATE,
    location VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id)
);
```

#### suppliers
```sql
CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    address JSONB,
    contact_person VARCHAR(255),
    payment_terms INTEGER DEFAULT 30, -- days
    rating DECIMAL(3,2) CHECK (rating >= 0 AND rating <= 5),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Quality Control

#### quality_checkpoints
```sql
CREATE TABLE quality_checkpoints (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    process_stage VARCHAR(100) NOT NULL, -- 'cutting', 'sewing', 'finishing', 'final'
    department_id UUID NOT NULL REFERENCES departments(id),
    criteria JSONB NOT NULL DEFAULT '[]',
    acceptable_limits JSONB DEFAULT '{}',
    sampling_method VARCHAR(100) DEFAULT 'random',
    sample_size INTEGER DEFAULT 1,
    is_mandatory BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### quality_inspections
```sql
CREATE TABLE quality_inspections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    checkpoint_id UUID NOT NULL REFERENCES quality_checkpoints(id),
    production_order_id UUID NOT NULL REFERENCES production_orders(id),
    task_id UUID REFERENCES tasks(id),
    inspector_id UUID NOT NULL REFERENCES users(id),
    inspection_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    sample_size INTEGER NOT NULL CHECK (sample_size > 0),
    results JSONB NOT NULL DEFAULT '[]',
    overall_result VARCHAR(20) NOT NULL, -- 'pass', 'fail', 'conditional'
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### defects
```sql
CREATE TABLE defects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    inspection_id UUID NOT NULL REFERENCES quality_inspections(id),
    defect_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL, -- 'minor', 'major', 'critical'
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    description TEXT,
    corrective_action TEXT,
    status VARCHAR(50) DEFAULT 'open', -- 'open', 'in_progress', 'resolved', 'closed'
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

---

## Relationships

### Primary Relationships

1. **Users ↔ Roles**: Many-to-One (Each user has one role)
2. **Users ↔ Departments**: Many-to-One (Each user belongs to one department)
3. **Orders ↔ Customers**: Many-to-One (Each order belongs to one customer)
4. **Orders ↔ Production Orders**: One-to-Many (Each order can have multiple production orders)
5. **Production Orders ↔ Tasks**: One-to-Many (Each production order has multiple tasks)
6. **Tasks ↔ Users**: Many-to-One (Each task assigned to one user)
7. **Materials ↔ Inventory Transactions**: One-to-Many (Each material has multiple transactions)

### Foreign Key Constraints

```sql
-- Add foreign key constraints with proper cascade rules
ALTER TABLE users ADD CONSTRAINT fk_users_role 
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE RESTRICT;

ALTER TABLE users ADD CONSTRAINT fk_users_department 
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE RESTRICT;

ALTER TABLE orders ADD CONSTRAINT fk_orders_customer 
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT;

ALTER TABLE production_orders ADD CONSTRAINT fk_production_orders_order 
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;

ALTER TABLE tasks ADD CONSTRAINT fk_tasks_production_order 
    FOREIGN KEY (production_order_id) REFERENCES production_orders(id) ON DELETE CASCADE;
```

---

## Indexes and Performance

### Primary Indexes

```sql
-- Performance indexes for frequent queries
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_department_role ON users(department_id, role_id);
CREATE INDEX idx_users_active ON users(is_active) WHERE is_active = true;

CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_date_range ON orders(order_date, delivery_date);
CREATE INDEX idx_orders_number ON orders(order_number);

CREATE INDEX idx_tasks_assigned_user ON tasks(assigned_to);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_production_order ON tasks(production_order_id);
CREATE INDEX idx_tasks_workstation ON tasks(workstation_id);

CREATE INDEX idx_inventory_transactions_material ON inventory_transactions(material_id);
CREATE INDEX idx_inventory_transactions_date ON inventory_transactions(created_at);
CREATE INDEX idx_inventory_transactions_type ON inventory_transactions(transaction_type);

-- Composite indexes for complex queries
CREATE INDEX idx_tasks_user_status ON tasks(assigned_to, status) WHERE status IN ('not_started', 'in_progress');
CREATE INDEX idx_orders_customer_status ON orders(customer_id, status);
```

### JSONB Indexes

```sql
-- JSONB indexes for specifications and metadata
CREATE INDEX idx_products_specifications ON products USING GIN (specifications);
CREATE INDEX idx_materials_specifications ON materials USING GIN (specifications);
CREATE INDEX idx_quality_criteria ON quality_checkpoints USING GIN (criteria);
CREATE INDEX idx_quality_results ON quality_inspections USING GIN (results);
```

---

## Data Types and Constraints

### Check Constraints

```sql
-- Status constraints
ALTER TABLE orders ADD CONSTRAINT chk_orders_status 
    CHECK (status IN ('pending', 'confirmed', 'in_production', 'completed', 'cancelled', 'on_hold'));

ALTER TABLE tasks ADD CONSTRAINT chk_tasks_status 
    CHECK (status IN ('not_started', 'in_progress', 'completed', 'cancelled', 'on_hold'));

ALTER TABLE production_orders ADD CONSTRAINT chk_production_status 
    CHECK (status IN ('planned', 'in_progress', 'completed', 'cancelled'));

-- Priority constraints
ALTER TABLE orders ADD CONSTRAINT chk_orders_priority 
    CHECK (priority IN ('low', 'medium', 'high', 'urgent'));

ALTER TABLE tasks ADD CONSTRAINT chk_tasks_priority 
    CHECK (priority IN ('low', 'medium', 'high', 'urgent'));

-- Date constraints
ALTER TABLE orders ADD CONSTRAINT chk_orders_delivery_date 
    CHECK (delivery_date >= order_date);

ALTER TABLE production_orders ADD CONSTRAINT chk_production_dates 
    CHECK (planned_end_date >= planned_start_date);

-- Quantity constraints
ALTER TABLE order_items ADD CONSTRAINT chk_order_items_quantity 
    CHECK (quantity > 0);

ALTER TABLE materials ADD CONSTRAINT chk_materials_stock 
    CHECK (current_stock >= 0 AND minimum_stock >= 0);
```

### Triggers for Audit Trail

```sql
-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to key tables
CREATE TRIGGER audit_users BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_orders BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_tasks BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

---

## Sample Data

### Roles
```sql
INSERT INTO roles (name, description, permissions) VALUES
('Administrator', 'Full system access', '["*"]'),
('Merchandiser', 'Order and customer management', '["orders:*", "customers:*", "reports:read"]'),
('Production Manager', 'Production oversight', '["production:*", "tasks:*", "quality:read"]'),
('Sewing Supervisor', 'Sewing department management', '["tasks:read", "tasks:update", "production:read"]'),
('Quality Controller', 'Quality inspections', '["quality:*", "production:read"]'),
('Operator', 'Task execution', '["tasks:read", "tasks:update_own"]');
```

### Departments
```sql
INSERT INTO departments (name, code, description) VALUES
('Merchandising', 'MERCH', 'Customer orders and planning'),
('Cutting', 'CUT', 'Fabric cutting operations'),
('Sewing', 'SEW', 'Garment sewing operations'),
('Finishing', 'FIN', 'Final garment finishing'),
('Quality Control', 'QC', 'Quality assurance and control'),
('Warehouse', 'WH', 'Inventory and shipping');
```

---

*This database design provides a robust foundation for the Cloth Manufacturing App with proper normalization, constraints, and performance optimization.*
