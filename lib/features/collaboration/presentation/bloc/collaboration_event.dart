part of 'collaboration_bloc.dart';

/// Base collaboration event
abstract class CollaborationEvent extends Equatable {
  const CollaborationEvent();

  @override
  List<Object?> get props => [];
}

// Workspace Events

/// Get workspaces requested
class GetWorkspacesRequested extends CollaborationEvent {
  final WorkspaceFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetWorkspacesRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Get workspace by ID requested
class GetWorkspaceByIdRequested extends CollaborationEvent {
  final String workspaceId;

  const GetWorkspaceByIdRequested(this.workspaceId);

  @override
  List<Object?> get props => [workspaceId];
}

/// Create workspace requested
class CreateWorkspaceRequested extends CollaborationEvent {
  final CreateWorkspaceRequest request;

  const CreateWorkspaceRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update workspace requested
class UpdateWorkspaceRequested extends CollaborationEvent {
  final UpdateWorkspaceRequest request;

  const UpdateWorkspaceRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete workspace requested
class DeleteWorkspaceRequested extends CollaborationEvent {
  final String workspaceId;

  const DeleteWorkspaceRequested(this.workspaceId);

  @override
  List<Object?> get props => [workspaceId];
}

/// Archive workspace requested
class ArchiveWorkspaceRequested extends CollaborationEvent {
  final String workspaceId;

  const ArchiveWorkspaceRequested(this.workspaceId);

  @override
  List<Object?> get props => [workspaceId];
}

/// Restore workspace requested
class RestoreWorkspaceRequested extends CollaborationEvent {
  final String workspaceId;

  const RestoreWorkspaceRequested(this.workspaceId);

  @override
  List<Object?> get props => [workspaceId];
}

// Member Events

/// Add member requested
class AddMemberRequested extends CollaborationEvent {
  final AddMemberRequest request;

  const AddMemberRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update member requested
class UpdateMemberRequested extends CollaborationEvent {
  final UpdateMemberRequest request;

  const UpdateMemberRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Remove member requested
class RemoveMemberRequested extends CollaborationEvent {
  final String workspaceId;
  final String userId;

  const RemoveMemberRequested({
    required this.workspaceId,
    required this.userId,
  });

  @override
  List<Object?> get props => [workspaceId, userId];
}

/// Get workspace members requested
class GetWorkspaceMembersRequested extends CollaborationEvent {
  final String workspaceId;
  final MemberFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetWorkspaceMembersRequested({
    required this.workspaceId,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [workspaceId, filter, pagination];
}

/// Invite member requested
class InviteMemberRequested extends CollaborationEvent {
  final InviteMemberRequest request;

  const InviteMemberRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Accept invitation requested
class AcceptInvitationRequested extends CollaborationEvent {
  final String invitationId;

  const AcceptInvitationRequested(this.invitationId);

  @override
  List<Object?> get props => [invitationId];
}

/// Decline invitation requested
class DeclineInvitationRequested extends CollaborationEvent {
  final String invitationId;

  const DeclineInvitationRequested(this.invitationId);

  @override
  List<Object?> get props => [invitationId];
}

// Channel Events

/// Get channels requested
class GetChannelsRequested extends CollaborationEvent {
  final String workspaceId;
  final ChannelFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetChannelsRequested({
    required this.workspaceId,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [workspaceId, filter, pagination];
}

/// Get channel by ID requested
class GetChannelByIdRequested extends CollaborationEvent {
  final String channelId;

  const GetChannelByIdRequested(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

/// Create channel requested
class CreateChannelRequested extends CollaborationEvent {
  final CreateChannelRequest request;

  const CreateChannelRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update channel requested
class UpdateChannelRequested extends CollaborationEvent {
  final UpdateChannelRequest request;

  const UpdateChannelRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete channel requested
class DeleteChannelRequested extends CollaborationEvent {
  final String channelId;

  const DeleteChannelRequested(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

/// Archive channel requested
class ArchiveChannelRequested extends CollaborationEvent {
  final String channelId;

  const ArchiveChannelRequested(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

/// Join channel requested
class JoinChannelRequested extends CollaborationEvent {
  final String channelId;

  const JoinChannelRequested(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

/// Leave channel requested
class LeaveChannelRequested extends CollaborationEvent {
  final String channelId;

  const LeaveChannelRequested(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

// Message Events

/// Get messages requested
class GetMessagesRequested extends CollaborationEvent {
  final String channelId;
  final MessageFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetMessagesRequested({
    required this.channelId,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [channelId, filter, pagination];
}

/// Get message by ID requested
class GetMessageByIdRequested extends CollaborationEvent {
  final String messageId;

  const GetMessageByIdRequested(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Send message requested
class SendMessageRequested extends CollaborationEvent {
  final SendMessageRequest request;

  const SendMessageRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Edit message requested
class EditMessageRequested extends CollaborationEvent {
  final EditMessageRequest request;

  const EditMessageRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete message requested
class DeleteMessageRequested extends CollaborationEvent {
  final String messageId;

  const DeleteMessageRequested(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Pin message requested
class PinMessageRequested extends CollaborationEvent {
  final String messageId;

  const PinMessageRequested(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Unpin message requested
class UnpinMessageRequested extends CollaborationEvent {
  final String messageId;

  const UnpinMessageRequested(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Add reaction requested
class AddReactionRequested extends CollaborationEvent {
  final AddReactionRequest request;

  const AddReactionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Remove reaction requested
class RemoveReactionRequested extends CollaborationEvent {
  final RemoveReactionRequest request;

  const RemoveReactionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Mark message as read requested
class MarkMessageAsReadRequested extends CollaborationEvent {
  final String messageId;

  const MarkMessageAsReadRequested(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Mark channel as read requested
class MarkChannelAsReadRequested extends CollaborationEvent {
  final String channelId;

  const MarkChannelAsReadRequested(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

// Thread Events

/// Get thread messages requested
class GetThreadMessagesRequested extends CollaborationEvent {
  final String parentMessageId;
  final PaginationParams? pagination;

  const GetThreadMessagesRequested({
    required this.parentMessageId,
    this.pagination,
  });

  @override
  List<Object?> get props => [parentMessageId, pagination];
}

/// Reply to thread requested
class ReplyToThreadRequested extends CollaborationEvent {
  final ReplyToThreadRequest request;

  const ReplyToThreadRequested(this.request);

  @override
  List<Object?> get props => [request];
}

// File Events

/// Upload file requested
class UploadFileRequested extends CollaborationEvent {
  final UploadFileRequest request;

  const UploadFileRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Get file download URL requested
class GetFileDownloadUrlRequested extends CollaborationEvent {
  final String attachmentId;

  const GetFileDownloadUrlRequested(this.attachmentId);

  @override
  List<Object?> get props => [attachmentId];
}

/// Delete file requested
class DeleteFileRequested extends CollaborationEvent {
  final String attachmentId;

  const DeleteFileRequested(this.attachmentId);

  @override
  List<Object?> get props => [attachmentId];
}

/// Get channel files requested
class GetChannelFilesRequested extends CollaborationEvent {
  final String channelId;
  final FileFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetChannelFilesRequested({
    required this.channelId,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [channelId, filter, pagination];
}

// Real-time Events

/// Update user presence requested
class UpdateUserPresenceRequested extends CollaborationEvent {
  final UpdatePresenceRequest request;

  const UpdateUserPresenceRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Send typing indicator requested
class SendTypingIndicatorRequested extends CollaborationEvent {
  final TypingIndicatorRequest request;

  const SendTypingIndicatorRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Start message stream requested
class StartMessageStreamRequested extends CollaborationEvent {
  final String channelId;

  const StartMessageStreamRequested(this.channelId);

  @override
  List<Object?> get props => [channelId];
}

/// Stop message stream requested
class StopMessageStreamRequested extends CollaborationEvent {
  const StopMessageStreamRequested();
}

/// Start activity stream requested
class StartActivityStreamRequested extends CollaborationEvent {
  final String workspaceId;

  const StartActivityStreamRequested(this.workspaceId);

  @override
  List<Object?> get props => [workspaceId];
}

/// Stop activity stream requested
class StopActivityStreamRequested extends CollaborationEvent {
  const StopActivityStreamRequested();
}

/// Start members stream requested
class StartMembersStreamRequested extends CollaborationEvent {
  final String workspaceId;

  const StartMembersStreamRequested(this.workspaceId);

  @override
  List<Object?> get props => [workspaceId];
}

/// Stop members stream requested
class StopMembersStreamRequested extends CollaborationEvent {
  const StopMembersStreamRequested();
}

/// Message received from stream
class MessageReceivedFromStream extends CollaborationEvent {
  final CollaborationMessage message;

  const MessageReceivedFromStream(this.message);

  @override
  List<Object?> get props => [message];
}

/// Activity received from stream
class ActivityReceivedFromStream extends CollaborationEvent {
  final RealTimeActivity activity;

  const ActivityReceivedFromStream(this.activity);

  @override
  List<Object?> get props => [activity];
}

/// Members updated from stream
class MembersUpdatedFromStream extends CollaborationEvent {
  final List<WorkspaceMember> members;

  const MembersUpdatedFromStream(this.members);

  @override
  List<Object?> get props => [members];
}

// Session Events

/// Get sessions requested
class GetSessionsRequested extends CollaborationEvent {
  final String workspaceId;
  final SessionFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetSessionsRequested({
    required this.workspaceId,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [workspaceId, filter, pagination];
}

/// Get session by ID requested
class GetSessionByIdRequested extends CollaborationEvent {
  final String sessionId;

  const GetSessionByIdRequested(this.sessionId);

  @override
  List<Object?> get props => [sessionId];
}

/// Create session requested
class CreateSessionRequested extends CollaborationEvent {
  final CreateSessionRequest request;

  const CreateSessionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update session requested
class UpdateSessionRequested extends CollaborationEvent {
  final UpdateSessionRequest request;

  const UpdateSessionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete session requested
class DeleteSessionRequested extends CollaborationEvent {
  final String sessionId;

  const DeleteSessionRequested(this.sessionId);

  @override
  List<Object?> get props => [sessionId];
}

/// Start session requested
class StartSessionRequested extends CollaborationEvent {
  final String sessionId;

  const StartSessionRequested(this.sessionId);

  @override
  List<Object?> get props => [sessionId];
}

/// End session requested
class EndSessionRequested extends CollaborationEvent {
  final String sessionId;

  const EndSessionRequested(this.sessionId);

  @override
  List<Object?> get props => [sessionId];
}

/// Join session requested
class JoinSessionRequested extends CollaborationEvent {
  final JoinSessionRequest request;

  const JoinSessionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Leave session requested
class LeaveSessionRequested extends CollaborationEvent {
  final String sessionId;

  const LeaveSessionRequested(this.sessionId);

  @override
  List<Object?> get props => [sessionId];
}

/// Update participant requested
class UpdateParticipantRequested extends CollaborationEvent {
  final UpdateParticipantRequest request;

  const UpdateParticipantRequested(this.request);

  @override
  List<Object?> get props => [request];
}

// Search Events

/// Search workspaces requested
class SearchWorkspacesRequested extends CollaborationEvent {
  final String query;
  final WorkspaceFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchWorkspacesRequested({
    required this.query,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Search messages requested
class SearchMessagesRequested extends CollaborationEvent {
  final String query;
  final String workspaceId;
  final String? channelId;
  final MessageFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchMessagesRequested({
    required this.query,
    required this.workspaceId,
    this.channelId,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, workspaceId, channelId, filter, pagination];
}

/// Search files requested
class SearchFilesRequested extends CollaborationEvent {
  final String query;
  final String workspaceId;
  final String? channelId;
  final FileFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchFilesRequested({
    required this.query,
    required this.workspaceId,
    this.channelId,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, workspaceId, channelId, filter, pagination];
}

// Analytics Events

/// Get workspace analytics requested
class GetWorkspaceAnalyticsRequested extends CollaborationEvent {
  final String workspaceId;
  final DateTime startDate;
  final DateTime endDate;

  const GetWorkspaceAnalyticsRequested({
    required this.workspaceId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [workspaceId, startDate, endDate];
}

/// Get channel analytics requested
class GetChannelAnalyticsRequested extends CollaborationEvent {
  final String channelId;
  final DateTime startDate;
  final DateTime endDate;

  const GetChannelAnalyticsRequested({
    required this.channelId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [channelId, startDate, endDate];
}

/// Get user activity analytics requested
class GetUserActivityAnalyticsRequested extends CollaborationEvent {
  final String userId;
  final DateTime startDate;
  final DateTime endDate;

  const GetUserActivityAnalyticsRequested({
    required this.userId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [userId, startDate, endDate];
}

// Notification Events

/// Get notifications requested
class GetNotificationsRequested extends CollaborationEvent {
  final NotificationFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetNotificationsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Mark notification as read requested
class MarkNotificationAsReadRequested extends CollaborationEvent {
  final String activityId;

  const MarkNotificationAsReadRequested(this.activityId);

  @override
  List<Object?> get props => [activityId];
}

/// Mark all notifications as read requested
class MarkAllNotificationsAsReadRequested extends CollaborationEvent {
  const MarkAllNotificationsAsReadRequested();
}

/// Update notification settings requested
class UpdateNotificationSettingsRequested extends CollaborationEvent {
  final UpdateNotificationSettingsRequest request;

  const UpdateNotificationSettingsRequested(this.request);

  @override
  List<Object?> get props => [request];
}
