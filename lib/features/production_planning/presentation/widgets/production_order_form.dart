import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hm_collection/core/theme/app_colors.dart';
import 'package:hm_collection/core/widgets/custom_text_field.dart';
import 'package:hm_collection/features/production_planning/presentation/bloc/production_bloc.dart';

import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../domain/entities/production_entities.dart';

class ProductionOrderForm extends StatefulWidget {
  final ProductionOrder? initialOrder;
  final Function(ProductionOrder order) onSave;

  const ProductionOrderForm({
    super.key,
    this.initialOrder,
    required this.onSave,
  });

  @override
  State<ProductionOrderForm> createState() => _ProductionOrderFormState();
}

class _ProductionOrderFormState extends State<ProductionOrderForm> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _orderNumberController;
  late TextEditingController _clientNameController;
  late TextEditingController _totalQuantityController;
  late ProductionStatus _status;
  late ProductionPriority _priority;
  late DateTime _plannedStartDate;
  late DateTime _plannedEndDate;

  @override
  void initState() {
    super.initState();
    _orderNumberController = TextEditingController(text: widget.initialOrder?.orderNumber ?? '');
    _clientNameController = TextEditingController(text: widget.initialOrder?.clientName ?? '');
    _totalQuantityController = TextEditingController(text: widget.initialOrder?.totalQuantity.toString() ?? '');
    _status = widget.initialOrder?.status ?? ProductionStatus.planned;
    _priority = widget.initialOrder?.priority ?? ProductionPriority.normal;
    _plannedStartDate = widget.initialOrder?.plannedStartDate ?? DateTime.now();
    _plannedEndDate = widget.initialOrder?.plannedEndDate ?? DateTime.now().add(const Duration(days: 7));
  }

  @override
  void dispose() {
    _orderNumberController.dispose();
    _clientNameController.dispose();
    _totalQuantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProductionBloc, ProductionState>(
      listener: (context, state) {
        if (state is ProductionOrderCreated || state is ProductionOrderUpdated) {
          Navigator.of(context).pop();
        } else if (state is ProductionError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomTextField(
                controller: _orderNumberController,
                label: 'Order Number',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an order number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _clientNameController,
                label: 'Client Name',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a client name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _totalQuantityController,
                label: 'Total Quantity',
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || int.tryParse(value) == null) {
                    return 'Please enter a valid quantity';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildStatusDropdown(),
              const SizedBox(height: 16),
              _buildPriorityDropdown(),
              const SizedBox(height: 16),
              _buildDatePicker(
                context,
                label: 'Planned Start Date',
                selectedDate: _plannedStartDate,
                onDateSelected: (date) => setState(() => _plannedStartDate = date),
              ),
              const SizedBox(height: 16),
              _buildDatePicker(
                context,
                label: 'Planned End Date',
                selectedDate: _plannedEndDate,
                onDateSelected: (date) => setState(() => _plannedEndDate = date),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                child: const Center(child: Text('Save Order')),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusDropdown() {
    return DropdownButtonFormField<ProductionStatus>(
      value: _status,
      decoration: const InputDecoration(
        labelText: 'Status',
        border: OutlineInputBorder(),
      ),
      items: ProductionStatus.values.map((status) {
        return DropdownMenuItem(
          value: status,
          child: Text(status.displayName),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _status = value;
          });
        }
      },
    );
  }

  Widget _buildPriorityDropdown() {
    return DropdownButtonFormField<ProductionPriority>(
      value: _priority,
      decoration: const InputDecoration(
        labelText: 'Priority',
        border: OutlineInputBorder(),
      ),
      items: ProductionPriority.values.map((priority) {
        return DropdownMenuItem(
          value: priority,
          child: Text(priority.displayName),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _priority = value;
          });
        }
      },
    );
  }

  Widget _buildDatePicker(
    BuildContext context,
    {
      required String label,
      required DateTime selectedDate,
      required ValueChanged<DateTime> onDateSelected,
    }
  ) {
    return InkWell(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: selectedDate,
          firstDate: DateTime(2000),
          lastDate: DateTime(2101),
        );
        if (pickedDate != null && pickedDate != selectedDate) {
          onDateSelected(pickedDate);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(MaterialLocalizations.of(context).formatShortDate(selectedDate)),
            const Icon(Icons.calendar_today),
          ],
        ),
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final updatedOrder = ProductionOrder(
        id: widget.initialOrder?.id ?? '',
        productionOrderNumber: widget.initialOrder?.productionOrderNumber ?? '',
        manufacturingOrderId: widget.initialOrder?.manufacturingOrderId ?? '',
        orderNumber: _orderNumberController.text,
        clientName: _clientNameController.text,
        status: _status,
        priority: _priority,
        plannedStartDate: _plannedStartDate,
        plannedEndDate: _plannedEndDate,
        totalQuantity: int.parse(_totalQuantityController.text),
        // These fields are not editable in this form, so we preserve their initial values
        tasks: widget.initialOrder?.tasks ?? [],
        resourceAllocations: widget.initialOrder?.resourceAllocations ?? [],
        metrics: widget.initialOrder?.metrics ?? const ProductionMetrics(
          plannedEfficiency: 1.0,
          actualEfficiency: 0.0,
          qualityScore: 100.0,
          costVariance: 0.0,
          scheduleVariance: 0.0, efficiency: 0, onTimeDelivery: 0, resourceUtilization: 0,
        ),
        currentDepartment: widget.initialOrder?.currentDepartment ?? DepartmentType.cutting,
        createdAt: widget.initialOrder?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );
      widget.onSave(updatedOrder);
    }
  }
}
