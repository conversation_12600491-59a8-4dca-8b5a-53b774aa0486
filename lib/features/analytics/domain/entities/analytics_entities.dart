import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Analytics dashboard entity
class AnalyticsDashboard extends BaseEntity {
  final String dashboardCode;
  final String dashboardName;
  final String description;
  final DashboardType type;
  final CommonStatus status;
  final String ownerId;
  final String ownerName;
  final List<String> sharedWithUserIds;
  final List<String> sharedWithRoleIds;
  final DashboardLayout layout;
  final List<DashboardWidget> widgets;
  final DashboardSettings settings;
  final Map<String, dynamic> filters;
  final DateTime? lastViewedAt;
  final int viewCount;
  final bool isDefault;
  final bool isPublic;

  const AnalyticsDashboard({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.dashboardCode,
    required this.dashboardName,
    required this.description,
    required this.type,
    required this.status,
    required this.ownerId,
    required this.ownerName,
    this.sharedWithUserIds = const [],
    this.sharedWithRoleIds = const [],
    required this.layout,
    this.widgets = const [],
    required this.settings,
    this.filters = const {},
    this.lastViewedAt,
    this.viewCount = 0,
    this.isDefault = false,
    this.isPublic = false,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        dashboardCode,
        dashboardName,
        description,
        type,
        status,
        ownerId,
        ownerName,
        sharedWithUserIds,
        sharedWithRoleIds,
        layout,
        widgets,
        settings,
        filters,
        lastViewedAt,
        viewCount,
        isDefault,
        isPublic,
      ];

  /// Check if dashboard is active
  bool get isActive => status == CommonStatus.active;

  /// Get widget count
  int get widgetCount => widgets.length;

  /// Check if user has access
  bool hasAccess(String userId, List<String> userRoles) {
    if (ownerId == userId) return true;
    if (isPublic) return true;
    if (sharedWithUserIds.contains(userId)) return true;
    return userRoles.any((role) => sharedWithRoleIds.contains(role));
  }
}

/// Dashboard layout configuration
class DashboardLayout extends Equatable {
  final int columns;
  final int rows;
  final double spacing;
  final CustomEdgeInsets padding;
  final bool autoResize;
  final LayoutType layoutType;

  const DashboardLayout({
    required this.columns,
    required this.rows,
    required this.spacing,
    required this.padding,
    this.autoResize = true,
    this.layoutType = LayoutType.grid,
  });

  @override
  List<Object?> get props => [
        columns,
        rows,
        spacing,
        padding,
        autoResize,
        layoutType,
      ];
}

/// Edge insets for layout
class CustomEdgeInsets extends Equatable {
  final double top;
  final double right;
  final double bottom;
  final double left;

  const CustomEdgeInsets({
    required this.top,
    required this.right,
    required this.bottom,
    required this.left,
  });

  const CustomEdgeInsets.all(double value)
      : top = value,
        right = value,
        bottom = value,
        left = value;

  factory CustomEdgeInsets.only({
    double top = 0.0,
    double right = 0.0,
    double bottom = 0.0,
    double left = 0.0,
  }) =>
      CustomEdgeInsets(
        top: top,
        right: right,
        bottom: bottom,
        left: left,
      );

  const CustomEdgeInsets.symmetric({
    double vertical = 0.0,
    double horizontal = 0.0,
  })  : top = vertical,
        right = horizontal,
        bottom = vertical,
        left = horizontal;

  @override
  List<Object?> get props => [top, right, bottom, left];
}

/// Dashboard widget configuration
class DashboardWidget extends Equatable {
  final String widgetId;
  final String widgetType;
  final String title;
  final String? description;
  final WidgetPosition position;
  final WidgetSize size;
  final WidgetConfiguration configuration;
  final Map<String, dynamic> dataSource;
  final WidgetRefreshSettings refreshSettings;
  final bool isVisible;
  final int order;

  const DashboardWidget({
    required this.widgetId,
    required this.widgetType,
    required this.title,
    this.description,
    required this.position,
    required this.size,
    required this.configuration,
    this.dataSource = const {},
    required this.refreshSettings,
    this.isVisible = true,
    this.order = 0,
  });

  @override
  List<Object?> get props => [
        widgetId,
        widgetType,
        title,
        description,
        position,
        size,
        configuration,
        dataSource,
        refreshSettings,
        isVisible,
        order,
      ];
}

/// Widget position on dashboard
class WidgetPosition extends Equatable {
  final int x;
  final int y;

  const WidgetPosition({
    required this.x,
    required this.y,
  });

  @override
  List<Object?> get props => [x, y];
}

/// Widget size configuration
class WidgetSize extends Equatable {
  final int width;
  final int height;
  final bool isResizable;

  const WidgetSize({
    required this.width,
    required this.height,
    this.isResizable = true,
  });

  @override
  List<Object?> get props => [width, height, isResizable];
}

/// Widget configuration settings
class WidgetConfiguration extends Equatable {
  final Map<String, dynamic> chartConfig;
  final Map<String, dynamic> styleConfig;
  final Map<String, dynamic> dataConfig;
  final List<String> metrics;
  final List<String> dimensions;
  final Map<String, dynamic> filters;

  const WidgetConfiguration({
    this.chartConfig = const {},
    this.styleConfig = const {},
    this.dataConfig = const {},
    this.metrics = const [],
    this.dimensions = const [],
    this.filters = const {},
  });

  @override
  List<Object?> get props => [
        chartConfig,
        styleConfig,
        dataConfig,
        metrics,
        dimensions,
        filters,
      ];
}

/// Widget refresh settings
class WidgetRefreshSettings extends Equatable {
  final bool autoRefresh;
  final Duration refreshInterval;
  final DateTime? lastRefreshed;

  const WidgetRefreshSettings({
    this.autoRefresh = false,
    this.refreshInterval = const Duration(minutes: 5),
    this.lastRefreshed,
  });

  @override
  List<Object?> get props => [autoRefresh, refreshInterval, lastRefreshed];
}

/// Dashboard settings
class DashboardSettings extends Equatable {
  final bool autoRefresh;
  final Duration refreshInterval;
  final String theme;
  final bool showFilters;
  final bool showLegend;
  final bool allowExport;
  final bool allowSharing;
  final Map<String, dynamic> customSettings;

  const DashboardSettings({
    this.autoRefresh = true,
    this.refreshInterval = const Duration(minutes: 5),
    this.theme = 'light',
    this.showFilters = true,
    this.showLegend = true,
    this.allowExport = true,
    this.allowSharing = true,
    this.customSettings = const {},
  });

  @override
  List<Object?> get props => [
        autoRefresh,
        refreshInterval,
        theme,
        showFilters,
        showLegend,
        allowExport,
        allowSharing,
        customSettings,
      ];
}

/// Analytics report entity
class AnalyticsReport extends BaseEntity {
  final String reportCode;
  final String reportName;
  final String description;
  final AnalyticsReportType type;
  final ReportCategory category;
  final ReportStatus status;
  final String generatedBy;
  final DateTime? generatedAt;
  final DateTime? scheduledAt;
  final ReportParameters parameters;
  final ReportData data;
  final ReportSettings settings;
  final List<String> tags;
  final List<String> recipients;
  final bool isScheduled;
  final ScheduleConfiguration? scheduleConfig;

  const AnalyticsReport({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.reportCode,
    required this.reportName,
    required this.description,
    required this.type,
    required this.category,
    required this.status,
    required this.generatedBy,
    this.generatedAt,
    this.scheduledAt,
    required this.parameters,
    required this.data,
    required this.settings,
    this.tags = const [],
    this.recipients = const [],
    this.isScheduled = false,
    this.scheduleConfig,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        reportCode,
        reportName,
        description,
        type,
        category,
        status,
        generatedBy,
        generatedAt,
        scheduledAt,
        parameters,
        data,
        settings,
        tags,
        recipients,
        isScheduled,
        scheduleConfig,
      ];

  /// Check if report is ready
  bool get isReady => status == ReportStatus.completed;

  /// Check if report is in progress
  bool get isInProgress => status == ReportStatus.generating;

  /// Check if report failed
  bool get hasFailed => status == ReportStatus.failed;
}

/// Report parameters
class ReportParameters extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final List<String> departments;
  final List<String> costCenters;
  final List<String> products;
  final List<String> customers;
  final List<String> suppliers;
  final Map<String, dynamic> filters;
  final List<String> metrics;
  final List<String> dimensions;
  final String groupBy;
  final String sortBy;
  final SortOrder sortOrder;

  const ReportParameters({
    required this.startDate,
    required this.endDate,
    this.departments = const [],
    this.costCenters = const [],
    this.products = const [],
    this.customers = const [],
    this.suppliers = const [],
    this.filters = const {},
    this.metrics = const [],
    this.dimensions = const [],
    this.groupBy = '',
    this.sortBy = '',
    this.sortOrder = SortOrder.ascending,
  });

  @override
  List<Object?> get props => [
        startDate,
        endDate,
        departments,
        costCenters,
        products,
        customers,
        suppliers,
        filters,
        metrics,
        dimensions,
        groupBy,
        sortBy,
        sortOrder,
      ];
}

/// Report data container
class ReportData extends Equatable {
  final Map<String, dynamic> summary;
  final List<ReportSection> sections;
  final List<ChartData> charts;
  final List<TableData> tables;
  final Map<String, dynamic> metadata;

  const ReportData({
    this.summary = const {},
    this.sections = const [],
    this.charts = const [],
    this.tables = const [],
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [summary, sections, charts, tables, metadata];
}

/// Report section
class ReportSection extends Equatable {
  final String sectionId;
  final String title;
  final String description;
  final SectionType type;
  final List<ReportItem> items;
  final Map<String, dynamic> configuration;

  const ReportSection({
    required this.sectionId,
    required this.title,
    required this.description,
    required this.type,
    this.items = const [],
    this.configuration = const {},
  });

  @override
  List<Object?> get props => [
        sectionId,
        title,
        description,
        type,
        items,
        configuration,
      ];
}

/// Report item
class ReportItem extends Equatable {
  final String itemId;
  final String label;
  final dynamic value;
  final String? unit;
  final String? format;
  final dynamic previousValue;
  final double? changePercentage;
  final TrendDirection? trend;
  final Map<String, dynamic> attributes;

  const ReportItem({
    required this.itemId,
    required this.label,
    required this.value,
    this.unit,
    this.format,
    this.previousValue,
    this.changePercentage,
    this.trend,
    this.attributes = const {},
  });

  @override
  List<Object?> get props => [
        itemId,
        label,
        value,
        unit,
        format,
        previousValue,
        changePercentage,
        trend,
        attributes,
      ];
}

/// Chart data for analytics
class ChartData extends Equatable {
  final String chartId;
  final String title;
  final ChartType chartType;
  final List<DataSeries> series;
  final List<String> categories;
  final ChartConfiguration configuration;

  const ChartData({
    required this.chartId,
    required this.title,
    required this.chartType,
    this.series = const [],
    this.categories = const [],
    required this.configuration,
  });

  @override
  List<Object?> get props => [
        chartId,
        title,
        chartType,
        series,
        categories,
        configuration,
      ];
}

/// Data series for charts
class DataSeries extends Equatable {
  final String seriesId;
  final String name;
  final List<DataPoint> dataPoints;
  final String color;
  final SeriesType type;

  const DataSeries({
    required this.seriesId,
    required this.name,
    this.dataPoints = const [],
    required this.color,
    required this.type,
  });

  @override
  List<Object?> get props => [seriesId, name, dataPoints, color, type];
}

/// Data point for series
class DataPoint extends Equatable {
  final String? label;
  final double value;
  final DateTime? timestamp;
  final Map<String, dynamic> metadata;

  const DataPoint({
    this.label,
    required this.value,
    this.timestamp,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [label, value, timestamp, metadata];
}

/// Table data for reports
class TableData extends Equatable {
  final String tableId;
  final String title;
  final List<TableColumn> columns;
  final List<AnalyticsTableRow> rows;
  final TableConfiguration configuration;

  const TableData({
    required this.tableId,
    required this.title,
    this.columns = const [],
    this.rows = const [],
    required this.configuration,
  });

  @override
  List<Object?> get props => [tableId, title, columns, rows, configuration];
}

/// Table column definition
class TableColumn extends Equatable {
  final String columnId;
  final String header;
  final String dataType;
  final bool sortable;
  final bool filterable;
  final String? format;
  final double? width;

  const TableColumn({
    required this.columnId,
    required this.header,
    required this.dataType,
    this.sortable = true,
    this.filterable = true,
    this.format,
    this.width,
  });

  @override
  List<Object?> get props => [
        columnId,
        header,
        dataType,
        sortable,
        filterable,
        format,
        width,
      ];
}

/// Analytics table row data
class AnalyticsTableRow extends Equatable {
  final String rowId;
  final Map<String, dynamic> cells;
  final Map<String, dynamic> metadata;

  const AnalyticsTableRow({
    required this.rowId,
    this.cells = const {},
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [rowId, cells, metadata];
}

/// Chart configuration
class ChartConfiguration extends Equatable {
  final Map<String, dynamic> colors;
  final Map<String, dynamic> axes;
  final Map<String, dynamic> legend;
  final Map<String, dynamic> tooltip;
  final Map<String, dynamic> animation;
  final bool responsive;

  const ChartConfiguration({
    this.colors = const {},
    this.axes = const {},
    this.legend = const {},
    this.tooltip = const {},
    this.animation = const {},
    this.responsive = true,
  });

  @override
  List<Object?> get props => [
        colors,
        axes,
        legend,
        tooltip,
        animation,
        responsive,
      ];
}

/// Table configuration
class TableConfiguration extends Equatable {
  final bool pagination;
  final int pageSize;
  final bool sorting;
  final bool filtering;
  final bool exportable;
  final Map<String, dynamic> styling;

  const TableConfiguration({
    this.pagination = true,
    this.pageSize = 25,
    this.sorting = true,
    this.filtering = true,
    this.exportable = true,
    this.styling = const {},
  });

  @override
  List<Object?> get props => [
        pagination,
        pageSize,
        sorting,
        filtering,
        exportable,
        styling,
      ];
}

/// Report settings
class ReportSettings extends Equatable {
  final String format;
  final String orientation;
  final bool includeCharts;
  final bool includeTables;
  final bool includeRawData;
  final Map<String, dynamic> styling;
  final Map<String, dynamic> branding;

  const ReportSettings({
    this.format = 'pdf',
    this.orientation = 'portrait',
    this.includeCharts = true,
    this.includeTables = true,
    this.includeRawData = false,
    this.styling = const {},
    this.branding = const {},
  });

  @override
  List<Object?> get props => [
        format,
        orientation,
        includeCharts,
        includeTables,
        includeRawData,
        styling,
        branding,
      ];
}

/// Schedule configuration for reports
class ScheduleConfiguration extends Equatable {
  final ScheduleFrequency frequency;
  final int interval;
  final List<int> daysOfWeek;
  final int dayOfMonth;
  final String timeOfDay;
  final String timezone;
  final DateTime? nextRun;
  final DateTime? lastRun;
  final bool isActive;

  const ScheduleConfiguration({
    required this.frequency,
    this.interval = 1,
    this.daysOfWeek = const [],
    this.dayOfMonth = 1,
    this.timeOfDay = '09:00',
    this.timezone = 'UTC',
    this.nextRun,
    this.lastRun,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [
        frequency,
        interval,
        daysOfWeek,
        dayOfMonth,
        timeOfDay,
        timezone,
        nextRun,
        lastRun,
        isActive,
      ];
}

// Enums

/// Dashboard type
enum DashboardType {
  executive,
  operational,
  financial,
  production,
  quality,
  inventory,
  sales,
  custom,
}

/// Dashboard type extension
extension DashboardTypeExtension on DashboardType {
  String get displayName {
    switch (this) {
      case DashboardType.executive:
        return 'Executive Dashboard';
      case DashboardType.operational:
        return 'Operational Dashboard';
      case DashboardType.financial:
        return 'Financial Dashboard';
      case DashboardType.production:
        return 'Production Dashboard';
      case DashboardType.quality:
        return 'Quality Dashboard';
      case DashboardType.inventory:
        return 'Inventory Dashboard';
      case DashboardType.sales:
        return 'Sales Dashboard';
      case DashboardType.custom:
        return 'Custom Dashboard';
    }
  }

  String get value => name;
}

/// Layout type
enum LayoutType {
  grid,
  freeform,
  masonry,
}

/// Layout type extension
extension LayoutTypeExtension on LayoutType {
  String get displayName {
    switch (this) {
      case LayoutType.grid:
        return 'Grid Layout';
      case LayoutType.freeform:
        return 'Freeform Layout';
      case LayoutType.masonry:
        return 'Masonry Layout';
    }
  }

  String get value => name;
}

/// Analytics report type
enum AnalyticsReportType {
  performance,
  financial,
  operational,
  quality,
  inventory,
  sales,
  production,
  efficiency,
  compliance,
  custom,
}

/// Analytics report type extension
extension AnalyticsReportTypeExtension on AnalyticsReportType {
  String get displayName {
    switch (this) {
      case AnalyticsReportType.performance:
        return 'Performance Report';
      case AnalyticsReportType.financial:
        return 'Financial Report';
      case AnalyticsReportType.operational:
        return 'Operational Report';
      case AnalyticsReportType.quality:
        return 'Quality Report';
      case AnalyticsReportType.inventory:
        return 'Inventory Report';
      case AnalyticsReportType.sales:
        return 'Sales Report';
      case AnalyticsReportType.production:
        return 'Production Report';
      case AnalyticsReportType.efficiency:
        return 'Efficiency Report';
      case AnalyticsReportType.compliance:
        return 'Compliance Report';
      case AnalyticsReportType.custom:
        return 'Custom Report';
    }
  }

  String get value => name;
}

/// Report category
enum ReportCategory {
  dashboard,
  scheduled,
  adhoc,
  regulatory,
  internal,
}

/// Report category extension
extension ReportCategoryExtension on ReportCategory {
  String get displayName {
    switch (this) {
      case ReportCategory.dashboard:
        return 'Dashboard Report';
      case ReportCategory.scheduled:
        return 'Scheduled Report';
      case ReportCategory.adhoc:
        return 'Ad-hoc Report';
      case ReportCategory.regulatory:
        return 'Regulatory Report';
      case ReportCategory.internal:
        return 'Internal Report';
    }
  }

  String get value => name;
}

/// Report status
enum ReportStatus {
  pending,
  generating,
  completed,
  failed,
  cancelled,
  scheduled,
}

/// Report status extension
extension ReportStatusExtension on ReportStatus {
  String get displayName {
    switch (this) {
      case ReportStatus.pending:
        return 'Pending';
      case ReportStatus.generating:
        return 'Generating';
      case ReportStatus.completed:
        return 'Completed';
      case ReportStatus.failed:
        return 'Failed';
      case ReportStatus.cancelled:
        return 'Cancelled';
      case ReportStatus.scheduled:
        return 'Scheduled';
    }
  }

  String get value => name;
}

/// Chart type
enum ChartType {
  line,
  bar,
  column,
  pie,
  donut,
  area,
  scatter,
  bubble,
  gauge,
  funnel,
  heatmap,
  treemap,
}

/// Chart type extension
extension ChartTypeExtension on ChartType {
  String get displayName {
    switch (this) {
      case ChartType.line:
        return 'Line Chart';
      case ChartType.bar:
        return 'Bar Chart';
      case ChartType.column:
        return 'Column Chart';
      case ChartType.pie:
        return 'Pie Chart';
      case ChartType.donut:
        return 'Donut Chart';
      case ChartType.area:
        return 'Area Chart';
      case ChartType.scatter:
        return 'Scatter Plot';
      case ChartType.bubble:
        return 'Bubble Chart';
      case ChartType.gauge:
        return 'Gauge Chart';
      case ChartType.funnel:
        return 'Funnel Chart';
      case ChartType.heatmap:
        return 'Heat Map';
      case ChartType.treemap:
        return 'Tree Map';
    }
  }

  String get value => name;
}

/// Series type
enum SeriesType {
  line,
  bar,
  area,
  scatter,
  bubble,
}

/// Series type extension
extension SeriesTypeExtension on SeriesType {
  String get displayName {
    switch (this) {
      case SeriesType.line:
        return 'Line Series';
      case SeriesType.bar:
        return 'Bar Series';
      case SeriesType.area:
        return 'Area Series';
      case SeriesType.scatter:
        return 'Scatter Series';
      case SeriesType.bubble:
        return 'Bubble Series';
    }
  }

  String get value => name;
}

/// Section type
enum SectionType {
  summary,
  chart,
  table,
  text,
  image,
  kpi,
}

/// Section type extension
extension SectionTypeExtension on SectionType {
  String get displayName {
    switch (this) {
      case SectionType.summary:
        return 'Summary Section';
      case SectionType.chart:
        return 'Chart Section';
      case SectionType.table:
        return 'Table Section';
      case SectionType.text:
        return 'Text Section';
      case SectionType.image:
        return 'Image Section';
      case SectionType.kpi:
        return 'KPI Section';
    }
  }

  String get value => name;
}

/// Trend direction
enum TrendDirection {
  up,
  down,
  stable,
  unknown,
}

/// Trend direction extension
extension TrendDirectionExtension on TrendDirection {
  String get displayName {
    switch (this) {
      case TrendDirection.up:
        return 'Increasing';
      case TrendDirection.down:
        return 'Decreasing';
      case TrendDirection.stable:
        return 'Stable';
      case TrendDirection.unknown:
        return 'Unknown';
    }
  }

  String get value => name;
}

/// Sort order
enum SortOrder {
  ascending,
  descending,
}

/// Sort order extension
extension SortOrderExtension on SortOrder {
  String get displayName {
    switch (this) {
      case SortOrder.ascending:
        return 'Ascending';
      case SortOrder.descending:
        return 'Descending';
    }
  }

  String get value => name;
}

/// Schedule frequency
enum ScheduleFrequency {
  hourly,
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
}

/// Schedule frequency extension
extension ScheduleFrequencyExtension on ScheduleFrequency {
  String get displayName {
    switch (this) {
      case ScheduleFrequency.hourly:
        return 'Hourly';
      case ScheduleFrequency.daily:
        return 'Daily';
      case ScheduleFrequency.weekly:
        return 'Weekly';
      case ScheduleFrequency.monthly:
        return 'Monthly';
      case ScheduleFrequency.quarterly:
        return 'Quarterly';
      case ScheduleFrequency.yearly:
        return 'Yearly';
    }
  }

  String get value => name;
}
