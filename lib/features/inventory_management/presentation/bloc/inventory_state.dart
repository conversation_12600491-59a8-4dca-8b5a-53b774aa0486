part of 'inventory_bloc.dart';

/// Base inventory state
abstract class InventoryState extends Equatable {
  const InventoryState();

  @override
  List<Object?> get props => [];
}

/// Initial inventory state
class InventoryInitial extends InventoryState {
  const InventoryInitial();
}

/// Inventory loading state
class InventoryLoading extends InventoryState {
  const InventoryLoading();
}

/// Inventory items loaded state
class InventoryItemsLoaded extends InventoryState {
  final List<InventoryItem> items;
  final Pagination? pagination;
  final InventoryFilterCriteria? filter;
  final bool isRefreshing;
  final List<String> selectedItemIds;
  final InventoryViewMode viewMode;
  final String? sortBy;
  final bool sortAscending;

  const InventoryItemsLoaded({
    required this.items,
    this.pagination,
    this.filter,
    this.isRefreshing = false,
    this.selectedItemIds = const [],
    this.viewMode = InventoryViewMode.list,
    this.sortBy,
    this.sortAscending = true,
  });

  /// Copy with new values
  InventoryItemsLoaded copyWith({
    List<InventoryItem>? items,
    Pagination? pagination,
    InventoryFilterCriteria? filter,
    bool? isRefreshing,
    List<String>? selectedItemIds,
    InventoryViewMode? viewMode,
    String? sortBy,
    bool? sortAscending,
  }) {
    return InventoryItemsLoaded(
      items: items ?? this.items,
      pagination: pagination ?? this.pagination,
      filter: filter ?? this.filter,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      selectedItemIds: selectedItemIds ?? this.selectedItemIds,
      viewMode: viewMode ?? this.viewMode,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  List<Object?> get props => [
        items,
        pagination,
        filter,
        isRefreshing,
        selectedItemIds,
        viewMode,
        sortBy,
        sortAscending,
      ];

  /// Check if item is selected
  bool isItemSelected(String itemId) {
    return selectedItemIds.contains(itemId);
  }

  /// Check if all items are selected
  bool get areAllItemsSelected {
    return items.isNotEmpty && selectedItemIds.length == items.length;
  }

  /// Check if any items are selected
  bool get hasSelectedItems {
    return selectedItemIds.isNotEmpty;
  }

  /// Get selected items
  List<InventoryItem> get selectedItems {
    return items.where((item) => selectedItemIds.contains(item.id)).toList();
  }

  /// Get low stock items count
  int get lowStockItemsCount {
    return items.where((item) => item.isLowStock).length;
  }

  /// Get out of stock items count
  int get outOfStockItemsCount {
    return items.where((item) => item.isOutOfStock).length;
  }

  /// Get expiring items count
  int get expiringItemsCount {
    return items.where((item) => item.isExpiringSoon).length;
  }
}

/// Inventory item details loaded state
class InventoryItemDetailsLoaded extends InventoryState {
  final InventoryItem item;

  const InventoryItemDetailsLoaded(this.item);

  @override
  List<Object?> get props => [item];
}

/// Inventory item created state
class InventoryItemCreated extends InventoryState {
  final InventoryItem item;

  const InventoryItemCreated(this.item);

  @override
  List<Object?> get props => [item];
}

/// Inventory item updated state
class InventoryItemUpdated extends InventoryState {
  final InventoryItem item;

  const InventoryItemUpdated(this.item);

  @override
  List<Object?> get props => [item];
}

/// Inventory item deleted state
class InventoryItemDeleted extends InventoryState {
  final String itemId;

  const InventoryItemDeleted(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

/// Stock level updated state
class StockLevelUpdated extends InventoryState {
  final InventoryItem item;

  const StockLevelUpdated(this.item);

  @override
  List<Object?> get props => [item];
}

/// Stock adjusted state
class StockAdjusted extends InventoryState {
  final StockMovement movement;

  const StockAdjusted(this.movement);

  @override
  List<Object?> get props => [movement];
}

/// Stock reserved state
class StockReserved extends InventoryState {
  final InventoryItem item;

  const StockReserved(this.item);

  @override
  List<Object?> get props => [item];
}

/// Reserved stock released state
class ReservedStockReleased extends InventoryState {
  final InventoryItem item;

  const ReservedStockReleased(this.item);

  @override
  List<Object?> get props => [item];
}

/// Stock movements loaded state
class StockMovementsLoaded extends InventoryState {
  final List<StockMovement> movements;
  final Pagination? pagination;
  final StockMovementFilterCriteria? filter;

  const StockMovementsLoaded({
    required this.movements,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [movements, pagination, filter];
}

/// Stock movements for item loaded state
class StockMovementsForItemLoaded extends InventoryState {
  final String itemId;
  final List<StockMovement> movements;
  final Pagination? pagination;

  const StockMovementsForItemLoaded({
    required this.itemId,
    required this.movements,
    this.pagination,
  });

  @override
  List<Object?> get props => [itemId, movements, pagination];
}

/// Low stock items loaded state
class LowStockItemsLoaded extends InventoryState {
  final List<InventoryItem> items;
  final Pagination? pagination;

  const LowStockItemsLoaded({
    required this.items,
    this.pagination,
  });

  @override
  List<Object?> get props => [items, pagination];
}

/// Out of stock items loaded state
class OutOfStockItemsLoaded extends InventoryState {
  final List<InventoryItem> items;
  final Pagination? pagination;

  const OutOfStockItemsLoaded({
    required this.items,
    this.pagination,
  });

  @override
  List<Object?> get props => [items, pagination];
}

/// Overstocked items loaded state
class OverstockedItemsLoaded extends InventoryState {
  final List<InventoryItem> items;
  final Pagination? pagination;

  const OverstockedItemsLoaded({
    required this.items,
    this.pagination,
  });

  @override
  List<Object?> get props => [items, pagination];
}

/// Expiring items loaded state
class ExpiringItemsLoaded extends InventoryState {
  final List<InventoryItem> items;
  final Pagination? pagination;
  final int daysAhead;

  const ExpiringItemsLoaded({
    required this.items,
    this.pagination,
    required this.daysAhead,
  });

  @override
  List<Object?> get props => [items, pagination, daysAhead];
}

/// Inventory items searched state
class InventoryItemsSearched extends InventoryState {
  final List<InventoryItem> items;
  final Pagination? pagination;
  final String query;
  final InventoryFilterCriteria? filter;

  const InventoryItemsSearched({
    required this.items,
    this.pagination,
    required this.query,
    this.filter,
  });

  @override
  List<Object?> get props => [items, pagination, query, filter];
}

/// Inventory items filtered state
class InventoryItemsFiltered extends InventoryState {
  final List<InventoryItem> items;
  final Pagination? pagination;
  final InventoryFilterCriteria filter;

  const InventoryItemsFiltered({
    required this.items,
    this.pagination,
    required this.filter,
  });

  @override
  List<Object?> get props => [items, pagination, filter];
}

/// Inventory statistics loaded state
class InventoryStatisticsLoaded extends InventoryState {
  final InventoryStatistics statistics;

  const InventoryStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Inventory valuation loaded state
class InventoryValuationLoaded extends InventoryState {
  final InventoryValuation valuation;

  const InventoryValuationLoaded(this.valuation);

  @override
  List<Object?> get props => [valuation];
}

/// Reorder suggestions generated state
class ReorderSuggestionsGenerated extends InventoryState {
  final List<ReorderSuggestion> suggestions;

  const ReorderSuggestionsGenerated(this.suggestions);

  @override
  List<Object?> get props => [suggestions];
}

/// Automatic reorder created state
class AutomaticReorderCreated extends InventoryState {
  final PurchaseOrder purchaseOrder;

  const AutomaticReorderCreated(this.purchaseOrder);

  @override
  List<Object?> get props => [purchaseOrder];
}

/// Inventory error state
class InventoryError extends InventoryState {
  final String message;

  const InventoryError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Inventory validation error state
class InventoryValidationError extends InventoryState {
  final Map<String, String> errors;

  const InventoryValidationError(this.errors);

  @override
  List<Object?> get props => [errors];
}

/// Inventory operation success state
class InventoryOperationSuccess extends InventoryState {
  final String message;

  const InventoryOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Inventory selection changed state
class InventorySelectionChanged extends InventoryState {
  final List<String> selectedItemIds;

  const InventorySelectionChanged(this.selectedItemIds);

  @override
  List<Object?> get props => [selectedItemIds];
}

/// Inventory view mode changed state
class InventoryViewModeChanged extends InventoryState {
  final InventoryViewMode viewMode;

  const InventoryViewModeChanged(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Inventory items sorted state
class InventoryItemsSorted extends InventoryState {
  final String sortBy;
  final bool ascending;

  const InventoryItemsSorted(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Inventory loading more state
class InventoryLoadingMore extends InventoryState {
  const InventoryLoadingMore();
}

/// Inventory refreshing state
class InventoryRefreshing extends InventoryState {
  const InventoryRefreshing();
}
