@echo off
setlocal enabledelayedexpansion

REM HM Collection Development Server Script for Windows
REM This script starts a development server for the Flutter web application

echo.
echo 🚀 Starting HM Collection Development Server...
echo.

REM Check if Flutter is installed
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Flutter is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "pubspec.yaml" (
    echo [ERROR] pubspec.yaml not found. Please run this script from the project root.
    pause
    exit /b 1
)

echo [INFO] Flutter version:
flutter --version
echo.

REM Get dependencies
echo [INFO] Getting dependencies...
flutter pub get

if %errorlevel% neq 0 (
    echo [ERROR] Failed to get dependencies
    pause
    exit /b 1
)

REM Check for web support
echo [INFO] Checking web support...
flutter devices | findstr "Chrome" >nul
if %errorlevel% neq 0 (
    echo [WARNING] Chrome not detected. Installing Chrome or using another browser is recommended.
)

REM Set default port and host
set PORT=%1
if "%PORT%"=="" set PORT=3000

set HOST=%2
if "%HOST%"=="" set HOST=localhost

echo [INFO] Starting development server...
echo [INFO] Host: %HOST%
echo [INFO] Port: %PORT%
echo [INFO] URL: http://%HOST%:%PORT%
echo.

REM Start the development server
echo [SUCCESS] 🌐 Development server starting...
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Run Flutter web server
flutter run -d chrome --web-hostname=%HOST% --web-port=%PORT%

echo [SUCCESS] Development server stopped.
pause
