import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../bloc/munda_bloc.dart';
import '../bloc/munda_event.dart';
import '../bloc/munda_state.dart';

/// Widget that displays real-time summary of Munda records
class MundaSummaryCard extends StatefulWidget {
  final bool showRefreshButton;
  final EdgeInsets? padding;

  const MundaSummaryCard({
    super.key,
    this.showRefreshButton = true,
    this.padding,
  });

  @override
  State<MundaSummaryCard> createState() => _MundaSummaryCardState();
}

class _MundaSummaryCardState extends State<MundaSummaryCard> {
  @override
  void initState() {
    super.initState();
    _loadSummary();
  }

  void _loadSummary() {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthAuthenticated) {
      context.read<MundaBloc>().add(
        LoadRecordsSummary(userId: authState.user.id),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),
          const SizedBox(height: 20),
          
          // Summary content
          BlocBuilder<MundaBloc, MundaState>(
            builder: (context, state) {
              if (state is MundaLoading) {
                return _buildLoadingState();
              } else if (state is RecordsLoaded && state.summary != null) {
                return _buildSummaryContent(state.summary!);
              } else if (state is MundaError) {
                return _buildErrorState(state);
              } else {
                return _buildEmptyState();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.summarize,
            color: Colors.white,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Today\'s Summary',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Real-time calculations',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
        if (widget.showRefreshButton)
          IconButton(
            onPressed: _loadSummary,
            icon: const Icon(
              Icons.refresh,
              color: Colors.white,
              size: 20,
            ),
            tooltip: 'Refresh Summary',
          ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Colors.white.withOpacity(0.8),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Loading summary...',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildPlaceholderStats(),
      ],
    );
  }

  Widget _buildSummaryContent(dynamic summary) {
    return Column(
      children: [
        // Main stats row
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'Total Rolls',
                summary.totalRolls.toString(),
                Icons.inventory_2,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatItem(
                'Total Pieces',
                summary.totalPieces.toString(),
                Icons.straighten,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatItem(
                'Total Amount',
                '₹${summary.totalAmount.toStringAsFixed(2)}',
                Icons.currency_rupee,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Additional stats
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildMiniStat(
                'Avg Rate',
                summary.totalPieces > 0 
                    ? '₹${(summary.totalAmount / summary.totalPieces).toStringAsFixed(2)}'
                    : '₹0.00',
              ),
              Container(
                width: 1,
                height: 20,
                color: Colors.white.withOpacity(0.3),
              ),
              _buildMiniStat(
                'Records',
                summary.totalRecords.toString(),
              ),
              Container(
                width: 1,
                height: 20,
                color: Colors.white.withOpacity(0.3),
              ),
              _buildMiniStat(
                'Last Updated',
                _formatTime(summary.lastUpdated),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(MundaError error) {
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.white.withOpacity(0.8),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Failed to load summary',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
            TextButton(
              onPressed: _loadSummary,
              child: Text(
                'Retry',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildPlaceholderStats(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.white.withOpacity(0.8),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'No records found for today',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildPlaceholderStats(),
      ],
    );
  }

  Widget _buildPlaceholderStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem('Total Rolls', '0', Icons.inventory_2),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatItem('Total Pieces', '0', Icons.straighten),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatItem('Total Amount', '₹0.00', Icons.currency_rupee),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMiniStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime? dateTime) {
    if (dateTime == null) return '--:--';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}
