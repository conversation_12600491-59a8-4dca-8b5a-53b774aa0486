import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hm_collection/features/efficiency_management/domain/entities/efficiency_metrics.dart';
import 'package:hm_collection/features/financial_management/domain/entities/financial_metrics.dart';
import 'package:hm_collection/features/inventory_management/domain/entities/inventory_metrics.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../production_planning/domain/entities/production_entities.dart';
import '../../../quality_control/domain/entities/quality_entities.dart';
import '../../domain/entities/analytics_entities.dart';
import '../../domain/entities/comparative_analysis.dart';
import '../../domain/entities/dimension_definition.dart';
import '../../domain/entities/forecast_data.dart';
import '../../domain/entities/analytics_entities.dart';
import '../../domain/entities/anomaly_detection_data.dart';
import '../../domain/entities/metric_definition.dart';
import '../../domain/entities/real_time_metrics.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../../domain/usecases/analytics_usecases.dart';
import '../widgets/analytics_kpi_cards.dart';

part 'analytics_event.dart';
part 'analytics_state.dart';

/// Analytics BLoC
@injectable
class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  final GetDashboardsUseCase _getDashboardsUseCase;
  final GetDashboardByIdUseCase _getDashboardByIdUseCase;
  final CreateDashboardUseCase _createDashboardUseCase;
  final UpdateDashboardUseCase _updateDashboardUseCase;
  final DeleteDashboardUseCase _deleteDashboardUseCase;
  final CloneDashboardUseCase _cloneDashboardUseCase;
  final ShareDashboardUseCase _shareDashboardUseCase;
  final AddWidgetUseCase _addWidgetUseCase;
  final UpdateWidgetUseCase _updateWidgetUseCase;
  final RemoveWidgetUseCase _removeWidgetUseCase;
  final ReorderWidgetsUseCase _reorderWidgetsUseCase;
  final GetReportsUseCase _getReportsUseCase;
  final GetReportByIdUseCase _getReportByIdUseCase;
  final GenerateReportUseCase _generateReportUseCase;
  final ScheduleReportUseCase _scheduleReportUseCase;
  final CancelReportUseCase _cancelReportUseCase;
  final DeleteReportUseCase _deleteReportUseCase;
  final GetKpiDataUseCase _getKpiDataUseCase;
  final GetChartDataUseCase _getChartDataUseCase;
  final GetTableDataUseCase _getTableDataUseCase;
  final GetProductionMetricsUseCase _getProductionMetricsUseCase;
  final GetQualityMetricsUseCase _getQualityMetricsUseCase;
  final GetFinancialMetricsUseCase _getFinancialMetricsUseCase;
  final GetInventoryMetricsUseCase _getInventoryMetricsUseCase;
  final GetEfficiencyMetricsUseCase _getEfficiencyMetricsUseCase;
  final GetTrendDataUseCase _getTrendDataUseCase;
  final GetComparativeAnalysisUseCase _getComparativeAnalysisUseCase;
  final GetForecastUseCase _getForecastUseCase;
  final GetAnomalyDetectionUseCase _getAnomalyDetectionUseCase;
  final GetRealTimeMetricsUseCase _getRealTimeMetricsUseCase;
  final ExportDashboardUseCase _exportDashboardUseCase;
  final ExportReportUseCase _exportReportUseCase;
  final SearchDashboardsUseCase _searchDashboardsUseCase;
  final SearchReportsUseCase _searchReportsUseCase;
  final GetAvailableMetricsUseCase _getAvailableMetricsUseCase;
  final GetAvailableDimensionsUseCase _getAvailableDimensionsUseCase;

  AnalyticsBloc(
    this._getDashboardsUseCase,
    this._getDashboardByIdUseCase,
    this._createDashboardUseCase,
    this._updateDashboardUseCase,
    this._deleteDashboardUseCase,
    this._cloneDashboardUseCase,
    this._shareDashboardUseCase,
    this._addWidgetUseCase,
    this._updateWidgetUseCase,
    this._removeWidgetUseCase,
    this._reorderWidgetsUseCase,
    this._getReportsUseCase,
    this._getReportByIdUseCase,
    this._generateReportUseCase,
    this._scheduleReportUseCase,
    this._cancelReportUseCase,
    this._deleteReportUseCase,
    this._getKpiDataUseCase,
    this._getChartDataUseCase,
    this._getTableDataUseCase,
    this._getProductionMetricsUseCase,
    this._getQualityMetricsUseCase,
    this._getFinancialMetricsUseCase,
    this._getInventoryMetricsUseCase,
    this._getEfficiencyMetricsUseCase,
    this._getTrendDataUseCase,
    this._getComparativeAnalysisUseCase,
    this._getForecastUseCase,
    this._getAnomalyDetectionUseCase,
    this._getRealTimeMetricsUseCase,
    this._exportDashboardUseCase,
    this._exportReportUseCase,
    this._searchDashboardsUseCase,
    this._searchReportsUseCase,
    this._getAvailableMetricsUseCase,
    this._getAvailableDimensionsUseCase,
  ) : super(const AnalyticsInitial()) {
    // Dashboard Events
    on<GetDashboardsRequested>(_onGetDashboardsRequested);
    on<GetDashboardByIdRequested>(_onGetDashboardByIdRequested);
    on<CreateDashboardRequested>(_onCreateDashboardRequested);
    on<UpdateDashboardRequested>(_onUpdateDashboardRequested);
    on<DeleteDashboardRequested>(_onDeleteDashboardRequested);
    on<CloneDashboardRequested>(_onCloneDashboardRequested);
    on<ShareDashboardRequested>(_onShareDashboardRequested);
    on<SearchDashboardsRequested>(_onSearchDashboardsRequested);

    // Widget Events
    on<AddWidgetRequested>(_onAddWidgetRequested);
    on<UpdateWidgetRequested>(_onUpdateWidgetRequested);
    on<RemoveWidgetRequested>(_onRemoveWidgetRequested);
    on<ReorderWidgetsRequested>(_onReorderWidgetsRequested);

    // Report Events
    on<GetReportsRequested>(_onGetReportsRequested);
    on<GetReportByIdRequested>(_onGetReportByIdRequested);
    on<GenerateReportRequested>(_onGenerateReportRequested);
    on<ScheduleReportRequested>(_onScheduleReportRequested);
    on<CancelReportRequested>(_onCancelReportRequested);
    on<DeleteReportRequested>(_onDeleteReportRequested);
    on<SearchReportsRequested>(_onSearchReportsRequested);

    // Data Analytics Events
    on<GetKpiDataRequested>(_onGetKpiDataRequested);
    on<GetChartDataRequested>(_onGetChartDataRequested);
    on<GetTableDataRequested>(_onGetTableDataRequested);

    // Performance Metrics Events
    on<GetProductionMetricsRequested>(_onGetProductionMetricsRequested);
    on<GetQualityMetricsRequested>(_onGetQualityMetricsRequested);
    on<GetFinancialMetricsRequested>(_onGetFinancialMetricsRequested);
    on<GetInventoryMetricsRequested>(_onGetInventoryMetricsRequested);
    on<GetEfficiencyMetricsRequested>(_onGetEfficiencyMetricsRequested);

    // Advanced Analytics Events
    on<GetTrendDataRequested>(_onGetTrendDataRequested);
    on<GetComparativeAnalysisRequested>(_onGetComparativeAnalysisRequested);
    on<GetForecastRequested>(_onGetForecastRequested);
    on<GetAnomalyDetectionRequested>(_onGetAnomalyDetectionRequested);
    on<GetRealTimeMetricsRequested>(_onGetRealTimeMetricsRequested);

    // Export Events
    on<ExportDashboardRequested>(_onExportDashboardRequested);
    on<ExportReportRequested>(_onExportReportRequested);

    // Metadata Events
    on<GetAvailableMetricsRequested>(_onGetAvailableMetricsRequested);
    on<GetAvailableDimensionsRequested>(_onGetAvailableDimensionsRequested);
  }

  // Dashboard Event Handlers
  Future<void> _onGetDashboardsRequested(
    GetDashboardsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getDashboardsUseCase(GetDashboardsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) => emit(AnalyticsDashboardsLoaded(response)),
    );
  }

  Future<void> _onGetDashboardByIdRequested(
    GetDashboardByIdRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getDashboardByIdUseCase(IdParams(event.dashboardId));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsDashboardLoaded(response.data!));
        } else {
          emit(const AnalyticsError('Dashboard not found'));
        }
      },
    );
  }

  Future<void> _onCreateDashboardRequested(
    CreateDashboardRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _createDashboardUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsDashboardCreated(response.data!));
        } else {
          emit(const AnalyticsError('Failed to create dashboard: No data returned'));
        }
      },
    );
  }

  Future<void> _onUpdateDashboardRequested(
    UpdateDashboardRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _updateDashboardUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsDashboardUpdated(response.data!));
        } else {
          emit(const AnalyticsError('Failed to update dashboard: No data received'));
        }
      },
    );
  }

  Future<void> _onDeleteDashboardRequested(
    DeleteDashboardRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _deleteDashboardUseCase(IdParams(event.dashboardId));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (_) => emit(AnalyticsDashboardDeleted(event.dashboardId)),
    );
  }

  Future<void> _onCloneDashboardRequested(
    CloneDashboardRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _cloneDashboardUseCase(CloneDashboardParams(
      dashboardId: event.dashboardId,
      newName: event.newName,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsDashboardCloned(response.data!));
        } else {
          emit(const AnalyticsError('Failed to clone dashboard: No data received'));
        }
      },
    );
  }

  Future<void> _onShareDashboardRequested(
    ShareDashboardRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _shareDashboardUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsDashboardShared(response.data!));
        } else {
          emit(const AnalyticsError('Failed to share dashboard: No data received'));
        }
      },
    );
  }

  Future<void> _onSearchDashboardsRequested(
    SearchDashboardsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _searchDashboardsUseCase(SearchDashboardsParams(
      query: event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) => emit(AnalyticsDashboardsSearched(response, event.query)),
    );
  }

  // Widget Event Handlers
  Future<void> _onAddWidgetRequested(
    AddWidgetRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _addWidgetUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsWidgetAdded(response.data! as AnalyticsDashboard));
        } else {
          emit(const AnalyticsError('Failed to add widget: No data received'));
        }
      },
    );
  }

  Future<void> _onUpdateWidgetRequested(
    UpdateWidgetRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _updateWidgetUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsWidgetUpdated(response.data!));
        } else {
          emit(const AnalyticsError('Failed to update widget: No data returned'));
        }
      },
    );
  }

  Future<void> _onRemoveWidgetRequested(
    RemoveWidgetRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _removeWidgetUseCase(RemoveWidgetParams(
      dashboardId: event.dashboardId,
      widgetId: event.widgetId,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (_) => emit(AnalyticsWidgetRemoved(event.dashboardId, event.widgetId)),
    );
  }

  Future<void> _onReorderWidgetsRequested(
    ReorderWidgetsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _reorderWidgetsUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsWidgetsReordered(response.data!));
        } else {
          emit(const AnalyticsError('Failed to reorder widgets: No data received'));
        }
      },
    );
  }

  // Report Event Handlers
  Future<void> _onGetReportsRequested(
    GetReportsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getReportsUseCase(GetReportsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) => emit(AnalyticsReportsLoaded(response)),
    );
  }

  Future<void> _onGetReportByIdRequested(
    GetReportByIdRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getReportByIdUseCase(IdParams(event.reportId));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsReportLoaded(response.data!));
        } else {
          emit(const AnalyticsError('Report data is null'));
        }
      },
    );
  }

  Future<void> _onGenerateReportRequested(
    GenerateReportRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _generateReportUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(AnalyticsReportGenerated(response.data!));
        } else {
          emit(const AnalyticsError('Failed to generate report: No data received'));
        }
      },
    );
  }

  Future<void> _onScheduleReportRequested(
    ScheduleReportRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _scheduleReportUseCase(event.request);

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) => emit(AnalyticsReportScheduled(response.data!)),
    );
  }

  Future<void> _onCancelReportRequested(
    CancelReportRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _cancelReportUseCase(IdParams(event.reportId));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (_) => emit(AnalyticsReportCancelled(event.reportId)),
    );
  }

  Future<void> _onDeleteReportRequested(
    DeleteReportRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _deleteReportUseCase(IdParams(event.reportId));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (_) => emit(AnalyticsReportDeleted(event.reportId)),
    );
  }

  Future<void> _onSearchReportsRequested(
    SearchReportsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _searchReportsUseCase(SearchReportsParams(
      query: event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (response) => emit(AnalyticsReportsSearched(response, event.query)),
    );
  }

  // Data Analytics Event Handlers
  Future<void> _onGetKpiDataRequested(
    GetKpiDataRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getKpiDataUseCase(GetKpiDataParams(
      metrics: event.metrics,
      startDate: event.startDate,
      endDate: event.endDate,
      filters: event.filters,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsKpiDataLoaded(data)),
    );
  }

  Future<void> _onGetChartDataRequested(
    GetChartDataRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getChartDataUseCase(GetChartDataParams(
      chartType: event.chartType,
      metrics: event.metrics,
      dimensions: event.dimensions,
      startDate: event.startDate,
      endDate: event.endDate,
      filters: event.filters,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsChartDataLoaded(data)),
    );
  }

  Future<void> _onGetTableDataRequested(
    GetTableDataRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getTableDataUseCase(GetTableDataParams(
      columns: event.columns,
      startDate: event.startDate,
      endDate: event.endDate,
      filters: event.filters,
      sortBy: event.sortBy,
      sortOrder: event.sortOrder,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsTableDataLoaded(data)),
    );
  }

  // Performance Metrics Event Handlers
  Future<void> _onGetProductionMetricsRequested(
    GetProductionMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getProductionMetricsUseCase(GetProductionMetricsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      departmentId: event.departmentId,
      productId: event.productId,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsProductionMetricsLoaded(data as ProductionMetrics)),
    );
  }

  Future<void> _onGetQualityMetricsRequested(
    GetQualityMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getQualityMetricsUseCase(GetQualityMetricsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      departmentId: event.departmentId,
      productId: event.productId,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsQualityMetricsLoaded(data as QualityMetrics)),
    );
  }

  Future<void> _onGetFinancialMetricsRequested(
    GetFinancialMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getFinancialMetricsUseCase(GetFinancialMetricsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      costCenterId: event.costCenterId,
      departmentId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsFinancialMetricsLoaded(data as FinancialMetrics)),
    );
  }

  Future<void> _onGetInventoryMetricsRequested(
    GetInventoryMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getInventoryMetricsUseCase(GetInventoryMetricsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      warehouseId: event.warehouseId,
      categoryId: event.categoryId,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsInventoryMetricsLoaded(data as InventoryMetrics)),
    );
  }

  Future<void> _onGetEfficiencyMetricsRequested(
    GetEfficiencyMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getEfficiencyMetricsUseCase(GetEfficiencyMetricsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      departmentId: event.departmentId,
      machineId: event.machineId,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsEfficiencyMetricsLoaded(data as EfficiencyMetrics)),
    );
  }

  // Advanced Analytics Event Handlers
  Future<void> _onGetTrendDataRequested(
    GetTrendDataRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getTrendDataUseCase(GetTrendDataParams(
      metric: event.metric,
      startDate: event.startDate,
      endDate: event.endDate,
      granularity: event.granularity,
      filters: event.filters,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsTrendDataLoaded(data as TrendData)),
    );
  }

  Future<void> _onGetComparativeAnalysisRequested(
    GetComparativeAnalysisRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getComparativeAnalysisUseCase(GetComparativeAnalysisParams(
      metrics: event.metrics,
      currentPeriodStart: event.currentPeriodStart,
      currentPeriodEnd: event.currentPeriodEnd,
      previousPeriodStart: event.previousPeriodStart,
      previousPeriodEnd: event.previousPeriodEnd,
      filters: event.filters,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsComparativeAnalysisLoaded(data as ComparativeAnalysis)),
    );
  }

  Future<void> _onGetForecastRequested(
    GetForecastRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getForecastUseCase(GetForecastParams(
      metric: event.metric,
      startDate: event.startDate,
      endDate: event.endDate,
      forecastPeriods: event.forecastPeriods,
      parameters: event.parameters,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsForecastLoaded(data as ForecastData)),
    );
  }

  Future<void> _onGetAnomalyDetectionRequested(
    GetAnomalyDetectionRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getAnomalyDetectionUseCase(GetAnomalyDetectionParams(
      metric: event.metric,
      startDate: event.startDate,
      endDate: event.endDate,
      parameters: event.parameters,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsAnomalyDetectionLoaded(data as AnomalyDetectionData)),
    );
  }

  Future<void> _onGetRealTimeMetricsRequested(
    GetRealTimeMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getRealTimeMetricsUseCase(GetRealTimeMetricsParams(
      metrics: event.metrics,
      filters: event.filters,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (data) => emit(AnalyticsRealTimeMetricsLoaded(data as RealTimeMetrics)),
    );
  }

  // Export Event Handlers
  Future<void> _onExportDashboardRequested(
    ExportDashboardRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _exportDashboardUseCase(ExportDashboardParams(
      dashboardId: event.dashboardId,
      format: event.format,
      options: event.options,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (url) => emit(AnalyticsDashboardExported(url)),
    );
  }

  Future<void> _onExportReportRequested(
    ExportReportRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _exportReportUseCase(ExportReportParams(
      reportId: event.reportId,
      format: event.format,
      options: event.options,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (url) => emit(AnalyticsReportExported(url)),
    );
  }

  // Metadata Event Handlers
  Future<void> _onGetAvailableMetricsRequested(
    GetAvailableMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getAvailableMetricsUseCase(GetAvailableMetricsParams(
      category: event.category,
      dataSource: event.dataSource,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (metrics) => emit(AnalyticsAvailableMetricsLoaded(metrics.cast<MetricDefinition>())),
    );
  }

  Future<void> _onGetAvailableDimensionsRequested(
    GetAvailableDimensionsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsLoading());

    final result = await _getAvailableDimensionsUseCase(GetAvailableDimensionsParams(
      category: event.category,
      dataSource: event.dataSource,
    ));

    result.fold(
      (failure) => emit(AnalyticsError(failure.message)),
      (dimensions) => emit(AnalyticsAvailableDimensionsLoaded(dimensions.cast<DimensionDefinition>())),
    );
  }
}
