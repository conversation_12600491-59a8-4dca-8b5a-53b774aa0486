import 'package:equatable/equatable.dart';

class QualityStandardFilter extends Equatable {
  final String? status;
  final String? type;
  final String? category;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? searchQuery;

  const QualityStandardFilter({
    this.status,
    this.type,
    this.category,
    this.fromDate,
    this.toDate,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        status,
        type,
        category,
        fromDate,
        toDate,
        searchQuery,
      ];

  QualityStandardFilter copyWith({
    String? status,
    String? type,
    String? category,
    DateTime? fromDate,
    DateTime? toDate,
    String? searchQuery,
  }) {
    return QualityStandardFilter(
      status: status ?? this.status,
      type: type ?? this.type,
      category: category ?? this.category,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}
