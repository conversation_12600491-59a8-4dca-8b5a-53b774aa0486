import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:hm_collection/core/auth/usecases/auth_usecases.dart';
import 'package:hm_collection/core/firebase/firebase_auth_service.dart';
import 'package:hm_collection/core/usecases/usecase.dart';
import 'package:hm_collection/features/auth/domain/repositories/auth_repository.dart' as domain_repo;
import 'package:hm_collection/shared/enums/common_enums.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/auth/entities/user_entities.dart';
import '../../domain/entities/user.dart';

part 'firebase_auth_event.dart';
part 'firebase_auth_state.dart';

@injectable
class FirebaseAuthBloc extends Bloc<FirebaseAuthEvent, FirebaseAuthState> {
  final FirebaseAuthService _firebaseAuthService;
  final domain_repo.AuthRepository _authRepository;
  StreamSubscription<AppUser?>? _authStateSubscription;

  FirebaseAuthBloc(
    this._firebaseAuthService,
    this._authRepository,
  ) : super(const FirebaseAuthInitial()) {
    on<FirebaseAuthStarted>(_onFirebaseAuthStarted);
    on<FirebaseSignInRequested>(_onFirebaseSignInRequested);
    on<FirebaseAuthGoogleSignInRequested>(_onFirebaseAuthGoogleSignInRequested);
    on<FirebaseSignUpRequested>(_onFirebaseSignUpRequested);
    on<FirebaseSignOutRequested>(_onFirebaseSignOutRequested);
    on<_FirebaseAuthStateChanged>(_onFirebaseAuthStateChanged);
  }

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }

  Future<void> _onFirebaseAuthStarted(
    FirebaseAuthStarted event,
    Emitter<FirebaseAuthState> emit,
  ) async {
    emit(const FirebaseAuthLoading());
    _authStateSubscription = _authRepository.authStateChanges.listen(
      (appUser) => add(_FirebaseAuthStateChanged(appUser)),
      onError: (error) => emit(FirebaseAuthError(_handleError(error))),
    );
  }

  Future<void> _onFirebaseSignInRequested(
    FirebaseSignInRequested event,
    Emitter<FirebaseAuthState> emit,
  ) async {
    emit(const FirebaseAuthLoading());
    try {
      await _firebaseAuthService.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );
    } catch (e) {
      emit(FirebaseAuthError(_handleError(e)));
    }
  }

  Future<void> _onFirebaseAuthGoogleSignInRequested(
    FirebaseAuthGoogleSignInRequested event,
    Emitter<FirebaseAuthState> emit,
  ) async {
    emit(const FirebaseAuthLoading());
    try {
      await _firebaseAuthService.signInWithGoogleForAdmin();
    } catch (e) {
      emit(FirebaseAuthError(_handleError(e)));
    }
  }

  Future<void> _onFirebaseSignUpRequested(
    FirebaseSignUpRequested event,
    Emitter<FirebaseAuthState> emit,
  ) async {
    emit(const FirebaseAuthLoading());
    try {
      await _firebaseAuthService.createUserWithEmailAndPassword(
        email: event.email,
        password: event.password,
        firstName: event.firstName,
        lastName: event.lastName,
        role: event.role,
      );
    } catch (e) {
      emit(FirebaseAuthError(_handleError(e)));
    }
  }

  Future<void> _onFirebaseSignOutRequested(
    FirebaseSignOutRequested event,
    Emitter<FirebaseAuthState> emit,
  ) async {
    emit(const FirebaseAuthLoading());
    try {
      await _firebaseAuthService.signOut();
    } catch (e) {
      emit(FirebaseAuthError(_handleError(e)));
    }
  }

  void _onFirebaseAuthStateChanged(
    _FirebaseAuthStateChanged event,
    Emitter<FirebaseAuthState> emit,
  ) {
    if (event.user != null) {
      emit(FirebaseAuthAuthenticated(_convertAppUserToUser(event.user!)));
    } else {
      emit(const FirebaseAuthUnauthenticated());
    }
  }

  User _convertAppUserToUser(AppUser appUser) {
    // Resolve department from Firebase-backed fields with sensible fallbacks
    final String? deptId = (appUser.currentDepartmentId != null && appUser.currentDepartmentId!.trim().isNotEmpty)
        ? appUser.currentDepartmentId
        : ((appUser.profile.department != null && appUser.profile.department!.trim().isNotEmpty)
            ? appUser.profile.department
            : (appUser.departmentIds.isNotEmpty ? appUser.departmentIds.first : null));

    final Department resolvedDepartment = deptId != null
        ? Department.fromString(deptId)
        : appUser.role.department; // fallback to role-mapped department

    return User(
      id: appUser.id,
      createdAt: appUser.createdAt,
      updatedAt: appUser.updatedAt,
      username: appUser.email.split('@').first,
      email: appUser.email,
      firstName: appUser.profile.firstName,
      lastName: appUser.profile.lastName,
      phoneNumber: appUser.phoneNumber,
      profileImageUrl: appUser.photoURL,
      role: appUser.role,
      department: resolvedDepartment,
      status: appUser.status,
      isActive: appUser.isActive,
      isEmailVerified: appUser.emailVerified,
      isPhoneVerified: appUser.phoneNumber != null,
      permissions: appUser.permissions,
      lastLoginAt: appUser.lastLoginAt,
    );
  }

  String _handleError(dynamic error) {
    if (error is firebase_auth.FirebaseAuthException) {
      return error.message ?? 'An unknown authentication error occurred.';
    }
    return error.toString();
  }
}
