import 'dart:io';
import 'dart:typed_data';
import 'package:dartz/dartz.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/bundle.dart';
import '../../domain/services/bundle_export_service.dart';

/// Implementation of bundle export service
class BundleExportServiceImpl implements BundleExportService {
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy HH:mm');
  final DateFormat _fileNameDateFormat = DateFormat('yyyyMMdd_HHmmss');

  @override
  Future<Either<Failure, String>> exportToExcel(
    List<Bundle> bundles, {
    String? fileName,
    bool includeStatistics = true,
  }) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Bundles'];

      // Add headers
      final headers = [
        'Bundle No',
        'Design No',
        'Lot No',
        'Roll No',
        'Alt No',
        'Size',
        'Piece Range',
        'Total Pieces',
        'Status',
        'Assigned To',
        'Created At',
        'Updated At',
        'Remarks',
      ];

      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = headers[i];
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: '#4472C4',
          fontColorHex: '#FFFFFF',
        );
      }

      // Add data rows
      for (int i = 0; i < bundles.length; i++) {
        final bundle = bundles[i];
        final rowIndex = i + 1;

        final rowData = [
          bundle.bundleNo,
          bundle.designNo,
          bundle.lotNo,
          bundle.rollNo,
          bundle.altNo,
          bundle.size.displayName,
          bundle.pieceRange,
          bundle.totalPieces,
          bundle.status.displayName,
          bundle.assignedTo ?? '',
          _dateFormat.format(bundle.createdAt),
          bundle.updatedAt != null ? _dateFormat.format(bundle.updatedAt!) : '',
          bundle.remarks ?? '',
        ];

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = rowData[j];
        }
      }

      // Add statistics sheet if requested
      if (includeStatistics) {
        _addStatisticsSheet(excel, bundles);
      }

      // Save file
      final directory = await getDefaultExportDirectory();
      final finalFileName = fileName ?? 'bundles_${_fileNameDateFormat.format(DateTime.now())}.xlsx';
      final filePath = '$directory/$finalFileName';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        return Right(filePath);
      } else {
        return Left(ServerFailure('Failed to generate Excel file'));
      }
    } catch (e) {
      return Left(ServerFailure('Export to Excel failed: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, String>> exportToPdf(
    List<Bundle> bundles, {
    String? fileName,
    bool includeStatistics = true,
    bool includeQrCodes = true,
  }) async {
    try {
      final pdf = pw.Document();

      // Add bundles page
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) {
            return [
              _buildPdfHeader(),
              pw.SizedBox(height: 20),
              _buildBundlesTable(bundles),
            ];
          },
        ),
      );

      // Add statistics page if requested
      if (includeStatistics) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(20),
            build: (pw.Context context) {
              return _buildStatisticsPage(bundles);
            },
          ),
        );
      }

      // Save file
      final directory = await getDefaultExportDirectory();
      final finalFileName = fileName ?? 'bundles_${_fileNameDateFormat.format(DateTime.now())}.pdf';
      final filePath = '$directory/$finalFileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());
      return Right(filePath);
    } catch (e) {
      return Left(ServerFailure('Export to PDF failed: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, String>> exportBundleSheet(
    Bundle bundle, {
    String format = 'pdf',
    bool includeQrCode = true,
    bool includeInstructions = true,
  }) async {
    try {
      if (format.toLowerCase() == 'pdf') {
        return _exportBundleSheetPdf(bundle, includeQrCode, includeInstructions);
      } else {
        return _exportBundleSheetExcel(bundle, includeInstructions);
      }
    } catch (e) {
      return Left(ServerFailure('Export bundle sheet failed: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, String>> exportSummaryReport(
    List<Bundle> bundles, {
    String format = 'pdf',
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final filteredBundles = _filterBundlesByDate(bundles, fromDate, toDate);
      
      if (format.toLowerCase() == 'pdf') {
        return _exportSummaryReportPdf(filteredBundles, fromDate, toDate);
      } else {
        return exportToExcel(filteredBundles, includeStatistics: true);
      }
    } catch (e) {
      return Left(ServerFailure('Export summary report failed: ${e.toString()}'));
    }
  }

  @override
  List<String> getSupportedFormats() {
    return ['pdf', 'excel', 'csv'];
  }

  @override
  Future<String> getDefaultExportDirectory() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final exportDir = Directory('${directory.path}/exports');
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }
      return exportDir.path;
    } catch (e) {
      // Fallback to documents directory
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    }
  }

  @override
  Future<Either<Failure, void>> shareFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await Share.shareXFiles([XFile(filePath)]);
        return const Right(null);
      } else {
        return Left(ServerFailure('File not found: $filePath'));
      }
    } catch (e) {
      return Left(ServerFailure('Share file failed: ${e.toString()}'));
    }
  }

  // Private helper methods

  void _addStatisticsSheet(Excel excel, List<Bundle> bundles) {
    final statsSheet = excel['Statistics'];
    
    // Calculate statistics
    final totalBundles = bundles.length;
    final totalPieces = bundles.fold<int>(0, (sum, bundle) => sum + bundle.totalPieces);
    final statusCounts = <BundleStatus, int>{};
    final sizeCounts = <GarmentSize, int>{};

    for (final bundle in bundles) {
      statusCounts[bundle.status] = (statusCounts[bundle.status] ?? 0) + 1;
      sizeCounts[bundle.size] = (sizeCounts[bundle.size] ?? 0) + 1;
    }

    int row = 0;

    // Add title
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = 'Bundle Statistics';
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
    );
    row += 2;

    // Add general statistics
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = 'Total Bundles:';
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = totalBundles;
    row++;

    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = 'Total Pieces:';
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = totalPieces;
    row += 2;

    // Add status breakdown
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = 'Status Breakdown:';
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).cellStyle = CellStyle(bold: true);
    row++;

    for (final entry in statusCounts.entries) {
      statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = entry.key.displayName;
      statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = entry.value;
      row++;
    }

    row++;

    // Add size breakdown
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = 'Size Breakdown:';
    statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).cellStyle = CellStyle(bold: true);
    row++;

    for (final entry in sizeCounts.entries) {
      statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = entry.key.displayName;
      statsSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = entry.value;
      row++;
    }
  }

  pw.Widget _buildPdfHeader() {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Bundle Report',
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'Generated on: ${_dateFormat.format(DateTime.now())}',
          style: const pw.TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  pw.Widget _buildBundlesTable(List<Bundle> bundles) {
    return pw.Table.fromTextArray(
      context: null,
      data: [
        ['Bundle No', 'Design', 'Lot', 'Roll', 'Size', 'Pieces', 'Status'],
        ...bundles.map((bundle) => [
          bundle.bundleNo,
          bundle.designNo,
          bundle.lotNo,
          bundle.rollNo,
          bundle.size.displayName,
          bundle.totalPieces.toString(),
          bundle.status.displayName,
        ]),
      ],
      headerStyle: pw.TextStyle(
        fontWeight: pw.FontWeight.bold,
        fontSize: 10,
      ),
      cellStyle: const pw.TextStyle(fontSize: 9),
      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.grey300,
      ),
      cellHeight: 25,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerLeft,
        2: pw.Alignment.centerLeft,
        3: pw.Alignment.centerLeft,
        4: pw.Alignment.center,
        5: pw.Alignment.centerRight,
        6: pw.Alignment.center,
      },
    );
  }

  pw.Widget _buildStatisticsPage(List<Bundle> bundles) {
    final totalBundles = bundles.length;
    final totalPieces = bundles.fold<int>(0, (sum, bundle) => sum + bundle.totalPieces);
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Bundle Statistics',
          style: pw.TextStyle(
            fontSize: 20,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 20),
        pw.Text('Total Bundles: $totalBundles'),
        pw.Text('Total Pieces: $totalPieces'),
        pw.SizedBox(height: 20),
        // Add more statistics as needed
      ],
    );
  }
