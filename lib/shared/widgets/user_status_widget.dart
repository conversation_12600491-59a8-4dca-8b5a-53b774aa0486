import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import '../../core/auth/entities/user_entities.dart';
import '../../core/services/user_status_service.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../enums/common_enums.dart';

/// Widget that displays real-time user status
class UserStatusWidget extends StatelessWidget {
  final String userId;
  final bool showLastActive;
  final bool showStatusText;
  final double size;

  const UserStatusWidget({
    super.key,
    required this.userId,
    this.showLastActive = true,
    this.showStatusText = true,
    this.size = 12,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<AppUser?>(
      stream: GetIt.instance<UserStatusService>().getUserStatus(userId),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return _buildOfflineIndicator();
        }

        final user = snapshot.data!;
        return _buildStatusIndicator(user);
      },
    );
  }

  Widget _buildStatusIndicator(AppUser user) {
    final isOnline = user.isActive && user.status == CommonStatus.active;
    final statusColor = _getStatusColor(user.status, isOnline);
    final statusText = _getStatusText(user.status, isOnline);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: statusColor,
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: 2,
            ),
          ),
        ),
        if (showStatusText) ...[
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                statusText,
                style: AppTextStyles.bodySmall.copyWith(
                  color: statusColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (showLastActive && user.lastActiveAt != null)
                Text(
                  _formatLastActive(user.lastActiveAt!),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 10,
                  ),
                ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildOfflineIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: Colors.grey,
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: 2,
            ),
          ),
        ),
        if (showStatusText) ...[
          const SizedBox(width: 6),
          Text(
            'Offline',
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Color _getStatusColor(CommonStatus status, bool isOnline) {
    if (!isOnline) return Colors.grey;

    switch (status) {
      case CommonStatus.active:
        return Colors.green;
      case CommonStatus.inactive:
        return Colors.grey;
      case CommonStatus.suspended:
        return Colors.red;
      case CommonStatus.pending:
        return Colors.orange;
      case CommonStatus.archived:
        return Colors.grey.withValues(alpha: 0.5);
    }
  }

  String _getStatusText(CommonStatus status, bool isOnline) {
    if (!isOnline) return 'Offline';

    switch (status) {
      case CommonStatus.active:
        return 'Online';
      case CommonStatus.inactive:
        return 'Inactive';
      case CommonStatus.suspended:
        return 'Suspended';
      case CommonStatus.pending:
        return 'Pending';
      case CommonStatus.archived:
        return 'Archived';
    }
  }

  String _formatLastActive(DateTime lastActive) {
    final now = DateTime.now();
    final difference = now.difference(lastActive);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Widget that shows a list of online users
class OnlineUsersWidget extends StatelessWidget {
  final int maxUsers;
  final VoidCallback? onViewAll;

  const OnlineUsersWidget({
    super.key,
    this.maxUsers = 5,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<AppUser>>(
      stream: GetIt.instance<UserStatusService>().getOnlineUsers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final onlineUsers = snapshot.data!;
        final displayUsers = onlineUsers.take(maxUsers).toList();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Online Users (${onlineUsers.length})',
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    if (onlineUsers.length > maxUsers && onViewAll != null)
                      TextButton(
                        onPressed: onViewAll,
                        child: const Text('View All'),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                if (displayUsers.isEmpty)
                  const Text('No users online')
                else
                  ...displayUsers.map((user) => _buildUserItem(user)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserItem(AppUser user) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: AppColors.primary,
            backgroundImage: user.photoURL != null 
                ? NetworkImage(user.photoURL!) 
                : null,
            child: user.photoURL == null
                ? Text(
                    user.initials,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.fullName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  user.role.displayName,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          UserStatusWidget(
            userId: user.id,
            showLastActive: false,
            showStatusText: false,
            size: 10,
          ),
        ],
      ),
    );
  }
}

/// Widget that shows user session information
class UserSessionsWidget extends StatelessWidget {
  final String userId;

  const UserSessionsWidget({
    super.key,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<UserSession>>(
      stream: GetIt.instance<UserStatusService>().getUserSessions(userId),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final sessions = snapshot.data!;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Active Sessions (${sessions.length})',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                if (sessions.isEmpty)
                  const Text('No active sessions')
                else
                  ...sessions.map((session) => _buildSessionItem(context, session)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSessionItem(BuildContext context, UserSession session) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getPlatformIcon(session.platform),
            size: 24,
            color: AppColors.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  session.deviceName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'Last active: ${_formatLastActive(session.lastActiveAt)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.logout, color: Colors.red),
            onPressed: () => _terminateSession(context, session),
            tooltip: 'Terminate Session',
          ),
        ],
      ),
    );
  }

  IconData _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'android':
        return Icons.android;
      case 'ios':
        return Icons.phone_iphone;
      case 'web':
        return Icons.web;
      default:
        return Icons.device_unknown;
    }
  }

  String _formatLastActive(DateTime lastActive) {
    final now = DateTime.now();
    final difference = now.difference(lastActive);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  void _terminateSession(BuildContext context, UserSession session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terminate Session'),
        content: Text(
          'Are you sure you want to terminate the session on ${session.deviceName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await GetIt.instance<UserStatusService>()
                    .terminateSession(session.sessionId);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Session terminated successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to terminate session: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Terminate'),
          ),
        ],
      ),
    );
  }
}
