import 'package:equatable/equatable.dart';

/// Represents real-time metrics data for the analytics dashboard
class RealTimeMetrics extends Equatable {
  /// The timestamp when the metrics were last updated
  final DateTime timestamp;
  
  /// Map of metric names to their current values
  final Map<String, dynamic> metrics;
  
  /// Optional status indicators for each metric (e.g., 'normal', 'warning', 'critical')
  final Map<String, String>? statuses;
  
  /// Optional trends for each metric (e.g., 'up', 'down', 'stable')
  final Map<String, String>? trends;
  
  const RealTimeMetrics({
    required this.timestamp,
    required this.metrics,
    this.statuses,
    this.trends,
  });
  
  /// Creates a RealTimeMetrics instance from JSON data
  factory RealTimeMetrics.fromJson(Map<String, dynamic> json) {
    return RealTimeMetrics(
      timestamp: DateTime.parse(json['timestamp'] as String),
      metrics: Map<String, dynamic>.from(json['metrics'] as Map),
      statuses: json['statuses'] != null 
          ? Map<String, String>.from(json['statuses'] as Map) 
          : null,
      trends: json['trends'] != null 
          ? Map<String, String>.from(json['trends'] as Map) 
          : null,
    );
  }
  
  /// Converts the RealTimeMetrics instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'metrics': metrics,
      if (statuses != null) 'statuses': statuses,
      if (trends != null) 'trends': trends,
    };
  }
  
  @override
  List<Object?> get props => [timestamp, metrics, statuses, trends];
  
  /// Creates a copy of this RealTimeMetrics with the given fields replaced
  RealTimeMetrics copyWith({
    DateTime? timestamp,
    Map<String, dynamic>? metrics,
    Map<String, String>? statuses,
    Map<String, String>? trends,
  }) {
    return RealTimeMetrics(
      timestamp: timestamp ?? this.timestamp,
      metrics: metrics ?? this.metrics,
      statuses: statuses ?? this.statuses,
      trends: trends ?? this.trends,
    );
  }
}
