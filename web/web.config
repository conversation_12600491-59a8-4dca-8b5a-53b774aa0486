<?xml version="1.0" encoding="UTF-8"?>
<!-- 
  Web.config for IIS deployment of HM Collection Flutter Web App
  This configuration ensures proper routing and MIME types for Flutter web apps
-->
<configuration>
  <system.webServer>
    
    <!-- URL Rewriting for SPA routing -->
    <rewrite>
      <rules>
        <!-- Handle Flutter web routing -->
        <rule name="Flutter Web SPA" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(api|assets|icons|favicon)" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>

    <!-- Static file handling -->
    <staticContent>
      <!-- WASM files -->
      <mimeMap fileExtension=".wasm" mimeType="application/wasm" />
      
      <!-- Dart files -->
      <mimeMap fileExtension=".dart" mimeType="application/dart" />
      
      <!-- JSON files -->
      <mimeMap fileExtension=".json" mimeType="application/json" />
      
      <!-- Web manifest -->
      <mimeMap fileExtension=".webmanifest" mimeType="application/manifest+json" />
      
      <!-- Font files -->
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
      <mimeMap fileExtension=".woff" mimeType="font/woff" />
      <mimeMap fileExtension=".ttf" mimeType="font/ttf" />
      <mimeMap fileExtension=".otf" mimeType="font/otf" />
    </staticContent>

    <!-- Compression -->
    <httpCompression>
      <dynamicTypes>
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
        <add mimeType="text/css" enabled="true" />
        <add mimeType="text/html" enabled="true" />
        <add mimeType="application/dart" enabled="true" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
        <add mimeType="text/css" enabled="true" />
        <add mimeType="text/html" enabled="true" />
        <add mimeType="application/dart" enabled="true" />
      </staticTypes>
    </httpCompression>

    <!-- HTTP Headers -->
    <httpProtocol>
      <customHeaders>
        <!-- Security headers -->
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
        
        <!-- CORS headers (adjust as needed) -->
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS" />
        <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization" />
      </customHeaders>
    </httpProtocol>

    <!-- Caching -->
    <caching>
      <profiles>
        <!-- Cache static assets for 1 year -->
        <add extension=".js" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".css" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".png" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".jpg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".jpeg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".gif" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".svg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".woff" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".woff2" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".ttf" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".otf" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        <add extension=".wasm" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
        
        <!-- Cache HTML files for 1 hour -->
        <add extension=".html" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="01:00:00" />
        
        <!-- Cache JSON files for 1 day -->
        <add extension=".json" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
      </profiles>
    </caching>

    <!-- Default document -->
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
      </files>
    </defaultDocument>

    <!-- Error pages -->
    <httpErrors errorMode="Custom" defaultResponseMode="ExecuteURL">
      <remove statusCode="404" subStatusCode="-1" />
      <error statusCode="404" path="/index.html" responseMode="ExecuteURL" />
    </httpErrors>

  </system.webServer>
</configuration>
