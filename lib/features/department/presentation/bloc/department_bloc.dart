import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hm_collection/core/usecases/usecase.dart';
import 'package:hm_collection/features/department/domain/entities/department.dart';
import 'package:hm_collection/features/department/domain/usecases/get_departments.dart';

part 'department_event.dart';
part 'department_state.dart';

class DepartmentBloc extends Bloc<DepartmentEvent, DepartmentState> {
  final GetDepartments getDepartments;

  DepartmentBloc({required this.getDepartments}) : super(DepartmentInitial()) {
    on<FetchDepartments>(_onFetchDepartments);
  }

  void _onFetchDepartments(
    FetchDepartments event,
    Emitter<DepartmentState> emit,
  ) async {
    emit(DepartmentLoading());
    final failureOrDepartments = await getDepartments(NoParams());
    failureOrDepartments.fold(
      (failure) => emit(const DepartmentError(message: 'Failed to fetch departments')),
      (departments) => emit(DepartmentLoaded(departments: departments)),
    );
  }
}
