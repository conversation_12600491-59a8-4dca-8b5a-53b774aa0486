import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/custom_app_bar.dart';

/// Analytics and reporting page
class AnalyticsPage extends StatefulWidget {
  const AnalyticsPage({super.key});

  @override
  State<AnalyticsPage> createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends State<AnalyticsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Analytics & Reporting',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshData(),
            tooltip: 'Refresh Data',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () => _exportData(),
            tooltip: 'Export Data',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildReportsTab(),
                _buildInsightsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Dashboards',
          ),
          Tab(
            icon: Icon(Icons.assessment),
            text: 'Reports',
          ),
          Tab(
            icon: Icon(Icons.insights),
            text: 'Insights',
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analytics Dashboards',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // KPI Cards
          Row(
            children: [
              Expanded(
                child: _buildKpiCard(
                  'Total Orders',
                  '1,234',
                  Icons.shopping_cart,
                  AppColors.primary,
                  '+12%',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildKpiCard(
                  'Production Rate',
                  '89%',
                  Icons.trending_up,
                  AppColors.success,
                  '+5%',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildKpiCard(
                  'Quality Score',
                  '96.5%',
                  Icons.verified,
                  AppColors.warning,
                  '+2%',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildKpiCard(
                  'Revenue',
                  '\$125K',
                  Icons.attach_money,
                  AppColors.error,
                  '+8%',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Charts placeholder
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Production Analytics',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 300,
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text('Production Charts will be displayed here'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reports',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Report categories
          _buildReportCategory('Production Reports', [
            'Daily Production Summary',
            'Weekly Production Analysis',
            'Monthly Production Report',
            'Production Efficiency Report',
          ]),
          
          const SizedBox(height: 16),
          
          _buildReportCategory('Quality Reports', [
            'Quality Control Summary',
            'Defect Analysis Report',
            'Quality Trends Report',
            'Compliance Report',
          ]),
          
          const SizedBox(height: 16),
          
          _buildReportCategory('Financial Reports', [
            'Revenue Analysis',
            'Cost Analysis Report',
            'Profit & Loss Report',
            'Budget vs Actual Report',
          ]),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Business Insights',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Insights cards
          _buildInsightCard(
            'Production Optimization',
            'Machine efficiency can be improved by 15% with better scheduling',
            Icons.precision_manufacturing,
            AppColors.primary,
          ),
          
          const SizedBox(height: 16),
          
          _buildInsightCard(
            'Quality Improvement',
            'Implementing automated quality checks could reduce defects by 25%',
            Icons.verified,
            AppColors.success,
          ),
          
          const SizedBox(height: 16),
          
          _buildInsightCard(
            'Cost Reduction',
            'Material waste can be reduced by 10% with better inventory management',
            Icons.trending_down,
            AppColors.warning,
          ),
        ],
      ),
    );
  }

  Widget _buildKpiCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String change,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  change,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTextStyles.headlineMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCategory(String title, List<String> reports) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...reports.map((report) => ListTile(
              leading: const Icon(Icons.description),
              title: Text(report),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.visibility),
                    onPressed: () => _viewReport(report),
                    tooltip: 'View Report',
                  ),
                  IconButton(
                    icon: const Icon(Icons.download),
                    onPressed: () => _downloadReport(report),
                    tooltip: 'Download Report',
                  ),
                ],
              ),
              onTap: () => _viewReport(report),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightCard(
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              icon: const Icon(Icons.arrow_forward),
              onPressed: () => _viewInsightDetails(title),
            ),
          ],
        ),
      ),
    );
  }

  void _refreshData() {
    // Implement data refresh
  }

  void _exportData() {
    // Implement data export
  }

  void _viewReport(String report) {
    // Implement report viewing
  }

  void _downloadReport(String report) {
    // Implement report download
  }

  void _viewInsightDetails(String insight) {
    // Implement insight details view
  }
}
