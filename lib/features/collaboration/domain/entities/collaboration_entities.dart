import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Collaboration workspace entity
class CollaborationWorkspace extends BaseEntity {
  final String workspaceCode;
  final String workspaceName;
  final String description;
  final WorkspaceType type;
  final CommonStatus status;
  final String ownerId;
  final String ownerName;
  final List<WorkspaceMember> members;
  final WorkspaceSettings settings;
  final List<String> tags;
  final DateTime? lastActivityAt;
  final bool isPublic;
  final bool isArchived;

  const CollaborationWorkspace({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.workspaceCode,
    required this.workspaceName,
    required this.description,
    required this.type,
    required this.status,
    required this.ownerId,
    required this.ownerName,
    this.members = const [],
    required this.settings,
    this.tags = const [],
    this.lastActivityAt,
    this.isPublic = false,
    this.isArchived = false,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        workspaceCode,
        workspaceName,
        description,
        type,
        status,
        ownerId,
        ownerName,
        members,
        settings,
        tags,
        lastActivityAt,
        isPublic,
        isArchived,
      ];

  /// Check if workspace is active
  bool get isActive => status == CommonStatus.active && !isArchived;

  /// Get member count
  int get memberCount => members.length;

  /// Get active member count
  int get activeMemberCount => members.where((m) => m.isActive).length;

  /// Check if user is member
  bool isMember(String userId) {
    return members.any((m) => m.userId == userId);
  }

  /// Check if user is admin
  bool isAdmin(String userId) {
    return ownerId == userId || members.any((m) => m.userId == userId && m.role == WorkspaceRole.admin);
  }

  /// Get member by user ID
  WorkspaceMember? getMember(String userId) {
    try {
      return members.firstWhere((m) => m.userId == userId);
    } catch (e) {
      return null;
    }
  }
}

/// Workspace member
class WorkspaceMember extends Equatable {
  final String userId;
  final String userName;
  final String userEmail;
  final String? userAvatar;
  final WorkspaceRole role;
  final DateTime joinedAt;
  final DateTime? lastSeenAt;
  final bool isActive;
  final bool isOnline;
  final Map<String, dynamic> permissions;

  const WorkspaceMember({
    required this.userId,
    required this.userName,
    required this.userEmail,
    this.userAvatar,
    required this.role,
    required this.joinedAt,
    this.lastSeenAt,
    this.isActive = true,
    this.isOnline = false,
    this.permissions = const {},
  });

  @override
  List<Object?> get props => [
        userId,
        userName,
        userEmail,
        userAvatar,
        role,
        joinedAt,
        lastSeenAt,
        isActive,
        isOnline,
        permissions,
      ];

  /// Check if member has permission
  bool hasPermission(String permission) {
    return permissions[permission] == true;
  }
}

/// Workspace settings
class WorkspaceSettings extends Equatable {
  final bool allowGuestAccess;
  final bool requireApprovalToJoin;
  final bool allowFileSharing;
  final bool allowScreenSharing;
  final bool enableNotifications;
  final bool enableRealTimeUpdates;
  final int maxMembers;
  final Map<String, dynamic> customSettings;

  const WorkspaceSettings({
    this.allowGuestAccess = false,
    this.requireApprovalToJoin = true,
    this.allowFileSharing = true,
    this.allowScreenSharing = true,
    this.enableNotifications = true,
    this.enableRealTimeUpdates = true,
    this.maxMembers = 100,
    this.customSettings = const {},
  });

  @override
  List<Object?> get props => [
        allowGuestAccess,
        requireApprovalToJoin,
        allowFileSharing,
        allowScreenSharing,
        enableNotifications,
        enableRealTimeUpdates,
        maxMembers,
        customSettings,
      ];
}

/// Collaboration message entity
class CollaborationMessage extends BaseEntity {
  final String messageCode;
  final String workspaceId;
  final String channelId;
  final String senderId;
  final String senderName;
  final String? senderAvatar;
  final MessageType type;
  final String content;
  final List<MessageAttachment> attachments;
  final String? replyToMessageId;
  final List<MessageReaction> reactions;
  final List<String> mentions;
  final MessageStatus status;
  final DateTime? editedAt;
  final bool isPinned;
  final bool isThreadStarter;
  final int threadCount;

  const CollaborationMessage({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.messageCode,
    required this.workspaceId,
    required this.channelId,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    required this.type,
    required this.content,
    this.attachments = const [],
    this.replyToMessageId,
    this.reactions = const [],
    this.mentions = const [],
    this.status = MessageStatus.sent,
    this.editedAt,
    this.isPinned = false,
    this.isThreadStarter = false,
    this.threadCount = 0,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        messageCode,
        workspaceId,
        channelId,
        senderId,
        senderName,
        senderAvatar,
        type,
        content,
        attachments,
        replyToMessageId,
        reactions,
        mentions,
        status,
        editedAt,
        isPinned,
        isThreadStarter,
        threadCount,
      ];

  /// Check if message is edited
  bool get isEdited => editedAt != null;

  /// Check if message is reply
  bool get isReply => replyToMessageId != null;

  /// Check if message has thread
  bool get hasThread => threadCount > 0;

  /// Get reaction count
  int get reactionCount => reactions.fold(0, (sum, r) => sum + r.count);

  /// Check if user reacted
  bool hasUserReacted(String userId, String emoji) {
    return reactions.any((r) => r.emoji == emoji && r.userIds.contains(userId));
  }
}

/// Message attachment
class MessageAttachment extends Equatable {
  final String attachmentId;
  final String fileName;
  final String fileType;
  final int fileSize;
  final String? fileUrl;
  final String? thumbnailUrl;
  final Map<String, dynamic> metadata;

  const MessageAttachment({
    required this.attachmentId,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    this.fileUrl,
    this.thumbnailUrl,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [
        attachmentId,
        fileName,
        fileType,
        fileSize,
        fileUrl,
        thumbnailUrl,
        metadata,
      ];

  /// Check if attachment is image
  bool get isImage => ['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileType.toLowerCase());

  /// Check if attachment is document
  bool get isDocument => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].contains(fileType.toLowerCase());

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

/// Message reaction
class MessageReaction extends Equatable {
  final String emoji;
  final List<String> userIds;
  final int count;

  const MessageReaction({
    required this.emoji,
    required this.userIds,
    required this.count,
  });

  @override
  List<Object?> get props => [emoji, userIds, count];
}

/// Collaboration channel entity
class CollaborationChannel extends BaseEntity {
  final String channelCode;
  final String workspaceId;
  final String channelName;
  final String description;
  final ChannelType type;
  final CommonStatus status;
  final String createdBy;
  final List<String> memberIds;
  final ChannelSettings settings;
  final DateTime? lastMessageAt;
  final int messageCount;
  final int unreadCount;
  final bool isArchived;

  const CollaborationChannel({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.channelCode,
    required this.workspaceId,
    required this.channelName,
    required this.description,
    required this.type,
    required this.status,
    required this.createdBy,
    this.memberIds = const [],
    required this.settings,
    this.lastMessageAt,
    this.messageCount = 0,
    this.unreadCount = 0,
    this.isArchived = false,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        channelCode,
        workspaceId,
        channelName,
        description,
        type,
        status,
        createdBy,
        memberIds,
        settings,
        lastMessageAt,
        messageCount,
        unreadCount,
        isArchived,
      ];

  /// Check if channel is active
  bool get isActive => status == CommonStatus.active && !isArchived;

  /// Get member count
  int get memberCount => memberIds.length;

  /// Check if user is member
  bool isMember(String userId) {
    return memberIds.contains(userId);
  }

  /// Check if channel has unread messages
  bool get hasUnreadMessages => unreadCount > 0;
}

/// Channel settings
class ChannelSettings extends Equatable {
  final bool isPrivate;
  final bool allowThreads;
  final bool allowReactions;
  final bool allowFileUploads;
  final bool muteNotifications;
  final Map<String, dynamic> customSettings;

  const ChannelSettings({
    this.isPrivate = false,
    this.allowThreads = true,
    this.allowReactions = true,
    this.allowFileUploads = true,
    this.muteNotifications = false,
    this.customSettings = const {},
  });

  @override
  List<Object?> get props => [
        isPrivate,
        allowThreads,
        allowReactions,
        allowFileUploads,
        muteNotifications,
        customSettings,
      ];
}

/// Real-time activity entity
class RealTimeActivity extends BaseEntity {
  final String activityCode;
  final String workspaceId;
  final String? channelId;
  final String userId;
  final String userName;
  final String? userAvatar;
  final ActivityType type;
  final String description;
  final Map<String, dynamic> data;
  final List<String> affectedUserIds;
  final ActivityPriority priority;
  final bool isRead;

  const RealTimeActivity({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.activityCode,
    required this.workspaceId,
    this.channelId,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.type,
    required this.description,
    this.data = const {},
    this.affectedUserIds = const [],
    this.priority = ActivityPriority.normal,
    this.isRead = false,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        activityCode,
        workspaceId,
        channelId,
        userId,
        userName,
        userAvatar,
        type,
        description,
        data,
        affectedUserIds,
        priority,
        isRead,
      ];

  /// Check if activity affects user
  bool affectsUser(String userId) {
    return affectedUserIds.contains(userId) || this.userId == userId;
  }
}

/// Collaboration session entity
class CollaborationSession extends BaseEntity {
  final String sessionCode;
  final String workspaceId;
  final String? channelId;
  final String hostId;
  final String hostName;
  final SessionType type;
  final String title;
  final String description;
  final SessionStatus status;
  final DateTime? startedAt;
  final DateTime? endedAt;
  final List<SessionParticipant> participants;
  final SessionSettings settings;
  final Map<String, dynamic> sessionData;

  const CollaborationSession({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.sessionCode,
    required this.workspaceId,
    this.channelId,
    required this.hostId,
    required this.hostName,
    required this.type,
    required this.title,
    required this.description,
    required this.status,
    this.startedAt,
    this.endedAt,
    this.participants = const [],
    required this.settings,
    this.sessionData = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        sessionCode,
        workspaceId,
        channelId,
        hostId,
        hostName,
        type,
        title,
        description,
        status,
        startedAt,
        endedAt,
        participants,
        settings,
        sessionData,
      ];

  /// Check if session is active
  bool get isActive => status == SessionStatus.active;

  /// Get participant count
  int get participantCount => participants.length;

  /// Get active participant count
  int get activeParticipantCount => participants.where((p) => p.isActive).length;

  /// Get session duration
  Duration? get duration {
    if (startedAt == null) return null;
    final endTime = endedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }

  /// Check if user is participant
  bool isParticipant(String userId) {
    return participants.any((p) => p.userId == userId);
  }
}

/// Session participant
class SessionParticipant extends Equatable {
  final String userId;
  final String userName;
  final String? userAvatar;
  final DateTime joinedAt;
  final DateTime? leftAt;
  final bool isActive;
  final bool isMuted;
  final bool isScreenSharing;
  final ParticipantRole role;

  const SessionParticipant({
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.joinedAt,
    this.leftAt,
    this.isActive = true,
    this.isMuted = false,
    this.isScreenSharing = false,
    this.role = ParticipantRole.participant,
  });

  @override
  List<Object?> get props => [
        userId,
        userName,
        userAvatar,
        joinedAt,
        leftAt,
        isActive,
        isMuted,
        isScreenSharing,
        role,
      ];
}

/// Session settings
class SessionSettings extends Equatable {
  final bool allowScreenSharing;
  final bool allowFileSharing;
  final bool allowRecording;
  final bool requirePassword;
  final String? password;
  final int maxParticipants;
  final Map<String, dynamic> customSettings;

  const SessionSettings({
    this.allowScreenSharing = true,
    this.allowFileSharing = true,
    this.allowRecording = false,
    this.requirePassword = false,
    this.password,
    this.maxParticipants = 50,
    this.customSettings = const {},
  });

  @override
  List<Object?> get props => [
        allowScreenSharing,
        allowFileSharing,
        allowRecording,
        requirePassword,
        password,
        maxParticipants,
        customSettings,
      ];
}

// Enums

/// Workspace type
enum WorkspaceType {
  general,
  project,
  department,
  team,
  temporary,
}

/// Workspace type extension
extension WorkspaceTypeExtension on WorkspaceType {
  String get displayName {
    switch (this) {
      case WorkspaceType.general:
        return 'General Workspace';
      case WorkspaceType.project:
        return 'Project Workspace';
      case WorkspaceType.department:
        return 'Department Workspace';
      case WorkspaceType.team:
        return 'Team Workspace';
      case WorkspaceType.temporary:
        return 'Temporary Workspace';
    }
  }

  String get value => name;
}

/// Workspace role
enum WorkspaceRole {
  owner,
  admin,
  moderator,
  member,
  guest,
}

/// Workspace role extension
extension WorkspaceRoleExtension on WorkspaceRole {
  String get displayName {
    switch (this) {
      case WorkspaceRole.owner:
        return 'Owner';
      case WorkspaceRole.admin:
        return 'Admin';
      case WorkspaceRole.moderator:
        return 'Moderator';
      case WorkspaceRole.member:
        return 'Member';
      case WorkspaceRole.guest:
        return 'Guest';
    }
  }

  String get value => name;
}

/// Message type
enum MessageType {
  text,
  file,
  image,
  system,
  announcement,
}

/// Message type extension
extension MessageTypeExtension on MessageType {
  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'Text Message';
      case MessageType.file:
        return 'File Message';
      case MessageType.image:
        return 'Image Message';
      case MessageType.system:
        return 'System Message';
      case MessageType.announcement:
        return 'Announcement';
    }
  }

  String get value => name;
}

/// Message status
enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

/// Message status extension
extension MessageStatusExtension on MessageStatus {
  String get displayName {
    switch (this) {
      case MessageStatus.sending:
        return 'Sending';
      case MessageStatus.sent:
        return 'Sent';
      case MessageStatus.delivered:
        return 'Delivered';
      case MessageStatus.read:
        return 'Read';
      case MessageStatus.failed:
        return 'Failed';
    }
  }

  String get value => name;
}

/// Channel type
enum ChannelType {
  general,
  announcement,
  project,
  department,
  direct,
  group,
}

/// Channel type extension
extension ChannelTypeExtension on ChannelType {
  String get displayName {
    switch (this) {
      case ChannelType.general:
        return 'General Channel';
      case ChannelType.announcement:
        return 'Announcement Channel';
      case ChannelType.project:
        return 'Project Channel';
      case ChannelType.department:
        return 'Department Channel';
      case ChannelType.direct:
        return 'Direct Message';
      case ChannelType.group:
        return 'Group Channel';
    }
  }

  String get value => name;
}

/// Activity type
enum ActivityType {
  userJoined,
  userLeft,
  messagePosted,
  fileShared,
  channelCreated,
  channelUpdated,
  workspaceUpdated,
  sessionStarted,
  sessionEnded,
  taskAssigned,
  taskCompleted,
  systemUpdate,
}

/// Activity type extension
extension ActivityTypeExtension on ActivityType {
  String get displayName {
    switch (this) {
      case ActivityType.userJoined:
        return 'User Joined';
      case ActivityType.userLeft:
        return 'User Left';
      case ActivityType.messagePosted:
        return 'Message Posted';
      case ActivityType.fileShared:
        return 'File Shared';
      case ActivityType.channelCreated:
        return 'Channel Created';
      case ActivityType.channelUpdated:
        return 'Channel Updated';
      case ActivityType.workspaceUpdated:
        return 'Workspace Updated';
      case ActivityType.sessionStarted:
        return 'Session Started';
      case ActivityType.sessionEnded:
        return 'Session Ended';
      case ActivityType.taskAssigned:
        return 'Task Assigned';
      case ActivityType.taskCompleted:
        return 'Task Completed';
      case ActivityType.systemUpdate:
        return 'System Update';
    }
  }

  String get value => name;
}

/// Activity priority
enum ActivityPriority {
  low,
  normal,
  high,
  urgent,
}

/// Activity priority extension
extension ActivityPriorityExtension on ActivityPriority {
  String get displayName {
    switch (this) {
      case ActivityPriority.low:
        return 'Low';
      case ActivityPriority.normal:
        return 'Normal';
      case ActivityPriority.high:
        return 'High';
      case ActivityPriority.urgent:
        return 'Urgent';
    }
  }

  String get value => name;
}

/// Session type
enum SessionType {
  meeting,
  presentation,
  training,
  brainstorming,
  review,
  standup,
}

/// Session type extension
extension SessionTypeExtension on SessionType {
  String get displayName {
    switch (this) {
      case SessionType.meeting:
        return 'Meeting';
      case SessionType.presentation:
        return 'Presentation';
      case SessionType.training:
        return 'Training';
      case SessionType.brainstorming:
        return 'Brainstorming';
      case SessionType.review:
        return 'Review';
      case SessionType.standup:
        return 'Stand-up';
    }
  }

  String get value => name;
}

/// Session status
enum SessionStatus {
  scheduled,
  active,
  paused,
  ended,
  cancelled,
}

/// Session status extension
extension SessionStatusExtension on SessionStatus {
  String get displayName {
    switch (this) {
      case SessionStatus.scheduled:
        return 'Scheduled';
      case SessionStatus.active:
        return 'Active';
      case SessionStatus.paused:
        return 'Paused';
      case SessionStatus.ended:
        return 'Ended';
      case SessionStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value => name;
}

/// Participant role
enum ParticipantRole {
  host,
  moderator,
  participant,
  observer,
}

/// Participant role extension
extension ParticipantRoleExtension on ParticipantRole {
  String get displayName {
    switch (this) {
      case ParticipantRole.host:
        return 'Host';
      case ParticipantRole.moderator:
        return 'Moderator';
      case ParticipantRole.participant:
        return 'Participant';
      case ParticipantRole.observer:
        return 'Observer';
    }
  }

  String get value => name;
}
