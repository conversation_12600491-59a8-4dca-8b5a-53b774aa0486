import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/client_entities.dart';

/// A card widget that displays order statistics
class OrderStatisticsCard extends StatelessWidget {
  final OrderStatistics statistics;
  final Function(String)? onTap;

  const OrderStatisticsCard({
    Key? key,
    required this.statistics,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap != null ? () => onTap!('all') : null,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Order Statistics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              InkWell(
                onTap: onTap != null ? () => onTap!('total_orders') : null,
                borderRadius: BorderRadius.circular(8),
                child: _buildStatisticRow(
                  context,
                  'Total Orders',
                  '${statistics.totalOrders}',
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: onTap != null ? () => onTap!('in_progress') : null,
                borderRadius: BorderRadius.circular(8),
                child: _buildStatisticRow(
                  context,
                  'In Progress',
                  '${statistics.inProgressOrders}',
                  Icons.hourglass_bottom,
                  Colors.orange,
                ),
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: onTap != null ? () => onTap!('completed') : null,
                borderRadius: BorderRadius.circular(8),
                child: _buildStatisticRow(
                  context,
                  'Completed',
                  '${statistics.completedOrders}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: onTap != null ? () => onTap!('overdue') : null,
                borderRadius: BorderRadius.circular(8),
                child: _buildStatisticRow(
                  context,
                  'Overdue',
                  '${statistics.overdueOrders}',
                  Icons.warning_amber_rounded,
                  Colors.red,
                ),
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: onTap != null ? () => onTap!('total_value') : null,
                borderRadius: BorderRadius.circular(8),
                child: _buildStatisticRow(
                  context,
                  'Total Value',
                  '\$${_formatNumber(statistics.totalValue)}',
                  Icons.attach_money,
                  Colors.purple,
                ),
              ),
              const SizedBox(height: 16),
              _buildProgressIndicator(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    final completionRate = statistics.totalOrders > 0
        ? statistics.completedOrders / statistics.totalOrders
        : 0.0;
    final onTimeRate = statistics.onTimeDeliveryRate;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildProgressRow(
          context,
          'Completion Rate',
          completionRate,
          Colors.blue,
        ),
        const SizedBox(height: 12),
        _buildProgressRow(
          context,
          'On-time Delivery',
          onTimeRate,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildProgressRow(
    BuildContext context,
    String label,
    double value,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${(value * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: value,
          backgroundColor: Colors.grey[200],
          valueColor: AlwaysStoppedAnimation<Color>(
            value >= 0.8 ? color : Colors.orange,
          ),
          minHeight: 6,
          borderRadius: BorderRadius.circular(3),
        ),
      ],
    );
  }

  String _formatNumber(double value) {
    final formatter = NumberFormat('#,##0.00', 'en_US');
    return formatter.format(value);
  }
}

/// A grid of order statistics cards
class OrderStatisticsGrid extends StatelessWidget {
  final OrderStatistics statistics;

  const OrderStatisticsGrid({
    Key? key,
    required this.statistics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildStatCard(
          context,
          'Total Orders',
          '${statistics.totalOrders}',
          Icons.shopping_cart,
          Colors.blue,
        ),
        _buildStatCard(
          context,
          'In Progress',
          '${statistics.inProgressOrders}',
          Icons.hourglass_bottom,
          Colors.orange,
        ),
        _buildStatCard(
          context,
          'Completed',
          '${statistics.completedOrders}',
          Icons.check_circle,
          Colors.green,
        ),
        _buildStatCard(
          context,
          'Overdue',
          '${statistics.overdueOrders}',
          Icons.warning_amber_rounded,
          Colors.red,
        ),
        _buildStatCard(
          context,
          'Total Value',
          '\$${_formatNumber(statistics.totalValue)}',
          Icons.attach_money,
          Colors.purple,
        ),
        _buildStatCard(
          context,
          'On-time',
          '${(statistics.onTimeDeliveryRate * 100).toStringAsFixed(1)}%',
          Icons.timer,
          Colors.teal,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
  
  String _formatNumber(double value) {
    final formatter = NumberFormat('#,##0.00', 'en_US');
    return formatter.format(value);
  }
}

// Import the OrderStatistics class from client_entities.dart
// export 'package:hm_collection/features/order_management/domain/entities/client_entities.dart' show OrderStatistics;