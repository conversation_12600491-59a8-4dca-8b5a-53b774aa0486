import 'package:flutter/material.dart';
import 'package:hm_collection/features/quality_control/presentation/widgets/quality_search_bar.dart';
import '../../domain/entities/quality_entities.dart';

// Re-export the InspectionStatus enum for convenience
export '../../domain/entities/quality_entities.dart' show InspectionStatus;

/// List view for quality inspections
class QualityInspectionListView extends StatelessWidget {
  final List<QualityInspection> inspections;
  final Function(QualityInspection)? onInspectionTap;
  final Function(QualityInspection)? onInspectionEdit;
  final Function(QualityInspection)? onInspectionDelete;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const QualityInspectionListView({
    Key? key,
    required this.inspections,
    this.onInspectionTap,
    this.onInspectionEdit,
    this.onInspectionDelete,
    this.isLoading = false,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (inspections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.verified,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No quality inspections found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Quality inspections will appear here when they are created',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: ListView.builder(
        itemCount: inspections.length,
        itemBuilder: (context, index) {
          final inspection = inspections[index];
          return QualityInspectionListTile(
            inspection: inspection,
            onTap: () => onInspectionTap?.call(inspection),
            onEdit: () => onInspectionEdit?.call(inspection),
            onDelete: () => onInspectionDelete?.call(inspection),
          );
        },
      ),
    );
  }
}

/// Individual quality inspection list tile
class QualityInspectionListTile extends StatelessWidget {
  final QualityInspection inspection;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const QualityInspectionListTile({
    Key? key,
    required this.inspection,
    this.onTap,
    this.onEdit,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        onTap: onTap,
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(inspection.status as QualityInspectionStatus),
          child: Icon(
            _getStatusIcon(inspection.status),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          inspection.inspectionNumber,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Product: ${inspection.product}'),
            Text('Inspector: ${inspection.inspectorName}'),
            Text('Status: ${inspection.status.displayName}'),
            Text('Date: ${_formatDate(inspection.inspectionDate)}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (inspection.result.qualityScore != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getScoreColor(inspection.result.qualityScore).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${inspection.result.qualityScore.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: _getScoreColor(inspection.result.qualityScore),
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    onEdit?.call();
                    break;
                  case 'delete':
                    onDelete?.call();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Edit'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('Delete', style: TextStyle(color: Colors.red)),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  Color _getStatusColor(QualityInspectionStatus status) {
    switch (status) {
      case QualityInspectionStatus.pending:
        return Colors.orange;
      case QualityInspectionStatus.inProgress:
        return Colors.blue;
      case QualityInspectionStatus.completed:
        return Colors.green;
      case QualityInspectionStatus.failed:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(InspectionStatus status) {
    switch (status) {
      case InspectionStatus.scheduled:
        return Icons.schedule;
      case InspectionStatus.inProgress:
        return Icons.play_arrow;
      case InspectionStatus.completed:
        return Icons.check_circle;
      case InspectionStatus.onHold:
        return Icons.pause_circle_filled;
      case InspectionStatus.cancelled:
        return Icons.cancel;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Quality inspection grid view
class QualityInspectionGridView extends StatelessWidget {
  final List<QualityInspection> inspections;
  final Function(QualityInspection)? onInspectionTap;
  final bool isLoading;

  const QualityInspectionGridView({
    Key? key,
    required this.inspections,
    this.onInspectionTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (inspections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.verified,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No quality inspections found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: inspections.length,
      itemBuilder: (context, index) {
        final inspection = inspections[index];
        return QualityInspectionCard(
          inspection: inspection,
          onTap: () => onInspectionTap?.call(inspection),
        );
      },
    );
  }
}

/// Individual quality inspection card
class QualityInspectionCard extends StatelessWidget {
  final QualityInspection inspection;
  final VoidCallback? onTap;

  const QualityInspectionCard({
    Key? key,
    required this.inspection,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStatusColor(inspection.status as QualityInspectionStatus),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      inspection.inspectionNumber,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                inspection.orderNumber,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                'Inspector: ${inspection.inspectorName}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _formatDate(inspection.inspectionDate),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              if (inspection.result.qualityScore != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: inspection.result.qualityScore! / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
_getScoreColor(inspection.result.qualityScore!),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${inspection.result.qualityScore.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _getScoreColor(inspection.result.qualityScore),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(QualityInspectionStatus status) {
    switch (status) {
      case QualityInspectionStatus.pending:
        return Colors.orange;
      case QualityInspectionStatus.inProgress:
        return Colors.blue;
      case QualityInspectionStatus.completed:
        return Colors.green;
      case QualityInspectionStatus.failed:
        return Colors.red;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Extension for displaying user-friendly status names
extension InspectionStatusExtension on InspectionStatus {
  String get displayName {
    switch (this) {
      case InspectionStatus.scheduled:
        return 'Scheduled';
      case InspectionStatus.inProgress:
        return 'In Progress';
      case InspectionStatus.completed:
        return 'Completed';
      case InspectionStatus.onHold:
        return 'On Hold';
      case InspectionStatus.cancelled:
        return 'Cancelled';
    }
  }
}