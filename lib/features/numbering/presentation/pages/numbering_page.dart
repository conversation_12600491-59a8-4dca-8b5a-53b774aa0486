import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
// import '../../../../shared/widgets/app_bar_widget.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../domain/entities/numbering_task.dart';
import '../bloc/numbering_bloc.dart';
import '../bloc/numbering_event.dart';
import '../bloc/numbering_state.dart';
import '../widgets/numbering_task_form.dart';
import '../widgets/numbering_task_table.dart';
import '../widgets/numbering_statistics_card.dart';
import '../widgets/numbering_filter_bar.dart';
import '../widgets/numbering_summary_card.dart';

/// Main numbering management page
class NumberingPage extends StatefulWidget {
  const NumberingPage({super.key});

  @override
  State<NumberingPage> createState() => _NumberingPageState();
}

class _NumberingPageState extends State<NumberingPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late NumberingBloc _numberingBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _numberingBloc = GetIt.instance<NumberingBloc>();
    
    // Load initial data
    _numberingBloc.add(const LoadNumberingTasks());
    _numberingBloc.add(const LoadTaskStatistics());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _numberingBloc,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Numbering Management', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),),
          backgroundColor: AppColors.primary.withOpacity(0.3),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white,),
              onPressed: () => _numberingBloc.add(const RefreshNumberingTasks()),
              tooltip: 'Refresh',
            ),
            IconButton(
              icon: const Icon(Icons.add, color: Colors.white,),
              onPressed: () => _showCreateTaskDialog(context),
              tooltip: 'Add New Task',
            ),
            PopupMenuButton<String>(
              onSelected: _handleMenuAction,
              iconColor: Colors.white,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'export',
                  child: Row(
                    children: [
                      Icon(Icons.download),
                      SizedBox(width: 8),
                      Text('Export Tasks'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'statistics',
                  child: Row(
                    children: [
                      Icon(Icons.analytics),
                      SizedBox(width: 8),
                      Text('View Statistics'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'settings',
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('Settings'),
                    ],
                  ),
                ),
              ],
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            tabs: const [
              Tab(
                icon: Icon(Icons.list),
                text: 'All Tasks',
              ),
              Tab(
                icon: Icon(Icons.assignment),
                text: 'My Tasks',
              ),
              Tab(
                icon: Icon(Icons.analytics),
                text: 'Statistics',
              ),
            ],
          ),
        ),
        body: BlocListener<NumberingBloc, NumberingState>(
          listener: _handleStateChanges,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAllTasksTab(),
              _buildMyTasksTab(),
              _buildStatisticsTab(),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _showCreateTaskDialog(context),
          tooltip: 'Add New Task',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  /// Build all tasks tab
  Widget _buildAllTasksTab() {
    return Column(
      children: [
        const NumberingFilterBar(),
        BlocBuilder<NumberingBloc, NumberingState>(
          buildWhen: (previous, current) =>
              current is NumberingTasksLoaded || current is NumberingLoading,
          builder: (context, state) {
            if (state is NumberingTasksLoaded && state.statistics != null) {
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: NumberingSummaryCard(statistics: state.statistics!),
              );
            }
            return const SizedBox.shrink();
          },
        ),
        Expanded(
          child: BlocBuilder<NumberingBloc, NumberingState>(
            builder: (context, state) {
              if (state is NumberingLoading) {
                return const LoadingWidget(message: 'Loading tasks...');
              }

              if (state is NumberingError) {
                return _buildErrorWidget(state.message);
              }

              if (state is NumberingTasksEmpty) {
                return _buildEmptyWidget();
              }

              if (state is NumberingTasksLoaded) {
                return NumberingTaskTable(
                  tasks: state.tasks,
                  selectedTaskIds: state.selectedTaskIds,
                  onTaskSelected: (taskId) =>
                      _numberingBloc.add(ToggleTaskSelection(taskId)),
                  onSelectAll: () => _numberingBloc.add(const SelectAllTasks()),
                  onDeselectAll: () => _numberingBloc.add(const DeselectAllTasks()),
                  onTaskAction: _handleTaskAction,
                  onEditTask: _showEditTaskDialog,
                  onDeleteTask: _confirmDeleteTask,
                );
              }

              if (state is NumberingSearchResults) {
                return NumberingTaskTable(
                  tasks: state.results,
                  selectedTaskIds: const {},
                  onTaskSelected: (taskId) =>
                      _numberingBloc.add(ToggleTaskSelection(taskId)),
                  onSelectAll: () => _numberingBloc.add(const SelectAllTasks()),
                  onDeselectAll: () => _numberingBloc.add(const DeselectAllTasks()),
                  onTaskAction: _handleTaskAction,
                  onEditTask: _showEditTaskDialog,
                  onDeleteTask: _confirmDeleteTask,
                );
              }

              return const SizedBox.shrink();
            },
          ),
        ),
      ],
    );
  }

  /// Build my tasks tab
  Widget _buildMyTasksTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _numberingBloc.add(const GetNewNumberingTask()),
                  icon: const Icon(Icons.assignment_add),
                  label: const Text('Get New Task'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _numberingBloc.add(const LoadMyNumberingTasks()),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh My Tasks'),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: BlocBuilder<NumberingBloc, NumberingState>(
            builder: (context, state) {
              if (state is NumberingLoading) {
                return const LoadingWidget(message: 'Loading your tasks...');
              }

              if (state is NumberingError) {
                return _buildErrorWidget(state.message);
              }

              if (state is NumberingTasksLoaded) {
                // Filter tasks assigned to current user
                // This would need proper user filtering in the BLoC
                return NumberingTaskTable(
                  tasks: state.tasks,
                  selectedTaskIds: state.selectedTaskIds,
                  onTaskSelected: (taskId) =>
                      _numberingBloc.add(ToggleTaskSelection(taskId)),
                  onSelectAll: () => _numberingBloc.add(const SelectAllTasks()),
                  onDeselectAll: () => _numberingBloc.add(const DeselectAllTasks()),
                  onTaskAction: _handleTaskAction,
                  onEditTask: _showEditTaskDialog,
                  onDeleteTask: _confirmDeleteTask,
                  showMyTasksActions: true,
                );
              }

              return _buildEmptyWidget(message: 'No tasks assigned to you');
            },
          ),
        ),
      ],
    );
  }

  /// Build statistics tab
  Widget _buildStatisticsTab() {
    return BlocBuilder<NumberingBloc, NumberingState>(
      builder: (context, state) {
        if (state is NumberingLoading) {
          return const LoadingWidget(message: 'Loading statistics...');
        }

        if (state is NumberingStatisticsLoaded) {
          return NumberingStatisticsCard(statistics: state.statistics);
        }

        if (state is NumberingTasksLoaded && state.statistics != null) {
          return NumberingStatisticsCard(statistics: state.statistics!);
        }

        return const Center(
          child: Text('No statistics available'),
        );
      },
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error',
            style: AppTextStyles.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _numberingBloc.add(const RefreshNumberingTasks()),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build empty widget
  Widget _buildEmptyWidget({String? message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.assignment_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            message ?? 'No numbering tasks found',
            style: AppTextStyles.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first numbering task to get started',
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _showCreateTaskDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Create Task'),
          ),
        ],
      ),
    );
  }

  /// Handle state changes
  void _handleStateChanges(BuildContext context, NumberingState state) {
    if (state is NumberingTaskOperationSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: AppColors.success,
        ),
      );
    }

    if (state is NumberingError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _showExportDialog();
        break;
      case 'statistics':
        _tabController.animateTo(2);
        break;
      case 'settings':
        _showSettingsDialog();
        break;
    }
  }

  /// Handle task actions
  void _handleTaskAction(String action, NumberingTask task) {
    switch (action) {
      case 'complete':
        _numberingBloc.add(CompleteNumberingTask(task.id));
        break;
      case 'send_to_munda':
        _numberingBloc.add(SendTaskToMunda(task.id));
        break;
      case 'edit':
        _showEditTaskDialog(task);
        break;
      case 'delete':
        _confirmDeleteTask(task);
        break;
    }
  }

  /// Show create task dialog
  void _showCreateTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: NumberingTaskForm(
          onSubmit: (taskData) {
            _numberingBloc.add(CreateNumberingTask(
              date: taskData['date'],
              orderNo: taskData['orderNo'],
              clientName: taskData['clientName'],
              designNo: taskData['designNo'],
              size: taskData['size'],
              color: taskData['color'],
              bundleNo: taskData['bundleNo'],
              pieceStartNo: taskData['pieceStartNo'],
              pieceEndNo: taskData['pieceEndNo'],
              ratePerPiece: taskData['ratePerPiece'],
              checkedBy: taskData['checkedBy'],
              remarks: taskData['remarks'],
            ));
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  /// Show edit task dialog
  void _showEditTaskDialog(NumberingTask task) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: NumberingTaskForm(
          task: task,
          onSubmit: (taskData) {
            _numberingBloc.add(UpdateNumberingTask(
              taskId: task.id,
              date: taskData['date'],
              orderNo: taskData['orderNo'],
              clientName: taskData['clientName'],
              designNo: taskData['designNo'],
              size: taskData['size'],
              color: taskData['color'],
              bundleNo: taskData['bundleNo'],
              pieceStartNo: taskData['pieceStartNo'],
              pieceEndNo: taskData['pieceEndNo'],
              ratePerPiece: taskData['ratePerPiece'],
              checkedBy: taskData['checkedBy'],
              remarks: taskData['remarks'],
            ));
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  /// Confirm delete task
  void _confirmDeleteTask(NumberingTask task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Task'),
        content: Text('Are you sure you want to delete task ${task.bundleNo}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _numberingBloc.add(DeleteNumberingTask(task.id));
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Show export dialog
  void _showExportDialog() {
    // Implementation for export dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  /// Show settings dialog
  void _showSettingsDialog() {
    // Implementation for settings dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings functionality coming soon')),
    );
  }
}
