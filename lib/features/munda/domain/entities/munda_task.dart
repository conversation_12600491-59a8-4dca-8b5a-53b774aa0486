import 'package:equatable/equatable.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Munda task entity representing a work assignment
class MundaTask extends AuditableEntity with AssignableMixin {
  /// Task title/name
  final String title;
  
  /// Task description
  final String description;
  
  /// Task status
  final MundaTaskStatus status;
  
  /// Priority level
  final Priority priority;
  
  /// Assigned user ID (Munda operator)
  @override
  final String? assignedTo;
  
  /// Assignment timestamp
  @override
  final DateTime? assignedAt;
  
  /// User who assigned the task
  @override
  final String? assignedBy;
  
  /// Due date for task completion
  final DateTime? dueDate;
  
  /// Task start date
  final DateTime? startedAt;
  
  /// Task completion date
  final DateTime? completedAt;
  
  /// Date when task was sent to bundling
  final DateTime? sentToBundlingAt;
  
  /// Expected number of records to be created
  final int? expectedRecords;
  
  /// Actual number of records created
  final int actualRecords;
  
  /// Target pieces count
  final int? targetPieces;
  
  /// Actual pieces count (sum from records)
  final int actualPieces;
  
  /// Target amount
  final double? targetAmount;
  
  /// Actual amount (sum from records)
  final double actualAmount;
  
  /// Related order ID if applicable
  final String? orderId;
  
  /// Related production batch ID if applicable
  final String? productionBatchId;
  
  /// Design numbers involved in this task
  final List<String> designNumbers;
  
  /// Lot numbers involved in this task
  final List<String> lotNumbers;
  
  /// Instructions for the operator
  final String? instructions;
  
  /// Notes from the operator
  final String? operatorNotes;
  
  /// Quality check notes
  final String? qualityNotes;
  
  /// Attachments (file URLs or IDs)
  final List<String> attachments;

  const MundaTask({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    super.createdBy,
    super.updatedBy,
    super.deletedBy,
    super.version = 1,
    required this.title,
    required this.description,
    required this.status,
    required this.priority,
    this.assignedTo,
    this.assignedAt,
    this.assignedBy,
    this.dueDate,
    this.startedAt,
    this.completedAt,
    this.sentToBundlingAt,
    this.expectedRecords,
    this.actualRecords = 0,
    this.targetPieces,
    this.actualPieces = 0,
    this.targetAmount,
    this.actualAmount = 0.0,
    this.orderId,
    this.productionBatchId,
    this.designNumbers = const [],
    this.lotNumbers = const [],
    this.instructions,
    this.operatorNotes,
    this.qualityNotes,
    this.attachments = const [],
  });

  /// Check if task is overdue
  bool get isOverdue {
    if (dueDate == null || status.isActive == false) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// Check if task is in progress
  bool get isInProgress => status == MundaTaskStatus.inProgress;

  /// Check if task is completed
  bool get isCompleted => status == MundaTaskStatus.completed;

  /// Check if task is sent to bundling
  bool get isSentToBundling => status == MundaTaskStatus.sentToBundling;

  /// Check if task can be started
  bool get canStart => status == MundaTaskStatus.assigned && assignedTo != null;

  /// Check if task can be completed
  bool get canComplete => status == MundaTaskStatus.inProgress && actualRecords > 0;

  /// Check if task can be sent to bundling
  bool get canSendToBundling => status == MundaTaskStatus.completed;

  /// Get completion percentage
  double get completionPercentage {
    if (expectedRecords == null || expectedRecords! == 0) return 0.0;
    return (actualRecords / expectedRecords!) * 100;
  }

  /// Get pieces completion percentage
  double get piecesCompletionPercentage {
    if (targetPieces == null || targetPieces! == 0) return 0.0;
    return (actualPieces / targetPieces!) * 100;
  }

  /// Get amount completion percentage
  double get amountCompletionPercentage {
    if (targetAmount == null || targetAmount! == 0) return 0.0;
    return (actualAmount / targetAmount!) * 100;
  }

  /// Get task duration
  Duration? get duration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }

  /// Check if task is active
  @override
  bool get isActive => status.isActive && !isDeleted;

  /// Copy with new values
  MundaTask copyWith({
    String? title,
    String? description,
    MundaTaskStatus? status,
    Priority? priority,
    String? assignedTo,
    DateTime? assignedAt,
    String? assignedBy,
    DateTime? dueDate,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? sentToBundlingAt,
    int? expectedRecords,
    int? actualRecords,
    int? targetPieces,
    int? actualPieces,
    double? targetAmount,
    double? actualAmount,
    String? orderId,
    String? productionBatchId,
    List<String>? designNumbers,
    List<String>? lotNumbers,
    String? instructions,
    String? operatorNotes,
    String? qualityNotes,
    List<String>? attachments,
    DateTime? updatedAt,
    String? updatedBy,
    int? version,
  }) {
    return MundaTask(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy,
      version: version ?? this.version + 1,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedAt: assignedAt ?? this.assignedAt,
      assignedBy: assignedBy ?? this.assignedBy,
      dueDate: dueDate ?? this.dueDate,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      sentToBundlingAt: sentToBundlingAt ?? this.sentToBundlingAt,
      expectedRecords: expectedRecords ?? this.expectedRecords,
      actualRecords: actualRecords ?? this.actualRecords,
      targetPieces: targetPieces ?? this.targetPieces,
      actualPieces: actualPieces ?? this.actualPieces,
      targetAmount: targetAmount ?? this.targetAmount,
      actualAmount: actualAmount ?? this.actualAmount,
      orderId: orderId ?? this.orderId,
      productionBatchId: productionBatchId ?? this.productionBatchId,
      designNumbers: designNumbers ?? this.designNumbers,
      lotNumbers: lotNumbers ?? this.lotNumbers,
      instructions: instructions ?? this.instructions,
      operatorNotes: operatorNotes ?? this.operatorNotes,
      qualityNotes: qualityNotes ?? this.qualityNotes,
      attachments: attachments ?? this.attachments,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'status': status.value,
      'priority': priority.value,
      'assignedTo': assignedTo,
      'assignedAt': assignedAt,
      'assignedBy': assignedBy,
      'dueDate': dueDate,
      'startedAt': startedAt,
      'completedAt': completedAt,
      'sentToBundlingAt': sentToBundlingAt,
      'expectedRecords': expectedRecords,
      'actualRecords': actualRecords,
      'targetPieces': targetPieces,
      'actualPieces': actualPieces,
      'targetAmount': targetAmount,
      'actualAmount': actualAmount,
      'orderId': orderId,
      'productionBatchId': productionBatchId,
      'designNumbers': designNumbers,
      'lotNumbers': lotNumbers,
      'instructions': instructions,
      'operatorNotes': operatorNotes,
      'qualityNotes': qualityNotes,
      'attachments': attachments,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'deletedBy': deletedBy,
      'version': version,
    };
  }

  /// Create from Firestore document
  factory MundaTask.fromFirestore(String id, Map<String, dynamic> data) {
    return MundaTask(
      id: id,
      createdAt: (data['createdAt'] as DateTime?) ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as DateTime?) ?? DateTime.now(),
      deletedAt: data['deletedAt'] as DateTime?,
      createdBy: data['createdBy'] as String?,
      updatedBy: data['updatedBy'] as String?,
      deletedBy: data['deletedBy'] as String?,
      version: data['version'] as int? ?? 1,
      title: data['title'] as String,
      description: data['description'] as String,
      status: MundaTaskStatus.fromString(data['status'] as String),
      priority: Priority.fromString(data['priority'] as String),
      assignedTo: data['assignedTo'] as String?,
      assignedAt: data['assignedAt'] as DateTime?,
      assignedBy: data['assignedBy'] as String?,
      dueDate: data['dueDate'] as DateTime?,
      startedAt: data['startedAt'] as DateTime?,
      completedAt: data['completedAt'] as DateTime?,
      sentToBundlingAt: data['sentToBundlingAt'] as DateTime?,
      expectedRecords: data['expectedRecords'] as int?,
      actualRecords: data['actualRecords'] as int? ?? 0,
      targetPieces: data['targetPieces'] as int?,
      actualPieces: data['actualPieces'] as int? ?? 0,
      targetAmount: (data['targetAmount'] as num?)?.toDouble(),
      actualAmount: (data['actualAmount'] as num?)?.toDouble() ?? 0.0,
      orderId: data['orderId'] as String?,
      productionBatchId: data['productionBatchId'] as String?,
      designNumbers: List<String>.from(data['designNumbers'] as List? ?? []),
      lotNumbers: List<String>.from(data['lotNumbers'] as List? ?? []),
      instructions: data['instructions'] as String?,
      operatorNotes: data['operatorNotes'] as String?,
      qualityNotes: data['qualityNotes'] as String?,
      attachments: List<String>.from(data['attachments'] as List? ?? []),
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        title,
        description,
        status,
        priority,
        assignedTo,
        assignedAt,
        assignedBy,
        dueDate,
        startedAt,
        completedAt,
        sentToBundlingAt,
        expectedRecords,
        actualRecords,
        targetPieces,
        actualPieces,
        targetAmount,
        actualAmount,
        orderId,
        productionBatchId,
        designNumbers,
        lotNumbers,
        instructions,
        operatorNotes,
        qualityNotes,
        attachments,
      ];

  @override
  String toString() {
    return 'MundaTask(id: $id, title: $title, status: $status, assignedTo: $assignedTo, actualRecords: $actualRecords)';
  }
}
