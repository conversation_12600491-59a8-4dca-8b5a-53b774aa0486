import 'package:flutter_test/flutter_test.dart';
import 'package:hm_collection/features/auth/domain/entities/login_credentials.dart';

void main() {
  group('LoginCredentials', () {
    test('to<PERSON><PERSON> should return a map with email, password, and remember_me', () {
      // Arrange
      const credentials = LoginCredentials(
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: true,
      );

      // Act
      final json = credentials.toJson();

      // Assert
      expect(json, isA<Map<String, dynamic>>());
      expect(json['email'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
      expect(json['remember_me'], isTrue);
    });

    test('toJson should handle null rememberMe', () {
      // Arrange
      final credentials = LoginCredentials(
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: null,
      );

      // Act
      final json = credentials.toJson();

      // Assert
      expect(json['remember_me'], isNull);
    });
  });
}
