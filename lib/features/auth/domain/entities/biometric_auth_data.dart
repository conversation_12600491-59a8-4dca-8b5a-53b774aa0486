import 'package:equatable/equatable.dart';

/// Represents biometric authentication data for a user
class BiometricAuthData extends Equatable {
  final String userId;
  final String biometricType;
  final String signature;
  final DateTime timestamp;

  const BiometricAuthData({
    required this.userId,
    required this.biometricType,
    required this.signature,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [userId, biometricType, signature, timestamp];

  @override
  bool? get stringify => true;
}
