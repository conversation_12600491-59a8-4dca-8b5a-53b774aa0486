import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_auth/firebase_auth.dart' hide AuthProvider;
import 'package:get_it/get_it.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hm_collection/core/network/api_client.dart';
import 'package:hm_collection/core/network/network_info.dart';
import 'package:hm_collection/features/auth/data/repositories/firebase_auth_repository_impl.dart';
import 'package:hm_collection/features/auth/domain/repositories/auth_repository.dart';
import 'package:hm_collection/features/auth/presentation/bloc/firebase_auth_bloc.dart';
import 'package:hm_collection/features/dashboard/data/repositories/analytics_repository_impl.dart';
import 'package:hm_collection/features/dashboard/data/repositories/dashboard_repository_impl.dart';
import 'package:hm_collection/features/dashboard/domain/repositories/dashboard_repository.dart';
import 'package:hm_collection/features/dashboard/domain/usecases/dashboard_usecases.dart';
import 'package:hm_collection/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:hm_collection/features/department/data/datasources/department_remote_data_source.dart';
import 'package:hm_collection/features/department/data/repositories/department_repository_impl.dart';
import 'package:hm_collection/features/department/domain/repositories/department_repository.dart';
import 'package:hm_collection/features/department/domain/usecases/get_departments.dart';
import 'package:hm_collection/features/department/presentation/bloc/department_bloc.dart';
import 'package:hm_collection/features/financial_management/data/repositories/financial_repository_impl.dart';
import 'package:hm_collection/features/financial_management/domain/repositories/financial_repository.dart';
import 'package:hm_collection/features/financial_management/domain/usecases/financial_usecases.dart';
import 'package:hm_collection/features/financial_management/presentation/bloc/financial_bloc.dart';
import 'package:hm_collection/features/inventory_management/data/repositories/inventory_repository_impl.dart';
import 'package:hm_collection/features/inventory_management/domain/repositories/inventory_repository.dart';
import 'package:hm_collection/features/inventory_management/domain/usecases/inventory_usecases.dart';
import 'package:hm_collection/features/inventory_management/presentation/bloc/inventory_bloc.dart';
import 'package:hm_collection/features/manufacturing/data/datasources/employee_remote_data_source.dart';
import 'package:hm_collection/features/manufacturing/data/repositories/employee_repository_impl.dart';
import 'package:hm_collection/features/manufacturing/data/repositories/firebase_employee_repository_impl.dart';
import 'package:hm_collection/features/manufacturing/domain/repositories/employee_repository.dart';
import 'package:hm_collection/features/manufacturing/domain/usecases/employee_usecases.dart';
import 'package:hm_collection/features/manufacturing/presentation/bloc/employee_bloc.dart';
import 'package:hm_collection/features/order_management/data/repositories/order_repository_impl.dart';
import 'package:hm_collection/features/order_management/domain/repositories/order_repository.dart';
import 'package:hm_collection/features/order_management/domain/usecases/order_usecases.dart';
import 'package:hm_collection/features/order_management/presentation/bloc/order_bloc.dart';
import 'package:hm_collection/features/production_planning/data/repositories/production_repository_impl.dart';
import 'package:hm_collection/features/production_planning/domain/repositories/production_repository.dart';
import 'package:hm_collection/features/production_planning/domain/usecases/production_usecases.dart';
import 'package:hm_collection/features/production_planning/presentation/bloc/production_bloc.dart';
import 'package:hm_collection/features/quality_control/data/repositories/quality_repository_impl.dart';
import 'package:hm_collection/features/quality_control/domain/repositories/quality_repository.dart';
import 'package:hm_collection/features/quality_control/domain/usecases/quality_usecases.dart';
import 'package:hm_collection/features/quality_control/presentation/bloc/quality_bloc.dart';
import 'package:hm_collection/features/resource_management/data/repositories/resource_repository_impl.dart';
import 'package:hm_collection/features/resource_management/domain/repositories/resource_repository.dart';
import 'package:hm_collection/features/resource_management/domain/usecases/resource_usecases.dart';
import 'package:hm_collection/features/resource_management/presentation/bloc/resource_bloc.dart';
import 'package:hm_collection/features/numbering/data/datasources/numbering_local_datasource.dart';
import 'package:hm_collection/features/numbering/data/datasources/numbering_remote_datasource.dart';
import 'package:hm_collection/features/numbering/data/repositories/numbering_repository_impl.dart';
import 'package:hm_collection/features/numbering/domain/repositories/numbering_repository.dart';
import 'package:hm_collection/features/numbering/domain/usecases/numbering_usecases.dart';
import 'package:hm_collection/features/numbering/presentation/bloc/numbering_bloc.dart';

// Bundling imports
import 'package:hm_collection/features/bundling/data/datasources/bundling_local_datasource.dart';
import 'package:hm_collection/features/bundling/data/datasources/bundling_remote_datasource.dart';
import 'package:hm_collection/features/bundling/data/repositories/bundling_repository_impl.dart';
import 'package:hm_collection/features/bundling/domain/repositories/bundling_repository.dart';
import 'package:hm_collection/features/bundling/domain/usecases/bundling_usecases.dart';
import 'package:hm_collection/features/bundling/presentation/bloc/bundling_bloc.dart';

import '../auth/providers/auth_provider.dart';
import '../firebase/firebase_auth_service.dart';

// Global GetIt instance
final GetIt getIt = GetIt.instance;

// This function configures and registers all the necessary dependencies.
Future<void> configureDependencies() async {
  // --- 1. External Packages ---
  getIt.registerLazySingleton<FirebaseAuth>(() => FirebaseAuth.instance);
  getIt.registerLazySingleton<FirebaseFirestore>(() => FirebaseFirestore.instance);
  getIt.registerLazySingleton<GoogleSignIn>(() => GoogleSignIn());
  getIt.registerLazySingleton<Connectivity>(() => Connectivity());

  // --- 2. Core Services ---
  getIt.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(getIt<Connectivity>()),
  );
  getIt.registerLazySingleton<ApiClient>(() => ApiClient());

  // SharedPreferences for local storage
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton<SharedPreferences>(() => sharedPreferences);

  getIt.registerLazySingleton<FirebaseAuthService>(
    () => FirebaseAuthService(
      getIt<FirebaseAuth>(),
      getIt<FirebaseFirestore>(),
      getIt<GoogleSignIn>(),
    ),
  );

  // --- 3. Data Sources ---
  getIt.registerLazySingleton<DepartmentRemoteDataSource>(
    () => DepartmentRemoteDataSourceImpl(firestore: getIt<FirebaseFirestore>()),
  );
  getIt.registerLazySingleton<EmployeeRemoteDataSource>(
    () => EmployeeRemoteDataSourceImpl(firestore: getIt<FirebaseFirestore>()),
  );

  // Numbering data sources
  getIt.registerLazySingleton<NumberingRemoteDataSource>(
    () => NumberingRemoteDataSource(getIt<FirebaseFirestore>()),
  );
  getIt.registerLazySingleton<NumberingLocalDataSource>(
    () => NumberingLocalDataSource(getIt<SharedPreferences>()),
  );

  // Bundling data sources
  getIt.registerLazySingleton<BundlingRemoteDataSource>(
    () => BundlingRemoteDataSource(getIt<FirebaseFirestore>()),
  );
  getIt.registerLazySingleton<BundlingLocalDataSource>(
    () => BundlingLocalDataSource(getIt<SharedPreferences>()),
  );

  // --- 4. Repositories ---
  getIt.registerLazySingleton<AuthRepository>(
    () => FirebaseAuthRepositoryImpl(
      getIt<FirebaseAuthService>(),
    ),
  );
  getIt.registerLazySingleton<DashboardRepository>(
    () => DashboardRepositoryImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<AnalyticsRepository>(
    () => AnalyticsRepositoryImpl(),
  );
  getIt.registerLazySingleton<OrderRepository>(
    () => OrderRepositoryImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<InventoryRepository>(
    () => InventoryRepositoryImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<ProductionRepository>(
    () => ProductionRepositoryImpl(getIt<FirebaseFirestore>(), getIt<FirebaseAuth>()),
  );
  getIt.registerLazySingleton<QualityRepository>(
    () => QualityRepositoryImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<ResourceRepository>(
    () => ResourceRepositoryImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<FinancialRepository>(
    () => FinancialRepositoryImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<DepartmentRepository>(
    () => DepartmentRepositoryImpl(remoteDataSource: getIt<DepartmentRemoteDataSource>()),
  );
  getIt.registerLazySingleton<EmployeeRepository>(
    () => FirebaseEmployeeRepositoryImpl(
      firestore: getIt<FirebaseFirestore>(),
      auth: getIt<FirebaseAuth>(),
    ),
  );

  // Numbering repository
  getIt.registerLazySingleton<NumberingRepository>(
    () => NumberingRepositoryImpl(
      getIt<NumberingRemoteDataSource>(),
      getIt<NumberingLocalDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // Bundling repository
  getIt.registerLazySingleton<BundlingRepository>(
    () => BundlingRepositoryImpl(
      getIt<BundlingRemoteDataSource>(),
      getIt<BundlingLocalDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // --- 5. Use Cases ---
  getIt.registerLazySingleton<GetDashboardLayoutUseCase>(
      () => GetDashboardLayoutUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<UpdateDashboardLayoutUseCase>(
      () => UpdateDashboardLayoutUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<GetDashboardStatisticsUseCase>(
      () => GetDashboardStatisticsUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<GetDashboardChartsUseCase>(
      () => GetDashboardChartsUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<GetRecentActivitiesUseCase>(
      () => GetRecentActivitiesUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<GetNotificationsUseCase>(
      () => GetNotificationsUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<MarkNotificationAsReadUseCase>(
      () => MarkNotificationAsReadUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<GetQuickActionsUseCase>(
      () => GetQuickActionsUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<GetWidgetDataUseCase>(
      () => GetWidgetDataUseCase(getIt<DashboardRepository>()));
  getIt.registerLazySingleton<GetAnalyticsUseCase>(
      () => GetAnalyticsUseCase(getIt<AnalyticsRepository>()));

  // Order Use Cases
  getIt.registerLazySingleton<GetOrdersUseCase>(
      () => GetOrdersUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<GetOrderByIdUseCase>(
      () => GetOrderByIdUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<CreateOrderUseCase>(
      () => CreateOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<UpdateOrderUseCase>(
      () => UpdateOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<DeleteOrderUseCase>(
      () => DeleteOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<UpdateOrderStatusUseCase>(
      () => UpdateOrderStatusUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<SearchOrdersUseCase>(
      () => SearchOrdersUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<GetOrdersByClientUseCase>(
      () => GetOrdersByClientUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<GetOverdueOrdersUseCase>(
      () => GetOverdueOrdersUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<GetUrgentOrdersUseCase>(
      () => GetUrgentOrdersUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<CancelOrderUseCase>(
      () => CancelOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<DuplicateOrderUseCase>(
      () => DuplicateOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<GetOrderStatisticsUseCase>(
      () => GetOrderStatisticsUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<GetOrderTimelineUseCase>(
      () => GetOrderTimelineUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<AddOrderNoteUseCase>(
      () => AddOrderNoteUseCase(getIt<OrderRepository>()));

  // Inventory Use Cases
  getIt.registerLazySingleton<GetInventoryItemsUseCase>(
      () => GetInventoryItemsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetInventoryItemByIdUseCase>(
      () => GetInventoryItemByIdUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<CreateInventoryItemUseCase>(
      () => CreateInventoryItemUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<UpdateInventoryItemUseCase>(
      () => UpdateInventoryItemUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<DeleteInventoryItemUseCase>(
      () => DeleteInventoryItemUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<UpdateStockLevelUseCase>(
      () => UpdateStockLevelUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<AdjustStockUseCase>(
      () => AdjustStockUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<ReserveStockUseCase>(
      () => ReserveStockUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<ReleaseReservedStockUseCase>(
      () => ReleaseReservedStockUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetStockMovementsUseCase>(
      () => GetStockMovementsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetStockMovementsForItemUseCase>(
      () => GetStockMovementsForItemUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetLowStockItemsUseCase>(
      () => GetLowStockItemsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetOutOfStockItemsUseCase>(
      () => GetOutOfStockItemsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetOverstockedItemsUseCase>(
      () => GetOverstockedItemsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetExpiringItemsUseCase>(
      () => GetExpiringItemsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<SearchInventoryItemsUseCase>(
      () => SearchInventoryItemsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetInventoryStatisticsUseCase>(
      () => GetInventoryStatisticsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GetInventoryValuationUseCase>(
      () => GetInventoryValuationUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<GenerateReorderSuggestionsUseCase>(
      () => GenerateReorderSuggestionsUseCase(getIt<InventoryRepository>()));
  getIt.registerLazySingleton<CreateAutomaticReorderUseCase>(
      () => CreateAutomaticReorderUseCase(getIt<InventoryRepository>()));

  // Production Use Cases
  getIt.registerLazySingleton<GetProductionOrdersUseCase>(
      () => GetProductionOrdersUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetProductionOrderByIdUseCase>(
      () => GetProductionOrderByIdUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<CreateProductionOrderUseCase>(
      () => CreateProductionOrderUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<UpdateProductionOrderUseCase>(
      () => UpdateProductionOrderUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<UpdateProductionOrderStatusUseCase>(
      () => UpdateProductionOrderStatusUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<StartProductionOrderUseCase>(
      () => StartProductionOrderUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<PauseProductionOrderUseCase>(
      () => PauseProductionOrderUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<ResumeProductionOrderUseCase>(
      () => ResumeProductionOrderUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<CompleteProductionOrderUseCase>(
      () => CompleteProductionOrderUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetProductionTasksUseCase>(
      () => GetProductionTasksUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<UpdateTaskStatusUseCase>(
      () => UpdateTaskStatusUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<AssignTaskToWorkersUseCase>(
      () => AssignTaskToWorkersUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<UpdateTaskProgressUseCase>(
      () => UpdateTaskProgressUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetResourceAllocationsUseCase>(
      () => GetResourceAllocationsUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<AllocateResourceUseCase>(
      () => AllocateResourceUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<ReleaseResourceUseCase>(
      () => ReleaseResourceUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetProductionStatisticsUseCase>(
      () => GetProductionStatisticsUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetProductionCapacityUseCase>(
      () => GetProductionCapacityUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetProductionBottlenecksUseCase>(
      () => GetProductionBottlenecksUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetOverdueProductionOrdersUseCase>(
      () => GetOverdueProductionOrdersUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<GetUrgentProductionOrdersUseCase>(
      () => GetUrgentProductionOrdersUseCase(getIt<ProductionRepository>()));
  getIt.registerLazySingleton<SearchProductionOrdersUseCase>(
      () => SearchProductionOrdersUseCase(getIt<ProductionRepository>()));

  // Numbering Use Cases
  getIt.registerLazySingleton<GetAllNumberingTasksUseCase>(
      () => GetAllNumberingTasksUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<GetNumberingTaskByIdUseCase>(
      () => GetNumberingTaskByIdUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<CreateNumberingTaskUseCase>(
      () => CreateNumberingTaskUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<UpdateNumberingTaskUseCase>(
      () => UpdateNumberingTaskUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<DeleteNumberingTaskUseCase>(
      () => DeleteNumberingTaskUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<GetNewTaskUseCase>(
      () => GetNewTaskUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<CompleteTaskUseCase>(
      () => CompleteTaskUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<SendTaskToMundaUseCase>(
      () => SendTaskToMundaUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<GetTaskStatisticsUseCase>(
      () => GetTaskStatisticsUseCase(getIt<NumberingRepository>()));
  getIt.registerLazySingleton<SearchTasksUseCase>(
      () => SearchTasksUseCase(getIt<NumberingRepository>()));

  // Bundling Use Cases
  getIt.registerLazySingleton<GetAllBundlesUseCase>(
      () => GetAllBundlesUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<GetBundleByIdUseCase>(
      () => GetBundleByIdUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<CreateBundlesUseCase>(
      () => CreateBundlesUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<UpdateBundleUseCase>(
      () => UpdateBundleUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<DeleteBundleUseCase>(
      () => DeleteBundleUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<IssueBundleToSewingUseCase>(
      () => IssueBundleToSewingUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<StartBundleWorkUseCase>(
      () => StartBundleWorkUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<CompleteBundleWorkUseCase>(
      () => CompleteBundleWorkUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<CancelBundleUseCase>(
      () => CancelBundleUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<GetBundlesAssignedToUserUseCase>(
      () => GetBundlesAssignedToUserUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<GetBundlesByStatusUseCase>(
      () => GetBundlesByStatusUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<SearchBundlesUseCase>(
      () => SearchBundlesUseCase(getIt<BundlingRepository>()));
  getIt.registerLazySingleton<GetBundleStatisticsUseCase>(
      () => GetBundleStatisticsUseCase(getIt<BundlingRepository>()));

  // Quality Use Cases
  getIt.registerLazySingleton<GetQualityInspectionsUseCase>(
      () => GetQualityInspectionsUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetQualityInspectionByIdUseCase>(
      () => GetQualityInspectionByIdUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<CreateQualityInspectionUseCase>(
      () => CreateQualityInspectionUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<UpdateQualityInspectionUseCase>(
      () => UpdateQualityInspectionUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<StartQualityInspectionUseCase>(
      () => StartQualityInspectionUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<CompleteQualityInspectionUseCase>(
      () => CompleteQualityInspectionUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<UpdateCheckpointResultUseCase>(
      () => UpdateCheckpointResultUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<AddQualityDefectUseCase>(
      () => AddQualityDefectUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<UpdateQualityDefectUseCase>(
      () => UpdateQualityDefectUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<ResolveQualityDefectUseCase>(
      () => ResolveQualityDefectUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetQualityDefectsUseCase>(
      () => GetQualityDefectsUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetQualityStandardsUseCase>(
      () => GetQualityStandardsUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<CreateQualityStandardUseCase>(
      () => CreateQualityStandardUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<ApproveQualityStandardUseCase>(
      () => ApproveQualityStandardUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetQualityStatisticsUseCase>(
      () => GetQualityStatisticsUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetQualityTrendsUseCase>(
      () => GetQualityTrendsUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetDefectAnalysisUseCase>(
      () => GetDefectAnalysisUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<SearchQualityInspectionsUseCase>(
      () => SearchQualityInspectionsUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetPendingInspectionsUseCase>(
      () => GetPendingInspectionsUseCase(getIt<QualityRepository>()));
  getIt.registerLazySingleton<GetFailedInspectionsUseCase>(
      () => GetFailedInspectionsUseCase(getIt<QualityRepository>()));

  // Resource Use Cases
  getIt.registerLazySingleton<GetMachinesUseCase>(
      () => GetMachinesUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetMachineByIdUseCase>(
      () => GetMachineByIdUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateMachineUseCase>(
      () => CreateMachineUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<UpdateMachineUseCase>(
      () => UpdateMachineUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<UpdateMachineStatusUseCase>(
      () => UpdateMachineStatusUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetMachineSchedulesUseCase>(
      () => GetMachineSchedulesUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateMachineScheduleUseCase>(
      () => CreateMachineScheduleUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetMachineMaintenanceRecordsUseCase>(
      () => GetMachineMaintenanceRecordsUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateMaintenanceRecordUseCase>(
      () => CreateMaintenanceRecordUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CompleteMaintenanceUseCase>(
      () => CompleteMaintenanceUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetAvailableMachinesUseCase>(
      () => GetAvailableMachinesUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetMachinesDueForMaintenanceUseCase>(
      () => GetMachinesDueForMaintenanceUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetMachineUtilizationReportUseCase>(
      () => GetMachineUtilizationReportUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<SearchMachinesUseCase>(
      () => SearchMachinesUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetWorkersUseCase>(
      () => GetWorkersUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetWorkerByIdUseCase>(
      () => GetWorkerByIdUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateWorkerUseCase>(
      () => CreateWorkerUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<UpdateWorkerUseCase>(
      () => UpdateWorkerUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<UpdateWorkerStatusUseCase>(
      () => UpdateWorkerStatusUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetWorkerSchedulesUseCase>(
      () => GetWorkerSchedulesUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateWorkerScheduleUseCase>(
      () => CreateWorkerScheduleUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetWorkerAttendanceUseCase>(
      () => GetWorkerAttendanceUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<RecordAttendanceUseCase>(
      () => RecordAttendanceUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetWorkerLeaveRequestsUseCase>(
      () => GetWorkerLeaveRequestsUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateLeaveRequestUseCase>(
      () => CreateLeaveRequestUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<ApproveLeaveRequestUseCase>(
      () => ApproveLeaveRequestUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetAvailableWorkersUseCase>(
      () => GetAvailableWorkersUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetWorkerPerformanceReportUseCase>(
      () => GetWorkerPerformanceReportUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<SearchWorkersUseCase>(
      () => SearchWorkersUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetFacilitiesUseCase>(
      () => GetFacilitiesUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetFacilityByIdUseCase>(
      () => GetFacilityByIdUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateFacilityUseCase>(
      () => CreateFacilityUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetDepartmentsUseCase>(
      () => GetDepartmentsUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<CreateDepartmentUseCase>(
      () => CreateDepartmentUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetFacilityCapacityReportUseCase>(
      () => GetFacilityCapacityReportUseCase(getIt<ResourceRepository>()));
  getIt.registerLazySingleton<GetFacilityUtilizationReportUseCase>(
      () => GetFacilityUtilizationReportUseCase(getIt<ResourceRepository>()));

  // Department Use Cases
  getIt.registerLazySingleton<GetDepartments>(() => GetDepartments(getIt<DepartmentRepository>()));

  // Employee Use Cases
  getIt.registerLazySingleton<GetEmployeesUseCase>(() => GetEmployeesUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeeByIdUseCase>(() => GetEmployeeByIdUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeeByEmployeeIdUseCase>(() => GetEmployeeByEmployeeIdUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<CreateEmployeeUseCase>(() => CreateEmployeeUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<UpdateEmployeeUseCase>(() => UpdateEmployeeUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<DeleteEmployeeUseCase>(() => DeleteEmployeeUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<SearchEmployeesUseCase>(() => SearchEmployeesUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeesByDepartmentUseCase>(() => GetEmployeesByDepartmentUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeesByRoleUseCase>(() => GetEmployeesByRoleUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeesBySupervisorUseCase>(() => GetEmployeesBySupervisorUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeeStreamUseCase>(() => GetEmployeeStreamUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeesStreamUseCase>(() => GetEmployeesStreamUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<ActivateEmployeeUseCase>(() => ActivateEmployeeUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<DeactivateEmployeeUseCase>(() => DeactivateEmployeeUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<TransferEmployeeUseCase>(() => TransferEmployeeUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<PromoteEmployeeUseCase>(() => PromoteEmployeeUseCase(getIt<EmployeeRepository>()));
  getIt.registerLazySingleton<GetEmployeeStatisticsUseCase>(() => GetEmployeeStatisticsUseCase(getIt<EmployeeRepository>()));

  // Financial Use Cases
  getIt.registerLazySingleton<GetCostCentersUseCase>(
      () => GetCostCentersUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetCostCenterByIdUseCase>(
      () => GetCostCenterByIdUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<CreateCostCenterUseCase>(
      () => CreateCostCenterUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<UpdateCostCenterUseCase>(
      () => UpdateCostCenterUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<DeleteCostCenterUseCase>(
      () => DeleteCostCenterUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetTransactionsUseCase>(
      () => GetTransactionsUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetTransactionByIdUseCase>(
      () => GetTransactionByIdUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<CreateTransactionUseCase>(
      () => CreateTransactionUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<UpdateTransactionUseCase>(
      () => UpdateTransactionUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<DeleteTransactionUseCase>(
      () => DeleteTransactionUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<ApproveTransactionUseCase>(
      () => ApproveTransactionUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<RejectTransactionUseCase>(
      () => RejectTransactionUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetBudgetsUseCase>(
      () => GetBudgetsUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetBudgetByIdUseCase>(
      () => GetBudgetByIdUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<CreateBudgetUseCase>(
      () => CreateBudgetUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<UpdateBudgetUseCase>(
      () => UpdateBudgetUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<DeleteBudgetUseCase>(
      () => DeleteBudgetUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<ApproveBudgetUseCase>(
      () => ApproveBudgetUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<RejectBudgetUseCase>(
      () => RejectBudgetUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetReportsUseCase>(
      () => GetReportsUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GenerateReportUseCase>(
      () => GenerateReportUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetProfitLossStatementUseCase>(
      () => GetProfitLossStatementUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetCostAnalysisReportUseCase>(
      () => GetCostAnalysisReportUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetBudgetVarianceReportUseCase>(
      () => GetBudgetVarianceReportUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetFinancialSummaryUseCase>(
      () => GetFinancialSummaryUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<GetCostTrendsUseCase>(
      () => GetCostTrendsUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<SearchCostCentersUseCase>(
      () => SearchCostCentersUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<SearchTransactionsUseCase>(
      () => SearchTransactionsUseCase(getIt<FinancialRepository>()));
  getIt.registerLazySingleton<SearchBudgetsUseCase>(
      () => SearchBudgetsUseCase(getIt<FinancialRepository>()));

  // --- 6. BLoCs & Providers ---
  getIt.registerFactory<FirebaseAuthBloc>(
    () => FirebaseAuthBloc(
      getIt<FirebaseAuthService>(),
      getIt<AuthRepository>(),
    ),
  );

  getIt.registerFactory<DashboardBloc>(
    () => DashboardBloc(
      getIt<GetDashboardLayoutUseCase>(),
      getIt<UpdateDashboardLayoutUseCase>(),
      getIt<GetDashboardStatisticsUseCase>(),
      getIt<GetDashboardChartsUseCase>(),
      getIt<GetRecentActivitiesUseCase>(),
      getIt<GetNotificationsUseCase>(), 
      getIt<MarkNotificationAsReadUseCase>(),
      getIt<GetQuickActionsUseCase>(),
      getIt<GetWidgetDataUseCase>(),
      getIt<GetAnalyticsUseCase>(),
    ),
  );

  getIt.registerFactory<OrderBloc>(
    () => OrderBloc(
      getIt<GetOrdersUseCase>(),
      getIt<GetOrderByIdUseCase>(),
      getIt<CreateOrderUseCase>(),
      getIt<UpdateOrderUseCase>(),
      getIt<DeleteOrderUseCase>(),
      getIt<UpdateOrderStatusUseCase>(),
      getIt<SearchOrdersUseCase>(),
      getIt<GetOrdersByClientUseCase>(),
      getIt<GetOverdueOrdersUseCase>(),
      getIt<GetUrgentOrdersUseCase>(),
      getIt<CancelOrderUseCase>(),
      getIt<DuplicateOrderUseCase>(),
      getIt<GetOrderStatisticsUseCase>(),
      getIt<GetOrderTimelineUseCase>(),
      getIt<AddOrderNoteUseCase>(),
    ),
  );

  getIt.registerFactory<InventoryBloc>(
    () => InventoryBloc(
      getIt<GetInventoryItemsUseCase>(),
      getIt<GetInventoryItemByIdUseCase>(),
      getIt<CreateInventoryItemUseCase>(),
      getIt<UpdateInventoryItemUseCase>(),
      getIt<DeleteInventoryItemUseCase>(),
      getIt<UpdateStockLevelUseCase>(),
      getIt<AdjustStockUseCase>(),
      getIt<ReserveStockUseCase>(),
      getIt<ReleaseReservedStockUseCase>(),
      getIt<GetStockMovementsUseCase>(),
      getIt<GetStockMovementsForItemUseCase>(),
      getIt<GetLowStockItemsUseCase>(),
      getIt<GetOutOfStockItemsUseCase>(),
      getIt<GetOverstockedItemsUseCase>(),
      getIt<GetExpiringItemsUseCase>(),
      getIt<SearchInventoryItemsUseCase>(),
      getIt<GetInventoryStatisticsUseCase>(),
      getIt<GetInventoryValuationUseCase>(),
      getIt<GenerateReorderSuggestionsUseCase>(),
      getIt<CreateAutomaticReorderUseCase>(),
    ),
  );

  getIt.registerFactory<ProductionBloc>(
    () => ProductionBloc(
      getIt<GetProductionOrdersUseCase>(),
      getIt<GetProductionOrderByIdUseCase>(),
      getIt<CreateProductionOrderUseCase>(),
      getIt<UpdateProductionOrderUseCase>(),
      getIt<UpdateProductionOrderStatusUseCase>(),
      getIt<StartProductionOrderUseCase>(),
      getIt<PauseProductionOrderUseCase>(),
      getIt<ResumeProductionOrderUseCase>(),
      getIt<CompleteProductionOrderUseCase>(),
      getIt<GetProductionTasksUseCase>(),
      getIt<UpdateTaskStatusUseCase>(),
      getIt<AssignTaskToWorkersUseCase>(),
      getIt<UpdateTaskProgressUseCase>(),
      getIt<GetResourceAllocationsUseCase>(),
      getIt<AllocateResourceUseCase>(),
      getIt<ReleaseResourceUseCase>(),
      getIt<GetProductionStatisticsUseCase>(),
      getIt<GetProductionCapacityUseCase>(),
      getIt<GetProductionBottlenecksUseCase>(),
      getIt<GetOverdueProductionOrdersUseCase>(),
      getIt<GetUrgentProductionOrdersUseCase>(),
      getIt<SearchProductionOrdersUseCase>(),
    ),
  );

  getIt.registerFactory<QualityBloc>(
    () => QualityBloc(
      getIt<GetQualityInspectionsUseCase>(),
      getIt<GetQualityInspectionByIdUseCase>(),
      getIt<CreateQualityInspectionUseCase>(),
      getIt<UpdateQualityInspectionUseCase>(),
      getIt<StartQualityInspectionUseCase>(),
      getIt<CompleteQualityInspectionUseCase>(),
      getIt<UpdateCheckpointResultUseCase>(),
      getIt<AddQualityDefectUseCase>(),
      getIt<UpdateQualityDefectUseCase>(),
      getIt<ResolveQualityDefectUseCase>(),
      getIt<GetQualityDefectsUseCase>(),
      getIt<GetQualityStandardsUseCase>(),
      getIt<CreateQualityStandardUseCase>(),
      getIt<ApproveQualityStandardUseCase>(),
      getIt<GetQualityStatisticsUseCase>(),
      getIt<GetQualityTrendsUseCase>(),
      getIt<GetDefectAnalysisUseCase>(),
      getIt<SearchQualityInspectionsUseCase>(),
      getIt<GetPendingInspectionsUseCase>(),
      getIt<GetFailedInspectionsUseCase>(),
    ),
  );

  getIt.registerFactory<ResourceBloc>(
    () => ResourceBloc(
      getIt<GetMachinesUseCase>(),
      getIt<GetMachineByIdUseCase>(),
      getIt<CreateMachineUseCase>(),
      getIt<UpdateMachineUseCase>(),
      getIt<UpdateMachineStatusUseCase>(),
      getIt<GetMachineSchedulesUseCase>(),
      getIt<CreateMachineScheduleUseCase>(),
      getIt<GetMachineMaintenanceRecordsUseCase>(),
      getIt<CreateMaintenanceRecordUseCase>(),
      getIt<CompleteMaintenanceUseCase>(),
      getIt<GetAvailableMachinesUseCase>(),
      getIt<GetMachinesDueForMaintenanceUseCase>(),
      getIt<GetMachineUtilizationReportUseCase>(),
      getIt<SearchMachinesUseCase>(),
      getIt<GetWorkersUseCase>(),
      getIt<GetWorkerByIdUseCase>(),
      getIt<CreateWorkerUseCase>(),
      getIt<UpdateWorkerUseCase>(),
      getIt<UpdateWorkerStatusUseCase>(),
      getIt<GetWorkerSchedulesUseCase>(),
      getIt<CreateWorkerScheduleUseCase>(),
      getIt<GetWorkerAttendanceUseCase>(),
      getIt<RecordAttendanceUseCase>(),
      getIt<GetWorkerLeaveRequestsUseCase>(),
      getIt<CreateLeaveRequestUseCase>(),
      getIt<ApproveLeaveRequestUseCase>(),
      getIt<GetAvailableWorkersUseCase>(),
      getIt<GetWorkerPerformanceReportUseCase>(),
      getIt<SearchWorkersUseCase>(),
      getIt<GetFacilitiesUseCase>(),
      getIt<GetFacilityByIdUseCase>(),
      getIt<CreateFacilityUseCase>(),
      getIt<GetDepartmentsUseCase>(),
      getIt<CreateDepartmentUseCase>(),
      getIt<GetFacilityCapacityReportUseCase>(),
      getIt<GetFacilityUtilizationReportUseCase>(),
    ),
  );

  getIt.registerFactory<EmployeeBloc>(
    () => EmployeeBloc(
      getIt<EmployeeRepository>(),
    ),
  );

  getIt.registerFactory<DepartmentBloc>(
    () => DepartmentBloc(getDepartments: getIt<GetDepartments>()),
  );

  getIt.registerFactory<FinancialBloc>(
    () => FinancialBloc(
      getIt<GetCostCentersUseCase>(),
      getIt<GetCostCenterByIdUseCase>(),
      getIt<CreateCostCenterUseCase>(),
      getIt<UpdateCostCenterUseCase>(),
      getIt<DeleteCostCenterUseCase>(),
      getIt<GetTransactionsUseCase>(),
      getIt<GetTransactionByIdUseCase>(),
      getIt<CreateTransactionUseCase>(),
      getIt<UpdateTransactionUseCase>(),
      getIt<DeleteTransactionUseCase>(),
      getIt<ApproveTransactionUseCase>(),
      getIt<RejectTransactionUseCase>(),
      getIt<GetBudgetsUseCase>(),
      getIt<GetBudgetByIdUseCase>(),
      getIt<CreateBudgetUseCase>(),
      getIt<UpdateBudgetUseCase>(),
      getIt<DeleteBudgetUseCase>(),
      getIt<ApproveBudgetUseCase>(),
      getIt<RejectBudgetUseCase>(),
      getIt<GetReportsUseCase>(),
      getIt<GenerateReportUseCase>(),
      getIt<GetProfitLossStatementUseCase>(),
      getIt<GetCostAnalysisReportUseCase>(),
      getIt<GetBudgetVarianceReportUseCase>(),
      getIt<GetFinancialSummaryUseCase>(),
      getIt<GetCostTrendsUseCase>(),
      getIt<SearchCostCentersUseCase>(),
      getIt<SearchTransactionsUseCase>(),
      getIt<SearchBudgetsUseCase>(),
    ),
  );

  getIt.registerFactory<NumberingBloc>(
    () => NumberingBloc(
      getIt<GetAllNumberingTasksUseCase>(),
      getIt<GetNumberingTaskByIdUseCase>(),
      getIt<CreateNumberingTaskUseCase>(),
      getIt<UpdateNumberingTaskUseCase>(),
      getIt<DeleteNumberingTaskUseCase>(),
      getIt<GetNewTaskUseCase>(),
      getIt<CompleteTaskUseCase>(),
      getIt<SendTaskToMundaUseCase>(),
      getIt<GetTaskStatisticsUseCase>(),
      getIt<SearchTasksUseCase>(),
      getIt<AuthProvider>(),
    ),
  );

  getIt.registerFactory<BundlingBloc>(
    () => BundlingBloc(
      getIt<GetAllBundlesUseCase>(),
      getIt<GetBundleByIdUseCase>(),
      getIt<CreateBundlesUseCase>(),
      getIt<UpdateBundleUseCase>(),
      getIt<DeleteBundleUseCase>(),
      getIt<IssueBundleToSewingUseCase>(),
      getIt<StartBundleWorkUseCase>(),
      getIt<CompleteBundleWorkUseCase>(),
      getIt<CancelBundleUseCase>(),
      getIt<GetBundlesAssignedToUserUseCase>(),
      getIt<GetBundlesByStatusUseCase>(),
      getIt<SearchBundlesUseCase>(),
      getIt<GetBundleStatisticsUseCase>(),
      getIt<AuthProvider>(),
    ),
  );

  getIt.registerLazySingleton<AuthProvider>(
    () => AuthProvider(getIt<FirebaseAuthService>()),
  );
}