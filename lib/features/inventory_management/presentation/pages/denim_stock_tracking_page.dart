import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_dropdown.dart';
import '../../domain/entities/denim_inventory_entities.dart';
import 'denim_purchase_page.dart';
import 'denim_return_page.dart';
import 'denim_types_master_page.dart';

class DenimStockTrackingPage extends StatefulWidget {
  const DenimStockTrackingPage({super.key});

  @override
  State<DenimStockTrackingPage> createState() => _DenimStockTrackingPageState();
}

class _DenimStockTrackingPageState extends State<DenimStockTrackingPage> {
  final _searchController = TextEditingController();

  // Filter state
  DenimRollStatus? _selectedStatus;
  String? _selectedLocation;
  String? _selectedDenimType;
  bool _showLowStock = false;

  // Mock data - in real app, this would come from repository
  final List<DenimRoll> _denimRolls = [
    DenimRoll(
      id: '1',
      rollId: 'DN001-20241201-001',
      denimTypeId: '1',
      denimCode: 'DN001',
      denimType: '12oz Indigo',
      color: 'Navy Blue',
      width: 58,
      totalPurchasedQty: 100,
      returnedQty: 0,
      usedQty: 20,
      balanceQty: 80,
      location: 'Warehouse A',
      status: DenimRollStatus.inStock,
      gsm: 320,
      supplierName: 'Textile Mills Ltd.',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now(),
    ),
    DenimRoll(
      id: '2',
      rollId: 'DN002-20241201-001',
      denimTypeId: '2',
      denimCode: 'DN002',
      denimType: 'Stretch Denim',
      color: 'Black',
      width: 60,
      totalPurchasedQty: 150,
      returnedQty: 10,
      usedQty: 30,
      balanceQty: 110,
      location: 'Warehouse B',
      status: DenimRollStatus.inStock,
      gsm: 280,
      supplierName: 'Premium Fabrics Inc.',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now(),
    ),
    DenimRoll(
      id: '3',
      rollId: 'DN001-20241130-002',
      denimTypeId: '1',
      denimCode: 'DN001',
      denimType: '12oz Indigo',
      color: 'Light Blue',
      width: 58,
      totalPurchasedQty: 80,
      returnedQty: 0,
      usedQty: 75,
      balanceQty: 5,
      location: 'Cutting Department',
      status: DenimRollStatus.sentToCutting,
      gsm: 320,
      supplierName: 'Textile Mills Ltd.',
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now(),
    ),
    DenimRoll(
      id: '4',
      rollId: 'DN003-20241129-001',
      denimTypeId: '3',
      denimCode: 'DN003',
      denimType: 'Raw Denim',
      color: 'Indigo',
      width: 56,
      totalPurchasedQty: 120,
      returnedQty: 20,
      usedQty: 0,
      balanceQty: 100,
      location: 'Warehouse A',
      status: DenimRollStatus.inStock,
      gsm: 350,
      supplierName: 'Heritage Denim Co.',
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      updatedAt: DateTime.now(),
    ),
  ];

  final List<String> _locations = ['Warehouse A', 'Warehouse B', 'Cutting Department'];
  final List<String> _denimTypes = ['12oz Indigo', 'Stretch Denim', 'Raw Denim'];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<DenimRoll> get _filteredRolls {
    var filtered = _denimRolls.where((roll) {
      // Search filter
      final searchQuery = _searchController.text.toLowerCase();
      if (searchQuery.isNotEmpty) {
        if (!roll.rollId.toLowerCase().contains(searchQuery) &&
            !roll.denimType.toLowerCase().contains(searchQuery) &&
            !roll.color.toLowerCase().contains(searchQuery)) {
          return false;
        }
      }

      // Status filter
      if (_selectedStatus != null && roll.status != _selectedStatus) {
        return false;
      }

      // Location filter
      if (_selectedLocation != null && roll.location != _selectedLocation) {
        return false;
      }

      // Denim type filter
      if (_selectedDenimType != null && roll.denimType != _selectedDenimType) {
        return false;
      }

      // Low stock filter
      if (_showLowStock && roll.balanceQty > 10) {
        return false;
      }

      return true;
    }).toList();

    // Sort by balance quantity (low stock first)
    filtered.sort((a, b) => a.balanceQty.compareTo(b.balanceQty));
    return filtered;
  }

  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedLocation = null;
      _selectedDenimType = null;
      _showLowStock = false;
      _searchController.clear();
    });
  }

  Future<void> _showSearchDialog() async {
    final searchQuery = await showDialog<String>(
      context: context,
      builder: (context) {
        final searchController = TextEditingController(text: _searchController.text);
        return AlertDialog(
          title: const Text('Search Stock'),
          content: CustomTextField(
            controller: searchController,
            label: 'Search by Roll ID, Type, or Color',
            prefixIcon: const Icon(Icons.search),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(searchController.text);
              },
              child: const Text('Search'),
            ),
          ],
        );
      },
    );

    if (searchQuery != null) {
      setState(() {
        _searchController.text = searchQuery;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredRolls = _filteredRolls;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Denim Stock Tracking',
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // In real app, this would refresh data from repository
              setState(() {});
            },
          ),
          PopupMenuButton<String>(
            iconColor: Colors.white,
            onSelected: (value) {
              switch (value) {
                case 'purchase':
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) => const DenimPurchasePage()));
                  break;
                case 'return':
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) => const DenimReturnPage()));
                  break;
                case 'master':
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) => const DenimTypesMasterPage()));
                  break;
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'purchase',
                child: Text('Denim Purchase'),
              ),
              const PopupMenuItem<String>(
                value: 'return',
                child: Text('Denim Return'),
              ),
              const PopupMenuItem<String>(
                value: 'master',
                child: Text('Denim Types Master'),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildFiltersSection(),
            _buildStockSummary(filteredRolls),
            _buildStockList(filteredRolls),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: CustomDropdown<DenimRollStatus>(
                  label: 'Status',
                  value: _selectedStatus,
                  items: DenimRollStatus.values
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status.displayName),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => _selectedStatus = value),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomDropdown<String>(
                  label: 'Location',
                  value: _selectedLocation,
                  items: _locations
                      .map((location) => DropdownMenuItem(
                            value: location,
                            child: Text(location),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => _selectedLocation = value),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomDropdown<String>(
                  label: 'Denim Type',
                  value: _selectedDenimType,
                  items: _denimTypes
                      .map((type) => DropdownMenuItem(
                            value: type,
                            child: Text(type),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => _selectedDenimType = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Show Low Stock Only'),
                  value: _showLowStock,
                  onChanged: (value) => setState(() => _showLowStock = value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              TextButton.icon(
                onPressed: _clearFilters,
                icon: const Icon(Icons.clear),
                label: const Text('Clear Filters'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStockSummary(List<DenimRoll> rolls) {
    final totalRolls = rolls.length;
    final inStockRolls = rolls.where((r) => r.status == DenimRollStatus.inStock).length;
    final lowStockRolls = rolls.where((r) => r.balanceQty <= 10).length;
    final totalValue = rolls.fold<double>(0, (sum, roll) => sum + (roll.balanceQty * 10)); // Assuming $10 per unit

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'Total Rolls',
              totalRolls.toString(),
              Icons.inventory,
              AppColors.primary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'In Stock',
              inStockRolls.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Low Stock',
              lowStockRolls.toString(),
              Icons.warning,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Total Value',
              '\$${totalValue.toStringAsFixed(0)}',
              Icons.attach_money,
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStockList(List<DenimRoll> rolls) {
    if (rolls.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No denim rolls found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Try adjusting your filters',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: rolls.length,
      itemBuilder: (context, index) {
        final roll = rolls[index];
        return _buildStockCard(roll);
      },
    );
  }

  Widget _buildStockCard(DenimRoll roll) {
    final isLowStock = roll.balanceQty <= 10;
    final utilizationPercentage = ((roll.usedQty + roll.returnedQty) / roll.totalPurchasedQty * 100);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        roll.rollId,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${roll.denimType} - ${roll.color}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(roll.status),
              ],
            ),
            const SizedBox(height: 16),

            // Stock information
            Row(
              children: [
                Expanded(
                  child: _buildStockInfo(
                    'Total Purchased',
                    '${roll.totalPurchasedQty} ${roll.unit}',
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStockInfo(
                    'Used',
                    '${roll.usedQty} ${roll.unit}',
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStockInfo(
                    'Returned',
                    '${roll.returnedQty} ${roll.unit}',
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildStockInfo(
                    'Balance',
                    '${roll.balanceQty} ${roll.unit}',
                    isLowStock ? Colors.red : Colors.green,
                    isHighlighted: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Utilization',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${utilizationPercentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: utilizationPercentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    utilizationPercentage > 80
                        ? Colors.red
                        : utilizationPercentage > 60
                            ? Colors.orange
                            : Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Additional details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    Icons.straighten,
                    'Width',
                    '${roll.width}"',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.fitness_center,
                    'GSM',
                    roll.gsm.toString(),
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.location_on,
                    'Location',
                    roll.location ?? 'N/A',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.business,
                    'Supplier',
                    roll.supplierName ?? 'N/A',
                  ),
                ),
              ],
            ),
            if (isLowStock) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    const Text(
                      'Low Stock Alert',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(DenimRollStatus status) {
    Color color;
    switch (status) {
      case DenimRollStatus.inStock:
        color = Colors.green;
        break;
      case DenimRollStatus.sentToCutting:
        color = Colors.blue;
        break;
      case DenimRollStatus.returned:
        color = Colors.red;
        break;
      case DenimRollStatus.damaged:
        color = Colors.orange;
        break;
      case DenimRollStatus.outOfStock:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStockInfo(String label, String value, Color color, {bool isHighlighted = false}) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: isHighlighted ? 16 : 14,
            fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w600,
            color: color,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
