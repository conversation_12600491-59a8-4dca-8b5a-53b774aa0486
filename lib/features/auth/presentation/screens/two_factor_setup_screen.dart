// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'dart:typed_data';
//
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
//
// // Conditional import for web
// import 'package:universal_html/html.dart' as html;
// import 'package:otp/otp.dart';
// import 'package:qr_flutter/qr_flutter.dart';
// import 'package:injectable/injectable.dart';
// import 'package:hm_collection/core/firebase/firebase_auth_service.dart';
// import 'package:provider/provider.dart';
//
// class TwoFactorSetupScreen extends StatefulWidget {
//   const TwoFactorSetupScreen({Key? key}) : super(key: key);
//
//   @override
//   _TwoFactorSetupScreenState createState() => _TwoFactorSetupScreenState();
// }
//
// class _TwoFactorSetupScreenState extends State<TwoFactorSetupScreen> {
//   final _formKey = GlobalKey<FormState>();
//   final _codeController = TextEditingController();
//   String? _secret;
//   String? _qrCodeData;
//   bool _isLoading = false;
//   bool _isVerified = false;
//   bool _showVerificationForm = false;
//   bool _showBackupCodes = false;
//   List<String> _backupCodes = [];
//   bool _codesCopied = false;
//   bool _codesDownloaded = false;
//
//   @override
//   void initState() {
//     super.initState();
//     _generateSecret();
//   }
//
//   @override
//   void dispose() {
//     _codeController.dispose();
//     super.dispose();
//   }
//
//   Future<void> _generateSecret() async {
//     setState(() => _isLoading = true);
//     try {
//       final authService = context.read<FirebaseAuthService>();
//       _secret = await authService.enableTwoFactorAuth();
//
//       // Generate TOTP URI with proper parameters
//       final userEmail = authService.currentFirebaseUser?.email ?? '<EMAIL>';
//       final appName = 'HM Collection';
//
//       // Generate TOTP code with the secret
//       final code = OTP.generateTOTPCodeString(
//         _secret!,
//         DateTime.now().millisecondsSinceEpoch,
//         algorithm: Algorithm.SHA1,
//         isGoogle: true,
//         length: 6,
//         interval: 30,
//       );
//
//       // Create the OTP Auth URI for the QR code
//       _qrCodeData = 'otpauth://totp/$appName:$userEmail?secret=${Uri.encodeComponent(_secret!)}&issuer=${Uri.encodeComponent(appName)}&algorithm=SHA1&digits=6&period=30';
//
//       setState(() => _showVerificationForm = true);
//     } catch (e) {
//       _showErrorSnackBar('Error setting up 2FA: ${e.toString()}');
//     } finally {
//       setState(() => _isLoading = false);
//     }
//   }
//
//
//
//   void _showErrorSnackBar(String message) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         backgroundColor: Colors.red,
//       ),
//     );
//   }
//
//   void _showSuccessSnackBar(String message) {
//     if (!mounted) return;
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         backgroundColor: Colors.green,
//       ),
//     );
//   }
//
//   Future<void> _verifyCode() async {
//     if (!_formKey.currentState!.validate()) return;
//
//     setState(() => _isLoading = true);
//     try {
//       final authService = context.read<FirebaseAuthService>();
//       final code = _codeController.text.trim();
//
//       // Verify the code
//       final isValid = await authService.verifyTwoFactorCode(code);
//
//       if (isValid) {
//         // Get backup codes to show to the user
//         _backupCodes = await authService.getBackupCodes();
//
//         setState(() {
//           _isVerified = true;
//           _showBackupCodes = true;
//         });
//
//         _showSuccessSnackBar('Two-factor authentication has been successfully enabled!');
//       } else {
//         _showErrorSnackBar('Invalid verification code. Please try again.');
//       }
//     } catch (e) {
//       _showErrorSnackBar('Verification failed: ${e.toString()}');
//     } finally {
//       if (mounted) {
//         setState(() => _isLoading = false);
//       }
//     }
//   }
//
//   Future<void> _copyBackupCodes() async {
//     if (_backupCodes.isEmpty) return;
//
//     final text = _backupCodes.map((code) => '• $code').join('\n');
//     await Clipboard.setData(ClipboardData(text: text));
//
//     if (!mounted) return;
//     setState(() => _codesCopied = true);
//     _showSuccessSnackBar('Backup codes copied to clipboard');
//
//     // Reset the copied state after 3 seconds
//     Future.delayed(const Duration(seconds: 3), () {
//       if (mounted) {
//         setState(() => _codesCopied = false);
//       }
//     });
//   }
//
//   Future<void> _downloadBackupCodes() async {
//     if (_backupCodes.isEmpty) return;
//
//     final text = 'HM Collection - Backup Codes\n\n' +
//         _backupCodes.join('\n');
//
//     try {
//       if (kIsWeb) {
//         // For web, use the universal_html package which is web-compatible
//         final bytes = utf8.encode(text);
//         final blob = Blob([Uint8List.fromList(bytes)]);
//         final url = Url.createObjectUrlFromBlob(blob);
//         final anchor = AnchorElement(href: url)
//           ..setAttribute('download', 'hm-collection-backup-codes.txt')
//           ..click();
//         Url.revokeObjectUrl(url);
//         _showSuccessSnackBar('Backup codes download started');
//       } else {
//         // For mobile, use the clipboard
//         await Clipboard.setData(ClipboardData(text: text));
//         _showSuccessSnackBar('Backup codes copied to clipboard');
//       }
//
//       if (mounted) {
//         setState(() => _codesDownloaded = true);
//       }
//     } catch (e) {
//       debugPrint('Error downloading backup codes: $e');
//       _showErrorSnackBar('Failed to download backup codes');
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Set Up Two-Factor Authentication'),
//         elevation: 0,
//       ),
//       body: _isLoading
//           ? const Center(child: CircularProgressIndicator())
//           : SingleChildScrollView(
//               padding: const EdgeInsets.all(24.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.stretch,
//                 children: [
//                   if (_isVerified) ..._buildVerifiedUI()
//                   else if (_showVerificationForm) ..._buildVerificationUI()
//                   else ..._buildInitialUI(),
//                 ],
//               ),
//             ),
//     );
//   }
//
//   List<Widget> _buildInitialUI() {
//     return [
//       const Icon(
//         Icons.security,
//         size: 80,
//         color: Colors.blue,
//       ),
//       const SizedBox(height: 24),
//       const Text(
//         'Set Up Two-Factor Authentication',
//         style: TextStyle(
//           fontSize: 24,
//           fontWeight: FontWeight.bold,
//         ),
//         textAlign: TextAlign.center,
//       ),
//       const SizedBox(height: 16),
//       const Text(
//         'Two-factor authentication adds an extra layer of security to your account.\n\n'
//         'You\'ll need to enter a verification code from your authenticator app each time you sign in.',
//         style: TextStyle(fontSize: 16, height: 1.5),
//         textAlign: TextAlign.center,
//       ),
//       const SizedBox(height: 32),
//       SizedBox(
//         width: double.infinity,
//         child: ElevatedButton.icon(
//           icon: const Icon(Icons.security),
//           label: const Text('Begin Setup', style: TextStyle(fontSize: 16)),
//           style: ElevatedButton.styleFrom(
//             padding: const EdgeInsets.symmetric(vertical: 16),
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(8),
//             ),
//           ),
//           onPressed: _generateSecret,
//         ),
//       ),
//     ];
//   }
//
//   List<Widget> _buildVerificationUI() {
//     return [
//       const Text(
//         'Step 1: Scan the QR Code',
//         style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//       ),
//       const SizedBox(height: 16),
//       const Text(
//         'Open your authenticator app (like Google Authenticator or Authy) and scan this QR code:',
//         style: TextStyle(fontSize: 16, height: 1.5),
//       ),
//       const SizedBox(height: 16),
//       if (_qrCodeData != null) ...[
//         Center(
//           child: Card(
//             elevation: 4,
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 children: [
//                   QrImageView(
//                     data: _qrCodeData!,
//                     version: QrVersions.auto,
//                     size: 200.0,
//                   ),
//                   const SizedBox(height: 16),
//                   Text(
//                     'Or enter this code manually:',
//                     style: Theme.of(context).textTheme.bodyMedium,
//                   ),
//                   const SizedBox(height: 8),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       SelectableText(
//                         _secret ?? '',
//                         style: const TextStyle(
//                           fontFamily: 'monospace',
//                           fontSize: 18,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       IconButton(
//                         icon: const Icon(Icons.copy),
//                         onPressed: () {
//                           Clipboard.setData(ClipboardData(text: _secret ?? ''));
//                           _showSuccessSnackBar('Copied to clipboard');
//                         },
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ],
//       const SizedBox(height: 32),
//       const Text(
//         'Step 2: Enter Verification Code',
//         style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//       ),
//       const SizedBox(height: 16),
//       const Text(
//         'Enter the 6-digit verification code from your authenticator app:',
//         style: TextStyle(fontSize: 16, height: 1.5),
//       ),
//       const SizedBox(height: 16),
//       Form(
//         key: _formKey,
//         child: TextFormField(
//           controller: _codeController,
//           keyboardType: TextInputType.number,
//           decoration: const InputDecoration(
//             labelText: 'Verification Code',
//             border: OutlineInputBorder(),
//             prefixIcon: Icon(Icons.security),
//             hintText: '123456',
//           ),
//           validator: (value) {
//             if (value == null || value.isEmpty) {
//               return 'Please enter the verification code';
//             }
//             if (value.length != 6 || int.tryParse(value) == null) {
//               return 'Please enter a valid 6-digit code';
//             }
//             return null;
//           },
//         ),
//       ),
//       const SizedBox(height: 24),
//       ElevatedButton(
//         onPressed: _verifyCode,
//         child: const Text('Verify and Enable 2FA'),
//       ),
//     ];
//   }
//
//   List<Widget> _buildVerifiedUI() {
//     if (!_showBackupCodes) {
//       return [
//         const CircularProgressIndicator(),
//         const SizedBox(height: 24),
//         const Text(
//           'Finalizing setup...',
//           style: TextStyle(fontSize: 16),
//         ),
//       ];
//     }
//
//     return [
//       const Icon(
//         Icons.verified_user,
//         size: 80,
//         color: Colors.green,
//       ),
//       const SizedBox(height: 24),
//       const Text(
//         'Two-Factor Authentication Enabled',
//         style: TextStyle(
//           fontSize: 24,
//           fontWeight: FontWeight.bold,
//         ),
//         textAlign: TextAlign.center,
//       ),
//       const SizedBox(height: 16),
//       const Text(
//         'Your account is now protected with two-factor authentication.\n\n'
//         'Save these backup codes in a safe place. You can use them to access your account if you lose your device.',
//         style: TextStyle(fontSize: 16, height: 1.5),
//         textAlign: TextAlign.center,
//       ),
//       const SizedBox(height: 24),
//       Card(
//         elevation: 2,
//         margin: const EdgeInsets.symmetric(horizontal: 16),
//         child: Padding(
//           padding: const EdgeInsets.all(16),
//           child: Column(
//             children: [
//               const Text(
//                 'Backup Codes',
//                 style: TextStyle(
//                   fontSize: 18,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               const SizedBox(height: 16),
//               ..._backupCodes.map((code) => Padding(
//                 padding: const EdgeInsets.symmetric(vertical: 4),
//                 child: Text(
//                   code,
//                   style: const TextStyle(
//                     fontFamily: 'monospace',
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//               )).toList(),
//               const SizedBox(height: 16),
//               const Text(
//                 'Each code can only be used once.',
//                 style: TextStyle(fontSize: 12, color: Colors.grey),
//                 textAlign: TextAlign.center,
//               ),
//             ],
//           ),
//         ),
//       ),
//       const SizedBox(height: 24),
//       Row(
//         children: [
//           Expanded(
//             child: OutlinedButton.icon(
//               icon: Icon(_codesCopied ? Icons.check : Icons.copy),
//               label: Text(_codesCopied ? 'Copied!' : 'Copy'),
//               onPressed: _codesCopied ? null : _copyBackupCodes,
//               style: OutlinedButton.styleFrom(
//                 padding: const EdgeInsets.symmetric(vertical: 12),
//               ),
//             ),
//           ),
//           const SizedBox(width: 16),
//           Expanded(
//             child: OutlinedButton.icon(
//               icon: Icon(_codesDownloaded ? Icons.check : Icons.download),
//               label: Text(_codesDownloaded ? 'Downloaded!' : 'Download'),
//               onPressed: _codesDownloaded ? null : _downloadBackupCodes,
//               style: OutlinedButton.styleFrom(
//                 padding: const EdgeInsets.symmetric(vertical: 12),
//               ),
//             ),
//           ),
//         ],
//       ),
//       const SizedBox(height: 16),
//       SizedBox(
//         width: double.infinity,
//         child: ElevatedButton(
//           onPressed: () => Navigator.of(context).pop(true),
//           style: ElevatedButton.styleFrom(
//             padding: const EdgeInsets.symmetric(vertical: 16),
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(8),
//             ),
//           ),
//           child: const Text('I have saved my backup codes', style: TextStyle(fontSize: 16)),
//         ),
//       ),
//     ];
//   }
// }
