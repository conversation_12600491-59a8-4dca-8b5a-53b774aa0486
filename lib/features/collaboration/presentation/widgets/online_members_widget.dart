import 'package:flutter/material.dart';

/// Widget that displays online members in a workspace
class OnlineMembersWidget extends StatelessWidget {
  final List<OnlineMember> onlineMembers;
  final VoidCallback? onViewAll;
  final Function(OnlineMember)? onMemberTap;

  const OnlineMembersWidget({
    Key? key,
    required this.onlineMembers,
    this.onViewAll,
    this.onMemberTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Online Members',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${onlineMembers.length}',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                if (onlineMembers.length > 5)
                  TextButton(
                    onPressed: onViewAll,
                    child: const Text('View All'),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (onlineMembers.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'No members online',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              )
            else
              SizedBox(
                height: 60,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: onlineMembers.take(10).length,
                  itemBuilder: (context, index) {
                    final member = onlineMembers[index];
                    return _buildMemberAvatar(context, member);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberAvatar(BuildContext context, OnlineMember member) {
    return GestureDetector(
      onTap: () => onMemberTap?.call(member),
      child: Container(
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            Stack(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: member.avatarUrl != null
                      ? NetworkImage(member.avatarUrl!)
                      : null,
                  child: member.avatarUrl == null
                      ? Text(
                          member.name.substring(0, 1).toUpperCase(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStatusColor(member.status),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              member.name.split(' ').first,
              style: Theme.of(context).textTheme.bodySmall,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(OnlineStatus status) {
    switch (status) {
      case OnlineStatus.online:
        return Colors.green;
      case OnlineStatus.away:
        return Colors.orange;
      case OnlineStatus.busy:
        return Colors.red;
      case OnlineStatus.offline:
        return Colors.grey;
    }
  }
}

/// Expanded view of online members
class OnlineMembersListView extends StatelessWidget {
  final List<OnlineMember> onlineMembers;
  final Function(OnlineMember)? onMemberTap;
  final Function(OnlineMember)? onMessageMember;

  const OnlineMembersListView({
    Key? key,
    required this.onlineMembers,
    this.onMemberTap,
    this.onMessageMember,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: onlineMembers.length,
      itemBuilder: (context, index) {
        final member = onlineMembers[index];
        return ListTile(
          leading: Stack(
            children: [
              CircleAvatar(
                backgroundImage: member.avatarUrl != null
                    ? NetworkImage(member.avatarUrl!)
                    : null,
                child: member.avatarUrl == null
                    ? Text(
                        member.name.substring(0, 1).toUpperCase(),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getStatusColor(member.status),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
            ],
          ),
          title: Text(member.name),
          subtitle: Text(member.role),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(member.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  member.status.displayName,
                  style: TextStyle(
                    color: _getStatusColor(member.status),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.message),
                onPressed: () => onMessageMember?.call(member),
                tooltip: 'Send Message',
              ),
            ],
          ),
          onTap: () => onMemberTap?.call(member),
        );
      },
    );
  }

  Color _getStatusColor(OnlineStatus status) {
    switch (status) {
      case OnlineStatus.online:
        return Colors.green;
      case OnlineStatus.away:
        return Colors.orange;
      case OnlineStatus.busy:
        return Colors.red;
      case OnlineStatus.offline:
        return Colors.grey;
    }
  }
}

// Placeholder classes - these should be defined in collaboration_entities.dart
class OnlineMember {
  final String id;
  final String name;
  final String role;
  final String? avatarUrl;
  final OnlineStatus status;
  final DateTime lastSeen;

  const OnlineMember({
    required this.id,
    required this.name,
    required this.role,
    this.avatarUrl,
    required this.status,
    required this.lastSeen,
  });
}

enum OnlineStatus { online, away, busy, offline }

extension OnlineStatusExtension on OnlineStatus {
  String get displayName {
    switch (this) {
      case OnlineStatus.online:
        return 'Online';
      case OnlineStatus.away:
        return 'Away';
      case OnlineStatus.busy:
        return 'Busy';
      case OnlineStatus.offline:
        return 'Offline';
    }
  }
}