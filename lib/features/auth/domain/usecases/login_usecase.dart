import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';

import '../entities/auth_tokens.dart';
import '../entities/login_credentials.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Login use case
@injectable
class LoginUseCase implements UseCase<LoginResult, LoginParams> {
  final AuthRepository _authRepository;
  final LocalAuthRepository _localAuthRepository;

  const LoginUseCase(
    this._authRepository,
    this._localAuthRepository,
  );

  @override
  Future<Either<Failure, LoginResult>> call(LoginParams params) async {
    try {
      // Validate credentials
      if (!params.credentials.isValid) {
        return const Left(ValidationFailure('Invalid credentials provided'));
      }

      // Attempt login
      final loginResult = await _authRepository.login(params.credentials);

      return await loginResult.fold(
        (failure) => Left(failure),
        (tokenResponse) async {
          if (!tokenResponse.success || !tokenResponse.hasData) {
            return Left(AuthFailure(
              tokenResponse.firstError ?? 'Login failed',
              details: tokenResponse.errors,
            ));
          }

          final tokens = tokenResponse.data!;

          // Get user profile
          final userResult = await _authRepository.getCurrentUser();

          return await userResult.fold(
            (failure) => Left(failure),
            (userResponse) async {
              if (!userResponse.success || !userResponse.hasData) {
                return Left(AuthFailure(
                  userResponse.firstError ?? 'Failed to get user profile',
                  details: userResponse.errors,
                ));
              }

              final user = userResponse.data!;

              // Check if user can login
              if (!user.canLogin) {
                return const Left(AuthFailure(
                  'Account is not active or has been suspended',
                ));
              }

              // Save tokens locally if remember me is enabled
              if (params.credentials.rememberMe ?? false) {
                await _localAuthRepository.saveTokens(tokens);
                await _localAuthRepository.saveUser(user);
                
                if (params.saveCredentials) {
                  await _localAuthRepository.saveLoginCredentials(params.credentials);
                }
              }

              // Save last login time
              await _localAuthRepository.saveLastLoginTime(DateTime.now());

              return Right(LoginResult(
                user: user,
                tokens: tokens,
                isFirstLogin: user.lastLoginAt == null,
                requiresPasswordChange: _shouldRequirePasswordChange(user),
                requiresTwoFactorSetup: _shouldRequireTwoFactorSetup(user),
              ));
            },
          );
        },
      );
    } catch (e) {
      return Left(UnknownFailure('Login failed: $e'));
    }
  }

  /// Check if password change is required
  bool _shouldRequirePasswordChange(User user) {
    // Require password change if:
    // 1. User has never logged in before
    // 2. Password is older than 90 days (if implemented)
    // 3. Account was created by admin and user hasn't changed password
    return user.lastLoginAt == null;
  }

  /// Check if two-factor authentication setup is required
  bool _shouldRequireTwoFactorSetup(User user) {
    // Require 2FA setup for admin and department heads
    return user.isAdmin || user.isDepartmentHead;
  }
}

/// Login parameters
class LoginParams {
  final LoginCredentials credentials;
  final bool saveCredentials;
  final bool enableBiometric;

  const LoginParams({
    required this.credentials,
    this.saveCredentials = false,
    this.enableBiometric = false,
  });
}

/// Login result
class LoginResult {
  final User user;
  final AuthTokens tokens;
  final bool isFirstLogin;
  final bool requiresPasswordChange;
  final bool requiresTwoFactorSetup;

  const LoginResult({
    required this.user,
    required this.tokens,
    required this.isFirstLogin,
    required this.requiresPasswordChange,
    required this.requiresTwoFactorSetup,
  });

  /// Check if any additional setup is required
  bool get requiresAdditionalSetup {
    return requiresPasswordChange || requiresTwoFactorSetup;
  }
}

/// Biometric login use case
@injectable
class BiometricLoginUseCase implements UseCase<LoginResult, BiometricLoginParams> {
  final AuthRepository _authRepository;
  final LocalAuthRepository _localAuthRepository;

  const BiometricLoginUseCase(
    this._authRepository,
    this._localAuthRepository,
  );

  @override
  Future<Either<Failure, LoginResult>> call(BiometricLoginParams params) async {
    try {
      // Check if biometric is available and enabled
      final isAvailable = await _localAuthRepository.isBiometricAvailable();
      if (isAvailable.isLeft()) {
        return Left(isAvailable.fold((l) => l, (r) => const UnknownFailure('Biometric check failed')));
      }

      if (!isAvailable.getOrElse(() => false)) {
        return const Left(PermissionFailure('Biometric authentication is not available'));
      }

      final isEnabled = await _localAuthRepository.isBiometricEnabled();
      if (isEnabled.isLeft()) {
        return Left(isEnabled.fold((l) => l, (r) => const UnknownFailure('Biometric check failed')));
      }

      if (!isEnabled.getOrElse(() => false)) {
        return const Left(PermissionFailure('Biometric authentication is not enabled'));
      }

      // Authenticate with biometric
      final biometricResult = await _localAuthRepository.authenticateWithBiometric();
      if (biometricResult.isLeft()) {
        return Left(biometricResult.fold((l) => l, (r) => const AuthFailure('Biometric authentication failed')));
      }

      if (!biometricResult.getOrElse(() => false)) {
        return const Left(AuthFailure('Biometric authentication failed'));
      }

      // Get saved user and tokens
      final userResult = await _localAuthRepository.getUser();
      final tokensResult = await _localAuthRepository.getTokens();

      if (userResult.isLeft() || tokensResult.isLeft()) {
        return const Left(CacheFailure('No saved authentication data found'));
      }

      final user = userResult.getOrElse(() => null);
      final tokens = tokensResult.getOrElse(() => null);

      if (user == null || tokens == null) {
        return const Left(CacheFailure('No saved authentication data found'));
      }

      // Check if tokens are still valid
      if (tokens.isExpired) {
        // Try to refresh tokens
        final refreshResult = await _authRepository.refreshToken(tokens.refreshToken);
        
        return await refreshResult.fold(
          (failure) => Left(failure),
          (tokenResponse) async {
            if (!tokenResponse.success || !tokenResponse.hasData) {
              return const Left(AuthFailure('Failed to refresh authentication'));
            }

            final newTokens = tokenResponse.data!;
            await _localAuthRepository.saveTokens(newTokens);

            return Right(LoginResult(
              user: user,
              tokens: newTokens,
              isFirstLogin: false,
              requiresPasswordChange: false,
              requiresTwoFactorSetup: false,
            ));
          },
        );
      }

      return Right(LoginResult(
        user: user,
        tokens: tokens,
        isFirstLogin: false,
        requiresPasswordChange: false,
        requiresTwoFactorSetup: false,
      ));
    } catch (e) {
      return Left(UnknownFailure('Biometric login failed: $e'));
    }
  }
}

/// Biometric login parameters
class BiometricLoginParams {
  final String reason;

  const BiometricLoginParams({
    this.reason = 'Please authenticate to login',
  });
}

/// Auto login use case
@injectable
class AutoLoginUseCase implements UseCase<LoginResult?, NoParams> {
  final AuthRepository _authRepository;
  final LocalAuthRepository _localAuthRepository;

  const AutoLoginUseCase(
    this._authRepository,
    this._localAuthRepository,
  );

  @override
  Future<Either<Failure, LoginResult?>> call(NoParams params) async {
    try {
      // Check if auto-login is enabled
      final isAutoLoginEnabled = await _localAuthRepository.isAutoLoginEnabled();
      if (isAutoLoginEnabled.isLeft()) {
        return const Right(null);
      }

      if (!isAutoLoginEnabled.getOrElse(() => false)) {
        return const Right(null);
      }

      // Get saved tokens
      final tokensResult = await _localAuthRepository.getTokens();
      if (tokensResult.isLeft()) {
        return const Right(null);
      }

      final tokens = tokensResult.getOrElse(() => null);
      if (tokens == null) {
        return const Right(null);
      }

      // Check if tokens are valid or can be refreshed
      if (tokens.isExpired) {
        final refreshResult = await _authRepository.refreshToken(tokens.refreshToken);
        
        return await refreshResult.fold(
          (failure) => const Right(null), // Silent failure for auto-login
          (tokenResponse) async {
            if (!tokenResponse.success || !tokenResponse.hasData) {
              return const Right(null);
            }

            final newTokens = tokenResponse.data!;
            await _localAuthRepository.saveTokens(newTokens);

            // Get user profile
            final userResult = await _authRepository.getCurrentUser();
            return await userResult.fold(
              (failure) => const Right(null),
              (userResponse) async {
                if (!userResponse.success || !userResponse.hasData) {
                  return const Right(null);
                }

                final user = userResponse.data!;
                await _localAuthRepository.saveUser(user);

                return Right(LoginResult(
                  user: user,
                  tokens: newTokens,
                  isFirstLogin: false,
                  requiresPasswordChange: false,
                  requiresTwoFactorSetup: false,
                ));
              },
            );
          },
        );
      }

      // Get saved user
      final userResult = await _localAuthRepository.getUser();
      if (userResult.isLeft()) {
        return const Right(null);
      }

      final user = userResult.getOrElse(() => null);
      if (user == null) {
        return const Right(null);
      }

      return Right(LoginResult(
        user: user,
        tokens: tokens,
        isFirstLogin: false,
        requiresPasswordChange: false,
        requiresTwoFactorSetup: false,
      ));
    } catch (e) {
      return const Right(null); // Silent failure for auto-login
    }
  }
}
