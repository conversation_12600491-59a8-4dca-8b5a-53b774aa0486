import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

// Import authentication functions
import {
  onUserCreate,
  onUserDelete,
  updateUserRole,
  updateUserStatus,
  updateLastActive,
} from './auth/userManagement';

import {
  updateUserPresence,
  trackUserLogin,
  trackUserLogout,
  getUserSessions,
  terminateUserSession,
  monitorInactiveUsers,
  cleanupOldData,
} from './auth/realtimeStatus';

// Export authentication functions
export {
  onUserCreate,
  onUserDelete,
  updateUserRole,
  updateUserStatus,
  updateLastActive,
  updateUserPresence,
  trackUserLogin,
  trackUserLogout,
  getUserSessions,
  terminateUserSession,
  monitorInactiveUsers,
  cleanupOldData,
};

/**
 * Get user profile by ID
 */
export const getUserProfile = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { userId } = data;
  const requestingUserId = context.auth.uid;

  try {
    // Check if user can access this profile
    const canAccess = await canUserAccessProfile(requestingUserId, userId);
    if (!canAccess) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to access this profile');
    }

    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User not found');
    }

    const userData = userDoc.data()!;
    
    // Remove sensitive information
    const { metadata, ...publicData } = userData;
    
    return { success: true, user: publicData };
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get user profile');
  }
});

/**
 * Update user profile
 */
export const updateUserProfile = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { userId, profileData } = data;
  const requestingUserId = context.auth.uid;

  try {
    // Check if user can update this profile
    const canUpdate = await canUserUpdateProfile(requestingUserId, userId);
    if (!canUpdate) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to update this profile');
    }

    // Validate profile data
    const validatedData = validateProfileData(profileData);
    
    await admin.firestore().collection('users').doc(userId).update({
      ...validatedData,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      'metadata.updatedBy': requestingUserId,
      'metadata.version': admin.firestore.FieldValue.increment(1),
    });

    // Log profile update
    await logUserActivity(userId, 'PROFILE_UPDATED', {
      updatedBy: requestingUserId,
      changes: Object.keys(validatedData),
    });

    return { success: true, message: 'Profile updated successfully' };
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update user profile');
  }
});

/**
 * Send notification to user
 */
export const sendUserNotification = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { userId, notification } = data;
  const senderId = context.auth.uid;

  try {
    // Check if sender has permission to send notifications
    const hasPermission = await userHasPermission(senderId, 'notifications.send');
    if (!hasPermission) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to send notifications');
    }

    const notificationData = {
      userId,
      title: notification.title,
      message: notification.message,
      type: notification.type || 'info',
      priority: notification.priority || 'medium',
      read: false,
      actionUrl: notification.actionUrl || null,
      actionText: notification.actionText || null,
      senderId,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: notification.expiresAt ? new Date(notification.expiresAt) : null,
    };

    await admin.firestore().collection('userNotifications').add(notificationData);

    return { success: true, message: 'Notification sent successfully' };
  } catch (error) {
    console.error('Error sending notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});

// Helper functions
async function canUserAccessProfile(requestingUserId: string, targetUserId: string): Promise<boolean> {
  if (requestingUserId === targetUserId) return true;
  return await userHasPermission(requestingUserId, 'users.read');
}

async function canUserUpdateProfile(requestingUserId: string, targetUserId: string): Promise<boolean> {
  if (requestingUserId === targetUserId) return true;
  return await userHasPermission(requestingUserId, 'users.update');
}

async function userHasPermission(userId: string, permission: string): Promise<boolean> {
  try {
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    const userData = userDoc.data();
    
    if (!userData) return false;
    if (userData.role === 'admin') return true;
    
    return userData.permissions?.includes(permission) || false;
  } catch (error) {
    console.error('Error checking user permission:', error);
    return false;
  }
}

function validateProfileData(profileData: any): any {
  const allowedFields = [
    'displayName', 'phoneNumber', 'photoURL', 'profile',
    'departmentIds', 'currentDepartmentId'
  ];

  const validatedData: any = {};
  
  for (const field of allowedFields) {
    if (profileData[field] !== undefined) {
      validatedData[field] = profileData[field];
    }
  }

  return validatedData;
}

async function logUserActivity(userId: string, action: string, details: any) {
  try {
    await admin.firestore().collection('userActivityLogs').add({
      userId,
      action,
      details,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error('Error logging user activity:', error);
  }
}
