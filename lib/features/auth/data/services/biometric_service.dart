import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/material.dart';

class BiometricService {
  final LocalAuthentication _localAuth = LocalAuthentication();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  static const String _biometricKey = 'use_biometric';
  static const String _storedEmailKey = 'stored_email';
  static const String _storedPasswordKey = 'stored_password';

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAvailable() async {
    if (kIsWeb) {
      // Web doesn't support biometric authentication
      return false;
    }
    
    try {
      return await _localAuth.canCheckBiometrics;
    } on PlatformException catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Check if biometric authentication is enabled for the app
  Future<bool> isBiometricEnabled() async {
    try {
      final useBiometric = await _secureStorage.read(key: _biometricKey);
      return useBiometric == 'true';
    } catch (e) {
      print('Error reading biometric preference: $e');
      return false;
    }
  }

  /// Enable or disable biometric authentication
  Future<void> setBiometricPreference(bool enable) async {
    await _secureStorage.write(
      key: _biometricKey,
      value: enable.toString(),
    );
  }

  /// Store user credentials securely
  Future<void> storeCredentials(String email, String password) async {
    try {
      await _secureStorage.write(key: _storedEmailKey, value: email);
      await _secureStorage.write(key: _storedPasswordKey, value: password);
    } catch (e) {
      print('Error storing credentials: $e');
      rethrow;
    }
  }

  /// Get stored credentials
  Future<Map<String, String?>> getStoredCredentials() async {
    try {
      final email = await _secureStorage.read(key: _storedEmailKey);
      final password = await _secureStorage.read(key: _storedPasswordKey);
      return {'email': email, 'password': password};
    } catch (e) {
      print('Error retrieving stored credentials: $e');
      return {'email': null, 'password': null};
    }
  }

  /// Clear stored credentials
  Future<void> clearStoredCredentials() async {
    try {
      await _secureStorage.delete(key: _storedEmailKey);
      await _secureStorage.delete(key: _storedPasswordKey);
      await _secureStorage.delete(key: _biometricKey);
    } catch (e) {
      print('Error clearing stored credentials: $e');
      rethrow;
    }
  }

  /// Authenticate with biometrics
  Future<bool> authenticateWithBiometrics() async {
    if (kIsWeb) {
      // Web doesn't support biometric authentication
      return false;
    }
    
    try {
      final isAvailable = await isBiometricAvailable();
      if (!isAvailable) return false;

      return await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access your account',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
      );
    } on PlatformException catch (e) {
      debugPrint('Error during biometric authentication: $e');
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    if (kIsWeb) {
      // Web doesn't support biometrics
      return [];
    }
    
    try {
      return await _localAuth.getAvailableBiometrics();
    } on PlatformException catch (e) {
      debugPrint('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Get the most secure biometric type available
  Future<BiometricType?> getMostSecureBiometricType() async {
    final availableBiometrics = await getAvailableBiometrics();
    
    if (availableBiometrics.contains(BiometricType.face)) {
      return BiometricType.face;
    } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
      return BiometricType.fingerprint;
    } else if (availableBiometrics.contains(BiometricType.iris)) {
      return BiometricType.iris;
    } else if (availableBiometrics.isNotEmpty) {
      return availableBiometrics.first;
    }
    
    return null;
  }

  /// Get the biometric icon based on available biometrics
  Future<IconData> getBiometricIcon() async {
    final biometricType = await getMostSecureBiometricType();
    
    switch (biometricType) {
      case BiometricType.face:
        return Icons.face_retouching_natural;
      case BiometricType.fingerprint:
        return Icons.fingerprint;
      case BiometricType.iris:
        return Icons.remove_red_eye;
      case BiometricType.strong:
        return Icons.verified_user;
      case BiometricType.weak:
        return Icons.security_update_warning;
      default:
        return Icons.security;
    }
  }
}
