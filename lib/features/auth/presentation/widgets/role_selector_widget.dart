import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';

/// Role selector widget for manufacturing roles
class RoleSelectorWidget extends StatefulWidget {
  final UserRole? selectedRole;
  final Function(UserRole) onRoleSelected;

  const RoleSelectorWidget({
    super.key,
    this.selectedRole,
    required this.onRoleSelected,
  });

  @override
  State<RoleSelectorWidget> createState() => _RoleSelectorWidgetState();
}

class _RoleSelectorWidgetState extends State<RoleSelectorWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Role Selection Field
        InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.textSecondary.withAlpha(77)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.work,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Role *',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        widget.selectedRole?.displayName ?? 'Select your role',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: widget.selectedRole != null
                              ? AppColors.textPrimary
                              : AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  _isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
        
        // Role List
        if (_isExpanded) ...[
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.textSecondary.withAlpha(77)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: UserRole.values
                  .where((role) => _isManufacturingRole(role))
                  .map((role) => _buildRoleCard(role))
                  .toList(),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildRoleCard(UserRole role) {
    final isSelected = widget.selectedRole == role;
    
    return Container(
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () {
          widget.onRoleSelected(role);
          setState(() {
            _isExpanded = false;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected 
                ? AppColors.primary.withAlpha(26)
                : AppColors.surface,
            border: Border.all(
              color: isSelected 
                  ? AppColors.primary
                  : AppColors.textSecondary.withAlpha(51),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              // Role Icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getRoleColor(role).withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getRoleIcon(role),
                  color: _getRoleColor(role),
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Role Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      role.displayName,
                      style: AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.primary : AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      role.description,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    // Permissions Preview
                    Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: role.permissions.take(3).map((permission) => Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getRoleColor(role).withAlpha(26),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          permission,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: _getRoleColor(role),
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )).toList(),
                    ),
                  ],
                ),
              ),
              
              // Selection Indicator
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: AppColors.primary,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  bool _isManufacturingRole(UserRole role) {
    return [
      UserRole.cuttingHead,
      UserRole.cuttingMaster,
      UserRole.cuttingHelper,
      UserRole.sewingHead,
      UserRole.sewingSupervisor,
      UserRole.sewingOperator,
      UserRole.qualityController,
      UserRole.finishingHead,
      UserRole.finishingOperator,
    ].contains(role);
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return Colors.red;
      case UserRole.cuttingHead:
      case UserRole.sewingHead:
      case UserRole.finishingHead:
        return Colors.purple;
      case UserRole.sewingSupervisor:
      case UserRole.cuttingMaster:
        return Colors.orange;
      case UserRole.qualityController:
        return Colors.green;
      case UserRole.sewingOperator:
      case UserRole.finishingOperator:
      case UserRole.cuttingHelper:
        return Colors.blue;
      default:
        return Colors.teal;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return Icons.admin_panel_settings;
      case UserRole.cuttingHead:
      case UserRole.sewingHead:
      case UserRole.finishingHead:
        return Icons.manage_accounts;
      case UserRole.sewingSupervisor:
        return Icons.supervisor_account;
      case UserRole.cuttingMaster:
        return Icons.engineering;
      case UserRole.qualityController:
        return Icons.verified;
      case UserRole.sewingOperator:
      case UserRole.finishingOperator:
        return Icons.precision_manufacturing;
      case UserRole.cuttingHelper:
        return Icons.person;
      default:
        return Icons.work;
    }
  }
}
