import 'package:equatable/equatable.dart';

import '../../../../shared/models/base_entity.dart';

/// Product entity
class Product extends BaseEntity {
  final String name;
  final String? code;
  final String? description;
  final String? category;
  final String? imageUrl;
  final Map<String, dynamic>? specifications;

  const Product({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.name,
    this.code,
    this.description,
    this.category,
    this.imageUrl,
    this.specifications,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        code,
        description,
        category,
        imageUrl,
        specifications,
      ];

  /// Create a copy of the product with updated fields
  Product copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? name,
    String? code,
    String? description,
    String? category,
    String? imageUrl,
    Map<String, dynamic>? specifications,
  }) {
    return Product(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      specifications: specifications ?? this.specifications,
    );
  }
}
