import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../entities/employee.dart';

/// Employee repository interface
abstract class EmployeeRepository {
  /// Get all employees with optional filtering
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployees({
    EmployeeFilterCriteria? filter,
    int limit = 50,
  });

  /// Get employee by ID
  Future<Either<Failure, ApiResponse<Employee>>> getEmployeeById(String id);

  /// Get employee by employee ID
  Future<Either<Failure, ApiResponse<Employee>>> getEmployeeByEmployeeId(String employeeId);

  /// Create new employee
  Future<Either<Failure, ApiResponse<Employee>>> createEmployee(CreateEmployeeRequest request);

  /// Update existing employee
  Future<Either<Failure, ApiResponse<Employee>>> updateEmployee(UpdateEmployeeRequest request);

  /// Delete employee (soft delete)
  Future<Either<Failure, ApiResponse<void>>> deleteEmployee(
    String id, {
    String? reason,
  });

  /// Search employees by name, email, or employee ID
  Future<Either<Failure, ApiResponse<List<Employee>>>> searchEmployees(
    String query, {
    EmployeeFilterCriteria? filter,
    int limit = 20,
  });

  /// Get employees by department
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesByDepartment(
    String departmentId, {
    int limit = 50,
  });

  /// Get employees by role
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesByRole(
    String roleId, {
    int limit = 50,
  });

  /// Get employees by supervisor
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesBySupervisor(
    String supervisorId, {
    int limit = 50,
  });

  /// Get real-time stream of employees
  Stream<List<Employee>> getEmployeesStream({
    EmployeeFilterCriteria? filter,
    int limit = 50,
  });

  /// Get real-time stream of employee by ID
  Stream<Employee?> getEmployeeStream(String id);

  /// Activate employee
  Future<Either<Failure, ApiResponse<Employee>>> activateEmployee(String id);

  /// Deactivate employee
  Future<Either<Failure, ApiResponse<Employee>>> deactivateEmployee(
    String id, {
    String? reason,
  });

  /// Transfer employee to different department
  Future<Either<Failure, ApiResponse<Employee>>> transferEmployee(
    String id,
    String newDepartmentId, {
    String? newSupervisorId,
    String? reason,
  });

  /// Promote employee to new role
  Future<Either<Failure, ApiResponse<Employee>>> promoteEmployee(
    String id,
    String newRoleId, {
    String? reason,
  });

  /// Get employee statistics
  Future<Either<Failure, ApiResponse<EmployeeStatistics>>> getEmployeeStatistics();
}

/// Create employee request
class CreateEmployeeRequest {
  final String firstName;
  final String lastName;
  final String email;
  final String? phoneNumber;
  final String role;
  final String department;
  final DateTime hireDate;
  final String? supervisorId;
  final List<String> skills;
  final double? hourlyRate;
  final String? emergencyContact;
  final String? address;
  final Map<String, dynamic> metadata;

  const CreateEmployeeRequest({
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phoneNumber,
    required this.role,
    required this.department,
    required this.hireDate,
    this.supervisorId,
    this.skills = const [],
    this.hourlyRate,
    this.emergencyContact,
    this.address,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phoneNumber': phoneNumber,
      'role': role,
      'department': department,
      'hireDate': hireDate.toIso8601String(),
      'supervisorId': supervisorId,
      'skills': skills,
      'hourlyRate': hourlyRate,
      'emergencyContact': emergencyContact,
      'address': address,
      'metadata': metadata,
    };
  }
}

/// Update employee request
class UpdateEmployeeRequest {
  final String id;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phoneNumber;
  final String? role;
  final String? department;
  final String? status;
  final DateTime? hireDate;
  final DateTime? terminationDate;
  final String? profileImageUrl;
  final bool? isActive;
  final String? supervisorId;
  final List<String>? skills;
  final double? hourlyRate;
  final String? emergencyContact;
  final String? address;
  final Map<String, dynamic>? metadata;

  const UpdateEmployeeRequest({
    required this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phoneNumber,
    this.role,
    this.department,
    this.status,
    this.hireDate,
    this.terminationDate,
    this.profileImageUrl,
    this.isActive,
    this.supervisorId,
    this.skills,
    this.hourlyRate,
    this.emergencyContact,
    this.address,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{'id': id};
    
    if (firstName != null) json['firstName'] = firstName;
    if (lastName != null) json['lastName'] = lastName;
    if (email != null) json['email'] = email;
    if (phoneNumber != null) json['phoneNumber'] = phoneNumber;
    if (role != null) json['role'] = role;
    if (department != null) json['department'] = department;
    if (status != null) json['status'] = status;
    if (hireDate != null) json['hireDate'] = hireDate!.toIso8601String();
    if (terminationDate != null) json['terminationDate'] = terminationDate!.toIso8601String();
    if (profileImageUrl != null) json['profileImageUrl'] = profileImageUrl;
    if (isActive != null) json['isActive'] = isActive;
    if (supervisorId != null) json['supervisorId'] = supervisorId;
    if (skills != null) json['skills'] = skills;
    if (hourlyRate != null) json['hourlyRate'] = hourlyRate;
    if (emergencyContact != null) json['emergencyContact'] = emergencyContact;
    if (address != null) json['address'] = address;
    if (metadata != null) json['metadata'] = metadata;
    
    return json;
  }
}

/// Employee statistics
class EmployeeStatistics {
  final int totalEmployees;
  final int activeEmployees;
  final int inactiveEmployees;
  final Map<String, int> employeesByDepartment;
  final Map<String, int> employeesByRole;
  final Map<String, int> employeesByStatus;
  final double averageYearsOfService;
  final int newHiresThisMonth;
  final int terminationsThisMonth;

  const EmployeeStatistics({
    required this.totalEmployees,
    required this.activeEmployees,
    required this.inactiveEmployees,
    required this.employeesByDepartment,
    required this.employeesByRole,
    required this.employeesByStatus,
    required this.averageYearsOfService,
    required this.newHiresThisMonth,
    required this.terminationsThisMonth,
  });

  factory EmployeeStatistics.fromJson(Map<String, dynamic> json) {
    return EmployeeStatistics(
      totalEmployees: json['totalEmployees'] ?? 0,
      activeEmployees: json['activeEmployees'] ?? 0,
      inactiveEmployees: json['inactiveEmployees'] ?? 0,
      employeesByDepartment: Map<String, int>.from(json['employeesByDepartment'] ?? {}),
      employeesByRole: Map<String, int>.from(json['employeesByRole'] ?? {}),
      employeesByStatus: Map<String, int>.from(json['employeesByStatus'] ?? {}),
      averageYearsOfService: json['averageYearsOfService']?.toDouble() ?? 0.0,
      newHiresThisMonth: json['newHiresThisMonth'] ?? 0,
      terminationsThisMonth: json['terminationsThisMonth'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalEmployees': totalEmployees,
      'activeEmployees': activeEmployees,
      'inactiveEmployees': inactiveEmployees,
      'employeesByDepartment': employeesByDepartment,
      'employeesByRole': employeesByRole,
      'employeesByStatus': employeesByStatus,
      'averageYearsOfService': averageYearsOfService,
      'newHiresThisMonth': newHiresThisMonth,
      'terminationsThisMonth': terminationsThisMonth,
    };
  }
}
