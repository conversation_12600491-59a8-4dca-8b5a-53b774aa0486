class ExpenseBreakdown {
  final Map<String, double> categories; // category name to amount
  final double totalExpenses;
  final DateTime startDate;
  final DateTime endDate;

  const ExpenseBreakdown({
    required this.categories,
    required this.totalExpenses,
    required this.startDate,
    required this.endDate,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExpenseBreakdown &&
          runtimeType == other.runtimeType &&
          categories == other.categories &&
          totalExpenses == other.totalExpenses &&
          startDate == other.startDate &&
          endDate == other.endDate;

  @override
  int get hashCode => 
      categories.hashCode ^ 
      totalExpenses.hashCode ^ 
      startDate.hashCode ^ 
      endDate.hashCode;
}
