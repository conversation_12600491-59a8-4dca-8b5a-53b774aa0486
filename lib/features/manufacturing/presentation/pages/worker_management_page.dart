import 'package:flutter/material.dart';

import '../../../../core/auth/widgets/permission_guard.dart';
import '../../../resource_management/domain/entities/worker_entities.dart';
import '../widgets/worker_card_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../widgets/worker_stats_widget.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../domain/entities/manufacturing_entities.dart';


/// Worker management page for manufacturing
class WorkerManagementPage extends StatefulWidget {
  const WorkerManagementPage({super.key});

  @override
  State<WorkerManagementPage> createState() => _WorkerManagementPageState();
}

class _WorkerManagementPageState extends State<WorkerManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  WorkerRole? _selectedRoleFilter;
  String? _selectedDepartmentFilter;
  WorkerStatus? _selectedStatusFilter;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Worker Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
            tooltip: 'Filter Workers',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(),
            tooltip: 'Search Workers',
          ),
          PermissionGuard(
            permission: 'manufacturing.workers.create',
            child: IconButton(
              icon: const Icon(Icons.person_add),
              onPressed: () => _showAddWorkerDialog(),
              tooltip: 'Add Worker',
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildWorkersTab(),
                _buildAttendanceTab(),
                _buildPerformanceTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: PermissionGuard(
        permission: 'manufacturing.workers.create',
        child: FloatingActionButton(
          onPressed: () => _showAddWorkerDialog(),
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.person_add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        isScrollable: true,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.people),
            text: 'Workers',
          ),
          Tab(
            icon: Icon(Icons.access_time),
            text: 'Attendance',
          ),
          Tab(
            icon: Icon(Icons.trending_up),
            text: 'Performance',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Worker Management Overview',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Worker Statistics
          const WorkerStatsWidget(),
          
          const SizedBox(height: 24),
          
          // Shift Overview
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current Shift Status',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildShiftOverview(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Department Workforce
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Workforce by Department',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDepartmentWorkforce(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Top Performers
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Top Performers This Month',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => _tabController.animateTo(3),
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildTopPerformers(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkersTab() {
    return PermissionGuard(
      permission: 'manufacturing.workers.read',
      fallback: const Center(
        child: Text('You do not have permission to view workers'),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Manufacturing Workers',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    if (_selectedRoleFilter != null)
                      Chip(
                        label: Text(_selectedRoleFilter!.displayName),
                        onDeleted: () => setState(() => _selectedRoleFilter = null),
                        deleteIcon: const Icon(Icons.close, size: 16),
                      ),
                    const SizedBox(width: 8),
                    PermissionGuard(
                      permission: 'manufacturing.workers.create',
                      child: ElevatedButton.icon(
                        onPressed: () => _showAddWorkerDialog(),
                        icon: const Icon(Icons.person_add),
                        label: const Text('Add Worker'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: _buildWorkerGrid(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Attendance Management',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Attendance Summary
          Row(
            children: [
              Expanded(
                child: _buildAttendanceCard(
                  'Present Today',
                  '142',
                  Icons.check_circle,
                  AppColors.success,
                  '89% attendance rate',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildAttendanceCard(
                  'Absent',
                  '18',
                  Icons.cancel,
                  AppColors.error,
                  '11% absence rate',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildAttendanceCard(
                  'On Leave',
                  '5',
                  Icons.event_busy,
                  AppColors.warning,
                  'Planned leaves',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildAttendanceCard(
                  'Late Arrivals',
                  '8',
                  Icons.schedule,
                  AppColors.primary,
                  'Today',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Attendance Chart
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Weekly Attendance Trend',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 300,
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text('Attendance trend charts will be displayed here'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Worker Performance Analytics',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Performance Metrics
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Average Performance',
                  '87.3%',
                  Icons.trending_up,
                  AppColors.success,
                  '+2.1% vs last month',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Tasks Completed',
                  '1,456',
                  Icons.task_alt,
                  AppColors.primary,
                  'This month',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Quality Score',
                  '94.8%',
                  Icons.verified,
                  AppColors.warning,
                  'Average quality',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Training Hours',
                  '245',
                  Icons.school,
                  AppColors.error,
                  'Total this quarter',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Performance Leaderboard
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Performance Leaderboard',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildPerformanceLeaderboard(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShiftOverview() {
    final shiftData = [
      {'shift': 'Morning (6 AM - 2 PM)', 'present': 58, 'total': 65, 'percentage': 89.2},
      {'shift': 'Afternoon (2 PM - 10 PM)', 'present': 52, 'total': 60, 'percentage': 86.7},
      {'shift': 'Night (10 PM - 6 AM)', 'present': 32, 'total': 35, 'percentage': 91.4},
    ];

    return Column(
      children: shiftData.map((shift) => Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.primary.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    shift['shift'] as String,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${shift['present']}/${shift['total']} workers present',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: LinearProgressIndicator(
                value: (shift['percentage'] as double) / 100,
                backgroundColor: AppColors.surface,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '${shift['percentage']}%',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildDepartmentWorkforce() {
    final departmentData = [
      {'name': 'Cutting', 'workers': 25, 'present': 23, 'efficiency': 92.0},
      {'name': 'Stitching', 'workers': 45, 'present': 41, 'efficiency': 91.1},
      {'name': 'Quality Control', 'workers': 15, 'present': 14, 'efficiency': 93.3},
      {'name': 'Packing', 'workers': 20, 'present': 18, 'efficiency': 90.0},
      {'name': 'Finishing', 'workers': 18, 'present': 16, 'efficiency': 88.9},
    ];

    return Column(
      children: departmentData.map((dept) => ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withOpacity(0.1),
          child: Text(
            '${dept['present']}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        title: Text(dept['name'] as String),
        subtitle: Text('${dept['present']}/${dept['workers']} present • ${dept['efficiency']}% efficiency'),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppColors.textSecondary,
        ),
        onTap: () => _viewDepartmentWorkers(dept['name'] as String),
      )).toList(),
    );
  }

  Widget _buildTopPerformers() {
    final topPerformers = [
      {'name': 'Sarah Johnson', 'department': 'Quality Control', 'score': 98.5, 'tasks': 45},
      {'name': 'Mike Wilson', 'department': 'Cutting', 'score': 96.8, 'tasks': 52},
      {'name': 'Lisa Chen', 'department': 'Stitching', 'score': 95.2, 'tasks': 67},
    ];

    return Column(
      children: topPerformers.asMap().entries.map((entry) {
        final index = entry.key;
        final performer = entry.value;
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getRankColor(index),
            child: Text(
              '${index + 1}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          title: Text(performer['name'] as String),
          subtitle: Text('${performer['department']} • ${performer['tasks']} tasks completed'),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${performer['score']}%',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          onTap: () => _viewWorkerDetails(performer['name'] as String),
        );
      }).toList(),
    );
  }

  Widget _buildWorkerGrid() {
    final workers = _getMockWorkers();
    final filteredWorkers = workers.where((worker) {
      if (_selectedRoleFilter != null && worker.role != _selectedRoleFilter) {
        return false;
      }
      if (_selectedStatusFilter != null && worker.status != _selectedStatusFilter) {
        return false;
      }
      if (_searchQuery.isNotEmpty && !worker.name.toLowerCase().contains(_searchQuery.toLowerCase())) {
        return false;
      }
      return true;
    }).toList();

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: filteredWorkers.length,
      itemBuilder: (context, index) {
        final worker = filteredWorkers[index];
        return WorkerCardWidget(
          worker: worker,
          onTap: () => _viewWorkerDetails(worker.name),
          onEdit: () => _editWorker(worker),
          onDelete: () => _deleteWorker(worker),
        );
      },
    );
  }

  Widget _buildAttendanceCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceLeaderboard() {
    final leaderboard = [
      {'rank': 1, 'name': 'Sarah Johnson', 'department': 'Quality Control', 'score': 98.5},
      {'rank': 2, 'name': 'Mike Wilson', 'department': 'Cutting', 'score': 96.8},
      {'rank': 3, 'name': 'Lisa Chen', 'department': 'Stitching', 'score': 95.2},
      {'rank': 4, 'name': 'John Doe', 'department': 'Packing', 'score': 94.1},
      {'rank': 5, 'name': 'Jane Smith', 'department': 'Finishing', 'score': 93.7},
    ];

    return Column(
      children: leaderboard.map((entry) => Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: _getRankColor((entry['rank'] as int) - 1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  '${entry['rank']}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry['name'] as String,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    entry['department'] as String,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              '${entry['score']}%',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  List<ManufacturingWorker> _getMockWorkers() {
    return [
      ManufacturingWorker(
        id: '1',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+1234567890',
        employeeId: 'EMP001',
        role: WorkerRole.operator,
        departmentId: 'dept_1',
        departmentName: 'Quality Control',
        position: 'Senior Quality Inspector',
        status: WorkerStatus.active,
        skills: ['Quality Inspection', 'Fabric Analysis'],
        experienceYears: 5.5,
        performanceScore: 98.5,
        joinedAt: DateTime.now().subtract(const Duration(days: 365 * 5)),
        lastActiveAt: DateTime.now().subtract(const Duration(minutes: 10)),
        currentShift: WorkerShift.morning,
        isPresent: true,
      ),
      // Add more mock workers...
    ];
  }

  Color _getRankColor(int index) {
    switch (index) {
      case 0:
        return Colors.amber; // Gold
      case 1:
        return Colors.grey; // Silver
      case 2:
        return Colors.brown; // Bronze
      default:
        return AppColors.primary;
    }
  }

  void _showFilterDialog() {
    // Implement filter dialog
  }

  void _showSearchDialog() {
    // Implement search dialog
  }

  void _showAddWorkerDialog() {
    // Implement add worker dialog
  }

  void _viewWorkerDetails(String workerName) {
    // Navigate to worker details page
  }

  void _editWorker(ManufacturingWorker worker) {
    // Implement edit worker
  }

  void _deleteWorker(ManufacturingWorker worker) {
    // Implement delete worker
  }

  void _viewDepartmentWorkers(String departmentName) {
    // Navigate to department workers view
  }
}
