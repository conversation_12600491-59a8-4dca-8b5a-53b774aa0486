part of 'order_bloc.dart';

/// Base order event
abstract class OrderEvent extends Equatable {
  const OrderEvent();

  @override
  List<Object?> get props => [];
}

/// Load orders
class LoadOrdersRequested extends OrderEvent {
  final OrderFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadOrdersRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Refresh orders
class RefreshOrdersRequested extends OrderEvent {
  const RefreshOrdersRequested();
}

/// Load order details
class LoadOrderDetailsRequested extends OrderEvent {
  final String orderId;

  const LoadOrderDetailsRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Create order
class CreateOrderRequested extends OrderEvent {
  final CreateOrderRequest request;

  const CreateOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update order
class UpdateOrderRequested extends OrderEvent {
  final repo.UpdateOrderRequest request;

  const UpdateOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete order
class DeleteOrderRequested extends OrderEvent {
  final String orderId;
  final String? reason;

  const DeleteOrderRequested(this.orderId, {this.reason});

  @override
  List<Object?> get props => [orderId, reason];
}

/// Update order status
class UpdateOrderStatusRequested extends OrderEvent {
  final String orderId;
  final OrderStatus status;
  final String? reason;

  const UpdateOrderStatusRequested(
    this.orderId,
    this.status, {
    this.reason,
  });

  @override
  List<Object?> get props => [orderId, status, reason];
}

/// Search orders
class SearchOrdersRequested extends OrderEvent {
  final String query;
  final OrderFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchOrdersRequested(
    this.query, {
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Filter orders
class FilterOrdersRequested extends OrderEvent {
  final OrderFilterCriteria filter;
  final PaginationParams? pagination;

  const FilterOrdersRequested(
    this.filter, {
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load orders by client
class LoadOrdersByClientRequested extends OrderEvent {
  final String clientId;
  final PaginationParams? pagination;

  const LoadOrdersByClientRequested(
    this.clientId, {
    this.pagination,
  });

  @override
  List<Object?> get props => [clientId, pagination];
}

/// Load overdue orders
class LoadOverdueOrdersRequested extends OrderEvent {
  final PaginationParams? pagination;

  const LoadOverdueOrdersRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Load urgent orders
class LoadUrgentOrdersRequested extends OrderEvent {
  final PaginationParams? pagination;

  const LoadUrgentOrdersRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Cancel order
class CancelOrderRequested extends OrderEvent {
  final String orderId;
  final String reason;

  const CancelOrderRequested(this.orderId, this.reason);

  @override
  List<Object?> get props => [orderId, reason];
}

/// Duplicate order
class DuplicateOrderRequested extends OrderEvent {
  final String orderId;
  final DuplicateOrderRequest request;

  const DuplicateOrderRequested(this.orderId, this.request);

  @override
  List<Object?> get props => [orderId, request];
}

/// Load order statistics
class LoadOrderStatisticsRequested extends OrderEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? clientId;

  const LoadOrderStatisticsRequested({
    this.startDate,
    this.endDate,
    this.clientId,
  });

  @override
  List<Object?> get props => [startDate, endDate, clientId];
}

/// Load order timeline
class LoadOrderTimelineRequested extends OrderEvent {
  final String orderId;

  const LoadOrderTimelineRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Add order note
class AddOrderNoteRequested extends OrderEvent {
  final AddOrderNoteRequest request;

  const AddOrderNoteRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update order note
class UpdateOrderNoteRequested extends OrderEvent {
  final UpdateOrderNoteRequest request;

  const UpdateOrderNoteRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete order note
class DeleteOrderNoteRequested extends OrderEvent {
  final String noteId;

  const DeleteOrderNoteRequested(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

/// Add order attachment
class AddOrderAttachmentRequested extends OrderEvent {
  final AddOrderAttachmentRequest request;

  const AddOrderAttachmentRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete order attachment
class DeleteOrderAttachmentRequested extends OrderEvent {
  final String attachmentId;

  const DeleteOrderAttachmentRequested(this.attachmentId);

  @override
  List<Object?> get props => [attachmentId];
}

/// Update order item status
class UpdateOrderItemStatusRequested extends OrderEvent {
  final String orderId;
  final String itemId;
  final OrderItemStatus status;
  final int? completedQuantity;

  const UpdateOrderItemStatusRequested(
    this.orderId,
    this.itemId,
    this.status, {
    this.completedQuantity,
  });

  @override
  List<Object?> get props => [orderId, itemId, status, completedQuantity];
}

/// Hold order
class HoldOrderRequested extends OrderEvent {
  final String orderId;
  final String reason;

  const HoldOrderRequested(this.orderId, this.reason);

  @override
  List<Object?> get props => [orderId, reason];
}

/// Resume order
class ResumeOrderRequested extends OrderEvent {
  final String orderId;

  const ResumeOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Export orders
class ExportOrdersRequested extends OrderEvent {
  final OrderFilterCriteria? filter;
  final String format;

  const ExportOrdersRequested({
    this.filter,
    this.format = 'csv',
  });

  @override
  List<Object?> get props => [filter, format];
}

/// Import orders
class ImportOrdersRequested extends OrderEvent {
  final String filePath;
  final bool validateOnly;

  const ImportOrdersRequested(
    this.filePath, {
    this.validateOnly = false,
  });

  @override
  List<Object?> get props => [filePath, validateOnly];
}

/// Bulk update orders
class BulkUpdateOrdersRequested extends OrderEvent {
  final List<BulkUpdateOrderRequest> requests;

  const BulkUpdateOrdersRequested(this.requests);

  @override
  List<Object?> get props => [requests];
}

/// Load order production schedule
class LoadOrderProductionScheduleRequested extends OrderEvent {
  final String orderId;

  const LoadOrderProductionScheduleRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Update order production schedule
class UpdateOrderProductionScheduleRequested extends OrderEvent {
  final String orderId;
  final List<OrderProductionSchedule> schedule;

  const UpdateOrderProductionScheduleRequested(
    this.orderId,
    this.schedule,
  );

  @override
  List<Object?> get props => [orderId, schedule];
}

/// Clear order state
class ClearOrderState extends OrderEvent {
  const ClearOrderState();
}

/// Load more orders (pagination)
class LoadMoreOrdersRequested extends OrderEvent {
  const LoadMoreOrdersRequested();
}

/// Sort orders
class SortOrdersRequested extends OrderEvent {
  final String sortBy;
  final bool ascending;

  const SortOrdersRequested(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Select order
class SelectOrderRequested extends OrderEvent {
  final String orderId;

  const SelectOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Deselect order
class DeselectOrderRequested extends OrderEvent {
  final String orderId;

  const DeselectOrderRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Select all orders
class SelectAllOrdersRequested extends OrderEvent {
  const SelectAllOrdersRequested();
}

/// Deselect all orders
class DeselectAllOrdersRequested extends OrderEvent {
  const DeselectAllOrdersRequested();
}

/// Toggle order selection
class ToggleOrderSelectionRequested extends OrderEvent {
  final String orderId;

  const ToggleOrderSelectionRequested(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Change order view mode
class ChangeOrderViewModeRequested extends OrderEvent {
  final OrderViewMode viewMode;

  const ChangeOrderViewModeRequested(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Order view mode enum
enum OrderViewMode {
  list,
  grid,
  kanban,
}

/// Order view mode extension
extension OrderViewModeExtension on OrderViewMode {
  String get displayName {
    switch (this) {
      case OrderViewMode.list:
        return 'List';
      case OrderViewMode.grid:
        return 'Grid';
      case OrderViewMode.kanban:
        return 'Kanban';
    }
  }

  IconData get icon {
    switch (this) {
      case OrderViewMode.list:
        return Icons.list;
      case OrderViewMode.grid:
        return Icons.grid_view;
      case OrderViewMode.kanban:
        return Icons.view_kanban;
    }
  }
}
