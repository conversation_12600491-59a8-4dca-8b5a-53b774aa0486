/// Route constants for navigation throughout the app
class RouteConstants {
  // Root Routes
  static const String root = '/';
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  
  // Authentication Routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  static const String changePassword = '/change-password';
  
  // Main Navigation Routes
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  
  // Order Management Routes
  static const String orders = '/orders';
  static const String ordersList = '$orders/list';
  static const String orderDetails = '$orders/details';
  static const String createOrder = '$orders/create';
  static const String editOrder = '$orders/edit';
  static const String orderHistory = '$orders/history';
  
  // Customer Management Routes
  static const String customers = '/customers';
  static const String customersList = '$customers/list';
  static const String customerDetails = '$customers/details';
  static const String createCustomer = '$customers/create';
  static const String editCustomer = '$customers/edit';
  
  // Production Management Routes
  static const String production = '/production';
  static const String productionDashboard = '$production/dashboard';
  static const String productionOrders = '$production/orders';
  static const String productionOrderDetails = '$production/order-details';
  static const String createProductionOrder = '$production/create-order';
  static const String productionSchedule = '$production/schedule';
  static const String productionTasks = '$production/tasks';
  static const String taskDetails = '$production/task-details';
  static const String assignTask = '$production/assign-task';
  
  // Department Routes
  static const String departments = '/departments';
  
  // Cutting Department Routes
  static const String cutting = '$departments/cutting';
  static const String cuttingDashboard = '$cutting/dashboard';
  static const String cuttingTasks = '$cutting/tasks';
  static const String markerPlanning = '$cutting/marker-planning';
  static const String fabricSpreading = '$cutting/fabric-spreading';
  
  // Sewing Department Routes
  static const String sewing = '$departments/sewing';
  static const String sewingDashboard = '$sewing/dashboard';
  static const String sewingLines = '$sewing/lines';
  static const String sewingLineDetails = '$sewing/line-details';
  static const String sewingTasks = '$sewing/tasks';
  static const String sewingOperators = '$sewing/operators';
  
  // Quality Control Routes
  static const String quality = '$departments/quality';
  static const String qualityDashboard = '$quality/dashboard';
  static const String inspections = '$quality/inspections';
  static const String inspectionDetails = '$quality/inspection-details';
  static const String createInspection = '$quality/create-inspection';
  static const String qualityReports = '$quality/reports';
  static const String defectTracking = '$quality/defects';
  static const String qualityStandards = '$quality/standards';
  
  // Finishing Department Routes
  static const String finishing = '$departments/finishing';
  static const String finishingDashboard = '$finishing/dashboard';
  static const String finishingTasks = '$finishing/tasks';
  static const String trimming = '$finishing/trimming';
  static const String ironing = '$finishing/ironing';
  static const String packing = '$finishing/packing';
  
  // Inventory Management Routes
  static const String inventory = '/inventory';
  static const String inventoryDashboard = '$inventory/dashboard';
  static const String materials = '$inventory/materials';
  static const String materialDetails = '$inventory/material-details';
  static const String addMaterial = '$inventory/add-material';
  static const String editMaterial = '$inventory/edit-material';
  static const String stockLevels = '$inventory/stock-levels';
  static const String stockMovements = '$inventory/movements';
  static const String lowStockAlerts = '$inventory/low-stock';
  static const String billOfMaterials = '$inventory/bom';
  static const String purchaseOrders = '$inventory/purchase-orders';
  
  // User Management Routes
  static const String users = '/users';
  static const String usersList = '$users/list';
  static const String userDetails = '$users/details';
  static const String createUser = '$users/create';
  static const String editUser = '$users/edit';
  static const String userRoles = '$users/roles';
  static const String userPermissions = '$users/permissions';
  
  // Workstation Routes
  static const String workstations = '/workstations';
  static const String workstationsList = '$workstations/list';
  static const String workstationDetails = '$workstations/details';
  static const String workstationTasks = '$workstations/tasks';
  static const String workstationPerformance = '$workstations/performance';
  
  // Reporting Routes
  static const String reports = '/reports';
  static const String productionReports = '$reports/production';
  static const String qualityReportsRoute = '$reports/quality';
  static const String inventoryReports = '$reports/inventory';
  static const String performanceReports = '$reports/performance';
  static const String customReports = '$reports/custom';
  
  // Analytics Routes
  static const String analytics = '/analytics';
  static const String productionAnalytics = '$analytics/production';
  static const String qualityAnalytics = '$analytics/quality';
  static const String performanceAnalytics = '$analytics/performance';
  static const String trendsAnalysis = '$analytics/trends';
  
  // Settings Routes
  static const String appSettings = '$settings/app';
  static const String userSettings = '$settings/user';
  static const String systemSettings = '$settings/system';
  static const String notificationSettings = '$settings/notifications';
  static const String securitySettings = '$settings/security';
  static const String backupSettings = '$settings/backup';
  
  // Help and Support Routes
  static const String help = '/help';
  static const String documentation = '$help/docs';
  static const String tutorials = '$help/tutorials';
  static const String support = '$help/support';
  static const String about = '$help/about';
  
  // Error Routes
  static const String error = '/error';
  static const String notFound = '/not-found';
  static const String unauthorized = '/unauthorized';
  static const String maintenance = '/maintenance';
  
  // Route Parameters
  static const String idParam = 'id';
  static const String userIdParam = 'userId';
  static const String orderIdParam = 'orderId';
  static const String taskIdParam = 'taskId';
  static const String customerIdParam = 'customerId';
  static const String materialIdParam = 'materialId';
  static const String workstationIdParam = 'workstationId';
  static const String inspectionIdParam = 'inspectionId';
  static const String departmentParam = 'department';
  static const String roleParam = 'role';
  static const String statusParam = 'status';
  
  // Route Names for Navigation
  static const String dashboardName = 'dashboard';
  static const String ordersName = 'orders';
  static const String productionName = 'production';
  static const String inventoryName = 'inventory';
  static const String qualityName = 'quality';
  static const String reportsName = 'reports';
  static const String settingsName = 'settings';
  static const String profileName = 'profile';
  
  // Deep Link Patterns
  static const String orderDeepLink = '/order/:$orderIdParam';
  static const String taskDeepLink = '/task/:$taskIdParam';
  static const String userDeepLink = '/user/:$userIdParam';
  static const String materialDeepLink = '/material/:$materialIdParam';
  static const String inspectionDeepLink = '/inspection/:$inspectionIdParam';
}
