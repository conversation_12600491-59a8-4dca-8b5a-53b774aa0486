/// API endpoints and configuration constants
class ApiConstants {
  // Base Configuration
  static const String baseUrl = 'https://api.hmcollection.com';
  static const String apiVersion = '/v1';
  static const String fullBaseUrl = '$baseUrl$apiVersion';
  
  // Headers
  static const String contentTypeHeader = 'Content-Type';
  static const String authorizationHeader = 'Authorization';
  static const String acceptHeader = 'Accept';
  static const String userAgentHeader = 'User-Agent';
  
  static const String contentTypeJson = 'application/json';
  static const String contentTypeFormData = 'multipart/form-data';
  static const String bearerPrefix = 'Bearer ';
  
  // Authentication Endpoints
  static const String auth = '/auth';
  static const String login = '$auth/login';
  static const String adminLogin = '$auth/admin/login';
  static const String register = '$auth/register';
  static const String logout = '$auth/logout';
  static const String refreshToken = '$auth/refresh';
  static const String forgotPassword = '$auth/forgot-password';
  static const String resetPassword = '$auth/reset-password';
  static const String changePassword = '$auth/change-password';
  static const String verifyToken = '$auth/verify';
  
  // User Management Endpoints
  static const String users = '/users';
  static const String userProfile = '$users/profile';
  static const String updateProfile = '$users/profile';
  static const String userRoles = '$users/roles';
  static const String userPermissions = '$users/permissions';
  static const String usersByRole = '$users/by-role';
  static const String usersByDepartment = '$users/by-department';
  
  // Order Management Endpoints
  static const String orders = '/orders';
  static const String createOrder = orders;
  static const String updateOrder = orders; // + /{id}
  static const String deleteOrder = orders; // + /{id}
  static const String orderDetails = orders; // + /{id}
  static const String orderStatus = '$orders/status'; // + /{id}
  static const String orderHistory = '$orders/history'; // + /{id}
  static const String ordersByCustomer = '$orders/by-customer'; // + /{customerId}
  static const String ordersByStatus = '$orders/by-status'; // + /{status}
  static const String orderSearch = '$orders/search';
  
  // Customer Management Endpoints
  static const String customers = '/customers';
  static const String createCustomer = customers;
  static const String updateCustomer = customers; // + /{id}
  static const String deleteCustomer = customers; // + /{id}
  static const String customerDetails = customers; // + /{id}
  static const String customerOrders = '$customers/orders'; // + /{id}
  
  // Production Management Endpoints
  static const String production = '/production';
  static const String productionOrders = '$production/orders';
  static const String createProductionOrder = productionOrders;
  static const String updateProductionOrder = productionOrders; // + /{id}
  static const String productionOrderDetails = productionOrders; // + /{id}
  static const String productionSchedule = '$production/schedule';
  static const String productionStatus = '$production/status';
  static const String productionSummary = '$production/summary';
  
  // Task Management Endpoints
  static const String tasks = '/tasks';
  static const String createTask = tasks;
  static const String updateTask = tasks; // + /{id}
  static const String deleteTask = tasks; // + /{id}
  static const String taskDetails = tasks; // + /{id}
  static const String tasksByUser = '$tasks/by-user'; // + /{userId}
  static const String tasksByDepartment = '$tasks/by-department'; // + /{department}
  static const String tasksByStatus = '$tasks/by-status'; // + /{status}
  static const String taskAssignment = '$tasks/assign'; // + /{id}
  static const String taskCompletion = '$tasks/complete'; // + /{id}
  
  // Department Management Endpoints
  static const String departments = '/departments';
  static const String cutting = '$departments/cutting';
  static const String sewing = '$departments/sewing';
  static const String quality = '$departments/quality';
  static const String finishing = '$departments/finishing';
  static const String warehouse = '$departments/warehouse';
  
  // Workstation Endpoints
  static const String workstations = '/workstations';
  static const String workstationStatus = '$workstations/status';
  static const String workstationTasks = '$workstations/tasks'; // + /{id}
  static const String workstationPerformance = '$workstations/performance'; // + /{id}
  
  // Inventory Management Endpoints
  static const String inventory = '/inventory';
  static const String materials = '$inventory/materials';
  static const String createMaterial = materials;
  static const String updateMaterial = materials; // + /{id}
  static const String deleteMaterial = materials; // + /{id}
  static const String materialDetails = materials; // + /{id}
  static const String stockLevels = '$inventory/stock-levels';
  static const String lowStockAlerts = '$inventory/low-stock';
  static const String stockMovements = '$inventory/movements';
  static const String billOfMaterials = '$inventory/bom';
  
  // Quality Control Endpoints
  static const String qualityControl = '/quality';
  static const String inspections = '$qualityControl/inspections';
  static const String createInspection = inspections;
  static const String updateInspection = inspections; // + /{id}
  static const String inspectionDetails = inspections; // + /{id}
  static const String qualityReports = '$qualityControl/reports';
  static const String defects = '$qualityControl/defects';
  static const String qualityStandards = '$qualityControl/standards';
  static const String qualityMetrics = '$qualityControl/metrics';
  
  // Reporting and Analytics Endpoints
  static const String reports = '/reports';
  static const String productionReports = '$reports/production';
  static const String qualityReportsEndpoint = '$reports/quality';
  static const String inventoryReports = '$reports/inventory';
  static const String performanceReports = '$reports/performance';
  static const String dashboardData = '$reports/dashboard';
  static const String analytics = '/analytics';
  static const String kpis = '$analytics/kpis';

  // Dashboard Endpoints
  static const String dashboard = '/dashboard';
  static const String dashboardLayout = '$dashboard/layout';
  static const String dashboardWidgets = '$dashboard/widgets';
  static const String dashboardStatistics = '$dashboard/statistics';

  // Extended Order Management Endpoints
  static const String ordersByClient = '$orders/client';
  static const String overdueOrders = '$orders/overdue';
  static const String urgentOrders = '$orders/urgent';
  static const String orderStatistics = '$orders/statistics';
  static const String orderTimeline = '$orders/timeline';
  static const String orderNotes = '$orders/notes';
  static const String orderAttachments = '$orders/attachments';

  // Client Management Endpoints
  static const String clients = '/clients';
  static const String clientStatistics = '$clients/statistics';
  static const String clientOrderHistory = '$clients/orders';

  // Production Planning Endpoints
  static const String productionOrdersPlanning = '/production-orders';
  static const String productionTasks = '/production-tasks';
  static const String resourceAllocations = '/resource-allocations';
  static const String productionSchedules = '/production-schedules';
  static const String workCenters = '/work-centers';
  static const String productionStatistics = '$productionOrdersPlanning/statistics';
  static const String productionCapacity = '$productionOrdersPlanning/capacity';
  static const String productionBottlenecks = '$productionOrdersPlanning/bottlenecks';

  // Extended Inventory Management Endpoints
  static const String inventoryItems = '/inventory-items';
  static const String stockMovementsExtended = '/stock-movements';
  static const String billOfMaterialsExtended = '/bill-of-materials';
  static const String purchaseOrders = '/purchase-orders';
  static const String suppliers = '/suppliers';
  static const String supplierQuotations = '/supplier-quotations';
  static const String inventoryStatistics = '$inventoryItems/statistics';
  static const String inventoryValuation = '$inventoryItems/valuation';
  static const String reorderSuggestions = '$inventoryItems/reorder-suggestions';

  // Extended Quality Control Endpoints
  static const String qualityInspections = '/quality-inspections';
  static const String qualityDefects = '/quality-defects';
  static const String qualityStandardsExtended = '/quality-standards';
  static const String qualityCheckpoints = '/quality-checkpoints';
  static const String complianceAudits = '/compliance-audits';
  static const String complianceRequirements = '/compliance-requirements';
  static const String certifications = '/certifications';
  static const String qualityStatistics = '$qualityInspections/statistics';
  static const String qualityTrends = '$qualityInspections/trends';
  static const String defectAnalysis = '$qualityDefects/analysis';

  // Resource Management Endpoints
  static const String machines = '/machines';
  static const String machineSchedules = '/machine-schedules';
  static const String machineMaintenance = '/machine-maintenance';
  static const String workers = '/workers';
  static const String workerSchedules = '/worker-schedules';
  static const String workerAttendance = '/worker-attendance';
  static const String workerLeave = '/worker-leave';
  static const String facilities = '/facilities';
  static const String departmentsExtended = '/departments';
  static const String machineUtilization = '$machines/utilization';
  static const String workerPerformance = '$workers/performance';
  static const String facilityCapacity = '$facilities/capacity';
  static const String facilityUtilization = '$facilities/utilization';
  
  // Notification Endpoints
  static const String notifications = '/notifications';
  static const String sendNotification = notifications;
  static const String markAsRead = '$notifications/read'; // + /{id}
  static const String markAllAsRead = '$notifications/read-all';
  static const String notificationSettings = '$notifications/settings';
  
  // File Upload Endpoints
  static const String files = '/files';
  static const String uploadFile = '$files/upload';
  static const String downloadFile = '$files/download'; // + /{id}
  static const String deleteFile = '$files/delete'; // + /{id}
  
  // Settings Endpoints
  static const String settings = '/settings';
  static const String appSettings = '$settings/app';
  static const String userSettings = '$settings/user';
  static const String systemSettings = '$settings/system';
  
  // Real-time Endpoints
  static const String websocket = '/ws';
  static const String realTimeUpdates = '$websocket/updates';
  static const String taskUpdates = '$websocket/tasks';
  static const String productionUpdates = '$websocket/production';
  static const String notificationUpdates = '$websocket/notifications';
  
  // Health Check
  static const String health = '/health';
  static const String version = '/version';
  
  // Query Parameters
  static const String pageParam = 'page';
  static const String limitParam = 'limit';
  static const String sortParam = 'sort';
  static const String orderParam = 'order';
  static const String searchParam = 'search';
  static const String filterParam = 'filter';
  static const String fromDateParam = 'from_date';
  static const String toDateParam = 'to_date';
  static const String statusParam = 'status';
  static const String departmentParam = 'department';
  static const String userIdParam = 'user_id';
  static const String roleParam = 'role';
}
