import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/shared/models/pagination.dart';
import '../entities/financial_entities.dart';
import '../entities/financial_report_entities.dart' hide FinancialSummary, CostTrends;

///* Financial Repository Interface
abstract class FinancialRepository {
  ///* Cost Center Methods
  Future<Either<Failure, ApiListResponse<CostCenter>>> getCostCenters(
      {CostCenterFilterCriteria? filter, PaginationParams? pagination});

  Future<Either<Failure, ApiResponse<CostCenter>>> getCostCenterById(
      String costCenterId);

  Future<Either<Failure, ApiResponse<CostCenter>>> createCostCenter(
      CreateCostCenterRequest request);

  Future<Either<Failure, ApiResponse<CostCenter>>> updateCostCenter(
      UpdateCostCenterRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteCostCenter(String costCenterId);

  Future<Either<Failure, ApiListResponse<CostCenter>>> searchCostCenters(
      {required String query,
      CostCenterFilterCriteria? filter,
      PaginationParams? pagination});

  ///* Transaction Methods
  Future<Either<Failure, ApiListResponse<FinancialTransaction>>> getTransactions(
      {TransactionFilterCriteria? filter, PaginationParams? pagination});

  Future<Either<Failure, ApiResponse<FinancialTransaction>>> getTransactionById(
      String transactionId);

  Future<Either<Failure, ApiResponse<FinancialTransaction>>> createTransaction(
      CreateTransactionRequest request);

  Future<Either<Failure, ApiResponse<FinancialTransaction>>> updateTransaction(
      UpdateTransactionRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteTransaction(String transactionId);

  Future<Either<Failure, ApiResponse<FinancialTransaction>>> approveTransaction(
      String transactionId, String approverId);

  Future<Either<Failure, ApiResponse<FinancialTransaction>>> rejectTransaction(
      String transactionId, String rejectedBy, String reason);

  Future<Either<Failure, ApiListResponse<FinancialTransaction>>> searchTransactions(
      {required String query,
      TransactionFilterCriteria? filter,
      PaginationParams? pagination});

  ///* Budget Methods
  Future<Either<Failure, ApiListResponse<Budget>>> getBudgets(
      {BudgetFilterCriteria? filter, PaginationParams? pagination});

  Future<Either<Failure, ApiResponse<Budget>>> getBudgetById(String budgetId);

  Future<Either<Failure, ApiResponse<Budget>>> createBudget(
      CreateBudgetRequest request);

  Future<Either<Failure, ApiResponse<Budget>>> updateBudget(
      UpdateBudgetRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteBudget(String budgetId);

  Future<Either<Failure, ApiResponse<Budget>>> approveBudget(
      String budgetId, String approverId);

  Future<Either<Failure, ApiResponse<Budget>>> rejectBudget(
      String budgetId, String rejectedBy, String reason);

  Future<Either<Failure, ApiListResponse<Budget>>> searchBudgets(
      {required String query,
      BudgetFilterCriteria? filter,
      PaginationParams? pagination});

  ///* Report Methods
  Future<Either<Failure, ApiListResponse<FinancialReport>>> getReports(
      {ReportFilterCriteria? filter, PaginationParams? pagination});

  Future<Either<Failure, ApiResponse<FinancialReport>>> generateReport(
      GenerateReportRequest request);

  Future<Either<Failure, ProfitLossStatement>> getProfitLossStatement(
      {required DateTime startDate,
      required DateTime endDate,
      String? costCenterId,
      String? departmentId});

  Future<Either<Failure, CostAnalysisReport>> getCostAnalysisReport(
      {required DateTime startDate,
      required DateTime endDate,
      String? costCenterId,
      String? departmentId});

  Future<Either<Failure, BudgetVarianceReport>> getBudgetVarianceReport(
      {required DateTime startDate,
      required DateTime endDate,
      String? budgetId,
      String? costCenterId});

  ///* Analytics Methods
  Future<Either<Failure, FinancialSummary>> getFinancialSummary(
      {required DateTime startDate,
      required DateTime endDate,
      String? costCenterId});

  Future<Either<Failure, CostTrends>> getCostTrends(
      {required DateTime startDate,
      required DateTime endDate,
      CostCategory? category,
      String? costCenterId});
}
