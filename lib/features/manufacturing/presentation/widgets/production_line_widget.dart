import 'package:flutter/material.dart';

class ProductionLineWidget extends StatelessWidget {
  final String lineId;
  final String lineName;
  final VoidCallback onTap;
  final String status; // e.g., 'Running', 'Idle', 'Maintenance'
  final double efficiency; // 0.0 to 100.0
  final int currentOrder;

  const ProductionLineWidget({
    Key? key,
    required this.lineId,
    required this.lineName,
    required this.onTap,
    this.status = 'Idle',  
    this.efficiency = 0.0,
    this.currentOrder = 0,
  }) : super(key: key);

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'running':
        return Colors.green;
      case 'idle':
        return Colors.orange;
      case 'maintenance':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    lineName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: _getStatusColor(status).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: _getStatusColor(status),
                        width: 1.0,
                      ),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                        color: _getStatusColor(status),
                        fontSize: 12.0,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8.0),
              Row(
                children: [
                  const Icon(Icons.speed, size: 16.0, color: Colors.blue),
                  const SizedBox(width: 4.0),
                  Text(
                    'Efficiency: ${efficiency.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16.0),
                  const Icon(Icons.assignment, size: 16.0, color: Colors.blue),
                  const SizedBox(width: 4.0),
                  Text(
                    'Order #$currentOrder',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 8.0),
              LinearProgressIndicator(
                value: efficiency / 100,
                backgroundColor: Colors.grey[200],
                valueColor: AlwaysStoppedAnimation<Color>(
                  efficiency > 70
                      ? Colors.green
                      : efficiency > 30
                          ? Colors.orange
                          : Colors.red,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
