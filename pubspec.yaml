name: hm_collection
description: "Cloth Manufacturing Management App - A comprehensive solution for managing the entire cloth manufacturing lifecycle."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1

  # State Management - Bloc Pattern
  flutter_bloc: ^8.1.6
  bloc: ^8.1.4
  equatable: ^2.0.5
  
  # Biometric Authentication
  local_auth: ^2.2.0  # Core package with platform-specific implementations
  
  # Secure Storage
  flutter_secure_storage: ^9.0.0

  # Functional Programming
  dartz: ^0.10.1
  universal_html: ^2.2.1

  # Networking and API
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0

  # Local Storage
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Dependency Injection
  get_it: ^8.0.0
  injectable: ^2.5.0

  # Navigation
  go_router: ^14.6.1
  #url_strategy: ^0.3.0

  # Web Interop
  js: ^0.6.7

  # Utilities
  intl: ^0.19.0
  uuid: ^4.5.1
  logger: ^2.4.0

  # PDF and Printing
  pdf: ^3.11.1
  printing: ^5.13.2

  # Permissions and Device Info
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  connectivity_plus: ^6.0.5

  # Biometric Authentication (disabled)
  # local_auth: ^2.3.0

  # Firebase - Updated to compatible versions
  firebase_core: ^3.7.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.4
  # firebase_messaging: ^15.0.0  # Temporarily commented out due to PromiseJsImpl conflicts
  firebase_analytics: ^11.2.1
  firebase_crashlytics: ^4.1.1
  # firebase_storage: ^12.0.0  # Temporarily commented out due to PromiseJsImpl conflicts
  firebase_remote_config: ^5.0.4
  google_sign_in: ^6.2.1
  # firebase_storage_web: ^4.0.0  # Temporarily commented out due to PromiseJsImpl conflicts
  # firebase_storage_platform_interface: ^5.0.0  # Temporarily commented out due to PromiseJsImpl conflicts

  # Real-time Updates
  web_socket_channel: ^3.0.1

  # File Handling
  path_provider: ^2.1.4
  file_picker: ^8.1.2

  # OTP and 2FA
  otp: ^3.2.0
  base32: ^2.1.3

  # Image Handling
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2

  # Charts and Analytics
  fl_chart: ^0.69.0

  # Date and Time
  table_calendar: ^3.1.2

  # Form Validation
  formz: ^0.7.0
  zxcvbn: ^1.0.0

  # Notifications
  flutter_local_notifications: ^18.0.1

  # QR Code and Barcode
  qr_flutter: ^4.1.0
  mobile_scanner: ^5.2.3
  
  # OTP Generation and Verification
  encrypt: ^5.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  very_good_analysis: ^6.0.0

  # Code Generation
  build_runner: ^2.4.8
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.0
  injectable_generator: ^2.4.3
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.4
  integration_test:
    sdk: flutter

# Dependency overrides to fix version conflicts
dependency_overrides:
  js: ^0.6.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets for the application
  assets:
    - assets/images/
    - assets/images/icons/
    - assets/images/icons/department_icons/
    - assets/images/icons/status_icons/
    - assets/images/logos/
    - assets/images/illustrations/
    - assets/config/
    - assets/translations/

  # Custom fonts for the application
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
          weight: 400
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
