import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/core/usecases/usecase.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/features/quality_control/domain/entities/quality_defect_filter.dart';
import 'package:hm_collection/features/quality_control/domain/entities/requests/update_checkpoint_result_request.dart';
import 'package:hm_collection/features/quality_control/domain/entities/requests/update_quality_defect_request.dart';
import 'package:hm_collection/features/quality_control/domain/entities/requests/update_quality_inspection_request.dart';
import 'package:hm_collection/features/quality_control/domain/entities/quality_inspection_filter.dart';
import 'package:hm_collection/shared/models/pagination.dart';
import 'package:hm_collection/features/quality_control/domain/entities/quality_entities.dart';
import 'package:hm_collection/features/quality_control/domain/repositories/quality_repository.dart';

import '../../presentation/widgets/quality_statistics_card.dart';
import '../entities/defect_analysis_entity.dart';
import '../entities/quality_standard_filter.dart';
import '../entities/quality_trends_entity.dart';
import '../entities/requests/create_quality_standard_request.dart';
import '../entities/requests/resolve_quality_defect_request.dart';
import '../entities/requests/update_quality_defect_request.dart';
import '../entities/requests/update_quality_inspection_request.dart';
import '../models/requests/add_quality_defect_request.dart';
import '../models/requests/create_quality_inspection_request.dart';

class GetQualityInspectionsUseCase
    implements
        UseCase<ApiListResponse<QualityInspection>,
            GetQualityInspectionsParams> {
  final QualityRepository repository;

  GetQualityInspectionsUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<QualityInspection>>> call(
      GetQualityInspectionsParams params) async {
    return await repository.getQualityInspections(params);
  }
}

class GetQualityInspectionByIdUseCase
    implements UseCase<ApiResponse<QualityInspection>, IdParams> {
  final QualityRepository repository;

  GetQualityInspectionByIdUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityInspection>>> call(
      IdParams params) async {
    return await repository.getQualityInspectionById(params.id);
  }
}

class CreateQualityInspectionUseCase
    implements
        UseCase<ApiResponse<QualityInspection>, CreateQualityInspectionRequest> {
  final QualityRepository repository;

  CreateQualityInspectionUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityInspection>>> call(
      CreateQualityInspectionRequest params) async {
    return await repository.createQualityInspection(params);
  }
}

class UpdateQualityInspectionUseCase
    implements
        UseCase<ApiResponse<QualityInspection>, UpdateQualityInspectionRequest> {
  final QualityRepository repository;

  UpdateQualityInspectionUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityInspection>>> call(
      UpdateQualityInspectionRequest params) async {
    return await repository.updateQualityInspection(params);
  }
}

class StartQualityInspectionUseCase
    implements UseCase<ApiResponse<QualityInspection>, IdParams> {
  final QualityRepository repository;

  StartQualityInspectionUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityInspection>>> call(
      IdParams params) async {
    return await repository.startQualityInspection(params.id);
  }
}

class CompleteQualityInspectionUseCase
    implements
        UseCase<ApiResponse<QualityInspection>,
            CompleteQualityInspectionParams> {
  final QualityRepository repository;

  CompleteQualityInspectionUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityInspection>>> call(
      CompleteQualityInspectionParams params) async {
    return await repository.completeQualityInspection(params);
  }
}

class UpdateCheckpointResultUseCase
    implements
        UseCase<ApiResponse<CheckpointResult>, UpdateCheckpointResultParams> {
  final QualityRepository repository;

  UpdateCheckpointResultUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<CheckpointResult>>> call(
      UpdateCheckpointResultParams params) async {
    return await repository.updateCheckpointResult(params);
  }
}

class AddQualityDefectUseCase
    implements UseCase<ApiResponse<QualityDefect>, AddQualityDefectRequest> {
  final QualityRepository repository;

  AddQualityDefectUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityDefect>>> call(
      AddQualityDefectRequest params) async {
    return await repository.addQualityDefect(params);
  }
}

class UpdateQualityDefectUseCase
    implements UseCase<ApiResponse<QualityDefect>, UpdateQualityDefectRequest> {
  final QualityRepository repository;

  UpdateQualityDefectUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityDefect>>> call(
      UpdateQualityDefectRequest params) async {
    return await repository.updateQualityDefect(params);
  }
}

class ResolveQualityDefectUseCase
    implements UseCase<ApiResponse<QualityDefect>, ResolveQualityDefectParams> {
  final QualityRepository repository;

  ResolveQualityDefectUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityDefect>>> call(
      ResolveQualityDefectParams params) async {
    return await repository.resolveQualityDefect(params);
  }
}

class GetQualityDefectsUseCase
    implements UseCase<ApiListResponse<QualityDefect>, GetQualityDefectsParams> {
  final QualityRepository repository;

  GetQualityDefectsUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<QualityDefect>>> call(
      GetQualityDefectsParams params) async {
    return await repository.getQualityDefects(params);
  }
}

class GetQualityStandardsUseCase
    implements
        UseCase<ApiListResponse<QualityStandard>, GetQualityStandardsParams> {
  final QualityRepository repository;

  GetQualityStandardsUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<QualityStandard>>> call(
      GetQualityStandardsParams params) async {
    return await repository.getQualityStandards(params);
  }
}

class CreateQualityStandardUseCase
    implements UseCase<ApiResponse<QualityStandard>, CreateQualityStandardRequest> {
  final QualityRepository repository;

  CreateQualityStandardUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityStandard>>> call(
      CreateQualityStandardRequest params) async {
    return await repository.createQualityStandard(params);
  }
}

class ApproveQualityStandardUseCase
    implements UseCase<ApiResponse<QualityStandard>, ApproveQualityStandardParams> {
  final QualityRepository repository;

  ApproveQualityStandardUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<QualityStandard>>> call(
      ApproveQualityStandardParams params) async {
    return await repository.approveQualityStandard(params);
  }
}

class GetQualityStatisticsUseCase
    implements UseCase<QualityStatistics, GetQualityStatisticsParams> {
  final QualityRepository repository;

  GetQualityStatisticsUseCase(this.repository);

  @override
  Future<Either<Failure, QualityStatistics>> call(
      GetQualityStatisticsParams params) async {
    return await repository.getQualityStatistics(params);
  }
}

class GetQualityTrendsUseCase
    implements UseCase<QualityTrends, GetQualityTrendsParams> {
  final QualityRepository repository;

  GetQualityTrendsUseCase(this.repository);

  @override
  Future<Either<Failure, QualityTrends>> call(
      GetQualityTrendsParams params) async {
    return await repository.getQualityTrends(params);
  }
}

class GetDefectAnalysisUseCase
    implements UseCase<DefectAnalysis, GetDefectAnalysisParams> {
  final QualityRepository repository;

  GetDefectAnalysisUseCase(this.repository);

  @override
  Future<Either<Failure, DefectAnalysis>> call(
      GetDefectAnalysisParams params) async {
    return await repository.getDefectAnalysis(params);
  }
}

class SearchQualityInspectionsUseCase
    implements
        UseCase<ApiListResponse<QualityInspection>,
            SearchQualityInspectionsParams> {
  final QualityRepository repository;

  SearchQualityInspectionsUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<QualityInspection>>> call(
      SearchQualityInspectionsParams params) async {
    return await repository.searchQualityInspections(params);
  }
}

class GetPendingInspectionsUseCase
    implements
        UseCase<ApiListResponse<QualityInspection>,
            GetPendingInspectionsParams> {
  final QualityRepository repository;

  GetPendingInspectionsUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<QualityInspection>>> call(
      GetPendingInspectionsParams params) async {
    return await repository.getPendingInspections(params);
  }
}

class GetFailedInspectionsUseCase
    implements
        UseCase<ApiListResponse<QualityInspection>,
            GetFailedInspectionsParams> {
  final QualityRepository repository;

  GetFailedInspectionsUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<QualityInspection>>> call(
      GetFailedInspectionsParams params) async {
    return await repository.getFailedInspections(params);
  }
}

class GetQualityInspectionsParams extends Equatable {
  final QualityInspectionFilter? filter;
  final PaginationParams? pagination;

  const GetQualityInspectionsParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class CompleteQualityInspectionParams extends Equatable {
  final String inspectionId;
  final UpdateQualityInspectionRequest request;

  const CompleteQualityInspectionParams(this.inspectionId, this.request);

  @override
  List<Object?> get props => [inspectionId, request];
}

class UpdateCheckpointResultParams extends Equatable {
  final String checkpointId;
  final UpdateCheckpointResultRequest request;

  const UpdateCheckpointResultParams(this.checkpointId, this.request);

  @override
  List<Object?> get props => [checkpointId, request];
}

class ResolveQualityDefectParams extends Equatable {
  final String defectId;
  final ResolveQualityDefectRequest request;

  const ResolveQualityDefectParams(this.defectId, this.request);

  @override
  List<Object?> get props => [defectId, request];
}

class GetQualityDefectsParams extends Equatable {
  final QualityDefectFilter? filter;
  final PaginationParams? pagination;

  const GetQualityDefectsParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class GetQualityStandardsParams extends Equatable {
  final QualityStandardFilter? filter;
  final PaginationParams? pagination;

  const GetQualityStandardsParams({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

class ApproveQualityStandardParams extends Equatable {
  final String standardId;
  final String approverId;

  const ApproveQualityStandardParams(this.standardId, this.approverId);

  @override
  List<Object?> get props => [standardId, approverId];
}

class GetQualityStatisticsParams extends Equatable {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? departmentId;

  const GetQualityStatisticsParams(
      {this.startDate, this.endDate, this.departmentId});

  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

class GetQualityTrendsParams extends Equatable {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? productId;

  const GetQualityTrendsParams({this.startDate, this.endDate, this.productId});

  @override
  List<Object?> get props => [startDate, endDate, productId];
}

class GetDefectAnalysisParams extends Equatable {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? categoryId;

  const GetDefectAnalysisParams({this.startDate, this.endDate, this.categoryId});

  @override
  List<Object?> get props => [startDate, endDate, categoryId];
}

class SearchQualityInspectionsParams extends Equatable {
  final String query;
  final QualityInspectionFilter? filter;
  final PaginationParams? pagination;

  const SearchQualityInspectionsParams(this.query, {this.filter, this.pagination});

  @override
  List<Object?> get props => [query, filter, pagination];
}

class GetPendingInspectionsParams extends Equatable {
  final PaginationParams? pagination;

  const GetPendingInspectionsParams({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

class GetFailedInspectionsParams extends Equatable {
  final PaginationParams? pagination;

  const GetFailedInspectionsParams({this.pagination});

  @override
  List<Object?> get props => [pagination];
}
