part of 'dashboard_bloc.dart';

/// Base dashboard event
abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

/// Load dashboard for user
class LoadDashboardRequested extends DashboardEvent {
  final User user;

  const LoadDashboardRequested(this.user);

  @override
  List<Object?> get props => [user];
}

/// Refresh dashboard data
class RefreshDashboardRequested extends DashboardEvent {
  final User user;

  const RefreshDashboardRequested(this.user);

  @override
  List<Object?> get props => [user];
}

/// Update dashboard layout
class UpdateDashboardLayoutRequested extends DashboardEvent {
  final DashboardLayout layout;

  const UpdateDashboardLayoutRequested(this.layout);

  @override
  List<Object?> get props => [layout];
}

/// Load dashboard statistics
class LoadDashboardStatisticsRequested extends DashboardEvent {
  final UserRole role;
  final Department department;

  const LoadDashboardStatisticsRequested(this.role, this.department);

  @override
  List<Object?> get props => [role, department];
}

/// Load dashboard charts
class LoadDashboardChartsRequested extends DashboardEvent {
  final UserRole role;
  final Department department;
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadDashboardChartsRequested(
    this.role,
    this.department, {
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [role, department, startDate, endDate];
}

/// Load recent activities
class LoadRecentActivitiesRequested extends DashboardEvent {
  final String? userId;
  final UserRole? role;
  final Department? department;
  final PaginationParams? pagination;

  const LoadRecentActivitiesRequested({
    this.userId,
    this.role,
    this.department,
    this.pagination,
  });

  @override
  List<Object?> get props => [userId, role, department, pagination];
}

/// Load notifications
class LoadNotificationsRequested extends DashboardEvent {
  final String? userId;
  final bool? unreadOnly;
  final PaginationParams? pagination;

  const LoadNotificationsRequested({
    this.userId,
    this.unreadOnly,
    this.pagination,
  });

  @override
  List<Object?> get props => [userId, unreadOnly, pagination];
}

/// Mark notification as read
class MarkNotificationAsReadRequested extends DashboardEvent {
  final String notificationId;

  const MarkNotificationAsReadRequested(this.notificationId);

  @override
  List<Object?> get props => [notificationId];
}

/// Load quick actions
class LoadQuickActionsRequested extends DashboardEvent {
  final UserRole role;
  final Department department;

  const LoadQuickActionsRequested(this.role, this.department);

  @override
  List<Object?> get props => [role, department];
}

/// Load widget data
class LoadWidgetDataRequested extends DashboardEvent {
  final String widgetId;
  final Map<String, dynamic> configuration;

  const LoadWidgetDataRequested(this.widgetId, this.configuration);

  @override
  List<Object?> get props => [widgetId, configuration];
}

/// Load analytics data
class LoadAnalyticsRequested extends DashboardEvent {
  final AnalyticsType type;
  final Department? department;
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadAnalyticsRequested(
    this.type, {
    this.department,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [type, department, startDate, endDate];
}

/// Filter dashboard data
class FilterDashboardDataRequested extends DashboardEvent {
  final DateTimeRange? dateFilter;
  final Department? departmentFilter;

  const FilterDashboardDataRequested({
    this.dateFilter,
    this.departmentFilter,
  });

  @override
  List<Object?> get props => [dateFilter, departmentFilter];
}

/// Add widget to dashboard
class AddWidgetRequested extends DashboardEvent {
  final DashboardWidget widget;

  const AddWidgetRequested(this.widget);

  @override
  List<Object?> get props => [widget];
}

/// Remove widget from dashboard
class RemoveWidgetRequested extends DashboardEvent {
  final String widgetId;

  const RemoveWidgetRequested(this.widgetId);

  @override
  List<Object?> get props => [widgetId];
}

/// Reorder widgets
class ReorderWidgetsRequested extends DashboardEvent {
  final List<DashboardWidget> widgets;

  const ReorderWidgetsRequested(this.widgets);

  @override
  List<Object?> get props => [widgets];
}

/// Update widget configuration
class UpdateWidgetConfigurationRequested extends DashboardEvent {
  final String widgetId;
  final Map<String, dynamic> configuration;

  const UpdateWidgetConfigurationRequested(this.widgetId, this.configuration);

  @override
  List<Object?> get props => [widgetId, configuration];
}

/// Toggle widget visibility
class ToggleWidgetVisibilityRequested extends DashboardEvent {
  final String widgetId;

  const ToggleWidgetVisibilityRequested(this.widgetId);

  @override
  List<Object?> get props => [widgetId];
}

/// Export dashboard data
class ExportDashboardDataRequested extends DashboardEvent {
  final UserRole role;
  final Department department;
  final DateTime? startDate;
  final DateTime? endDate;
  final String format;

  const ExportDashboardDataRequested(
    this.role,
    this.department, {
    this.startDate,
    this.endDate,
    this.format = 'pdf',
  });

  @override
  List<Object?> get props => [role, department, startDate, endDate, format];
}

/// Clear dashboard state
class ClearDashboardState extends DashboardEvent {
  const ClearDashboardState();
}

/// Date time range helper class
class DateTimeRange extends Equatable {
  final DateTime start;
  final DateTime end;

  const DateTimeRange(this.start, this.end);

  @override
  List<Object?> get props => [start, end];
}
