import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/user_management.dart';

/// Dialog for filtering users
class UserFilterDialog extends StatefulWidget {
  final UserFilterCriteria? currentFilter;

  const UserFilterDialog({
    super.key,
    this.currentFilter,
  });

  @override
  State<UserFilterDialog> createState() => _UserFilterDialogState();
}

class _UserFilterDialogState extends State<UserFilterDialog> {
  late UserRole? _selectedRole;
  late Department? _selectedDepartment;
  late CommonStatus? _selectedStatus;
  late bool? _isActive;
  DateTime? _createdAfter;
  DateTime? _createdBefore;
  DateTime? _lastLoginAfter;
  DateTime? _lastLoginBefore;

  @override
  void initState() {
    super.initState();
    _initializeFilters();
  }

  void _initializeFilters() {
    final filter = widget.currentFilter;
    _selectedRole = filter?.role;
    _selectedDepartment = filter?.department;
    _selectedStatus = filter?.status;
    _isActive = filter?.isActive;
    _createdAfter = filter?.createdAfter;
    _createdBefore = filter?.createdBefore;
    _lastLoginAfter = filter?.lastLoginAfter;
    _lastLoginBefore = filter?.lastLoginBefore;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildFilterContent(),
            const SizedBox(height: 24),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.filter_list,
          color: AppColors.primary,
        ),
        const SizedBox(width: 8),
        Text(
          'Filter Users',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildFilterContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRoleFilter(),
          const SizedBox(height: 16),
          _buildDepartmentFilter(),
          const SizedBox(height: 16),
          _buildStatusFilter(),
          const SizedBox(height: 16),
          _buildActiveFilter(),
          const SizedBox(height: 16),
          _buildDateFilters(),
        ],
      ),
    );
  }

  Widget _buildRoleFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Role',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<UserRole?>(
          value: _selectedRole,
          decoration: const InputDecoration(
            hintText: 'Select role',
            border: OutlineInputBorder(),
          ),
          items: [
            const DropdownMenuItem<UserRole?>(
              value: null,
              child: Text('All Roles'),
            ),
            ...UserRole.values.map((role) => DropdownMenuItem(
              value: role,
              child: Text(role.displayName),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _selectedRole = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDepartmentFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Department',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<Department?>(
          value: _selectedDepartment,
          decoration: const InputDecoration(
            hintText: 'Select department',
            border: OutlineInputBorder(),
          ),
          items: [
            const DropdownMenuItem<Department?>(
              value: null,
              child: Text('All Departments'),
            ),
            ...Department.values.map((dept) => DropdownMenuItem(
              value: dept,
              child: Text(dept.displayName),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _selectedDepartment = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<CommonStatus?>(
          value: _selectedStatus,
          decoration: const InputDecoration(
            hintText: 'Select status',
            border: OutlineInputBorder(),
          ),
          items: [
            const DropdownMenuItem<CommonStatus?>(
              value: null,
              child: Text('All Status'),
            ),
            ...CommonStatus.values.map((status) => DropdownMenuItem(
              value: status,
              child: Text(status.displayName),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _selectedStatus = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildActiveFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Active Status',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<bool?>(
          value: _isActive,
          decoration: const InputDecoration(
            hintText: 'Select active status',
            border: OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem<bool?>(
              value: null,
              child: Text('All Users'),
            ),
            DropdownMenuItem<bool?>(
              value: true,
              child: Text('Active Only'),
            ),
            DropdownMenuItem<bool?>(
              value: false,
              child: Text('Inactive Only'),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _isActive = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Filters',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: 'Created After',
                value: _createdAfter,
                onChanged: (date) => setState(() => _createdAfter = date),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateField(
                label: 'Created Before',
                value: _createdBefore,
                onChanged: (date) => setState(() => _createdBefore = date),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: 'Last Login After',
                value: _lastLoginAfter,
                onChanged: (date) => setState(() => _lastLoginAfter = date),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateField(
                label: 'Last Login Before',
                value: _lastLoginBefore,
                onChanged: (date) => setState(() => _lastLoginBefore = date),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? value,
    required Function(DateTime?) onChanged,
  }) {
    return InkWell(
      onTap: () => _selectDate(onChanged),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: value != null
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => onChanged(null),
                )
              : const Icon(Icons.calendar_today),
        ),
        child: Text(
          value != null
              ? '${value.day}/${value.month}/${value.year}'
              : 'Select date',
          style: TextStyle(
            color: value != null 
                ? Theme.of(context).textTheme.bodyLarge?.color
                : Theme.of(context).hintColor,
          ),
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        TextButton(
          onPressed: _clearFilters,
          child: const Text('Clear All'),
        ),
        const Spacer(),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _applyFilters,
          child: const Text('Apply Filters'),
        ),
      ],
    );
  }

  Future<void> _selectDate(Function(DateTime?) onChanged) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      onChanged(date);
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedRole = null;
      _selectedDepartment = null;
      _selectedStatus = null;
      _isActive = null;
      _createdAfter = null;
      _createdBefore = null;
      _lastLoginAfter = null;
      _lastLoginBefore = null;
    });
  }

  void _applyFilters() {
    final filter = UserFilterCriteria(
      role: _selectedRole,
      department: _selectedDepartment,
      status: _selectedStatus,
      isActive: _isActive,
      createdAfter: _createdAfter,
      createdBefore: _createdBefore,
      lastLoginAfter: _lastLoginAfter,
      lastLoginBefore: _lastLoginBefore,
    );

    Navigator.of(context).pop(filter);
  }
}
