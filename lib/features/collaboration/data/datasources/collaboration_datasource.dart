import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/collaboration_entities.dart';
import '../../domain/repositories/collaboration_repository.dart';

/// Collaboration data source interface
abstract class CollaborationDataSource {
  // Workspace Management
  Future<ApiListResponse<CollaborationWorkspace>> getWorkspaces({
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<CollaborationWorkspace>> getWorkspaceById(String workspaceId);

  Future<ApiResponse<CollaborationWorkspace>> createWorkspace(CreateWorkspaceRequest request);

  Future<ApiResponse<CollaborationWorkspace>> updateWorkspace(UpdateWorkspaceRequest request);

  Future<ApiVoidResponse> deleteWorkspace(String workspaceId);

  Future<ApiResponse<CollaborationWorkspace>> archiveWorkspace(String workspaceId);

  Future<ApiResponse<CollaborationWorkspace>> restoreWorkspace(String workspaceId);

  // Member Management
  Future<ApiResponse<WorkspaceMember>> addMember(AddMemberRequest request);

  Future<ApiResponse<WorkspaceMember>> updateMember(UpdateMemberRequest request);

  Future<ApiVoidResponse> removeMember(String workspaceId, String userId);

  Future<ApiListResponse<WorkspaceMember>> getWorkspaceMembers({
    required String workspaceId,
    MemberFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<WorkspaceMember>> inviteMember(InviteMemberRequest request);

  Future<ApiVoidResponse> acceptInvitation(String invitationId);

  Future<ApiVoidResponse> declineInvitation(String invitationId);

  // Channel Management
  Future<ApiListResponse<CollaborationChannel>> getChannels({
    required String workspaceId,
    ChannelFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<CollaborationChannel>> getChannelById(String channelId);

  Future<ApiResponse<CollaborationChannel>> createChannel(CreateChannelRequest request);

  Future<ApiResponse<CollaborationChannel>> updateChannel(UpdateChannelRequest request);

  Future<ApiVoidResponse> deleteChannel(String channelId);

  Future<ApiResponse<CollaborationChannel>> archiveChannel(String channelId);

  Future<ApiResponse<CollaborationChannel>> joinChannel(String channelId);

  Future<ApiVoidResponse> leaveChannel(String channelId);

  // Message Management
  Future<ApiListResponse<CollaborationMessage>> getMessages({
    required String channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<CollaborationMessage>> getMessageById(String messageId);

  Future<ApiResponse<CollaborationMessage>> sendMessage(SendMessageRequest request);

  Future<ApiResponse<CollaborationMessage>> editMessage(EditMessageRequest request);

  Future<ApiVoidResponse> deleteMessage(String messageId);

  Future<ApiResponse<CollaborationMessage>> pinMessage(String messageId);

  Future<ApiVoidResponse> unpinMessage(String messageId);

  Future<ApiResponse<CollaborationMessage>> addReaction(AddReactionRequest request);

  Future<ApiVoidResponse> removeReaction(RemoveReactionRequest request);

  Future<ApiVoidResponse> markMessageAsRead(String messageId);

  Future<ApiVoidResponse> markChannelAsRead(String channelId);

  // Thread Management
  Future<ApiListResponse<CollaborationMessage>> getThreadMessages({
    required String parentMessageId,
    PaginationParams? pagination,
  });

  Future<ApiResponse<CollaborationMessage>> replyToThread(ReplyToThreadRequest request);

  // File Management
  Future<ApiResponse<MessageAttachment>> uploadFile(UploadFileRequest request);

  Future<String> getFileDownloadUrl(String attachmentId);

  Future<ApiVoidResponse> deleteFile(String attachmentId);

  Future<ApiListResponse<MessageAttachment>> getChannelFiles({
    required String channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  });

  // Real-time Features
  Stream<CollaborationMessage> getMessageStream(String channelId);

  Stream<RealTimeActivity> getActivityStream(String workspaceId);

  Stream<List<WorkspaceMember>> getOnlineMembersStream(String workspaceId);

  Future<ApiVoidResponse> updateUserPresence(UpdatePresenceRequest request);

  Future<ApiVoidResponse> sendTypingIndicator(TypingIndicatorRequest request);

  // Session Management
  Future<ApiListResponse<CollaborationSession>> getSessions({
    required String workspaceId,
    SessionFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiResponse<CollaborationSession>> getSessionById(String sessionId);

  Future<ApiResponse<CollaborationSession>> createSession(CreateSessionRequest request);

  Future<ApiResponse<CollaborationSession>> updateSession(UpdateSessionRequest request);

  Future<ApiVoidResponse> deleteSession(String sessionId);

  Future<ApiResponse<CollaborationSession>> startSession(String sessionId);

  Future<ApiResponse<CollaborationSession>> endSession(String sessionId);

  Future<ApiResponse<SessionParticipant>> joinSession(JoinSessionRequest request);

  Future<ApiVoidResponse> leaveSession(String sessionId);

  Future<ApiResponse<SessionParticipant>> updateParticipant(UpdateParticipantRequest request);

  // Search and Discovery
  Future<ApiListResponse<CollaborationWorkspace>> searchWorkspaces({
    required String query,
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiListResponse<CollaborationMessage>> searchMessages({
    required String query,
    required String workspaceId,
    String? channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiListResponse<MessageAttachment>> searchFiles({
    required String query,
    required String workspaceId,
    String? channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  });

  // Analytics and Insights
  Future<Map<String, dynamic>> getWorkspaceAnalytics({
    required String workspaceId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<Map<String, dynamic>> getChannelAnalytics({
    required String channelId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<Map<String, dynamic>> getUserActivityAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  // Notifications
  Future<ApiListResponse<RealTimeActivity>> getNotifications({
    NotificationFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<ApiVoidResponse> markNotificationAsRead(String activityId);

  Future<ApiVoidResponse> markAllNotificationsAsRead();

  Future<ApiVoidResponse> updateNotificationSettings(UpdateNotificationSettingsRequest request);
}
