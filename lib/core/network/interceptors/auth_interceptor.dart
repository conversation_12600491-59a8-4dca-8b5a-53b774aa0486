import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../constants/api_constants.dart';
import '../../constants/app_constants.dart';

/// Interceptor for handling authentication headers
class AuthInterceptor extends Interceptor {
  final GetIt _getIt = GetIt.instance;
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Skip auth for login and refresh token endpoints
    if (_shouldSkipAuth(options.path)) {
      return handler.next(options);
    }
    
    try {
      final prefs = _getIt<SharedPreferences>();
      final accessToken = prefs.getString(AppConstants.accessTokenKey);
      
      if (accessToken != null && accessToken.isNotEmpty) {
        options.headers[ApiConstants.authorizationHeader] = 
            '${ApiConstants.bearerPrefix}$accessToken';
      }
    } catch (e) {
      // If we can't get the token, continue without it
      // The server will return 401 if auth is required
    }
    
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle 401 Unauthorized errors
    if (err.response?.statusCode == 401) {
      final refreshed = await _tryRefreshToken();
      
      if (refreshed) {
        // Retry the original request with new token
        final clonedRequest = await _retryRequest(err.requestOptions);
        return handler.resolve(clonedRequest);
      } else {
        // Refresh failed, clear tokens and redirect to login
        await _clearTokensAndRedirect();
      }
    }
    
    handler.next(err);
  }
  
  /// Check if authentication should be skipped for this endpoint
  bool _shouldSkipAuth(String path) {
    final skipAuthPaths = [
      ApiConstants.login,
      ApiConstants.refreshToken,
      ApiConstants.forgotPassword,
      ApiConstants.resetPassword,
      ApiConstants.health,
      ApiConstants.version,
    ];
    
    return skipAuthPaths.any((skipPath) => path.contains(skipPath));
  }
  
  /// Attempt to refresh the access token
  Future<bool> _tryRefreshToken() async {
    try {
      final prefs = _getIt<SharedPreferences>();
      final refreshToken = prefs.getString(AppConstants.refreshTokenKey);
      
      if (refreshToken == null || refreshToken.isEmpty) {
        return false;
      }
      
      final dio = Dio();
      final response = await dio.post(
        '${ApiConstants.fullBaseUrl}${ApiConstants.refreshToken}',
        data: {'refresh_token': refreshToken},
        options: Options(
          headers: {
            ApiConstants.contentTypeHeader: ApiConstants.contentTypeJson,
            ApiConstants.acceptHeader: ApiConstants.contentTypeJson,
          },
        ),
      );
      
      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final newAccessToken = data['access_token'] as String?;
        final newRefreshToken = data['refresh_token'] as String?;
        
        if (newAccessToken != null) {
          await prefs.setString(AppConstants.accessTokenKey, newAccessToken);
          
          if (newRefreshToken != null) {
            await prefs.setString(AppConstants.refreshTokenKey, newRefreshToken);
          }
          
          return true;
        }
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Retry the original request with the new token
  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    try {
      final prefs = _getIt<SharedPreferences>();
      final accessToken = prefs.getString(AppConstants.accessTokenKey);
      
      if (accessToken != null) {
        requestOptions.headers[ApiConstants.authorizationHeader] = 
            '${ApiConstants.bearerPrefix}$accessToken';
      }
      
      final dio = Dio();
      return await dio.fetch(requestOptions);
    } catch (e) {
      rethrow;
    }
  }
  
  /// Clear tokens and redirect to login
  Future<void> _clearTokensAndRedirect() async {
    try {
      final prefs = _getIt<SharedPreferences>();
      await prefs.remove(AppConstants.accessTokenKey);
      await prefs.remove(AppConstants.refreshTokenKey);
      await prefs.remove(AppConstants.userDataKey);
      
      // TODO: Navigate to login screen
      // This should be handled by the app's navigation logic
      // You might want to use a navigation service or event bus
    } catch (e) {
      // Handle error silently
    }
  }
}
