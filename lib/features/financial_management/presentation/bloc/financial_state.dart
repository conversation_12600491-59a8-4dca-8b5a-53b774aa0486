part of 'financial_bloc.dart';


/// Base class for all financial states
abstract class FinancialState extends Equatable {
  const FinancialState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class FinancialInitial extends FinancialState {
  const FinancialInitial();
}

/// Loading state
class FinancialLoading extends FinancialState {
  const FinancialLoading();
}

/// Error state
class FinancialError extends FinancialState {
  final String message;

  const FinancialError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Financial summary loaded state
class FinancialSummaryLoaded extends FinancialState {
  final FinancialSummary summary;

  const FinancialSummaryLoaded(this.summary);

  @override
  List<Object?> get props => [summary];
}

/// Profit and loss data loaded state
class ProfitLossDataLoaded extends FinancialState {
  final List<ProfitLossData> data;

  const ProfitLossDataLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Budget overview loaded state
class BudgetOverviewLoaded extends FinancialState {
  final BudgetOverview overview;

  const BudgetOverviewLoaded(this.overview);

  @override
  List<Object?> get props => [overview];
}

/// Financial KPIs loaded state
class FinancialKPIsLoaded extends FinancialState {
  final List<FinancialKPI> kpis;

  const FinancialKPIsLoaded(this.kpis);

  @override
  List<Object?> get props => [kpis];
}

/// Expense breakdown loaded state
class ExpenseBreakdownLoaded extends FinancialState {
  final ExpenseBreakdown breakdown;

  const ExpenseBreakdownLoaded(this.breakdown);

  @override
  List<Object?> get props => [breakdown];
}

/// Revenue analysis loaded state
class RevenueAnalysisLoaded extends FinancialState {
  final RevenueAnalysis analysis;

  const RevenueAnalysisLoaded(this.analysis);

  @override
  List<Object?> get props => [analysis];
}

/// Budget created state
class BudgetCreated extends FinancialState {
  final Budget budget;

  const BudgetCreated(this.budget);

  @override
  List<Object?> get props => [budget];
}

/// Budget updated state
class BudgetUpdated extends FinancialState {
  final Budget budget;

  const BudgetUpdated(this.budget);

  @override
  List<Object?> get props => [budget];
}

/// Budget deleted state
class BudgetDeleted extends FinancialState {
  final String budgetId;

  const BudgetDeleted(this.budgetId);

  @override
  List<Object?> get props => [budgetId];
}

/// Financial reports loaded state
class FinancialReportsLoaded extends FinancialState {
  final List<FinancialReport> reports;

  const FinancialReportsLoaded(this.reports);

  @override
  List<Object?> get props => [reports];
}

/// Financial data exported state
class FinancialDataExported extends FinancialState {
  final String filePath;
  final ExportFormat format;

  const FinancialDataExported({
    required this.filePath,
    required this.format,
  });

  @override
  List<Object?> get props => [filePath, format];
}

/// Single financial transaction loaded state
class FinancialTransactionLoaded extends FinancialState {
  final FinancialTransaction transaction;

  const FinancialTransactionLoaded(this.transaction);

  @override
  List<Object?> get props => [transaction];
}

/// Financial transactions loaded state
class FinancialTransactionsLoaded extends FinancialState {
  final List<FinancialTransaction> transactions;

  const FinancialTransactionsLoaded(this.transactions);

  @override
  List<Object?> get props => [transactions];
}

/// Financial budgets loaded state
class FinancialBudgetsLoaded extends FinancialState {
  final List<Budget> budgets;

  const FinancialBudgetsLoaded(this.budgets);

  @override
  List<Object?> get props => [budgets];
}

/// Single cost center loaded state
class FinancialCostCenterLoaded extends FinancialState {
  final CostCenter costCenter;

  const FinancialCostCenterLoaded(this.costCenter);

  @override
  List<Object?> get props => [costCenter];
}

/// Cost centers loaded state
class FinancialCostCentersLoaded extends FinancialState {
  final List<CostCenter> costCenters;

  const FinancialCostCentersLoaded(this.costCenters);

  @override
  List<Object?> get props => [costCenters];
}

/// Cost center deleted state
class FinancialCostCenterDeleted extends FinancialState {
  final String costCenterId;

  const FinancialCostCenterDeleted(this.costCenterId);

  @override
  List<Object?> get props => [costCenterId];
}

/// Transaction updated state
class FinancialTransactionUpdated extends FinancialState {
  final FinancialTransaction transaction;

  const FinancialTransactionUpdated(this.transaction);

  @override
  List<Object?> get props => [transaction];
}

/// Transaction deleted state
class FinancialTransactionDeleted extends FinancialState {
  final String transactionId;

  const FinancialTransactionDeleted(this.transactionId);

  @override
  List<Object?> get props => [transactionId];
}

/// Transaction approved state
class FinancialTransactionApproved extends FinancialState {
  final FinancialTransaction transaction;

  const FinancialTransactionApproved(this.transaction);

  @override
  List<Object?> get props => [transaction];
}

/// Transaction rejected state
class FinancialTransactionRejected extends FinancialState {
  final FinancialTransaction transaction;

  const FinancialTransactionRejected(this.transaction);

  @override
  List<Object?> get props => [transaction];
}

/// Transactions searched state
class FinancialTransactionsSearched extends FinancialState {
  final List<FinancialTransaction> transactions;
  final String query;

  const FinancialTransactionsSearched(this.transactions, this.query);

  @override
  List<Object?> get props => [transactions, query];
}

/// Single budget loaded state
class FinancialBudgetLoaded extends FinancialState {
  final Budget budget;

  const FinancialBudgetLoaded(this.budget);

  @override
  List<Object?> get props => [budget];
}

/// Budget created state
class FinancialBudgetCreated extends FinancialState {
  final Budget budget;

  const FinancialBudgetCreated(this.budget);

  @override
  List<Object?> get props => [budget];
}

/// Budget updated state
class FinancialBudgetUpdated extends FinancialState {
  final Budget budget;

  const FinancialBudgetUpdated(this.budget);

  @override
  List<Object?> get props => [budget];
}

/// Budget deleted state
class FinancialBudgetDeleted extends FinancialState {
  final String budgetId;

  const FinancialBudgetDeleted(this.budgetId);

  @override
  List<Object?> get props => [budgetId];
}

/// Budget approved state
class FinancialBudgetApproved extends FinancialState {
  final Budget budget;

  const FinancialBudgetApproved(this.budget);

  @override
  List<Object?> get props => [budget];
}

/// Budget rejected state
class FinancialBudgetRejected extends FinancialState {
  final Budget budget;

  const FinancialBudgetRejected(this.budget);

  @override
  List<Object?> get props => [budget];
}

/// Budgets searched state
class FinancialBudgetsSearched extends FinancialState {
  final List<Budget> budgets;
  final String query;

  const FinancialBudgetsSearched(this.budgets, this.query);

  @override
  List<Object?> get props => [budgets, query];
}

/// Report generated state
class FinancialReportGenerated extends FinancialState {
  final FinancialReport report;

  const FinancialReportGenerated(this.report);

  @override
  List<Object?> get props => [report];
}

/// Profit and loss statement loaded state
class FinancialProfitLossStatementLoaded extends FinancialState {
  final ProfitLossStatement statement;

  const FinancialProfitLossStatementLoaded(this.statement);

  @override
  List<Object?> get props => [statement];
}

/// Cost analysis report loaded state
class FinancialCostAnalysisReportLoaded extends FinancialState {
  final CostAnalysisReport report;

  const FinancialCostAnalysisReportLoaded(this.report);

  @override
  List<Object?> get props => [report];
}

/// Budget variance report loaded state
class FinancialBudgetVarianceReportLoaded extends FinancialState {
  final BudgetVarianceReport report;

  const FinancialBudgetVarianceReportLoaded(this.report);

  @override
  List<Object?> get props => [report];
}

/// Cost trends loaded state
class FinancialCostTrendsLoaded extends FinancialState {
  final List<CostTrend> trends;

  const FinancialCostTrendsLoaded(this.trends);

  @override
  List<Object?> get props => [trends];
}

