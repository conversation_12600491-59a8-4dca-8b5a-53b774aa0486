import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/auth/providers/auth_provider.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/bundle.dart';
import '../../domain/entities/bundle_input.dart';
import '../../domain/entities/bundle_statistics.dart';
import '../../domain/usecases/bundling_usecases.dart';

part 'bundling_event.dart';
part 'bundling_state.dart';

/// BLoC for managing bundling operations
@injectable
class BundlingBloc extends Bloc<BundlingEvent, BundlingState> {
  final GetAllBundlesUseCase _getAllBundlesUseCase;
  final GetBundleByIdUseCase _getBundleByIdUseCase;
  final CreateBundlesUseCase _createBundlesUseCase;
  final UpdateBundleUseCase _updateBundleUseCase;
  final DeleteBundleUseCase _deleteBundleUseCase;
  final IssueBundleToSewingUseCase _issueBundleToSewingUseCase;
  final StartBundleWorkUseCase _startBundleWorkUseCase;
  final CompleteBundleWorkUseCase _completeBundleWorkUseCase;
  final CancelBundleUseCase _cancelBundleUseCase;
  final GetBundlesAssignedToUserUseCase _getBundlesAssignedToUserUseCase;
  final GetBundlesByStatusUseCase _getBundlesByStatusUseCase;
  final SearchBundlesUseCase _searchBundlesUseCase;
  final GetBundleStatisticsUseCase _getBundleStatisticsUseCase;
  final AuthProvider _authProvider;

  StreamSubscription<List<Bundle>>? _bundlesStreamSubscription;

  BundlingBloc(
    this._getAllBundlesUseCase,
    this._getBundleByIdUseCase,
    this._createBundlesUseCase,
    this._updateBundleUseCase,
    this._deleteBundleUseCase,
    this._issueBundleToSewingUseCase,
    this._startBundleWorkUseCase,
    this._completeBundleWorkUseCase,
    this._cancelBundleUseCase,
    this._getBundlesAssignedToUserUseCase,
    this._getBundlesByStatusUseCase,
    this._searchBundlesUseCase,
    this._getBundleStatisticsUseCase,
    this._authProvider,
  ) : super(const BundlingInitial()) {
    on<LoadBundles>(_onLoadBundles);
    on<LoadBundleById>(_onLoadBundleById);
    on<CreateBundles>(_onCreateBundles);
    on<UpdateBundle>(_onUpdateBundle);
    on<DeleteBundle>(_onDeleteBundle);
    on<IssueBundleToSewing>(_onIssueBundleToSewing);
    on<StartBundleWork>(_onStartBundleWork);
    on<CompleteBundleWork>(_onCompleteBundleWork);
    on<CancelBundle>(_onCancelBundle);
    on<SearchBundles>(_onSearchBundles);
    on<LoadBundleStatistics>(_onLoadBundleStatistics);
    on<LoadMyBundles>(_onLoadMyBundles);
    on<LoadBundlesByStatus>(_onLoadBundlesByStatus);
    on<RefreshBundles>(_onRefreshBundles);
    on<ValidateBundleInput>(_onValidateBundleInput);
    on<PreviewBundleCreation>(_onPreviewBundleCreation);
    on<ApplyBundleFilters>(_onApplyBundleFilters);
    on<ClearBundleFilters>(_onClearBundleFilters);
    on<ExportBundles>(_onExportBundles);
    on<SyncOfflineBundles>(_onSyncOfflineBundles);
    on<ResetBundlingState>(_onResetBundlingState);
  }

  @override
  Future<void> close() {
    _bundlesStreamSubscription?.cancel();
    return super.close();
  }

  /// Handle load bundles event
  Future<void> _onLoadBundles(LoadBundles event, Emitter<BundlingState> emit) async {
    emit(const BundlingLoading());

    final params = GetAllBundlesParams(
      status: event.status,
      designNo: event.designNo,
      lotNo: event.lotNo,
      rollNo: event.rollNo,
      altNo: event.altNo,
      size: event.size,
      assignedTo: event.assignedTo,
      startDate: event.startDate,
      endDate: event.endDate,
      limit: event.limit,
      offset: event.offset,
    );

    final result = await _getAllBundlesUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundles) => emit(BundlesLoaded(
        bundles: bundles,
        currentFilter: event.status,
      )),
    );
  }

  /// Handle load bundle by ID event
  Future<void> _onLoadBundleById(LoadBundleById event, Emitter<BundlingState> emit) async {
    emit(const BundlingLoading());

    final result = await _getBundleByIdUseCase(event.bundleId);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundle) => emit(BundleLoaded(bundle)),
    );
  }

  /// Handle create bundles event
  Future<void> _onCreateBundles(CreateBundles event, Emitter<BundlingState> emit) async {
    emit(const BundlingOperationInProgress(
      operation: 'Creating bundles',
      message: 'Please wait while bundles are being created...',
    ));

    final input = BundleInput(
      designNo: event.designNo,
      lotNo: event.lotNo,
      rollNo: event.rollNo,
      altNo: event.altNo,
      size: event.size,
      totalPiecesAvailable: event.totalPiecesAvailable,
      piecesPerBundle: event.piecesPerBundle,
      color: event.color,
      orderNo: event.orderNo,
      clientName: event.clientName,
      remarks: event.remarks,
    );

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final params = CreateBundlesParams(
      input: input,
      createdBy: currentUser.uid,
    );

    final result = await _createBundlesUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundles) => emit(BundlesCreated(
        bundles: bundles,
        message: 'Successfully created ${bundles.length} bundles',
      )),
    );
  }

  /// Handle update bundle event
  Future<void> _onUpdateBundle(UpdateBundle event, Emitter<BundlingState> emit) async {
    emit(const BundlingOperationInProgress(
      operation: 'Updating bundle',
      message: 'Please wait while bundle is being updated...',
    ));

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final updatedBundle = event.bundle.copyWith(
      updatedAt: DateTime.now(),
      updatedBy: currentUser.uid,
    );

    final params = UpdateBundleParams(bundle: updatedBundle);
    final result = await _updateBundleUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundle) => emit(BundleUpdated(
        bundle: bundle,
        message: 'Bundle updated successfully',
      )),
    );
  }

  /// Handle delete bundle event
  Future<void> _onDeleteBundle(DeleteBundle event, Emitter<BundlingState> emit) async {
    emit(const BundlingOperationInProgress(
      operation: 'Deleting bundle',
      message: 'Please wait while bundle is being deleted...',
    ));

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final params = DeleteBundleParams(
      bundleId: event.bundleId,
      deletedBy: currentUser.uid,
    );

    final result = await _deleteBundleUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (_) => emit(BundleDeleted(
        bundleId: event.bundleId,
        message: 'Bundle deleted successfully',
      )),
    );
  }

  /// Handle issue bundle to sewing event
  Future<void> _onIssueBundleToSewing(
    IssueBundleToSewing event,
    Emitter<BundlingState> emit,
  ) async {
    emit(const BundlingOperationInProgress(
      operation: 'Issuing bundle to sewing',
      message: 'Please wait while bundle is being issued...',
    ));

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final params = IssueBundleParams(
      bundleId: event.bundleId,
      assignedTo: event.assignedTo,
      issuedBy: currentUser.uid,
    );

    final result = await _issueBundleToSewingUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundle) => emit(BundleIssuedToSewing(
        bundle: bundle,
        message: 'Bundle issued to sewing successfully',
      )),
    );
  }

  /// Handle start bundle work event
  Future<void> _onStartBundleWork(StartBundleWork event, Emitter<BundlingState> emit) async {
    emit(const BundlingOperationInProgress(
      operation: 'Starting bundle work',
      message: 'Please wait while bundle work is being started...',
    ));

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final params = StartBundleParams(
      bundleId: event.bundleId,
      startedBy: currentUser.uid,
    );

    final result = await _startBundleWorkUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundle) => emit(BundleWorkStarted(
        bundle: bundle,
        message: 'Bundle work started successfully',
      )),
    );
  }

  /// Handle complete bundle work event
  Future<void> _onCompleteBundleWork(
    CompleteBundleWork event,
    Emitter<BundlingState> emit,
  ) async {
    emit(const BundlingOperationInProgress(
      operation: 'Completing bundle work',
      message: 'Please wait while bundle work is being completed...',
    ));

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final params = CompleteBundleParams(
      bundleId: event.bundleId,
      completedBy: currentUser.uid,
      remarks: event.remarks,
    );

    final result = await _completeBundleWorkUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundle) => emit(BundleWorkCompleted(
        bundle: bundle,
        message: 'Bundle work completed successfully',
      )),
    );
  }

  /// Handle cancel bundle event
  Future<void> _onCancelBundle(CancelBundle event, Emitter<BundlingState> emit) async {
    emit(const BundlingOperationInProgress(
      operation: 'Cancelling bundle',
      message: 'Please wait while bundle is being cancelled...',
    ));

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final params = CancelBundleParams(
      bundleId: event.bundleId,
      cancelledBy: currentUser.uid,
      reason: event.reason,
    );

    final result = await _cancelBundleUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundle) => emit(BundleCancelled(
        bundle: bundle,
        message: 'Bundle cancelled successfully',
      )),
    );
  }

  /// Handle search bundles event
  Future<void> _onSearchBundles(SearchBundles event, Emitter<BundlingState> emit) async {
    emit(const BundlingLoading());

    final result = await _searchBundlesUseCase(event.query);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundles) => emit(BundleSearchResults(
        bundles: bundles,
        query: event.query,
      )),
    );
  }

  /// Handle load bundle statistics event
  Future<void> _onLoadBundleStatistics(
    LoadBundleStatistics event,
    Emitter<BundlingState> emit,
  ) async {
    emit(const BundlingLoading());

    final params = GetBundleStatisticsParams(
      fromDate: event.fromDate,
      toDate: event.toDate,
      designNo: event.designNo,
      size: event.size,
      status: event.status,
    );

    final result = await _getBundleStatisticsUseCase(params);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (statistics) => emit(BundleStatisticsLoaded(statistics)),
    );
  }

  /// Handle load my bundles event
  Future<void> _onLoadMyBundles(LoadMyBundles event, Emitter<BundlingState> emit) async {
    emit(const BundlingLoading());

    final currentUser = _authProvider.currentUser;
    if (currentUser == null) {
      emit(const BundlingPermissionError('User not authenticated'));
      return;
    }

    final result = await _getBundlesAssignedToUserUseCase(currentUser.uid);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundles) => emit(BundlesLoaded(bundles: bundles)),
    );
  }

  /// Handle load bundles by status event
  Future<void> _onLoadBundlesByStatus(
    LoadBundlesByStatus event,
    Emitter<BundlingState> emit,
  ) async {
    emit(const BundlingLoading());

    final result = await _getBundlesByStatusUseCase(event.status);
    result.fold(
      (failure) => emit(BundlingError(message: failure.message)),
      (bundles) => emit(BundlesLoaded(
        bundles: bundles,
        currentFilter: event.status,
      )),
    );
  }

  /// Handle refresh bundles event
  Future<void> _onRefreshBundles(RefreshBundles event, Emitter<BundlingState> emit) async {
    // Get current filter if any
    BundleStatus? currentFilter;
    if (state is BundlesLoaded) {
      currentFilter = (state as BundlesLoaded).currentFilter;
    }

    // Reload bundles with current filter
    add(LoadBundles(status: currentFilter));
  }

  /// Handle validate bundle input event
  Future<void> _onValidateBundleInput(
    ValidateBundleInput event,
    Emitter<BundlingState> emit,
  ) async {
    final input = BundleInput(
      designNo: event.designNo,
      lotNo: event.lotNo,
      rollNo: event.rollNo,
      altNo: event.altNo,
      size: event.size,
      totalPiecesAvailable: event.totalPiecesAvailable,
      piecesPerBundle: event.piecesPerBundle,
    );

    final isValid = input.isValid;
    final errors = input.validationErrors;

    emit(BundleInputValidated(
      isValid: isValid,
      errors: errors,
    ));
  }

  /// Handle preview bundle creation event
  Future<void> _onPreviewBundleCreation(
    PreviewBundleCreation event,
    Emitter<BundlingState> emit,
  ) async {
    final previews = event.input.generateBundlePreviews();
    emit(BundleCreationPreview(
      input: event.input,
      previews: previews,
    ));
  }

  /// Handle apply bundle filters event
  Future<void> _onApplyBundleFilters(
    ApplyBundleFilters event,
    Emitter<BundlingState> emit,
  ) async {
    emit(BundleFiltersApplied(
      status: event.status,
      designNo: event.designNo,
      lotNo: event.lotNo,
      rollNo: event.rollNo,
      size: event.size,
      startDate: event.startDate,
      endDate: event.endDate,
    ));

    // Load bundles with applied filters
    add(LoadBundles(
      status: event.status,
      designNo: event.designNo,
      lotNo: event.lotNo,
      rollNo: event.rollNo,
      size: event.size,
      startDate: event.startDate,
      endDate: event.endDate,
    ));
  }

  /// Handle clear bundle filters event
  Future<void> _onClearBundleFilters(
    ClearBundleFilters event,
    Emitter<BundlingState> emit,
  ) async {
    emit(const BundleFiltersCleared());

    // Load all bundles without filters
    add(const LoadBundles());
  }

  /// Handle export bundles event
  Future<void> _onExportBundles(ExportBundles event, Emitter<BundlingState> emit) async {
    emit(const BundlingOperationInProgress(
      operation: 'Exporting bundles',
      message: 'Please wait while bundles are being exported...',
    ));

    // This would be implemented with actual export functionality
    // For now, just emit a success state
    emit(BundleExportCompleted(
      filePath: '/path/to/exported/file.${event.format}',
      format: event.format,
      message: 'Bundles exported successfully',
    ));
  }

  /// Handle sync offline bundles event
  Future<void> _onSyncOfflineBundles(
    SyncOfflineBundles event,
    Emitter<BundlingState> emit,
  ) async {
    emit(const BundlingOperationInProgress(
      operation: 'Syncing offline bundles',
      message: 'Please wait while offline bundles are being synced...',
    ));

    // This would be implemented with actual sync functionality
    // For now, just emit a success state
    emit(const OfflineBundlesSynced(
      syncedCount: 0,
      message: 'All offline bundles synced successfully',
    ));
  }

  /// Handle reset bundling state event
  Future<void> _onResetBundlingState(
    ResetBundlingState event,
    Emitter<BundlingState> emit,
  ) async {
    emit(const BundlingInitial());
  }
}
