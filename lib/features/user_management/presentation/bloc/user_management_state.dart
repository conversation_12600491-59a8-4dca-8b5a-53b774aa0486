part of 'user_management_bloc.dart';

/// Base user management state
abstract class UserManagementState extends Equatable {
  const UserManagementState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class UserManagementInitial extends UserManagementState {
  const UserManagementInitial();
}

/// Loading state
class UserManagementLoading extends UserManagementState {
  const UserManagementLoading();
}

/// Error state
class UserManagementError extends UserManagementState {
  final String message;

  const UserManagementError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Users loaded state
class UsersLoaded extends UserManagementState {
  final List<User> users;
  final Pagination? pagination;
  final UserFilterCriteria? filter;

  const UsersLoaded({
    required this.users,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [users, pagination, filter];
}

/// User details loaded state
class UserDetailsLoaded extends UserManagementState {
  final User user;

  const UserDetailsLoaded(this.user);

  @override
  List<Object?> get props => [user];
}

/// User created state
class UserCreated extends UserManagementState {
  final User user;

  const UserCreated(this.user);

  @override
  List<Object?> get props => [user];
}

/// User updated state
class UserUpdated extends UserManagementState {
  final User user;

  const UserUpdated(this.user);

  @override
  List<Object?> get props => [user];
}

/// User deleted state
class UserDeleted extends UserManagementState {
  final String userId;

  const UserDeleted(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// User activated state
class UserActivated extends UserManagementState {
  final String userId;

  const UserActivated(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// User deactivated state
class UserDeactivated extends UserManagementState {
  final String userId;

  const UserDeactivated(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Role assigned state
class RoleAssigned extends UserManagementState {
  final String userId;
  final UserRole role;

  const RoleAssigned(this.userId, this.role);

  @override
  List<Object?> get props => [userId, role];
}

/// Permissions granted state
class PermissionsGranted extends UserManagementState {
  final String userId;
  final List<String> permissions;

  const PermissionsGranted(this.userId, this.permissions);

  @override
  List<Object?> get props => [userId, permissions];
}

/// Permissions revoked state
class PermissionsRevoked extends UserManagementState {
  final String userId;
  final List<String> permissions;

  const PermissionsRevoked(this.userId, this.permissions);

  @override
  List<Object?> get props => [userId, permissions];
}

/// Users searched state
class UsersSearched extends UserManagementState {
  final List<User> users;
  final String query;
  final Pagination? pagination;

  const UsersSearched({
    required this.users,
    required this.query,
    this.pagination,
  });

  @override
  List<Object?> get props => [users, query, pagination];
}

/// User statistics loaded state
class UserStatisticsLoaded extends UserManagementState {
  final UserStatistics statistics;

  const UserStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Available roles loaded state
class AvailableRolesLoaded extends UserManagementState {
  final List<RoleDefinition> roles;

  const AvailableRolesLoaded(this.roles);

  @override
  List<Object?> get props => [roles];
}

/// Available permissions loaded state
class AvailablePermissionsLoaded extends UserManagementState {
  final List<Permission> permissions;

  const AvailablePermissionsLoaded(this.permissions);

  @override
  List<Object?> get props => [permissions];
}

/// Users filtered state
class UsersFiltered extends UserManagementState {
  final List<User> users;
  final UserFilterCriteria filter;
  final Pagination? pagination;

  const UsersFiltered({
    required this.users,
    required this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [users, filter, pagination];
}

/// User password reset state
class UserPasswordReset extends UserManagementState {
  final String userId;

  const UserPasswordReset(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// User locked state
class UserLocked extends UserManagementState {
  final String userId;

  const UserLocked(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// User unlocked state
class UserUnlocked extends UserManagementState {
  final String userId;

  const UserUnlocked(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Bulk operation completed state
class BulkOperationCompleted extends UserManagementState {
  final String operation;
  final int affectedCount;

  const BulkOperationCompleted(this.operation, this.affectedCount);

  @override
  List<Object?> get props => [operation, affectedCount];
}

/// Export completed state
class ExportCompleted extends UserManagementState {
  final String filePath;
  final String format;

  const ExportCompleted(this.filePath, this.format);

  @override
  List<Object?> get props => [filePath, format];
}

/// Import completed state
class ImportCompleted extends UserManagementState {
  final Map<String, dynamic> result;

  const ImportCompleted(this.result);

  @override
  List<Object?> get props => [result];
}

/// User activity logs loaded state
class UserActivityLogsLoaded extends UserManagementState {
  final List<UserActivityLog> logs;
  final String userId;
  final Pagination? pagination;

  const UserActivityLogsLoaded({
    required this.logs,
    required this.userId,
    this.pagination,
  });

  @override
  List<Object?> get props => [logs, userId, pagination];
}
