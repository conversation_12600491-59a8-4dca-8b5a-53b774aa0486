import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../data/models/production_task_filter.dart';
import '../../domain/entities/production_entities.dart';
import '../../domain/entities/schedule_entities.dart';
import '../../data/repositories/production_repository_impl.dart';
import '../../domain/models/create_production_order_request.dart';
import '../../domain/models/update_production_order_request.dart';
import '../../domain/repositories/production_repository.dart';
import '../../domain/usecases/production_usecases.dart';

part 'production_event.dart';
part 'production_state.dart';

/// Production Bloc
@injectable
class ProductionBloc extends Bloc<ProductionEvent, ProductionState> {
  final GetProductionOrdersUseCase _getProductionOrdersUseCase;
  final GetProductionOrderByIdUseCase _getProductionOrderByIdUseCase;
  final CreateProductionOrderUseCase _createProductionOrderUseCase;
  final UpdateProductionOrderUseCase _updateProductionOrderUseCase;
  final UpdateProductionOrderStatusUseCase _updateProductionOrderStatusUseCase;
  final StartProductionOrderUseCase _startProductionOrderUseCase;
  final PauseProductionOrderUseCase _pauseProductionOrderUseCase;
  final ResumeProductionOrderUseCase _resumeProductionOrderUseCase;
  final CompleteProductionOrderUseCase _completeProductionOrderUseCase;
  final GetProductionTasksUseCase _getProductionTasksUseCase;
  final UpdateTaskStatusUseCase _updateTaskStatusUseCase;
  final AssignTaskToWorkersUseCase _assignTaskToWorkersUseCase;
  final UpdateTaskProgressUseCase _updateTaskProgressUseCase;
  final GetResourceAllocationsUseCase _getResourceAllocationsUseCase;
  final AllocateResourceUseCase _allocateResourceUseCase;
  final ReleaseResourceUseCase _releaseResourceUseCase;
  final GetProductionStatisticsUseCase _getProductionStatisticsUseCase;
  final GetProductionCapacityUseCase _getProductionCapacityUseCase;
  final GetProductionBottlenecksUseCase _getProductionBottlenecksUseCase;
  final GetOverdueProductionOrdersUseCase _getOverdueProductionOrdersUseCase;
  final GetUrgentProductionOrdersUseCase _getUrgentProductionOrdersUseCase;
  final SearchProductionOrdersUseCase _searchProductionOrdersUseCase;

  ProductionBloc(
    this._getProductionOrdersUseCase,
    this._getProductionOrderByIdUseCase,
    this._createProductionOrderUseCase,
    this._updateProductionOrderUseCase,
    this._updateProductionOrderStatusUseCase,
    this._startProductionOrderUseCase,
    this._pauseProductionOrderUseCase,
    this._resumeProductionOrderUseCase,
    this._completeProductionOrderUseCase,
    this._getProductionTasksUseCase,
    this._updateTaskStatusUseCase,
    this._assignTaskToWorkersUseCase,
    this._updateTaskProgressUseCase,
    this._getResourceAllocationsUseCase,
    this._allocateResourceUseCase,
    this._releaseResourceUseCase,
    this._getProductionStatisticsUseCase,
    this._getProductionCapacityUseCase,
    this._getProductionBottlenecksUseCase,
    this._getOverdueProductionOrdersUseCase,
    this._getUrgentProductionOrdersUseCase,
    this._searchProductionOrdersUseCase,
  ) : super(const ProductionInitial()) {
    on<LoadProductionOrdersRequested>(_onLoadProductionOrdersRequested);
    on<RefreshProductionOrdersRequested>(_onRefreshProductionOrdersRequested);
    on<LoadProductionOrderDetailsRequested>(_onLoadProductionOrderDetailsRequested);
    on<CreateProductionOrderRequested>(_onCreateProductionOrderRequested);
    on<UpdateProductionOrderRequested>(_onUpdateProductionOrderRequested);
    on<UpdateProductionOrderStatusRequested>(_onUpdateProductionOrderStatusRequested);
    on<StartProductionOrderRequested>(_onStartProductionOrderRequested);
    on<PauseProductionOrderRequested>(_onPauseProductionOrderRequested);
    on<ResumeProductionOrderRequested>(_onResumeProductionOrderRequested);
    on<CompleteProductionOrderRequested>(_onCompleteProductionOrderRequested);
    on<LoadProductionTasksRequested>(_onLoadProductionTasksRequested);
    on<UpdateTaskStatusRequested>(_onUpdateTaskStatusRequested);
    on<AssignTaskToWorkersRequested>(_onAssignTaskToWorkersRequested);
    on<UpdateTaskProgressRequested>(_onUpdateTaskProgressRequested);
    on<LoadResourceAllocationsRequested>(_onLoadResourceAllocationsRequested);
    on<AllocateResourceRequested>(_onAllocateResourceRequested);
    on<ReleaseResourceRequested>(_onReleaseResourceRequested);
    on<LoadProductionStatisticsRequested>(_onLoadProductionStatisticsRequested);
    on<LoadProductionCapacityRequested>(_onLoadProductionCapacityRequested);
    on<LoadProductionBottlenecksRequested>(_onLoadProductionBottlenecksRequested);
    on<LoadOverdueProductionOrdersRequested>(_onLoadOverdueProductionOrdersRequested);
    on<LoadUrgentProductionOrdersRequested>(_onLoadUrgentProductionOrdersRequested);
    on<SearchProductionOrdersRequested>(_onSearchProductionOrdersRequested);
    on<FilterProductionOrdersRequested>(_onFilterProductionOrdersRequested);
    on<DeleteProductionOrderRequested>(_onDeleteProductionOrderRequested);
    on<ClearProductionState>(_onClearProductionState);
  }

  Future<void> _onLoadProductionOrdersRequested(
    LoadProductionOrdersRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _getProductionOrdersUseCase(GetProductionOrdersParams(
      filter: event.filter?.toOrderFilter(),
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) => emit(ProductionOrdersLoaded(
        orders: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onRefreshProductionOrdersRequested(
    RefreshProductionOrdersRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final currentState = state;
    if (currentState is ProductionOrdersLoaded) {
      emit(currentState.copyWith(isRefreshing: true));

      final result = await _getProductionOrdersUseCase(GetProductionOrdersParams(
        filter: currentState.filter?.toOrderFilter(),
        pagination: const PaginationParams(page: 1, perPage: 20),
      ));

      result.fold(
        (failure) => emit(currentState.copyWith(isRefreshing: false)),
        (response) => emit(currentState.copyWith(
          orders: response.data ?? [],
          pagination: response.pagination,
          isRefreshing: false,
        )),
      );
    }
  }

  Future<void> _onLoadProductionOrderDetailsRequested(
    LoadProductionOrderDetailsRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _getProductionOrderByIdUseCase(IdParams(event.productionOrderId));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderDetailsLoaded(response.data!));
        } else {
          emit(const ProductionError('Production order not found'));
        }
      },
    );
  }

  Future<void> _onCreateProductionOrderRequested(
    CreateProductionOrderRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _createProductionOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderCreated(response.data!));
        } else {
          emit(const ProductionError('Failed to create production order'));
        }
      },
    );
  }

  Future<void> _onUpdateProductionOrderRequested(
    UpdateProductionOrderRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _updateProductionOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderUpdated(response.data!));
        } else {
          emit(const ProductionError('Failed to update production order'));
        }
      },
    );
  }

  Future<void> _onUpdateProductionOrderStatusRequested(
    UpdateProductionOrderStatusRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _updateProductionOrderStatusUseCase(UpdateProductionOrderStatusParams(
      event.productionOrderId,
      event.status as String,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderStatusUpdated(response.data!));
        } else {
          emit(const ProductionError('Failed to update production order status'));
        }
      },
    );
  }

  Future<void> _onStartProductionOrderRequested(
    StartProductionOrderRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _startProductionOrderUseCase(IdParams(event.productionOrderId));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderStarted(response.data!));
        } else {
          emit(const ProductionError('Failed to start production order'));
        }
      },
    );
  }

  Future<void> _onPauseProductionOrderRequested(
    PauseProductionOrderRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _pauseProductionOrderUseCase(PauseProductionOrderParams(
      event.productionOrderId,
      event.reason,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderPaused(response.data!));
        } else {
          emit(const ProductionError('Failed to pause production order'));
        }
      },
    );
  }

  Future<void> _onResumeProductionOrderRequested(
    ResumeProductionOrderRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _resumeProductionOrderUseCase(IdParams(event.productionOrderId));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderResumed(response.data!));
        } else {
          emit(const ProductionError('Failed to resume production order'));
        }
      },
    );
  }

  Future<void> _onCompleteProductionOrderRequested(
    CompleteProductionOrderRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _completeProductionOrderUseCase(IdParams(event.productionOrderId));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ProductionOrderCompleted(response.data!));
        } else {
          emit(const ProductionError('Failed to complete production order'));
        }
      },
    );
  }

  Future<void> _onLoadProductionTasksRequested(
    LoadProductionTasksRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    // Map presentation-level TaskFilterCriteria to repository-level ProductionTaskFilter
    final ProductionTaskFilter? repoFilter = event.filter == null
        ? null
        : ProductionTaskFilter(
            // Convert single status to list expected by repository filter
            statuses: event.filter!.status != null ? [event.filter!.status!] : null,
            // Map assigned worker ID to repository field
            assignedToUserIds:
                event.filter!.assignedWorker != null ? [event.filter!.assignedWorker!] : null,
            // Use department if provided
            department: event.filter!.department,
            // Map date range to start date window
            startDate: event.filter!.startDate,
            endDate: event.filter!.endDate,
            // Pass search query through
            searchQuery: event.filter!.searchQuery,
          );

    final result = await _getProductionTasksUseCase(GetProductionTasksParams(
      filter: repoFilter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) => emit(ProductionTasksLoaded(
        tasks: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onUpdateTaskStatusRequested(
    UpdateTaskStatusRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _updateTaskStatusUseCase(UpdateTaskStatusParams(
      event.taskId,
      event.status as String,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(TaskStatusUpdated(response.data!));
        } else {
          emit(const ProductionError('Failed to update task status'));
        }
      },
    );
  }

  Future<void> _onAssignTaskToWorkersRequested(
    AssignTaskToWorkersRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _assignTaskToWorkersUseCase(AssignTaskToWorkersParams(
      event.taskId,
      event.workerIds,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(TaskAssignedToWorkers(response.data!));
        } else {
          emit(const ProductionError('Failed to assign task to workers'));
        }
      },
    );
  }

  Future<void> _onUpdateTaskProgressRequested(
    UpdateTaskProgressRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _updateTaskProgressUseCase(UpdateTaskProgressParams(
      event.taskId,
      event.completionPercentage,
      notes: event.notes,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(TaskProgressUpdated(response.data!));
        } else {
          emit(const ProductionError('Failed to update task progress'));
        }
      },
    );
  }

  Future<void> _onLoadResourceAllocationsRequested(
    LoadResourceAllocationsRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _getResourceAllocationsUseCase(GetResourceAllocationsParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) => emit(ResourceAllocationsLoaded(
        allocations: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onAllocateResourceRequested(
    AllocateResourceRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _allocateResourceUseCase(event.request);

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) {
        if (response.data != null) {
          emit(ResourceAllocated(response.data!));
        } else {
          emit(const ProductionError('Failed to allocate resource'));
        }
      },
    );
  }

  Future<void> _onReleaseResourceRequested(
    ReleaseResourceRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _releaseResourceUseCase(ReleaseResourceParams(
      event.allocationId,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (success) => emit(ResourceReleased(event.allocationId)),
    );
  }

  Future<void> _onLoadProductionStatisticsRequested(
    LoadProductionStatisticsRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _getProductionStatisticsUseCase(GetProductionStatisticsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      departmentId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (statistics) => emit(ProductionStatisticsLoaded(statistics)),
    );
  }

  Future<void> _onLoadProductionCapacityRequested(
    LoadProductionCapacityRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _getProductionCapacityUseCase(GetProductionCapacityParams(
      date: event.date,
      departmentId: event.departmentId,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (capacity) => emit(ProductionCapacityLoaded(capacity)),
    );
  }

  Future<void> _onLoadProductionBottlenecksRequested(
    LoadProductionBottlenecksRequested event,
    Emitter<ProductionState> emit,
  ) async {
    final result = await _getProductionBottlenecksUseCase(GetProductionBottlenecksParams(
      startDate: event.startDate,
      endDate: event.endDate,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (bottlenecks) => emit(ProductionBottlenecksLoaded(bottlenecks)),
    );
  }

  Future<void> _onLoadOverdueProductionOrdersRequested(
    LoadOverdueProductionOrdersRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _getOverdueProductionOrdersUseCase(GetOverdueProductionOrdersParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) => emit(OverdueProductionOrdersLoaded(
        orders: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadUrgentProductionOrdersRequested(
    LoadUrgentProductionOrdersRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _getUrgentProductionOrdersUseCase(GetUrgentProductionOrdersParams(
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) => emit(UrgentProductionOrdersLoaded(
        orders: response.data ?? [],
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onSearchProductionOrdersRequested(
    SearchProductionOrdersRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _searchProductionOrdersUseCase(SearchProductionOrdersParams(
      event.query,
      filter: event.filter?.toOrderFilter(),
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) => emit(ProductionOrdersSearched(
        orders: response.data ?? [],
        pagination: response.pagination,
        query: event.query,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onFilterProductionOrdersRequested(
    FilterProductionOrdersRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    final result = await _getProductionOrdersUseCase(GetProductionOrdersParams(
      filter: event.filter.toOrderFilter(),
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(ProductionError(failure.message)),
      (response) => emit(ProductionOrdersFiltered(
        orders: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  void _onClearProductionState(
    ClearProductionState event,
    Emitter<ProductionState> emit,
  ) {
    emit(const ProductionInitial());
  }



  void _onDeleteProductionOrderRequested(
    DeleteProductionOrderRequested event,
    Emitter<ProductionState> emit,
  ) async {
    emit(const ProductionLoading());

    // Note: We need to add a delete use case or use the repository directly
    try {
      // For now, we'll use the repository directly
      final repository = GetIt.instance<ProductionRepository>();
      if (repository is ProductionRepositoryImpl) {
        final result = await repository.deleteProductionOrder(
          event.orderId,
          reason: event.reason,
        );

        result.fold(
          (failure) => emit(ProductionError(failure.message)),
          (response) {
            if (response.success) {
              emit(ProductionOrderDeleted(event.orderId));
              // Refresh the orders list
              add(const LoadProductionOrdersRequested());
            } else {
              emit(ProductionError(response.message ?? 'Failed to delete production order'));
            }
          },
        );
      } else {
        emit(const ProductionError('Delete operation not supported'));
      }
    } catch (e) {
      emit(ProductionError('Failed to delete production order: $e'));
    }
  }
}
