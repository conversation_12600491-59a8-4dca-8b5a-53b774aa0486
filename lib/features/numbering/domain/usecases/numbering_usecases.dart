import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/numbering_task.dart';
import '../repositories/numbering_repository.dart';

/// Get all numbering tasks use case
@injectable
class GetAllNumberingTasksUseCase implements UseCase<List<NumberingTask>, GetAllTasksParams> {
  final NumberingRepository _repository;

  const GetAllNumberingTasksUseCase(this._repository);

  @override
  Future<Either<Failure, List<NumberingTask>>> call(GetAllTasksParams params) async {
    return await _repository.getAllTasks(
      status: params.status,
      orderNo: params.orderNo,
      clientName: params.clientName,
      startDate: params.startDate,
      endDate: params.endDate,
      limit: params.limit,
      offset: params.offset,
    );
  }
}

/// Get numbering task by ID use case
@injectable
class GetNumberingTaskByIdUseCase implements UseCase<NumberingTask, String> {
  final NumberingRepository _repository;

  const GetNumberingTaskByIdUseCase(this._repository);

  @override
  Future<Either<Failure, NumberingTask>> call(String taskId) async {
    return await _repository.getTaskById(taskId);
  }
}

/// Create numbering task use case
@injectable
class CreateNumberingTaskUseCase implements UseCase<NumberingTask, CreateTaskParams> {
  final NumberingRepository _repository;

  const CreateNumberingTaskUseCase(this._repository);

  @override
  Future<Either<Failure, NumberingTask>> call(CreateTaskParams params) async {
    // Get default rate if not provided
    double ratePerPiece = params.ratePerPiece;
    if (ratePerPiece <= 0) {
      final defaultRateResult = await _repository.getDefaultRatePerPiece();
      ratePerPiece = defaultRateResult.fold(
        (failure) => 2.0, // Fallback default rate
        (rate) => rate,
      );
    }

    final task = NumberingTask(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      date: params.date,
      orderNo: params.orderNo,
      clientName: params.clientName,
      designNo: params.designNo,
      size: params.size,
      color: params.color,
      bundleNo: params.bundleNo,
      pieceStartNo: params.pieceStartNo,
      pieceEndNo: params.pieceEndNo,
      ratePerPiece: ratePerPiece,
      checkedBy: params.checkedBy,
      remarks: params.remarks,
      status: NumberingTaskStatus.pending,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: params.createdBy,
      updatedBy: params.createdBy,
    );

    return await _repository.createTask(task);
  }
}

/// Update numbering task use case
@injectable
class UpdateNumberingTaskUseCase implements UseCase<NumberingTask, UpdateTaskParams> {
  final NumberingRepository _repository;

  const UpdateNumberingTaskUseCase(this._repository);

  @override
  Future<Either<Failure, NumberingTask>> call(UpdateTaskParams params) async {
    // Get existing task first
    final existingTaskResult = await _repository.getTaskById(params.taskId);
    return existingTaskResult.fold(
      (failure) => Left(failure),
      (existingTask) async {
        final updatedTask = existingTask.copyWith(
          date: params.date,
          orderNo: params.orderNo,
          clientName: params.clientName,
          designNo: params.designNo,
          size: params.size,
          color: params.color,
          bundleNo: params.bundleNo,
          pieceStartNo: params.pieceStartNo,
          pieceEndNo: params.pieceEndNo,
          ratePerPiece: params.ratePerPiece,
          checkedBy: params.checkedBy,
          remarks: params.remarks,
          status: params.status,
          updatedAt: DateTime.now(),
          updatedBy: params.updatedBy,
        );

        return await _repository.updateTask(updatedTask);
      },
    );
  }
}

/// Delete numbering task use case
@injectable
class DeleteNumberingTaskUseCase implements UseCase<void, String> {
  final NumberingRepository _repository;

  const DeleteNumberingTaskUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(String taskId) async {
    return await _repository.deleteTask(taskId);
  }
}

/// Get new task use case (assign to user)
@injectable
class GetNewTaskUseCase implements UseCase<NumberingTask, String> {
  final NumberingRepository _repository;

  const GetNewTaskUseCase(this._repository);

  @override
  Future<Either<Failure, NumberingTask>> call(String userId) async {
    return await _repository.getNewTask(userId);
  }
}

/// Complete task use case
@injectable
class CompleteTaskUseCase implements UseCase<NumberingTask, CompleteTaskParams> {
  final NumberingRepository _repository;

  const CompleteTaskUseCase(this._repository);

  @override
  Future<Either<Failure, NumberingTask>> call(CompleteTaskParams params) async {
    return await _repository.completeTask(params.taskId, params.userId);
  }
}

/// Send task to Munda use case
@injectable
class SendTaskToMundaUseCase implements UseCase<NumberingTask, SendToMundaParams> {
  final NumberingRepository _repository;

  const SendTaskToMundaUseCase(this._repository);

  @override
  Future<Either<Failure, NumberingTask>> call(SendToMundaParams params) async {
    return await _repository.sendTaskToMunda(params.taskId, params.userId);
  }
}

/// Get task statistics use case
@injectable
class GetTaskStatisticsUseCase implements UseCase<NumberingTaskStatistics, GetStatisticsParams> {
  final NumberingRepository _repository;

  const GetTaskStatisticsUseCase(this._repository);

  @override
  Future<Either<Failure, NumberingTaskStatistics>> call(GetStatisticsParams params) async {
    return await _repository.getTaskStatistics(
      startDate: params.startDate,
      endDate: params.endDate,
      userId: params.userId,
    );
  }
}

/// Search tasks use case
@injectable
class SearchTasksUseCase implements UseCase<List<NumberingTask>, SearchTasksParams> {
  final NumberingRepository _repository;

  const SearchTasksUseCase(this._repository);

  @override
  Future<Either<Failure, List<NumberingTask>>> call(SearchTasksParams params) async {
    return await _repository.searchTasks(
      searchQuery: params.searchQuery,
      status: params.status,
      startDate: params.startDate,
      endDate: params.endDate,
      limit: params.limit,
      offset: params.offset,
    );
  }
}

// Parameter classes for use cases
class GetAllTasksParams {
  final NumberingTaskStatus? status;
  final String? orderNo;
  final String? clientName;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? limit;
  final int? offset;

  const GetAllTasksParams({
    this.status,
    this.orderNo,
    this.clientName,
    this.startDate,
    this.endDate,
    this.limit,
    this.offset,
  });
}

class CreateTaskParams {
  final DateTime date;
  final String orderNo;
  final String clientName;
  final String designNo;
  final GarmentSize size;
  final String color;
  final String bundleNo;
  final int pieceStartNo;
  final int pieceEndNo;
  final double ratePerPiece;
  final String checkedBy;
  final String remarks;
  final String createdBy;

  const CreateTaskParams({
    required this.date,
    required this.orderNo,
    required this.clientName,
    required this.designNo,
    required this.size,
    required this.color,
    required this.bundleNo,
    required this.pieceStartNo,
    required this.pieceEndNo,
    required this.ratePerPiece,
    required this.checkedBy,
    required this.remarks,
    required this.createdBy,
  });
}

class UpdateTaskParams {
  final String taskId;
  final DateTime? date;
  final String? orderNo;
  final String? clientName;
  final String? designNo;
  final GarmentSize? size;
  final String? color;
  final String? bundleNo;
  final int? pieceStartNo;
  final int? pieceEndNo;
  final double? ratePerPiece;
  final String? checkedBy;
  final String? remarks;
  final NumberingTaskStatus? status;
  final String updatedBy;

  const UpdateTaskParams({
    required this.taskId,
    this.date,
    this.orderNo,
    this.clientName,
    this.designNo,
    this.size,
    this.color,
    this.bundleNo,
    this.pieceStartNo,
    this.pieceEndNo,
    this.ratePerPiece,
    this.checkedBy,
    this.remarks,
    this.status,
    required this.updatedBy,
  });
}

class CompleteTaskParams {
  final String taskId;
  final String userId;

  const CompleteTaskParams({
    required this.taskId,
    required this.userId,
  });
}

class SendToMundaParams {
  final String taskId;
  final String userId;

  const SendToMundaParams({
    required this.taskId,
    required this.userId,
  });
}

class GetStatisticsParams {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? userId;

  const GetStatisticsParams({
    this.startDate,
    this.endDate,
    this.userId,
  });
}

class SearchTasksParams {
  final String? searchQuery;
  final NumberingTaskStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? limit;
  final int? offset;

  const SearchTasksParams({
    this.searchQuery,
    this.status,
    this.startDate,
    this.endDate,
    this.limit,
    this.offset,
  });
}
