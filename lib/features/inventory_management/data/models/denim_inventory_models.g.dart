// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'denim_inventory_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DenimTypeModel _$DenimTypeModelFromJson(Map<String, dynamic> json) =>
    DenimTypeModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      denimCode: json['denimCode'] as String,
      type: json['type'] as String,
      description: json['description'] as String,
      gsm: (json['gsm'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      supplierId: json['supplierId'] as String?,
      supplierName: json['supplierName'] as String?,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$DenimTypeModelToJson(DenimTypeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
      'denimCode': instance.denimCode,
      'type': instance.type,
      'description': instance.description,
      'gsm': instance.gsm,
      'width': instance.width,
      'supplierId': instance.supplierId,
      'supplierName': instance.supplierName,
      'isActive': instance.isActive,
    };

DenimRollModel _$DenimRollModelFromJson(Map<String, dynamic> json) =>
    DenimRollModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      rollId: json['rollId'] as String,
      denimTypeId: json['denimTypeId'] as String,
      denimCode: json['denimCode'] as String,
      denimType: json['denimType'] as String,
      color: json['color'] as String,
      width: (json['width'] as num).toDouble(),
      totalPurchasedQty: (json['totalPurchasedQty'] as num).toDouble(),
      returnedQty: (json['returnedQty'] as num?)?.toDouble() ?? 0.0,
      usedQty: (json['usedQty'] as num?)?.toDouble() ?? 0.0,
      balanceQty: (json['balanceQty'] as num).toDouble(),
      location: json['location'] as String?,
      status: $enumDecode(_$DenimRollStatusEnumMap, json['status']),
      unit: $enumDecodeNullable(_$UnitEnumMap, json['unit']) ?? Unit.meters,
      gsm: (json['gsm'] as num).toDouble(),
      supplierId: json['supplierId'] as String?,
      supplierName: json['supplierName'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$DenimRollModelToJson(DenimRollModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'rollId': instance.rollId,
      'denimTypeId': instance.denimTypeId,
      'denimCode': instance.denimCode,
      'denimType': instance.denimType,
      'color': instance.color,
      'width': instance.width,
      'totalPurchasedQty': instance.totalPurchasedQty,
      'returnedQty': instance.returnedQty,
      'usedQty': instance.usedQty,
      'balanceQty': instance.balanceQty,
      'location': instance.location,
      'status': _$DenimRollStatusEnumMap[instance.status]!,
      'gsm': instance.gsm,
      'supplierId': instance.supplierId,
      'supplierName': instance.supplierName,
      'unit': instance.unit,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
      'metadata': instance.metadata,
    };

const _$DenimRollStatusEnumMap = {
  DenimRollStatus.inStock: 'inStock',
  DenimRollStatus.sentToCutting: 'sentToCutting',
  DenimRollStatus.returned: 'returned',
  DenimRollStatus.damaged: 'damaged',
  DenimRollStatus.outOfStock: 'outOfStock',
};

const _$UnitEnumMap = {
  Unit.meters: 'meters',
  Unit.yards: 'yards',
};

DenimPurchaseRecordModel _$DenimPurchaseRecordModelFromJson(
        Map<String, dynamic> json) =>
    DenimPurchaseRecordModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      srNo: (json['srNo'] as num).toInt(),
      purchaseDate: DateTime.parse(json['purchaseDate'] as String),
      supplierName: json['supplierName'] as String,
      supplierId: json['supplierId'] as String?,
      rollId: json['rollId'] as String,
      denimTypeId: json['denimTypeId'] as String,
      denimType: json['denimType'] as String,
      color: json['color'] as String,
      width: (json['width'] as num).toDouble(),
      length: (json['length'] as num).toDouble(),
      unit: json['unit'] as String? ?? 'yards',
      gsm: (json['gsm'] as num).toDouble(),
      ratePerUnit: (json['ratePerUnit'] as num).toDouble(),
      totalCost: (json['totalCost'] as num).toDouble(),
      invoiceNo: json['invoiceNo'] as String,
      remarks: json['remarks'] as String?,
      status: $enumDecodeNullable(_$PurchaseStatusEnumMap, json['status']) ??
          PurchaseStatus.completed,
      driverName: json['driverName'] as String?,
      vehicleNumber: json['vehicleNumber'] as String?,
    );

Map<String, dynamic> _$DenimPurchaseRecordModelToJson(
        DenimPurchaseRecordModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
      'srNo': instance.srNo,
      'purchaseDate': instance.purchaseDate.toIso8601String(),
      'supplierName': instance.supplierName,
      'supplierId': instance.supplierId,
      'rollId': instance.rollId,
      'denimTypeId': instance.denimTypeId,
      'denimType': instance.denimType,
      'color': instance.color,
      'width': instance.width,
      'length': instance.length,
      'unit': instance.unit,
      'gsm': instance.gsm,
      'ratePerUnit': instance.ratePerUnit,
      'totalCost': instance.totalCost,
      'invoiceNo': instance.invoiceNo,
      'remarks': instance.remarks,
      'status': _$PurchaseStatusEnumMap[instance.status]!,
      'driverName': instance.driverName,
      'vehicleNumber': instance.vehicleNumber,
    };

const _$PurchaseStatusEnumMap = {
  PurchaseStatus.pending: 'pending',
  PurchaseStatus.completed: 'completed',
  PurchaseStatus.cancelled: 'cancelled',
};

DenimReturnRecordModel _$DenimReturnRecordModelFromJson(
        Map<String, dynamic> json) =>
    DenimReturnRecordModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      srNo: (json['srNo'] as num).toInt(),
      returnDate: DateTime.parse(json['returnDate'] as String),
      rollId: json['rollId'] as String,
      supplierName: json['supplierName'] as String,
      supplierId: json['supplierId'] as String?,
      reasonForReturn: json['reasonForReturn'] as String,
      quantityReturned: (json['quantityReturned'] as num).toDouble(),
      unit: json['unit'] as String? ?? 'yards',
      debitNoteNo: json['debitNoteNo'] as String,
      remarks: json['remarks'] as String?,
      status: $enumDecodeNullable(_$ReturnStatusEnumMap, json['status']) ??
          ReturnStatus.pending,
      refundAmount: (json['refundAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$DenimReturnRecordModelToJson(
        DenimReturnRecordModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
      'srNo': instance.srNo,
      'returnDate': instance.returnDate.toIso8601String(),
      'rollId': instance.rollId,
      'supplierName': instance.supplierName,
      'supplierId': instance.supplierId,
      'reasonForReturn': instance.reasonForReturn,
      'quantityReturned': instance.quantityReturned,
      'unit': instance.unit,
      'debitNoteNo': instance.debitNoteNo,
      'remarks': instance.remarks,
      'status': _$ReturnStatusEnumMap[instance.status]!,
      'refundAmount': instance.refundAmount,
    };

const _$ReturnStatusEnumMap = {
  ReturnStatus.pending: 'pending',
  ReturnStatus.approved: 'approved',
  ReturnStatus.rejected: 'rejected',
};
