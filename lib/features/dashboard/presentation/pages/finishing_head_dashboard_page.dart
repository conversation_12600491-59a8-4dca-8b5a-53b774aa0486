import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Finishing Head Dashboard Page - Finishing department management
class FinishingHeadDashboardPage extends StatelessWidget {
  const FinishingHeadDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Finishing Head Dashboard'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildFinishingHeadDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildFinishingHeadDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildFinishingOverview(),
          const SizedBox(height: 24),
          _buildFinishingActions(context),
          const SizedBox(height: 24),
          _buildProcessingStations(),
          const SizedBox(height: 24),
          _buildTeamPerformance(),
          const SizedBox(height: 24),
          _buildShippingQueue(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.green,
            Colors.green.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Finishing Head'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Finishing department management',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.check_circle,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildFinishingOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Finishing Department Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Active Stations',
              value: '6',
              icon: Icons.workspaces,
              color: Colors.blue,
            ),
            DashboardMetricCard(
              title: 'Team Members',
              value: '18',
              icon: Icons.people,
              color: Colors.green,
            ),
            DashboardMetricCard(
              title: 'Completed Today',
              value: '125',
              icon: Icons.check_circle,
              color: Colors.purple,
              trend: '+15%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Efficiency',
              value: '91%',
              icon: Icons.trending_up,
              color: Colors.orange,
              trend: '+4%',
              isPositiveTrend: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinishingActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Department Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Station Management',
              subtitle: 'Manage finishing stations',
              icon: Icons.workspaces,
              color: Colors.blue,
              onTap: () {
                // Navigate to station management
              },
            ),
            DashboardCard(
              title: 'Task Assignment',
              subtitle: 'Assign finishing tasks',
              icon: Icons.assignment_ind,
              color: Colors.green,
              onTap: () {
                // Navigate to task assignment
              },
            ),
            DashboardCard(
              title: 'Quality Control',
              subtitle: 'Final quality inspection',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () {
                // Navigate to quality control
              },
            ),
            DashboardCard(
              title: 'Packaging',
              subtitle: 'Manage packaging process',
              icon: Icons.inventory_2,
              color: Colors.purple,
              onTap: () {
                // Navigate to packaging
              },
            ),
            DashboardCard(
              title: 'Shipping Prep',
              subtitle: 'Prepare for shipping',
              icon: Icons.local_shipping,
              color: Colors.teal,
              onTap: () {
                // Navigate to shipping prep
              },
            ),
            DashboardCard(
              title: 'Reports',
              subtitle: 'View department reports',
              icon: Icons.analytics,
              color: Colors.indigo,
              onTap: () {
                // Navigate to reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProcessingStations() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.workspaces,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Processing Stations Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '6/6 Active',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStationItem('Pressing Station', 'Ironing & Pressing', '95%', 'Active', Colors.green),
            _buildStationItem('Trimming Station', 'Thread Trimming', '88%', 'Active', Colors.green),
            _buildStationItem('Inspection Station', 'Final Inspection', '92%', 'Active', Colors.green),
            _buildStationItem('Packaging Station', 'Product Packaging', '90%', 'Active', Colors.green),
            _buildStationItem('Labeling Station', 'Label Application', '85%', 'Active', Colors.green),
            _buildStationItem('Folding Station', 'Garment Folding', '93%', 'Active', Colors.green),
          ],
        ),
      ),
    );
  }

  Widget _buildStationItem(String station, String process, String efficiency, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              station,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              process,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              efficiency,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTeamPerformance() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.people,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Team Performance',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to detailed performance
                  },
                  child: const Text('View Details'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPerformanceCard('Operators', '15', '89%', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceCard('Supervisors', '3', '94%', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceCard('Avg. Efficiency', '91%', '+4%', Colors.purple),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(String title, String value, String subtitle, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 9,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildShippingQueue() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.local_shipping,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Ready for Shipping',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '12 Orders',
                    style: TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildShippingItem(
              'Order #1234',
              'Shirts - 100 pieces',
              'Ready',
              Colors.green,
              Icons.check_circle,
            ),
            _buildShippingItem(
              'Order #1235',
              'Trousers - 75 pieces',
              'Packaging',
              Colors.blue,
              Icons.inventory_2,
            ),
            _buildShippingItem(
              'Order #1236',
              'Dresses - 50 pieces',
              'Ready',
              Colors.green,
              Icons.check_circle,
            ),
            _buildShippingItem(
              'Order #1237',
              'Jackets - 25 pieces',
              'Final Check',
              Colors.orange,
              Icons.verified,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShippingItem(String order, String description, String status, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
