import 'package:equatable/equatable.dart';

/// Represents the login credentials for authentication
class LoginCredentials extends Equatable {
  /// The user's email address
  final String email;

  /// The user's password
  final String password;
  final bool? rememberMe;

  /// Creates a [LoginCredentials] instance
  const LoginCredentials({
    required this.email,
    required this.password,
    required this.rememberMe,
  });

  /// Creates an empty [LoginCredentials] instance
  factory LoginCredentials.empty() {
    return const LoginCredentials(email: '', password: '', rememberMe: false);
  }

  /// Creates a copy of this [LoginCredentials] with the given fields replaced
  LoginCredentials copyWith({
    String? email,
    String? password,
  }) {
    return LoginCredentials(
      email: email ?? this.email,
      password: password ?? this.password,
      rememberMe: rememberMe ?? this.rememberMe,
    );
  }

  /// Validates the login credentials
  bool get isValid => 
      email.isNotEmpty && 
      RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email) &&
      password.isNotEmpty &&
      password.length >= 6;

  @override
  List<Object> get props => [email, password];

  /// Converts the login credentials to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'remember_me': rememberMe,
    };
  }

  @override
  String toString() => 'LoginCredentials(email: $email, password: ********)';
}
