import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

/// Base entity class for all domain models
abstract class BaseEntity extends Equatable {
  /// Unique identifier
  final String id;
  
  /// Creation timestamp
  final DateTime createdAt;
  
  /// Last update timestamp
  final DateTime updatedAt;
  
  /// Soft delete timestamp (null if not deleted)
  final DateTime? deletedAt;

  const BaseEntity({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Check if entity is deleted
  bool get isDeleted => deletedAt != null;

  /// Check if entity is active (not deleted)
  bool get isActive => deletedAt == null;

  /// Get age of entity
  Duration get age => DateTime.now().difference(createdAt);

  /// Get time since last update
  Duration get timeSinceUpdate => DateTime.now().difference(updatedAt);

  @override
  List<Object?> get props => [id, createdAt, updatedAt, deletedAt];
}

/// Base entity with audit information
abstract class AuditableEntity extends BaseEntity {
  /// User who created the entity
  final String? createdBy;
  
  /// User who last updated the entity
  final String? updatedBy;
  
  /// User who deleted the entity
  final String? deletedBy;
  
  /// Version number for optimistic locking
  final int version;

  const AuditableEntity({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    this.createdBy,
    this.updatedBy,
    this.deletedBy,
    this.version = 1,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        createdBy,
        updatedBy,
        deletedBy,
        version,
      ];
}

/// Base model class for API DTOs
abstract class BaseModel extends Equatable {
  const BaseModel();

  /// Convert to JSON
  Map<String, dynamic> toJson();

  /// Create copy with updated fields
  BaseModel copyWith();
}

/// Base class for value objects
abstract class ValueObject<T> extends Equatable {
  final T value;

  const ValueObject(this.value);

  @override
  List<Object?> get props => [value];

  @override
  String toString() => value.toString();
}

/// Base class for identifiers
class Id extends ValueObject<String> {
  const Id(String value) : super(value);

  /// Create a new unique ID
  factory Id.generate() {
    // This would typically use UUID package
    return Id(DateTime.now().millisecondsSinceEpoch.toString());
  }

  /// Create from string
  factory Id.fromString(String value) {
    if (value.isEmpty) {
      throw ArgumentError('ID cannot be empty');
    }
    return Id(value);
  }

  /// Check if ID is valid
  bool get isValid => value.isNotEmpty;
}

/// Base class for enums with string values
abstract class StringEnum extends Equatable {
  final String value;

  const StringEnum(this.value);

  @override
  List<Object?> get props => [value];

  @override
  String toString() => value;
}

/// Base class for status enums
abstract class StatusEnum extends StringEnum {
  const StatusEnum(String value) : super(value);

  /// Check if status is active/positive
  bool get isActive;

  /// Check if status is inactive/negative
  bool get isInactive => !isActive;

  /// Get display name for the status
  String get displayName;

  /// Get color associated with the status
  String get colorCode;
}

/// Base class for priority enums
abstract class PriorityEnum extends StringEnum {
  const PriorityEnum(String value) : super(value);

  /// Get priority level (higher number = higher priority)
  int get level;

  /// Check if this is high priority
  bool get isHigh;

  /// Check if this is low priority
  bool get isLow;

  /// Get display name for the priority
  String get displayName;

  /// Get color associated with the priority
  String get colorCode;
}

/// Base class for department enums
abstract class DepartmentEnum extends StringEnum {
  const DepartmentEnum(String value) : super(value);

  /// Get department display name
  String get displayName;

  /// Get department description
  String get description;

  /// Get department color code
  String get colorCode;

  /// Get department icon name
  String get iconName;
}

/// Base class for role enums
abstract class RoleEnum extends StringEnum {
  const RoleEnum(String value) : super(value);

  /// Get role display name
  String get displayName;

  /// Get role description
  String get description;

  /// Get list of permissions for this role
  List<String> get permissions;

  /// Check if role has specific permission
  bool hasPermission(String permission) => permissions.contains(permission);

  /// Get role hierarchy level (higher number = higher authority)
  int get hierarchyLevel;
}

/// Base class for permission enums
abstract class PermissionEnum extends StringEnum {
  const PermissionEnum(String value) : super(value);

  /// Get permission display name
  String get displayName;

  /// Get permission description
  String get description;

  /// Get permission category
  String get category;

  /// Check if this is a read permission
  bool get isRead;

  /// Check if this is a write permission
  bool get isWrite;

  /// Check if this is a delete permission
  bool get isDelete;

  /// Check if this is an admin permission
  bool get isAdmin;
}

/// Base class for measurement units
abstract class UnitEnum extends StringEnum {
  const UnitEnum(String value) : super(value);

  /// Get unit display name
  String get displayName;

  /// Get unit symbol
  String get symbol;

  /// Get unit type (length, weight, volume, etc.)
  String get unitType;

  /// Convert to base unit
  double toBaseUnit(double value);

  /// Convert from base unit
  double fromBaseUnit(double value);
}

/// Base class for currency enums
abstract class CurrencyEnum extends StringEnum {
  const CurrencyEnum(String value) : super(value);

  /// Get currency display name
  String get displayName;

  /// Get currency symbol
  String get symbol;

  /// Get currency code (ISO 4217)
  String get code;

  /// Get number of decimal places
  int get decimalPlaces;
}

/// Mixin for entities that can be archived
mixin ArchivableMixin {
  DateTime? get archivedAt;
  String? get archivedBy;

  bool get isArchived => archivedAt != null;
  bool get isNotArchived => archivedAt == null;
}

/// Mixin for entities that can be favorited
mixin FavoritableMixin {
  bool get isFavorite;
  DateTime? get favoritedAt;
  String? get favoritedBy;
}

/// Mixin for entities with tags
mixin TaggableMixin {
  List<String> get tags;

  bool hasTag(String tag) => tags.contains(tag);
  bool hasAnyTag(List<String> searchTags) => 
      searchTags.any((tag) => tags.contains(tag));
  bool hasAllTags(List<String> searchTags) => 
      searchTags.every((tag) => tags.contains(tag));
}

/// Mixin for entities with comments
mixin CommentableMixin {
  String? get comments;
  bool get hasComments => comments != null && comments!.isNotEmpty;
}

/// Mixin for entities with attachments
mixin AttachableMixin {
  List<String> get attachmentIds;
  bool get hasAttachments => attachmentIds.isNotEmpty;
  int get attachmentCount => attachmentIds.length;
}

/// Mixin for entities that can be assigned to users
mixin AssignableMixin {
  String? get assignedTo;
  DateTime? get assignedAt;
  String? get assignedBy;

  bool get isAssigned => assignedTo != null;
  bool get isUnassigned => assignedTo == null;
}

/// Mixin for entities with location information
mixin LocationMixin {
  String? get location;
  double? get latitude;
  double? get longitude;

  bool get hasLocation => location != null;
  bool get hasCoordinates => latitude != null && longitude != null;
}
