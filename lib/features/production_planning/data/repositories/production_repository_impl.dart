import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/features/manufacturing/domain/entities/manufacturing_entities.dart';
import 'package:hm_collection/features/production_planning/domain/entities/production_entities.dart';
import 'package:hm_collection/features/production_planning/domain/models/requests/allocate_resource_request.dart' hide AllocateResourceRequest;
import 'package:hm_collection/features/resource_management/domain/entities/facility_entities.dart';
import 'package:hm_collection/shared/enums/common_enums.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/shared/models/pagination.dart';
import '../../domain/models/create_production_order_request.dart';
import '../../domain/models/update_production_order_request.dart';
import '../../domain/repositories/production_repository.dart';
import '../../domain/usecases/production_usecases.dart';
import '../models/production_order_filter.dart';
import '../models/production_order_model.dart';

class ProductionRepositoryImpl implements ProductionRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  static const String _collectionName = 'production_orders';

  ProductionRepositoryImpl(this._firestore, this._auth);

  @override
  Future<Either<Failure, ApiResponse<ResourceAllocation>>> allocateResource(
      AllocateResourceRequest request) async {
    try {
      // Get the production order ID for the task
      final taskDoc = await _firestore.collection('productionTasks').doc(request.taskId).get();
      if (!taskDoc.exists) {
        return const Left(NotFoundFailure('Task not found'));
      }
      final productionOrderId = taskDoc.data()?['productionOrderId'] as String?;
      if (productionOrderId == null) {
        return const Left(ValidationFailure('Task is not associated with a production order'));
      }

      // Get resource details
      final resourceDoc = await _firestore.collection('resources').doc(request.resourceId).get();
      if (!resourceDoc.exists) {
        return const Left(NotFoundFailure('Resource not found'));
      }
      final resourceData = resourceDoc.data() as Map<String, dynamic>;
      
      final now = DateTime.now();
      final allocation = ResourceAllocation(
        id: _firestore.collection('resourceAllocations').doc().id,
        createdAt: now,
        updatedAt: now,
        productionOrderId: productionOrderId,
        taskId: request.taskId,
        resourceType: request.resourceType,
        resourceId: request.resourceId,
        resourceName: resourceData['name'] ?? 'Unnamed Resource',
        allocatedQuantity: request.quantity.toInt(),
        usedQuantity: 0,
        allocationDate: now,
        status: AllocationStatus.allocated,
        costPerUnit: (resourceData['costPerUnit'] as num?)?.toDouble() ?? 0.0,
        totalCost: (resourceData['costPerUnit'] as num? ?? 0.0) * request.quantity,
        specifications: resourceData['specifications'] ?? {},
      );

      // Save to Firestore
      await _firestore
          .collection('resourceAllocations')
          .doc(allocation.id)
          .set(allocation.toJson());

      return Right(ApiResponse<ResourceAllocation>(
        success: true,
        message: 'Resource allocated successfully',
        data: allocation,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to allocate resource: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> createProductionOrder(
      CreateProductionOrderRequest request) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final now = DateTime.now();
      final docRef = _firestore.collection(_collectionName).doc();

      final orderNumber = await _generateProductionOrderNumber();

      final productionOrder = ProductionOrder(
        id: docRef.id,
        createdAt: now,
        updatedAt: now,
        productionOrderNumber: orderNumber,
        manufacturingOrderId: request.manufacturingOrderId,
        orderNumber: request.orderNumber,
        clientName: '', // Not in request, default to empty
        status: ProductionStatus.planned, // Default status
        priority: ProductionPriority.normal, // Default priority
        plannedStartDate: now, // Default to now
        plannedEndDate: request.targetDate, // from request
        tasks: [], // Default to empty list
        resourceAllocations: [], // Default to empty list
        metrics: const ProductionMetrics( // Default metrics
          plannedEfficiency: 1.0,
          actualEfficiency: 0.0,
          qualityScore: 100.0,
          costVariance: 0.0,
          scheduleVariance: 0.0,
          efficiency: 0.0,
          onTimeDelivery: 0.0,
          resourceUtilization: 0.0,
        ),
        notes: request.notes != null
            ? [
                ProductionNote(
                    id: '', // Embedded, so ID is not critical here
                    createdAt: now,
                    updatedAt: now,
                    productionOrderId: docRef.id,
                    userId: currentUser.uid,
                    userName: currentUser.displayName ?? currentUser.email ?? '',
                    content: request.notes!,
                    type: NoteType.general,
                    priority: NotePriority.normal)
              ]
            : [],
        metadata: {
          ...?request.customFields,
          'createdBy': currentUser.uid,
          'createdByEmail': currentUser.email,
          'productId': request.productId,
        },
        assignedSupervisor: null, // Not in request
        currentDepartment: DepartmentType.cutting, // Default department
        totalQuantity: request.quantity, // from request
        completedQuantity: 0,
      );

      final model = ProductionOrderModel.fromEntity(productionOrder);
      final data = model.toMap();
      data['createdBy'] = currentUser.uid;
      data['updatedBy'] = currentUser.uid;

      await docRef.set(data);

      return Right(ApiResponse(
        success: true,
        message: 'Production order created successfully',
        data: productionOrder,
      ));
    } on FirebaseException catch (e) {
      return Left(ServerFailure('Firebase error: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure('Failed to create production order: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ProductionBottleneck>>> getProductionBottlenecks(
      GetProductionBottlenecksParams params) {
    // TODO: implement getProductionBottlenecks
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ProductionCapacity>> getProductionCapacity(
      GetProductionCapacityParams params) {
    // TODO: implement getProductionCapacity
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> getProductionOrderById(
      String orderId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final doc = await _firestore.collection(_collectionName).doc(orderId).get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Production order not found'));
      }

      final data = doc.data()!;
      if (data['deletedAt'] != null) {
        return const Left(NotFoundFailure('Production order has been deleted'));
      }

      final order = ProductionOrderModel.fromFirestore(doc).toEntity();

      return Right(ApiResponse(
        success: true,
        message: 'Production order retrieved successfully',
        data: order,
      ));
    } on FirebaseException catch (e) {
      return Left(ServerFailure('Firebase error: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure('Failed to get production order: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> getProductionOrders({
    ProductionOrderFilter? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      Query<Map<String, dynamic>> query =
          _firestore.collection(_collectionName).where('deletedAt', isNull: true).orderBy('createdAt', descending: true);

      // Apply filters
      if (filter != null) {
        if (filter.statuses != null && filter.statuses!.isNotEmpty) {
          query = query.where('status', whereIn: filter.statuses!.map((s) => s.name).toList());
        }
        if (filter.department != null) {
          query = query.where('currentDepartment', isEqualTo: filter.department!.value);
        }
        if (filter.startDate != null) {
          query = query.where('plannedStartDate', isGreaterThanOrEqualTo: filter.startDate);
        }
        if (filter.endDate != null) {
          query = query.where('plannedEndDate', isLessThanOrEqualTo: filter.endDate);
        }
      }

      final totalCount = await _getTotalCount(filter);

      final int perPage = pagination?.perPage ?? 50;
      query = query.limit(perPage);

      // Note: For full pagination with page > 1, Firestore requires cursor-based
      // pagination using startAfter(lastDocument). This is a simplified implementation.

      final querySnapshot = await query.get();

      final orders = querySnapshot.docs.map((doc) => ProductionOrderModel.fromFirestore(doc).toEntity()).toList();

      return Right(ApiListResponse(
        success: true,
        message: 'Production orders retrieved successfully',
        data: orders,
        pagination: Pagination.create(
          currentPage: pagination?.page ?? 1,
          perPage: perPage,
          total: totalCount,
        ),
      ));
    } on FirebaseException catch (e) {
      return Left(ServerFailure('Firebase error: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure('Failed to get production orders: $e'));
    }
  }

  @override
  Future<Either<Failure, ProductionStatistics>> getProductionStatistics(
      GetProductionStatisticsParams params) {
    // TODO: implement getProductionStatistics
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiListResponse<ProductionTask>>> getProductionTasks(
      GetProductionTasksParams params) async {
    try {
      if (params.filter == null) {
        return const Left(ValidationFailure('Filter parameters are required'));
      }

      // Get the production order ID from the filter
      final productionOrderId = params.filter?.productionOrderId;
      if (productionOrderId == null || productionOrderId.isEmpty) {
        return const Left(ValidationFailure('productionOrderId is required'));
      }

      // Create a query against the Firestore collection
      Query query = _firestore
          .collection('productionTasks')
          .where('productionOrderId', isEqualTo: productionOrderId);

      // Apply status filter if provided
      if (params.filter!.statuses != null && params.filter!.statuses!.isNotEmpty) {
        query = query.where('status', whereIn: params.filter!.statuses!.map((e) => e.toString().split('.').last).toList());
      }

      // Apply department filter if provided
      if (params.filter!.department != null) {
        query = query.where('department', isEqualTo: params.filter!.department!.toString().split('.').last);
      }

      // Apply date range filter if provided
      if (params.filter!.startDate != null) {
        query = query.where('dueDate', isGreaterThanOrEqualTo: params.filter!.startDate);
      }
      if (params.filter!.endDate != null) {
        query = query.where('dueDate', isLessThanOrEqualTo: params.filter!.endDate);
      }

      // Apply sorting
      query = query.orderBy('dueDate');
      
      // Apply pagination if provided
      if (params.pagination != null) {
        final pagination = params.pagination!;
        
        // Apply limit
        query = query.limit(pagination.perPage);
        
        // Apply cursor-based pagination if lastDocumentId is provided
        if (pagination.lastDocumentId != null) {
          final lastDoc = await _firestore
              .collection('productionTasks')
              .doc(pagination.lastDocumentId)
              .get();
          query = query.startAfterDocument(lastDoc);
        }
        // Fallback to offset-based pagination if page is provided
        else if (pagination.page != null && pagination.page! > 1) {
          final previousPageQuery = query.limit(pagination.perPage * (pagination.page! - 1));
          final previousPageSnapshot = await previousPageQuery.get();
          final lastDoc = previousPageSnapshot.docs.lastOrNull;
          
          if (lastDoc != null) {
            query = query.startAfterDocument(lastDoc);
          }
        }
      }

      // Execute the query
      final querySnapshot = await query.get();
      
      // Get total count for pagination (without the limit for accurate total)
      final totalCount = (await query.limit(1000).count().get()).count;
      
      // Convert documents to ProductionTask objects
      final tasks = querySnapshot.docs
          .map((doc) => ProductionTask.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();

      return Right(ApiListResponse<ProductionTask>(
        success: true,
        data: tasks,
        pagination: Pagination(
          currentPage: params.pagination?.page ?? 1,
          perPage: params.pagination?.perPage ?? tasks.length,
          total: totalCount ?? 0, // Provide default value of 0 if totalCount is null
          totalPages: params.pagination != null 
              ? (totalCount! / params.pagination!.perPage).ceil()
              : 1,
          hasNextPage: params.pagination != null 
              ? (params.pagination!.page * params.pagination!.perPage) < totalCount!
              : false,
          hasPreviousPage: params.pagination?.page != null && params.pagination!.page > 1,
        ),
      ));
    } on FirebaseException catch (e) {
      return Left(ServerFailure('Failed to fetch production tasks: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<ResourceAllocation>>> getResourceAllocations(
      GetResourceAllocationsParams params) {
    // TODO: implement getResourceAllocations
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> releaseResource(ReleaseResourceParams params) {
    // TODO: implement releaseResource
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> searchProductionOrders(
      SearchProductionOrdersParams params) {
    // TODO: implement searchProductionOrders
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> updateProductionOrder(
      UpdateProductionOrderRequest request) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(request.id);

      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Production order not found'));
      }

      final existingData = doc.data()!;
      if (existingData['deletedAt'] != null) {
        return const Left(NotFoundFailure('Production order has been deleted'));
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': currentUser.uid,
      };

      if (request.clientName != null) updateData['clientName'] = request.clientName;
      if (request.status != null) updateData['status'] = request.status!.name;
      if (request.priority != null) updateData['priority'] = request.priority!.name;
      if (request.plannedStartDate != null) {
        updateData['plannedStartDate'] = Timestamp.fromDate(request.plannedStartDate!);
      }
      if (request.plannedEndDate != null) {
        updateData['plannedEndDate'] = Timestamp.fromDate(request.plannedEndDate!);
      }
      if (request.actualStartDate != null) {
        updateData['actualStartDate'] = Timestamp.fromDate(request.actualStartDate!);
      }
      if (request.actualEndDate != null) {
        updateData['actualEndDate'] = Timestamp.fromDate(request.actualEndDate!);
      }
      if (request.assignedSupervisor != null) {
        updateData['assignedSupervisor'] = request.assignedSupervisor;
      }
      if (request.currentDepartment != null) {
        updateData['currentDepartment'] = request.currentDepartment!.value;
      }
      if (request.completionPercentage != null) {
        updateData['completionPercentage'] = request.completionPercentage;
      }
      if (request.completedQuantity != null) {
        updateData['completedQuantity'] = request.completedQuantity;
      }
      if (request.metadata != null) {
        updateData['metadata'] = request.metadata;
      }

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      final updatedOrder = ProductionOrderModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse(
        success: true,
        message: 'Production order updated successfully',
        data: updatedOrder,
      ));
    } on FirebaseException catch (e) {
      return Left(ServerFailure('Firebase error: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure('Failed to update production order: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionTask>>> updateTaskProgress(
      UpdateTaskProgressParams params) {
    // TODO: implement updateTaskProgress
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionTask>>> updateTaskStatus(
      UpdateTaskStatusParams params) {
    // TODO: implement updateTaskStatus
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionTask>>> assignTaskToWorkers(
      AssignTaskToWorkersParams params) {
    // TODO: implement assignTaskToWorkers
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> completeProductionOrder(
      String orderId) {
    // TODO: implement completeProductionOrder
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> getOverdueProductionOrders(
      GetOverdueProductionOrdersParams params) {
    // TODO: implement getOverdueProductionOrders
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiListResponse<ProductionOrder>>> getUrgentProductionOrders(
      GetUrgentProductionOrdersParams params) {
    // TODO: implement getUrgentProductionOrders
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> pauseProductionOrder(
      PauseProductionOrderParams params) {
    // TODO: implement pauseProductionOrder
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> resumeProductionOrder(
      String orderId) async {
    return await _updateProductionOrderStatus(orderId, ProductionStatus.inProgress);
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> startProductionOrder(
      String orderId) async {
    return await _updateProductionOrderStatus(orderId, ProductionStatus.inProgress);
  }

  @override
  Future<Either<Failure, ApiResponse<bool>>> deleteProductionOrder(
      String orderId, {String? reason}) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(orderId);

      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Production order not found'));
      }

      final existingData = doc.data()!;
      if (existingData['deletedAt'] != null) {
        return const Left(ValidationFailure('Production order already deleted'));
      }

      await docRef.update({
        'deletedAt': Timestamp.fromDate(DateTime.now()),
        'deletedBy': currentUser.uid,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': currentUser.uid,
        'metadata.deleteReason': reason ?? 'No reason provided',
      });

      return const Right(ApiResponse(
        success: true,
        message: 'Production order deleted successfully',
        data: true,
      ));
    } on FirebaseException catch (e) {
      return Left(ServerFailure('Firebase error: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure('Failed to delete production order: $e'));
    }
  }

  Future<Either<Failure, ApiResponse<ProductionOrder>>> _updateProductionOrderStatus(
      String orderId, ProductionStatus status) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final docRef = _firestore.collection(_collectionName).doc(orderId);

      final updateData = <String, dynamic>{
        'status': status.name,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': currentUser.uid,
      };

      if (status == ProductionStatus.inProgress) {
        final doc = await docRef.get();
        if (doc.exists && doc.data()?['actualStartDate'] == null) {
          updateData['actualStartDate'] = Timestamp.fromDate(DateTime.now());
        }
      } else if (status == ProductionStatus.completed) {
        updateData['actualEndDate'] = Timestamp.fromDate(DateTime.now());
        updateData['completionPercentage'] = 100.0;
      }

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      if (!updatedDoc.exists) {
        return const Left(NotFoundFailure('Production order not found'));
      }

      final updatedOrder = ProductionOrderModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse(
        success: true,
        message: 'Production order status updated successfully',
        data: updatedOrder,
      ));
    } on FirebaseException catch (e) {
      return Left(ServerFailure('Firebase error: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure('Failed to update production order status: $e'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionOrder>>> updateProductionOrderStatus(
      UpdateProductionOrderStatusParams params) async {
    final status = ProductionStatus.values.firstWhere(
      (e) => e.name == params.status,
      orElse: () => ProductionStatus.planned,
    );
    return await _updateProductionOrderStatus(params.productionOrderId, status);
  }

  Future<String> _generateProductionOrderNumber() async {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');

    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final todayOrdersQuery = await _firestore
        .collection(_collectionName)
        .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
        .where('createdAt', isLessThan: Timestamp.fromDate(endOfDay))
        .get();

    final sequence = (todayOrdersQuery.docs.length + 1).toString().padLeft(3, '0');

    return 'PO$year$month$day$sequence';
  }

  Future<int> _getTotalCount(ProductionOrderFilter? filter) async {
    try {
      Query<Map<String, dynamic>> query =
          _firestore.collection(_collectionName).where('deletedAt', isNull: true);

      if (filter != null) {
        if (filter.statuses != null && filter.statuses!.isNotEmpty) {
          query = query.where('status', whereIn: filter.statuses!.map((s) => s.name).toList());
        }
        if (filter.department != null) {
          query = query.where('currentDepartment', isEqualTo: filter.department!.value);
        }
        if (filter.startDate != null) {
          query = query.where('plannedStartDate', isGreaterThanOrEqualTo: filter.startDate);
        }
        if (filter.endDate != null) {
          query = query.where('plannedEndDate', isLessThanOrEqualTo: filter.endDate);
        }
      }

      final snapshot = await query.get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Stream<List<ProductionOrder>> getProductionOrdersStream({
    ProductionOrderFilter? filter,
    int limit = 50,
  }) {
    Query<Map<String, dynamic>> query =
        _firestore.collection(_collectionName).where('deletedAt', isNull: true).orderBy('createdAt', descending: true).limit(limit);

    if (filter != null) {
      if (filter.statuses != null && filter.statuses!.isNotEmpty) {
        query = query.where('status', whereIn: filter.statuses!.map((s) => s.name).toList());
      }
      if (filter.department != null) {
        query = query.where('currentDepartment', isEqualTo: filter.department!.value);
      }
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => ProductionOrderModel.fromFirestore(doc).toEntity()).toList();
    });
  }

  Stream<ProductionOrder?> getProductionOrderStream(String orderId) {
    return _firestore.collection(_collectionName).doc(orderId).snapshots().map((doc) {
      if (!doc.exists) return null;

      final data = doc.data()!;
      if (data['deletedAt'] != null) return null;

      return ProductionOrderModel.fromFirestore(doc).toEntity();
    });
  }
}
