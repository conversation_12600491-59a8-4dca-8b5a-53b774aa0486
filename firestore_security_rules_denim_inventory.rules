// Firebase Firestore Security Rules for Denim Inventory Management
// These rules should be added to your main firestore.rules file

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions for authentication and authorization
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isInventoryManager() {
      return isAuthenticated() && 
             request.auth.token.role == 'inventory_manager';
    }
    
    function isAdminOrInventoryManager() {
      return isAuthenticated() && 
             (request.auth.token.role == 'admin' || 
              request.auth.token.role == 'inventory_manager');
    }
    
    function isOwnerOrInventoryManager(userId) {
      return isAuthenticated() && 
             (request.auth.uid == userId || 
              request.auth.token.role == 'inventory_manager' ||
              request.auth.token.role == 'admin');
    }
    
    function validateDenimTypeData() {
      return request.resource.data.keys().hasAll(['denimCode', 'type', 'description', 'gsm', 'width', 'isActive', 'createdAt', 'updatedAt']) &&
             request.resource.data.denimCode is string &&
             request.resource.data.type is string &&
             request.resource.data.description is string &&
             request.resource.data.gsm is number &&
             request.resource.data.width is number &&
             request.resource.data.isActive is bool &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.updatedAt is timestamp;
    }
    
    function validateDenimRollData() {
      return request.resource.data.keys().hasAll(['rollId', 'denimTypeId', 'denimCode', 'denimType', 'color', 'width', 'totalPurchasedQty', 'returnedQty', 'usedQty', 'balanceQty', 'status', 'gsm', 'unit', 'createdAt', 'updatedAt']) &&
             request.resource.data.rollId is string &&
             request.resource.data.denimTypeId is string &&
             request.resource.data.denimCode is string &&
             request.resource.data.denimType is string &&
             request.resource.data.color is string &&
             request.resource.data.width is number &&
             request.resource.data.totalPurchasedQty is number &&
             request.resource.data.returnedQty is number &&
             request.resource.data.usedQty is number &&
             request.resource.data.balanceQty is number &&
             request.resource.data.status is string &&
             request.resource.data.gsm is number &&
             request.resource.data.unit is string &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.updatedAt is timestamp;
    }
    
    function validatePurchaseRecordData() {
      return request.resource.data.keys().hasAll(['purchaseDate', 'supplierName', 'rollId', 'denimTypeId', 'quantity', 'unit', 'ratePerUnit', 'totalCost', 'status', 'createdAt', 'updatedAt']) &&
             request.resource.data.purchaseDate is timestamp &&
             request.resource.data.supplierName is string &&
             request.resource.data.rollId is string &&
             request.resource.data.denimTypeId is string &&
             request.resource.data.quantity is number &&
             request.resource.data.unit is string &&
             request.resource.data.ratePerUnit is number &&
             request.resource.data.totalCost is number &&
             request.resource.data.status is string &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.updatedAt is timestamp;
    }
    
    function validateReturnRecordData() {
      return request.resource.data.keys().hasAll(['returnDate', 'rollId', 'supplierName', 'reasonForReturn', 'quantityReturned', 'unit', 'status', 'createdAt', 'updatedAt']) &&
             request.resource.data.returnDate is timestamp &&
             request.resource.data.rollId is string &&
             request.resource.data.supplierName is string &&
             request.resource.data.reasonForReturn is string &&
             request.resource.data.quantityReturned is number &&
             request.resource.data.unit is string &&
             request.resource.data.status is string &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.updatedAt is timestamp;
    }
    
    // Denim Types Collection Rules
    match /denimTypes/{denimTypeId} {
      // Read: Allow authenticated users to read denim types
      allow read: if isAuthenticated();
      
      // Create: Only inventory managers and admins can create new denim types
      allow create: if isAdminOrInventoryManager() && 
                       validateDenimTypeData();
      
      // Update: Only inventory managers and admins can update denim types
      allow update: if isAdminOrInventoryManager() && 
                       validateDenimTypeData() &&
                       request.resource.data.createdAt == resource.data.createdAt;
      
      // Delete: Only admins can delete denim types
      allow delete: if isAuthenticated() && 
                       request.auth.token.role == 'admin';
    }
    
    // Denim Rolls Collection Rules
    match /denimRolls/{rollId} {
      // Read: Allow authenticated users to read denim rolls
      allow read: if isAuthenticated();
      
      // Create: Only inventory managers and admins can create new denim rolls
      allow create: if isAdminOrInventoryManager() && 
                       validateDenimRollData();
      
      // Update: Only inventory managers and admins can update denim rolls
      allow update: if isAdminOrInventoryManager() && 
                       validateDenimRollData() &&
                       request.resource.data.createdAt == resource.data.createdAt;
      
      // Delete: Only admins can delete denim rolls
      allow delete: if isAuthenticated() && 
                       request.auth.token.role == 'admin';
    }
    
    // Denim Purchase Records Collection Rules
    match /denimPurchaseRecords/{purchaseId} {
      // Read: Allow authenticated users to read purchase records
      allow read: if isAuthenticated();
      
      // Create: Only inventory managers and admins can create purchase records
      allow create: if isAdminOrInventoryManager() && 
                       validatePurchaseRecordData() &&
                       request.resource.data.createdBy == request.auth.uid;
      
      // Update: Only inventory managers and admins can update purchase records
      // Also allow the creator to update their own records
      allow update: if (isAdminOrInventoryManager() || 
                       request.auth.uid == resource.data.createdBy) && 
                       validatePurchaseRecordData() &&
                       request.resource.data.createdAt == resource.data.createdAt &&
                       request.resource.data.createdBy == resource.data.createdBy;
      
      // Delete: Only admins can delete purchase records
      allow delete: if isAuthenticated() && 
                       request.auth.token.role == 'admin';
    }
    
    // Denim Return Records Collection Rules
    match /denimReturnRecords/{returnId} {
      // Read: Allow authenticated users to read return records
      allow read: if isAuthenticated();
      
      // Create: Only inventory managers and admins can create return records
      allow create: if isAdminOrInventoryManager() && 
                       validateReturnRecordData() &&
                       request.resource.data.createdBy == request.auth.uid;
      
      // Update: Only inventory managers and admins can update return records
      // Also allow the creator to update their own records
      allow update: if (isAdminOrInventoryManager() || 
                       request.auth.uid == resource.data.createdBy) && 
                       validateReturnRecordData() &&
                       request.resource.data.createdAt == resource.data.createdAt &&
                       request.resource.data.createdBy == resource.data.createdBy;
      
      // Delete: Only admins can delete return records
      allow delete: if isAuthenticated() && 
                       request.auth.token.role == 'admin';
    }
    
    // Stock Movement Logs Collection Rules (for audit trail)
    match /stockMovementLogs/{logId} {
      // Read: Allow authenticated users to read stock movement logs
      allow read: if isAuthenticated();
      
      // Create: Only inventory managers and admins can create stock movement logs
      // These are typically created automatically by the system
      allow create: if isAdminOrInventoryManager() &&
                       request.resource.data.keys().hasAll(['rollId', 'movementType', 'quantity', 'previousBalance', 'newBalance', 'reason', 'createdBy', 'createdAt']) &&
                       request.resource.data.createdBy == request.auth.uid;
      
      // Update: Stock movement logs should be immutable once created
      allow update: if false;
      
      // Delete: Only admins can delete stock movement logs (for data cleanup)
      allow delete: if isAuthenticated() && 
                       request.auth.token.role == 'admin';
    }
    
    // Inventory Analytics Collection Rules (for dashboard metrics)
    match /inventoryAnalytics/{analyticsId} {
      // Read: Allow authenticated users to read analytics
      allow read: if isAuthenticated();
      
      // Create/Update: Only system processes should create/update analytics
      // In practice, these would be updated via Cloud Functions
      allow create, update: if isAdminOrInventoryManager();
      
      // Delete: Only admins can delete analytics
      allow delete: if isAuthenticated() && 
                       request.auth.token.role == 'admin';
    }
    
    // Supplier Information Collection Rules
    match /suppliers/{supplierId} {
      // Read: Allow authenticated users to read supplier information
      allow read: if isAuthenticated();
      
      // Create: Only inventory managers and admins can create suppliers
      allow create: if isAdminOrInventoryManager();
      
      // Update: Only inventory managers and admins can update suppliers
      allow update: if isAdminOrInventoryManager();
      
      // Delete: Only admins can delete suppliers
      allow delete: if isAuthenticated() && 
                       request.auth.token.role == 'admin';
    }
  }
}

// Additional Notes:
// 1. These rules assume that user roles are stored in custom claims in the authentication token
// 2. The 'createdBy' field should be automatically set to request.auth.uid when creating records
// 3. Consider implementing rate limiting for write operations in production
// 4. Regular backups should be configured for critical inventory data
// 5. Consider implementing field-level validation for more granular control
// 6. Monitor Firestore usage and costs, especially for read operations on large collections
// 7. Implement proper indexing for frequently queried fields (rollId, denimTypeId, status, etc.)
