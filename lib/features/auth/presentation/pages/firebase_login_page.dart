import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:hm_collection/features/auth/presentation/pages/admin_login_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/administrator_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/cutting_master_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../../../core/widgets/custom_text_field.dart';
import 'package:hm_collection/shared/widgets/loading_screen.dart';
import '../../data/services/biometric_service.dart';
import '../widgets/loading_button.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../bloc/firebase_auth_bloc.dart';
import '../widgets/loading_button.dart';
import '../widgets/role_selection_widget.dart';
import '../../../../shared/enums/common_enums.dart';
import 'cutting_master_login_page.dart';

/// Firebase login page
class FirebaseLoginPage extends StatefulWidget {
  const FirebaseLoginPage({super.key});

  @override
  State<FirebaseLoginPage> createState() => _FirebaseLoginPageState();
}

class _FirebaseLoginPageState extends State<FirebaseLoginPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _signInFormKey = GlobalKey<FormState>();
  final _signUpFormKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _rememberMe = false;
  bool _useBiometric = false;
  bool _isBiometricAvailable = false;
  bool _isBiometricEnabled = false;

  final BiometricService _biometricService = BiometricService();
  final _secureStorage = const FlutterSecureStorage();
  UserRole _selectedRole = UserRole.sewingOperator;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _checkBiometricStatus();
    _loadSavedCredentials();
  }

  Future<void> _checkBiometricStatus() async {
    final isAvailable = await _biometricService.isBiometricAvailable();
    final isEnabled = await _biometricService.isBiometricEnabled();

    if (mounted) {
      setState(() {
        _isBiometricAvailable = isAvailable;
        _isBiometricEnabled = isEnabled;
      });

      // Try biometric login if enabled
      if (isEnabled) {
        _tryBiometricLogin();
      }
    }
  }

  Future<void> _loadSavedCredentials() async {
    if (_rememberMe) {
      final credentials = await _biometricService.getStoredCredentials();
      if (mounted && credentials['email'] != null && credentials['password'] != null) {
        setState(() {
          _emailController.text = credentials['email']!;
          _passwordController.text = credentials['password']!;
        });
      }
    }
  }

  Future<void> _tryBiometricLogin() async {
    if (!_isBiometricAvailable || !_isBiometricEnabled) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Biometric authentication is not available or enabled')),
        );
      }
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final credentials = await _biometricService.getStoredCredentials();
      if (credentials['email'] == null || credentials['password'] == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No saved credentials found')),
          );
        }
        return;
      }

      final isAuthenticated = await _biometricService.authenticateWithBiometrics();

      if (isAuthenticated && mounted) {
        setState(() {
          _emailController.text = credentials['email']!;
          _passwordController.text = credentials['password']!;
        });
        await _handleSignIn();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Authentication failed')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error during biometric login: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<FirebaseAuthBloc>(),
      child: Scaffold(
        body: BlocListener<FirebaseAuthBloc, FirebaseAuthState>(
          listener: _handleAuthStateChanges,
          child: LoadingOverlay(
            isLoading: _isLoading,
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  children: [
                    const SizedBox(height: 40),
                    _buildHeader(),
                    const SizedBox(height: 40),
                    _buildTabBar(),
                    const SizedBox(height: 20),
                    _buildTabBarView(),
                    const SizedBox(height: 20),
                    _buildForgotPasswordButton(),
                    const SizedBox(height: 20),
                    _buildAdminCreationButton(),
                    const SizedBox(height: 12),
                    _buildCuttingMasterLoginButton(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(60),
          ),
          child: const Icon(
            Icons.factory,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),
        Text(
          'HM Collection',
          style: AppTextStyles.headlineLarge.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Manufacturing Management System',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.primary,
            width: 2,
          ),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorSize: TabBarIndicatorSize.tab,
        tabs: const [
          Tab(text: 'Sign In'),
          Tab(text: 'Sign Up'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return SizedBox(
      height: 400, // Fixed height to ensure proper rendering
      child: TabBarView(
        controller: _tabController,
        children: [
          SingleChildScrollView(
            child: _buildSignInForm(),
          ),
          SingleChildScrollView(
            child: _buildSignUpForm(),
          ),
        ],
      ),
    );
  }

  Widget _buildSignInForm() {
    return Form(
      key: _signInFormKey,
      child: Column(
        children: [
          CustomTextField(
            controller: _emailController,
            label: 'Email',
            hintText: 'Enter your email',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email_outlined,
            validator: _validateEmail,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _passwordController,
            label: 'Password',
            hintText: 'Enter your password',
            obscureText: !_isPasswordVisible,
            prefixIcon: Icons.lock_outlined,
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
            ),
            validator: _validatePassword,
          ),
          const SizedBox(height: 16),
          _buildRememberMe(),
          const SizedBox(height: 24),
          _buildSignInButton(),
        ],
      ),
    );
  }

  Widget _buildRememberMe() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Checkbox(
              value: _rememberMe,
              onChanged: (value) {
                setState(() {
                  _rememberMe = value ?? false;
                  if (!_rememberMe) {
                    _biometricService.clearStoredCredentials();
                    _biometricService.setBiometricPreference(false);
                    _isBiometricEnabled = false;
                  }
                });
              },
            ),
            const Text('Remember me'),
          ],
        ),
        if (_rememberMe && _isBiometricAvailable) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              const SizedBox(width: 40), // Align with the checkbox
              Checkbox(
                value: _isBiometricEnabled,
                onChanged: _isLoading ? null : (value) async {
                  final useBiometric = value ?? false;
                  await _biometricService.setBiometricPreference(useBiometric);
                  setState(() {
                    _isBiometricEnabled = useBiometric;
                  });
                },
              ),
              const Text('Enable biometric login'),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildSignInButton() {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        return Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: LoadingButton(
                onPressed: _isLoading ? null : _handleSignIn,
                isLoading: _isLoading,
                // Correct label for sign-in action
                child: const Text('Sign In'),
              ),
            ),
            if (_isBiometricAvailable) ...[
              const SizedBox(height: 16),
              _buildBiometricButton(),
            ],
          ],
        );
      },
    );
  }

  Widget _buildBiometricButton() {
    return FutureBuilder<IconData>(
      future: _biometricService.getBiometricIcon(),
      builder: (context, snapshot) {
        final icon = snapshot.data ?? Icons.fingerprint;
        return Column(
          children: [
            OutlinedButton.icon(
              onPressed: _isLoading ? null : _tryBiometricLogin,
              icon: _isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Icon(icon),
              label: Text(_isLoading ? 'Authenticating...' : 'Use Biometric'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                side: BorderSide(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Or sign in with email and password',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).hintColor,
                  ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSignUpForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(bottom: 20), // Add some padding at the bottom
      child: Form(
        key: _signUpFormKey,
        child: Column(
          mainAxisSize: MainAxisSize.min, // Important for scrolling
          children: [
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _firstNameController,
                    label: 'First Name',
                    hintText: 'Enter first name',
                    prefixIcon: Icons.person_outlined,
                    validator: _validateFirstName,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _lastNameController,
                    label: 'Last Name',
                    hintText: 'Enter last name',
                    prefixIcon: Icons.person_outlined,
                    validator: _validateLastName,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _emailController,
              label: 'Email',
              hintText: 'Enter your email',
              keyboardType: TextInputType.emailAddress,
              prefixIcon: Icons.email_outlined,
              validator: _validateEmail,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _passwordController,
              label: 'Password',
              hintText: 'Enter your password',
              obscureText: !_isPasswordVisible,
              prefixIcon: Icons.lock_outlined,
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
              validator: _validatePassword,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _confirmPasswordController,
              label: 'Confirm Password',
              hintText: 'Confirm your password',
              obscureText: !_isConfirmPasswordVisible,
              prefixIcon: Icons.lock_outlined,
              suffixIcon: IconButton(
                icon: Icon(
                  _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                  });
                },
              ),
              validator: _validateConfirmPassword,
            ),
            const SizedBox(height: 16),
            RoleSelectionWidget(
              selectedRole: _selectedRole,
              onRoleChanged: (role) {
                setState(() {
                  _selectedRole = role;
                });
              },
            ),
            const SizedBox(height: 24),
            LoadingButton(
              onPressed: _handleSignUp,
              isLoading: _isLoading,
              child: const Text('Sign Up'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForgotPasswordButton() {
    return TextButton(
      onPressed: _isLoading ? null : _handleForgotPassword,
      child: Text(
        'Forgot Password?',
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildAdminCreationButton() {
    return TextButton(
      onPressed: _isLoading ? null : _navigateToAdminCreation,
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(color: AppColors.primary),
        ),
      ),
      child: Text(
        'Create Admin Accounts',
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCuttingMasterLoginButton() {
    return TextButton.icon(
      onPressed: _isLoading ? null : _navigateToCuttingMasterLogin,
      icon: const Icon(Icons.content_cut, size: 18),
      label: const Text('Cutting Master Login'),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(color: Color(0xFF6A1B9A)),
        ),
        foregroundColor: const Color(0xFF6A1B9A),
      ),
    );
  }

  void _navigateToAdminCreation() {
    // GoRouter.of(context).push('/admin-creation');
    Navigator.of(context).push(MaterialPageRoute(builder: (context)=> const AdminLoginPage()));
  }

  void _navigateToCuttingMasterLogin() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context)=> const CuttingMasterLoginPage()));
  }

  void _navigateBasedOnRole() {
    // TODO: Implement role-based redirects here later.
    // For now, always go to the main dashboard using GoRouter for consistency.
    if (!mounted) return;
    Navigator.of(context).push(MaterialPageRoute(builder: (context)=> const AdministratorDashboardPage()));
  }

  void _handleAuthStateChanges(BuildContext context, FirebaseAuthState state) {
    if (state is FirebaseAuthAuthenticated) {
      setState(() {
        _isLoading = false;
      });
      _showSuccessSnackBar('Welcome back!');
      _navigateBasedOnRole();
    } else if (state is FirebaseAuthError) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar(state.message);
    } else if (state is FirebaseAuthLoading) {
      setState(() {
        _isLoading = true;
      });
    }
  }

  Future<void> _handleSignIn() async {
    if (!_signInFormKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    // Store credentials if remember me is enabled
    if (_rememberMe) {
      await _biometricService.storeCredentials(email, password);
    } else {
      await _biometricService.clearStoredCredentials();
    }

    context.read<FirebaseAuthBloc>().add(
      FirebaseSignInRequested(
        email: email,
        password: password,
      ),
    );
  }

  void _handleSignUp() {
    if (_signUpFormKey.currentState?.validate() ?? false) {
      context.read<FirebaseAuthBloc>().add(
        FirebaseSignUpRequested(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          role: _selectedRole,
        ),
      );
    }
  }

  void _handleForgotPassword() {
    if (_emailController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter your email address first');
      return;
    }

    context.read<FirebaseAuthBloc>().add(
      SendPasswordResetEmailRequested(_emailController.text.trim()),
    );
  }

  void _clearSignUpForm() {
    _firstNameController.clear();
    _lastNameController.clear();
    _emailController.clear();
    _passwordController.clear();
    _confirmPasswordController.clear();
    setState(() {
      _selectedRole = UserRole.sewingOperator;
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  String? _validateFirstName(String? value) {
    if (value == null || value.isEmpty) {
      return 'First name is required';
    }
    return null;
  }

  String? _validateLastName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Last name is required';
    }
    return null;
  }
}
