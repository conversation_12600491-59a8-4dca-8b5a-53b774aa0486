import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../entities/cutting_master_entities.dart';
import '../repositories/cutting_master_repository.dart';

/// Use cases for cutting master operations
class CuttingMasterUseCases {
  final CuttingMasterRepository _repository;

  CuttingMasterUseCases(this._repository);

  // Marker use cases

  /// Create a new marker
  Future<Either<Failure, ApiResponse<Marker>>> createMarker({
    required String markerNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required double fabricWidth,
    required double markerLength,
    required int piecesPerMarker,
    required List<MarkerPiece> pieces,
    required String createdBy,
    required String createdByName,
    required double efficiency,
    String? notes,
    Map<String, dynamic> specifications = const {},
  }) {
    return _repository.createMarker(
      markerNumber: markerNumber,
      productId: productId,
      productName: productName,
      fabricType: fabricType,
      fabricWidth: fabricWidth,
      markerLength: markerLength,
      piecesPerMarker: piecesPerMarker,
      pieces: pieces,
      createdBy: createdBy,
      createdByName: createdByName,
      efficiency: efficiency,
      notes: notes,
      specifications: specifications,
    );
  }

  /// Get markers with optional filters
  Future<Either<Failure, ApiListResponse<Marker>>> getMarkers({
    MarkerStatus? status,
    String? productId,
    int? limit,
    String? lastDocumentId,
  }) {
    return _repository.getMarkers(
      status: status,
      productId: productId,
      limit: limit,
      lastDocumentId: lastDocumentId,
    );
  }

  /// Get marker by ID
  Future<Either<Failure, ApiResponse<Marker>>> getMarkerById(String markerId) {
    return _repository.getMarkerById(markerId);
  }

  /// Approve a marker
  Future<Either<Failure, ApiResponse<Marker>>> approveMarker({
    required String markerId,
    required String approvedBy,
    required String approvedByName,
  }) {
    return _repository.approveMarker(
      markerId: markerId,
      approvedBy: approvedBy,
      approvedByName: approvedByName,
    );
  }

  /// Reject a marker
  Future<Either<Failure, ApiResponse<Marker>>> rejectMarker({
    required String markerId,
    required String rejectedBy,
    required String rejectedByName,
    required String reason,
  }) {
    return _repository.rejectMarker(
      markerId: markerId,
      rejectedBy: rejectedBy,
      rejectedByName: rejectedByName,
      reason: reason,
    );
  }

  // Cutting task use cases

  /// Create a new cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> createCuttingTask({
    required String taskNumber,
    required String markerId,
    required String markerNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required int plannedQuantity,
    required String assignedTo,
    required String assignedToName,
    required String assignedBy,
    required String assignedByName,
    required DateTime plannedStartDate,
    required DateTime plannedEndDate,
    String? notes,
    Map<String, dynamic> metadata = const {},
  }) {
    return _repository.createCuttingTask(
      taskNumber: taskNumber,
      markerId: markerId,
      markerNumber: markerNumber,
      productId: productId,
      productName: productName,
      fabricType: fabricType,
      plannedQuantity: plannedQuantity,
      assignedTo: assignedTo,
      assignedToName: assignedToName,
      assignedBy: assignedBy,
      assignedByName: assignedByName,
      plannedStartDate: plannedStartDate,
      plannedEndDate: plannedEndDate,
      notes: notes,
      metadata: metadata,
    );
  }

  /// Get cutting tasks with optional filters
  Future<Either<Failure, ApiListResponse<CuttingTask>>> getCuttingTasks({
    CuttingTaskStatus? status,
    String? assignedTo,
    String? productId,
    int? limit,
    String? lastDocumentId,
  }) {
    return _repository.getCuttingTasks(
      status: status,
      assignedTo: assignedTo,
      productId: productId,
      limit: limit,
      lastDocumentId: lastDocumentId,
    );
  }

  /// Start a cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> startCuttingTask(String taskId) {
    return _repository.startCuttingTask(taskId);
  }

  /// Complete a cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> completeCuttingTask({
    required String taskId,
    required int actualQuantity,
    required List<CuttingPiece> cutPieces,
    List<QualityCheck>? qualityChecks,
    String? notes,
  }) {
    return _repository.completeCuttingTask(
      taskId: taskId,
      actualQuantity: actualQuantity,
      cutPieces: cutPieces,
      qualityChecks: qualityChecks,
      notes: notes,
    );
  }

  // Fabric calculation use cases

  /// Create a new fabric calculation
  Future<Either<Failure, ApiResponse<FabricCalculation>>> createFabricCalculation({
    required String calculationNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required double fabricWidth,
    required Map<String, int> sizeQuantities,
    required double wastagePercentage,
    required String calculatedBy,
    required String calculatedByName,
    String? notes,
  }) {
    // Compute totals in use case to satisfy repository contract
    double totalFabricRequired = 0.0;
    sizeQuantities.forEach((size, quantity) {
      // Simplified domain rule: assume each piece needs 1.5 meters
      totalFabricRequired += quantity * 1.5;
    });
    final double totalFabricWithWastage =
        totalFabricRequired * (1 + wastagePercentage / 100);

    return _repository.createFabricCalculation(
      calculationNumber: calculationNumber,
      productId: productId,
      productName: productName,
      fabricType: fabricType,
      fabricWidth: fabricWidth,
      sizeQuantities: sizeQuantities,
      totalFabricRequired: totalFabricRequired,
      wastagePercentage: wastagePercentage,
      totalFabricWithWastage: totalFabricWithWastage,
      calculatedBy: calculatedBy,
      calculatedByName: calculatedByName,
      notes: notes,
    );
  }

  /// Get fabric calculations with optional filters
  Future<Either<Failure, ApiListResponse<FabricCalculation>>> getFabricCalculations({
    FabricCalculationStatus? status,
    String? productId,
    int? limit,
    String? lastDocumentId,
  }) {
    return _repository.getFabricCalculations(
      status: status,
      productId: productId,
      limit: limit,
      lastDocumentId: lastDocumentId,
    );
  }

  // Production log use cases

  /// Create a new production log entry
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> createProductionLogEntry({
    required String logNumber,
    required String taskId,
    required String taskType,
    required String operatorId,
    required String operatorName,
    required DateTime startTime,
    required int quantityProduced,
    double? qualityScore,
    String? notes,
    Map<String, dynamic> metrics = const {},
  }) {
    return _repository.createProductionLogEntry(
      logNumber: logNumber,
      taskId: taskId,
      taskType: taskType,
      operatorId: operatorId,
      operatorName: operatorName,
      startTime: startTime,
      quantityProduced: quantityProduced,
      qualityScore: qualityScore,
      notes: notes,
      metrics: metrics,
    );
  }

  /// Get production log entries with optional filters
  Future<Either<Failure, ApiListResponse<ProductionLogEntry>>> getProductionLogEntries({
    String? taskId,
    String? operatorId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    String? lastDocumentId,
  }) {
    return _repository.getProductionLogEntries(
      taskId: taskId,
      operatorId: operatorId,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
      lastDocumentId: lastDocumentId,
    );
  }

  // Analytics use cases

  /// Get marker analytics
  Future<Either<Failure, ApiResponse<Map<String, dynamic>>>> getMarkerAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String? productId,
  }) async {
    final result = await _repository.getMarkerEfficiencyReport(
      startDate: startDate,
      endDate: endDate,
      productId: productId,
    );
    return result.map((data) => ApiResponse(data: data, message: 'Success', success: true));
  }

  /// Get cutting task analytics
  Future<Either<Failure, ApiResponse<Map<String, dynamic>>>> getCuttingTaskAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String? productId,
  }) async {
    final result = await _repository.getCuttingTaskAnalytics(
      startDate: startDate,
      endDate: endDate,
      productId: productId,
    );
    return result.map((data) => ApiResponse(data: data, message: 'Success', success: true));
  }

  /// Get production analytics
  Future<Either<Failure, ApiResponse<Map<String, dynamic>>>> getProductionAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String? operatorId,
  }) async {
    final result = await _repository.getProductionSummary(
      startDate: startDate,
      endDate: endDate,
      operatorId: operatorId,
    );
    return result.map((data) => ApiResponse(data: data, message: 'Success', success: true));
  }
}
