import 'package:equatable/equatable.dart';
import '../../domain/entities/numbering_task.dart';


/// Base class for all numbering states
abstract class NumberingState extends Equatable {
  const NumberingState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class NumberingInitial extends NumberingState {
  const NumberingInitial();
}

/// Loading state
class NumberingLoading extends NumberingState {
  final String? message;

  const NumberingLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// State when tasks are loaded successfully
class NumberingTasksLoaded extends NumberingState {
  final List<NumberingTask> tasks;
  final bool hasReachedMax;
  final int totalCount;
  final Set<String> selectedTaskIds;
  final NumberingTaskStatistics? statistics;
  final double? defaultRatePerPiece;

  const NumberingTasksLoaded({
    required this.tasks,
    this.hasReachedMax = false,
    this.totalCount = 0,
    this.selectedTaskIds = const {},
    this.statistics,
    this.defaultRatePerPiece,
  });

  @override
  List<Object?> get props => [
        tasks,
        hasReachedMax,
        totalCount,
        selectedTaskIds,
        statistics,
        defaultRatePerPiece,
      ];

  /// Create a copy with updated fields
  NumberingTasksLoaded copyWith({
    List<NumberingTask>? tasks,
    bool? hasReachedMax,
    int? totalCount,
    Set<String>? selectedTaskIds,
    NumberingTaskStatistics? statistics,
    double? defaultRatePerPiece,
  }) {
    return NumberingTasksLoaded(
      tasks: tasks ?? this.tasks,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      totalCount: totalCount ?? this.totalCount,
      selectedTaskIds: selectedTaskIds ?? this.selectedTaskIds,
      statistics: statistics ?? this.statistics,
      defaultRatePerPiece: defaultRatePerPiece ?? this.defaultRatePerPiece,
    );
  }

  /// Get selected tasks
  List<NumberingTask> get selectedTasks {
    return tasks.where((task) => selectedTaskIds.contains(task.id)).toList();
  }

  /// Check if all tasks are selected
  bool get areAllTasksSelected {
    return tasks.isNotEmpty && selectedTaskIds.length == tasks.length;
  }

  /// Check if any tasks are selected
  bool get hasSelectedTasks {
    return selectedTaskIds.isNotEmpty;
  }
}

/// State when a single task is loaded
class NumberingTaskLoaded extends NumberingState {
  final NumberingTask task;

  const NumberingTaskLoaded(this.task);

  @override
  List<Object?> get props => [task];
}

/// State when task operation is successful
class NumberingTaskOperationSuccess extends NumberingState {
  final String message;
  final NumberingTask? task;
  final String? operationType; // 'create', 'update', 'delete', 'complete', 'send_to_munda'

  const NumberingTaskOperationSuccess({
    required this.message,
    this.task,
    this.operationType,
  });

  @override
  List<Object?> get props => [message, task, operationType];
}

/// State when statistics are loaded
class NumberingStatisticsLoaded extends NumberingState {
  final NumberingTaskStatistics statistics;

  const NumberingStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// State when default rate is loaded
class DefaultRateLoaded extends NumberingState {
  final double rate;

  const DefaultRateLoaded(this.rate);

  @override
  List<Object?> get props => [rate];
}

/// State when export is successful
class NumberingExportSuccess extends NumberingState {
  final String filePath;
  final String format;

  const NumberingExportSuccess({
    required this.filePath,
    required this.format,
  });

  @override
  List<Object?> get props => [filePath, format];
}

/// Error state
class NumberingError extends NumberingState {
  final String message;
  final String? errorCode;
  final dynamic error;

  const NumberingError({
    required this.message,
    this.errorCode,
    this.error,
  });

  @override
  List<Object?> get props => [message, errorCode, error];
}

/// State when no tasks are found
class NumberingTasksEmpty extends NumberingState {
  final String message;

  const NumberingTasksEmpty({
    this.message = 'No numbering tasks found',
  });

  @override
  List<Object?> get props => [message];
}

/// State when refreshing tasks
class NumberingRefreshing extends NumberingState {
  final List<NumberingTask> currentTasks;

  const NumberingRefreshing(this.currentTasks);

  @override
  List<Object?> get props => [currentTasks];
}

/// State when loading more tasks (pagination)
class NumberingLoadingMore extends NumberingState {
  final List<NumberingTask> currentTasks;

  const NumberingLoadingMore(this.currentTasks);

  @override
  List<Object?> get props => [currentTasks];
}

/// State when searching tasks
class NumberingSearching extends NumberingState {
  final String searchQuery;
  final List<NumberingTask> currentTasks;

  const NumberingSearching({
    required this.searchQuery,
    required this.currentTasks,
  });

  @override
  List<Object?> get props => [searchQuery, currentTasks];
}

/// State when search results are loaded
class NumberingSearchResults extends NumberingState {
  final String searchQuery;
  final List<NumberingTask> results;
  final int totalCount;

  const NumberingSearchResults({
    required this.searchQuery,
    required this.results,
    required this.totalCount,
  });

  @override
  List<Object?> get props => [searchQuery, results, totalCount];
}

/// State when watching tasks (real-time updates)
class NumberingWatchingTasks extends NumberingState {
  final List<NumberingTask> tasks;
  final NumberingTaskStatus? statusFilter;
  final String? userFilter;

  const NumberingWatchingTasks({
    required this.tasks,
    this.statusFilter,
    this.userFilter,
  });

  @override
  List<Object?> get props => [tasks, statusFilter, userFilter];
}

/// State when bulk operation is in progress
class NumberingBulkOperationInProgress extends NumberingState {
  final String operationType;
  final List<String> taskIds;
  final int processedCount;
  final int totalCount;

  const NumberingBulkOperationInProgress({
    required this.operationType,
    required this.taskIds,
    required this.processedCount,
    required this.totalCount,
  });

  @override
  List<Object?> get props => [operationType, taskIds, processedCount, totalCount];

  double get progress => totalCount > 0 ? processedCount / totalCount : 0.0;
}

/// State when bulk operation is completed
class NumberingBulkOperationCompleted extends NumberingState {
  final String operationType;
  final int successCount;
  final int failureCount;
  final List<String> failedTaskIds;

  const NumberingBulkOperationCompleted({
    required this.operationType,
    required this.successCount,
    required this.failureCount,
    required this.failedTaskIds,
  });

  @override
  List<Object?> get props => [operationType, successCount, failureCount, failedTaskIds];
}

/// State when offline mode is detected
class NumberingOfflineMode extends NumberingState {
  final List<NumberingTask> cachedTasks;
  final String message;

  const NumberingOfflineMode({
    required this.cachedTasks,
    this.message = 'Working offline. Changes will sync when online.',
  });

  @override
  List<Object?> get props => [cachedTasks, message];
}

/// State when sync is in progress
class NumberingSyncing extends NumberingState {
  final String message;
  final double? progress;

  const NumberingSyncing({
    this.message = 'Syncing data...',
    this.progress,
  });

  @override
  List<Object?> get props => [message, progress];
}

/// State when sync is completed
class NumberingSyncCompleted extends NumberingState {
  final String message;
  final int syncedCount;

  const NumberingSyncCompleted({
    required this.message,
    required this.syncedCount,
  });

  @override
  List<Object?> get props => [message, syncedCount];
}

/// State when validation error occurs
class NumberingValidationError extends NumberingState {
  final Map<String, String> fieldErrors;
  final String? generalError;

  const NumberingValidationError({
    required this.fieldErrors,
    this.generalError,
  });

  @override
  List<Object?> get props => [fieldErrors, generalError];
}

/// State when permission error occurs
class NumberingPermissionError extends NumberingState {
  final String message;
  final String requiredPermission;

  const NumberingPermissionError({
    required this.message,
    required this.requiredPermission,
  });

  @override
  List<Object?> get props => [message, requiredPermission];
}
