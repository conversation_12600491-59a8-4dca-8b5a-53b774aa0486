import '../constants/app_constants.dart';

/// Utility class for common validation functions
class ValidationUtils {
  /// Validate email address
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }
  
  /// Validate password
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }
    
    if (password.length < AppConstants.minPasswordLength) {
      return 'Password must be at least ${AppConstants.minPasswordLength} characters';
    }
    
    if (password.length > AppConstants.maxPasswordLength) {
      return 'Password must be less than ${AppConstants.maxPasswordLength} characters';
    }
    
    // Check for at least one uppercase letter
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    
    // Check for at least one lowercase letter
    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }
    
    // Check for at least one digit
    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    
    // Check for at least one special character
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Password must contain at least one special character';
    }
    
    return null;
  }
  
  /// Validate username
  static String? validateUsername(String? username) {
    if (username == null || username.isEmpty) {
      return 'Username is required';
    }
    
    if (username.length < AppConstants.minUsernameLength) {
      return 'Username must be at least ${AppConstants.minUsernameLength} characters';
    }
    
    if (username.length > AppConstants.maxUsernameLength) {
      return 'Username must be less than ${AppConstants.maxUsernameLength} characters';
    }
    
    // Check for valid characters (alphanumeric and underscore)
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username)) {
      return 'Username can only contain letters, numbers, and underscores';
    }
    
    return null;
  }
  
  /// Validate phone number
  static String? validatePhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return 'Phone number is required';
    }
    
    // Remove all non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digitsOnly.length < 10) {
      return 'Phone number must be at least 10 digits';
    }
    
    if (digitsOnly.length > 15) {
      return 'Phone number must be less than 15 digits';
    }
    
    return null;
  }
  
  /// Validate required field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
  
  /// Validate minimum length
  static String? validateMinLength(String? value, int minLength, String fieldName) {
    if (value == null || value.length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }
    return null;
  }
  
  /// Validate maximum length
  static String? validateMaxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.length > maxLength) {
      return '$fieldName must be less than $maxLength characters';
    }
    return null;
  }
  
  /// Validate numeric value
  static String? validateNumeric(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    
    if (double.tryParse(value) == null) {
      return '$fieldName must be a valid number';
    }
    
    return null;
  }
  
  /// Validate positive number
  static String? validatePositiveNumber(String? value, String fieldName) {
    final numericError = validateNumeric(value, fieldName);
    if (numericError != null) return numericError;
    
    final number = double.parse(value!);
    if (number <= 0) {
      return '$fieldName must be greater than 0';
    }
    
    return null;
  }
  
  /// Validate integer value
  static String? validateInteger(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    
    if (int.tryParse(value) == null) {
      return '$fieldName must be a valid integer';
    }
    
    return null;
  }
  
  /// Validate date format (yyyy-MM-dd)
  static String? validateDate(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    
    try {
      DateTime.parse(value);
      return null;
    } catch (e) {
      return '$fieldName must be a valid date (YYYY-MM-DD)';
    }
  }
  
  /// Validate URL
  static String? validateUrl(String? url, String fieldName) {
    if (url == null || url.isEmpty) {
      return '$fieldName is required';
    }
    
    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return '$fieldName must be a valid URL';
      }
      return null;
    } catch (e) {
      return '$fieldName must be a valid URL';
    }
  }
  
  /// Validate file size
  static String? validateFileSize(int? fileSizeBytes, String fieldName) {
    if (fileSizeBytes == null) {
      return '$fieldName is required';
    }
    
    if (fileSizeBytes > AppConstants.maxFileSize) {
      final maxSizeMB = AppConstants.maxFileSize / (1024 * 1024);
      return '$fieldName must be less than ${maxSizeMB.toStringAsFixed(1)}MB';
    }
    
    return null;
  }
  
  /// Validate file extension
  static String? validateFileExtension(String? fileName, List<String> allowedExtensions, String fieldName) {
    if (fileName == null || fileName.isEmpty) {
      return '$fieldName is required';
    }
    
    final extension = fileName.split('.').last.toLowerCase();
    
    if (!allowedExtensions.contains(extension)) {
      return '$fieldName must be one of: ${allowedExtensions.join(', ')}';
    }
    
    return null;
  }
  
  /// Validate quantity (positive integer)
  static String? validateQuantity(String? value, String fieldName) {
    final integerError = validateInteger(value, fieldName);
    if (integerError != null) return integerError;
    
    final quantity = int.parse(value!);
    if (quantity <= 0) {
      return '$fieldName must be greater than 0';
    }
    
    return null;
  }
  
  /// Validate percentage (0-100)
  static String? validatePercentage(String? value, String fieldName) {
    final numericError = validateNumeric(value, fieldName);
    if (numericError != null) return numericError;
    
    final percentage = double.parse(value!);
    if (percentage < 0 || percentage > 100) {
      return '$fieldName must be between 0 and 100';
    }
    
    return null;
  }
  
  /// Validate order number format
  static String? validateOrderNumber(String? orderNumber) {
    if (orderNumber == null || orderNumber.isEmpty) {
      return 'Order number is required';
    }
    
    // Example format: ORD-2024-001234
    final orderRegex = RegExp(r'^ORD-\d{4}-\d{6}$');
    
    if (!orderRegex.hasMatch(orderNumber)) {
      return 'Order number must be in format: ORD-YYYY-XXXXXX';
    }
    
    return null;
  }
  
  /// Validate production order number format
  static String? validateProductionOrderNumber(String? productionOrderNumber) {
    if (productionOrderNumber == null || productionOrderNumber.isEmpty) {
      return 'Production order number is required';
    }
    
    // Example format: PRD-2024-001234
    final productionOrderRegex = RegExp(r'^PRD-\d{4}-\d{6}$');
    
    if (!productionOrderRegex.hasMatch(productionOrderNumber)) {
      return 'Production order number must be in format: PRD-YYYY-XXXXXX';
    }
    
    return null;
  }
}
