import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';

import '../auth/entities/user_entities.dart';
import 'firebase_config.dart';

/// Firebase authentication service
@singleton
class FirebaseAuthService {
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  final FirebaseConfig _firebaseConfig;

  FirebaseAuthService(
    this._auth,
    this._firestore,
    this._firebaseConfig,
  );

  /// Get authentication state stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Get user changes stream
  Stream<User?> get userChanges => _auth.userChanges();

  /// Get current Firebase user
  User? get currentUser => _auth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => currentUser != null;

  /// Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Log analytics event
      await _firebaseConfig.logEvent('login', parameters: {
        'method': 'email_password',
      });

      return credential;
    } on FirebaseAuthException catch (e) {
      // Log error
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Create user with email and password
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Log analytics event
      await _firebaseConfig.logEvent('sign_up', parameters: {
        'method': 'email_password',
      });

      return credential;
    } on FirebaseAuthException catch (e) {
      // Log error
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();

      // Log analytics event
      await _firebaseConfig.logEvent('logout');
    } catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);

      // Log analytics event
      await _firebaseConfig.logEvent('password_reset_requested');
    } on FirebaseAuthException catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      await user.updatePassword(newPassword);

      // Log analytics event
      await _firebaseConfig.logEvent('password_updated');
    } on FirebaseAuthException catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Re-authenticate user
  Future<UserCredential> reauthenticateWithCredential({
    required String email,
    required String password,
  }) async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      final credential = EmailAuthProvider.credential(
        email: email,
        password: password,
      );

      return await user.reauthenticateWithCredential(credential);
    } on FirebaseAuthException catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Update email
  Future<void> updateEmail(String newEmail) async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      await user.updateEmail(newEmail);

      // Log analytics event
      await _firebaseConfig.logEvent('email_updated');
    } on FirebaseAuthException catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      await user.sendEmailVerification();

      // Log analytics event
      await _firebaseConfig.logEvent('email_verification_sent');
    } on FirebaseAuthException catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Reload user
  Future<void> reloadUser() async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      await user.reload();
    } catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Delete user
  Future<void> deleteUser() async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      // Delete user data from Firestore first
      await _firestore.collection('users').doc(user.uid).delete();

      // Delete Firebase Auth user
      await user.delete();

      // Log analytics event
      await _firebaseConfig.logEvent('account_deleted');
    } catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Update display name
  Future<void> updateDisplayName(String displayName) async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      await user.updateDisplayName(displayName);
    } catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Update photo URL
  Future<void> updatePhotoURL(String photoURL) async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user signed in');

      await user.updatePhotoURL(photoURL);
    } catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      rethrow;
    }
  }

  /// Get ID token
  Future<String?> getIdToken({bool forceRefresh = false}) async {
    try {
      final user = currentUser;
      if (user == null) return null;

      return await user.getIdToken(forceRefresh);
    } catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      return null;
    }
  }

  /// Get ID token result
  Future<IdTokenResult?> getIdTokenResult({bool forceRefresh = false}) async {
    try {
      final user = currentUser;
      if (user == null) return null;

      return await user.getIdTokenResult(forceRefresh);
    } catch (e) {
      await _firebaseConfig.recordError(e, StackTrace.current);
      return null;
    }
  }

  /// Check if email is verified
  bool get isEmailVerified => currentUser?.emailVerified ?? false;

  /// Get user email
  String? get userEmail => currentUser?.email;

  /// Get user UID
  String? get userUID => currentUser?.uid;

  /// Get user display name
  String? get userDisplayName => currentUser?.displayName;

  /// Get user photo URL
  String? get userPhotoURL => currentUser?.photoURL;

  /// Get user phone number
  String? get userPhoneNumber => currentUser?.phoneNumber;

  /// Get user metadata
  UserMetadata? get userMetadata => currentUser?.metadata;

  /// Get user provider data
  List<UserInfo> get userProviderData => currentUser?.providerData ?? [];

  /// Check if user is anonymous
  bool get isAnonymous => currentUser?.isAnonymous ?? false;

  /// Get user creation time
  DateTime? get userCreationTime => currentUser?.metadata.creationTime;

  /// Get user last sign in time
  DateTime? get userLastSignInTime => currentUser?.metadata.lastSignInTime;

  /// Set user properties for analytics
  Future<void> setUserProperties({
    String? userId,
    Map<String, String?>? properties,
  }) async {
    await _firebaseConfig.setUserProperties(
      userId: userId ?? userUID,
      properties: properties,
    );
  }

  /// Log user event
  Future<void> logUserEvent(
    String eventName, {
    Map<String, Object?>? parameters,
  }) async {
    await _firebaseConfig.logEvent(
      eventName,
      parameters: {
        'user_id': userUID,
        ...?parameters,
      },
    );
  }

  /// Handle authentication errors
  String getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak.';
      case 'invalid-email':
        return 'Invalid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'requires-recent-login':
        return 'Please sign in again to complete this action.';
      case 'credential-already-in-use':
        return 'This credential is already associated with another account.';
      case 'invalid-credential':
        return 'Invalid credentials provided.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with the same email but different credentials.';
      case 'invalid-verification-code':
        return 'Invalid verification code.';
      case 'invalid-verification-id':
        return 'Invalid verification ID.';
      case 'missing-verification-code':
        return 'Verification code is required.';
      case 'missing-verification-id':
        return 'Verification ID is required.';
      case 'session-cookie-expired':
        return 'Session has expired. Please sign in again.';
      case 'uid-already-exists':
        return 'User ID already exists.';
      case 'email-change-needs-verification':
        return 'Email change requires verification.';
      case 'multi-factor-auth-required':
        return 'Multi-factor authentication is required.';
      case 'maximum-second-factor-count-exceeded':
        return 'Maximum number of second factors exceeded.';
      case 'unsupported-first-factor':
        return 'Unsupported first factor.';
      case 'unverified-email':
        return 'Email address is not verified.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }
}
