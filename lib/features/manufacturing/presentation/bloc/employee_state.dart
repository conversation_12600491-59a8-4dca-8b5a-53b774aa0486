part of 'employee_bloc.dart';

/// Base class for all employee states
abstract class EmployeeState extends Equatable {
  const EmployeeState();

  @override
  List<Object?> get props => [];
}

/// Initial employee state
class EmployeeInitial extends EmployeeState {
  const EmployeeInitial();
}

/// Employee loading state
class EmployeeLoading extends EmployeeState {
  const EmployeeLoading();
}

/// Employee refreshing state
class EmployeeRefreshing extends EmployeeState {
  const EmployeeRefreshing();
}

/// Employees loaded state
class EmployeesLoaded extends EmployeeState {
  final List<Employee> employees;
  final EmployeeFilterCriteria? filter;
  final bool hasReachedMax;
  final int totalCount;

  const EmployeesLoaded({
    required this.employees,
    this.filter,
    this.hasReachedMax = false,
    this.totalCount = 0,
  });

  @override
  List<Object?> get props => [employees, filter, hasReachedMax, totalCount];

  EmployeesLoaded copyWith({
    List<Employee>? employees,
    EmployeeFilterCriteria? filter,
    bool? hasReachedMax,
    int? totalCount,
  }) {
    return EmployeesLoaded(
      employees: employees ?? this.employees,
      filter: filter ?? this.filter,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      totalCount: totalCount ?? this.totalCount,
    );
  }
}

/// Employee details loaded state
class EmployeeDetailsLoaded extends EmployeeState {
  final Employee employee;

  const EmployeeDetailsLoaded(this.employee);

  @override
  List<Object?> get props => [employee];
}

/// Employee created state
class EmployeeCreated extends EmployeeState {
  final Employee employee;

  const EmployeeCreated(this.employee);

  @override
  List<Object?> get props => [employee];
}

/// Employee updated state
class EmployeeUpdated extends EmployeeState {
  final Employee employee;

  const EmployeeUpdated(this.employee);

  @override
  List<Object?> get props => [employee];
}

/// Employee deleted state
class EmployeeDeleted extends EmployeeState {
  final String employeeId;

  const EmployeeDeleted(this.employeeId);

  @override
  List<Object?> get props => [employeeId];
}

/// Employee search results state
class EmployeeSearchResults extends EmployeeState {
  final List<Employee> employees;
  final String query;
  final EmployeeFilterCriteria? filter;

  const EmployeeSearchResults({
    required this.employees,
    required this.query,
    this.filter,
  });

  @override
  List<Object?> get props => [employees, query, filter];
}

/// Employee statistics loaded state
class EmployeeStatisticsLoaded extends EmployeeState {
  final EmployeeStatistics statistics;

  const EmployeeStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Employee operation success state
class EmployeeOperationSuccess extends EmployeeState {
  final String message;
  final Employee? employee;

  const EmployeeOperationSuccess(this.message, {this.employee});

  @override
  List<Object?> get props => [message, employee];
}

/// Employee activated state
class EmployeeActivated extends EmployeeState {
  final Employee employee;

  const EmployeeActivated(this.employee);

  @override
  List<Object?> get props => [employee];
}

/// Employee deactivated state
class EmployeeDeactivated extends EmployeeState {
  final Employee employee;

  const EmployeeDeactivated(this.employee);

  @override
  List<Object?> get props => [employee];
}

/// Employee transferred state
class EmployeeTransferred extends EmployeeState {
  final Employee employee;
  final Department newDepartment;

  const EmployeeTransferred(this.employee, this.newDepartment);

  @override
  List<Object?> get props => [employee, newDepartment];
}

/// Employee promoted state
class EmployeePromoted extends EmployeeState {
  final Employee employee;
  final UserRole newRole;

  const EmployeePromoted(this.employee, this.newRole);

  @override
  List<Object?> get props => [employee, newRole];
}

/// Employees exported state
class EmployeesExported extends EmployeeState {
  final String filePath;
  final String format;
  final int count;

  const EmployeesExported({
    required this.filePath,
    required this.format,
    required this.count,
  });

  @override
  List<Object?> get props => [filePath, format, count];
}

/// Employees imported state
class EmployeesImported extends EmployeeState {
  final int successCount;
  final int failureCount;
  final List<String> errors;

  const EmployeesImported({
    required this.successCount,
    required this.failureCount,
    required this.errors,
  });

  @override
  List<Object?> get props => [successCount, failureCount, errors];
}

/// Bulk operation completed state
class BulkOperationCompleted extends EmployeeState {
  final String operation;
  final int successCount;
  final int failureCount;
  final List<String> errors;

  const BulkOperationCompleted({
    required this.operation,
    required this.successCount,
    required this.failureCount,
    required this.errors,
  });

  @override
  List<Object?> get props => [operation, successCount, failureCount, errors];
}

/// Real-time listening started state
class EmployeeListeningStarted extends EmployeeState {
  final EmployeeFilterCriteria? filter;

  const EmployeeListeningStarted({this.filter});

  @override
  List<Object?> get props => [filter];
}

/// Real-time listening stopped state
class EmployeeListeningStopped extends EmployeeState {
  const EmployeeListeningStopped();
}

/// Real-time employees updated state
class EmployeesRealTimeUpdated extends EmployeeState {
  final List<Employee> employees;
  final EmployeeFilterCriteria? filter;

  const EmployeesRealTimeUpdated({
    required this.employees,
    this.filter,
  });

  @override
  List<Object?> get props => [employees, filter];
}

/// Employee error state
class EmployeeError extends EmployeeState {
  final String message;
  final String? code;
  final dynamic details;

  const EmployeeError(
    this.message, {
    this.code,
    this.details,
  });

  @override
  List<Object?> get props => [message, code, details];
}

/// Employee validation error state
class EmployeeValidationError extends EmployeeState {
  final Map<String, String> fieldErrors;
  final String? generalError;

  const EmployeeValidationError({
    required this.fieldErrors,
    this.generalError,
  });

  @override
  List<Object?> get props => [fieldErrors, generalError];
}

/// Employee network error state
class EmployeeNetworkError extends EmployeeState {
  final String message;
  final bool canRetry;

  const EmployeeNetworkError({
    required this.message,
    this.canRetry = true,
  });

  @override
  List<Object?> get props => [message, canRetry];
}

/// Employee permission error state
class EmployeePermissionError extends EmployeeState {
  final String message;
  final String requiredPermission;

  const EmployeePermissionError({
    required this.message,
    required this.requiredPermission,
  });

  @override
  List<Object?> get props => [message, requiredPermission];
}

/// Employee not found error state
class EmployeeNotFoundError extends EmployeeState {
  final String employeeId;
  final String message;

  const EmployeeNotFoundError({
    required this.employeeId,
    required this.message,
  });

  @override
  List<Object?> get props => [employeeId, message];
}

/// Employee conflict error state
class EmployeeConflictError extends EmployeeState {
  final String message;
  final Employee? conflictingEmployee;

  const EmployeeConflictError({
    required this.message,
    this.conflictingEmployee,
  });

  @override
  List<Object?> get props => [message, conflictingEmployee];
}

/// Employee loading more state (for pagination)
class EmployeeLoadingMore extends EmployeeState {
  final List<Employee> currentEmployees;

  const EmployeeLoadingMore(this.currentEmployees);

  @override
  List<Object?> get props => [currentEmployees];
}

/// Employee form validation state
class EmployeeFormValidation extends EmployeeState {
  final Map<String, String?> fieldValidations;
  final bool isValid;

  const EmployeeFormValidation({
    required this.fieldValidations,
    required this.isValid,
  });

  @override
  List<Object?> get props => [fieldValidations, isValid];
}

/// Employee form submitted state
class EmployeeFormSubmitted extends EmployeeState {
  final bool isCreating;
  final Employee? employee;

  const EmployeeFormSubmitted({
    required this.isCreating,
    this.employee,
  });

  @override
  List<Object?> get props => [isCreating, employee];
}

/// Employee form reset state
class EmployeeFormReset extends EmployeeState {
  const EmployeeFormReset();
}
