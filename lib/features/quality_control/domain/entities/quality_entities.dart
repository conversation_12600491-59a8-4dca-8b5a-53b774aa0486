import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';
import 'defect_analysis_entity.dart';
import 'product_entity.dart';

/// Quality inspection entity
class QualityInspection extends BaseEntity {
  final String inspectionNumber;
  final String orderId;
  final String orderNumber;
  final String productionOrderId;
  final String? batchId;
  final InspectionType type;
  final InspectionStage stage;
  final InspectionStatus status;
  final String inspectorId;
  final String inspectorName;
  final DateTime inspectionDate;
  final DateTime? completedDate;
  final List<QualityCheckpoint> checkpoints;
  final List<QualityDefect> defects;
  final QualityResult result;
  final QualityMetrics metrics;
  final List<String> attachments;
  final String? notes;
  final String? correctionActions;
  final bool requiresRework;
  final String? reworkInstructions;
  final Product? product;

  const QualityInspection({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.inspectionNumber,
    required this.orderId,
    required this.orderNumber,
    required this.productionOrderId,
    this.batchId,
    required this.type,
    required this.stage,
    required this.status,
    required this.inspectorId,
    required this.inspectorName,
    required this.inspectionDate,
    this.completedDate,
    required this.checkpoints,
    this.defects = const [],
    required this.result,
    required this.metrics,
    this.attachments = const [],
    this.notes,
    this.correctionActions,
    this.requiresRework = false,
    this.reworkInstructions,
    this.product,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        inspectionNumber,
        orderId,
        orderNumber,
        productionOrderId,
        batchId,
        type,
        stage,
        status,
        inspectorId,
        inspectorName,
        inspectionDate,
        completedDate,
        checkpoints,
        defects,
        result,
        metrics,
        attachments,
        notes,
        correctionActions,
        requiresRework,
        reworkInstructions,
        product,
      ];

  /// Check if inspection is completed
  bool get isCompleted => status == InspectionStatus.completed;

  /// Check if inspection passed
  bool get isPassed => result.overallResult == QualityResultType.passed;

  /// Check if inspection failed
  bool get isFailed => result.overallResult == QualityResultType.failed;

  /// Get total defects count
  int get totalDefectsCount => defects.length;

  /// Get critical defects count
  int get criticalDefectsCount => defects.where((d) => d.severity == DefectSeverity.critical).length;

  /// Get major defects count
  int get majorDefectsCount => defects.where((d) => d.severity == DefectSeverity.major).length;

  /// Get minor defects count
  int get minorDefectsCount => defects.where((d) => d.severity == DefectSeverity.minor).length;

  /// Get inspection duration
  Duration? get inspectionDuration {
    if (completedDate == null) return null;
    return completedDate!.difference(inspectionDate);
  }

  /// Get completion percentage
  double get completionPercentage {
    if (checkpoints.isEmpty) return 0.0;
    final completedCheckpoints = checkpoints.where((c) => c.isCompleted).length;
    return (completedCheckpoints / checkpoints.length) * 100;
  }
}

/// Quality checkpoint entity
class QualityCheckpoint extends BaseEntity {
  final String inspectionId;
  final String checkpointName;
  final String description;
  final CheckpointType type;
  final CheckpointCategory category;
  final bool isRequired;
  final bool isCritical;
  final CheckpointStatus status;
  final QualityStandard standard;
  final CheckpointResult? result;
  final String? inspectorNotes;
  final List<String> attachments;
  final DateTime? inspectedAt;

  const QualityCheckpoint({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.inspectionId,
    required this.checkpointName,
    required this.description,
    required this.type,
    required this.category,
    this.isRequired = true,
    this.isCritical = false,
    required this.status,
    required this.standard,
    this.result,
    this.inspectorNotes,
    this.attachments = const [],
    this.inspectedAt,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        inspectionId,
        checkpointName,
        description,
        type,
        category,
        isRequired,
        isCritical,
        status,
        standard,
        result,
        inspectorNotes,
        attachments,
        inspectedAt,
      ];

  /// Check if checkpoint is completed
  bool get isCompleted => status == CheckpointStatus.completed;

  /// Check if checkpoint passed
  bool get isPassed => result?.isPassed ?? false;

  /// Check if checkpoint failed
  bool get isFailed => result?.isFailed ?? false;
}

/// Quality defect entity
class QualityDefect extends BaseEntity {
  final String inspectionId;
  final String? checkpointId;
  final String defectCode;
  final String defectName;
  final String description;
  final DefectCategory category;
  final DefectType type;
  final DefectSeverity severity;
  final DefectStatus status;
  final String location;
  final int quantity;
  final String? rootCause;
  final String? correctionAction;
  final String? preventiveAction;
  final String detectedBy;
  final DateTime detectedAt;
  final String? assignedTo;
  final DateTime? resolvedAt;
  final List<String> attachments;

  const QualityDefect({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.inspectionId,
    this.checkpointId,
    required this.defectCode,
    required this.defectName,
    required this.description,
    required this.category,
    required this.type,
    required this.severity,
    required this.status,
    required this.location,
    this.quantity = 1,
    this.rootCause,
    this.correctionAction,
    this.preventiveAction,
    required this.detectedBy,
    required this.detectedAt,
    this.assignedTo,
    this.resolvedAt,
    this.attachments = const [],
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        inspectionId,
        checkpointId,
        defectCode,
        defectName,
        description,
        category,
        type,
        severity,
        status,
        location,
        quantity,
        rootCause,
        correctionAction,
        preventiveAction,
        detectedBy,
        detectedAt,
        assignedTo,
        resolvedAt,
        attachments,
      ];

  /// Check if defect is resolved
  bool get isResolved => status == DefectStatus.resolved;

  /// Check if defect is open
  bool get isOpen => status == DefectStatus.open;

  /// Get resolution time
  Duration? get resolutionTime {
    if (resolvedAt == null) return null;
    return resolvedAt!.difference(detectedAt);
  }
}

/// Quality standard entity
class QualityStandard extends BaseEntity {
  static const String collectionName = 'quality_standards';

  static const String fieldStandardCode = 'standardCode';
  static const String fieldStandardName = 'standardName';
  static const String fieldDescription = 'description';
  static const String fieldType = 'type';
  static const String fieldCategory = 'category';
  static const String fieldStatus = 'status';
  static const String fieldParameters = 'parameters';
  static const String fieldVersion = 'version';
  static const String fieldEffectiveDate = 'effectiveDate';
  static const String fieldExpiryDate = 'expiryDate';
  static const String fieldCreatedBy = 'createdBy';
  static const String fieldApprovedBy = 'approvedBy';
  static const String fieldApprovedAt = 'approvedAt';
  final String standardCode;
  final String standardName;
  final String description;
  final StandardType type;
  final StandardCategory category;
  final CommonStatus status;
  final List<QualityParameter> parameters;
  final String? version;
  final DateTime? effectiveDate;
  final DateTime? expiryDate;
  final String? createdBy;
  final String? approvedBy;
  final DateTime? approvedAt;

  const QualityStandard({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.standardCode,
    required this.standardName,
    required this.description,
    required this.type,
    required this.category,
    required this.status,
    required this.parameters,
    this.version,
    this.effectiveDate,
    this.expiryDate,
    this.createdBy,
    this.approvedBy,
    this.approvedAt,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        standardCode,
        standardName,
        description,
        type,
        category,
        status,
        parameters,
        version,
        effectiveDate,
        expiryDate,
        createdBy,
        approvedBy,
        approvedAt,
      ];

  /// Check if standard is active
  bool get isActive => status == CommonStatus.active;

  /// Check if standard is approved
  bool get isApproved => approvedAt != null && approvedBy != null;

  /// Check if standard is expired
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// Converts the QualityStandard instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      if (deletedAt != null) 'deletedAt': deletedAt!.toIso8601String(),
      'standardCode': standardCode,
      'standardName': standardName,
      'description': description,
      'type': type.toString().split('.').last,
      'category': category.toString().split('.').last,
      'status': status.toString().split('.').last,
      'parameters': parameters.map((param) => param.toJson()).toList(),
      if (version != null) 'version': version,
      if (effectiveDate != null) 'effectiveDate': effectiveDate!.toIso8601String(),
      if (expiryDate != null) 'expiryDate': expiryDate!.toIso8601String(),
      if (createdBy != null) 'createdBy': createdBy,
      if (approvedBy != null) 'approvedBy': approvedBy,
      if (approvedAt != null) 'approvedAt': approvedAt!.toIso8601String(),
    };
  }

  /// Creates a QualityStandard instance from a JSON map
  factory QualityStandard.fromJson(Map<String, dynamic> json) {
    return QualityStandard(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt'] as String) : null,
      standardCode: json['standardCode'] as String,
      standardName: json['standardName'] as String,
      description: json['description'] as String,
      type: StandardType.values.firstWhere(
        (e) => e.toString() == 'StandardType.${json['type']}',
        orElse: () => StandardType.internal,
      ),
      category: StandardCategory.values.firstWhere(
        (e) => e.toString() == 'StandardCategory.${json['category']}',
        orElse: () => StandardCategory.material,
      ),
      status: CommonStatus.values.firstWhere(
        (e) => e.toString() == 'CommonStatus.${json['status']}',
        orElse: () => CommonStatus.active,
      ),
      parameters: (json['parameters'] as List<dynamic>?)
              ?.map((param) => QualityParameter.fromJson(param as Map<String, dynamic>))
              .toList() ??
          [],
      version: json['version'] as String?,
      effectiveDate: json['effectiveDate'] != null ? DateTime.parse(json['effectiveDate'] as String) : null,
      expiryDate: json['expiryDate'] != null ? DateTime.parse(json['expiryDate'] as String) : null,
      createdBy: json['createdBy'] as String?,
      approvedBy: json['approvedBy'] as String?,
      approvedAt: json['approvedAt'] != null ? DateTime.parse(json['approvedAt'] as String) : null,
    );
  }
}

/// Quality parameter entity
class QualityParameter extends Equatable {
  final String parameterName;
  final String description;
  final ParameterType type;
  final String unit;
  final double? minValue;
  final double? maxValue;
  final String? expectedValue;
  final List<String>? allowedValues;
  final bool isRequired;
  final bool isCritical;

  const QualityParameter({
    required this.parameterName,
    required this.description,
    required this.type,
    required this.unit,
    this.minValue,
    this.maxValue,
    this.expectedValue,
    this.allowedValues,
    this.isRequired = true,
    this.isCritical = false,
  });

  factory QualityParameter.fromJson(Map<String, dynamic> json) {
    return QualityParameter(
      parameterName: json['parameterName'] as String,
      description: json['description'] as String,
      type: ParameterType.values.firstWhere(
        (e) => e.toString() == 'ParameterType.${json['type']}',
        orElse: () => ParameterType.text,
      ),
      unit: json['unit'] as String,
      minValue: json['minValue']?.toDouble(),
      maxValue: json['maxValue']?.toDouble(),
      expectedValue: json['expectedValue'] as String?,
      allowedValues: json['allowedValues'] != null
          ? List<String>.from(json['allowedValues'] as List)
          : null,
      isRequired: json['isRequired'] as bool? ?? true,
      isCritical: json['isCritical'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'parameterName': parameterName,
      'description': description,
      'type': type.toString().split('.').last,
      'unit': unit,
      'minValue': minValue,
      'maxValue': maxValue,
      'expectedValue': expectedValue,
      'allowedValues': allowedValues,
      'isRequired': isRequired,
      'isCritical': isCritical,
    };
  }

  @override
  List<Object?> get props => [
        parameterName,
        description,
        type,
        unit,
        minValue,
        maxValue,
        expectedValue,
        allowedValues,
        isRequired,
        isCritical,
      ];
}

/// Quality result entity
class QualityResult extends Equatable {
  final QualityResultType overallResult;
  final double qualityScore;
  final int totalCheckpoints;
  final int passedCheckpoints;
  final int failedCheckpoints;
  final int totalDefects;
  final int criticalDefects;
  final int majorDefects;
  final int minorDefects;
  final String? resultSummary;
  final Map<String, dynamic> detailedResults;

  const QualityResult({
    required this.overallResult,
    required this.qualityScore,
    required this.totalCheckpoints,
    required this.passedCheckpoints,
    required this.failedCheckpoints,
    required this.totalDefects,
    required this.criticalDefects,
    required this.majorDefects,
    required this.minorDefects,
    this.resultSummary,
    this.detailedResults = const {},
  });

  @override
  List<Object?> get props => [
        overallResult,
        qualityScore,
        totalCheckpoints,
        passedCheckpoints,
        failedCheckpoints,
        totalDefects,
        criticalDefects,
        majorDefects,
        minorDefects,
        resultSummary,
        detailedResults,
      ];

  /// Get pass rate percentage
  double get passRate {
    if (totalCheckpoints == 0) return 0.0;
    return (passedCheckpoints / totalCheckpoints) * 100;
  }

  /// Get defect rate
  double get defectRate {
    if (totalCheckpoints == 0) return 0.0;
    return (totalDefects / totalCheckpoints) * 100;
  }
}

/// Quality metrics entity
class QualityMetrics extends Equatable {
  final double inspectionTime;
  final double defectDensity;
  final double firstPassYield;
  final double reworkRate;
  final double customerSatisfaction;
  final Map<String, double> categoryScores;

  const QualityMetrics({
    required this.inspectionTime,
    required this.defectDensity,
    required this.firstPassYield,
    required this.reworkRate,
    required this.customerSatisfaction,
    this.categoryScores = const {},
  });

  @override
  List<Object?> get props => [
        inspectionTime,
        defectDensity,
        firstPassYield,
        reworkRate,
        customerSatisfaction,
        categoryScores,
      ];
}

/// Checkpoint result entity
class CheckpointResult extends Equatable {
  final QualityResultType result;
  final double? measuredValue;
  final String? textValue;
  final bool? booleanValue;
  final String? notes;
  final List<String> attachments;

  const CheckpointResult({
    required this.result,
    this.measuredValue,
    this.textValue,
    this.booleanValue,
    this.notes,
    this.attachments = const [],
  });

  @override
  List<Object?> get props => [result, measuredValue, textValue, booleanValue, notes, attachments];

  /// Check if result passed
  bool get isPassed => result == QualityResultType.passed;

  /// Check if result failed
  bool get isFailed => result == QualityResultType.failed;
}

// Enums

/// Inspection type enum
enum InspectionType {
  incoming,
  inProcess,
  final_,
  random,
  customer,
}

/// Inspection type extension
extension InspectionTypeExtension on InspectionType {
  String get displayName {
    switch (this) {
      case InspectionType.incoming:
        return 'Incoming';
      case InspectionType.inProcess:
        return 'In-Process';
      case InspectionType.final_:
        return 'Final';
      case InspectionType.random:
        return 'Random';
      case InspectionType.customer:
        return 'Customer';
    }
  }

  String get value => name;
}

/// Inspection stage enum
enum InspectionStage {
  materialReceiving,
  cutting,
  sewing,
  finishing,
  packaging,
  shipping,
}

/// Inspection status enum
enum InspectionStatus {
  scheduled,
  inProgress,
  completed,
  onHold,
  cancelled,
}

/// Quality result type enum
enum QualityResultType {
  passed,
  failed,
  conditional,
  pending,
}

/// Checkpoint type enum
enum CheckpointType {
  visual,
  measurement,
  functional,
  documentation,
}

/// Checkpoint category enum
enum CheckpointCategory {
  appearance,
  dimension,
  performance,
  safety,
  compliance,
}

/// Checkpoint status enum
enum CheckpointStatus {
  pending,
  inProgress,
  completed,
  skipped,
}

/// Defect category enum
enum DefectCategory {
  fabric,
  construction,
  finishing,
  packaging,
  labeling,
}

/// Defect type enum
enum DefectType {
  visual,
  functional,
  dimensional,
  material,
  workmanship,
}

/// Defect severity enum
enum DefectSeverity {
  critical,
  major,
  minor,
}

/// Defect severity extension
extension DefectSeverityExtension on DefectSeverity {
  String get displayName {
    switch (this) {
      case DefectSeverity.critical:
        return 'Critical';
      case DefectSeverity.major:
        return 'Major';
      case DefectSeverity.minor:
        return 'Minor';
    }
  }

  String get value => name;

  int get priority {
    switch (this) {
      case DefectSeverity.critical:
        return 3;
      case DefectSeverity.major:
        return 2;
      case DefectSeverity.minor:
        return 1;
    }
  }
}

/// Defect status enum
enum DefectStatus {
  open,
  inProgress,
  resolved,
  closed,
  rejected,
}

/// Standard type enum
enum StandardType {
  internal,
  industry,
  customer,
  regulatory,
}

/// Standard category enum
enum StandardCategory {
  material,
  process,
  product,
  safety,
  environmental,
}

/// Parameter type enum
enum ParameterType {
  numeric,
  text,
  boolean,
  selection,
  measurement,
}
