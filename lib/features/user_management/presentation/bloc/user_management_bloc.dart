import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../../auth/domain/entities/user.dart';
import '../../domain/entities/user_management.dart';
import '../../domain/usecases/user_management_usecases.dart';

part 'user_management_event.dart';
part 'user_management_state.dart';

/// User management Bloc
@injectable
class UserManagementBloc extends Bloc<UserManagementEvent, UserManagementState> {
  final GetUsersUseCase _getUsersUseCase;
  final GetUserByIdUseCase _getUserByIdUseCase;
  final CreateUserUseCase _createUserUseCase;
  final UpdateUserUseCase _updateUserUseCase;
  final DeleteUserUseCase _deleteUserUseCase;
  final ActivateUserUseCase _activateUserUseCase;
  final DeactivateUserUseCase _deactivateUserUseCase;
  final AssignRoleUseCase _assignRoleUseCase;
  final GrantPermissionsUseCase _grantPermissionsUseCase;
  final RevokePermissionsUseCase _revokePermissionsUseCase;
  final SearchUsersUseCase _searchUsersUseCase;
  final GetUserStatisticsUseCase _getUserStatisticsUseCase;
  final GetAvailableRolesUseCase _getAvailableRolesUseCase;
  final GetAvailablePermissionsUseCase _getAvailablePermissionsUseCase;

  UserManagementBloc(
    this._getUsersUseCase,
    this._getUserByIdUseCase,
    this._createUserUseCase,
    this._updateUserUseCase,
    this._deleteUserUseCase,
    this._activateUserUseCase,
    this._deactivateUserUseCase,
    this._assignRoleUseCase,
    this._grantPermissionsUseCase,
    this._revokePermissionsUseCase,
    this._searchUsersUseCase,
    this._getUserStatisticsUseCase,
    this._getAvailableRolesUseCase,
    this._getAvailablePermissionsUseCase,
  ) : super(const UserManagementInitial()) {
    on<LoadUsersRequested>(_onLoadUsersRequested);
    on<LoadUserDetailsRequested>(_onLoadUserDetailsRequested);
    on<CreateUserRequested>(_onCreateUserRequested);
    on<UpdateUserRequested>(_onUpdateUserRequested);
    on<DeleteUserRequested>(_onDeleteUserRequested);
    on<ActivateUserRequested>(_onActivateUserRequested);
    on<DeactivateUserRequested>(_onDeactivateUserRequested);
    on<AssignRoleRequested>(_onAssignRoleRequested);
    on<GrantPermissionsRequested>(_onGrantPermissionsRequested);
    on<RevokePermissionsRequested>(_onRevokePermissionsRequested);
    on<SearchUsersRequested>(_onSearchUsersRequested);
    on<LoadUserStatisticsRequested>(_onLoadUserStatisticsRequested);
    on<LoadAvailableRolesRequested>(_onLoadAvailableRolesRequested);
    on<LoadAvailablePermissionsRequested>(_onLoadAvailablePermissionsRequested);
    on<FilterUsersRequested>(_onFilterUsersRequested);
    on<RefreshUsersRequested>(_onRefreshUsersRequested);
  }

  Future<void> _onLoadUsersRequested(
    LoadUsersRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _getUsersUseCase(GetUsersParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(UsersLoaded(
        users: response.data ?? [],
        pagination: response.pagination,
        filter: event.filter,
      )),
    );
  }

  Future<void> _onLoadUserDetailsRequested(
    LoadUserDetailsRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _getUserByIdUseCase(IdParams(event.userId));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) {
        if (response.hasData) {
          emit(UserDetailsLoaded(response.data!));
        } else {
          emit(const UserManagementError('User not found'));
        }
      },
    );
  }

  Future<void> _onCreateUserRequested(
    CreateUserRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _createUserUseCase(CreateUserParams(event.request));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) {
        if (response.hasData) {
          emit(UserCreated(response.data!));
        } else {
          emit(const UserManagementError('Failed to create user'));
        }
      },
    );
  }

  Future<void> _onUpdateUserRequested(
    UpdateUserRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _updateUserUseCase(UpdateUserParams(event.request));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) {
        if (response.hasData) {
          emit(UserUpdated(response.data!));
        } else {
          emit(const UserManagementError('Failed to update user'));
        }
      },
    );
  }

  Future<void> _onDeleteUserRequested(
    DeleteUserRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _deleteUserUseCase(DeleteUserParams(
      event.userId,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(UserDeleted(event.userId)),
    );
  }

  Future<void> _onActivateUserRequested(
    ActivateUserRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _activateUserUseCase(IdParams(event.userId));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(UserActivated(event.userId)),
    );
  }

  Future<void> _onDeactivateUserRequested(
    DeactivateUserRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _deactivateUserUseCase(DeactivateUserParams(
      event.userId,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(UserDeactivated(event.userId)),
    );
  }

  Future<void> _onAssignRoleRequested(
    AssignRoleRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _assignRoleUseCase(AssignRoleParams(event.request));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(RoleAssigned(event.request.userId, event.request.role)),
    );
  }

  Future<void> _onGrantPermissionsRequested(
    GrantPermissionsRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _grantPermissionsUseCase(PermissionParams(
      event.userId,
      event.permissions,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(PermissionsGranted(event.userId, event.permissions)),
    );
  }

  Future<void> _onRevokePermissionsRequested(
    RevokePermissionsRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _revokePermissionsUseCase(PermissionParams(
      event.userId,
      event.permissions,
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(PermissionsRevoked(event.userId, event.permissions)),
    );
  }

  Future<void> _onSearchUsersRequested(
    SearchUsersRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _searchUsersUseCase(SearchUsersParams(
      event.query,
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(UsersSearched(
        users: response.data ?? [],
        query: event.query,
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onLoadUserStatisticsRequested(
    LoadUserStatisticsRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _getUserStatisticsUseCase(const NoParams());

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (statistics) => emit(UserStatisticsLoaded(statistics)),
    );
  }

  Future<void> _onLoadAvailableRolesRequested(
    LoadAvailableRolesRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    final result = await _getAvailableRolesUseCase(const NoParams());

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (roles) => emit(AvailableRolesLoaded(roles)),
    );
  }

  Future<void> _onLoadAvailablePermissionsRequested(
    LoadAvailablePermissionsRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    final result = await _getAvailablePermissionsUseCase(const NoParams());

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (permissions) => emit(AvailablePermissionsLoaded(permissions)),
    );
  }

  Future<void> _onFilterUsersRequested(
    FilterUsersRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    emit(const UserManagementLoading());

    final result = await _getUsersUseCase(GetUsersParams(
      filter: event.filter,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(UsersFiltered(
        users: response.data ?? [],
        filter: event.filter,
        pagination: response.pagination,
      )),
    );
  }

  Future<void> _onRefreshUsersRequested(
    RefreshUsersRequested event,
    Emitter<UserManagementState> emit,
  ) async {
    final currentState = state;
    UserFilterCriteria? filter;
    PaginationParams? pagination;

    if (currentState is UsersLoaded) {
      filter = currentState.filter;
      pagination = const PaginationParams(page: 1);
    } else if (currentState is UsersFiltered) {
      filter = currentState.filter;
      pagination = const PaginationParams(page: 1);
    }

    final result = await _getUsersUseCase(GetUsersParams(
      filter: filter,
      pagination: pagination,
    ));

    result.fold(
      (failure) => emit(UserManagementError(failure.message)),
      (response) => emit(UsersLoaded(
        users: response.data ?? [],
        pagination: response.pagination,
        filter: filter,
      )),
    );
  }
}
