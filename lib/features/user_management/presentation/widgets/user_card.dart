import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../auth/domain/entities/user.dart';

/// User card widget for displaying user information in lists
class UserCard extends StatelessWidget {
  final User user;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onToggleStatus;

  const UserCard({
    super.key,
    required this.user,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onToggleStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildAvatar(),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildUserInfo(context),
                  ),
                  _buildStatusChip(),
                  const SizedBox(width: 8),
                  _buildMenuButton(context),
                ],
              ),
              const SizedBox(height: 12),
              _buildUserDetails(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 24,
      backgroundColor: _getRoleColor(),
      backgroundImage: user.profileImageUrl != null 
          ? NetworkImage(user.profileImageUrl!)
          : null,
      child: user.profileImageUrl == null
          ? Text(
              user.initials,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            )
          : null,
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          user.displayName,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          user.email,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip() {
    final color = _getStatusColor();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        user.status.displayName,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMenuButton(BuildContext context) {
    return PopupMenuButton<String>(
      onSelected: (value) => _handleMenuAction(value, context),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'view',
          child: ListTile(
            leading: Icon(Icons.visibility),
            title: Text('View Details'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        if (onEdit != null)
          const PopupMenuItem(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit),
              title: Text('Edit'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        if (onToggleStatus != null)
          PopupMenuItem(
            value: 'toggle_status',
            child: ListTile(
              leading: Icon(
                user.isActive ? Icons.block : Icons.check_circle,
                color: user.isActive ? AppColors.error : AppColors.success,
              ),
              title: Text(user.isActive ? 'Deactivate' : 'Activate'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        if (onDelete != null)
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: AppColors.error),
              title: Text('Delete', style: TextStyle(color: AppColors.error)),
              contentPadding: EdgeInsets.zero,
            ),
          ),
      ],
      child: const Icon(Icons.more_vert),
    );
  }

  Widget _buildUserDetails(BuildContext context) {
    return Row(
      children: [
        _buildDetailChip(
          icon: Icons.work_outline,
          label: user.role.displayName,
          color: _getRoleColor(),
        ),
        const SizedBox(width: 8),
        _buildDetailChip(
          icon: Icons.business_outlined,
          label: user.department.displayName,
          color: _getDepartmentColor(),
        ),
        const Spacer(),
        if (user.lastLoginAt != null)
          Text(
            'Last login: ${_formatLastLogin()}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
      ],
    );
  }

  Widget _buildDetailChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (user.status) {
      case CommonStatus.active:
        return AppColors.success;
      case CommonStatus.inactive:
        return AppColors.textSecondary;
      case CommonStatus.pending:
        return AppColors.warning;
      case CommonStatus.suspended:
        return AppColors.error;
      case CommonStatus.archived:
        return AppColors.textSecondary;
    }
  }

  Color _getRoleColor() {
    switch (user.role) {
      case UserRole.administrator:
        return AppColors.error;
      case UserRole.merchandiser:
        return AppColors.primary;
      case UserRole.inventoryManager:
        return AppColors.warning;
      case UserRole.cuttingHead:
      case UserRole.sewingHead:
      case UserRole.finishingHead:
        return AppColors.info;
      case UserRole.sewingSupervisor:
        return AppColors.secondary;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getDepartmentColor() {
    switch (user.department) {
      case Department.merchandising:
        return AppColors.primary;
      case Department.cutting:
        return const Color(0xFF8B5CF6);
      case Department.sewing:
        return const Color(0xFF06B6D4);
      case Department.quality:
        return AppColors.error;
      case Department.finishing:
        return AppColors.success;
      case Department.warehouse:
        return AppColors.warning;
      case Department.administration:
        return AppColors.textSecondary;
    }
  }

  String _formatLastLogin() {
    if (user.lastLoginAt == null) return 'Never';
    
    final now = DateTime.now();
    final difference = now.difference(user.lastLoginAt!);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _handleMenuAction(String action, BuildContext context) {
    switch (action) {
      case 'view':
        onTap?.call();
        break;
      case 'edit':
        onEdit?.call();
        break;
      case 'toggle_status':
        onToggleStatus?.call();
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }
}

/// Compact user card for smaller spaces
class CompactUserCard extends StatelessWidget {
  final User user;
  final VoidCallback? onTap;

  const CompactUserCard({
    super.key,
    required this.user,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: _getRoleColor(),
                backgroundImage: user.profileImageUrl != null 
                    ? NetworkImage(user.profileImageUrl!)
                    : null,
                child: user.profileImageUrl == null
                    ? Text(
                        user.initials,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.displayName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      user.role.displayName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: user.isActive ? AppColors.success : AppColors.textSecondary,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getRoleColor() {
    switch (user.role) {
      case UserRole.administrator:
        return AppColors.error;
      case UserRole.merchandiser:
        return AppColors.primary;
      case UserRole.inventoryManager:
        return AppColors.warning;
      case UserRole.cuttingHead:
      case UserRole.sewingHead:
      case UserRole.finishingHead:
        return AppColors.info;
      case UserRole.sewingSupervisor:
        return AppColors.secondary;
      default:
        return AppColors.textSecondary;
    }
  }
}
