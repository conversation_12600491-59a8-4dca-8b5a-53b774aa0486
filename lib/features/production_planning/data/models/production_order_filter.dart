import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../presentation/widgets/production_search_bar.dart';

/// Filter criteria for querying production orders at the repository level
class ProductionOrderFilter extends Equatable {
  /// List of order statuses to filter by
  final List<ProductionOrderStatus>? statuses;

  /// Start date range for filtering orders
  final DateTime? startDate;

  /// End date range for filtering orders
  final DateTime? endDate;

  /// Department to filter orders by
  final DepartmentType? department;

  /// Search query to filter orders by name or description
  final String? searchQuery;

  const ProductionOrderFilter({
    this.statuses,
    this.startDate,
    this.endDate,
    this.department,
    this.searchQuery,
  });

  /// Creates a copy of this filter with the given fields replaced with the new values
  ProductionOrderFilter copyWith({
    List<ProductionOrderStatus>? statuses,
    DateTime? startDate,
    DateTime? endDate,
    DepartmentType? department,
    String? searchQuery,
  }) {
    return ProductionOrderFilter(
      statuses: statuses ?? this.statuses,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      department: department ?? this.department,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  /// Returns true if this filter has no filtering conditions set
  bool get isEmpty {
    return statuses == null &&
        startDate == null &&
        endDate == null &&
        department == null &&
        searchQuery == null;
  }

  @override
  List<Object?> get props => [
        statuses,
        startDate,
        endDate,
        department,
        searchQuery,
      ];
}
