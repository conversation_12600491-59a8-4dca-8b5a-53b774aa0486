import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/models/pagination.dart';
import '../../data/models/production_order_filter.dart';
import '../../data/repositories/production_repository_impl.dart';
import '../../domain/entities/production_entities.dart';
import '../bloc/production_bloc.dart';
import 'production_list_view.dart';
import 'production_kanban_view.dart';
import 'production_timeline_view.dart';

/// Real-time Production Orders Widget with Firebase Firestore streams
class RealTimeProductionOrders extends StatefulWidget {
  final ProductionOrderFilter? filter;
  final ProductionViewMode viewMode;
  final Function(ProductionOrder)? onOrderTap;
  final Function(String)? onOrderLongPress;
  final Function(String, ProductionStatus)? onStatusChanged;
  final VoidCallback? onRefresh;

  const RealTimeProductionOrders({
    super.key,
    this.filter,
    this.viewMode = ProductionViewMode.list,
    this.onOrderTap,
    this.onOrderLongPress,
    this.onStatusChanged,
    this.onRefresh,
  });

  @override
  State<RealTimeProductionOrders> createState() => _RealTimeProductionOrdersState();
}

class _RealTimeProductionOrdersState extends State<RealTimeProductionOrders> {
  late ProductionRepositoryImpl _repository;

  @override
  void initState() {
    super.initState();
    _repository = GetIt.instance<ProductionRepositoryImpl>();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ProductionOrder>>(
      stream: _repository.getProductionOrdersStream(
        filter: widget.filter,
        limit: 50,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          return _buildErrorWidget(snapshot.error.toString());
        }

        final orders = snapshot.data ?? [];

        if (orders.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            widget.onRefresh?.call();
            // The stream will automatically update
          },
          child: _buildOrdersView(orders),
        );
      },
    );
  }

  Widget _buildOrdersView(List<ProductionOrder> orders) {
    switch (widget.viewMode) {
      case ProductionViewMode.list:
        return ProductionListView(
          orders: orders,
          selectedOrderIds: const [], // You can manage selection state here
          onOrderTap: (orderId) {
            final order = orders.firstWhere((o) => o.id == orderId);
            widget.onOrderTap?.call(order);
          },
          onOrderLongPress: widget.onOrderLongPress,
          onSelectionChanged: (selectedIds) {
            // Handle selection changes
          },
          onStatusChanged: widget.onStatusChanged,
          onLoadMore: () {
            // Handle load more if needed
          },
          hasMore: false, // Implement pagination if needed
          isLoading: false,
        );
      case ProductionViewMode.kanban:
        return ProductionKanbanView(
          orders: orders,
          onOrderTap: widget.onOrderTap,
          onStatusChanged: (order, status) {
            widget.onStatusChanged?.call(order.id, status);
          },
        );
      case ProductionViewMode.timeline:
        return ProductionTimelineView(
          orders: orders,
          onOrderTap: widget.onOrderTap,
        );
      case ProductionViewMode.calendar:
        return _buildCalendarView(orders);
    }
  }

  Widget _buildCalendarView(List<ProductionOrder> orders) {
    return const Center(
      child: Text(
        'Calendar view coming soon',
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading production orders',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              widget.onRefresh?.call();
              setState(() {}); // Trigger rebuild to restart stream
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.precision_manufacturing_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Production Orders Found',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first production order to get started.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // Navigate to create production order
              context.read<ProductionBloc>().add(const NavigateToCreateProductionOrderRequested());
            },
            child: const Text('Create Production Order'),
          ),
        ],
      ),
    );
  }
}

/// Real-time Single Production Order Widget
class RealTimeProductionOrder extends StatefulWidget {
  final String orderId;
  final Widget Function(ProductionOrder order) builder;
  final Widget Function(String error)? errorBuilder;
  final Widget? loadingWidget;

  const RealTimeProductionOrder({
    super.key,
    required this.orderId,
    required this.builder,
    this.errorBuilder,
    this.loadingWidget,
  });

  @override
  State<RealTimeProductionOrder> createState() => _RealTimeProductionOrderState();
}

class _RealTimeProductionOrderState extends State<RealTimeProductionOrder> {
  late ProductionRepositoryImpl _repository;

  @override
  void initState() {
    super.initState();
    _repository = GetIt.instance<ProductionRepositoryImpl>();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ProductionOrder?>(
      stream: _repository.getProductionOrderStream(widget.orderId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return widget.loadingWidget ?? const CircularProgressIndicator();
        }

        if (snapshot.hasError) {
          return widget.errorBuilder?.call(snapshot.error.toString()) ??
              Text('Error: ${snapshot.error}');
        }

        final order = snapshot.data;
        if (order == null) {
          return widget.errorBuilder?.call('Production order not found') ??
              const Text('Production order not found');
        }

        return widget.builder(order);
      },
    );
  }
}

/// Production Order Status Badge with real-time updates
class RealTimeStatusBadge extends StatelessWidget {
  final String orderId;

  const RealTimeStatusBadge({
    super.key,
    required this.orderId,
  });

  @override
  Widget build(BuildContext context) {
    return RealTimeProductionOrder(
      orderId: orderId,
      builder: (order) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: order.status.color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: order.status.color),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              order.status.icon,
              size: 16,
              color: order.status.color,
            ),
            const SizedBox(width: 4),
            Text(
              order.status.displayName,
              style: TextStyle(
                color: order.status.color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
      loadingWidget: const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
      errorBuilder: (error) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey),
        ),
        child: const Text(
          'Unknown',
          style: TextStyle(
            color: Colors.grey,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}
