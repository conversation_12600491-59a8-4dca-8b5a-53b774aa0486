import 'package:flutter/material.dart';

import '../../domain/entities/financial_entities.dart';

/// A card widget that displays financial summary
class FinancialSummaryCard extends StatelessWidget {
  final FinancialSummary summary;
  final VoidCallback? onTap;

  const FinancialSummaryCard({
    Key? key,
    required this.summary,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Financial Summary',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildSummaryRow(
                context,
                'Total Revenue',
                '\$${summary.totalRevenue.toStringAsFixed(2)}',
                Icons.trending_up,
                Colors.green,
                summary.revenueChange,
              ),
              const SizedBox(height: 12),
              _buildSummaryRow(
                context,
                'Total Expenses',
                '\$${summary.totalExpenses.toStringAsFixed(2)}',
                Icons.trending_down,
                Colors.red,
                summary.expenseChange,
              ),
              const SizedBox(height: 12),
              _buildSummaryRow(
                context,
                'Net Profit',
                '\$${summary.netProfit.toStringAsFixed(2)}',
                Icons.account_balance_wallet,
                summary.netProfit >= 0 ? Colors.green : Colors.red,
                summary.profitChange,
              ),
              const SizedBox(height: 12),
              _buildSummaryRow(
                context,
                'Profit Margin',
                '${summary.profitMargin.toStringAsFixed(1)}%',
                Icons.percent,
                Colors.blue,
                summary.marginChange,
              ),
              const SizedBox(height: 16),
              _buildProfitIndicator(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
    double? change,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Row(
                children: [
                  Text(
                    value,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (change != null) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: (change >= 0 ? Colors.green : Colors.red).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            change >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                            size: 12,
                            color: change >= 0 ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${change.abs().toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 10,
                              color: change >= 0 ? Colors.green : Colors.red,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfitIndicator(BuildContext context) {
    final profitRatio = summary.totalRevenue > 0 ? summary.netProfit / summary.totalRevenue : 0.0;
    final isPositive = profitRatio >= 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Profitability',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              isPositive ? 'Profitable' : 'Loss',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isPositive ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: isPositive ? profitRatio.clamp(0.0, 1.0) : 0.0,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            isPositive ? Colors.green : Colors.red,
          ),
        ),
      ],
    );
  }
}

/// A grid of financial summary cards
class FinancialSummaryGrid extends StatelessWidget {
  final FinancialSummary summary;

  const FinancialSummaryGrid({
    Key? key,
    required this.summary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildSummaryCard(
          context,
          'Revenue',
          '\$${(summary.totalRevenue / 1000).toStringAsFixed(1)}K',
          Icons.trending_up,
          Colors.green,
          summary.revenueChange,
        ),
        _buildSummaryCard(
          context,
          'Expenses',
          '\$${(summary.totalExpenses / 1000).toStringAsFixed(1)}K',
          Icons.trending_down,
          Colors.red,
          summary.expenseChange,
        ),
        _buildSummaryCard(
          context,
          'Net Profit',
          '\$${(summary.netProfit / 1000).toStringAsFixed(1)}K',
          Icons.account_balance_wallet,
          summary.netProfit >= 0 ? Colors.green : Colors.red,
          summary.profitChange,
        ),
        _buildSummaryCard(
          context,
          'Margin',
          '${summary.profitMargin.toStringAsFixed(1)}%',
          Icons.percent,
          Colors.blue,
          summary.marginChange,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    double? change,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                if (change != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: (change >= 0 ? Colors.green : Colors.red).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          change >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                          size: 10,
                          color: change >= 0 ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${change.abs().toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 8,
                            color: change >= 0 ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Financial KPI cards
class FinancialKPICards extends StatelessWidget {
  final List<FinancialKPI> kpis;

  const FinancialKPICards({
    Key? key,
    required this.kpis,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: kpis.length,
        itemBuilder: (context, index) {
          final kpi = kpis[index];
          return Container(
            width: 160,
            margin: EdgeInsets.only(
              left: index == 0 ? 16 : 8,
              right: index == kpis.length - 1 ? 16 : 8,
            ),
            child: Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      kpi.value,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: kpi.color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      kpi.title,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (kpi.trend != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            kpi.trend! > 0 ? Icons.trending_up : Icons.trending_down,
                            size: 16,
                            color: kpi.trend! > 0 ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${kpi.trend!.abs().toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 12,
                              color: kpi.trend! > 0 ? Colors.green : Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Monthly financial comparison card
class MonthlyComparisonCard extends StatelessWidget {
  final MonthlyFinancialData currentMonth;
  final MonthlyFinancialData previousMonth;

  const MonthlyComparisonCard({
    Key? key,
    required this.currentMonth,
    required this.previousMonth,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final revenueChange = _calculatePercentageChange(
      previousMonth.revenue,
      currentMonth.revenue,
    );
    final expenseChange = _calculatePercentageChange(
      previousMonth.expenses,
      currentMonth.expenses,
    );
    final profitChange = _calculatePercentageChange(
      previousMonth.profit,
      currentMonth.profit,
    );

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Comparison',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildComparisonRow(
              context,
              'Revenue',
              currentMonth.revenue,
              previousMonth.revenue,
              revenueChange,
              Colors.green,
            ),
            const SizedBox(height: 12),
            _buildComparisonRow(
              context,
              'Expenses',
              currentMonth.expenses,
              previousMonth.expenses,
              expenseChange,
              Colors.red,
            ),
            const SizedBox(height: 12),
            _buildComparisonRow(
              context,
              'Profit',
              currentMonth.profit,
              previousMonth.profit,
              profitChange,
              currentMonth.profit >= 0 ? Colors.green : Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonRow(
    BuildContext context,
    String label,
    double currentValue,
    double previousValue,
    double change,
    Color color,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                '\$${currentValue.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: (change >= 0 ? Colors.green : Colors.red).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                change >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                size: 14,
                color: change >= 0 ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 4),
              Text(
                '${change.abs().toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 12,
                  color: change >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  double _calculatePercentageChange(double oldValue, double newValue) {
    if (oldValue == 0) return newValue > 0 ? 100.0 : 0.0;
    return ((newValue - oldValue) / oldValue) * 100;
  }
}

class FinancialKPI {
  final String title;
  final String value;
  final Color color;
  final double? trend;

  const FinancialKPI({
    required this.title,
    required this.value,
    required this.color,
    this.trend,
  });
}

class MonthlyFinancialData {
  final String month;
  final double revenue;
  final double expenses;
  final double profit;

  const MonthlyFinancialData({
    required this.month,
    required this.revenue,
    required this.expenses,
    required this.profit,
  });
}