part of 'dashboard_bloc.dart';

/// Base dashboard state
abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

/// Initial dashboard state
class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

/// Dashboard loading state
class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

/// Dashboard loaded state
class DashboardLoaded extends DashboardState {
  final DashboardLayout layout;
  final List<DashboardStatistics> statistics;
  final List<DashboardChartData> charts;
  final List<DashboardActivity> recentActivities;
  final List<DashboardNotification> notifications;
  final List<QuickAction> quickActions;
  final bool isRefreshing;
  final DateTimeRange? dateFilter;
  final Department? departmentFilter;

  const DashboardLoaded({
    required this.layout,
    required this.statistics,
    required this.charts,
    required this.recentActivities,
    required this.notifications,
    required this.quickActions,
    this.isRefreshing = false,
    this.dateFilter,
    this.departmentFilter,
  });

  /// Copy with new values
  DashboardLoaded copyWith({
    DashboardLayout? layout,
    List<DashboardStatistics>? statistics,
    List<DashboardChartData>? charts,
    List<DashboardActivity>? recentActivities,
    List<DashboardNotification>? notifications,
    List<QuickAction>? quickActions,
    bool? isRefreshing,
    DateTimeRange? dateFilter,
    Department? departmentFilter,
  }) {
    return DashboardLoaded(
      layout: layout ?? this.layout,
      statistics: statistics ?? this.statistics,
      charts: charts ?? this.charts,
      recentActivities: recentActivities ?? this.recentActivities,
      notifications: notifications ?? this.notifications,
      quickActions: quickActions ?? this.quickActions,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      dateFilter: dateFilter ?? this.dateFilter,
      departmentFilter: departmentFilter ?? this.departmentFilter,
    );
  }

  @override
  List<Object?> get props => [
        layout,
        statistics,
        charts,
        recentActivities,
        notifications,
        quickActions,
        isRefreshing,
        dateFilter,
        departmentFilter,
      ];
}

/// Dashboard error state
class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Widget data loaded state
class WidgetDataLoaded extends DashboardState {
  final String widgetId;
  final Map<String, dynamic> data;

  const WidgetDataLoaded(this.widgetId, this.data);

  @override
  List<Object?> get props => [widgetId, data];
}

/// Analytics data loaded state
class AnalyticsLoaded extends DashboardState {
  final AnalyticsType type;
  final Map<String, dynamic> data;

  const AnalyticsLoaded(this.type, this.data);

  @override
  List<Object?> get props => [type, data];
}

/// Dashboard layout updated state
class DashboardLayoutUpdated extends DashboardState {
  final DashboardLayout layout;

  const DashboardLayoutUpdated(this.layout);

  @override
  List<Object?> get props => [layout];
}

/// Widget added state
class WidgetAdded extends DashboardState {
  final DashboardWidget widget;

  const WidgetAdded(this.widget);

  @override
  List<Object?> get props => [widget];
}

/// Widget removed state
class WidgetRemoved extends DashboardState {
  final String widgetId;

  const WidgetRemoved(this.widgetId);

  @override
  List<Object?> get props => [widgetId];
}

/// Widgets reordered state
class WidgetsReordered extends DashboardState {
  final List<DashboardWidget> widgets;

  const WidgetsReordered(this.widgets);

  @override
  List<Object?> get props => [widgets];
}

/// Widget configuration updated state
class WidgetConfigurationUpdated extends DashboardState {
  final String widgetId;
  final Map<String, dynamic> configuration;

  const WidgetConfigurationUpdated(this.widgetId, this.configuration);

  @override
  List<Object?> get props => [widgetId, configuration];
}

/// Widget visibility toggled state
class WidgetVisibilityToggled extends DashboardState {
  final String widgetId;
  final bool isVisible;

  const WidgetVisibilityToggled(this.widgetId, this.isVisible);

  @override
  List<Object?> get props => [widgetId, isVisible];
}

/// Dashboard data exported state
class DashboardDataExported extends DashboardState {
  final String filePath;
  final String format;

  const DashboardDataExported(this.filePath, this.format);

  @override
  List<Object?> get props => [filePath, format];
}

/// Notification marked as read state
class NotificationMarkedAsRead extends DashboardState {
  final String notificationId;

  const NotificationMarkedAsRead(this.notificationId);

  @override
  List<Object?> get props => [notificationId];
}

/// Dashboard refreshing state
class DashboardRefreshing extends DashboardState {
  const DashboardRefreshing();
}

/// Dashboard filter applied state
class DashboardFilterApplied extends DashboardState {
  final DateTimeRange? dateFilter;
  final Department? departmentFilter;

  const DashboardFilterApplied({
    this.dateFilter,
    this.departmentFilter,
  });

  @override
  List<Object?> get props => [dateFilter, departmentFilter];
}
