import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/collaboration_entities.dart';
import '../../domain/repositories/collaboration_repository.dart';
import '../datasources/collaboration_datasource.dart';

/// Collaboration repository implementation
@LazySingleton(as: CollaborationRepository)
class CollaborationRepositoryImpl implements CollaborationRepository {
  final CollaborationDataSource _dataSource;

  const CollaborationRepositoryImpl(this._dataSource);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationWorkspace>>> getWorkspaces({
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getWorkspaces(
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> getWorkspaceById(String workspaceId) async {
    try {
      final result = await _dataSource.getWorkspaceById(workspaceId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> createWorkspace(CreateWorkspaceRequest request) async {
    try {
      final result = await _dataSource.createWorkspace(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> updateWorkspace(UpdateWorkspaceRequest request) async {
    try {
      final result = await _dataSource.updateWorkspace(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteWorkspace(String workspaceId) async {
    try {
      final result = await _dataSource.deleteWorkspace(workspaceId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> archiveWorkspace(String workspaceId) async {
    try {
      final result = await _dataSource.archiveWorkspace(workspaceId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> restoreWorkspace(String workspaceId) async {
    try {
      final result = await _dataSource.restoreWorkspace(workspaceId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<WorkspaceMember>>> addMember(AddMemberRequest request) async {
    try {
      final result = await _dataSource.addMember(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<WorkspaceMember>>> updateMember(UpdateMemberRequest request) async {
    try {
      final result = await _dataSource.updateMember(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> removeMember(String workspaceId, String userId) async {
    try {
      final result = await _dataSource.removeMember(workspaceId, userId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<WorkspaceMember>>> getWorkspaceMembers({
    required String workspaceId,
    MemberFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getWorkspaceMembers(
        workspaceId: workspaceId,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<WorkspaceMember>>> inviteMember(InviteMemberRequest request) async {
    try {
      final result = await _dataSource.inviteMember(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> acceptInvitation(String invitationId) async {
    try {
      final result = await _dataSource.acceptInvitation(invitationId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> declineInvitation(String invitationId) async {
    try {
      final result = await _dataSource.declineInvitation(invitationId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<CollaborationChannel>>> getChannels({
    required String workspaceId,
    ChannelFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getChannels(
        workspaceId: workspaceId,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> getChannelById(String channelId) async {
    try {
      final result = await _dataSource.getChannelById(channelId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> createChannel(CreateChannelRequest request) async {
    try {
      final result = await _dataSource.createChannel(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> updateChannel(UpdateChannelRequest request) async {
    try {
      final result = await _dataSource.updateChannel(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteChannel(String channelId) async {
    try {
      final result = await _dataSource.deleteChannel(channelId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> archiveChannel(String channelId) async {
    try {
      final result = await _dataSource.archiveChannel(channelId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> joinChannel(String channelId) async {
    try {
      final result = await _dataSource.joinChannel(channelId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> leaveChannel(String channelId) async {
    try {
      final result = await _dataSource.leaveChannel(channelId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> getMessages({
    required String channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getMessages(
        channelId: channelId,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> getMessageById(String messageId) async {
    try {
      final result = await _dataSource.getMessageById(messageId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> sendMessage(SendMessageRequest request) async {
    try {
      final result = await _dataSource.sendMessage(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> editMessage(EditMessageRequest request) async {
    try {
      final result = await _dataSource.editMessage(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteMessage(String messageId) async {
    try {
      final result = await _dataSource.deleteMessage(messageId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> pinMessage(String messageId) async {
    try {
      final result = await _dataSource.pinMessage(messageId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> unpinMessage(String messageId) async {
    try {
      final result = await _dataSource.unpinMessage(messageId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> addReaction(AddReactionRequest request) async {
    try {
      final result = await _dataSource.addReaction(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> removeReaction(RemoveReactionRequest request) async {
    try {
      final result = await _dataSource.removeReaction(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> markMessageAsRead(String messageId) async {
    try {
      final result = await _dataSource.markMessageAsRead(messageId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> markChannelAsRead(String channelId) async {
    try {
      final result = await _dataSource.markChannelAsRead(channelId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> getThreadMessages({
    required String parentMessageId,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getThreadMessages(
        parentMessageId: parentMessageId,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> replyToThread(ReplyToThreadRequest request) async {
    try {
      final result = await _dataSource.replyToThread(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<MessageAttachment>>> uploadFile(UploadFileRequest request) async {
    try {
      final result = await _dataSource.uploadFile(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> getFileDownloadUrl(String attachmentId) async {
    try {
      final result = await _dataSource.getFileDownloadUrl(attachmentId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteFile(String attachmentId) async {
    try {
      final result = await _dataSource.deleteFile(attachmentId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<MessageAttachment>>> getChannelFiles({
    required String channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getChannelFiles(
        channelId: channelId,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Stream<CollaborationMessage> getMessageStream(String channelId) {
    return _dataSource.getMessageStream(channelId);
  }

  @override
  Stream<RealTimeActivity> getActivityStream(String workspaceId) {
    return _dataSource.getActivityStream(workspaceId);
  }

  @override
  Stream<List<WorkspaceMember>> getOnlineMembersStream(String workspaceId) {
    return _dataSource.getOnlineMembersStream(workspaceId);
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> updateUserPresence(UpdatePresenceRequest request) async {
    try {
      final result = await _dataSource.updateUserPresence(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> sendTypingIndicator(TypingIndicatorRequest request) async {
    try {
      final result = await _dataSource.sendTypingIndicator(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<CollaborationSession>>> getSessions({
    required String workspaceId,
    SessionFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getSessions(
        workspaceId: workspaceId,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> getSessionById(String sessionId) async {
    try {
      final result = await _dataSource.getSessionById(sessionId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> createSession(CreateSessionRequest request) async {
    try {
      final result = await _dataSource.createSession(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> updateSession(UpdateSessionRequest request) async {
    try {
      final result = await _dataSource.updateSession(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteSession(String sessionId) async {
    try {
      final result = await _dataSource.deleteSession(sessionId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> startSession(String sessionId) async {
    try {
      final result = await _dataSource.startSession(sessionId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> endSession(String sessionId) async {
    try {
      final result = await _dataSource.endSession(sessionId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<SessionParticipant>>> joinSession(JoinSessionRequest request) async {
    try {
      final result = await _dataSource.joinSession(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> leaveSession(String sessionId) async {
    try {
      final result = await _dataSource.leaveSession(sessionId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<SessionParticipant>>> updateParticipant(UpdateParticipantRequest request) async {
    try {
      final result = await _dataSource.updateParticipant(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<CollaborationWorkspace>>> searchWorkspaces({
    required String query,
    WorkspaceFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.searchWorkspaces(
        query: query,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> searchMessages({
    required String query,
    required String workspaceId,
    String? channelId,
    MessageFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.searchMessages(
        query: query,
        workspaceId: workspaceId,
        channelId: channelId,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<MessageAttachment>>> searchFiles({
    required String query,
    required String workspaceId,
    String? channelId,
    FileFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.searchFiles(
        query: query,
        workspaceId: workspaceId,
        channelId: channelId,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getWorkspaceAnalytics({
    required String workspaceId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final result = await _dataSource.getWorkspaceAnalytics(
        workspaceId: workspaceId,
        startDate: startDate,
        endDate: endDate,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getChannelAnalytics({
    required String channelId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final result = await _dataSource.getChannelAnalytics(
        channelId: channelId,
        startDate: startDate,
        endDate: endDate,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUserActivityAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final result = await _dataSource.getUserActivityAnalytics(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<RealTimeActivity>>> getNotifications({
    NotificationFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getNotifications(
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> markNotificationAsRead(String activityId) async {
    try {
      final result = await _dataSource.markNotificationAsRead(activityId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> markAllNotificationsAsRead() async {
    try {
      final result = await _dataSource.markAllNotificationsAsRead();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> updateNotificationSettings(UpdateNotificationSettingsRequest request) async {
    try {
      final result = await _dataSource.updateNotificationSettings(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
