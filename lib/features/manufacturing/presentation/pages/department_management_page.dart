import 'package:flutter/material.dart';

import '../../../../core/auth/widgets/permission_guard.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../domain/entities/manufacturing_entities.dart';
import '../widgets/department_card_widget.dart';

/// Department management page for cloth manufacturing
class DepartmentManagementPage extends StatefulWidget {
  const DepartmentManagementPage({super.key});

  @override
  State<DepartmentManagementPage> createState() => _DepartmentManagementPageState();
}

class _DepartmentManagementPageState extends State<DepartmentManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DepartmentType? _selectedDepartmentFilter;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Department Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
            tooltip: 'Filter Departments',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(),
            tooltip: 'Search Departments',
          ),
          PermissionGuard(
            permission: 'manufacturing.departments.create',
            child: IconButton(
              icon: const Icon(Icons.add_business),
              onPressed: () => _showCreateDepartmentDialog(),
              tooltip: 'Create Department',
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildDepartmentsTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: PermissionGuard(
        permission: 'manufacturing.departments.create',
        child: FloatingActionButton(
          onPressed: () => _showCreateDepartmentDialog(),
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.business),
            text: 'Departments',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'Analytics',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manufacturing Departments Overview',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Department Statistics
          const DepartmentStatsWidget(),
          
          const SizedBox(height: 24),
          
          // Production Flow
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Production Flow',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildProductionFlow(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Active Departments Summary
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Active Departments',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => _tabController.animateTo(1),
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildActiveDepartmentsList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartmentsTab() {
    return PermissionGuard(
      permission: 'manufacturing.departments.read',
      fallback: const Center(
        child: Text('You do not have permission to view departments'),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Manufacturing Departments',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    if (_selectedDepartmentFilter != null)
                      Chip(
                        label: Text(_selectedDepartmentFilter!.displayName),
                        onDeleted: () => setState(() => _selectedDepartmentFilter = null),
                        deleteIcon: const Icon(Icons.close, size: 16),
                      ),
                    const SizedBox(width: 8),
                    PermissionGuard(
                      permission: 'manufacturing.departments.create',
                      child: ElevatedButton.icon(
                        onPressed: () => _showCreateDepartmentDialog(),
                        icon: const Icon(Icons.add),
                        label: const Text('Add Department'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: _buildDepartmentGrid(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Department Analytics',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Performance Metrics
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Overall Efficiency',
                  '87.5%',
                  Icons.trending_up,
                  AppColors.success,
                  '+5.2% vs last month',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Active Workers',
                  '156',
                  Icons.people,
                  AppColors.primary,
                  '12 departments',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Tasks Completed',
                  '1,234',
                  Icons.task_alt,
                  AppColors.warning,
                  'Today: 89',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Quality Score',
                  '96.2%',
                  Icons.verified,
                  AppColors.error,
                  '+2.1% improvement',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Department Performance Chart
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Department Performance',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 300,
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text('Department performance charts will be displayed here'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductionFlow() {
    final flowSteps = [
      'Fabric → Cutting → Numbering → Bundling',
      'Singer → Overlock → Side Sewing → Belt',
      'Bottom Heming → Luppi → I-Kaaj → Washing',
      'Thread Cutting → Labeling → Pressing → Packing',
      'Quality Check → Dispatch'
    ];

    return Column(
      children: flowSteps.map((step) => Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.primary.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            const Icon(Icons.arrow_forward, color: AppColors.primary, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                step,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildActiveDepartmentsList() {
    // Mock data for active departments
    final activeDepartments = [
      {'name': 'Cutting', 'workers': 12, 'efficiency': 92.5, 'status': 'Active'},
      {'name': 'Stitching', 'workers': 25, 'efficiency': 88.3, 'status': 'Active'},
      {'name': 'Quality Control', 'workers': 8, 'efficiency': 95.1, 'status': 'Active'},
      {'name': 'Packing', 'workers': 15, 'efficiency': 89.7, 'status': 'Active'},
    ];

    return Column(
      children: activeDepartments.map((dept) => ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.success.withOpacity(0.1),
          child: const Icon(Icons.business, color: AppColors.success),
        ),
        title: Text(dept['name'] as String),
        subtitle: Text('${dept['workers']} workers • ${dept['efficiency']}% efficiency'),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.success.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            dept['status'] as String,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.success,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        onTap: () => _viewDepartmentDetails(dept['name'] as String),
      )).toList(),
    );
  }

  Widget _buildDepartmentGrid() {
    // Mock departments data
    final departments = _getMockDepartments();
    final filteredDepartments = departments.where((dept) {
      if (_selectedDepartmentFilter != null && dept.type != _selectedDepartmentFilter) {
        return false;
      }
      if (_searchQuery.isNotEmpty && !dept.name.toLowerCase().contains(_searchQuery.toLowerCase())) {
        return false;
      }
      return true;
    }).toList();

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: filteredDepartments.length,
      itemBuilder: (context, index) {
        final department = filteredDepartments[index];
        return DepartmentCardWidget(
          department: department,
          onTap: () => _viewDepartmentDetails(department.name),
          onEdit: () => _editDepartment(department),
          onDelete: () => _deleteDepartment(department),
        );
      },
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ManufacturingDepartment> _getMockDepartments() {
    return [
      ManufacturingDepartment(
        id: '1',
        name: 'Fabric Department',
        description: 'Fabric receiving and quality check',
        supervisorId: 'sup_1',
        supervisorName: 'John Smith',
        type: DepartmentType.fabric,
        status: DepartmentStatus.active,
        workerIds: ['w1', 'w2', 'w3'],
        machineIds: ['m1', 'm2'],
        capacity: 5,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      // Add more mock departments...
    ];
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Departments'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<DepartmentType>(
              value: _selectedDepartmentFilter,
              decoration: const InputDecoration(labelText: 'Department Type'),
              items: DepartmentType.values.map((type) => DropdownMenuItem(
                value: type,
                child: Text(type.displayName),
              )).toList(),
              onChanged: (value) => setState(() => _selectedDepartmentFilter = value),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {});
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Departments'),
        content: TextField(
          decoration: const InputDecoration(
            labelText: 'Search by name',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) => _searchQuery = value,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {});
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showCreateDepartmentDialog() {
    // Implement create department dialog
  }

  void _viewDepartmentDetails(String departmentName) {
    // Navigate to department details page
  }

  void _editDepartment(ManufacturingDepartment department) {
    // Implement edit department
  }

  void _deleteDepartment(ManufacturingDepartment department) {
    // Implement delete department
  }
}
