import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Quality Controller Dashboard Page - Quality inspection and control
class QualityControllerDashboardPage extends StatelessWidget {
  const QualityControllerDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quality Controller Dashboard'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildQualityControllerDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildQualityControllerDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildQualityOverview(),
          const SizedBox(height: 24),
          _buildQualityActions(context),
          const SizedBox(height: 24),
          _buildInspectionQueue(),
          const SizedBox(height: 24),
          _buildDefectAnalysis(),
          const SizedBox(height: 24),
          _buildQualityTrends(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red,
            Colors.red.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Quality Controller'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Quality inspection and control',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.verified,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildQualityOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quality Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Inspections Today',
              value: '45',
              icon: Icons.search,
              color: Colors.blue,
              trend: '+8',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Pass Rate',
              value: '94%',
              icon: Icons.check_circle,
              color: Colors.green,
              trend: '+2%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Defects Found',
              value: '12',
              icon: Icons.error,
              color: Colors.red,
              trend: '-3',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Pending Queue',
              value: '8',
              icon: Icons.pending,
              color: Colors.orange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQualityActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quality Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Start Inspection',
              subtitle: 'Begin quality inspection',
              icon: Icons.search,
              color: Colors.blue,
              onTap: () {
                // Navigate to start inspection
              },
            ),
            DashboardCard(
              title: 'Log Defects',
              subtitle: 'Record quality issues',
              icon: Icons.error_outline,
              color: Colors.red,
              onTap: () {
                // Navigate to defect logging
              },
            ),
            DashboardCard(
              title: 'Approve Batch',
              subtitle: 'Approve quality batch',
              icon: Icons.check_circle,
              color: Colors.green,
              onTap: () {
                // Navigate to batch approval
              },
            ),
            DashboardCard(
              title: 'Quality Reports',
              subtitle: 'Generate quality reports',
              icon: Icons.analytics,
              color: Colors.purple,
              onTap: () {
                // Navigate to quality reports
              },
            ),
            DashboardCard(
              title: 'Standards Check',
              subtitle: 'Verify quality standards',
              icon: Icons.rule,
              color: Colors.orange,
              onTap: () {
                // Navigate to standards check
              },
            ),
            DashboardCard(
              title: 'Training Materials',
              subtitle: 'Access quality guidelines',
              icon: Icons.school,
              color: Colors.teal,
              onTap: () {
                // Navigate to training materials
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInspectionQueue() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.queue,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Inspection Queue',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '8 Pending',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildQueueItem(
              'QC-001',
              'Shirt batch - Order #1234',
              'High',
              'Pending',
              Colors.orange,
            ),
            _buildQueueItem(
              'QC-002',
              'Trouser batch - Order #1235',
              'Medium',
              'In Progress',
              Colors.blue,
            ),
            _buildQueueItem(
              'QC-003',
              'Dress batch - Order #1236',
              'High',
              'Completed',
              Colors.green,
            ),
            _buildQueueItem(
              'QC-004',
              'Jacket batch - Order #1237',
              'Low',
              'Pending',
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQueueItem(String qcId, String description, String priority, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  qcId,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getPriorityColor(priority).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              priority,
              style: TextStyle(
                color: _getPriorityColor(priority),
                fontWeight: FontWeight.w500,
                fontSize: 10,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget _buildDefectAnalysis() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.analytics,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Defect Analysis',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to detailed analysis
                  },
                  child: const Text('View Details'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDefectItem('Stitching Issues', '45%', Colors.red, 0.45),
            const SizedBox(height: 8),
            _buildDefectItem('Fabric Defects', '25%', Colors.orange, 0.25),
            const SizedBox(height: 8),
            _buildDefectItem('Sizing Problems', '20%', Colors.blue, 0.20),
            const SizedBox(height: 8),
            _buildDefectItem('Color Variations', '10%', Colors.purple, 0.10),
          ],
        ),
      ),
    );
  }

  Widget _buildDefectItem(String defectType, String percentage, Color color, double progress) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            defectType,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 3,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          percentage,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildQualityTrends() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.trending_up,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Quality Trends',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Improving',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTrendCard('Today', '94%', '+2%', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildTrendCard('This Week', '92%', '+1%', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildTrendCard('This Month', '90%', '+3%', Colors.purple),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildTrendCard('Target', '95%', 'Goal', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendCard(String period, String percentage, String trend, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            percentage,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            period,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            trend,
            style: TextStyle(
              fontSize: 9,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
