# API Design Specification - Cloth Manufacturing App

## Overview

This document outlines the RESTful API design for the Cloth Manufacturing App, including endpoints, request/response formats, authentication, and error handling.

## Table of Contents

1. [API Overview](#api-overview)
2. [Authentication](#authentication)
3. [Common Response Formats](#common-response-formats)
4. [<PERSON><PERSON><PERSON> Handling](#error-handling)
5. [Pagination](#pagination)
6. [API Endpoints](#api-endpoints)
7. [WebSocket Events](#websocket-events)
8. [Rate Limiting](#rate-limiting)

---

## API Overview

### Base URL
- **Development**: `https://dev-api.hmcollection.com/v1`
- **Staging**: `https://staging-api.hmcollection.com/v1`
- **Production**: `https://api.hmcollection.com/v1`

### Content Type
- **Request**: `application/json`
- **Response**: `application/json`

### HTTP Methods
- **GET**: Retrieve data
- **POST**: Create new resources
- **PUT**: Update existing resources (full update)
- **PATCH**: Partial update of resources
- **DELETE**: Remove resources

---

## Authentication

### JWT Token Authentication

#### Login Request
```http
POST /auth/login
Content-Type: application/json

{
  "username": "john.doe",
  "password": "securePassword123",
  "deviceId": "device-uuid-123",
  "deviceInfo": {
    "platform": "android",
    "version": "12",
    "model": "Samsung Galaxy S21"
  }
}
```

#### Login Response
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "username": "john.doe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": {
        "id": "role-456",
        "name": "Sewing Supervisor",
        "permissions": ["view_production", "update_tasks", "assign_operators"]
      },
      "department": {
        "id": "dept-789",
        "name": "Sewing Department",
        "code": "SEW"
      },
      "profileImage": "https://cdn.example.com/profiles/john.jpg"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 900,
      "tokenType": "Bearer"
    }
  },
  "message": "Login successful",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Token Refresh
```http
POST /auth/refresh
Authorization: Bearer <refresh_token>

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Authorization Header
```http
Authorization: Bearer <access_token>
```

---

## Common Response Formats

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format",
      "value": "invalid-email"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req-123-456-789"
}
```

---

## Error Handling

### HTTP Status Codes

| Status Code | Description | Usage |
|-------------|-------------|-------|
| 200 | OK | Successful GET, PUT, PATCH |
| 201 | Created | Successful POST |
| 204 | No Content | Successful DELETE |
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource conflict |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |

### Error Codes

| Error Code | Description |
|------------|-------------|
| `VALIDATION_ERROR` | Input validation failed |
| `AUTHENTICATION_FAILED` | Invalid credentials |
| `AUTHORIZATION_FAILED` | Insufficient permissions |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `RESOURCE_CONFLICT` | Resource already exists |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `SERVER_ERROR` | Internal server error |

---

## Pagination

### Query Parameters
```http
GET /orders?page=1&limit=20&sort=createdAt&order=desc
```

### Pagination Response
```json
{
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrevious": false,
    "links": {
      "first": "/orders?page=1&limit=20",
      "last": "/orders?page=8&limit=20",
      "next": "/orders?page=2&limit=20",
      "previous": null
    }
  }
}
```

---

## API Endpoints

### Authentication Endpoints

#### Login
```http
POST /auth/login
```

#### Logout
```http
POST /auth/logout
Authorization: Bearer <access_token>
```

#### Refresh Token
```http
POST /auth/refresh
```

#### Get Profile
```http
GET /auth/profile
Authorization: Bearer <access_token>
```

#### Update Profile
```http
PUT /auth/profile
Authorization: Bearer <access_token>

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890"
}
```

### Order Management Endpoints

#### Get Orders
```http
GET /orders?page=1&limit=20&status=pending&customer=123
Authorization: Bearer <access_token>
```

#### Get Order Details
```http
GET /orders/{orderId}
Authorization: Bearer <access_token>
```

#### Create Order
```http
POST /orders
Authorization: Bearer <access_token>

{
  "customerId": "customer-123",
  "orderNumber": "ORD-2024-001",
  "deliveryDate": "2024-02-15",
  "priority": "high",
  "items": [
    {
      "productId": "product-456",
      "quantity": 100,
      "specifications": {
        "size": "M",
        "color": "Blue",
        "fabric": "Cotton"
      },
      "unitPrice": 25.50
    }
  ],
  "notes": "Rush order for premium customer"
}
```

#### Update Order
```http
PUT /orders/{orderId}
Authorization: Bearer <access_token>

{
  "status": "in_production",
  "deliveryDate": "2024-02-20",
  "notes": "Updated delivery date as requested"
}
```

#### Update Order Status
```http
PATCH /orders/{orderId}/status
Authorization: Bearer <access_token>

{
  "status": "completed",
  "notes": "Order completed successfully"
}
```

### Production Management Endpoints

#### Get Production Dashboard
```http
GET /production/dashboard
Authorization: Bearer <access_token>
```

#### Get Production Orders
```http
GET /production/orders?status=active&department=sewing
Authorization: Bearer <access_token>
```

#### Create Production Order
```http
POST /production/orders
Authorization: Bearer <access_token>

{
  "orderId": "order-123",
  "plannedStartDate": "2024-01-20",
  "plannedEndDate": "2024-01-25",
  "priority": "high",
  "departments": ["cutting", "sewing", "finishing"]
}
```

#### Get Tasks
```http
GET /production/tasks?assignedTo=user-123&status=in_progress
Authorization: Bearer <access_token>
```

#### Create Task
```http
POST /production/tasks
Authorization: Bearer <access_token>

{
  "productionOrderId": "prod-order-456",
  "title": "Cut fabric for Order ORD-2024-001",
  "description": "Cut 100 pieces according to marker plan",
  "assignedTo": "user-789",
  "workstationId": "workstation-101",
  "estimatedDuration": 240,
  "priority": "high",
  "dependencies": []
}
```

#### Update Task Status
```http
PATCH /production/tasks/{taskId}/status
Authorization: Bearer <access_token>

{
  "status": "completed",
  "actualDuration": 220,
  "notes": "Task completed ahead of schedule",
  "qualityCheck": {
    "passed": true,
    "inspector": "user-456",
    "notes": "Quality standards met"
  }
}
```

### Inventory Management Endpoints

#### Get Materials
```http
GET /inventory/materials?category=fabric&lowStock=true
Authorization: Bearer <access_token>
```

#### Get Material Details
```http
GET /inventory/materials/{materialId}
Authorization: Bearer <access_token>
```

#### Update Stock Level
```http
PATCH /inventory/materials/{materialId}/stock
Authorization: Bearer <access_token>

{
  "quantity": 500,
  "transactionType": "received",
  "reference": "PO-2024-001",
  "notes": "Stock received from supplier"
}
```

#### Get Stock Transactions
```http
GET /inventory/transactions?materialId=material-123&dateFrom=2024-01-01
Authorization: Bearer <access_token>
```

### Quality Control Endpoints

#### Get Quality Checkpoints
```http
GET /quality/checkpoints?stage=cutting&active=true
Authorization: Bearer <access_token>
```

#### Create Quality Inspection
```http
POST /quality/inspections
Authorization: Bearer <access_token>

{
  "checkpointId": "checkpoint-123",
  "productionOrderId": "prod-order-456",
  "inspectorId": "user-789",
  "sampleSize": 10,
  "results": [
    {
      "criteria": "Fabric quality",
      "result": "pass",
      "notes": "Good quality fabric"
    },
    {
      "criteria": "Cut accuracy",
      "result": "pass",
      "notes": "Cuts within tolerance"
    }
  ],
  "overallResult": "pass",
  "defectsFound": []
}
```

#### Get Quality Reports
```http
GET /quality/reports?dateFrom=2024-01-01&dateTo=2024-01-31&department=sewing
Authorization: Bearer <access_token>
```

### User Management Endpoints

#### Get Users
```http
GET /users?department=sewing&role=operator&active=true
Authorization: Bearer <access_token>
```

#### Create User
```http
POST /users
Authorization: Bearer <access_token>

{
  "username": "jane.smith",
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "roleId": "role-456",
  "departmentId": "dept-789",
  "password": "temporaryPassword123",
  "isActive": true
}
```

#### Update User
```http
PUT /users/{userId}
Authorization: Bearer <access_token>

{
  "firstName": "Jane",
  "lastName": "Smith-Johnson",
  "email": "<EMAIL>",
  "roleId": "role-789",
  "isActive": true
}
```

### Reporting Endpoints

#### Get Production Report
```http
GET /reports/production?dateFrom=2024-01-01&dateTo=2024-01-31&department=sewing
Authorization: Bearer <access_token>
```

#### Get Quality Report
```http
GET /reports/quality?dateFrom=2024-01-01&dateTo=2024-01-31
Authorization: Bearer <access_token>
```

#### Get Inventory Report
```http
GET /reports/inventory?category=fabric&includeTransactions=true
Authorization: Bearer <access_token>
```

---

## WebSocket Events

### Connection
```javascript
// Connect to WebSocket
const ws = new WebSocket('wss://api.hmcollection.com/ws');

// Authentication
ws.send(JSON.stringify({
  type: 'auth',
  token: 'Bearer <access_token>'
}));
```

### Real-time Events

#### Task Status Update
```json
{
  "type": "task_status_updated",
  "data": {
    "taskId": "task-123",
    "status": "completed",
    "updatedBy": "user-456",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### Production Alert
```json
{
  "type": "production_alert",
  "data": {
    "alertType": "bottleneck_detected",
    "workstationId": "workstation-101",
    "message": "Workstation experiencing delays",
    "severity": "medium",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### Quality Issue
```json
{
  "type": "quality_issue",
  "data": {
    "inspectionId": "inspection-789",
    "productionOrderId": "prod-order-456",
    "issueType": "defect_found",
    "severity": "high",
    "message": "Critical defect found in cutting stage",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

---

## Rate Limiting

### Rate Limits
- **Authentication endpoints**: 5 requests per minute per IP
- **General API endpoints**: 100 requests per minute per user
- **Real-time endpoints**: 1000 requests per minute per user
- **File upload endpoints**: 10 requests per minute per user

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

### Rate Limit Exceeded Response
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again later.",
    "details": {
      "limit": 100,
      "remaining": 0,
      "resetTime": "2024-01-15T10:35:00Z"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

*This API specification provides a comprehensive foundation for the Cloth Manufacturing App backend services. All endpoints should be implemented with proper authentication, authorization, validation, and error handling.*
