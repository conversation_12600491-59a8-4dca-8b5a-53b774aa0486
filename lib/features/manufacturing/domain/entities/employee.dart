import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Employee entity for manufacturing operations
class Employee extends AuditableEntity {
  final String employeeId;
  final String firstName;
  final String lastName;
  final String email;
  final String? phoneNumber;
  final UserRole role;
  final Department department;
  final CommonStatus status;
  final DateTime hireDate;
  final DateTime? terminationDate;
  final String? profileImageUrl;
  final bool isActive;
  final String? supervisorId;
  final List<String> skills;
  final Map<String, dynamic> metadata;
  final double? hourlyRate;
  final String? emergencyContact;
  final String? address;

  const Employee({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    String? deletedBy,
    int version = 1,
    required this.employeeId,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phoneNumber,
    required this.role,
    required this.department,
    required this.status,
    required this.hireDate,
    this.terminationDate,
    this.profileImageUrl,
    this.isActive = true,
    this.supervisorId,
    this.skills = const [],
    this.metadata = const {},
    this.hourlyRate,
    this.emergencyContact,
    this.address,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          deletedBy: deletedBy,
          version: version,
        );

  /// Get full name
  String get fullName => '$firstName $lastName';

  /// Get display name
  String get displayName => fullName.trim().isNotEmpty ? fullName : employeeId;

  /// Get initials for avatar
  String get initials {
    final first = firstName.isNotEmpty ? firstName[0].toUpperCase() : '';
    final last = lastName.isNotEmpty ? lastName[0].toUpperCase() : '';
    return '$first$last'.isNotEmpty ? '$first$last' : employeeId.substring(0, 2).toUpperCase();
  }

  /// Check if employee is currently employed
  bool get isCurrentlyEmployed => isActive && status == CommonStatus.active && terminationDate == null;

  /// Get years of service
  double get yearsOfService {
    final endDate = terminationDate ?? DateTime.now();
    final difference = endDate.difference(hireDate);
    return difference.inDays / 365.25;
  }

  /// Check if employee is in specific department
  bool isInDepartment(Department dept) => department == dept;

  /// Check if employee has specific role
  bool hasRole(UserRole userRole) => role == userRole;

  /// Check if employee is admin
  bool get isAdmin => role == UserRole.administrator;

  /// Check if employee is department head
  bool get isDepartmentHead {
    return [
      UserRole.cuttingHead,
      UserRole.sewingHead,
      UserRole.finishingHead,
    ].contains(role);
  }

  /// Check if employee is supervisor
  bool get isSupervisor => role == UserRole.sewingSupervisor;

  /// Check if employee is operator
  bool get isOperator {
    return [
      UserRole.sewingOperator,
      UserRole.finishingOperator,
      UserRole.cuttingHelper,
    ].contains(role);
  }

  /// Get role hierarchy level
  int get hierarchyLevel => role.hierarchyLevel;

  /// Check if employee can manage another employee
  bool canManage(Employee otherEmployee) {
    return hierarchyLevel > otherEmployee.hierarchyLevel;
  }

  /// Copy with new values
  Employee copyWith({
    String? employeeId,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    UserRole? role,
    Department? department,
    CommonStatus? status,
    DateTime? hireDate,
    DateTime? terminationDate,
    String? profileImageUrl,
    bool? isActive,
    String? supervisorId,
    List<String>? skills,
    Map<String, dynamic>? metadata,
    double? hourlyRate,
    String? emergencyContact,
    String? address,
    DateTime? updatedAt,
    String? updatedBy,
    int? version,
  }) {
    return Employee(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy,
      version: version ?? this.version + 1,
      employeeId: employeeId ?? this.employeeId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      department: department ?? this.department,
      status: status ?? this.status,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      supervisorId: supervisorId ?? this.supervisorId,
      skills: skills ?? this.skills,
      metadata: metadata ?? this.metadata,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      address: address ?? this.address,
    );
  }

  /// Convert to a map for Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'employeeId': employeeId,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phoneNumber': phoneNumber,
      'role': role.value,
      'department': department.value,
      'status': status.value,
      'hireDate': hireDate,
      'terminationDate': terminationDate,
      'profileImageUrl': profileImageUrl,
      'isActive': isActive,
      'supervisorId': supervisorId,
      'skills': skills,
      'metadata': metadata,
      'hourlyRate': hourlyRate,
      'emergencyContact': emergencyContact,
      'address': address,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'deletedBy': deletedBy,
      'version': version,
    };
  }

  /// Create from Firestore document
  factory Employee.fromFirestore(Map<String, dynamic> data, String id) {
    return Employee(
      id: id,
      employeeId: data['employeeId'] ?? '',
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      email: data['email'] ?? '',
      phoneNumber: data['phoneNumber'],
      role: UserRole.values.firstWhere(
        (r) => r.value == data['role'],
        orElse: () => UserRole.viewer,
      ),
      department: Department.values.firstWhere(
        (d) => d.value == data['department'],
        orElse: () => Department.cutting,
      ),
      status: CommonStatus.values.firstWhere(
        (s) => s.value == data['status'],
        orElse: () => CommonStatus.active,
      ),
      hireDate: (data['hireDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      terminationDate: (data['terminationDate'] as Timestamp?)?.toDate(),
      profileImageUrl: data['profileImageUrl'],
      isActive: data['isActive'] ?? true,
      supervisorId: data['supervisorId'],
      skills: List<String>.from(data['skills'] ?? []),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      hourlyRate: data['hourlyRate']?.toDouble(),
      emergencyContact: data['emergencyContact'],
      address: data['address'],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
      deletedBy: data['deletedBy'],
      version: data['version'] ?? 1,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        employeeId,
        firstName,
        lastName,
        email,
        phoneNumber,
        role,
        department,
        status,
        hireDate,
        terminationDate,
        profileImageUrl,
        isActive,
        supervisorId,
        skills,
        metadata,
        hourlyRate,
        emergencyContact,
        address,
      ];

  @override
  String toString() {
    return 'Employee(id: $id, employeeId: $employeeId, fullName: $fullName, role: $role, department: $department, status: $status)';
  }
}

/// Employee filter criteria
class EmployeeFilterCriteria extends Equatable {
  final UserRole? role;
  final Department? department;
  final CommonStatus? status;
  final bool? isActive;
  final String? supervisorId;
  final DateTime? hireDateFrom;
  final DateTime? hireDateTo;
  final List<String>? skills;

  const EmployeeFilterCriteria({
    this.role,
    this.department,
    this.status,
    this.isActive,
    this.supervisorId,
    this.hireDateFrom,
    this.hireDateTo,
    this.skills,
  });

  @override
  List<Object?> get props => [
        role,
        department,
        status,
        isActive,
        supervisorId,
        hireDateFrom,
        hireDateTo,
        skills,
      ];

  /// Convert to query parameters
  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (role != null) params['role'] = role!.value;
    if (department != null) params['department'] = department!.value;
    if (status != null) params['status'] = status!.value;
    if (isActive != null) params['isActive'] = isActive;
    if (supervisorId != null) params['supervisorId'] = supervisorId;
    if (hireDateFrom != null) params['hireDateFrom'] = hireDateFrom!.toIso8601String();
    if (hireDateTo != null) params['hireDateTo'] = hireDateTo!.toIso8601String();
    if (skills != null && skills!.isNotEmpty) params['skills'] = skills;
    
    return params;
  }
}
