import 'package:dartz/dartz.dart';
import 'package:hm_collection/features/auth/domain/entities/biometric_auth_data.dart';
import 'package:hm_collection/features/auth/domain/entities/login_credentials.dart';
import 'package:hm_collection/features/auth/domain/entities/password_reset_confirm_request.dart';
import 'package:hm_collection/features/auth/domain/entities/two_factor_auth_data.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/enums/common_enums.dart';
import '../entities/auth_tokens.dart';
import '../entities/user.dart';
import '../../../../core/auth/entities/user_entities.dart' as auth_entities;

/// Registration request
class RegisterRequest {
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final UserRole role;

  const RegisterRequest({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    required this.role,
  });
}

/// Password reset request
class PasswordResetRequest {
  final String email;

  const PasswordResetRequest({required this.email});
}

/// Import the PasswordResetConfirmRequest from auth_entities


/// Change password request
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
  });
}

/// Biometric authentication request
class BiometricAuthRequest {
  final String userId;
  final String biometricData;

  const BiometricAuthRequest({
    required this.userId,
    required this.biometricData,
  });
}

/// Two-factor verification request
class TwoFactorVerificationRequest {
  final String code;
  final String userId;

  const TwoFactorVerificationRequest({
    required this.code,
    required this.userId,
  });
}

/// Authentication repository interface
abstract class AuthRepository {
  /// Register a new user
  Future<Either<Failure, ApiResponse>> register(RegisterRequest request);
  /// Login with credentials
  Future<Either<Failure, ApiResponse<AuthTokens>>> login(
    LoginCredentials credentials,
  );

  /// Logout current user
  Future<Either<Failure, ApiVoidResponse>> logout();

  /// Refresh access token
  Future<Either<Failure, ApiResponse<AuthTokens>>> refreshToken(
    String refreshToken,
  );

  /// Get current user profile
  Future<Either<Failure, ApiResponse<User>>> getCurrentUser();

  /// Update user profile
  Future<Either<Failure, ApiResponse<User>>> updateProfile(
    User user,
  );

  /// Change password
  Future<Either<Failure, ApiVoidResponse>> changePassword(
    ChangePasswordRequest request,
  );

  /// Request password reset
  Future<Either<Failure, ApiVoidResponse>> requestPasswordReset(
    PasswordResetRequest request,
  );

  /// Confirm password reset
  Future<Either<Failure, ApiVoidResponse>> confirmPasswordReset(
    PasswordResetConfirmRequest confirmation,
  );

  /// Enable two-factor authentication
  Future<Either<Failure, ApiResponse<String>>> enableTwoFactorAuth();

  /// Disable two-factor authentication
  Future<Either<Failure, ApiVoidResponse>> disableTwoFactorAuth(
    String code,
  );

  /// Verify two-factor authentication code
  Future<Either<Failure, ApiVoidResponse>> verifyTwoFactorAuth(
    TwoFactorAuthData data,
  );

  /// Authenticate with biometrics
  Future<Either<Failure, ApiResponse<AuthTokens>>> authenticateWithBiometrics(
    BiometricAuthData data,
  );

  /// Register biometric authentication
  Future<Either<Failure, ApiVoidResponse>> registerBiometrics(
    BiometricAuthData data,
  );

  /// Remove biometric authentication
  Future<Either<Failure, ApiVoidResponse>> removeBiometrics(
    String biometricType,
  );

  /// Get user sessions
  Future<Either<Failure, ApiListResponse<UserSession>>> getUserSessions();

  /// Revoke session
  Future<Either<Failure, ApiVoidResponse>> revokeSession(
    String sessionId,
  );

  /// Revoke all sessions except current
  Future<Either<Failure, ApiVoidResponse>> revokeAllOtherSessions();

  /// Check if user exists
  Future<Either<Failure, bool>> userExists(String username);

  /// Validate token
  Future<Either<Failure, bool>> validateToken(String token);

  /// Get user permissions
  Future<Either<Failure, List<String>>> getUserPermissions();

  /// Update user preferences
  Future<Either<Failure, ApiVoidResponse>> updateUserPreferences(
    UserPreferences preferences,
  );

  /// Get user preferences
  Future<Either<Failure, ApiResponse<UserPreferences>>> getUserPreferences();

  /// Sends an email verification to the current user
  Future<Either<Failure, void>> sendEmailVerification();

  /// Check if the current user's email is verified
  Future<Either<Failure, bool>> isEmailVerified();

  /// Admin login with credentials
  Future<Either<Failure, ApiResponse<AuthTokens>>> adminLogin(
    LoginCredentials credentials,
  );

  /// Stream of user data changes
  Stream<auth_entities.AppUser?> get userDataChanges;

  /// Stream of auth state changes
  Stream<auth_entities.AppUser?> get authStateChanges;

}

/// Local authentication repository interface
abstract class LocalAuthRepository {
  /// Save authentication tokens locally
  Future<Either<Failure, void>> saveTokens(AuthTokens tokens);

  /// Get saved authentication tokens
  Future<Either<Failure, AuthTokens?>> getTokens();

  /// Clear saved authentication tokens
  Future<Either<Failure, void>> clearTokens();

  /// Save user data locally
  Future<Either<Failure, void>> saveUser(User user);

  /// Get saved user data
  Future<Either<Failure, User?>> getUser();

  /// Clear saved user data
  Future<Either<Failure, void>> clearUser();

  /// Save user preferences locally
  Future<Either<Failure, void>> saveUserPreferences(UserPreferences preferences);

  /// Get saved user preferences
  Future<Either<Failure, UserPreferences?>> getUserPreferences();

  /// Clear user preferences
  Future<Either<Failure, void>> clearUserPreferences();

  /// Check if biometric authentication is available
  Future<Either<Failure, bool>> isBiometricAvailable();

  /// Check if biometric authentication is enabled
  Future<Either<Failure, bool>> isBiometricEnabled();

  /// Enable biometric authentication
  Future<Either<Failure, void>> enableBiometric();

  /// Disable biometric authentication
  Future<Either<Failure, void>> disableBiometric();

  /// Authenticate with biometrics
  Future<Either<Failure, bool>> authenticateWithBiometric();

  /// Save login credentials for auto-login
  Future<Either<Failure, void>> saveLoginCredentials(LoginCredentials credentials);

  /// Get saved login credentials
  Future<Either<Failure, LoginCredentials?>> getLoginCredentials();

  /// Clear saved login credentials
  Future<Either<Failure, void>> clearLoginCredentials();

  /// Check if auto-login is enabled
  Future<Either<Failure, bool>> isAutoLoginEnabled();

  /// Enable auto-login
  Future<Either<Failure, void>> enableAutoLogin();

  /// Disable auto-login
  Future<Either<Failure, void>> disableAutoLogin();

  /// Get device information
  Future<Either<Failure, Map<String, String>>> getDeviceInfo();

  /// Generate device ID
  Future<Either<Failure, String>> generateDeviceId();

  /// Save last login timestamp
  Future<Either<Failure, void>> saveLastLoginTime(DateTime timestamp);

  /// Get last login timestamp
  Future<Either<Failure, DateTime?>> getLastLoginTime();

  /// Clear all authentication data
  Future<Either<Failure, void>> clearAllAuthData();
}

/// Session management repository interface
abstract class SessionRepository {
  /// Start new session
  Future<Either<Failure, UserSession>> startSession(
    String userId,
    Map<String, String> deviceInfo,
  );

  /// Update session activity
  Future<Either<Failure, void>> updateSessionActivity(String sessionId);

  /// End session
  Future<Either<Failure, void>> endSession(String sessionId);

  /// Get active sessions for user
  Future<Either<Failure, List<UserSession>>> getActiveSessions(String userId);

  /// Get session by ID
  Future<Either<Failure, UserSession?>> getSession(String sessionId);

  /// Check if session is valid
  Future<Either<Failure, bool>> isSessionValid(String sessionId);

  /// Extend session expiry
  Future<Either<Failure, void>> extendSession(
    String sessionId,
    Duration extension,
  );

  /// Clean up expired sessions
  Future<Either<Failure, void>> cleanupExpiredSessions();

  /// Get session statistics
  Future<Either<Failure, Map<String, dynamic>>> getSessionStats(String userId);
}

/// Permission management repository interface
abstract class PermissionRepository {
  /// Get user permissions
  Future<Either<Failure, List<String>>> getUserPermissions(String userId);

  /// Check if user has permission
  Future<Either<Failure, bool>> hasPermission(
    String userId,
    String permission,
  );

  /// Check if user has any of the permissions
  Future<Either<Failure, bool>> hasAnyPermission(
    String userId,
    List<String> permissions,
  );

  /// Check if user has all permissions
  Future<Either<Failure, bool>> hasAllPermissions(
    String userId,
    List<String> permissions,
  );

  /// Get role permissions
  Future<Either<Failure, List<String>>> getRolePermissions(String role);

  /// Update user permissions
  Future<Either<Failure, void>> updateUserPermissions(
    String userId,
    List<String> permissions,
  );

  /// Grant permission to user
  Future<Either<Failure, void>> grantPermission(
    String userId,
    String permission,
  );

  /// Revoke permission from user
  Future<Either<Failure, void>> revokePermission(
    String userId,
    String permission,
  );

  /// Get all available permissions
  Future<Either<Failure, List<String>>> getAllPermissions();

  /// Get permissions by category
  Future<Either<Failure, Map<String, List<String>>>> getPermissionsByCategory();
}
