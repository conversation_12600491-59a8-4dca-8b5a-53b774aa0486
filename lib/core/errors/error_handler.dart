import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import 'exceptions.dart';
import 'failures.dart';

/// Global error handler for the application
class ErrorHandler {
  static final Logger _logger = Logger();
  
  /// Converts exceptions to failures
  static Failure handleException(dynamic exception) {
    _logger.e('Exception occurred: $exception');
    
    if (exception is ServerException) {
      return _handleServerException(exception);
    } else if (exception is NetworkException) {
      return NetworkFailure(exception.message, code: exception.code);
    } else if (exception is CacheException) {
      return CacheFailure(exception.message, code: exception.code);
    } else if (exception is AuthException) {
      return AuthFailure(exception.message, code: exception.code);
    } else if (exception is AuthorizationException) {
      return AuthorizationFailure(exception.message, code: exception.code);
    } else if (exception is ValidationException) {
      return ValidationFailure(
        exception.message,
        code: exception.code,
        fieldErrors: exception.fieldErrors,
      );
    } else if (exception is FileException) {
      return FileFailure(exception.message, code: exception.code);
    } else if (exception is PermissionException) {
      return PermissionFailure(exception.message, code: exception.code);
    } else if (exception is DatabaseException) {
      return DatabaseFailure(exception.message, code: exception.code);
    } else if (exception is TimeoutException) {
      return TimeoutFailure(exception.message, code: exception.code);
    } else if (exception is FormatException) {
      return FormatFailure(exception.message, code: exception.code);
    } else if (exception is BusinessLogicException) {
      return BusinessLogicFailure(exception.message, code: exception.code);
    } else if (exception is ExternalServiceException) {
      return ExternalServiceFailure(exception.message, code: exception.code);
    } else if (exception is ConfigurationException) {
      return ConfigurationFailure(exception.message, code: exception.code);
    } else if (exception is DioException) {
      return _handleDioException(exception);
    } else {
      return UnknownFailure(
        exception.toString().isNotEmpty 
          ? exception.toString() 
          : AppConstants.genericErrorMessage,
      );
    }
  }
  
  /// Handles server exceptions with specific status codes
  static Failure _handleServerException(ServerException exception) {
    switch (exception.statusCode) {
      case 400:
        return ValidationFailure(
          exception.message.isNotEmpty 
            ? exception.message 
            : 'Bad request',
          code: exception.code,
        );
      case 401:
        return AuthFailure(
          exception.message.isNotEmpty 
            ? exception.message 
            : AppConstants.unauthorizedErrorMessage,
          code: exception.code,
        );
      case 403:
        return AuthorizationFailure(
          exception.message.isNotEmpty 
            ? exception.message 
            : 'Access forbidden',
          code: exception.code,
        );
      case 404:
        return ServerFailure(
          exception.message.isNotEmpty 
            ? exception.message 
            : 'Resource not found',
          code: exception.code,
        );
      case 422:
        return ValidationFailure(
          exception.message.isNotEmpty 
            ? exception.message 
            : 'Validation failed',
          code: exception.code,
        );
      case 429:
        return ServerFailure(
          exception.message.isNotEmpty 
            ? exception.message 
            : 'Too many requests',
          code: exception.code,
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return ServerFailure(
          exception.message.isNotEmpty
            ? exception.message
            : 'Server error occurred',
          code: exception.code,
        );
      case null:
        return ServerFailure(
          exception.message.isNotEmpty
            ? exception.message
            : 'Unknown server error',
          code: exception.code,
        );
      default:
        return ServerFailure(
          exception.message.isNotEmpty
            ? exception.message
            : 'Unknown server error',
          code: exception.code,
        );
    }
  }
  
  /// Handles Dio exceptions
  static Failure _handleDioException(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutFailure(
          AppConstants.timeoutErrorMessage,
          code: 'TIMEOUT',
          details: exception.message,
        );
      case DioExceptionType.badResponse:
        final statusCode = exception.response?.statusCode;
        final message = _extractErrorMessage(exception.response?.data) ??
            exception.message ??
            'Server error occurred';
        
        return _handleServerException(
          ServerException(message, statusCode: statusCode),
        );
      case DioExceptionType.cancel:
        return ServerFailure(
          'Request was cancelled',
          code: 'CANCELLED',
          details: exception.message,
        );
      case DioExceptionType.connectionError:
        return NetworkFailure(
          AppConstants.networkErrorMessage,
          code: 'CONNECTION_ERROR',
          details: exception.message,
        );
      case DioExceptionType.badCertificate:
        return NetworkFailure(
          'SSL certificate error',
          code: 'SSL_ERROR',
          details: exception.message,
        );
      case DioExceptionType.unknown:
      default:
        return UnknownFailure(
          exception.message ?? AppConstants.genericErrorMessage,
          code: 'UNKNOWN_DIO_ERROR',
          details: exception.toString(),
        );
    }
  }
  
  /// Extracts error message from response data
  static String? _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return null;
    
    if (responseData is Map<String, dynamic>) {
      // Try different common error message keys
      final errorKeys = ['message', 'error', 'detail', 'msg', 'description'];
      
      for (final key in errorKeys) {
        if (responseData.containsKey(key) && responseData[key] is String) {
          return responseData[key] as String;
        }
      }
      
      // Check for nested error objects
      if (responseData.containsKey('error') && responseData['error'] is Map) {
        final errorObj = responseData['error'] as Map<String, dynamic>;
        for (final key in errorKeys) {
          if (errorObj.containsKey(key) && errorObj[key] is String) {
            return errorObj[key] as String;
          }
        }
      }
      
      // Check for validation errors
      if (responseData.containsKey('errors') && responseData['errors'] is Map) {
        final errors = responseData['errors'] as Map<String, dynamic>;
        final firstError = errors.values.first;
        if (firstError is List && firstError.isNotEmpty) {
          return firstError.first.toString();
        }
      }
    }
    
    return null;
  }
  
  /// Logs error with appropriate level
  static void logError(dynamic error, {StackTrace? stackTrace}) {
    if (error is Failure) {
      _logger.e('Failure: ${error.message}', error: error, stackTrace: stackTrace);
    } else if (error is AppException) {
      _logger.e('Exception: ${error.message}', error: error, stackTrace: stackTrace);
    } else {
      _logger.e('Unknown error: $error', error: error, stackTrace: stackTrace);
    }
  }
  
  /// Gets user-friendly error message
  static String getUserFriendlyMessage(Failure failure) {
    if (failure is NetworkFailure) {
      return AppConstants.networkErrorMessage;
    } else if (failure is TimeoutFailure) {
      return AppConstants.timeoutErrorMessage;
    } else if (failure is AuthFailure) {
      return AppConstants.unauthorizedErrorMessage;
    } else if (failure is AuthorizationFailure) {
      return 'You do not have permission to perform this action.';
    } else if (failure is ValidationFailure) {
      if (failure.fieldErrors != null && failure.fieldErrors!.isNotEmpty) {
        final firstError = failure.fieldErrors!.values.first;
        return firstError.isNotEmpty ? firstError.first : failure.message;
      }
      return failure.message;
    } else if (failure is ServerFailure) {
      return failure.message.isNotEmpty
        ? failure.message
        : 'Server error occurred. Please try again.';
    } else {
      return failure.message.isNotEmpty
        ? failure.message
        : AppConstants.genericErrorMessage;
    }
  }
  
  /// Determines if error should trigger retry
  static bool shouldRetry(Failure failure) {
    return failure is NetworkFailure || 
           failure is TimeoutFailure ||
           (failure is ServerFailure && 
            failure.code != null && 
            ['500', '502', '503', '504'].contains(failure.code));
  }
  
  /// Determines if error should trigger logout
  static bool shouldLogout(Failure failure) {
    return failure is AuthFailure && 
           (failure.code == '401' || failure.message.contains('expired'));
  }
}
