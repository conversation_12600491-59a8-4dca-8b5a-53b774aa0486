import 'dart:async';
import 'dart:convert';
import 'dart:io' show Platform;
import 'dart:math';
import 'dart:typed_data';

import 'package:base32/base32.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import 'package:otp/otp.dart';

import '../../features/auth/domain/entities/biometric_auth_data.dart';
import '../../features/auth/domain/entities/user.dart' as app_user;
import '../../shared/enums/common_enums.dart';
import '../../shared/enums/common_enums.dart' as app_user show UserRole;
import '../constants/app_constants.dart';

/// Firebase Authentication Service
@LazySingleton()
class FirebaseAuthService {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;
  final GoogleSignIn _googleSignIn;

  // Stream controllers for auth state
  final StreamController<app_user.User?> _userController =
      StreamController<app_user.User?>.broadcast();
  final StreamController<bool> _authStateController =
      StreamController<bool>.broadcast();

  StreamSubscription<firebase_auth.User?>? _authStateSubscription;
  StreamSubscription<DocumentSnapshot>? _userDocSubscription;

  FirebaseAuthService(this._firebaseAuth, this._firestore, this._googleSignIn) {
    _initializeAuthStateListener();
  }

  // Public streams
  Stream<app_user.User?> get userStream => _userController.stream;
  Stream<bool> get authStateStream => _authStateController.stream;

  // Current user getters
  firebase_auth.User? get currentFirebaseUser => _firebaseAuth.currentUser;
  bool get isSignedIn => _firebaseAuth.currentUser != null;

  Future<app_user.User> signInWithGoogleForAdmin() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw firebase_auth.FirebaseAuthException(
          code: 'sign-in-cancelled',
          message: 'The sign-in was cancelled by the user.',
        );
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final firebase_auth.AuthCredential credential =
          firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final firebase_auth.UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(credential);
      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        throw firebase_auth.FirebaseAuthException(
          code: 'user-not-found',
          message: 'Sign-in failed, user not found.',
        );
      }

      // Check for user document in Firestore
      final userDocRef = _firestore.collection('users').doc(firebaseUser.uid);
      final userDoc = await userDocRef.get();

      if (userDoc.exists) {
        // User exists, map it and return
        return _mapFirestoreToUser(userDoc.id, userDoc.data() as Map<String, dynamic>);
      } else {
        // User document doesn't exist, create a new one.
        final displayName = firebaseUser.displayName ?? '';
        final firstName = displayName.split(' ').first;
        final lastName = displayName.split(' ').skip(1).join(' ');
        const defaultRole = app_user.UserRole.viewer;

        final newUser = app_user.User(
          id: firebaseUser.uid,
          email: firebaseUser.email!,
          firstName: firstName,
          lastName: lastName,
          username: firebaseUser.email!.split('@').first,
          role: defaultRole,
          status: CommonStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          isEmailVerified: firebaseUser.emailVerified,
          department: defaultRole.department, // Corrected default department
          isPhoneVerified: false,
          permissions: _getDefaultPermissions(defaultRole),
        );

        await userDocRef.set(newUser.toFirestore());
        
        // Return the newly created user
        return newUser;
      }
    } on firebase_auth.FirebaseAuthException {
      rethrow; // Re-throw Firebase-specific exceptions
    } catch (e) {
      // Catch-all for other errors
      throw Exception('An unexpected error occurred during Google Sign-In: $e');
    }
  }

  /// Checks if a user with the given email exists in Firebase Authentication
  /// Returns true if a user with the email exists, false otherwise
  /// Updates user preferences in Firestore
  Future<void> updateUserPreferences(
      String userId, Map<String, dynamic> preferences) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'preferences': preferences,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update user preferences: $e');
    }
  }

  /// Gets user preferences from Firestore
  /// Returns a map containing the user's preferences
  /// Throws an exception if the operation fails
  Future<Map<String, dynamic>> getUserPreferences(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();

      if (!doc.exists) {
        throw Exception('User document not found');
      }

      final data = doc.data();
      if (data == null) {
        return {}; // Return empty map if no preferences exist
      }

      // Return the preferences map or an empty map if it doesn't exist
      return Map<String, dynamic>.from(data['preferences'] ?? {});
    } catch (e) {
      throw Exception('Failed to get user preferences: $e');
    }
  }

  /// Checks if a user with the given email exists in Firebase Authentication
  /// Returns true if a user with the email exists, false otherwise
  Future<bool> checkIfUserExists(String email) async {
    try {
      // This will throw a FirebaseAuthException if no user is found with that email
      final methods = await _firebaseAuth.fetchSignInMethodsForEmail(email);
      return methods.isNotEmpty;
    } on firebase_auth.FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        return false;
      }
      // Re-throw other FirebaseAuthExceptions
      rethrow;
    } catch (e) {
      // Re-throw any other exceptions
      rethrow;
    }
  }

  /// Revoke a specific session
  /// Note: Firebase Auth doesn't support revoking specific sessions directly.
  /// This implementation signs out the current user as a workaround.
  /// For more granular control, consider implementing a custom token system.
  Future<void> revokeSession(String sessionId) async {
    try {
      // In a real implementation, you would typically:
      // 1. Verify the session ID is valid
      // 2. Update your database to mark the session as revoked
      // 3. Force token refresh on the client side

      // For this basic implementation, we'll just sign out the current user
      // which will invalidate their current session
      await _firebaseAuth.signOut();
    } catch (e) {
      if (e is firebase_auth.FirebaseAuthException) {
        rethrow;
      }
      throw firebase_auth.FirebaseAuthException(
        code: 'session-revocation-failed',
        message: 'Failed to revoke session: ${e.toString()}',
      );
    }
  }

  /// Get user permissions from Firestore
  /// Returns a list of permission strings for the given user ID
  Future<List<String>> getUserPermissions(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return [];
      }

      final data = userDoc.data();
      if (data == null || !data.containsKey('permissions')) {
        return [];
      }

      final permissions = List<String>.from(data['permissions'] as List? ?? []);
      return permissions;
    } catch (e) {
      throw Exception('Failed to fetch user permissions: $e');
    }
  }

  /// Revoke all other sessions except the current one
  /// This forces a token refresh which will invalidate all previous tokens
  /// Note: This will sign out the user on all other devices
  Future<void> revokeAllOtherSessions() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        // Force token refresh which will invalidate all previous tokens
        await user.getIdToken(true);

        // In a real implementation, you would also want to update your database
        // to track active sessions and revoke them as needed
        debugPrint('All other sessions have been revoked');
      }
    } catch (e) {
      if (e is firebase_auth.FirebaseAuthException) {
        rethrow;
      }
      throw firebase_auth.FirebaseAuthException(
        code: 'revoke-sessions-failed',
        message: 'Failed to revoke other sessions: ${e.toString()}',
      );
    }
  }

  /// Get all active sessions for the current user
  Future<List<app_user.UserSession>> getUserSessions() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw firebase_auth.FirebaseAuthException(
          code: 'no-current-user',
          message: 'No user is currently signed in',
        );
      }

      // Get the ID token result which contains the current session
      final tokenResult = await user.getIdTokenResult();

      // In a real implementation, you would typically query Firestore or another database
      // to get all active sessions. For this basic implementation, we'll just return
      // the current session.

      final currentSession = app_user.UserSession(
        sessionId: tokenResult.token ??
            'current-session-${DateTime.now().millisecondsSinceEpoch}',
        userId: user.uid,
        deviceId:
            'current-device', // In a real app, you'd get this from device info
        deviceName:
            'Current Device', // In a real app, you'd get this from device info
        ipAddress:
            '127.0.0.1', // In a real app, you'd get this from request metadata
        userAgent: 'Flutter/${kIsWeb ? 'Web' : Platform.operatingSystem}',
        createdAt: user.metadata.creationTime ?? DateTime.now(),
        lastActiveAt: user.metadata.lastSignInTime ?? DateTime.now(),
        expiresAt: tokenResult.expirationTime ??
            DateTime.now().add(const Duration(days: 30)),
        isActive: true,
      );

      // Return a list with the current session
      // In a production app, you would query your database for all active sessions
      return [currentSession];
    } catch (e) {
      if (e is firebase_auth.FirebaseAuthException) {
        rethrow;
      }
      throw firebase_auth.FirebaseAuthException(
        code: 'failed-to-get-sessions',
        message: 'Failed to get user sessions: ${e.toString()}',
      );
    }
  }

  /// Generates a secure random secret for TOTP
  String _generateSecureSecret() {
    final random = Random.secure();
    final values = List<int>.generate(32, (i) => random.nextInt(256));
    return base32.encode(Uint8List.fromList(values));
  }

  /// Encrypts the 2FA secret before storing it
  String _encryptSecret(String secret, String userId) {
    // In a production app, use a secure key management system
    // and never hardcode encryption keys
    final keyString = '${_firebaseAuth.app.options.apiKey!}-$userId';
    // Create a secure key using SHA-256 hash of the key string
    final hashed = sha256.convert(utf8.encode(keyString));
    final key = encrypt.Key(Uint8List.fromList(hashed.bytes.sublist(0, 32)));
    final iv = encrypt.IV.fromSecureRandom(16);
    final encrypter =
        encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.cbc));
    final encrypted = encrypter.encrypt(secret, iv: iv);
    return '${base64Encode(iv.bytes)}:${encrypted.base64}';
  }

  /// Decrypts the 2FA secret
  String _decryptSecret(String encryptedData, String userId) {
    try {
      final parts = encryptedData.split(':');
      if (parts.length != 2) throw Exception('Invalid encrypted data format');

      final iv = encrypt.IV(base64Decode(parts[0]));
      final keyString = '${_firebaseAuth.app.options.apiKey!}-$userId';
      final hashed = sha256.convert(utf8.encode(keyString));
      final key = encrypt.Key(Uint8List.fromList(hashed.bytes.sublist(0, 32)));
      final encrypter =
          encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.cbc));
      final encrypted = encrypt.Encrypted(base64Decode(parts[1]));

      return encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      debugPrint('Error decrypting 2FA secret: $e');
      rethrow;
    }
  }

  /// Enables two-factor authentication for the current user
  /// Returns a secret key that can be used to set up an authenticator app
  Future<String> enableTwoFactorAuth() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw firebase_auth.FirebaseAuthException(
        code: 'no-current-user',
        message: 'No user is currently signed in',
      );
    }

    try {
      // Generate a secure random secret for TOTP
      final secret = _generateSecureSecret();

      // Encrypt the secret before storing
      final encryptedSecret = _encryptSecret(secret, user.uid);

      // Store the encrypted secret in the user's document in Firestore
      await _firestore.collection('users').doc(user.uid).set(
        {
          'twoFactorSecret': encryptedSecret,
          'twoFactorEnabled': false, // Will be set to true after verification
          'twoFactorBackupCodes': _generateBackupCodes(),
          'twoFactorSetupDate': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        },
        SetOptions(merge: true),
      );

      return secret;
    } catch (e) {
      debugPrint('Error enabling 2FA: $e');
      throw firebase_auth.FirebaseAuthException(
        code: '2fa-setup-failed',
        message: 'Failed to set up two-factor authentication: $e',
      );
    }
  }

  /// Generates backup codes for 2FA recovery
  List<String> _generateBackupCodes() {
    final random = Random.secure();
    final codes = <String>[];

    // Generate 10 backup codes, each 12 characters long
    for (var i = 0; i < 10; i++) {
      final code = String.fromCharCodes(
        List.generate(12, (_) => random.nextInt(26) + 97), // a-z
      );
      codes.add(code);
    }

    return codes;
  }

  /// Verifies a two-factor authentication code for the current user
  /// Returns true if the code is valid, false otherwise
  Future<bool> verifyTwoFactorCode(String code) async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw firebase_auth.FirebaseAuthException(
        code: 'no-current-user',
        message: 'No user is currently signed in',
      );
    }

    try {
      // Get the user's document from Firestore
      final doc = await _firestore.collection('users').doc(user.uid).get();

      if (!doc.exists) {
        throw firebase_auth.FirebaseAuthException(
          code: 'user-not-found',
          message: 'User document not found',
        );
      }

      final userData = doc.data()!;
      final encryptedSecret = userData['twoFactorSecret'] as String?;

      if (encryptedSecret == null) {
        throw firebase_auth.FirebaseAuthException(
          code: '2fa-not-enabled',
          message: 'Two-factor authentication is not enabled for this account',
        );
      }

      // Check if the code is a backup code
      final backupCodes =
          List<String>.from(userData['twoFactorBackupCodes'] ?? []);
      final isBackupCode = backupCodes.contains(code);

      if (isBackupCode) {
        // Remove the used backup code
        backupCodes.remove(code);
        await _firestore.collection('users').doc(user.uid).update({
          'twoFactorBackupCodes': backupCodes,
          'lastBackupCodeUsedAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        return true;
      }

      // Decrypt the secret
      final secret = _decryptSecret(encryptedSecret, user.uid);

      // Verify the TOTP code
      final now = DateTime.now();
      final isValid = OTP.generateTOTPCodeString(
            secret,
            now.millisecondsSinceEpoch,
            algorithm: Algorithm.SHA1,
            isGoogle: true,
          ) ==
          code;

      // If the code is valid and 2FA isn't already enabled, mark it as enabled
      if (isValid && !(userData['twoFactorEnabled'] ?? false)) {
        await _firestore.collection('users').doc(user.uid).update({
          'twoFactorEnabled': true,
          'twoFactorEnabledAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      return isValid;
    } catch (e) {
      debugPrint('Error verifying 2FA code: $e');
      return false;
    }
  }

  /// Checks if two-factor authentication is enabled for the current user
  Future<bool> isTwoFactorEnabled() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) return false;

    try {
      final doc = await _firestore.collection('users').doc(user.uid).get();
      return doc.exists && (doc.data()?['twoFactorEnabled'] == true);
    } catch (e) {
      debugPrint('Error checking 2FA status: $e');
      return false;
    }
  }

  /// Disables two-factor authentication for the current user
  /// [code] The current 2FA code to verify before disabling
  Future<void> disableTwoFactorAuth(String code) async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw firebase_auth.FirebaseAuthException(
        code: 'no-current-user',
        message: 'No user is currently signed in',
      );
    }

    try {
      // First verify the code to ensure the user has access to the authenticator app
      final isCodeValid = await verifyTwoFactorCode(code);
      if (!isCodeValid) {
        throw firebase_auth.FirebaseAuthException(
          code: 'invalid-verification-code',
          message: 'Invalid 2FA verification code',
        );
      }

      // If code is valid, disable 2FA
      await _firestore.collection('users').doc(user.uid).update({
        'twoFactorEnabled': false,
        'twoFactorDisabledAt': FieldValue.serverTimestamp(),
        'twoFactorSecret': FieldValue.delete(),
        'twoFactorBackupCodes': FieldValue.delete(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Two-factor authentication disabled for user: ${user.uid}');
    } catch (e) {
      debugPrint('Error disabling 2FA: $e');
      rethrow;
    }
  }

  /// Gets the remaining backup codes for the current user
  Future<List<String>> getBackupCodes() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw firebase_auth.FirebaseAuthException(
        code: 'no-current-user',
        message: 'No user is currently signed in',
      );
    }

    try {
      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (!doc.exists) return [];

      final data = doc.data()!;
      if (data['twoFactorEnabled'] != true) return [];

      return List<String>.from(data['twoFactorBackupCodes'] ?? []);
    } catch (e) {
      debugPrint('Error getting backup codes: $e');
      return [];
    }
  }

  /// Initialize Firebase auth state listener
  void _initializeAuthStateListener() {
    _authStateSubscription = _firebaseAuth.authStateChanges().listen(
      (firebase_auth.User? firebaseUser) async {
        debugPrint('Firebase auth state changed: ${firebaseUser?.uid}');

        if (firebaseUser != null) {
          // If user's email is verified with Firebase, ensure our Firestore doc is up to date.
          if (firebaseUser.emailVerified) {
            final userDocRef =
                _firestore.collection('users').doc(firebaseUser.uid);
            final userDoc = await userDocRef.get();
            if (userDoc.exists &&
                (userDoc.data()?['isEmailVerified'] == false)) {
              await userDocRef.update({'isEmailVerified': true});
            }
          }
          // User is signed in, get user data from Firestore
          await _listenToUserDocument(firebaseUser.uid);
          _authStateController.add(true);
        } else {
          // User is signed out
          _userDocSubscription?.cancel();
          _userController.add(null);
          _authStateController.add(false);
        }
      },
      onError: (error) {
        debugPrint('Firebase auth state error: $error');
        _authStateController.addError(error);
      },
    );
  }

  /// Listen to user document changes in Firestore
  Future<void> _listenToUserDocument(String uid) async {
    try {
      _userDocSubscription?.cancel();

      _userDocSubscription =
          _firestore.collection('users').doc(uid).snapshots().listen(
        (DocumentSnapshot doc) {
          if (doc.exists) {
            try {
              final userData = doc.data() as Map<String, dynamic>;
              final user = _mapFirestoreToUser(doc.id, userData);
              _userController.add(user);
            } catch (e) {
              debugPrint('Error mapping user data: $e');
              _userController.addError(e);
            }
          } else {
            debugPrint('User document does not exist for UID: $uid');
            _userController.add(null);
          }
        },
        onError: (error) {
          debugPrint('User document stream error: $error');
          _userController.addError(error);
        },
      );
    } catch (e) {
      debugPrint('Error setting up user document listener: $e');
      _userController.addError(e);
    }
  }

  /// Sign in with biometric authentication
  Future<firebase_auth.UserCredential> signInWithBiometric(
      BiometricAuthData data) async {
    try {
      // Get the stored credential for the user
      final storedCredential = await _getStoredCredential(data.userId);

      if (storedCredential == null) {
        throw firebase_auth.FirebaseAuthException(
          code: 'no-stored-credential',
          message: 'No stored credential found for biometric authentication',
        );
      }

      // In a real app, you would verify the biometric authentication here
      // using the local_auth package or similar

      // Sign in with the stored credential
      final credential = firebase_auth.EmailAuthProvider.credential(
        email: storedCredential.email,
        password: storedCredential.password,
      );

      final userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        debugPrint(
            'Biometric authentication successful: ${userCredential.user!.uid}');
        return userCredential;
      } else {
        throw firebase_auth.FirebaseAuthException(
          code: 'biometric-auth-failed',
          message: 'Biometric authentication failed',
        );
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('Firebase biometric auth error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected biometric auth error: $e');
      rethrow;
    }
  }

  /// Registers biometric authentication for the current user
  /// Stores the user's credentials securely for future biometric authentication
  Future<void> registerBiometric(BiometricAuthData data) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw firebase_auth.FirebaseAuthException(
          code: 'no-current-user',
          message: 'No user is currently signed in',
        );
      }

      // Store biometric authentication data in Firestore
      await _firestore.collection('biometric_credentials').doc(user.uid).set({
        'userId': user.uid,
        'email': user.email,
        'biometricType': data.biometricType,
        'biometricSignature': data.signature, // Store the biometric signature
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Biometric authentication registered for user: ${user.uid}');
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint(
          'Firebase biometric registration error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected biometric registration error: $e');
      rethrow;
    }
  }

  /// Removes biometric authentication for the current user
  Future<void> removeBiometric(String biometricType) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw firebase_auth.FirebaseAuthException(
          code: 'no-current-user',
          message: 'No user is currently signed in',
        );
      }

      // Remove the biometric credentials from Firestore
      await _firestore
          .collection('biometric_credentials')
          .doc(user.uid)
          .delete();

      debugPrint('Biometric authentication removed for user: ${user.uid}');
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('Firebase biometric removal error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected biometric removal error: $e');
      rethrow;
    }
  }

  /// Helper method to get stored credentials for biometric authentication
  Future<({String email, String password})?> _getStoredCredential(
      String userId) async {
    try {
      // In a real app, you would retrieve this from secure storage
      // This is just a placeholder implementation
      final doc = await _firestore
          .collection('biometric_credentials')
          .doc(userId)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        return (
          email: data['email'] as String,
          password: data['password'] as String,
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error getting stored credential: $e');
      return null;
    }
  }

  /// Sign in with email and password
  Future<firebase_auth.UserCredential?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Ensure a Firestore user document exists for this UID
      if (credential.user != null) {
        final userDocRef = _firestore.collection('users').doc(credential.user!.uid);
        final userDoc = await userDocRef.get();

        if (!userDoc.exists) {
          // If the document doesn't exist, create it with default values
          await _createUserDocument(credential.user!, 
            firstName: credential.user!.displayName?.split(' ').first ?? '',
            lastName: credential.user!.displayName?.split(' ').skip(1).join(' ') ?? '',
            role: app_user.UserRole.viewer, // Assign a default role
          );
        }
      }

      debugPrint('Firebase sign in successful: ${credential.user?.uid}');
      return credential;
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('Firebase sign in error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected sign in error: $e');
      // Return null to let callers handle as a failed login instead of crashing on type cast issues
      return null;
    }
  }

  /// Create user with email and password
  Future<firebase_auth.UserCredential?> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required app_user.UserRole role,
  }) async {
    try {
      // Use a transaction to ensure atomicity
      return await _firestore
          .runTransaction<firebase_auth.UserCredential?>((transaction) async {
        // Check if user already exists
        try {
          final existingUser =
              await _firebaseAuth.fetchSignInMethodsForEmail(email);
          if (existingUser.isNotEmpty) {
            throw firebase_auth.FirebaseAuthException(
              code: 'email-already-in-use',
              message: 'An account already exists with this email address.',
            );
          }
        } on firebase_auth.FirebaseAuthException catch (e) {
          // Rethrow email-already-in-use, otherwise ignore (e.g., user-not-found is expected)
          if (e.code == 'email-already-in-use') {
            rethrow;
          }
        }

        // Create user with Firebase Auth
        final credential = await _firebaseAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );

        final user = credential.user;
        if (user == null) {
          throw Exception('User creation failed, user is null.');
        }

        // Update display name
        await user.updateDisplayName('$firstName $lastName');

        // Create user document in Firestore within the same transaction
        final userRef = _firestore.collection('users').doc(user.uid);
        final userData = {
          'uid': user.uid,
          'email': email,
          'firstName': firstName,
          'lastName': lastName,
          'displayName': '$firstName $lastName',
          'username': email.split('@').first,
          'role': role.value,
          'status': CommonStatus.pending.value,
          'department': role.department.value,
          'isActive': true,
          'isEmailVerified': false,
          'permissions': _getDefaultPermissions(role),
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          'lastLoginAt': null,
          'lastActiveAt': null,
          'metadata': {
            'registrationSource': 'mobile_app',
            'appVersion': AppConstants.appVersion,
          },
        };
        transaction.set(userRef, userData);

        debugPrint('User document created successfully in transaction');

        // Send email verification
        await user.sendEmailVerification();
        debugPrint('Email verification sent');

        return credential;
      });
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('Firebase registration error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected registration error: $e');
      rethrow;
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _firebaseAuth.signOut();
      debugPrint('Firebase sign out successful');
    } catch (e) {
      debugPrint('Firebase sign out error: $e');
      rethrow;
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      debugPrint('Password reset email sent to: $email');
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('Password reset error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected password reset error: $e');
      rethrow;
    }
  }

  /// Confirm password reset with the provided code and new password
  Future<void> confirmPasswordReset({
    required String code,
    required String newPassword,
  }) async {
    try {
      await _firebaseAuth.confirmPasswordReset(
        code: code,
        newPassword: newPassword,
      );
      debugPrint('Password has been reset successfully');
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('Password reset confirmation error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected password reset confirmation error: $e');
      rethrow;
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        debugPrint('Email verification sent to: ${user.email}');
      }
    } catch (e) {
      debugPrint('Email verification error: $e');
      rethrow;
    }
  }

  /// Reload current user
  Future<void> reloadUser() async {
    try {
      await _firebaseAuth.currentUser?.reload();
    } catch (e) {
      debugPrint('Reload user error: $e');
    }
  }

  /// Update user profile in Firestore
  Future<void> updateUserProfile(
      String uid, Map<String, dynamic> updates) async {
    try {
      updates['updatedAt'] = FieldValue.serverTimestamp();
      await _firestore.collection('users').doc(uid).update(updates);
      debugPrint('User profile updated in Firestore: $uid');
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      rethrow;
    }
  }

  /// Update last login time
  Future<void> updateLastLoginTime(String uid) async {
    try {
      await _firestore.collection('users').doc(uid).update({
        'lastLoginAt': FieldValue.serverTimestamp(),
        'lastActiveAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating last login time: $e');
    }
  }

  /// Map Firestore data to app User entity
  app_user.User _mapFirestoreToUser(String id, Map<String, dynamic> data) {
    return app_user.User(
      id: id,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      username: data['username'] ?? '',
      email: data['email'] ?? '',
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      role: app_user.UserRole.fromString(data['role'] ?? 'viewer'),
      department: Department.fromString(data['department'] ?? 'administration'),
      status: CommonStatus.fromString(data['status'] ?? 'pending'),
      isActive: data['isActive'] ?? false,
      isEmailVerified: data['isEmailVerified'] ?? false,
      isPhoneVerified: data['isPhoneVerified'] ?? false,
      permissions: List<String>.from(data['permissions'] ?? []),
      lastLoginAt: (data['lastLoginAt'] as Timestamp?)?.toDate(),
    );
  }

  /// Get default permissions for role
  List<String> _getDefaultPermissions(app_user.UserRole role) {
    return role.permissions;
  }

  /// Dispose resources
  void dispose() {
    _authStateSubscription?.cancel();
    _userDocSubscription?.cancel();
    _userController.close();
    _authStateController.close();
  }

  /// Creates a user document in Firestore with default values
  Future<void> _createUserDocument(firebase_auth.User user, {
    required String firstName,
    required String lastName,
    required app_user.UserRole role,
  }) async {
    final userRef = _firestore.collection('users').doc(user.uid);
    final userData = {
      'uid': user.uid,
      'email': user.email,
      'firstName': firstName,
      'lastName': lastName,
      'displayName': '$firstName $lastName',
      'username': user.email?.split('@').first ?? '',
      'role': role.value,
      'status': CommonStatus.active.value,
      'department': role.department.value,
      'isActive': true,
      'isEmailVerified': user.emailVerified,
      'permissions': _getDefaultPermissions(role),
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
      'lastLoginAt': FieldValue.serverTimestamp(), // Set on creation
      'lastActiveAt': FieldValue.serverTimestamp(),
      'metadata': {
        'registrationSource': 'auto_created_on_login',
        'appVersion': AppConstants.appVersion,
      },
    };
    await userRef.set(userData);
    debugPrint('User document auto-created successfully for UID: ${user.uid}');
  }
}
