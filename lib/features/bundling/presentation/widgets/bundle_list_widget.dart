import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/entities/bundle.dart';

/// Widget for displaying a list of bundles
class BundleListWidget extends StatelessWidget {
  final List<Bundle> bundles;
  final Function(Bundle) onBundleSelected;
  final Future<void> Function()? onRefresh;
  final bool showActions;

  const BundleListWidget({
    super.key,
    required this.bundles,
    required this.onBundleSelected,
    this.onRefresh,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    if (bundles.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: onRefresh ?? () async {},
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: bundles.length,
        itemBuilder: (context, index) {
          final bundle = bundles[index];
          return _buildBundleCard(context, bundle);
        },
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No bundles found',
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first bundle to get started',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build bundle card
  Widget _buildBundleCard(BuildContext context, Bundle bundle) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => onBundleSelected(bundle),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          bundle.bundleNo,
                          style: AppTextStyles.titleMedium.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${bundle.designNo} • ${bundle.lotNo} • ${bundle.rollNo}',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(bundle.status),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildInfoChip(
                    icon: Icons.straighten,
                    label: 'Size',
                    value: bundle.size.displayName,
                  ),
                  const SizedBox(width: 12),
                  _buildInfoChip(
                    icon: Icons.inventory,
                    label: 'Pieces',
                    value: '${bundle.totalPieces}',
                  ),
                  const SizedBox(width: 12),
                  _buildInfoChip(
                    icon: Icons.numbers,
                    label: 'Range',
                    value: bundle.pieceRange,
                  ),
                ],
              ),
              if (bundle.assignedTo != null || bundle.remarks != null) ...[
                const SizedBox(height: 12),
                if (bundle.assignedTo != null)
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Assigned to: ${bundle.assignedTo}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                if (bundle.remarks != null) ...[
                  const SizedBox(height: 4),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.note,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          bundle.remarks!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
              if (showActions && _hasAvailableActions(bundle)) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                _buildActionButtons(context, bundle),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build status chip
  Widget _buildStatusChip(BundleStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Color(int.parse(status.colorCode.substring(1), radix: 16) + 0xFF000000)
            .withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Color(int.parse(status.colorCode.substring(1), radix: 16) + 0xFF000000),
          width: 1,
        ),
      ),
      child: Text(
        status.displayName,
        style: AppTextStyles.bodySmall.copyWith(
          color: Color(int.parse(status.colorCode.substring(1), radix: 16) + 0xFF000000),
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Build info chip
  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Check if bundle has available actions
  bool _hasAvailableActions(Bundle bundle) {
    return bundle.canBeIssued || 
           bundle.canBeStarted || 
           bundle.canBeCompleted || 
           bundle.canBeCancelled;
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context, Bundle bundle) {
    final actions = <Widget>[];

    if (bundle.canBeIssued) {
      actions.add(
        _buildActionButton(
          icon: Icons.send,
          label: 'Issue',
          color: AppColors.info,
          onPressed: () {
            // Handle issue action
          },
        ),
      );
    }

    if (bundle.canBeStarted) {
      actions.add(
        _buildActionButton(
          icon: Icons.play_arrow,
          label: 'Start',
          color: AppColors.success,
          onPressed: () {
            // Handle start action
          },
        ),
      );
    }

    if (bundle.canBeCompleted) {
      actions.add(
        _buildActionButton(
          icon: Icons.check,
          label: 'Complete',
          color: AppColors.success,
          onPressed: () {
            // Handle complete action
          },
        ),
      );
    }

    if (bundle.canBeCancelled) {
      actions.add(
        _buildActionButton(
          icon: Icons.cancel,
          label: 'Cancel',
          color: AppColors.error,
          onPressed: () {
            // Handle cancel action
          },
        ),
      );
    }

    return Wrap(
      spacing: 8,
      children: actions,
    );
  }

  /// Build action button
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }
}
