import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../../errors/exceptions.dart';

/// Interceptor for handling and transforming errors
class ErrorInterceptor extends Interceptor {
  final Logger _logger = Logger();
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final exception = _transformDioException(err);
    _logger.e('API Error: ${exception.message}', error: err);
    
    // Transform DioException to our custom exception
    handler.reject(
      DioException(
        requestOptions: err.requestOptions,
        response: err.response,
        type: err.type,
        error: exception,
        message: exception.message,
      ),
    );
  }
  
  /// Transform DioException to custom AppException
  AppException _transformDioException(DioException dioException) {
    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException(
          'Request timeout. Please check your connection and try again.',
          code: 'TIMEOUT',
          details: dioException.message,
        );
        
      case DioExceptionType.badResponse:
        return _handleBadResponse(dioException);
        
      case DioExceptionType.cancel:
        return ServerException(
          'Request was cancelled',
          code: 'CANCELLED',
          details: dioException.message,
        );
        
      case DioExceptionType.connectionError:
        return NetworkException(
          'Connection error. Please check your internet connection.',
          code: 'CONNECTION_ERROR',
          details: dioException.message,
        );
        
      case DioExceptionType.badCertificate:
        return NetworkException(
          'SSL certificate error. Please check your connection security.',
          code: 'SSL_ERROR',
          details: dioException.message,
        );
        
      case DioExceptionType.unknown:
      default:
        return ServerException(
          dioException.message ?? 'An unexpected error occurred',
          code: 'UNKNOWN',
          details: dioException.toString(),
        );
    }
  }
  
  /// Handle bad response errors based on status code
  AppException _handleBadResponse(DioException dioException) {
    final response = dioException.response;
    final statusCode = response?.statusCode;
    final responseData = response?.data;
    
    // Extract error message from response
    String errorMessage = _extractErrorMessage(responseData) ?? 
                         dioException.message ?? 
                         'Server error occurred';
    
    switch (statusCode) {
      case 400:
        return ValidationException(
          errorMessage,
          code: 'BAD_REQUEST',
          fieldErrors: _extractFieldErrors(responseData),
          details: responseData,
        );
        
      case 401:
        return AuthException(
          errorMessage.isEmpty ? 'Authentication failed' : errorMessage,
          code: 'UNAUTHORIZED',
          details: responseData,
        );
        
      case 403:
        return AuthorizationException(
          errorMessage.isEmpty ? 'Access forbidden' : errorMessage,
          code: 'FORBIDDEN',
          details: responseData,
        );
        
      case 404:
        return ServerException(
          errorMessage.isEmpty ? 'Resource not found' : errorMessage,
          code: 'NOT_FOUND',
          statusCode: statusCode,
          details: responseData,
        );
        
      case 409:
        return BusinessLogicException(
          errorMessage.isEmpty ? 'Conflict occurred' : errorMessage,
          code: 'CONFLICT',
          details: responseData,
        );
        
      case 422:
        return ValidationException(
          errorMessage.isEmpty ? 'Validation failed' : errorMessage,
          code: 'VALIDATION_ERROR',
          fieldErrors: _extractFieldErrors(responseData),
          details: responseData,
        );
        
      case 429:
        return ServerException(
          errorMessage.isEmpty ? 'Too many requests' : errorMessage,
          code: 'RATE_LIMIT',
          statusCode: statusCode,
          details: responseData,
        );
        
      case 500:
        return ServerException(
          errorMessage.isEmpty ? 'Internal server error' : errorMessage,
          code: 'INTERNAL_SERVER_ERROR',
          statusCode: statusCode,
          details: responseData,
        );
        
      case 502:
        return ServerException(
          errorMessage.isEmpty ? 'Bad gateway' : errorMessage,
          code: 'BAD_GATEWAY',
          statusCode: statusCode,
          details: responseData,
        );
        
      case 503:
        return ServerException(
          errorMessage.isEmpty ? 'Service unavailable' : errorMessage,
          code: 'SERVICE_UNAVAILABLE',
          statusCode: statusCode,
          details: responseData,
        );
        
      case 504:
        return ServerException(
          errorMessage.isEmpty ? 'Gateway timeout' : errorMessage,
          code: 'GATEWAY_TIMEOUT',
          statusCode: statusCode,
          details: responseData,
        );
        
      default:
        return ServerException(
          errorMessage.isEmpty ? 'Unknown server error' : errorMessage,
          code: 'UNKNOWN_SERVER_ERROR',
          statusCode: statusCode,
          details: responseData,
        );
    }
  }
  
  /// Extract error message from response data
  String? _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return null;
    
    if (responseData is Map<String, dynamic>) {
      // Try different common error message keys
      final errorKeys = ['message', 'error', 'detail', 'msg', 'description'];
      
      for (final key in errorKeys) {
        if (responseData.containsKey(key) && responseData[key] is String) {
          return responseData[key] as String;
        }
      }
      
      // Check for nested error objects
      if (responseData.containsKey('error') && responseData['error'] is Map) {
        final errorObj = responseData['error'] as Map<String, dynamic>;
        for (final key in errorKeys) {
          if (errorObj.containsKey(key) && errorObj[key] is String) {
            return errorObj[key] as String;
          }
        }
      }
    }
    
    return null;
  }
  
  /// Extract field-specific validation errors
  Map<String, List<String>>? _extractFieldErrors(dynamic responseData) {
    if (responseData == null || responseData is! Map<String, dynamic>) {
      return null;
    }
    
    final Map<String, List<String>> fieldErrors = {};
    
    // Check for 'errors' key (common in Laravel validation)
    if (responseData.containsKey('errors') && responseData['errors'] is Map) {
      final errors = responseData['errors'] as Map<String, dynamic>;
      
      for (final entry in errors.entries) {
        final field = entry.key;
        final value = entry.value;
        
        if (value is List) {
          fieldErrors[field] = value.map((e) => e.toString()).toList();
        } else if (value is String) {
          fieldErrors[field] = [value];
        }
      }
    }
    
    // Check for 'field_errors' key
    if (responseData.containsKey('field_errors') && 
        responseData['field_errors'] is Map) {
      final errors = responseData['field_errors'] as Map<String, dynamic>;
      
      for (final entry in errors.entries) {
        final field = entry.key;
        final value = entry.value;
        
        if (value is List) {
          fieldErrors[field] = value.map((e) => e.toString()).toList();
        } else if (value is String) {
          fieldErrors[field] = [value];
        }
      }
    }
    
    return fieldErrors.isNotEmpty ? fieldErrors : null;
  }
}
