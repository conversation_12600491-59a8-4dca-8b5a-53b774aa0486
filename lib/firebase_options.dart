// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCyEHFKqJsWRIk8WoESsculQDYDDmIIgp4',
    appId: '1:14251100231:web:e78dbfb505aa422ab60962',
    messagingSenderId: '14251100231',
    projectId: 'hm-collection-9c122',
    authDomain: 'hm-collection-9c122.firebaseapp.com',
    databaseURL: 'https://hm-collection-9c122-default-rtdb.firebaseio.com',
    storageBucket: 'hm-collection-9c122.firebasestorage.app',
    measurementId: 'G-RRFLYKWJN1',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDAstb2VakNT8FTbIancfrAwgO0Ku0guys',
    appId: '1:14251100231:android:f9373de7f9925390b60962',
    messagingSenderId: '14251100231',
    projectId: 'hm-collection-9c122',
    databaseURL: 'https://hm-collection-9c122-default-rtdb.firebaseio.com',
    storageBucket: 'hm-collection-9c122.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBM8ddfOeEJynBSQyw6LmNRRc6zyyIzbBY',
    appId: '1:14251100231:ios:aaa313478ada3f39b60962',
    messagingSenderId: '14251100231',
    projectId: 'hm-collection-9c122',
    databaseURL: 'https://hm-collection-9c122-default-rtdb.firebaseio.com',
    storageBucket: 'hm-collection-9c122.firebasestorage.app',
    iosClientId: '14251100231-4hmd7ecu7muhm2fr13oilmkvvm2gnej3.apps.googleusercontent.com',
    iosBundleId: 'com.example.hmCollection',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBM8ddfOeEJynBSQyw6LmNRRc6zyyIzbBY',
    appId: '1:14251100231:ios:aaa313478ada3f39b60962',
    messagingSenderId: '14251100231',
    projectId: 'hm-collection-9c122',
    databaseURL: 'https://hm-collection-9c122-default-rtdb.firebaseio.com',
    storageBucket: 'hm-collection-9c122.firebasestorage.app',
    iosClientId: '14251100231-4hmd7ecu7muhm2fr13oilmkvvm2gnej3.apps.googleusercontent.com',
    iosBundleId: 'com.example.hmCollection',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCyEHFKqJsWRIk8WoESsculQDYDDmIIgp4',
    appId: '1:14251100231:web:858921934d14ff70b60962',
    messagingSenderId: '14251100231',
    projectId: 'hm-collection-9c122',
    authDomain: 'hm-collection-9c122.firebaseapp.com',
    databaseURL: 'https://hm-collection-9c122-default-rtdb.firebaseio.com',
    storageBucket: 'hm-collection-9c122.firebasestorage.app',
    measurementId: 'G-FD7WBS1R0Y',
  );

}