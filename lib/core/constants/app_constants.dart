/// Application-wide constants for the Cloth Manufacturing App
class AppConstants {
  // App Information
  static const String appName = 'HM Collection';
  static const String appVersion = '1.0.0';
  static const String companyName = 'HM Manufacturing';

  // Development Configuration
  static const bool isDevelopmentMode = true; // Set to false for production
  static const bool useMockData =
      true; // Use mock data when API is not available

  // API Configuration
  static const int pageSize = 20;
  static const int timeoutDuration = 30; // seconds
  static const int maxRetryAttempts = 3;
  static const int retryDelay = 2; // seconds

  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String userPreferencesKey = 'user_preferences';
  static const String loginCredentialsKey = 'login_credentials';
  static const String autoLoginKey = 'auto_login';
  static const String lastLoginTimeKey = 'last_login_time';
  static const String userRoleKey = 'user_role';
  static const String userPermissionsKey = 'user_permissions';
  static const String lastSyncKey = 'last_sync';
  static const String offlineDataKey = 'offline_data';
  static const String settingsKey = 'app_settings';

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'MMM dd, yyyy';
  static const String displayDateTimeFormat = 'MMM dd, yyyy HH:mm';
  static const String timeFormat = 'HH:mm';
  static const String displayTimeFormat = 'hh:mm a';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int minPageSize = 5;

  // Cache Configuration
  static const int cacheExpiryHours = 24;
  static const int maxCacheSize = 100; // MB

  // Real-time Updates
  static const int heartbeatInterval = 30; // seconds
  static const int reconnectDelay = 5; // seconds
  static const int maxReconnectAttempts = 5;

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = [
    'pdf',
    'doc',
    'docx',
    'xls',
    'xlsx'
  ];

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;

  // Production Constants
  static const int defaultProductionTargetHours = 8;
  static const int maxOvertimeHours = 4;
  static const double defaultEfficiencyTarget = 85.0; // percentage

  // Quality Control
  static const double defaultQualityThreshold = 95.0; // percentage
  static const int maxDefectsPerBatch = 5;

  // Inventory
  static const int lowStockThreshold = 10;
  static const int criticalStockThreshold = 5;
  static const int reorderQuantityMultiplier = 2;

  // Notification Types
  static const String notificationTypeOrder = 'order';
  static const String notificationTypeProduction = 'production';
  static const String notificationTypeQuality = 'quality';
  static const String notificationTypeInventory = 'inventory';
  static const String notificationTypeSystem = 'system';

  // Error Messages
  static const String genericErrorMessage =
      'Something went wrong. Please try again.';
  static const String networkErrorMessage =
      'Please check your internet connection.';
  static const String timeoutErrorMessage =
      'Request timed out. Please try again.';
  static const String unauthorizedErrorMessage =
      'You are not authorized to perform this action.';
  static const String sessionExpiredMessage =
      'Your session has expired. Please login again.';

  // Success Messages
  static const String loginSuccessMessage = 'Login successful';
  static const String logoutSuccessMessage = 'Logout successful';
  static const String saveSuccessMessage = 'Data saved successfully';
  static const String updateSuccessMessage = 'Data updated successfully';
  static const String deleteSuccessMessage = 'Data deleted successfully';

  // Animation Durations
  static const int shortAnimationDuration = 200; // milliseconds
  static const int mediumAnimationDuration = 300; // milliseconds
  static const int longAnimationDuration = 500; // milliseconds

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 8.0;
  static const double cardElevation = 2.0;
  static const double buttonHeight = 48.0;
  static const double textFieldHeight = 56.0;

  // Feature Flags
  static const bool enableBiometricAuth = true;
  static const bool enableOfflineMode = true;
  static const bool enableRealTimeUpdates = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enableDebugMode = false;
}
