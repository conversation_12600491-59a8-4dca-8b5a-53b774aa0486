import 'package:equatable/equatable.dart';

/// Represents the analysis of defects in the quality control system
class DefectAnalysis extends Equatable {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, int> defectsByType;
  final Map<String, int> defectsBySeverity;
  final Map<String, int> defectsByDepartment;
  final int totalDefects;
  final int resolvedDefects;
  final int pendingDefects;
  final double averageResolutionTime; // in hours

  const DefectAnalysis({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.defectsByType,
    required this.defectsBySeverity,
    required this.defectsByDepartment,
    required this.totalDefects,
    required this.resolvedDefects,
    required this.pendingDefects,
    required this.averageResolutionTime,
  });

  /// Calculates the defect resolution rate as a percentage
  double get resolutionRate => totalDefects > 0 
      ? (resolvedDefects / totalDefects) * 100 
      : 0.0;

  @override
  List<Object?> get props => [
        id,
        startDate,
        endDate,
        defectsByType,
        defectsBySeverity,
        defectsByDepartment,
        totalDefects,
        resolvedDefects,
        pendingDefects,
        averageResolutionTime,
      ];

  /// Creates a copy of this DefectAnalysis with the given fields replaced
  DefectAnalysis copyWith({
    String? id,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, int>? defectsByType,
    Map<String, int>? defectsBySeverity,
    Map<String, int>? defectsByDepartment,
    int? totalDefects,
    int? resolvedDefects,
    int? pendingDefects,
    double? averageResolutionTime,
  }) {
    return DefectAnalysis(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      defectsByType: defectsByType ?? this.defectsByType,
      defectsBySeverity: defectsBySeverity ?? this.defectsBySeverity,
      defectsByDepartment: defectsByDepartment ?? this.defectsByDepartment,
      totalDefects: totalDefects ?? this.totalDefects,
      resolvedDefects: resolvedDefects ?? this.resolvedDefects,
      pendingDefects: pendingDefects ?? this.pendingDefects,
      averageResolutionTime: averageResolutionTime ?? this.averageResolutionTime,
    );
  }
}
