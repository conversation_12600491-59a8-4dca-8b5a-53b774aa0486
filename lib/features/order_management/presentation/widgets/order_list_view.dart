import 'package:flutter/material.dart';
import 'package:hm_collection/features/order_management/presentation/bloc/order_bloc.dart';
import '../../domain/entities/order_entities.dart';
//import '../bloc/order_event.dart' show OrderViewMode;

import '../../../../shared/enums/common_enums.dart';

/// A widget that displays a list of orders
class OrderListView extends StatelessWidget {
  final List<ManufacturingOrder> orders;
  final OrderViewMode viewMode;
  final Function(ManufacturingOrder)? onOrderTap;
  final Function(ManufacturingOrder)? onOrderLongPress;
  final Function(ManufacturingOrder)? onOrderEdit;
  final Function(ManufacturingOrder)? onOrderDelete;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final List<String>? selectedOrderIds;
  final Function(List<String>)? onSelectionChanged;
  final Function(String, OrderStatus)? onStatusChanged;
  final VoidCallback? onLoadMore;
  final bool hasMore;

  const OrderListView({
    Key? key,
    required this.orders,
    required this.viewMode,
    this.selectedOrderIds,
    this.onOrderTap,
    this.onOrderLongPress,
    this.onOrderEdit,
    this.onOrderDelete,
    this.isLoading = false,
    this.onRefresh,
    this.onSelectionChanged,
    this.onStatusChanged,
    this.onLoadMore,
    this.hasMore = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No orders found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Orders will appear here when they are created',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          Widget _buildOrderItem(ManufacturingOrder order) {
            return GestureDetector(
              onLongPress: onOrderLongPress != null ? () => onOrderLongPress!(order) : null,
              child: OrderListTile(
                order: order,
                isSelected: selectedOrderIds?.contains(order.id) ?? false,
                onTap: () => onOrderTap?.call(order),
                onEdit: onOrderEdit != null ? () => onOrderEdit!(order) : null,
                onDelete: onOrderDelete != null ? () => onOrderDelete!(order) : null,
                onStatusChanged: onStatusChanged != null
                    ? (status) => onStatusChanged!(order.id, status)
                    : null,
              ),
            );
          }
          return _buildOrderItem(order);
        },
      ),
    );
  }
}

/// Individual order list tile
class OrderListTile extends StatelessWidget {
  final ManufacturingOrder order;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final ValueChanged<OrderStatus>? onStatusChanged;

  const OrderListTile({
    Key? key,
    required this.order,
    this.isSelected = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onStatusChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        onTap: onTap,
        leading: Stack(
          children: [
            CircleAvatar(
              backgroundColor: _getStatusColor(order.status as OrderStatus),
              child: Text(
                order.orderNumber.isNotEmpty ? order.orderNumber.substring(0, 2).toUpperCase() : 'NA',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (isSelected)
              const Positioned(
                right: 0,
                bottom: 0,
                child: CircleAvatar(
                  radius: 10,
                  backgroundColor: Colors.blue,
                  child: Icon(Icons.check, size: 14, color: Colors.white),
                ),
              ),
          ],
        ),
        title: Text(
          'Order #${order.orderNumber}',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Customer: ${order.clientName}'),
            Text('Total: \$${order.pricing.totalAmount.toStringAsFixed(2)}'),
            Text('Status: ${order.status.toString().split('.').last}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                onEdit?.call();
                break;
              case 'delete':
                onDelete?.call();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('Edit'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Delete', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.draft:
        return Colors.grey;
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.inProduction:
        return Colors.purple;
      case OrderStatus.completed:
        return Colors.green;
        case OrderStatus.qualityCheck:
        return Colors.orange[800]!;
      case OrderStatus.shipped:
        return Colors.teal;
      case OrderStatus.delivered:
        return Colors.green[800]!;
      case OrderStatus.cancelled:
        return Colors.red;
      case OrderStatus.onHold:
        return Colors.amber[800]!;
    }
  }
}