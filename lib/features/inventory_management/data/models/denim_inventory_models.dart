import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart' hide Unit;
import 'package:json_annotation/json_annotation.dart';
import '../../../../shared/enums/purchase_status_enum.dart';
import '../../../../shared/enums/return_status_enum.dart';
import '../../domain/entities/denim_inventory_entities.dart';
import '../../../../shared/enums/unit_enum.dart';

// Import extensions for Unit enum

part 'denim_inventory_models.g.dart';

/// Denim Type model for Firebase
@JsonSerializable()
class DenimTypeModel extends DenimType {
  @override
  final String id;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? deletedAt;
  @override
  final String? createdBy;
  @override
  final String? updatedBy;
  @override
  final String denimCode;
  @override
  final String type;
  @override
  final String description;
  @override
  final double gsm;
  @override
  final double width;
  @override
  final String? supplierId;
  @override
  final String? supplierName;
  @override
  final bool isActive;

  const DenimTypeModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    required this.denimCode,
    required this.type,
    required this.description,
    required this.gsm,
    required this.width,
    this.supplierId,
    this.supplierName,
    this.isActive = true,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          denimCode: denimCode,
          type: type,
          description: description,
          gsm: gsm,
          width: width,
          supplierId: supplierId,
          supplierName: supplierName,
          isActive: isActive,
        );

  factory DenimTypeModel.fromJson(Map<String, dynamic> json) {
    return DenimTypeModel(
      id: json['id'] as String,
      denimCode: json['denimCode'] as String,
      type: json['type'] as String,
      description: json['description'] as String,
      gsm: (json['gsm'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      supplierId: json['supplierId'] as String?,
      supplierName: json['supplierName'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt'] as String) : null,
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'denimCode': denimCode,
      'type': type,
      'description': description,
      'gsm': gsm,
      'width': width,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimTypeModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimTypeModel(
      id: doc.id,
      denimCode: data['denimCode'] ?? '',
      type: data['type'] ?? '',
      description: data['description'] ?? '',
      gsm: (data['gsm'] ?? 0.0).toDouble(),
      width: (data['width'] ?? 0.0).toDouble(),
      supplierId: data['supplierId'],
      supplierName: data['supplierName'],
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'denimCode': denimCode,
      'type': type,
      'description': description,
      'gsm': gsm,
      'width': width,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimTypeModel.fromEntity(DenimType entity) {
    return DenimTypeModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      denimCode: entity.denimCode,
      type: entity.type,
      description: entity.description,
      gsm: entity.gsm,
      width: entity.width,
      supplierId: entity.supplierId,
      supplierName: entity.supplierName,
      isActive: entity.isActive,
    );
  }
}

/// Denim Roll model for Firebase
@JsonSerializable()
class DenimRollModel extends DenimRoll {
  DenimRollModel({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    super.createdBy,
    super.updatedBy,
    required super.rollId,
    required super.denimTypeId,
    required super.denimCode,
    required super.denimType,
    required super.color,
    required super.width,
    required super.totalPurchasedQty,
    super.returnedQty = 0.0,
    super.usedQty = 0.0,
    required super.balanceQty,
    super.location,
    required super.status,
    Unit unit = Unit.meters,
    required super.gsm,
    super.supplierId,
    super.supplierName,
    super.metadata,
  }) : super(
          unit: unit.displayName,
        );

  factory DenimRollModel.fromJson(Map<String, dynamic> json) {
    return DenimRollModel(
      id: json['id'] as String,
      rollId: json['rollId'] as String,
      denimTypeId: json['denimTypeId'] as String,
      denimCode: json['denimCode'] as String,
      denimType: json['denimType'] as String,
      color: json['color'] as String,
      width: (json['width'] as num).toDouble(),
      totalPurchasedQty: (json['totalPurchasedQty'] as num).toDouble(),
      returnedQty: (json['returnedQty'] as num?)?.toDouble() ?? 0.0,
      usedQty: (json['usedQty'] as num?)?.toDouble() ?? 0.0,
      balanceQty: (json['balanceQty'] as num).toDouble(),
      location: json['location'] as String?,
      status: DenimRollStatus.values.firstWhere(
        (e) => e.toString() == 'DenimRollStatus.${json['status']}',
        orElse: () => DenimRollStatus.inStock,
      ),
      unit: json['unit'] != null ? UnitUtils.fromString(json['unit'] as String) : Unit.meters,
      gsm: (json['gsm'] as num).toDouble(),
      supplierId: json['supplierId'] as String?,
      supplierName: json['supplierName'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map<String, dynamic>? ?? {}),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt'] as String) : null,
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rollId': rollId,
      'denimTypeId': denimTypeId,
      'denimCode': denimCode,
      'denimType': denimType,
      'color': color,
      'width': width,
      'totalPurchasedQty': totalPurchasedQty,
      'returnedQty': returnedQty,
      'usedQty': usedQty,
      'balanceQty': balanceQty,
      'location': location,
      'status': status.toString().split('.').last,
      'unit': unit,
      'gsm': gsm,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimRollModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimRollModel(
      id: doc.id,
      rollId: data['rollId'] ?? '',
      denimTypeId: data['denimTypeId'] ?? '',
      denimCode: data['denimCode'] ?? '',
      denimType: data['denimType'] ?? '',
      color: data['color'] ?? '',
      width: (data['width'] ?? 0.0).toDouble(),
      totalPurchasedQty: (data['totalPurchasedQty'] ?? 0.0).toDouble(),
      returnedQty: (data['returnedQty'] ?? 0.0).toDouble(),
      usedQty: (data['usedQty'] ?? 0.0).toDouble(),
      balanceQty: (data['balanceQty'] ?? 0.0).toDouble(),
      location: data['location'],
      status: DenimRollStatus.values.firstWhere(
        (e) => e.value == data['status'],
        orElse: () => DenimRollStatus.inStock,
      ),
      unit: data['unit'] != null ? UnitUtils.fromString(data['unit']) : Unit.meters,
      gsm: (data['gsm'] ?? 0.0).toDouble(),
      supplierId: data['supplierId'],
      supplierName: data['supplierName'],
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'rollId': rollId,
      'denimTypeId': denimTypeId,
      'denimCode': denimCode,
      'denimType': denimType,
      'color': color,
      'width': width,
      'totalPurchasedQty': totalPurchasedQty,
      'returnedQty': returnedQty,
      'usedQty': usedQty,
      'balanceQty': balanceQty,
      'location': location,
      'status': status.value,
      'unit': unit,
      'gsm': gsm,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimRollModel.fromEntity(DenimRoll entity) {
    return DenimRollModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      rollId: entity.rollId,
      denimTypeId: entity.denimTypeId,
      denimCode: entity.denimCode,
      denimType: entity.denimType,
      color: entity.color,
      width: entity.width,
      totalPurchasedQty: entity.totalPurchasedQty,
      returnedQty: entity.returnedQty,
      usedQty: entity.usedQty,
      balanceQty: entity.balanceQty,
      location: entity.location,
      status: entity.status,
      unit: UnitUtils.fromString(entity.unit),
      gsm: entity.gsm,
      supplierId: entity.supplierId,
      supplierName: entity.supplierName,
      metadata: entity.metadata,
    );
  }

  /// Update stock quantities
  DenimRollModel updateStock({
    double? returnedQty,
    double? usedQty,
    DenimRollStatus? status,
    String? location,
  }) {
    final newReturnedQty = returnedQty ?? this.returnedQty;
    final newUsedQty = usedQty ?? this.usedQty;
    final newBalanceQty = totalPurchasedQty - newReturnedQty - newUsedQty;

    return DenimRollModel(
      id: this.id,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy,
      rollId: rollId,
      denimTypeId: denimTypeId,
      denimCode: denimCode,
      denimType: denimType,
      color: color,
      width: width,
      totalPurchasedQty: totalPurchasedQty,
      returnedQty: newReturnedQty,
      usedQty: newUsedQty,
      balanceQty: newBalanceQty,
      location: location ?? this.location,
      status: status ?? this.status,
      unit: UnitUtils.fromString(this.unit),
      gsm: gsm,
      supplierId: supplierId,
      supplierName: supplierName,
      metadata: metadata,
    );
  }
}

/// Denim Purchase Record model for Firebase
@JsonSerializable()
class DenimPurchaseRecordModel extends DenimPurchaseRecord {
  const DenimPurchaseRecordModel({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    super.createdBy,
    super.updatedBy,
    required super.srNo,
    required super.purchaseDate,
    required super.supplierName,
    super.supplierId,
    required super.rollId,
    required super.denimTypeId,
    required super.denimType,
    required super.color,
    required super.width,
    required super.length,
    super.unit,
    required super.gsm,
    required super.ratePerUnit,
    required super.totalCost,
    required super.invoiceNo,
    super.remarks,
    PurchaseStatus super.status = PurchaseStatus.completed,
    super.driverName,
    super.vehicleNumber,
  });

  factory DenimPurchaseRecordModel.fromJson(Map<String, dynamic> json) =>
      _$DenimPurchaseRecordModelFromJson(json);

  Map<String, dynamic> toJson() => _$DenimPurchaseRecordModelToJson(this);

  factory DenimPurchaseRecordModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimPurchaseRecordModel(
      id: doc.id,
      srNo: (data['srNo'] ?? 0).toInt(),
      purchaseDate: (data['purchaseDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      supplierName: data['supplierName'] ?? '',
      supplierId: data['supplierId'],
      rollId: data['rollId'] ?? '',
      denimTypeId: data['denimTypeId'] ?? '',
      denimType: data['denimType'] ?? '',
      color: data['color'] ?? '',
      width: (data['width'] ?? 0.0).toDouble(),
      length: (data['length'] ?? 0.0).toDouble(),
      unit: data['unit'] ?? 'yards',
      gsm: (data['gsm'] ?? 0.0).toDouble(),
      ratePerUnit: (data['ratePerUnit'] ?? 0.0).toDouble(),
      totalCost: (data['totalCost'] ?? 0.0).toDouble(),
      invoiceNo: data['invoiceNo'] ?? '',
      remarks: data['remarks'],
      status: PurchaseStatus.values.firstWhere(
        (e) => e.value == data['status'],
        orElse: () => PurchaseStatus.completed,
      ),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
      driverName: data['driverName'] as String?,
      vehicleNumber: data['vehicleNumber'] as String?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'srNo': srNo,
      'purchaseDate': Timestamp.fromDate(purchaseDate),
      'supplierName': supplierName,
      'supplierId': supplierId,
      'rollId': rollId,
      'denimTypeId': denimTypeId,
      'denimType': denimType,
      'color': color,
      'width': width,
      'length': length,
      'unit': unit,
      'gsm': gsm,
      'ratePerUnit': ratePerUnit,
      'totalCost': totalCost,
      'invoiceNo': invoiceNo,
      'remarks': remarks,
      'status': status.value,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'driverName': driverName,
      'vehicleNumber': vehicleNumber,
    };
  }

  factory DenimPurchaseRecordModel.fromEntity(DenimPurchaseRecord entity) {
    return DenimPurchaseRecordModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      srNo: entity.srNo,
      purchaseDate: entity.purchaseDate,
      supplierName: entity.supplierName,
      supplierId: entity.supplierId,
      rollId: entity.rollId,
      denimTypeId: entity.denimTypeId,
      denimType: entity.denimType,
      color: entity.color,
      width: entity.width,
      length: entity.length,
      unit: entity.unit,
      gsm: entity.gsm,
      ratePerUnit: entity.ratePerUnit,
      totalCost: entity.totalCost,
      invoiceNo: entity.invoiceNo,
      remarks: entity.remarks,
      status: entity.status,
      driverName: entity.driverName,
      vehicleNumber: entity.vehicleNumber,
    );
  }
}

/// Denim Return Record model for Firebase
@JsonSerializable()
class DenimReturnRecordModel extends DenimReturnRecord {
  const DenimReturnRecordModel({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    super.createdBy,
    super.updatedBy,
    required super.srNo,
    required super.returnDate,
    required super.rollId,
    required super.supplierName,
    super.supplierId,
    required super.reasonForReturn,
    required super.quantityReturned,
    super.unit,
    required super.debitNoteNo,
    super.remarks,
    ReturnStatus super.status = ReturnStatus.pending,
    super.refundAmount,
  });

  factory DenimReturnRecordModel.fromJson(Map<String, dynamic> json) =>
      _$DenimReturnRecordModelFromJson(json);

  Map<String, dynamic> toJson() => _$DenimReturnRecordModelToJson(this);

  factory DenimReturnRecordModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimReturnRecordModel(
      id: doc.id,
      srNo: (data['srNo'] ?? 0).toInt(),
      returnDate: (data['returnDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      rollId: data['rollId'] ?? '',
      supplierName: data['supplierName'] ?? '',
      supplierId: data['supplierId'],
      reasonForReturn: data['reasonForReturn'] ?? '',
      quantityReturned: (data['quantityReturned'] ?? 0.0).toDouble(),
      unit: data['unit'] ?? 'yards',
      debitNoteNo: data['debitNoteNo'] ?? '',
      remarks: data['remarks'],
      status: ReturnStatus.values.firstWhere(
        (e) => e.value == data['status'],
        orElse: () => ReturnStatus.pending,
      ),
      refundAmount: (data['refundAmount'] as num?)?.toDouble(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'srNo': srNo,
      'returnDate': Timestamp.fromDate(returnDate),
      'rollId': rollId,
      'supplierName': supplierName,
      'supplierId': supplierId,
      'reasonForReturn': reasonForReturn,
      'quantityReturned': quantityReturned,
      'unit': unit,
      'debitNoteNo': debitNoteNo,
      'remarks': remarks,
      'status': status.value,
      'refundAmount': refundAmount,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimReturnRecordModel.fromEntity(DenimReturnRecord entity) {
    return DenimReturnRecordModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      srNo: entity.srNo,
      returnDate: entity.returnDate,
      rollId: entity.rollId,
      supplierName: entity.supplierName,
      supplierId: entity.supplierId,
      reasonForReturn: entity.reasonForReturn,
      quantityReturned: entity.quantityReturned,
      unit: entity.unit,
      debitNoteNo: entity.debitNoteNo,
      remarks: entity.remarks,
      status: entity.status,
      refundAmount: entity.refundAmount,
    );
  }
}
