part of 'analytics_bloc.dart';

/// Base analytics event
abstract class AnalyticsEvent extends Equatable {
  const AnalyticsEvent();

  @override
  List<Object?> get props => [];
}

// Dashboard Events

/// Get dashboards requested
class GetDashboardsRequested extends AnalyticsEvent {
  final DashboardFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetDashboardsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Get dashboard by ID requested
class GetDashboardByIdRequested extends AnalyticsEvent {
  final String dashboardId;

  const GetDashboardByIdRequested(this.dashboardId);

  @override
  List<Object?> get props => [dashboardId];
}

/// Create dashboard requested
class CreateDashboardRequested extends AnalyticsEvent {
  final CreateDashboardRequest request;

  const CreateDashboardRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update dashboard requested
class UpdateDashboardRequested extends AnalyticsEvent {
  final UpdateDashboardRequest request;

  const UpdateDashboardRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete dashboard requested
class DeleteDashboardRequested extends AnalyticsEvent {
  final String dashboardId;

  const DeleteDashboardRequested(this.dashboardId);

  @override
  List<Object?> get props => [dashboardId];
}

/// Clone dashboard requested
class CloneDashboardRequested extends AnalyticsEvent {
  final String dashboardId;
  final String newName;

  const CloneDashboardRequested({
    required this.dashboardId,
    required this.newName,
  });

  @override
  List<Object?> get props => [dashboardId, newName];
}

/// Share dashboard requested
class ShareDashboardRequested extends AnalyticsEvent {
  final ShareDashboardRequest request;

  const ShareDashboardRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Search dashboards requested
class SearchDashboardsRequested extends AnalyticsEvent {
  final String query;
  final DashboardFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchDashboardsRequested({
    required this.query,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

// Widget Events

/// Add widget requested
class AddWidgetRequested extends AnalyticsEvent {
  final AddWidgetRequest request;

  const AddWidgetRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update widget requested
class UpdateWidgetRequested extends AnalyticsEvent {
  final UpdateWidgetRequest request;

  const UpdateWidgetRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Remove widget requested
class RemoveWidgetRequested extends AnalyticsEvent {
  final String dashboardId;
  final String widgetId;

  const RemoveWidgetRequested({
    required this.dashboardId,
    required this.widgetId,
  });

  @override
  List<Object?> get props => [dashboardId, widgetId];
}

/// Reorder widgets requested
class ReorderWidgetsRequested extends AnalyticsEvent {
  final ReorderWidgetsRequest request;

  const ReorderWidgetsRequested(this.request);

  @override
  List<Object?> get props => [request];
}

// Report Events

/// Get reports requested
class GetReportsRequested extends AnalyticsEvent {
  final ReportFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetReportsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Get report by ID requested
class GetReportByIdRequested extends AnalyticsEvent {
  final String reportId;

  const GetReportByIdRequested(this.reportId);

  @override
  List<Object?> get props => [reportId];
}

/// Generate report requested
class GenerateReportRequested extends AnalyticsEvent {
  final GenerateReportRequest request;

  const GenerateReportRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Schedule report requested
class ScheduleReportRequested extends AnalyticsEvent {
  final ScheduleReportRequest request;

  const ScheduleReportRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Cancel report requested
class CancelReportRequested extends AnalyticsEvent {
  final String reportId;

  const CancelReportRequested(this.reportId);

  @override
  List<Object?> get props => [reportId];
}

/// Delete report requested
class DeleteReportRequested extends AnalyticsEvent {
  final String reportId;

  const DeleteReportRequested(this.reportId);

  @override
  List<Object?> get props => [reportId];
}

/// Search reports requested
class SearchReportsRequested extends AnalyticsEvent {
  final String query;
  final ReportFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchReportsRequested({
    required this.query,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

// Data Analytics Events

/// Get KPI data requested
class GetKpiDataRequested extends AnalyticsEvent {
  final List<String> metrics;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? filters;

  const GetKpiDataRequested({
    required this.metrics,
    required this.startDate,
    required this.endDate,
    this.filters,
  });

  @override
  List<Object?> get props => [metrics, startDate, endDate, filters];
}

/// Get chart data requested
class GetChartDataRequested extends AnalyticsEvent {
  final String chartType;
  final List<String> metrics;
  final List<String> dimensions;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? filters;

  const GetChartDataRequested({
    required this.chartType,
    required this.metrics,
    required this.dimensions,
    required this.startDate,
    required this.endDate,
    this.filters,
  });

  @override
  List<Object?> get props => [chartType, metrics, dimensions, startDate, endDate, filters];
}

/// Get table data requested
class GetTableDataRequested extends AnalyticsEvent {
  final List<String> columns;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? filters;
  final String? sortBy;
  final SortOrder? sortOrder;
  final PaginationParams? pagination;

  const GetTableDataRequested({
    required this.columns,
    required this.startDate,
    required this.endDate,
    this.filters,
    this.sortBy,
    this.sortOrder,
    this.pagination,
  });

  @override
  List<Object?> get props => [columns, startDate, endDate, filters, sortBy, sortOrder, pagination];
}

// Performance Metrics Events

/// Get production metrics requested
class GetProductionMetricsRequested extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  final String? productId;

  const GetProductionMetricsRequested({
    required this.startDate,
    required this.endDate,
    this.departmentId,
    this.productId,
  });

  @override
  List<Object?> get props => [startDate, endDate, departmentId, productId];
}

/// Get quality metrics requested
class GetQualityMetricsRequested extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  final String? productId;

  const GetQualityMetricsRequested({
    required this.startDate,
    required this.endDate,
    this.departmentId,
    this.productId,
  });

  @override
  List<Object?> get props => [startDate, endDate, departmentId, productId];
}

/// Get financial metrics requested
class GetFinancialMetricsRequested extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  final String? departmentId;

  const GetFinancialMetricsRequested({
    required this.startDate,
    required this.endDate,
    this.costCenterId,
    this.departmentId,
  });

  @override
  List<Object?> get props => [startDate, endDate, costCenterId, departmentId];
}

/// Get inventory metrics requested
class GetInventoryMetricsRequested extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;
  final String? warehouseId;
  final String? categoryId;

  const GetInventoryMetricsRequested({
    required this.startDate,
    required this.endDate,
    this.warehouseId,
    this.categoryId,
  });

  @override
  List<Object?> get props => [startDate, endDate, warehouseId, categoryId];
}

/// Get efficiency metrics requested
class GetEfficiencyMetricsRequested extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  final String? machineId;

  const GetEfficiencyMetricsRequested({
    required this.startDate,
    required this.endDate,
    this.departmentId,
    this.machineId,
  });

  @override
  List<Object?> get props => [startDate, endDate, departmentId, machineId];
}

// Advanced Analytics Events

/// Get trend data requested
class GetTrendDataRequested extends AnalyticsEvent {
  final String metric;
  final DateTime startDate;
  final DateTime endDate;
  final String granularity;
  final Map<String, dynamic>? filters;

  const GetTrendDataRequested({
    required this.metric,
    required this.startDate,
    required this.endDate,
    required this.granularity,
    this.filters,
  });

  @override
  List<Object?> get props => [metric, startDate, endDate, granularity, filters];
}

/// Get comparative analysis requested
class GetComparativeAnalysisRequested extends AnalyticsEvent {
  final List<String> metrics;
  final DateTime currentPeriodStart;
  final DateTime currentPeriodEnd;
  final DateTime previousPeriodStart;
  final DateTime previousPeriodEnd;
  final Map<String, dynamic>? filters;

  const GetComparativeAnalysisRequested({
    required this.metrics,
    required this.currentPeriodStart,
    required this.currentPeriodEnd,
    required this.previousPeriodStart,
    required this.previousPeriodEnd,
    this.filters,
  });

  @override
  List<Object?> get props => [
        metrics,
        currentPeriodStart,
        currentPeriodEnd,
        previousPeriodStart,
        previousPeriodEnd,
        filters,
      ];
}

/// Get forecast requested
class GetForecastRequested extends AnalyticsEvent {
  final String metric;
  final DateTime startDate;
  final DateTime endDate;
  final int forecastPeriods;
  final Map<String, dynamic>? parameters;

  const GetForecastRequested({
    required this.metric,
    required this.startDate,
    required this.endDate,
    required this.forecastPeriods,
    this.parameters,
  });

  @override
  List<Object?> get props => [metric, startDate, endDate, forecastPeriods, parameters];
}

/// Get anomaly detection requested
class GetAnomalyDetectionRequested extends AnalyticsEvent {
  final String metric;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? parameters;

  const GetAnomalyDetectionRequested({
    required this.metric,
    required this.startDate,
    required this.endDate,
    this.parameters,
  });

  @override
  List<Object?> get props => [metric, startDate, endDate, parameters];
}

/// Get real-time metrics requested
class GetRealTimeMetricsRequested extends AnalyticsEvent {
  final List<String> metrics;
  final Map<String, dynamic>? filters;

  const GetRealTimeMetricsRequested({
    required this.metrics,
    this.filters,
  });

  @override
  List<Object?> get props => [metrics, filters];
}

// Export Events

/// Export dashboard requested
class ExportDashboardRequested extends AnalyticsEvent {
  final String dashboardId;
  final String format;
  final Map<String, dynamic>? options;

  const ExportDashboardRequested({
    required this.dashboardId,
    required this.format,
    this.options,
  });

  @override
  List<Object?> get props => [dashboardId, format, options];
}

/// Export report requested
class ExportReportRequested extends AnalyticsEvent {
  final String reportId;
  final String format;
  final Map<String, dynamic>? options;

  const ExportReportRequested({
    required this.reportId,
    required this.format,
    this.options,
  });

  @override
  List<Object?> get props => [reportId, format, options];
}

// Metadata Events

/// Get available metrics requested
class GetAvailableMetricsRequested extends AnalyticsEvent {
  final String? category;
  final String? dataSource;

  const GetAvailableMetricsRequested({
    this.category,
    this.dataSource,
  });

  @override
  List<Object?> get props => [category, dataSource];
}

/// Get available dimensions requested
class GetAvailableDimensionsRequested extends AnalyticsEvent {
  final String? category;
  final String? dataSource;

  const GetAvailableDimensionsRequested({
    this.category,
    this.dataSource,
  });

  @override
  List<Object?> get props => [category, dataSource];
}
