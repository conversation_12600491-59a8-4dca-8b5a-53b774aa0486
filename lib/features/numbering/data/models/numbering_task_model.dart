import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/numbering_task.dart';

part 'numbering_task_model.g.dart';

/// Data model for NumberingTask entity
@JsonSerializable()
class NumberingTaskModel {
  final String id;
  @Json<PERSON><PERSON>(name: 'date', fromJson: _dateFromJson, toJson: _dateToJson)
  final DateTime date;
  @Json<PERSON><PERSON>(name: 'order_no')
  final String orderNo;
  @JsonKey(name: 'client_name')
  final String clientName;
  @Json<PERSON>ey(name: 'design_no')
  final String designNo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'size', fromJson: _sizeFromJson, toJson: _sizeToJson)
  final GarmentSize size;
  final String color;
  @JsonKey(name: 'bundle_no')
  final String bundleNo;
  @Json<PERSON>ey(name: 'piece_start_no')
  final int pieceStartNo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'piece_end_no')
  final int pieceEndNo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'rate_per_piece')
  final double ratePerPiece;
  @<PERSON>son<PERSON><PERSON>(name: 'checked_by')
  final String checkedBy;
  final String remarks;
  @Json<PERSON><PERSON>(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
  final NumberingTaskStatus status;
  @JsonKey(name: 'assigned_to')
  final String? assignedTo;
  @JsonKey(name: 'completed_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime? completedAt;
  @JsonKey(name: 'sent_to_munda_at', fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime? sentToMundaAt;
  @JsonKey(name: 'created_at', fromJson: _dateFromJson, toJson: _dateToJson)
  final DateTime createdAt;
  @JsonKey(name: 'updated_at', fromJson: _dateFromJson, toJson: _dateToJson)
  final DateTime updatedAt;
  @JsonKey(name: 'created_by')
  final String createdBy;
  @JsonKey(name: 'updated_by')
  final String updatedBy;

  const NumberingTaskModel({
    required this.id,
    required this.date,
    required this.orderNo,
    required this.clientName,
    required this.designNo,
    required this.size,
    required this.color,
    required this.bundleNo,
    required this.pieceStartNo,
    required this.pieceEndNo,
    required this.ratePerPiece,
    required this.checkedBy,
    required this.remarks,
    required this.status,
    this.assignedTo,
    this.completedAt,
    this.sentToMundaAt,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    required this.updatedBy,
  });

  /// Convert from domain entity to data model
  factory NumberingTaskModel.fromEntity(NumberingTask entity) {
    return NumberingTaskModel(
      id: entity.id,
      date: entity.date,
      orderNo: entity.orderNo,
      clientName: entity.clientName,
      designNo: entity.designNo,
      size: entity.size,
      color: entity.color,
      bundleNo: entity.bundleNo,
      pieceStartNo: entity.pieceStartNo,
      pieceEndNo: entity.pieceEndNo,
      ratePerPiece: entity.ratePerPiece,
      checkedBy: entity.checkedBy,
      remarks: entity.remarks,
      status: entity.status,
      assignedTo: entity.assignedTo,
      completedAt: entity.completedAt,
      sentToMundaAt: entity.sentToMundaAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
    );
  }

  /// Convert to domain entity
  NumberingTask toEntity() {
    return NumberingTask(
      id: id,
      date: date,
      orderNo: orderNo,
      clientName: clientName,
      designNo: designNo,
      size: size,
      color: color,
      bundleNo: bundleNo,
      pieceStartNo: pieceStartNo,
      pieceEndNo: pieceEndNo,
      ratePerPiece: ratePerPiece,
      checkedBy: checkedBy,
      remarks: remarks,
      status: status,
      assignedTo: assignedTo,
      completedAt: completedAt,
      sentToMundaAt: sentToMundaAt,
      createdAt: createdAt,
      updatedAt: updatedAt,
      createdBy: createdBy,
      updatedBy: updatedBy,
    );
  }

  /// Create from JSON
  factory NumberingTaskModel.fromJson(Map<String, dynamic> json) =>
      _$NumberingTaskModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NumberingTaskModelToJson(this);

  /// Create from Firestore document
  factory NumberingTaskModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = doc.id;
    return NumberingTaskModel.fromJson(data);
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id'); // Remove ID as it's handled by Firestore
    return json;
  }

  /// Create a copy with updated fields
  NumberingTaskModel copyWith({
    String? id,
    DateTime? date,
    String? orderNo,
    String? clientName,
    String? designNo,
    GarmentSize? size,
    String? color,
    String? bundleNo,
    int? pieceStartNo,
    int? pieceEndNo,
    double? ratePerPiece,
    String? checkedBy,
    String? remarks,
    NumberingTaskStatus? status,
    String? assignedTo,
    DateTime? completedAt,
    DateTime? sentToMundaAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return NumberingTaskModel(
      id: id ?? this.id,
      date: date ?? this.date,
      orderNo: orderNo ?? this.orderNo,
      clientName: clientName ?? this.clientName,
      designNo: designNo ?? this.designNo,
      size: size ?? this.size,
      color: color ?? this.color,
      bundleNo: bundleNo ?? this.bundleNo,
      pieceStartNo: pieceStartNo ?? this.pieceStartNo,
      pieceEndNo: pieceEndNo ?? this.pieceEndNo,
      ratePerPiece: ratePerPiece ?? this.ratePerPiece,
      checkedBy: checkedBy ?? this.checkedBy,
      remarks: remarks ?? this.remarks,
      status: status ?? this.status,
      assignedTo: assignedTo ?? this.assignedTo,
      completedAt: completedAt ?? this.completedAt,
      sentToMundaAt: sentToMundaAt ?? this.sentToMundaAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  // JSON conversion helper methods
  static DateTime _dateFromJson(dynamic value) {
    if (value is Timestamp) {
      return value.toDate();
    } else if (value is String) {
      return DateTime.parse(value);
    } else if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }
    return DateTime.now();
  }

  static dynamic _dateToJson(DateTime date) => date.toIso8601String();

  static DateTime? _dateTimeFromJson(dynamic value) {
    if (value == null) return null;
    if (value is Timestamp) {
      return value.toDate();
    } else if (value is String) {
      return DateTime.parse(value);
    } else if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }
    return null;
  }

  static dynamic _dateTimeToJson(DateTime? date) => date?.toIso8601String();

  static GarmentSize _sizeFromJson(String value) => GarmentSize.fromString(value);
  static String _sizeToJson(GarmentSize size) => size.value;

  static NumberingTaskStatus _statusFromJson(String value) => NumberingTaskStatus.fromString(value);
  static String _statusToJson(NumberingTaskStatus status) => status.value;
}

/// Statistics model for data layer
@JsonSerializable()
class NumberingTaskStatisticsModel {
  @JsonKey(name: 'total_tasks')
  final int totalTasks;
  @JsonKey(name: 'pending_tasks')
  final int pendingTasks;
  @JsonKey(name: 'in_progress_tasks')
  final int inProgressTasks;
  @JsonKey(name: 'completed_tasks')
  final int completedTasks;
  @JsonKey(name: 'sent_to_munda_tasks')
  final int sentToMundaTasks;
  @JsonKey(name: 'total_amount')
  final double totalAmount;
  @JsonKey(name: 'average_rate_per_piece')
  final double averageRatePerPiece;
  @JsonKey(name: 'total_pieces')
  final int totalPieces;
  @JsonKey(name: 'tasks_by_date')
  final Map<String, int> tasksByDate;
  @JsonKey(name: 'tasks_by_user')
  final Map<String, int> tasksByUser;

  const NumberingTaskStatisticsModel({
    required this.totalTasks,
    required this.pendingTasks,
    required this.inProgressTasks,
    required this.completedTasks,
    required this.sentToMundaTasks,
    required this.totalAmount,
    required this.averageRatePerPiece,
    required this.totalPieces,
    required this.tasksByDate,
    required this.tasksByUser,
  });

  /// Convert to domain entity
  NumberingTaskStatistics toEntity() {
    return NumberingTaskStatistics(
      totalTasks: totalTasks,
      pendingTasks: pendingTasks,
      inProgressTasks: inProgressTasks,
      completedTasks: completedTasks,
      sentToMundaTasks: sentToMundaTasks,
      totalAmount: totalAmount,
      averageRatePerPiece: averageRatePerPiece,
      totalPieces: totalPieces,
      tasksByDate: tasksByDate,
      tasksByUser: tasksByUser,
    );
  }

  /// Create from JSON
  factory NumberingTaskStatisticsModel.fromJson(Map<String, dynamic> json) =>
      _$NumberingTaskStatisticsModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NumberingTaskStatisticsModelToJson(this);
}
