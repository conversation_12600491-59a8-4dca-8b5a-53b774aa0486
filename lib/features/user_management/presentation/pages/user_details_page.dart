import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../auth/domain/entities/user.dart';
import '../bloc/user_management_bloc.dart';

/// User details page
class UserDetailsPage extends StatefulWidget {
  final String userId;

  const UserDetailsPage({
    super.key,
    required this.userId,
  });

  @override
  State<UserDetailsPage> createState() => _UserDetailsPageState();
}

class _UserDetailsPageState extends State<UserDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<UserManagementBloc>()
        ..add(LoadUserDetailsRequested(widget.userId)),
      child: Scaffold(
        body: BlocBuilder<UserManagementBloc, UserManagementState>(
          builder: (context, state) {
            if (state is UserManagementLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is UserManagementError) {
              return _buildErrorWidget(state.message);
            }

            if (state is UserDetailsLoaded) {
              return _buildUserDetails(state.user);
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Scaffold(
      appBar: AppBar(title: const Text('User Details')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading user details',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.read<UserManagementBloc>().add(
                  LoadUserDetailsRequested(widget.userId),
                );
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDetails(User user) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                background: _buildUserHeader(user),
              ),
              actions: [
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(value, user),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('Edit User'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    PopupMenuItem(
                      value: 'toggle_status',
                      child: ListTile(
                        leading: Icon(
                          user.isActive ? Icons.block : Icons.check_circle,
                          color: user.isActive ? AppColors.error : AppColors.success,
                        ),
                        title: Text(user.isActive ? 'Deactivate' : 'Activate'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'reset_password',
                      child: ListTile(
                        leading: Icon(Icons.lock_reset),
                        title: Text('Reset Password'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete, color: AppColors.error),
                        title: Text('Delete User', style: TextStyle(color: AppColors.error)),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
              bottom: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Overview'),
                  Tab(text: 'Permissions'),
                  Tab(text: 'Activity'),
                ],
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(user),
            _buildPermissionsTab(user),
            _buildActivityTab(user),
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader(User user) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Colors.white,
              backgroundImage: user.profileImageUrl != null
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
              child: user.profileImageUrl == null
                  ? Text(
                      user.initials,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    )
                  : null,
            ),
            const SizedBox(height: 12),
            Text(
              user.displayName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              user.role.displayName,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            title: 'Personal Information',
            icon: Icons.person_outline,
            children: [
              _buildInfoRow('Full Name', user.displayName),
              _buildInfoRow('Email', user.email),
              _buildInfoRow('Phone', user.phoneNumber ?? 'Not provided'),
              _buildInfoRow('Username', user.username),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            title: 'Work Information',
            icon: Icons.work_outline,
            children: [
              _buildInfoRow('Role', user.role.displayName),
              _buildInfoRow('Department', user.department.displayName),
              _buildInfoRow('Status', user.status.displayName),
              _buildInfoRow('Active', user.isActive ? 'Yes' : 'No'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            title: 'Account Information',
            icon: Icons.account_circle_outlined,
            children: [
              _buildInfoRow('Created', _formatDate(user.createdAt)),
              _buildInfoRow('Last Updated', _formatDate(user.updatedAt)),
              _buildInfoRow('Last Login', user.lastLoginAt != null 
                  ? _formatDate(user.lastLoginAt!) 
                  : 'Never'),
              _buildInfoRow('Email Verified', user.isEmailVerified ? 'Yes' : 'No'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsTab(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            title: 'Role Permissions',
            icon: Icons.security,
            children: [
              Text(
                'This user has ${user.permissions.length} permissions based on their role.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: user.permissions.map((permission) {
                  return Chip(
                    label: Text(
                      permission,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                    side: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
                  );
                }).toList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityTab(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            title: 'Recent Activity',
            icon: Icons.history,
            children: [
              Text(
                'Activity logs will be displayed here.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 16),
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    // TODO: Load activity logs
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Activity logs feature coming soon')),
                    );
                  },
                  child: const Text('Load Activity Logs'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _handleMenuAction(String action, User user) {
    switch (action) {
      case 'edit':
        _editUser(user);
        break;
      case 'toggle_status':
        _toggleUserStatus(user);
        break;
      case 'reset_password':
        _resetPassword(user);
        break;
      case 'delete':
        _deleteUser(user);
        break;
    }
  }

  void _editUser(User user) {
    // TODO: Navigate to edit user page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit user feature coming soon')),
    );
  }

  void _toggleUserStatus(User user) {
    if (user.isActive) {
      context.read<UserManagementBloc>().add(
        DeactivateUserRequested(user.id, reason: 'Deactivated by admin'),
      );
    } else {
      context.read<UserManagementBloc>().add(ActivateUserRequested(user.id));
    }
  }

  void _resetPassword(User user) {
    // TODO: Implement password reset
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Password reset feature coming soon')),
    );
  }

  void _deleteUser(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete ${user.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<UserManagementBloc>().add(
                DeleteUserRequested(user.id, reason: 'Deleted by admin'),
              );
              Navigator.of(context).pop(); // Go back to users list
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
