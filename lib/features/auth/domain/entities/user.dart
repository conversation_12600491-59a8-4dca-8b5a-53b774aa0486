import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';


/// User entity representing a system user
class User extends AuditableEntity {
  final String username;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final UserRole role;
  final Department department;
  final CommonStatus status;
  final DateTime? lastLoginAt;
  final DateTime? emailVerifiedAt;
  final DateTime? phoneVerifiedAt;
  @override
  final bool isActive;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final Map<String, dynamic>? preferences;
  final List<String> permissions;

  const User({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    super.createdBy,
    super.updatedBy,
    super.deletedBy,
    super.version = 1,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.profileImageUrl,
    required this.role,
    required this.department,
    required this.status,
    this.lastLoginAt,
    this.emailVerifiedAt,
    this.phoneVerifiedAt,
    required this.isActive,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    this.preferences,
    required this.permissions,
  });

  /// Get full name
  String get fullName => '$firstName $lastName';

  /// Get display name (full name or username)
  String get displayName => fullName.trim().isNotEmpty ? fullName : username;

  /// Get initials for avatar
  String get initials {
    final first = firstName.isNotEmpty ? firstName[0].toUpperCase() : '';
    final last = lastName.isNotEmpty ? lastName[0].toUpperCase() : '';
    return '$first$last'.isNotEmpty ? '$first$last' : username[0].toUpperCase();
  }

  /// Check if user can login
  bool get canLogin => isActive && status == CommonStatus.active && !isDeleted;

  /// Check if user has specific permission
  bool hasPermission(String permission) => permissions.contains(permission);

  /// Check if user has any of the specified permissions
  bool hasAnyPermission(List<String> permissionList) {
    return permissionList.any((permission) => permissions.contains(permission));
  }

  /// Check if user has all specified permissions
  bool hasAllPermissions(List<String> permissionList) {
    return permissionList.every((permission) => permissions.contains(permission));
  }

  /// Check if user is in specific department
  bool isInDepartment(Department dept) => department == dept;

  /// Check if user has specific role
  bool hasRole(UserRole userRole) => role == userRole;

  /// Check if user is admin
  bool get isAdmin => role == UserRole.administrator;

  /// Check if user is department head
  bool get isDepartmentHead {
    return [
      UserRole.cuttingHead,
      UserRole.sewingHead,
      UserRole.finishingHead,
    ].contains(role);
  }

  /// Check if user is supervisor
  bool get isSupervisor => role == UserRole.sewingSupervisor;

  /// Check if user is operator
  bool get isOperator {
    return [
     // UserRole.sewingOperator,
      UserRole.finishingOperator,
      UserRole.cuttingHelper,
    ].contains(role);
  }

  /// Get role hierarchy level
  int get hierarchyLevel => role.hierarchyLevel;

  /// Check if user can manage another user
  bool canManage(User otherUser) {
    return hierarchyLevel > otherUser.hierarchyLevel;
  }

  /// Check if user can access a specific department by ID
  ///
  /// [departmentId] should match the department's enum value (e.g., 'merchandising', 'cutting')
  bool canAccessDepartment(String departmentId) {
    // Admin can access all departments
    if (isAdmin) return true;

    // Compare the department's enum value with the provided departmentId
    return department.value == departmentId;
  }

  /// Copy with new values
  User copyWith({
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
    UserRole? role,
    Department? department,
    CommonStatus? status,
    DateTime? lastLoginAt,
    DateTime? emailVerifiedAt,
    DateTime? phoneVerifiedAt,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    Map<String, dynamic>? preferences,
    List<String>? permissions,
    DateTime? updatedAt,
    String? updatedBy,
    int? version,
  }) {
    return User(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy,
      version: version ?? this.version + 1,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      role: role ?? this.role,
      department: department ?? this.department,
      status: status ?? this.status,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      preferences: preferences ?? this.preferences,
      permissions: permissions ?? this.permissions,
    );
  }

  /// Convert to a map for Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'username': username,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'role': role.value,
      'department': department.value,
      'status': status.value,
      'lastLoginAt': lastLoginAt,
      'emailVerifiedAt': emailVerifiedAt,
      'phoneVerifiedAt': phoneVerifiedAt,
      'isActive': isActive,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'preferences': preferences,
      'permissions': permissions,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'deletedBy': deletedBy,
      'version': version,
    };
  }

  @override
  List<Object?> get props => [
        ...super.props,
        username,
        email,
        firstName,
        lastName,
        phoneNumber,
        profileImageUrl,
        role,
        department,
        status,
        lastLoginAt,
        emailVerifiedAt,
        phoneVerifiedAt,
        isActive,
        isEmailVerified,
        isPhoneVerified,
        preferences,
        permissions,
      ];

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, fullName: $fullName, role: $role, department: $department, status: $status)';
  }
}

/// User session information
class UserSession extends Equatable {
  final String sessionId;
  final String userId;
  final String deviceId;
  final String deviceName;
  final String ipAddress;
  final String userAgent;
  final DateTime createdAt;
  final DateTime lastActiveAt;
  final DateTime expiresAt;
  final bool isActive;

  const UserSession({
    required this.sessionId,
    required this.userId,
    required this.deviceId,
    required this.deviceName,
    required this.ipAddress,
    required this.userAgent,
    required this.createdAt,
    required this.lastActiveAt,
    required this.expiresAt,
    required this.isActive,
  });

  /// Check if session is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if session is valid
  bool get isValid => isActive && !isExpired;

  /// Get session duration
  Duration get duration => lastActiveAt.difference(createdAt);

  /// Get time until expiry
  Duration get timeUntilExpiry => expiresAt.difference(DateTime.now());

  @override
  List<Object?> get props => [
        sessionId,
        userId,
        deviceId,
        deviceName,
        ipAddress,
        userAgent,
        createdAt,
        lastActiveAt,
        expiresAt,
        isActive,
      ];
}

/// User preferences
@JsonSerializable(
  explicitToJson: true,
  createToJson: true,
  createFactory: true,
  includeIfNull: false,
)
class UserPreferences extends Equatable {
  @JsonKey(defaultValue: 'light')
  final String theme; // 'light', 'dark', 'system'

  @JsonKey(defaultValue: 'en')
  final String language;

  @JsonKey(name: 'date_format', defaultValue: 'MM/dd/yyyy')
  final String dateFormat;

  @JsonKey(name: 'time_format', defaultValue: '12h')
  final String timeFormat;

  @JsonKey(name: 'enable_notifications', defaultValue: true)
  final bool enableNotifications;

  @JsonKey(name: 'enable_email_notifications', defaultValue: true)
  final bool enableEmailNotifications;

  @JsonKey(name: 'enable_push_notifications', defaultValue: true)
  final bool enablePushNotifications;

  @JsonKey(name: 'notification_channels', defaultValue: {})
  final Map<String, bool> notificationChannels;

  @JsonKey(name: 'dashboard_layout', defaultValue: {})
  final Map<String, dynamic> dashboardLayout;

  @JsonKey(name: 'custom_settings', defaultValue: {})
  final Map<String, dynamic> customSettings;

  const UserPreferences({
    this.theme = 'light',
    this.language = 'en',
    this.dateFormat = 'MM/dd/yyyy',
    this.timeFormat = '12h',
    this.enableNotifications = true,
    this.enableEmailNotifications = true,
    this.enablePushNotifications = true,
    this.notificationChannels = const {},
    this.dashboardLayout = const {},
    this.customSettings = const {},
  });


  @override
  List<Object?> get props => [
        theme,
        language,
        dateFormat,
        timeFormat,
        enableNotifications,
        enableEmailNotifications,
        enablePushNotifications,
        notificationChannels,
        dashboardLayout,
        customSettings,
      ];

  /// Copy with new values
  UserPreferences copyWith({
    String? theme,
    String? language,
    String? dateFormat,
    String? timeFormat,
    bool? enableNotifications,
    bool? enableEmailNotifications,
    bool? enablePushNotifications,
    Map<String, bool>? notificationChannels,
    Map<String, dynamic>? dashboardLayout,
    Map<String, dynamic>? customSettings,
  }) {
    return UserPreferences(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableEmailNotifications: enableEmailNotifications ?? this.enableEmailNotifications,
      enablePushNotifications: enablePushNotifications ?? this.enablePushNotifications,
      notificationChannels: notificationChannels ?? this.notificationChannels,
      dashboardLayout: dashboardLayout ?? this.dashboardLayout,
      customSettings: customSettings ?? this.customSettings,
    );
  }
}
