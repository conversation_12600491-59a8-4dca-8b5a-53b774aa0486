import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/constants/api_constants.dart';
import '../../../../core/errors/error_handler.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/api_client.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../auth/data/models/user_model.dart';
import '../../../auth/domain/entities/user.dart';
import '../../domain/entities/user_management.dart';
import '../../domain/repositories/user_management_repository.dart';

/// User management repository implementation
@LazySingleton(as: UserManagementRepository)
class UserManagementRepositoryImpl implements UserManagementRepository {
  final ApiClient _apiClient;

  const UserManagementRepositoryImpl(this._apiClient);

  @override
  Future<Either<Failure, ApiListResponse<User>>> getUsers({
    UserFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      
      if (filter != null) {
        queryParams.addAll(filter.toQueryParams());
      }
      
      if (pagination != null) {
        queryParams.addAll(pagination.toQueryParams());
      }

      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.users}',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data!;
        final usersData = data['data'] as List<dynamic>? ?? [];
        final users = usersData
            .map((json) => UserModel.fromJson(json as Map<String, dynamic>).toEntity())
            .toList();

        final paginationData = data['pagination'] as Map<String, dynamic>?;
        final paginationInfo = paginationData != null 
            ? Pagination.fromJson(paginationData)
            : null;

        return Right(ApiListResponse<User>(
          success: true,
          data: users,
          pagination: paginationInfo,
        ));
      } else {
        return Left(ServerFailure(
          'Failed to get users',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> getUserById(String userId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.users}/$userId',
      );

      if (response.statusCode == 200 && response.data != null) {
        final user = UserModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: user));
      } else {
        return Left(ServerFailure(
          'Failed to get user',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> createUser(
    CreateUserRequest request,
  ) async {
    try {
      final requestData = {
        'username': request.username,
        'email': request.email,
        'first_name': request.firstName,
        'last_name': request.lastName,
        'phone_number': request.phoneNumber,
        'role': request.role.value,
        'department': request.department.value,
        'password': request.password,
        'send_welcome_email': request.sendWelcomeEmail,
        'require_password_change': request.requirePasswordChange,
        'additional_permissions': request.additionalPermissions,
        // 'is_active': true,
        // 'status': CommonStatus.active.value,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.users,
        data: requestData,
      );

      if (response.statusCode == 201 && response.data != null) {
        final user = UserModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: user));
      } else {
        return Left(ServerFailure(
          'Failed to create user',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> updateUser(
    UpdateUserRequest request,
  ) async {
    try {
      final requestData = <String, dynamic>{};
      
      if (request.username != null) requestData['username'] = request.username;
      if (request.email != null) requestData['email'] = request.email;
      if (request.firstName != null) requestData['first_name'] = request.firstName;
      if (request.lastName != null) requestData['last_name'] = request.lastName;
      if (request.phoneNumber != null) requestData['phone_number'] = request.phoneNumber;
      if (request.profileImageUrl != null) requestData['profile_image_url'] = request.profileImageUrl;
      if (request.role != null) requestData['role'] = request.role!.value;
      if (request.department != null) requestData['department'] = request.department!.value;
      if (request.status != null) requestData['status'] = request.status!.value;
      if (request.isActive != null) requestData['is_active'] = request.isActive;
      if (request.permissions != null) requestData['permissions'] = request.permissions;

      final response = await _apiClient.put<Map<String, dynamic>>(
        '${ApiConstants.users}/${request.userId}',
        data: requestData,
      );

      if (response.statusCode == 200 && response.data != null) {
        final user = UserModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: user));
      } else {
        return Left(ServerFailure(
          'Failed to update user',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteUser(
    String userId,
    String? reason,
  ) async {
    try {
      final requestData = <String, dynamic>{};
      if (reason != null) requestData['reason'] = reason;

      final response = await _apiClient.delete<Map<String, dynamic>>(
        '${ApiConstants.users}/$userId',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to delete user',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> activateUser(String userId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.users}/$userId/activate',
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to activate user',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deactivateUser(
    String userId,
    String? reason,
  ) async {
    try {
      final requestData = <String, dynamic>{};
      if (reason != null) requestData['reason'] = reason;

      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.users}/$userId/deactivate',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to deactivate user',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> assignRole(
    RoleAssignmentRequest request,
  ) async {
    try {
      final requestData = {
        'role': request.role.value,
        'department': request.department.value,
        'reason': request.reason,
        'effective_date': request.effectiveDate?.toIso8601String(),
        'additional_permissions': request.additionalPermissions,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.users}/${request.userId}/assign-role',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to assign role',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> grantPermissions(
    PermissionAssignmentRequest request,
  ) async {
    try {
      final requestData = {
        'permissions': request.permissions,
        'reason': request.reason,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.users}/${request.userId}/grant-permissions',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to grant permissions',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> revokePermissions(
    PermissionAssignmentRequest request,
  ) async {
    try {
      final requestData = {
        'permissions': request.permissions,
        'reason': request.reason,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.users}/${request.userId}/revoke-permissions',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to revoke permissions',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getUserPermissions(String userId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.users}/$userId/permissions',
      );

      if (response.statusCode == 200 && response.data != null) {
        final permissions = List<String>.from(response.data!['permissions'] ?? []);
        return Right(permissions);
      } else {
        return Left(ServerFailure(
          'Failed to get user permissions',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<UserActivityLog>>> getUserActivityLogs(
    String userId, {
    PaginationParams? pagination,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (pagination != null) {
        queryParams.addAll(pagination.toQueryParams());
      }

      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.users}/$userId/activity-logs',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data!;
        final logsData = data['data'] as List<dynamic>? ?? [];
        final logs = logsData
            .map((json) => _parseActivityLog(json as Map<String, dynamic>))
            .toList();

        final paginationData = data['pagination'] as Map<String, dynamic>?;
        final paginationInfo = paginationData != null 
            ? Pagination.fromJson(paginationData)
            : null;

        return Right(ApiListResponse<UserActivityLog>(
          success: true,
          data: logs,
          pagination: paginationInfo,
        ));
      } else {
        return Left(ServerFailure(
          'Failed to get activity logs',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, UserStatistics>> getUserStatistics() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.users}/statistics',
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data!;
        final statistics = UserStatistics(
          totalUsers: data['total_users'] ?? 0,
          activeUsers: data['active_users'] ?? 0,
          inactiveUsers: data['inactive_users'] ?? 0,
          usersByRole: Map<String, int>.from(data['users_by_role'] ?? {}),
          usersByDepartment: Map<String, int>.from(data['users_by_department'] ?? {}),
          usersByStatus: Map<String, int>.from(data['users_by_status'] ?? {}),
          usersLoggedInToday: data['users_logged_in_today'] ?? 0,
          usersLoggedInThisWeek: data['users_logged_in_this_week'] ?? 0,
          usersLoggedInThisMonth: data['users_logged_in_this_month'] ?? 0,
        );

        return Right(statistics);
      } else {
        return Left(ServerFailure(
          'Failed to get user statistics',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<User>>> searchUsers({
    required String query,
    UserFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final queryParams = <String, dynamic>{'search': query};
      
      if (filter != null) {
        queryParams.addAll(filter.toQueryParams());
      }
      
      if (pagination != null) {
        queryParams.addAll(pagination.toQueryParams());
      }

      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.users}/search',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data!;
        final usersData = data['data'] as List<dynamic>? ?? [];
        final users = usersData
            .map((json) => UserModel.fromJson(json as Map<String, dynamic>).toEntity())
            .toList();

        final paginationData = data['pagination'] as Map<String, dynamic>?;
        final paginationInfo = paginationData != null 
            ? Pagination.fromJson(paginationData)
            : null;

        return Right(ApiListResponse<User>(
          success: true,
          data: users,
          pagination: paginationInfo,
        ));
      } else {
        return Left(ServerFailure(
          'Failed to search users',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  // Helper methods for unimplemented features
  @override
  Future<Either<Failure, ApiListResponse<User>>> getUsersByRole(
    UserRole role, {
    PaginationParams? pagination,
  }) async {
    final filter = UserFilterCriteria(role: role);
    return getUsers(filter: filter, pagination: pagination);
  }

  @override
  Future<Either<Failure, ApiListResponse<User>>> getUsersByDepartment(
    Department department, {
    PaginationParams? pagination,
  }) async {
    final filter = UserFilterCriteria(department: department);
    return getUsers(filter: filter, pagination: pagination);
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> resetUserPassword(
    String userId,
    String newPassword,
  ) async {
    // TODO: Implement password reset
    return const Left(UnimplementedFailure('Password reset not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> forcePasswordChange(String userId) async {
    // TODO: Implement force password change
    return const Left(UnimplementedFailure('Force password change not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> lockUser(
    String userId,
    String reason,
  ) async {
    // TODO: Implement user locking
    return const Left(UnimplementedFailure('User locking not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> unlockUser(String userId) async {
    // TODO: Implement user unlocking
    return const Left(UnimplementedFailure('User unlocking not implemented'));
  }

  @override
  Future<Either<Failure, List<RoleDefinition>>> getAvailableRoles() async {
    // TODO: Implement get available roles
    return const Left(UnimplementedFailure('Get available roles not implemented'));
  }

  @override
  Future<Either<Failure, List<Permission>>> getAvailablePermissions() async {
    // TODO: Implement get available permissions
    return const Left(UnimplementedFailure('Get available permissions not implemented'));
  }

  @override
  Future<Either<Failure, Map<String, List<Permission>>>> getPermissionsByCategory() async {
    // TODO: Implement get permissions by category
    return const Left(UnimplementedFailure('Get permissions by category not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> bulkUpdateUsers(
    List<UpdateUserRequest> requests,
  ) async {
    // TODO: Implement bulk update
    return const Left(UnimplementedFailure('Bulk update not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> bulkDeleteUsers(
    List<String> userIds,
    String? reason,
  ) async {
    // TODO: Implement bulk delete
    return const Left(UnimplementedFailure('Bulk delete not implemented'));
  }

  @override
  Future<Either<Failure, String>> exportUsers({
    UserFilterCriteria? filter,
    String format = 'csv',
  }) async {
    // TODO: Implement export
    return const Left(UnimplementedFailure('Export not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<Map<String, dynamic>>>> importUsers(
    String filePath, {
    bool validateOnly = false,
  }) async {
    // TODO: Implement import
    return const Left(UnimplementedFailure('Import not implemented'));
  }

  UserActivityLog _parseActivityLog(Map<String, dynamic> json) {
    return UserActivityLog(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      deletedAt: json['deleted_at'] != null 
          ? DateTime.parse(json['deleted_at'] as String)
          : null,
      userId: json['user_id'] as String,
      action: json['action'] as String,
      description: json['description'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      ipAddress: json['ip_address'] as String?,
      userAgent: json['user_agent'] as String?,
      deviceInfo: json['device_info'] as String?,
    );
  }
}
