import 'package:equatable/equatable.dart';

/// Represents the criteria for filtering quality defects
class QualityDefectFilterCriteria extends Equatable {
  final String? status;
  final String? severity;
  final String? assignedTo;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? searchQuery;

  const QualityDefectFilterCriteria({
    this.status,
    this.severity,
    this.assignedTo,
    this.fromDate,
    this.toDate,
    this.searchQuery,
  });

  /// Creates a copy of this QualityDefectFilterCriteria with the given fields replaced
  QualityDefectFilterCriteria copyWith({
    String? status,
    String? severity,
    String? assignedTo,
    DateTime? fromDate,
    DateTime? toDate,
    String? searchQuery,
  }) {
    return QualityDefectFilterCriteria(
      status: status ?? this.status,
      severity: severity ?? this.severity,
      assignedTo: assignedTo ?? this.assignedTo,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [
        status,
        severity,
        assignedTo,
        fromDate,
        toDate,
        searchQuery,
      ];

  /// Returns true if no filters are applied
  bool get isEmpty =>
      status == null &&
      severity == null &&
      assignedTo == null &&
      fromDate == null &&
      toDate == null &&
      (searchQuery == null || searchQuery!.isEmpty);
}
