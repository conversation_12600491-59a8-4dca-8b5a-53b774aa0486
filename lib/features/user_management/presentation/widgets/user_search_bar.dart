import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';

/// Search bar widget for users
class UserSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final VoidCallback? onClear;
  final String? hintText;
  final bool enabled;

  const UserSearchBar({
    super.key,
    required this.onSearch,
    this.onClear,
    this.hintText,
    this.enabled = true,
  });

  @override
  State<UserSearchBar> createState() => _UserSearchBarState();
}

class _UserSearchBarState extends State<UserSearchBar> {
  final _controller = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      enabled: widget.enabled,
      onChanged: widget.onSearch,
      onSubmitted: widget.onSearch,
      decoration: InputDecoration(
        hintText: widget.hintText ?? 'Search users...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _controller.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: _clearSearch,
              )
            : null,
        filled: true,
        fillColor: Theme.of(context).cardColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.border.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: AppColors.primary,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
    );
  }

  void _clearSearch() {
    _controller.clear();
    widget.onSearch('');
    widget.onClear?.call();
    _focusNode.unfocus();
  }
}

/// Advanced search bar with filters
class AdvancedUserSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final Function(Map<String, dynamic>) onFilter;
  final VoidCallback? onClear;
  final Map<String, dynamic>? initialFilters;

  const AdvancedUserSearchBar({
    super.key,
    required this.onSearch,
    required this.onFilter,
    this.onClear,
    this.initialFilters,
  });

  @override
  State<AdvancedUserSearchBar> createState() => _AdvancedUserSearchBarState();
}

class _AdvancedUserSearchBarState extends State<AdvancedUserSearchBar> {
  final _searchController = TextEditingController();
  Map<String, dynamic> _filters = {};
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _filters = widget.initialFilters ?? {};
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _searchController,
                onChanged: widget.onSearch,
                decoration: InputDecoration(
                  hintText: 'Search users by name, email, or role...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_searchController.text.isNotEmpty)
                        IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: _clearSearch,
                        ),
                      IconButton(
                        icon: Icon(
                          Icons.tune,
                          color: _hasActiveFilters() 
                              ? AppColors.primary 
                              : AppColors.textSecondary,
                        ),
                        onPressed: _toggleFilters,
                      ),
                    ],
                  ),
                  filled: true,
                  fillColor: Theme.of(context).cardColor,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),
          ],
        ),
        if (_showFilters) ...[
          const SizedBox(height: 12),
          _buildFiltersSection(),
        ],
      ],
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Filters',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _clearFilters,
                child: const Text('Clear All'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildRoleFilter(),
              _buildDepartmentFilter(),
              _buildStatusFilter(),
              _buildDateFilter(),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _applyFilters,
                  child: const Text('Apply Filters'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _applyFilters,
                  child: const Text('Search'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRoleFilter() {
    return SizedBox(
      width: 150,
      child: DropdownButtonFormField<String>(
        value: _filters['role'],
        decoration: const InputDecoration(
          labelText: 'Role',
          border: OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(value: null, child: Text('All Roles')),
          DropdownMenuItem(value: 'administrator', child: Text('Administrator')),
          DropdownMenuItem(value: 'merchandiser', child: Text('Merchandiser')),
          DropdownMenuItem(value: 'cutting_head', child: Text('Cutting Head')),
          DropdownMenuItem(value: 'sewing_head', child: Text('Sewing Head')),
          DropdownMenuItem(value: 'quality_controller', child: Text('Quality Controller')),
        ],
        onChanged: (value) {
          setState(() {
            if (value == null) {
              _filters.remove('role');
            } else {
              _filters['role'] = value;
            }
          });
        },
      ),
    );
  }

  Widget _buildDepartmentFilter() {
    return SizedBox(
      width: 150,
      child: DropdownButtonFormField<String>(
        value: _filters['department'],
        decoration: const InputDecoration(
          labelText: 'Department',
          border: OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(value: null, child: Text('All Departments')),
          DropdownMenuItem(value: 'merchandising', child: Text('Merchandising')),
          DropdownMenuItem(value: 'cutting', child: Text('Cutting')),
          DropdownMenuItem(value: 'sewing', child: Text('Sewing')),
          DropdownMenuItem(value: 'quality', child: Text('Quality')),
          DropdownMenuItem(value: 'finishing', child: Text('Finishing')),
          DropdownMenuItem(value: 'warehouse', child: Text('Warehouse')),
        ],
        onChanged: (value) {
          setState(() {
            if (value == null) {
              _filters.remove('department');
            } else {
              _filters['department'] = value;
            }
          });
        },
      ),
    );
  }

  Widget _buildStatusFilter() {
    return SizedBox(
      width: 120,
      child: DropdownButtonFormField<String>(
        value: _filters['status'],
        decoration: const InputDecoration(
          labelText: 'Status',
          border: OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(value: null, child: Text('All Status')),
          DropdownMenuItem(value: 'active', child: Text('Active')),
          DropdownMenuItem(value: 'inactive', child: Text('Inactive')),
          DropdownMenuItem(value: 'pending', child: Text('Pending')),
          DropdownMenuItem(value: 'suspended', child: Text('Suspended')),
        ],
        onChanged: (value) {
          setState(() {
            if (value == null) {
              _filters.remove('status');
            } else {
              _filters['status'] = value;
            }
          });
        },
      ),
    );
  }

  Widget _buildDateFilter() {
    return SizedBox(
      width: 150,
      child: DropdownButtonFormField<String>(
        value: _filters['date_range'],
        decoration: const InputDecoration(
          labelText: 'Created',
          border: OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(value: null, child: Text('Any Time')),
          DropdownMenuItem(value: 'today', child: Text('Today')),
          DropdownMenuItem(value: 'week', child: Text('This Week')),
          DropdownMenuItem(value: 'month', child: Text('This Month')),
          DropdownMenuItem(value: 'year', child: Text('This Year')),
        ],
        onChanged: (value) {
          setState(() {
            if (value == null) {
              _filters.remove('date_range');
            } else {
              _filters['date_range'] = value;
            }
          });
        },
      ),
    );
  }

  bool _hasActiveFilters() {
    return _filters.isNotEmpty;
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
  }

  void _clearSearch() {
    _searchController.clear();
    widget.onSearch('');
  }

  void _clearFilters() {
    setState(() {
      _filters.clear();
    });
    widget.onFilter({});
  }

  void _applyFilters() {
    widget.onFilter(_filters);
    setState(() {
      _showFilters = false;
    });
  }
}
