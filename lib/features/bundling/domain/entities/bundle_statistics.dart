import 'package:equatable/equatable.dart';
import 'bundle.dart';

/// Bundle statistics for dashboard and reporting
class BundleStatistics extends Equatable {
  /// Total number of bundles
  final int totalBundles;
  
  /// Number of pending bundles
  final int pendingBundles;
  
  /// Number of bundles issued to sewing
  final int issuedBundles;
  
  /// Number of bundles in progress
  final int inProgressBundles;
  
  /// Number of completed bundles
  final int completedBundles;
  
  /// Number of cancelled bundles
  final int cancelledBundles;
  
  /// Total pieces across all bundles
  final int totalPieces;
  
  /// Total pieces completed
  final int completedPieces;
  
  /// Total pieces in progress
  final int inProgressPieces;
  
  /// Total pieces pending
  final int pendingPieces;
  
  /// Statistics by design
  final Map<String, DesignStatistics> designStats;
  
  /// Statistics by size
  final Map<GarmentSize, SizeStatistics> sizeStats;
  
  /// Statistics by status
  final Map<BundleStatus, StatusStatistics> statusStats;
  
  /// Date range for these statistics
  final DateTime? fromDate;
  final DateTime? toDate;

  const BundleStatistics({
    required this.totalBundles,
    required this.pendingBundles,
    required this.issuedBundles,
    required this.inProgressBundles,
    required this.completedBundles,
    required this.cancelledBundles,
    required this.totalPieces,
    required this.completedPieces,
    required this.inProgressPieces,
    required this.pendingPieces,
    required this.designStats,
    required this.sizeStats,
    required this.statusStats,
    this.fromDate,
    this.toDate,
  });

  @override
  List<Object?> get props => [
        totalBundles,
        pendingBundles,
        issuedBundles,
        inProgressBundles,
        completedBundles,
        cancelledBundles,
        totalPieces,
        completedPieces,
        inProgressPieces,
        pendingPieces,
        designStats,
        sizeStats,
        statusStats,
        fromDate,
        toDate,
      ];

  /// Calculate completion percentage
  double get completionPercentage {
    if (totalPieces == 0) return 0.0;
    return (completedPieces / totalPieces) * 100;
  }

  /// Calculate progress percentage
  double get progressPercentage {
    if (totalPieces == 0) return 0.0;
    return ((completedPieces + inProgressPieces) / totalPieces) * 100;
  }

  /// Get active bundles count (pending + issued + in progress)
  int get activeBundles {
    return pendingBundles + issuedBundles + inProgressBundles;
  }

  /// Get active pieces count (pending + issued + in progress)
  int get activePieces {
    return pendingPieces + inProgressPieces;
  }

  /// Check if there are any bundles
  bool get hasBundles => totalBundles > 0;

  /// Get efficiency metrics
  BundleEfficiencyMetrics get efficiencyMetrics {
    return BundleEfficiencyMetrics(
      totalBundles: totalBundles,
      completedBundles: completedBundles,
      totalPieces: totalPieces,
      completedPieces: completedPieces,
      averagePiecesPerBundle: totalBundles > 0 ? totalPieces / totalBundles : 0.0,
      completionRate: completionPercentage,
    );
  }

  /// Create empty statistics
  factory BundleStatistics.empty() {
    return const BundleStatistics(
      totalBundles: 0,
      pendingBundles: 0,
      issuedBundles: 0,
      inProgressBundles: 0,
      completedBundles: 0,
      cancelledBundles: 0,
      totalPieces: 0,
      completedPieces: 0,
      inProgressPieces: 0,
      pendingPieces: 0,
      designStats: {},
      sizeStats: {},
      statusStats: {},
    );
  }
}

/// Statistics for a specific design
class DesignStatistics extends Equatable {
  final String designNo;
  final int totalBundles;
  final int totalPieces;
  final int completedBundles;
  final int completedPieces;
  final Map<GarmentSize, int> sizeBreakdown;

  const DesignStatistics({
    required this.designNo,
    required this.totalBundles,
    required this.totalPieces,
    required this.completedBundles,
    required this.completedPieces,
    required this.sizeBreakdown,
  });

  @override
  List<Object?> get props => [
        designNo,
        totalBundles,
        totalPieces,
        completedBundles,
        completedPieces,
        sizeBreakdown,
      ];

  double get completionPercentage {
    if (totalPieces == 0) return 0.0;
    return (completedPieces / totalPieces) * 100;
  }
}

/// Statistics for a specific size
class SizeStatistics extends Equatable {
  final GarmentSize size;
  final int totalBundles;
  final int totalPieces;
  final int completedBundles;
  final int completedPieces;
  final Map<String, int> designBreakdown;

  const SizeStatistics({
    required this.size,
    required this.totalBundles,
    required this.totalPieces,
    required this.completedBundles,
    required this.completedPieces,
    required this.designBreakdown,
  });

  @override
  List<Object?> get props => [
        size,
        totalBundles,
        totalPieces,
        completedBundles,
        completedPieces,
        designBreakdown,
      ];

  double get completionPercentage {
    if (totalPieces == 0) return 0.0;
    return (completedPieces / totalPieces) * 100;
  }
}

/// Statistics for a specific status
class StatusStatistics extends Equatable {
  final BundleStatus status;
  final int bundleCount;
  final int pieceCount;
  final double percentage;

  const StatusStatistics({
    required this.status,
    required this.bundleCount,
    required this.pieceCount,
    required this.percentage,
  });

  @override
  List<Object?> get props => [status, bundleCount, pieceCount, percentage];
}

/// Bundle efficiency metrics
class BundleEfficiencyMetrics extends Equatable {
  final int totalBundles;
  final int completedBundles;
  final int totalPieces;
  final int completedPieces;
  final double averagePiecesPerBundle;
  final double completionRate;

  const BundleEfficiencyMetrics({
    required this.totalBundles,
    required this.completedBundles,
    required this.totalPieces,
    required this.completedPieces,
    required this.averagePiecesPerBundle,
    required this.completionRate,
  });

  @override
  List<Object?> get props => [
        totalBundles,
        completedBundles,
        totalPieces,
        completedPieces,
        averagePiecesPerBundle,
        completionRate,
      ];

  /// Bundle completion rate
  double get bundleCompletionRate {
    if (totalBundles == 0) return 0.0;
    return (completedBundles / totalBundles) * 100;
  }

  /// Piece completion rate
  double get pieceCompletionRate {
    if (totalPieces == 0) return 0.0;
    return (completedPieces / totalPieces) * 100;
  }

  /// Efficiency score (0-100)
  double get efficiencyScore {
    return (bundleCompletionRate + pieceCompletionRate) / 2;
  }
}
