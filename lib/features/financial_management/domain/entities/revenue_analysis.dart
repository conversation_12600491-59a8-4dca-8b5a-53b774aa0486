import 'package:equatable/equatable.dart';

/// Represents a revenue analysis with various financial metrics
class RevenueAnalysis extends Equatable {
  /// Total revenue for the period
  final double totalRevenue;
  
  /// Revenue growth compared to previous period (as a percentage)
  final double revenueGrowth;
  
  /// Average revenue per transaction
  final double averageRevenuePerTransaction;
  
  /// Revenue by category (e.g., product categories, services, etc.)
  final Map<String, double> revenueByCategory;
  
  /// Revenue trend data (e.g., monthly revenue for the last 12 months)
  final Map<DateTime, double> revenueTrend;

  const RevenueAnalysis({
    required this.totalRevenue,
    required this.revenueGrowth,
    required this.averageRevenuePerTransaction,
    required this.revenueByCategory,
    required this.revenueTrend,
  });

  @override
  List<Object?> get props => [
        totalRevenue,
        revenueGrowth,
        averageRevenuePerTransaction,
        revenueByCategory,
        revenueTrend,
      ];

  /// Creates a copy of this RevenueAnalysis with the given fields replaced with the new values
  RevenueAnalysis copyWith({
    double? totalRevenue,
    double? revenueGrowth,
    double? averageRevenuePerTransaction,
    Map<String, double>? revenueByCategory,
    Map<DateTime, double>? revenueTrend,
  }) {
    return RevenueAnalysis(
      totalRevenue: totalRevenue ?? this.totalRevenue,
      revenueGrowth: revenueGrowth ?? this.revenueGrowth,
      averageRevenuePerTransaction: 
          averageRevenuePerTransaction ?? this.averageRevenuePerTransaction,
      revenueByCategory: revenueByCategory ?? this.revenueByCategory,
      revenueTrend: revenueTrend ?? this.revenueTrend,
    );
  }
}
