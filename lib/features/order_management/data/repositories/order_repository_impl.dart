import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/constants/api_constants.dart';
import '../../../../core/errors/error_handler.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/api_client.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/client_entities.dart' hide UpdateOrderRequest, OrderStatistics, OrderProductionSchedule;
import '../../domain/models/create_order_request.dart';
import '../../domain/entities/order_entities.dart';
import '../../domain/repositories/order_repository.dart';
import '../../domain/usecases/order_usecases.dart';

/// Order repository implementation
@LazySingleton(as: OrderRepository)
class OrderRepositoryImpl implements OrderRepository {
  final ApiClient _apiClient;

  const OrderRepositoryImpl(this._apiClient);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOrders({
    OrderFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock orders for development
      final orders = _getMockOrders();
      return Right(ApiListResponse<ManufacturingOrder>(
        success: true,
        data: orders,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 15,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> getOrderById(
    String orderId,
  ) async {
    try {
      // Return mock order for development
      final order = _getMockOrder(orderId);
      return Right(ApiResponse.success(data: order));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> createOrder(
    CreateOrderRequest request,
  ) async {
    try {
      final requestData = {
        'client_id': request.clientId,
        'order_number': request.orderNumber,
        'type': request.type.toString().split('.').last,
        'priority': request.priority.toString().split('.').last,
        'required_date': request.requiredDate?.toIso8601String(),
        'items': request.items.map((item) => {
          'product_id': item.productId,
          'product_name': item.productName,
          'product_code': item.productCode,
          'description': item.description,
          'quantity': item.quantity,
          'unit': item.unit,
          'unit_price': item.unitPrice,
          'size': item.size?.value,
          'color': item.color,
          'fabric': item.fabric,
        }).toList(),
        'notes': request.notes,
        'attachments': request.attachments,
        'assigned_merchandiser': request.assignedMerchandiser,
        'metadata': request.metadata,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.orders,
        data: requestData,
      );

      if (response.statusCode == 201 && response.data != null) {
        final order = _parseOrder(response.data!);
        return Right(ApiResponse.success(data: order));
      } else {
        return Left(ServerFailure(
          'Failed to create order',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> updateOrder(
    UpdateOrderRequest request,
  ) async {
    try {
      final requestData = <String, dynamic>{};
      
      if (request.clientId != null) requestData['client_id'] = request.clientId;
      if (request.type != null) requestData['type'] = request.type!.value;
      if (request.priority != null) requestData['priority'] = request.priority!.value;
      if (request.requiredDate != null) requestData['required_date'] = request.requiredDate!.toIso8601String();
      if (request.assignedMerchandiser != null) requestData['assigned_merchandiser'] = request.assignedMerchandiser;
      
      if (request.items != null) {
        requestData['items'] = request.items!.map((item) => {
          'item_id': item.itemId,
          'product_id': item.productId,
          'product_name': item.productName,
          'product_code': item.productCode,
          'description': item.description,
          'quantity': item.quantity,
          'unit': item.unit,
          'unit_price': item.unitPrice,
          'size': item.size?.value,
          'color': item.color,
          'fabric': item.fabric,
          'specifications': item.specifications,
        }).toList();
      }

      final response = await _apiClient.put<Map<String, dynamic>>(
        '${ApiConstants.orders}/${request.orderId}',
        data: requestData,
      );

      if (response.statusCode == 200 && response.data != null) {
        final order = _parseOrder(response.data!);
        return Right(ApiResponse.success(data: order));
      } else {
        return Left(ServerFailure(
          'Failed to update order',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteOrder(
    String orderId,
    String? reason,
  ) async {
    try {
      final requestData = <String, dynamic>{};
      if (reason != null) requestData['reason'] = reason;

      final response = await _apiClient.delete<Map<String, dynamic>>(
        '${ApiConstants.orders}/$orderId',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to delete order',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> updateOrderStatus(
    String orderId,
    OrderStatus status,
    String? reason,
  ) async {
    try {
      final requestData = {
        'status': status.value,
        'reason': reason,
      };

      final response = await _apiClient.patch<Map<String, dynamic>>(
        '${ApiConstants.orders}/$orderId/status',
        data: requestData,
      );

      if (response.statusCode == 200 && response.data != null) {
        final order = _parseOrder(response.data!);
        return Right(ApiResponse.success(data: order));
      } else {
        return Left(ServerFailure(
          'Failed to update order status',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> searchOrders({
    required String query,
    OrderFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      // Return filtered mock orders for development
      final orders = _getMockOrders()
          .where((order) => 
              order.orderNumber.toLowerCase().contains(query.toLowerCase()) ||
              order.clientName.toLowerCase().contains(query.toLowerCase()))
          .toList();
      
      return Right(ApiListResponse<ManufacturingOrder>(
        success: true,
        data: orders,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 5,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOverdueOrders({
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock overdue orders for development
      final orders = _getMockOrders()
          .where((order) => order.isOverdue)
          .toList();
      
      return Right(ApiListResponse<ManufacturingOrder>(
        success: true,
        data: orders,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getUrgentOrders({
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock urgent orders for development
      final orders = _getMockOrders()
          .where((order) => order.isUrgent)
          .toList();
      
      return Right(ApiListResponse<ManufacturingOrder>(
        success: true,
        data: orders,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 3,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, OrderStatistics>> getOrderStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? clientId,
  }) async {
    try {
      // Return mock statistics for development
      return Right(_getMockOrderStatistics());
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  // Mock data methods for development
  List<ManufacturingOrder> _getMockOrders() {
    final now = DateTime.now();
    return [
      ManufacturingOrder(
        id: '1',
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 1)),
        orderNumber: 'ORD-2024-001',
        clientId: 'client1',
        clientName: 'Fashion Forward Ltd.',
        status: OrderStatus.inProduction,
        priority: OrderPriority.high,
        type: OrderType.bulk,
        orderDate: now.subtract(const Duration(days: 5)),
        requiredDate: now.add(const Duration(days: 10)),
        items: _getMockOrderItems(),
        pricing: const OrderPricing(
          subtotal: 15000.0,
          taxAmount: 1500.0,
          discountAmount: 500.0,
          shippingCost: 200.0,
          totalAmount: 16200.0,
          currency: 'USD',
        ),
        currentDepartment: Department.sewing,
        completionPercentage: 65.0,
      ),
      ManufacturingOrder(
        id: '2',
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        orderNumber: 'ORD-2024-002',
        clientId: 'client2',
        clientName: 'Urban Style Co.',
        status: OrderStatus.pending,
        priority: OrderPriority.urgent,
        type: OrderType.regular,
        orderDate: now.subtract(const Duration(days: 3)),
        requiredDate: now.add(const Duration(days: 7)),
        items: _getMockOrderItems(),
        pricing: const OrderPricing(
          subtotal: 8500.0,
          taxAmount: 850.0,
          discountAmount: 0.0,
          shippingCost: 150.0,
          totalAmount: 9500.0,
          currency: 'USD',
        ),
        currentDepartment: Department.merchandising,
        completionPercentage: 0.0,
      ),
      ManufacturingOrder(
        id: '3',
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 2)),
        orderNumber: 'ORD-2024-003',
        clientId: 'client3',
        clientName: 'Classic Wear Inc.',
        status: OrderStatus.completed,
        priority: OrderPriority.normal,
        type: OrderType.regular,
        orderDate: now.subtract(const Duration(days: 10)),
        requiredDate: now.subtract(const Duration(days: 2)),
        items: _getMockOrderItems(),
        pricing: const OrderPricing(
          subtotal: 12000.0,
          taxAmount: 1200.0,
          discountAmount: 300.0,
          shippingCost: 180.0,
          totalAmount: 13080.0,
          currency: 'USD',
        ),
        currentDepartment: Department.finishing,
        completionPercentage: 100.0,
      ),
    ];
  }

  List<OrderItem> _getMockOrderItems() {
    return [
      const OrderItem(
        id: 'item1',
        productId: 'prod1',
        productName: 'Cotton T-Shirt',
        productCode: 'CT-001',
        description: 'Premium cotton t-shirt with custom print',
        quantity: 100,
        unit: 'pieces',
        unitPrice: 12.50,
        totalPrice: 1250.0,
        size: GarmentSize.m,
        color: 'Navy Blue',
        fabric: 'Cotton',
        status: OrderItemStatus.inProgress,
        completedQuantity: 65,
      ),
      const OrderItem(
        id: 'item2',
        productId: 'prod2',
        productName: 'Polo Shirt',
        productCode: 'PS-002',
        description: 'Classic polo shirt with embroidered logo',
        quantity: 50,
        unit: 'pieces',
        unitPrice: 18.00,
        totalPrice: 900.0,
        size: GarmentSize.l,
        color: 'White',
        fabric: 'Cotton Blend',
        status: OrderItemStatus.pending,
        completedQuantity: 0,
      ),
    ];
  }

  ManufacturingOrder _getMockOrder(String orderId) {
    return _getMockOrders().firstWhere(
      (order) => order.id == orderId,
      orElse: () => _getMockOrders().first,
    );
  }

  OrderStatistics _getMockOrderStatistics() {
    return const OrderStatistics(
      totalOrders: 156,
      pendingOrders: 23,
      inProductionOrders: 45,
      completedOrders: 78,
      overdueOrders: 8,
      totalValue: 285000.0,
      averageOrderValue: 1826.92,
      ordersByStatus: {
        'pending': 23,
        'in_production': 45,
        'completed': 78,
        'shipped': 10,
      },
      ordersByPriority: {
        'low': 15,
        'normal': 89,
        'high': 42,
        'urgent': 10,
      },
      revenueByMonth: {
        'Jan': 85000.0,
        'Feb': 92000.0,
        'Mar': 108000.0,
      },
    );
  }

  ManufacturingOrder _parseOrder(Map<String, dynamic> json) {
    // Parse order from JSON - simplified for mock implementation
    return _getMockOrders().first;
  }

  // Unimplemented methods - return not implemented errors
  @override
  Future<Either<Failure, ApiResponse<OrderNote>>> addOrderNote(
    AddOrderNoteRequest request,
  ) async {
    return const Left(UnimplementedFailure('Add order note not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<OrderNote>>> updateOrderNote(
    UpdateOrderNoteRequest request,
  ) async {
    return const Left(UnimplementedFailure('Update order note not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteOrderNote(String noteId) async {
    return const Left(UnimplementedFailure('Delete order note not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<OrderAttachment>>> addOrderAttachment(
    AddOrderAttachmentRequest request,
  ) async {
    return const Left(UnimplementedFailure('Add order attachment not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteOrderAttachment(
    String attachmentId,
  ) async {
    return const Left(UnimplementedFailure('Delete order attachment not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<OrderItem>>> updateOrderItemStatus(
    String orderId,
    String itemId,
    OrderItemStatus status,
    int? completedQuantity,
  ) async {
    return const Left(UnimplementedFailure('Update order item status not implemented'));
  }

  @override
  Future<Either<Failure, List<OrderTimelineEvent>>> getOrderTimeline(
    String orderId,
  ) async {
    return const Left(UnimplementedFailure('Get order timeline not implemented'));
  }

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOrdersByClient(
    String clientId, {
    PaginationParams? pagination,
  }) async {
    return const Left(UnimplementedFailure('Get orders by client not implemented'));
  }

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingOrder>>> getOrdersByStatus(
    OrderStatus status, {
    PaginationParams? pagination,
  }) async {
    return const Left(UnimplementedFailure('Get orders by status not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> duplicateOrder(
    String orderId,
    DuplicateOrderRequest request,
  ) async {
    return const Left(UnimplementedFailure('Duplicate order not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> cancelOrder(
    String orderId,
    String reason,
  ) async {
    return const Left(UnimplementedFailure('Cancel order not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> holdOrder(
    String orderId,
    String reason,
  ) async {
    return const Left(UnimplementedFailure('Hold order not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<ManufacturingOrder>>> resumeOrder(
    String orderId,
  ) async {
    return const Left(UnimplementedFailure('Resume order not implemented'));
  }

  @override
  Future<Either<Failure, String>> exportOrders({
    OrderFilterCriteria? filter,
    String format = 'csv',
  }) async {
    return const Left(UnimplementedFailure('Export orders not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<OrderImportResult>>> importOrders(
    String filePath, {
    bool validateOnly = false,
  }) async {
    return const Left(UnimplementedFailure('Import orders not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<BulkUpdateResult>>> bulkUpdateOrders(
    List<BulkUpdateOrderRequest> requests,
  ) async {
    return const Left(UnimplementedFailure('Bulk update orders not implemented'));
  }

  @override
  Future<Either<Failure, List<OrderProductionSchedule>>> getOrderProductionSchedule(
    String orderId,
  ) async {
    return const Left(UnimplementedFailure('Get order production schedule not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> updateOrderProductionSchedule(
    String orderId,
    List<OrderProductionSchedule> schedule,
  ) async {
    return const Left(UnimplementedFailure('Update order production schedule not implemented'));
  }
}
