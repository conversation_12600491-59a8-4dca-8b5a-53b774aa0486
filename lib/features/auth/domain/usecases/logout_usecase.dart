import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';

import '../repositories/auth_repository.dart';

/// Logout use case
@injectable
class LogoutUseCase implements UseCase<bool, LogoutParams> {
  final AuthRepository _authRepository;
  final LocalAuthRepository _localAuthRepository;

  const LogoutUseCase(
    this._authRepository,
    this._localAuthRepository,
  );

  @override
  Future<Either<Failure, bool>> call(LogoutParams params) async {
    try {
      // Logout from server if not local only
      if (!params.localOnly) {
        final logoutResult = await _authRepository.logout();
        
        // Continue with local cleanup even if server logout fails
        logoutResult.fold(
          (failure) {
            // Log the failure but don't stop the logout process
            // This ensures user can logout even when offline
          },
          (response) {
            // Server logout successful
          },
        );
      }

      // Clear local authentication data
      await _clearLocalData(params);

      return const Right(true);
    } catch (e) {
      // Even if there's an error, try to clear local data
      try {
        await _clearLocalData(params);
      } catch (clearError) {
        // If we can't clear local data, that's a serious issue
        return Left(CacheFailure('Failed to clear local authentication data: $clearError'));
      }
      
      return Left(UnknownFailure('Logout failed: $e'));
    }
  }

  /// Clear local authentication data
  Future<void> _clearLocalData(LogoutParams params) async {
    final futures = <Future<Either<Failure, void>>>[];

    // Always clear tokens and user data
    futures.add(_localAuthRepository.clearTokens());
    futures.add(_localAuthRepository.clearUser());

    // Clear additional data based on parameters
    if (params.clearPreferences) {
      futures.add(_localAuthRepository.clearUserPreferences());
    }

    if (params.clearCredentials) {
      futures.add(_localAuthRepository.clearLoginCredentials());
    }

    if (params.clearBiometric) {
      futures.add(_localAuthRepository.disableBiometric());
    }

    if (params.clearAll) {
      futures.add(_localAuthRepository.clearAllAuthData());
    }

    // Wait for all clear operations to complete
    await Future.wait(futures);
  }
}

/// Logout parameters
class LogoutParams {
  final bool localOnly;
  final bool clearPreferences;
  final bool clearCredentials;
  final bool clearBiometric;
  final bool clearAll;
  final String? reason;

  const LogoutParams({
    this.localOnly = false,
    this.clearPreferences = false,
    this.clearCredentials = false,
    this.clearBiometric = false,
    this.clearAll = false,
    this.reason,
  });

  /// Create logout params for normal logout
  factory LogoutParams.normal() {
    return const LogoutParams();
  }

  /// Create logout params for security logout (clear everything)
  factory LogoutParams.security() {
    return const LogoutParams(
      clearPreferences: true,
      clearCredentials: true,
      clearBiometric: true,
      reason: 'Security logout',
    );
  }

  /// Create logout params for session timeout
  factory LogoutParams.timeout() {
    return const LogoutParams(
      localOnly: true,
      reason: 'Session timeout',
    );
  }

  /// Create logout params for forced logout
  factory LogoutParams.forced() {
    return const LogoutParams(
      clearAll: true,
      reason: 'Forced logout',
    );
  }
}

/// Logout all sessions use case
@injectable
class LogoutAllSessionsUseCase implements UseCase<bool, NoParams> {
  final AuthRepository _authRepository;
  final LocalAuthRepository _localAuthRepository;

  const LogoutAllSessionsUseCase(
    this._authRepository,
    this._localAuthRepository,
  );

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    try {
      // Revoke all other sessions on server
      final revokeResult = await _authRepository.revokeAllOtherSessions();
      
      return revokeResult.fold(
        (failure) => Left(failure),
        (response) async {
          if (!response.success) {
            return Left(ServerFailure(
              response.firstError ?? 'Failed to revoke all sessions',
              details: response.errors,
            ));
          }

          // Clear local data
          await _localAuthRepository.clearAllAuthData();

          return const Right(true);
        },
      );
    } catch (e) {
      return Left(UnknownFailure('Failed to logout all sessions: $e'));
    }
  }
}

/// Clear cache use case
@injectable
class ClearAuthCacheUseCase implements UseCase<bool, ClearCacheParams> {
  final LocalAuthRepository _localAuthRepository;

  const ClearAuthCacheUseCase(this._localAuthRepository);

  @override
  Future<Either<Failure, bool>> call(ClearCacheParams params) async {
    try {
      final futures = <Future<Either<Failure, void>>>[];

      if (params.clearTokens) {
        futures.add(_localAuthRepository.clearTokens());
      }

      if (params.clearUser) {
        futures.add(_localAuthRepository.clearUser());
      }

      if (params.clearPreferences) {
        futures.add(_localAuthRepository.clearUserPreferences());
      }

      if (params.clearCredentials) {
        futures.add(_localAuthRepository.clearLoginCredentials());
      }

      if (params.clearBiometric) {
        futures.add(_localAuthRepository.disableBiometric());
      }

      if (params.clearAll) {
        futures.add(_localAuthRepository.clearAllAuthData());
      }

      await Future.wait(futures);

      return const Right(true);
    } catch (e) {
      return Left(CacheFailure('Failed to clear cache: $e'));
    }
  }
}

/// Clear cache parameters
class ClearCacheParams {
  final bool clearTokens;
  final bool clearUser;
  final bool clearPreferences;
  final bool clearCredentials;
  final bool clearBiometric;
  final bool clearAll;

  const ClearCacheParams({
    this.clearTokens = false,
    this.clearUser = false,
    this.clearPreferences = false,
    this.clearCredentials = false,
    this.clearBiometric = false,
    this.clearAll = false,
  });

  /// Clear all authentication data
  factory ClearCacheParams.all() {
    return const ClearCacheParams(clearAll: true);
  }

  /// Clear only session data (tokens and user)
  factory ClearCacheParams.session() {
    return const ClearCacheParams(
      clearTokens: true,
      clearUser: true,
    );
  }

  /// Clear only stored credentials
  factory ClearCacheParams.credentials() {
    return const ClearCacheParams(
      clearCredentials: true,
      clearBiometric: true,
    );
  }
}

/// Revoke session use case
@injectable
class RevokeSessionUseCase implements UseCase<bool, RevokeSessionParams> {
  final AuthRepository _authRepository;

  const RevokeSessionUseCase(this._authRepository);

  @override
  Future<Either<Failure, bool>> call(RevokeSessionParams params) async {
    try {
      final revokeResult = await _authRepository.revokeSession(params.sessionId);
      
      return revokeResult.fold(
        (failure) => Left(failure),
        (response) {
          if (!response.success) {
            return Left(ServerFailure(
              response.firstError ?? 'Failed to revoke session',
              details: response.errors,
            ));
          }

          return const Right(true);
        },
      );
    } catch (e) {
      return Left(UnknownFailure('Failed to revoke session: $e'));
    }
  }
}

/// Revoke session parameters
class RevokeSessionParams {
  final String sessionId;
  final String? reason;

  const RevokeSessionParams({
    required this.sessionId,
    this.reason,
  });
}
