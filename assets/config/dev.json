{"apiBaseUrl": "https://dev-api.hmcollection.com", "enableLogging": true, "enableAnalytics": false, "cacheTimeout": 300, "features": {"biometricAuth": true, "offlineMode": true, "realTimeUpdates": true, "debugMode": true}, "database": {"name": "hm_collection_dev.db", "version": 1}, "notifications": {"enabled": true, "channels": {"orders": "Order Updates", "production": "Production Alerts", "quality": "Quality Control", "inventory": "Inventory Alerts", "system": "System Notifications"}}, "security": {"tokenExpiryMinutes": 60, "refreshTokenExpiryDays": 7, "maxLoginAttempts": 5, "lockoutDurationMinutes": 15}, "ui": {"theme": "system", "language": "en", "dateFormat": "MMM dd, yyyy", "timeFormat": "12h"}}