import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/manufacturing_entities.dart';
import '../../domain/entities/manufacturing_task_entities.dart';
import '../../../production_planning/domain/entities/production_entities.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../../../../shared/enums/common_enums.dart';

/// Manufacturing Task Order Model for Firestore serialization
class ManufacturingTaskOrderModel {
  final String id;
  final String orderNumber;
  final String productionOrderId;
  final String productId;
  final String productName;
  final String clientName;
  final int totalQuantity;
  final int completedQuantity;
  final String status;
  final String priority;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<Map<String, dynamic>> stageTasks;
  final String currentStage;
  final String? assignedSupervisor;
  final Map<String, dynamic> specifications;
  final List<Map<String, dynamic>> notes;
  final Map<String, dynamic> metrics;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final String? createdBy;
  final String? updatedBy;

  const ManufacturingTaskOrderModel({
    required this.id,
    required this.orderNumber,
    required this.productionOrderId,
    required this.productId,
    required this.productName,
    required this.clientName,
    required this.totalQuantity,
    this.completedQuantity = 0,
    required this.status,
    required this.priority,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.stageTasks = const [],
    required this.currentStage,
    this.assignedSupervisor,
    this.specifications = const {},
    this.notes = const [],
    required this.metrics,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
  });

  /// Convert from Firestore document
  factory ManufacturingTaskOrderModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return ManufacturingTaskOrderModel(
      id: doc.id,
      orderNumber: data['orderNumber'] ?? '',
      productionOrderId: data['productionOrderId'] ?? '',
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      clientName: data['clientName'] ?? '',
      totalQuantity: data['totalQuantity'] ?? 0,
      completedQuantity: data['completedQuantity'] ?? 0,
      status: data['status'] ?? 'created',
      priority: data['priority'] ?? 'normal',
      plannedStartDate: (data['plannedStartDate'] as Timestamp).toDate(),
      plannedEndDate: (data['plannedEndDate'] as Timestamp).toDate(),
      actualStartDate: data['actualStartDate'] != null
          ? (data['actualStartDate'] as Timestamp).toDate()
          : null,
      actualEndDate: data['actualEndDate'] != null
          ? (data['actualEndDate'] as Timestamp).toDate()
          : null,
      stageTasks: List<Map<String, dynamic>>.from(data['stageTasks'] ?? []),
      currentStage: data['currentStage'] ?? 'cuttingMaster',
      assignedSupervisor: data['assignedSupervisor'],
      specifications: Map<String, dynamic>.from(data['specifications'] ?? {}),
      notes: List<Map<String, dynamic>>.from(data['notes'] ?? []),
      metrics: Map<String, dynamic>.from(data['metrics'] ?? {}),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      deletedAt: data['deletedAt'] != null
          ? (data['deletedAt'] as Timestamp).toDate()
          : null,
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'orderNumber': orderNumber,
      'productionOrderId': productionOrderId,
      'productId': productId,
      'productName': productName,
      'clientName': clientName,
      'totalQuantity': totalQuantity,
      'completedQuantity': completedQuantity,
      'status': status,
      'priority': priority,
      'plannedStartDate': Timestamp.fromDate(plannedStartDate),
      'plannedEndDate': Timestamp.fromDate(plannedEndDate),
      'actualStartDate': actualStartDate != null
          ? Timestamp.fromDate(actualStartDate!)
          : null,
      'actualEndDate': actualEndDate != null
          ? Timestamp.fromDate(actualEndDate!)
          : null,
      'stageTasks': stageTasks,
      'currentStage': currentStage,
      'assignedSupervisor': assignedSupervisor,
      'specifications': specifications,
      'notes': notes,
      'metrics': metrics,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  /// Convert to domain entity
  ManufacturingTaskOrder toEntity() {
    return ManufacturingTaskOrder(
      id: id,
      orderNumber: orderNumber,
      productionOrderId: productionOrderId,
      productId: productId,
      productName: productName,
      clientName: clientName,
      totalQuantity: totalQuantity,
      completedQuantity: completedQuantity,
      status: ManufacturingOrderStatus.values.byName(status),
      priority: ManufacturingOrderPriority.values.byName(priority),
      plannedStartDate: plannedStartDate,
      plannedEndDate: plannedEndDate,
      actualStartDate: actualStartDate,
      actualEndDate: actualEndDate,
      stageTasks: stageTasks.map((task) => ManufacturingStageTaskModel.fromMap(task).toEntity()).toList(),
      currentStage: ManufacturingStage.values.byName(currentStage),
      assignedSupervisor: assignedSupervisor,
      specifications: specifications,
      notes: notes.map((note) => ManufacturingTaskNoteModel.fromMap(note).toEntity()).toList(),
      metrics: ManufacturingTaskMetricsModel.fromMap(metrics).toEntity(),
      metadata: metadata,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
    );
  }

  /// Convert from domain entity
  factory ManufacturingTaskOrderModel.fromEntity(ManufacturingTaskOrder entity) {
    return ManufacturingTaskOrderModel(
      id: entity.id,
      orderNumber: entity.orderNumber,
      productionOrderId: entity.productionOrderId,
      productId: entity.productId,
      productName: entity.productName,
      clientName: entity.clientName,
      totalQuantity: entity.totalQuantity,
      completedQuantity: entity.completedQuantity,
      status: entity.status.name,
      priority: entity.priority.name,
      plannedStartDate: entity.plannedStartDate,
      plannedEndDate: entity.plannedEndDate,
      actualStartDate: entity.actualStartDate,
      actualEndDate: entity.actualEndDate,
      stageTasks: entity.stageTasks.map((task) => ManufacturingStageTaskModel.fromEntity(task).toMap()).toList(),
      currentStage: entity.currentStage.name,
      assignedSupervisor: entity.assignedSupervisor,
      specifications: entity.specifications,
      notes: entity.notes.map((note) => ManufacturingTaskNoteModel.fromEntity(note).toMap()).toList(),
      metrics: ManufacturingTaskMetricsModel.fromEntity(entity.metrics).toMap(),
      metadata: entity.metadata,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    );
  }
}

/// Manufacturing Stage Task Model for Firestore serialization
class ManufacturingStageTaskModel {
  final String id;
  final String manufacturingTaskOrderId;
  final String stage;
  final String taskType;
  final String taskName;
  final String description;
  final String status;
  final String priority;
  final String department;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<String> assignedWorkers;
  final List<String> requiredSkills;
  final int estimatedHours;
  final int actualHours;
  final int quantityToProcess;
  final int quantityProcessed;
  final double qualityScore;
  final String? qualityFeedback;
  final List<Map<String, dynamic>> attachments;
  final List<Map<String, dynamic>> comments;
  final Map<String, dynamic> specifications;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const ManufacturingStageTaskModel({
    required this.id,
    required this.manufacturingTaskOrderId,
    required this.stage,
    required this.taskType,
    required this.taskName,
    required this.description,
    required this.status,
    required this.priority,
    required this.department,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.assignedWorkers = const [],
    this.requiredSkills = const [],
    required this.estimatedHours,
    this.actualHours = 0,
    required this.quantityToProcess,
    this.quantityProcessed = 0,
    this.qualityScore = 0.0,
    this.qualityFeedback,
    this.attachments = const [],
    this.comments = const [],
    this.specifications = const {},
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Convert from Map
  factory ManufacturingStageTaskModel.fromMap(Map<String, dynamic> data) {
    return ManufacturingStageTaskModel(
      id: data['id'] ?? '',
      manufacturingTaskOrderId: data['manufacturingTaskOrderId'] ?? '',
      stage: data['stage'] ?? '',
      taskType: data['taskType'] ?? '',
      taskName: data['taskName'] ?? '',
      description: data['description'] ?? '',
      status: data['status'] ?? 'pending',
      priority: data['priority'] ?? 'normal',
      department: data['department'] ?? 'other',
      plannedStartDate: data['plannedStartDate'] is Timestamp
          ? (data['plannedStartDate'] as Timestamp).toDate()
          : DateTime.parse(data['plannedStartDate']),
      plannedEndDate: data['plannedEndDate'] is Timestamp
          ? (data['plannedEndDate'] as Timestamp).toDate()
          : DateTime.parse(data['plannedEndDate']),
      actualStartDate: data['actualStartDate'] != null
          ? (data['actualStartDate'] is Timestamp
              ? (data['actualStartDate'] as Timestamp).toDate()
              : DateTime.parse(data['actualStartDate']))
          : null,
      actualEndDate: data['actualEndDate'] != null
          ? (data['actualEndDate'] is Timestamp
              ? (data['actualEndDate'] as Timestamp).toDate()
              : DateTime.parse(data['actualEndDate']))
          : null,
      assignedWorkers: List<String>.from(data['assignedWorkers'] ?? []),
      requiredSkills: List<String>.from(data['requiredSkills'] ?? []),
      estimatedHours: data['estimatedHours'] ?? 0,
      actualHours: data['actualHours'] ?? 0,
      quantityToProcess: data['quantityToProcess'] ?? 0,
      quantityProcessed: data['quantityProcessed'] ?? 0,
      qualityScore: (data['qualityScore'] ?? 0.0).toDouble(),
      qualityFeedback: data['qualityFeedback'],
      attachments: List<Map<String, dynamic>>.from(data['attachments'] ?? []),
      comments: List<Map<String, dynamic>>.from(data['comments'] ?? []),
      specifications: Map<String, dynamic>.from(data['specifications'] ?? {}),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: data['createdAt'] is Timestamp
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.parse(data['createdAt']),
      updatedAt: data['updatedAt'] is Timestamp
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(data['updatedAt']),
      deletedAt: data['deletedAt'] != null
          ? (data['deletedAt'] is Timestamp
              ? (data['deletedAt'] as Timestamp).toDate()
              : DateTime.parse(data['deletedAt']))
          : null,
    );
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'manufacturingTaskOrderId': manufacturingTaskOrderId,
      'stage': stage,
      'taskType': taskType,
      'taskName': taskName,
      'description': description,
      'status': status,
      'priority': priority,
      'department': department,
      'plannedStartDate': plannedStartDate.toIso8601String(),
      'plannedEndDate': plannedEndDate.toIso8601String(),
      'actualStartDate': actualStartDate?.toIso8601String(),
      'actualEndDate': actualEndDate?.toIso8601String(),
      'assignedWorkers': assignedWorkers,
      'requiredSkills': requiredSkills,
      'estimatedHours': estimatedHours,
      'actualHours': actualHours,
      'quantityToProcess': quantityToProcess,
      'quantityProcessed': quantityProcessed,
      'qualityScore': qualityScore,
      'qualityFeedback': qualityFeedback,
      'attachments': attachments,
      'comments': comments,
      'specifications': specifications,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  /// Convert to domain entity
  ManufacturingStageTask toEntity() {
    return ManufacturingStageTask(
      id: id,
      manufacturingTaskOrderId: manufacturingTaskOrderId,
      stage: ManufacturingStage.values.byName(stage),
      taskType: TaskType.values.byName(taskType),
      taskName: taskName,
      description: description,
      status: TaskStatus.values.byName(status),
      priority: TaskPriority.values.byName(priority),
      department: DepartmentType.values.byName(department),
      plannedStartDate: plannedStartDate,
      plannedEndDate: plannedEndDate,
      actualStartDate: actualStartDate,
      actualEndDate: actualEndDate,
      assignedWorkers: assignedWorkers,
      requiredSkills: requiredSkills,
      estimatedHours: estimatedHours,
      actualHours: actualHours,
      quantityToProcess: quantityToProcess,
      quantityProcessed: quantityProcessed,
      qualityScore: qualityScore,
      qualityFeedback: qualityFeedback,
      attachments: attachments.map((attachment) => TaskAttachment.fromJson(attachment)).toList(),
      comments: comments.map((comment) => TaskComment.fromJson(comment)).toList(),
      specifications: specifications,
      metadata: metadata,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
    );
  }

  /// Convert from domain entity
  factory ManufacturingStageTaskModel.fromEntity(ManufacturingStageTask entity) {
    return ManufacturingStageTaskModel(
      id: entity.id,
      manufacturingTaskOrderId: entity.manufacturingTaskOrderId,
      stage: entity.stage.name,
      taskType: entity.taskType.name,
      taskName: entity.taskName,
      description: entity.description,
      status: entity.status.name,
      priority: entity.priority.name,
      department: entity.department.name,
      plannedStartDate: entity.plannedStartDate,
      plannedEndDate: entity.plannedEndDate,
      actualStartDate: entity.actualStartDate,
      actualEndDate: entity.actualEndDate,
      assignedWorkers: entity.assignedWorkers,
      requiredSkills: entity.requiredSkills,
      estimatedHours: entity.estimatedHours,
      actualHours: entity.actualHours,
      quantityToProcess: entity.quantityToProcess,
      quantityProcessed: entity.quantityProcessed,
      qualityScore: entity.qualityScore,
      qualityFeedback: entity.qualityFeedback,
      attachments: entity.attachments.map((attachment) => attachment.toJson()).toList(),
      comments: entity.comments.map((comment) => comment.toJson()).toList(),
      specifications: entity.specifications,
      metadata: entity.metadata,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    );
  }
}

/// Manufacturing Task Note Model
class ManufacturingTaskNoteModel {
  final String id;
  final String manufacturingTaskOrderId;
  final String? stageTaskId;
  final String userId;
  final String userName;
  final String content;
  final String type;
  final String priority;
  final bool isInternal;
  final List<Map<String, dynamic>> attachments;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const ManufacturingTaskNoteModel({
    required this.id,
    required this.manufacturingTaskOrderId,
    this.stageTaskId,
    required this.userId,
    required this.userName,
    required this.content,
    required this.type,
    required this.priority,
    this.isInternal = false,
    this.attachments = const [],
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Convert from Map
  factory ManufacturingTaskNoteModel.fromMap(Map<String, dynamic> data) {
    return ManufacturingTaskNoteModel(
      id: data['id'] ?? '',
      manufacturingTaskOrderId: data['manufacturingTaskOrderId'] ?? '',
      stageTaskId: data['stageTaskId'],
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      content: data['content'] ?? '',
      type: data['type'] ?? 'general',
      priority: data['priority'] ?? 'normal',
      isInternal: data['isInternal'] ?? false,
      attachments: List<Map<String, dynamic>>.from(data['attachments'] ?? []),
      createdAt: data['createdAt'] is Timestamp
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.parse(data['createdAt']),
      updatedAt: data['updatedAt'] is Timestamp
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(data['updatedAt']),
      deletedAt: data['deletedAt'] != null
          ? (data['deletedAt'] is Timestamp
              ? (data['deletedAt'] as Timestamp).toDate()
              : DateTime.parse(data['deletedAt']))
          : null,
    );
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'manufacturingTaskOrderId': manufacturingTaskOrderId,
      'stageTaskId': stageTaskId,
      'userId': userId,
      'userName': userName,
      'content': content,
      'type': type,
      'priority': priority,
      'isInternal': isInternal,
      'attachments': attachments,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  /// Convert to domain entity
  ManufacturingTaskNote toEntity() {
    return ManufacturingTaskNote(
      id: id,
      manufacturingTaskOrderId: manufacturingTaskOrderId,
      stageTaskId: stageTaskId,
      userId: userId,
      userName: userName,
      content: content,
      type: NoteType.values.byName(type),
      priority: NotePriority.values.byName(priority),
      isInternal: isInternal,
      attachments: attachments.map((attachment) => NoteAttachment.fromJson(attachment)).toList(),
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
    );
  }

  /// Convert from domain entity
  factory ManufacturingTaskNoteModel.fromEntity(ManufacturingTaskNote entity) {
    return ManufacturingTaskNoteModel(
      id: entity.id,
      manufacturingTaskOrderId: entity.manufacturingTaskOrderId,
      stageTaskId: entity.stageTaskId,
      userId: entity.userId,
      userName: entity.userName,
      content: entity.content,
      type: entity.type.name,
      priority: entity.priority.name,
      isInternal: entity.isInternal,
      attachments: entity.attachments.map((attachment) => attachment.toJson()).toList(),
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    );
  }
}

/// Manufacturing Task Metrics Model
class ManufacturingTaskMetricsModel {
  final double plannedEfficiency;
  final double actualEfficiency;
  final double qualityScore;
  final double onTimeDelivery;
  final double resourceUtilization;
  final int totalStages;
  final int completedStages;
  final double averageStageTime;
  final double costVariance;
  final double scheduleVariance;

  const ManufacturingTaskMetricsModel({
    required this.plannedEfficiency,
    required this.actualEfficiency,
    required this.qualityScore,
    required this.onTimeDelivery,
    required this.resourceUtilization,
    required this.totalStages,
    required this.completedStages,
    required this.averageStageTime,
    required this.costVariance,
    required this.scheduleVariance,
  });

  /// Convert from Map
  factory ManufacturingTaskMetricsModel.fromMap(Map<String, dynamic> data) {
    return ManufacturingTaskMetricsModel(
      plannedEfficiency: (data['plannedEfficiency'] ?? 0.0).toDouble(),
      actualEfficiency: (data['actualEfficiency'] ?? 0.0).toDouble(),
      qualityScore: (data['qualityScore'] ?? 0.0).toDouble(),
      onTimeDelivery: (data['onTimeDelivery'] ?? 0.0).toDouble(),
      resourceUtilization: (data['resourceUtilization'] ?? 0.0).toDouble(),
      totalStages: data['totalStages'] ?? 0,
      completedStages: data['completedStages'] ?? 0,
      averageStageTime: (data['averageStageTime'] ?? 0.0).toDouble(),
      costVariance: (data['costVariance'] ?? 0.0).toDouble(),
      scheduleVariance: (data['scheduleVariance'] ?? 0.0).toDouble(),
    );
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'plannedEfficiency': plannedEfficiency,
      'actualEfficiency': actualEfficiency,
      'qualityScore': qualityScore,
      'onTimeDelivery': onTimeDelivery,
      'resourceUtilization': resourceUtilization,
      'totalStages': totalStages,
      'completedStages': completedStages,
      'averageStageTime': averageStageTime,
      'costVariance': costVariance,
      'scheduleVariance': scheduleVariance,
    };
  }

  /// Convert to domain entity
  ManufacturingTaskMetrics toEntity() {
    return ManufacturingTaskMetrics(
      plannedEfficiency: plannedEfficiency,
      actualEfficiency: actualEfficiency,
      qualityScore: qualityScore,
      onTimeDelivery: onTimeDelivery,
      resourceUtilization: resourceUtilization,
      totalStages: totalStages,
      completedStages: completedStages,
      averageStageTime: averageStageTime,
      costVariance: costVariance,
      scheduleVariance: scheduleVariance,
    );
  }

  /// Convert from domain entity
  factory ManufacturingTaskMetricsModel.fromEntity(ManufacturingTaskMetrics entity) {
    return ManufacturingTaskMetricsModel(
      plannedEfficiency: entity.plannedEfficiency,
      actualEfficiency: entity.actualEfficiency,
      qualityScore: entity.qualityScore,
      onTimeDelivery: entity.onTimeDelivery,
      resourceUtilization: entity.resourceUtilization,
      totalStages: entity.totalStages,
      completedStages: entity.completedStages,
      averageStageTime: entity.averageStageTime,
      costVariance: entity.costVariance,
      scheduleVariance: entity.scheduleVariance,
    );
  }
}

/// Manufacturing Stage Transfer Model
class ManufacturingStageTransferModel {
  final String id;
  final String manufacturingTaskOrderId;
  final String stageTaskId;
  final String fromStage;
  final String toStage;
  final String transferredBy;
  final String transferredByName;
  final String? receivedBy;
  final String? receivedByName;
  final DateTime transferredAt;
  final DateTime? receivedAt;
  final String status;
  final int quantityTransferred;
  final String? notes;
  final List<Map<String, dynamic>> attachments;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const ManufacturingStageTransferModel({
    required this.id,
    required this.manufacturingTaskOrderId,
    required this.stageTaskId,
    required this.fromStage,
    required this.toStage,
    required this.transferredBy,
    required this.transferredByName,
    this.receivedBy,
    this.receivedByName,
    required this.transferredAt,
    this.receivedAt,
    required this.status,
    required this.quantityTransferred,
    this.notes,
    this.attachments = const [],
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Convert from Firestore document
  factory ManufacturingStageTransferModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return ManufacturingStageTransferModel(
      id: doc.id,
      manufacturingTaskOrderId: data['manufacturingTaskOrderId'] ?? '',
      stageTaskId: data['stageTaskId'] ?? '',
      fromStage: data['fromStage'] ?? '',
      toStage: data['toStage'] ?? '',
      transferredBy: data['transferredBy'] ?? '',
      transferredByName: data['transferredByName'] ?? '',
      receivedBy: data['receivedBy'],
      receivedByName: data['receivedByName'],
      transferredAt: (data['transferredAt'] as Timestamp).toDate(),
      receivedAt: data['receivedAt'] != null
          ? (data['receivedAt'] as Timestamp).toDate()
          : null,
      status: data['status'] ?? 'pending',
      quantityTransferred: data['quantityTransferred'] ?? 0,
      notes: data['notes'],
      attachments: List<Map<String, dynamic>>.from(data['attachments'] ?? []),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      deletedAt: data['deletedAt'] != null
          ? (data['deletedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'manufacturingTaskOrderId': manufacturingTaskOrderId,
      'stageTaskId': stageTaskId,
      'fromStage': fromStage,
      'toStage': toStage,
      'transferredBy': transferredBy,
      'transferredByName': transferredByName,
      'receivedBy': receivedBy,
      'receivedByName': receivedByName,
      'transferredAt': Timestamp.fromDate(transferredAt),
      'receivedAt': receivedAt != null ? Timestamp.fromDate(receivedAt!) : null,
      'status': status,
      'quantityTransferred': quantityTransferred,
      'notes': notes,
      'attachments': attachments,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
    };
  }

  /// Convert to domain entity
  ManufacturingStageTransfer toEntity() {
    return ManufacturingStageTransfer(
      id: id,
      manufacturingTaskOrderId: manufacturingTaskOrderId,
      stageTaskId: stageTaskId,
      fromStage: ManufacturingStage.values.byName(fromStage),
      toStage: ManufacturingStage.values.byName(toStage),
      transferredBy: transferredBy,
      transferredByName: transferredByName,
      receivedBy: receivedBy,
      receivedByName: receivedByName,
      transferredAt: transferredAt,
      receivedAt: receivedAt,
      status: TransferStatus.values.byName(status),
      quantityTransferred: quantityTransferred,
      notes: notes,
      attachments: attachments.map((attachment) => TransferAttachment.fromJson(attachment)).toList(),
      metadata: metadata,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
    );
  }

  /// Convert from domain entity
  factory ManufacturingStageTransferModel.fromEntity(ManufacturingStageTransfer entity) {
    return ManufacturingStageTransferModel(
      id: entity.id,
      manufacturingTaskOrderId: entity.manufacturingTaskOrderId,
      stageTaskId: entity.stageTaskId,
      fromStage: entity.fromStage.name,
      toStage: entity.toStage.name,
      transferredBy: entity.transferredBy,
      transferredByName: entity.transferredByName,
      receivedBy: entity.receivedBy,
      receivedByName: entity.receivedByName,
      transferredAt: entity.transferredAt,
      receivedAt: entity.receivedAt,
      status: entity.status.name,
      quantityTransferred: entity.quantityTransferred,
      notes: entity.notes,
      attachments: entity.attachments.map((attachment) => attachment.toJson()).toList(),
      metadata: entity.metadata,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    );
  }
}
