import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';
import '../../../resource_management/presentation/bloc/resource_bloc.dart';

/// Production schedule entity
class ProductionSchedule extends BaseEntity {
  final String name;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final ScheduleStatus status;
  final List<ScheduleItem> items;
  final List<ResourceSchedule> resourceSchedules;
  final ScheduleMetrics metrics;
  final String? createdBy;
  final String? approvedBy;
  final DateTime? approvedAt;

  const ProductionSchedule({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.name,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.items,
    required this.resourceSchedules,
    required this.metrics,
    this.createdBy,
    this.approvedBy,
    this.approvedAt,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        description,
        startDate,
        endDate,
        status,
        items,
        resourceSchedules,
        metrics,
        createdBy,
        approvedBy,
        approvedAt,
      ];

  /// Get schedule duration in days
  int get durationInDays => endDate.difference(startDate).inDays;

  /// Get completion percentage
  double get completionPercentage {
    if (items.isEmpty) return 0.0;
    final completedItems = items.where((item) => item.isCompleted).length;
    return (completedItems / items.length) * 100;
  }

  /// Check if schedule is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(endDate) && !status.isCompleted;
  }
}

/// Schedule item entity
class ScheduleItem extends BaseEntity {
  final String scheduleId;
  final String productionOrderId;
  final String taskId;
  final String taskName;
  final Department department;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final ScheduleItemStatus status;
  final int estimatedHours;
  final int actualHours;
  final List<String> assignedResources;
  final List<ScheduleConflict> conflicts;
  final Map<String, dynamic> metadata;

  const ScheduleItem({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.scheduleId,
    required this.productionOrderId,
    required this.taskId,
    required this.taskName,
    required this.department,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    required this.status,
    required this.estimatedHours,
    this.actualHours = 0,
    this.assignedResources = const [],
    this.conflicts = const [],
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        scheduleId,
        productionOrderId,
        taskId,
        taskName,
        department,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        status,
        estimatedHours,
        actualHours,
        assignedResources,
        conflicts,
        metadata,
      ];

  /// Check if item is completed
  bool get isCompleted => status == ScheduleItemStatus.completed;

  /// Check if item is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(plannedEndDate) && !isCompleted;
  }

  /// Get schedule variance in hours
  double get scheduleVariance {
    if (actualStartDate == null || actualEndDate == null) return 0.0;
    final actualDuration = actualEndDate!.difference(actualStartDate!).inHours;
    return actualDuration.toDouble() - estimatedHours.toDouble();
  }
}

/// Resource schedule entity
class ResourceSchedule extends BaseEntity {
  final String scheduleId;
  final String resourceId;
  final String resourceName;
  final ResourceType resourceType;
  final Department department;
  final List<ResourceTimeSlot> timeSlots;
  final double utilizationPercentage;
  final double capacity;
  final double allocatedCapacity;

  const ResourceSchedule({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.scheduleId,
    required this.resourceId,
    required this.resourceName,
    required this.resourceType,
    required this.department,
    required this.timeSlots,
    required this.utilizationPercentage,
    required this.capacity,
    required this.allocatedCapacity,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        scheduleId,
        resourceId,
        resourceName,
        resourceType,
        department,
        timeSlots,
        utilizationPercentage,
        capacity,
        allocatedCapacity,
      ];

  /// Get available capacity
  double get availableCapacity => capacity - allocatedCapacity;

  /// Check if resource is overallocated
  bool get isOverallocated => allocatedCapacity > capacity;

  /// Get conflicts
  List<ResourceTimeSlot> get conflicts {
    return timeSlots.where((slot) => slot.hasConflict).toList();
  }
}

/// Resource time slot
class ResourceTimeSlot extends Equatable {
  final String resourceId;
  final String taskId;
  final String taskName;
  final DateTime startTime;
  final DateTime endTime;
  final double allocatedCapacity;
  final TimeSlotStatus status;
  final bool hasConflict;

  const ResourceTimeSlot({
    required this.resourceId,
    required this.taskId,
    required this.taskName,
    required this.startTime,
    required this.endTime,
    required this.allocatedCapacity,
    required this.status,
    this.hasConflict = false,
  });

  @override
  List<Object?> get props => [
        resourceId,
        taskId,
        taskName,
        startTime,
        endTime,
        allocatedCapacity,
        status,
        hasConflict,
      ];

  /// Get duration in hours
  double get durationInHours {
    return endTime.difference(startTime).inHours.toDouble();
  }

  /// Check if time slot overlaps with another
  bool overlapsWith(ResourceTimeSlot other) {
    return startTime.isBefore(other.endTime) && endTime.isAfter(other.startTime);
  }
}

/// Schedule conflict
class ScheduleConflict extends Equatable {
  final String conflictId;
  final ConflictType type;
  final String description;
  final ConflictSeverity severity;
  final List<String> affectedItems;
  final DateTime detectedAt;
  final bool isResolved;
  final String? resolution;

  const ScheduleConflict({
    required this.conflictId,
    required this.type,
    required this.description,
    required this.severity,
    required this.affectedItems,
    required this.detectedAt,
    this.isResolved = false,
    this.resolution,
  });

  @override
  List<Object?> get props => [
        conflictId,
        type,
        description,
        severity,
        affectedItems,
        detectedAt,
        isResolved,
        resolution,
      ];
}

/// Schedule metrics
class ScheduleMetrics extends Equatable {
  final double overallEfficiency;
  final double onTimePerformance;
  final double resourceUtilization;
  final int totalTasks;
  final int completedTasks;
  final int delayedTasks;
  final int conflictCount;
  final Map<Department, double> departmentEfficiency;

  const ScheduleMetrics({
    required this.overallEfficiency,
    required this.onTimePerformance,
    required this.resourceUtilization,
    required this.totalTasks,
    required this.completedTasks,
    required this.delayedTasks,
    required this.conflictCount,
    this.departmentEfficiency = const {},
  });

  @override
  List<Object?> get props => [
        overallEfficiency,
        onTimePerformance,
        resourceUtilization,
        totalTasks,
        completedTasks,
        delayedTasks,
        conflictCount,
        departmentEfficiency,
      ];

  /// Get completion percentage
  double get completionPercentage {
    if (totalTasks == 0) return 0.0;
    return (completedTasks / totalTasks) * 100;
  }

  /// Get delay percentage
  double get delayPercentage {
    if (totalTasks == 0) return 0.0;
    return (delayedTasks / totalTasks) * 100;
  }
}

/// Work center entity
class WorkCenter extends BaseEntity {
  final String name;
  final String description;
  final Department department;
  final WorkCenterType type;
  final WorkCenterStatus status;
  final double capacity;
  final String unit;
  final List<String> capabilities;
  final List<WorkCenterResource> resources;
  final WorkCenterMetrics metrics;
  final Map<String, dynamic> specifications;

  const WorkCenter({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.name,
    required this.description,
    required this.department,
    required this.type,
    required this.status,
    required this.capacity,
    required this.unit,
    required this.capabilities,
    required this.resources,
    required this.metrics,
    this.specifications = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        description,
        department,
        type,
        status,
        capacity,
        unit,
        capabilities,
        resources,
        metrics,
        specifications,
      ];

  /// Check if work center is available
  bool get isAvailable => status == WorkCenterStatus.available;

  /// Get current utilization
  double get currentUtilization => metrics.utilizationPercentage;
}

/// Work center resource
class WorkCenterResource extends Equatable {
  final String resourceId;
  final String resourceName;
  final ResourceType type;
  final double capacity;
  final String unit;
  final ResourceStatus status;

  const WorkCenterResource({
    required this.resourceId,
    required this.resourceName,
    required this.type,
    required this.capacity,
    required this.unit,
    required this.status,
  });

  @override
  List<Object?> get props => [resourceId, resourceName, type, capacity, unit, status];
}

/// Work center metrics
class WorkCenterMetrics extends Equatable {
  final double utilizationPercentage;
  final double efficiency;
  final double availability;
  final int totalJobs;
  final int completedJobs;
  final double averageSetupTime;
  final double averageProcessTime;

  const WorkCenterMetrics({
    required this.utilizationPercentage,
    required this.efficiency,
    required this.availability,
    required this.totalJobs,
    required this.completedJobs,
    required this.averageSetupTime,
    required this.averageProcessTime,
  });

  @override
  List<Object?> get props => [
        utilizationPercentage,
        efficiency,
        availability,
        totalJobs,
        completedJobs,
        averageSetupTime,
        averageProcessTime,
      ];
}

// Enums

/// Schedule status enum
enum ScheduleStatus {
  draft,
  planned,
  approved,
  active,
  completed,
  cancelled,
}

/// Schedule status extension
extension ScheduleStatusExtension on ScheduleStatus {
  String get displayName {
    switch (this) {
      case ScheduleStatus.draft:
        return 'Draft';
      case ScheduleStatus.planned:
        return 'Planned';
      case ScheduleStatus.approved:
        return 'Approved';
      case ScheduleStatus.active:
        return 'Active';
      case ScheduleStatus.completed:
        return 'Completed';
      case ScheduleStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value => name;

  bool get isCompleted => this == ScheduleStatus.completed;
  bool get isActive => this == ScheduleStatus.active;
  bool get canEdit => this == ScheduleStatus.draft || this == ScheduleStatus.planned;
}

/// Schedule item status enum
enum ScheduleItemStatus {
  scheduled,
  ready,
  inProgress,
  completed,
  delayed,
  cancelled,
}

/// Time slot status enum
enum TimeSlotStatus {
  scheduled,
  confirmed,
  inProgress,
  completed,
  cancelled,
}

/// Conflict type enum
enum ConflictType {
  resourceOverallocation,
  timeOverlap,
  dependencyViolation,
  capacityExceeded,
  skillMismatch,
}

/// Conflict severity enum
enum ConflictSeverity {
  low,
  medium,
  high,
  critical,
}

/// Work center type enum
enum WorkCenterType {
  cutting,
  sewing,
  finishing,
  quality,
  packaging,
  maintenance,
}

/// Work center status enum
enum WorkCenterStatus {
  available,
  busy,
  maintenance,
  breakdown,
  offline,
}

/// Resource status enum
enum ResourceStatus {
  available,
  allocated,
  maintenance,
  breakdown,
  offline,
}

/// Schedule filter criteria
class ScheduleFilterCriteria extends Equatable {
  final ScheduleStatus? status;
  final Department? department;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;
  final String? createdBy;
  final bool? isOverdue;

  const ScheduleFilterCriteria({
    this.status,
    this.department,
    this.startDate,
    this.endDate,
    this.searchQuery,
    this.createdBy,
    this.isOverdue,
  });

  @override
  List<Object?> get props => [
        status,
        department,
        startDate,
        endDate,
        searchQuery,
        createdBy,
        isOverdue,
      ];

  ScheduleFilterCriteria copyWith({
    ScheduleStatus? status,
    Department? department,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    String? createdBy,
    bool? isOverdue,
  }) {
    return ScheduleFilterCriteria(
      status: status ?? this.status,
      department: department ?? this.department,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      searchQuery: searchQuery ?? this.searchQuery,
      createdBy: createdBy ?? this.createdBy,
      isOverdue: isOverdue ?? this.isOverdue,
    );
  }

  Map<String, dynamic> toJson() => {
        if (status != null) 'status': status!.name,
        if (department != null) 'department': department!.value,
        if (startDate != null) 'startDate': startDate!.toIso8601String(),
        if (endDate != null) 'endDate': endDate!.toIso8601String(),
        if (searchQuery != null) 'searchQuery': searchQuery,
        if (createdBy != null) 'createdBy': createdBy,
        if (isOverdue != null) 'isOverdue': isOverdue,
      };
}
