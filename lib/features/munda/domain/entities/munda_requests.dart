import 'package:equatable/equatable.dart';
import '../../../../shared/enums/common_enums.dart';

/// Request to create a new Munda record
class CreateMundaRecordRequest extends Equatable {
  final DateTime entryDate;
  final String designNumber;
  final String lotNumber;
  final String rollNumber;
  final ThreadType threadType;
  final String? customThread;
  final int pieces;
  final double rate;
  final String checkedBy;
  final String? remarks;
  final String? taskId;

  const CreateMundaRecordRequest({
    required this.entryDate,
    required this.designNumber,
    required this.lotNumber,
    required this.rollNumber,
    required this.threadType,
    this.customThread,
    required this.pieces,
    required this.rate,
    required this.checkedBy,
    this.remarks,
    this.taskId,
  });

  /// Calculate amount
  double get amount => pieces * rate;

  /// Validate the request
  bool get isValid {
    return designNumber.isNotEmpty &&
           lotNumber.isNotEmpty &&
           rollNumber.isNotEmpty &&
           pieces > 0 &&
           rate > 0 &&
           checkedBy.isNotEmpty;
  }

  /// Convert to map for API
  Map<String, dynamic> toJson() {
    return {
      'entryDate': entryDate.toIso8601String(),
      'designNumber': designNumber,
      'lotNumber': lotNumber,
      'rollNumber': rollNumber,
      'threadType': threadType.value,
      'customThread': customThread,
      'pieces': pieces,
      'rate': rate,
      'amount': amount,
      'checkedBy': checkedBy,
      'remarks': remarks,
      'taskId': taskId,
    };
  }

  @override
  List<Object?> get props => [
        entryDate,
        designNumber,
        lotNumber,
        rollNumber,
        threadType,
        customThread,
        pieces,
        rate,
        checkedBy,
        remarks,
        taskId,
      ];
}

/// Request to update a Munda record
class UpdateMundaRecordRequest extends Equatable {
  final String recordId;
  final DateTime? entryDate;
  final String? designNumber;
  final String? lotNumber;
  final String? rollNumber;
  final ThreadType? threadType;
  final String? customThread;
  final int? pieces;
  final double? rate;
  final String? checkedBy;
  final String? remarks;
  final CommonStatus? status;

  const UpdateMundaRecordRequest({
    required this.recordId,
    this.entryDate,
    this.designNumber,
    this.lotNumber,
    this.rollNumber,
    this.threadType,
    this.customThread,
    this.pieces,
    this.rate,
    this.checkedBy,
    this.remarks,
    this.status,
  });

  /// Check if request has any updates
  bool get hasUpdates {
    return entryDate != null ||
           designNumber != null ||
           lotNumber != null ||
           rollNumber != null ||
           threadType != null ||
           customThread != null ||
           pieces != null ||
           rate != null ||
           checkedBy != null ||
           remarks != null ||
           status != null;
  }

  /// Convert to map for API
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (entryDate != null) data['entryDate'] = entryDate!.toIso8601String();
    if (designNumber != null) data['designNumber'] = designNumber;
    if (lotNumber != null) data['lotNumber'] = lotNumber;
    if (rollNumber != null) data['rollNumber'] = rollNumber;
    if (threadType != null) data['threadType'] = threadType!.value;
    if (customThread != null) data['customThread'] = customThread;
    if (pieces != null) data['pieces'] = pieces;
    if (rate != null) data['rate'] = rate;
    if (checkedBy != null) data['checkedBy'] = checkedBy;
    if (remarks != null) data['remarks'] = remarks;
    if (status != null) data['status'] = status!.value;
    
    // Recalculate amount if pieces or rate changed
    if (pieces != null || rate != null) {
      // Note: This would need the current record to calculate properly
      // In practice, this would be handled by the service layer
    }
    
    return data;
  }

  @override
  List<Object?> get props => [
        recordId,
        entryDate,
        designNumber,
        lotNumber,
        rollNumber,
        threadType,
        customThread,
        pieces,
        rate,
        checkedBy,
        remarks,
        status,
      ];
}

/// Request to create a new Munda task
class CreateMundaTaskRequest extends Equatable {
  final String title;
  final String description;
  final Priority priority;
  final String? assignedTo;
  final DateTime? dueDate;
  final int? expectedRecords;
  final int? targetPieces;
  final double? targetAmount;
  final String? orderId;
  final String? productionBatchId;
  final List<String> designNumbers;
  final List<String> lotNumbers;
  final String? instructions;

  const CreateMundaTaskRequest({
    required this.title,
    required this.description,
    required this.priority,
    this.assignedTo,
    this.dueDate,
    this.expectedRecords,
    this.targetPieces,
    this.targetAmount,
    this.orderId,
    this.productionBatchId,
    this.designNumbers = const [],
    this.lotNumbers = const [],
    this.instructions,
  });

  /// Validate the request
  bool get isValid {
    return title.isNotEmpty && description.isNotEmpty;
  }

  /// Convert to map for API
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'priority': priority.value,
      'assignedTo': assignedTo,
      'dueDate': dueDate?.toIso8601String(),
      'expectedRecords': expectedRecords,
      'targetPieces': targetPieces,
      'targetAmount': targetAmount,
      'orderId': orderId,
      'productionBatchId': productionBatchId,
      'designNumbers': designNumbers,
      'lotNumbers': lotNumbers,
      'instructions': instructions,
    };
  }

  @override
  List<Object?> get props => [
        title,
        description,
        priority,
        assignedTo,
        dueDate,
        expectedRecords,
        targetPieces,
        targetAmount,
        orderId,
        productionBatchId,
        designNumbers,
        lotNumbers,
        instructions,
      ];
}

/// Request to update a Munda task
class UpdateMundaTaskRequest extends Equatable {
  final String taskId;
  final String? title;
  final String? description;
  final MundaTaskStatus? status;
  final Priority? priority;
  final String? assignedTo;
  final DateTime? dueDate;
  final int? expectedRecords;
  final int? targetPieces;
  final double? targetAmount;
  final String? instructions;
  final String? operatorNotes;
  final String? qualityNotes;

  const UpdateMundaTaskRequest({
    required this.taskId,
    this.title,
    this.description,
    this.status,
    this.priority,
    this.assignedTo,
    this.dueDate,
    this.expectedRecords,
    this.targetPieces,
    this.targetAmount,
    this.instructions,
    this.operatorNotes,
    this.qualityNotes,
  });

  /// Check if request has any updates
  bool get hasUpdates {
    return title != null ||
           description != null ||
           status != null ||
           priority != null ||
           assignedTo != null ||
           dueDate != null ||
           expectedRecords != null ||
           targetPieces != null ||
           targetAmount != null ||
           instructions != null ||
           operatorNotes != null ||
           qualityNotes != null;
  }

  /// Convert to map for API
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (title != null) data['title'] = title;
    if (description != null) data['description'] = description;
    if (status != null) data['status'] = status!.value;
    if (priority != null) data['priority'] = priority!.value;
    if (assignedTo != null) data['assignedTo'] = assignedTo;
    if (dueDate != null) data['dueDate'] = dueDate!.toIso8601String();
    if (expectedRecords != null) data['expectedRecords'] = expectedRecords;
    if (targetPieces != null) data['targetPieces'] = targetPieces;
    if (targetAmount != null) data['targetAmount'] = targetAmount;
    if (instructions != null) data['instructions'] = instructions;
    if (operatorNotes != null) data['operatorNotes'] = operatorNotes;
    if (qualityNotes != null) data['qualityNotes'] = qualityNotes;
    
    return data;
  }

  @override
  List<Object?> get props => [
        taskId,
        title,
        description,
        status,
        priority,
        assignedTo,
        dueDate,
        expectedRecords,
        targetPieces,
        targetAmount,
        instructions,
        operatorNotes,
        qualityNotes,
      ];
}

/// Filter criteria for Munda records
class MundaRecordFilterCriteria extends Equatable {
  final String? searchQuery;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? designNumber;
  final String? lotNumber;
  final String? rollNumber;
  final ThreadType? threadType;
  final String? checkedBy;
  final String? taskId;
  final String? userId;
  final CommonStatus? status;
  final int? minPieces;
  final int? maxPieces;
  final double? minRate;
  final double? maxRate;
  final double? minAmount;
  final double? maxAmount;

  const MundaRecordFilterCriteria({
    this.searchQuery,
    this.fromDate,
    this.toDate,
    this.designNumber,
    this.lotNumber,
    this.rollNumber,
    this.threadType,
    this.checkedBy,
    this.taskId,
    this.userId,
    this.status,
    this.minPieces,
    this.maxPieces,
    this.minRate,
    this.maxRate,
    this.minAmount,
    this.maxAmount,
  });

  /// Convert to query parameters
  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = {};
    
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      params['search'] = searchQuery;
    }
    if (fromDate != null) params['fromDate'] = fromDate!.toIso8601String();
    if (toDate != null) params['toDate'] = toDate!.toIso8601String();
    if (designNumber != null) params['designNumber'] = designNumber;
    if (lotNumber != null) params['lotNumber'] = lotNumber;
    if (rollNumber != null) params['rollNumber'] = rollNumber;
    if (threadType != null) params['threadType'] = threadType!.value;
    if (checkedBy != null) params['checkedBy'] = checkedBy;
    if (taskId != null) params['taskId'] = taskId;
    if (userId != null) params['userId'] = userId;
    if (status != null) params['status'] = status!.value;
    if (minPieces != null) params['minPieces'] = minPieces;
    if (maxPieces != null) params['maxPieces'] = maxPieces;
    if (minRate != null) params['minRate'] = minRate;
    if (maxRate != null) params['maxRate'] = maxRate;
    if (minAmount != null) params['minAmount'] = minAmount;
    if (maxAmount != null) params['maxAmount'] = maxAmount;
    
    return params;
  }

  @override
  List<Object?> get props => [
        searchQuery,
        fromDate,
        toDate,
        designNumber,
        lotNumber,
        rollNumber,
        threadType,
        checkedBy,
        taskId,
        userId,
        status,
        minPieces,
        maxPieces,
        minRate,
        maxRate,
        minAmount,
        maxAmount,
      ];
}

/// Filter criteria for Munda tasks
class MundaTaskFilterCriteria extends Equatable {
  final String? searchQuery;
  final MundaTaskStatus? status;
  final Priority? priority;
  final String? assignedTo;
  final DateTime? fromDate;
  final DateTime? toDate;
  final DateTime? dueBefore;
  final DateTime? dueAfter;
  final String? orderId;
  final String? productionBatchId;
  final List<String>? designNumbers;
  final bool? isOverdue;

  const MundaTaskFilterCriteria({
    this.searchQuery,
    this.status,
    this.priority,
    this.assignedTo,
    this.fromDate,
    this.toDate,
    this.dueBefore,
    this.dueAfter,
    this.orderId,
    this.productionBatchId,
    this.designNumbers,
    this.isOverdue,
  });

  /// Convert to query parameters
  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = {};
    
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      params['search'] = searchQuery;
    }
    if (status != null) params['status'] = status!.value;
    if (priority != null) params['priority'] = priority!.value;
    if (assignedTo != null) params['assignedTo'] = assignedTo;
    if (fromDate != null) params['fromDate'] = fromDate!.toIso8601String();
    if (toDate != null) params['toDate'] = toDate!.toIso8601String();
    if (dueBefore != null) params['dueBefore'] = dueBefore!.toIso8601String();
    if (dueAfter != null) params['dueAfter'] = dueAfter!.toIso8601String();
    if (orderId != null) params['orderId'] = orderId;
    if (productionBatchId != null) params['productionBatchId'] = productionBatchId;
    if (designNumbers != null) params['designNumbers'] = designNumbers;
    if (isOverdue != null) params['isOverdue'] = isOverdue;
    
    return params;
  }

  @override
  List<Object?> get props => [
        searchQuery,
        status,
        priority,
        assignedTo,
        fromDate,
        toDate,
        dueBefore,
        dueAfter,
        orderId,
        productionBatchId,
        designNumbers,
        isOverdue,
      ];
}
