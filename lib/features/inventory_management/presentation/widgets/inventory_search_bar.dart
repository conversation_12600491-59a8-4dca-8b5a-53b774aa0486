import 'package:flutter/material.dart';

/// Search bar widget for inventory management
class InventorySearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onScanPressed;
  final TextEditingController? controller;
  final bool showFilter;
  final bool showScan;

  const InventorySearchBar({
    Key? key,
    this.hintText = 'Search inventory items...',
    this.onSearchChanged,
    this.onFilterPressed,
    this.onScanPressed,
    this.controller,
    this.showFilter = true,
    this.showScan = true,
  }) : super(key: key);

  @override
  State<InventorySearchBar> createState() => _InventorySearchBarState();
}

class _InventorySearchBarState extends State<InventorySearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _controller,
              onChanged: widget.onSearchChanged,
              decoration: InputDecoration(
                hintText: widget.hintText,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _controller.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          widget.onSearchChanged?.call('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).primaryColor),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          if (widget.showScan) ...[
            const SizedBox(width: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.qr_code_scanner),
                onPressed: widget.onScanPressed,
                tooltip: 'Scan Barcode',
              ),
            ),
          ],
          if (widget.showFilter) ...[
            const SizedBox(width: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: widget.onFilterPressed,
                tooltip: 'Filter',
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Advanced search bar with quick filters for inventory
class InventoryAdvancedSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<ItemCategory?>? onCategoryFilterChanged;
  final ValueChanged<StockStatus?>? onStockStatusFilterChanged;
  final VoidCallback? onDateFilterPressed;
  final VoidCallback? onScanPressed;
  final TextEditingController? controller;
  final ItemCategory? selectedCategory;
  final StockStatus? selectedStockStatus;

  const InventoryAdvancedSearchBar({
    Key? key,
    this.hintText = 'Search by item name, code...',
    this.onSearchChanged,
    this.onCategoryFilterChanged,
    this.onStockStatusFilterChanged,
    this.onDateFilterPressed,
    this.onScanPressed,
    this.controller,
    this.selectedCategory,
    this.selectedStockStatus,
  }) : super(key: key);

  @override
  State<InventoryAdvancedSearchBar> createState() => _InventoryAdvancedSearchBarState();
}

class _InventoryAdvancedSearchBarState extends State<InventoryAdvancedSearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search field with scan button
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  onChanged: widget.onSearchChanged,
                  decoration: InputDecoration(
                    hintText: widget.hintText,
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _controller.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _controller.clear();
                              widget.onSearchChanged?.call('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Theme.of(context).primaryColor),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: const Icon(Icons.qr_code_scanner),
                  onPressed: widget.onScanPressed,
                  tooltip: 'Scan Barcode',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Quick filters
          Row(
            children: [
              // Category filter
              Expanded(
                child: DropdownButtonFormField<ItemCategory?>(
                  value: widget.selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<ItemCategory?>(
                      value: null,
                      child: Text('All Categories'),
                    ),
                    ...ItemCategory.values.map((category) => DropdownMenuItem(
                      value: category,
                      child: Text(category.displayName),
                    )),
                  ],
                  onChanged: widget.onCategoryFilterChanged,
                ),
              ),
              const SizedBox(width: 12),
              
              // Stock status filter
              Expanded(
                child: DropdownButtonFormField<StockStatus?>(
                  value: widget.selectedStockStatus,
                  decoration: InputDecoration(
                    labelText: 'Stock Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<StockStatus?>(
                      value: null,
                      child: Text('All Status'),
                    ),
                    ...StockStatus.values.map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(status.displayName),
                    )),
                  ],
                  onChanged: widget.onStockStatusFilterChanged,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Inventory search bar with stock level filter
class InventoryStockSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<RangeValues>? onStockRangeChanged;
  final TextEditingController? controller;
  final RangeValues stockRange;
  final double maxStock;

  const InventoryStockSearchBar({
    Key? key,
    this.hintText = 'Search inventory items...',
    this.onSearchChanged,
    this.onStockRangeChanged,
    this.controller,
    this.stockRange = const RangeValues(0, 1000),
    this.maxStock = 1000,
  }) : super(key: key);

  @override
  State<InventoryStockSearchBar> createState() => _InventoryStockSearchBarState();
}

class _InventoryStockSearchBarState extends State<InventoryStockSearchBar> {
  late TextEditingController _controller;
  late RangeValues _stockRange;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _stockRange = widget.stockRange;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search field
          TextField(
            controller: _controller,
            onChanged: widget.onSearchChanged,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _controller.clear();
                        widget.onSearchChanged?.call('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Theme.of(context).primaryColor),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Stock range filter
          Text(
            'Stock Level Range: ${_stockRange.start.round()} - ${_stockRange.end.round()}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          RangeSlider(
            values: _stockRange,
            min: 0,
            max: widget.maxStock,
            divisions: 20,
            labels: RangeLabels(
              _stockRange.start.round().toString(),
              _stockRange.end.round().toString(),
            ),
            onChanged: (values) {
              setState(() => _stockRange = values);
              widget.onStockRangeChanged?.call(values);
            },
          ),
        ],
      ),
    );
  }
}

// Placeholder enums - these should be defined in inventory_entities.dart
enum ItemCategory { rawMaterial, fabric, accessory, packaging, consumable, tool, equipment, spare }
enum StockStatus { inStock, lowStock, outOfStock, overstock }

extension ItemCategoryExtension on ItemCategory {
  String get displayName {
    switch (this) {
      case ItemCategory.rawMaterial:
        return 'Raw Material';
      case ItemCategory.fabric:
        return 'Fabric';
      case ItemCategory.accessory:
        return 'Accessory';
      case ItemCategory.packaging:
        return 'Packaging';
      case ItemCategory.consumable:
        return 'Consumable';
      case ItemCategory.tool:
        return 'Tool';
      case ItemCategory.equipment:
        return 'Equipment';
      case ItemCategory.spare:
        return 'Spare';
    }
  }
}

extension StockStatusExtension on StockStatus {
  String get displayName {
    switch (this) {
      case StockStatus.inStock:
        return 'In Stock';
      case StockStatus.lowStock:
        return 'Low Stock';
      case StockStatus.outOfStock:
        return 'Out of Stock';
      case StockStatus.overstock:
        return 'Overstock';
    }
  }
}