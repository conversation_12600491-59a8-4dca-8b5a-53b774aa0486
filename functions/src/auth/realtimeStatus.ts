import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { UserStatus, UserSession, UserActivityLog } from '../types/user.types';

const db = admin.firestore();
const auth = admin.auth();

/**
 * Real-time user presence tracking
 * Updates user's online status and last active time
 */
export const updateUserPresence = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;
  const { isOnline, deviceInfo } = data;

  try {
    const updateData: any = {
      lastActiveAt: admin.firestore.FieldValue.serverTimestamp(),
      'metadata.updatedAt': admin.firestore.FieldValue.serverTimestamp(),
    };

    if (isOnline !== undefined) {
      updateData.isOnline = isOnline;
    }

    // Update user presence
    await db.collection('users').doc(userId).update(updateData);

    // Update or create user session
    if (deviceInfo) {
      await updateUserSession(userId, deviceInfo, isOnline);
    }

    // Log activity
    await logUserActivity(userId, isOnline ? 'USER_ONLINE' : 'USER_OFFLINE', {
      deviceInfo,
      timestamp: new Date(),
    });

    return { success: true, timestamp: new Date() };
  } catch (error) {
    console.error('Error updating user presence:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update user presence');
  }
});

/**
 * Track user login sessions
 */
export const trackUserLogin = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;
  const { deviceInfo, ipAddress, location } = data;

  try {
    // Create new session
    const sessionId = generateSessionId();
    const sessionData: UserSession = {
      sessionId,
      userId,
      deviceId: deviceInfo.deviceId,
      deviceName: deviceInfo.deviceName,
      platform: deviceInfo.platform,
      appVersion: deviceInfo.appVersion,
      loginAt: new Date(),
      logoutAt: null,
      lastActiveAt: new Date(),
      ipAddress,
      location: location || null,
      isActive: true,
    };

    // Save session
    await db.collection('userSessions').doc(sessionId).set({
      ...sessionData,
      loginAt: admin.firestore.FieldValue.serverTimestamp(),
      lastActiveAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Update user's last login
    await db.collection('users').doc(userId).update({
      lastLoginAt: admin.firestore.FieldValue.serverTimestamp(),
      isOnline: true,
      currentSessionId: sessionId,
    });

    // Log login activity
    await logUserActivity(userId, 'USER_LOGIN', {
      sessionId,
      deviceInfo,
      ipAddress,
      location,
    });

    // Check for suspicious login activity
    await checkSuspiciousActivity(userId, ipAddress, location);

    return { success: true, sessionId };
  } catch (error) {
    console.error('Error tracking user login:', error);
    throw new functions.https.HttpsError('internal', 'Failed to track user login');
  }
});

/**
 * Track user logout
 */
export const trackUserLogout = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;
  const { sessionId } = data;

  try {
    // Update session
    if (sessionId) {
      await db.collection('userSessions').doc(sessionId).update({
        logoutAt: admin.firestore.FieldValue.serverTimestamp(),
        isActive: false,
      });
    }

    // Update user status
    await db.collection('users').doc(userId).update({
      isOnline: false,
      currentSessionId: null,
      lastActiveAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Log logout activity
    await logUserActivity(userId, 'USER_LOGOUT', {
      sessionId,
    });

    return { success: true };
  } catch (error) {
    console.error('Error tracking user logout:', error);
    throw new functions.https.HttpsError('internal', 'Failed to track user logout');
  }
});

/**
 * Get user's active sessions
 */
export const getUserSessions = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;
  const { includeInactive = false } = data;

  try {
    let query = db.collection('userSessions').where('userId', '==', userId);
    
    if (!includeInactive) {
      query = query.where('isActive', '==', true);
    }

    const sessionsSnapshot = await query.orderBy('loginAt', 'desc').get();
    const sessions = sessionsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    return { success: true, sessions };
  } catch (error) {
    console.error('Error getting user sessions:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get user sessions');
  }
});

/**
 * Terminate user session
 */
export const terminateUserSession = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const requestingUserId = context.auth.uid;
  const { sessionId, userId } = data;

  try {
    // Check if user can terminate this session
    const canTerminate = await canUserTerminateSession(requestingUserId, userId);
    if (!canTerminate) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to terminate this session');
    }

    // Update session
    await db.collection('userSessions').doc(sessionId).update({
      logoutAt: admin.firestore.FieldValue.serverTimestamp(),
      isActive: false,
      terminatedBy: requestingUserId,
    });

    // If this is the current session, update user status
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    
    if (userData && userData.currentSessionId === sessionId) {
      await db.collection('users').doc(userId).update({
        isOnline: false,
        currentSessionId: null,
      });
    }

    // Revoke refresh tokens for this user
    await auth.revokeRefreshTokens(userId);

    // Log session termination
    await logUserActivity(userId, 'SESSION_TERMINATED', {
      sessionId,
      terminatedBy: requestingUserId,
    });

    return { success: true };
  } catch (error) {
    console.error('Error terminating user session:', error);
    throw new functions.https.HttpsError('internal', 'Failed to terminate user session');
  }
});

/**
 * Monitor inactive users and update their status
 */
export const monitorInactiveUsers = functions.pubsub.schedule('every 5 minutes').onRun(async (context) => {
  try {
    const inactiveThreshold = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes
    const offlineThreshold = new Date(Date.now() - 5 * 60 * 1000); // 5 minutes

    // Find users who should be marked as offline
    const onlineUsersSnapshot = await db.collection('users')
      .where('isOnline', '==', true)
      .where('lastActiveAt', '<', offlineThreshold)
      .get();

    const batch = db.batch();
    
    onlineUsersSnapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        isOnline: false,
        'metadata.updatedAt': admin.firestore.FieldValue.serverTimestamp(),
      });
    });

    // Find inactive sessions
    const activeSessionsSnapshot = await db.collection('userSessions')
      .where('isActive', '==', true)
      .where('lastActiveAt', '<', inactiveThreshold)
      .get();

    activeSessionsSnapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        isActive: false,
        logoutAt: admin.firestore.FieldValue.serverTimestamp(),
      });
    });

    await batch.commit();

    console.log(`Updated ${onlineUsersSnapshot.size} users and ${activeSessionsSnapshot.size} sessions`);
  } catch (error) {
    console.error('Error monitoring inactive users:', error);
  }
});

/**
 * Clean up old sessions and activity logs
 */
export const cleanupOldData = functions.pubsub.schedule('every 24 hours').onRun(async (context) => {
  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

    // Delete old inactive sessions
    const oldSessionsSnapshot = await db.collection('userSessions')
      .where('isActive', '==', false)
      .where('logoutAt', '<', thirtyDaysAgo)
      .limit(500)
      .get();

    const sessionBatch = db.batch();
    oldSessionsSnapshot.docs.forEach(doc => {
      sessionBatch.delete(doc.ref);
    });
    await sessionBatch.commit();

    // Archive old activity logs
    const oldLogsSnapshot = await db.collection('userActivityLogs')
      .where('timestamp', '<', ninetyDaysAgo)
      .limit(500)
      .get();

    const logBatch = db.batch();
    oldLogsSnapshot.docs.forEach(doc => {
      // Move to archived collection
      logBatch.set(db.collection('archivedActivityLogs').doc(doc.id), doc.data());
      logBatch.delete(doc.ref);
    });
    await logBatch.commit();

    console.log(`Cleaned up ${oldSessionsSnapshot.size} sessions and ${oldLogsSnapshot.size} activity logs`);
  } catch (error) {
    console.error('Error cleaning up old data:', error);
  }
});

// Helper functions

async function updateUserSession(userId: string, deviceInfo: any, isOnline: boolean) {
  try {
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    
    if (userData && userData.currentSessionId) {
      await db.collection('userSessions').doc(userData.currentSessionId).update({
        lastActiveAt: admin.firestore.FieldValue.serverTimestamp(),
        isActive: isOnline,
      });
    }
  } catch (error) {
    console.error('Error updating user session:', error);
  }
}

async function logUserActivity(userId: string, action: string, details: any) {
  try {
    await db.collection('userActivityLogs').add({
      userId,
      action,
      details,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error('Error logging user activity:', error);
  }
}

async function checkSuspiciousActivity(userId: string, ipAddress: string, location: any) {
  try {
    // Check for logins from new locations
    const recentLoginsSnapshot = await db.collection('userActivityLogs')
      .where('userId', '==', userId)
      .where('action', '==', 'USER_LOGIN')
      .orderBy('timestamp', 'desc')
      .limit(10)
      .get();

    const recentLocations = recentLoginsSnapshot.docs
      .map(doc => doc.data().details?.location)
      .filter(loc => loc);

    if (location && recentLocations.length > 0) {
      const isNewLocation = !recentLocations.some(loc => 
        loc.country === location.country && loc.city === location.city
      );

      if (isNewLocation) {
        // Send security alert
        await sendSecurityAlert(userId, 'NEW_LOCATION_LOGIN', {
          ipAddress,
          location,
          timestamp: new Date(),
        });
      }
    }
  } catch (error) {
    console.error('Error checking suspicious activity:', error);
  }
}

async function canUserTerminateSession(requestingUserId: string, targetUserId: string): Promise<boolean> {
  // Users can terminate their own sessions
  if (requestingUserId === targetUserId) {
    return true;
  }

  // Check if requesting user is admin
  const requestingUserDoc = await db.collection('users').doc(requestingUserId).get();
  const requestingUserData = requestingUserDoc.data();
  
  return requestingUserData?.role === 'admin';
}

async function sendSecurityAlert(userId: string, alertType: string, details: any) {
  try {
    await db.collection('securityAlerts').add({
      userId,
      alertType,
      details,
      resolved: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Send notification to user
    await db.collection('userNotifications').add({
      userId,
      title: 'Security Alert',
      message: 'New login detected from an unrecognized location',
      type: 'warning',
      priority: 'high',
      read: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error('Error sending security alert:', error);
  }
}

function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
