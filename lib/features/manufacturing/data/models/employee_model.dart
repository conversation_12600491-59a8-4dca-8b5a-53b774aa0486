import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hm_collection/features/resource_management/domain/entities/facility_entities.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/employee.dart';

/// Employee data model for Firestore serialization
class EmployeeModel {
  final String id;
  final String employeeId;
  final String firstName;
  final String lastName;
  final String email;
  final String? phoneNumber;
  final String role;
  final String department;
  final String status;
  final Timestamp hireDate;
  final Timestamp? terminationDate;
  final String? profileImageUrl;
  final bool isActive;
  final String? supervisorId;
  final List<String> skills;
  final Map<String, dynamic> metadata;
  final double? hourlyRate;
  final String? emergencyContact;
  final String? address;
  final Timestamp createdAt;
  final Timestamp updatedAt;
  final Timestamp? deletedAt;
  final String? createdBy;
  final String? updatedBy;
  final String? deletedBy;
  final int version;

  const EmployeeModel({
    required this.id,
    required this.employeeId,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phoneNumber,
    required this.role,
    required this.department,
    required this.status,
    required this.hireDate,
    this.terminationDate,
    this.profileImageUrl,
    this.isActive = true,
    this.supervisorId,
    this.skills = const [],
    this.metadata = const {},
    this.hourlyRate,
    this.emergencyContact,
    this.address,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.deletedBy,
    this.version = 1,
  });

  /// Create from Firestore document
  factory EmployeeModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return EmployeeModel(
      id: doc.id,
      employeeId: data['employeeId'] ?? '',
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      email: data['email'] ?? '',
      phoneNumber: data['phoneNumber'],
      role: data['role'] ?? 'viewer',
      department: data['department'] ?? 'cutting',
      status: data['status'] ?? 'active',
      hireDate: data['hireDate'] ?? Timestamp.now(),
      terminationDate: data['terminationDate'],
      profileImageUrl: data['profileImageUrl'],
      isActive: data['isActive'] ?? true,
      supervisorId: data['supervisorId'],
      skills: List<String>.from(data['skills'] ?? []),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      hourlyRate: data['hourlyRate']?.toDouble(),
      emergencyContact: data['emergencyContact'],
      address: data['address'],
      createdAt: data['createdAt'] ?? Timestamp.now(),
      updatedAt: data['updatedAt'] ?? Timestamp.now(),
      deletedAt: data['deletedAt'],
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
      deletedBy: data['deletedBy'],
      version: data['version'] ?? 1,
    );
  }

  /// Create from Employee entity
  factory EmployeeModel.fromEntity(Employee employee) {
    return EmployeeModel(
      id: employee.id,
      employeeId: employee.employeeId,
      firstName: employee.firstName,
      lastName: employee.lastName,
      email: employee.email,
      phoneNumber: employee.phoneNumber,
      role: employee.role.value,
      department: employee.department.value,
      status: employee.status.value,
      hireDate: Timestamp.fromDate(employee.hireDate),
      terminationDate: employee.terminationDate != null 
          ? Timestamp.fromDate(employee.terminationDate!)
          : null,
      profileImageUrl: employee.profileImageUrl,
      isActive: employee.isActive,
      supervisorId: employee.supervisorId,
      skills: employee.skills,
      metadata: employee.metadata,
      hourlyRate: employee.hourlyRate,
      emergencyContact: employee.emergencyContact,
      address: employee.address,
      createdAt: Timestamp.fromDate(employee.createdAt),
      updatedAt: Timestamp.fromDate(employee.updatedAt),
      deletedAt: employee.deletedAt != null 
          ? Timestamp.fromDate(employee.deletedAt!)
          : null,
      createdBy: employee.createdBy,
      updatedBy: employee.updatedBy,
      deletedBy: employee.deletedBy,
      version: employee.version,
    );
  }

  /// Convert to Employee entity
  Employee toEntity() {
    return Employee(
      id: id,
      employeeId: employeeId,
      firstName: firstName,
      lastName: lastName,
      email: email,
      phoneNumber: phoneNumber,
      role: UserRole.values.firstWhere(
        (r) => r.value == role,
        orElse: () => UserRole.viewer,
      ),
      department: Department.values.firstWhere(
        (d) => d.value == department,
        orElse: () => Department.cutting,
      ),
      status: CommonStatus.values.firstWhere(
        (s) => s.value == status,
        orElse: () => CommonStatus.active,
      ),
      hireDate: hireDate.toDate(),
      terminationDate: terminationDate?.toDate(),
      profileImageUrl: profileImageUrl,
      isActive: isActive,
      supervisorId: supervisorId,
      skills: skills,
      metadata: metadata,
      hourlyRate: hourlyRate,
      emergencyContact: emergencyContact,
      address: address,
      createdAt: createdAt.toDate(),
      updatedAt: updatedAt.toDate(),
      deletedAt: deletedAt?.toDate(),
      createdBy: createdBy,
      updatedBy: updatedBy,
      deletedBy: deletedBy,
      version: version,
    );
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    return {
      'employeeId': employeeId,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phoneNumber': phoneNumber,
      'role': role,
      'department': department,
      'status': status,
      'hireDate': hireDate,
      'terminationDate': terminationDate,
      'profileImageUrl': profileImageUrl,
      'isActive': isActive,
      'supervisorId': supervisorId,
      'skills': skills,
      'metadata': metadata,
      'hourlyRate': hourlyRate,
      'emergencyContact': emergencyContact,
      'address': address,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'deletedAt': deletedAt,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'deletedBy': deletedBy,
      'version': version,
    };
  }

  /// Copy with new values
  EmployeeModel copyWith({
    String? employeeId,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? role,
    String? department,
    String? status,
    Timestamp? hireDate,
    Timestamp? terminationDate,
    String? profileImageUrl,
    bool? isActive,
    String? supervisorId,
    List<String>? skills,
    Map<String, dynamic>? metadata,
    double? hourlyRate,
    String? emergencyContact,
    String? address,
    Timestamp? updatedAt,
    String? updatedBy,
    int? version,
  }) {
    return EmployeeModel(
      id: id,
      employeeId: employeeId ?? this.employeeId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      department: department ?? this.department,
      status: status ?? this.status,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      supervisorId: supervisorId ?? this.supervisorId,
      skills: skills ?? this.skills,
      metadata: metadata ?? this.metadata,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      address: address ?? this.address,
      createdAt: createdAt,
      updatedAt: updatedAt ?? Timestamp.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      deletedBy: deletedBy,
      version: version ?? this.version + 1,
    );
  }

  @override
  String toString() {
    return 'EmployeeModel(id: $id, employeeId: $employeeId, firstName: $firstName, lastName: $lastName, role: $role, department: $department)';
  }
}

/// Employee search criteria for Firestore queries
class EmployeeSearchCriteria {
  final String? searchTerm;
  final UserRole? role;
  final Department? department;
  final CommonStatus? status;
  final bool? isActive;
  final String? supervisorId;
  final DateTime? hireDateFrom;
  final DateTime? hireDateTo;
  final List<String>? skills;
  final int limit;
  final DocumentSnapshot? lastDocument;

  const EmployeeSearchCriteria({
    this.searchTerm,
    this.role,
    this.department,
    this.status,
    this.isActive,
    this.supervisorId,
    this.hireDateFrom,
    this.hireDateTo,
    this.skills,
    this.limit = 20,
    this.lastDocument,
  });

  /// Copy with new values
  EmployeeSearchCriteria copyWith({
    String? searchTerm,
    UserRole? role,
    Department? department,
    CommonStatus? status,
    bool? isActive,
    String? supervisorId,
    DateTime? hireDateFrom,
    DateTime? hireDateTo,
    List<String>? skills,
    int? limit,
    DocumentSnapshot? lastDocument,
  }) {
    return EmployeeSearchCriteria(
      searchTerm: searchTerm ?? this.searchTerm,
      role: role ?? this.role,
      department: department ?? this.department,
      status: status ?? this.status,
      isActive: isActive ?? this.isActive,
      supervisorId: supervisorId ?? this.supervisorId,
      hireDateFrom: hireDateFrom ?? this.hireDateFrom,
      hireDateTo: hireDateTo ?? this.hireDateTo,
      skills: skills ?? this.skills,
      limit: limit ?? this.limit,
      lastDocument: lastDocument ?? this.lastDocument,
    );
  }
}
