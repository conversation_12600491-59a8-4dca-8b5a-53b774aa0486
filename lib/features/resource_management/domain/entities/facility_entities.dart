import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Facility entity
class Facility extends BaseEntity {
  final String facilityCode;
  final String facilityName;
  final String description;
  final FacilityType type;
  final FacilityStatus status;
  final String address;
  final String city;
  final String state;
  final String country;
  final String postalCode;
  final double? latitude;
  final double? longitude;
  final FacilitySpecifications specifications;
  final List<String> departmentIds;
  final List<String> managerIds;
  final FacilityCapacity capacity;
  final FacilityUtilities utilities;
  final List<String> certifications;
  final DateTime? lastInspectionDate;
  final DateTime? nextInspectionDate;
  final List<String> attachments;
  final Map<String, dynamic> metadata;

  const Facility({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.facilityCode,
    required this.facilityName,
    required this.description,
    required this.type,
    required this.status,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    this.latitude,
    this.longitude,
    required this.specifications,
    this.departmentIds = const [],
    this.managerIds = const [],
    required this.capacity,
    required this.utilities,
    this.certifications = const [],
    this.lastInspectionDate,
    this.nextInspectionDate,
    this.attachments = const [],
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        facilityCode,
        facilityName,
        description,
        type,
        status,
        address,
        city,
        state,
        country,
        postalCode,
        latitude,
        longitude,
        specifications,
        departmentIds,
        managerIds,
        capacity,
        utilities,
        certifications,
        lastInspectionDate,
        nextInspectionDate,
        attachments,
        metadata,
      ];

  /// Check if facility is operational
  bool get isOperational => status == FacilityStatus.operational;

  /// Check if facility is under maintenance
  bool get isUnderMaintenance => status == FacilityStatus.maintenance;

  /// Check if inspection is due
  bool get isInspectionDue {
    if (nextInspectionDate == null) return false;
    return DateTime.now().isAfter(nextInspectionDate!);
  }

  /// Check if inspection is due soon
  bool get isInspectionDueSoon {
    if (nextInspectionDate == null) return false;
    final daysUntilInspection = nextInspectionDate!.difference(DateTime.now()).inDays;
    return daysUntilInspection <= 30 && daysUntilInspection > 0;
  }

  /// Get full address
  String get fullAddress => '$address, $city, $state $postalCode, $country';

  /// Get days until next inspection
  int? get daysUntilInspection {
    if (nextInspectionDate == null) return null;
    return nextInspectionDate!.difference(DateTime.now()).inDays;
  }
}

/// Facility specifications entity
class FacilitySpecifications extends Equatable {
  final double totalArea; // in square meters
  final double productionArea;
  final double storageArea;
  final double officeArea;
  final int numberOfFloors;
  final double ceilingHeight; // in meters
  final int maxOccupancy;
  final String buildingType;
  final int yearBuilt;
  final Map<String, dynamic> technicalSpecs;

  const FacilitySpecifications({
    required this.totalArea,
    required this.productionArea,
    required this.storageArea,
    required this.officeArea,
    this.numberOfFloors = 1,
    required this.ceilingHeight,
    required this.maxOccupancy,
    required this.buildingType,
    required this.yearBuilt,
    this.technicalSpecs = const {},
  });

  @override
  List<Object?> get props => [
        totalArea,
        productionArea,
        storageArea,
        officeArea,
        numberOfFloors,
        ceilingHeight,
        maxOccupancy,
        buildingType,
        yearBuilt,
        technicalSpecs,
      ];

  /// Get utilization percentage
  double get utilizationPercentage {
    final usedArea = productionArea + storageArea + officeArea;
    return (usedArea / totalArea) * 100;
  }
}

/// Facility capacity entity
class FacilityCapacity extends Equatable {
  final int maxWorkers;
  final int currentWorkers;
  final int maxMachines;
  final int currentMachines;
  final double maxProductionCapacity; // units per day
  final double currentProductionCapacity;
  final String capacityUnit;
  final Map<String, double> capacityByProduct;

  const FacilityCapacity({
    required this.maxWorkers,
    this.currentWorkers = 0,
    required this.maxMachines,
    this.currentMachines = 0,
    required this.maxProductionCapacity,
    this.currentProductionCapacity = 0.0,
    required this.capacityUnit,
    this.capacityByProduct = const {},
  });

  @override
  List<Object?> get props => [
        maxWorkers,
        currentWorkers,
        maxMachines,
        currentMachines,
        maxProductionCapacity,
        currentProductionCapacity,
        capacityUnit,
        capacityByProduct,
      ];

  /// Get worker utilization percentage
  double get workerUtilization {
    if (maxWorkers == 0) return 0.0;
    return (currentWorkers / maxWorkers) * 100;
  }

  /// Get machine utilization percentage
  double get machineUtilization {
    if (maxMachines == 0) return 0.0;
    return (currentMachines / maxMachines) * 100;
  }

  /// Get production utilization percentage
  double get productionUtilization {
    if (maxProductionCapacity == 0) return 0.0;
    return (currentProductionCapacity / maxProductionCapacity) * 100;
  }
}

/// Facility utilities entity
class FacilityUtilities extends Equatable {
  final ElectricalSystem electrical;
  final WaterSystem water;
  final HVACSystem hvac;
  final SafetySystem safety;
  final SecuritySystem security;
  final CommunicationSystem communication;

  const FacilityUtilities({
    required this.electrical,
    required this.water,
    required this.hvac,
    required this.safety,
    required this.security,
    required this.communication,
  });

  @override
  List<Object?> get props => [electrical, water, hvac, safety, security, communication];
}

/// Electrical system entity
class ElectricalSystem extends Equatable {
  final double totalCapacity; // in kW
  final double currentLoad;
  final String voltage;
  final int numberOfPhases;
  final bool hasBackupPower;
  final String? backupPowerType;
  final double? backupCapacity;
  final DateTime? lastInspection;
  final DateTime? nextInspection;

  const ElectricalSystem({
    required this.totalCapacity,
    this.currentLoad = 0.0,
    required this.voltage,
    this.numberOfPhases = 3,
    this.hasBackupPower = false,
    this.backupPowerType,
    this.backupCapacity,
    this.lastInspection,
    this.nextInspection,
  });

  @override
  List<Object?> get props => [
        totalCapacity,
        currentLoad,
        voltage,
        numberOfPhases,
        hasBackupPower,
        backupPowerType,
        backupCapacity,
        lastInspection,
        nextInspection,
      ];

  /// Get load utilization percentage
  double get loadUtilization {
    if (totalCapacity == 0) return 0.0;
    return (currentLoad / totalCapacity) * 100;
  }
}

/// Water system entity
class WaterSystem extends Equatable {
  final double dailyCapacity; // in liters
  final double currentUsage;
  final String waterSource;
  final bool hasWaterTreatment;
  final bool hasWasteWaterTreatment;
  final DateTime? lastQualityTest;
  final DateTime? nextQualityTest;

  const WaterSystem({
    required this.dailyCapacity,
    this.currentUsage = 0.0,
    required this.waterSource,
    this.hasWaterTreatment = false,
    this.hasWasteWaterTreatment = false,
    this.lastQualityTest,
    this.nextQualityTest,
  });

  @override
  List<Object?> get props => [
        dailyCapacity,
        currentUsage,
        waterSource,
        hasWaterTreatment,
        hasWasteWaterTreatment,
        lastQualityTest,
        nextQualityTest,
      ];

  /// Get usage percentage
  double get usagePercentage {
    if (dailyCapacity == 0) return 0.0;
    return (currentUsage / dailyCapacity) * 100;
  }
}

/// HVAC system entity
class HVACSystem extends Equatable {
  final double coolingCapacity; // in tons
  final double heatingCapacity; // in kW
  final String ventilationType;
  final double airChangeRate; // per hour
  final bool hasAirFiltration;
  final String? filterType;
  final DateTime? lastMaintenance;
  final DateTime? nextMaintenance;

  const HVACSystem({
    required this.coolingCapacity,
    required this.heatingCapacity,
    required this.ventilationType,
    required this.airChangeRate,
    this.hasAirFiltration = false,
    this.filterType,
    this.lastMaintenance,
    this.nextMaintenance,
  });

  @override
  List<Object?> get props => [
        coolingCapacity,
        heatingCapacity,
        ventilationType,
        airChangeRate,
        hasAirFiltration,
        filterType,
        lastMaintenance,
        nextMaintenance,
      ];
}

/// Safety system entity
class SafetySystem extends Equatable {
  final bool hasFireDetection;
  final bool hasFireSuppression;
  final bool hasEmergencyLighting;
  final bool hasEmergencyExits;
  final int numberOfExits;
  final bool hasFirstAidStations;
  final bool hasEyewashStations;
  final DateTime? lastSafetyInspection;
  final DateTime? nextSafetyInspection;

  const SafetySystem({
    this.hasFireDetection = false,
    this.hasFireSuppression = false,
    this.hasEmergencyLighting = false,
    this.hasEmergencyExits = false,
    this.numberOfExits = 0,
    this.hasFirstAidStations = false,
    this.hasEyewashStations = false,
    this.lastSafetyInspection,
    this.nextSafetyInspection,
  });

  @override
  List<Object?> get props => [
        hasFireDetection,
        hasFireSuppression,
        hasEmergencyLighting,
        hasEmergencyExits,
        numberOfExits,
        hasFirstAidStations,
        hasEyewashStations,
        lastSafetyInspection,
        nextSafetyInspection,
      ];
}

/// Security system entity
class SecuritySystem extends Equatable {
  final bool hasAccessControl;
  final bool hasCCTV;
  final bool hasAlarmSystem;
  final bool hasSecurityGuards;
  final int numberOfCameras;
  final int numberOfAccessPoints;
  final DateTime? lastSecurityAudit;
  final DateTime? nextSecurityAudit;

  const SecuritySystem({
    this.hasAccessControl = false,
    this.hasCCTV = false,
    this.hasAlarmSystem = false,
    this.hasSecurityGuards = false,
    this.numberOfCameras = 0,
    this.numberOfAccessPoints = 0,
    this.lastSecurityAudit,
    this.nextSecurityAudit,
  });

  @override
  List<Object?> get props => [
        hasAccessControl,
        hasCCTV,
        hasAlarmSystem,
        hasSecurityGuards,
        numberOfCameras,
        numberOfAccessPoints,
        lastSecurityAudit,
        nextSecurityAudit,
      ];
}

/// Communication system entity
class CommunicationSystem extends Equatable {
  final bool hasInternetConnection;
  final double internetBandwidth; // in Mbps
  final bool hasInternalNetwork;
  final bool hasWiFi;
  final bool hasPhoneSystem;
  final bool hasPASystem;
  final DateTime? lastNetworkAudit;
  final DateTime? nextNetworkAudit;

  const CommunicationSystem({
    this.hasInternetConnection = false,
    this.internetBandwidth = 0.0,
    this.hasInternalNetwork = false,
    this.hasWiFi = false,
    this.hasPhoneSystem = false,
    this.hasPASystem = false,
    this.lastNetworkAudit,
    this.nextNetworkAudit,
  });

  @override
  List<Object?> get props => [
        hasInternetConnection,
        internetBandwidth,
        hasInternalNetwork,
        hasWiFi,
        hasPhoneSystem,
        hasPASystem,
        lastNetworkAudit,
        nextNetworkAudit,
      ];
}

/// Operating hours for a department
class OperatingHours extends Equatable {
  final String startTime;
  final String endTime;
  final int breakDuration; // in minutes
  final bool isActive;

  const OperatingHours({
    required this.startTime,
    required this.endTime,
    this.breakDuration = 60,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [
        startTime,
        endTime,
        breakDuration,
        isActive,
      ];

  /// Check if the department is currently open based on the current time
  bool get isOpen {
    if (!isActive) return false;
    
    final now = TimeOfDay.now();
    final start = _parseTime(startTime);
    final end = _parseTime(endTime);
    
    if (start == null || end == null) return false;
    
    final nowInMinutes = now.hour * 60 + now.minute;
    final startInMinutes = start.hour * 60 + start.minute;
    final endInMinutes = end.hour * 60 + end.minute;
    
    return nowInMinutes >= startInMinutes && nowInMinutes <= endInMinutes;
  }
  
  TimeOfDay? _parseTime(String time) {
    try {
      final parts = time.split(':');
      return TimeOfDay(
        hour: int.parse(parts[0]),
        minute: int.parse(parts[1]),
      );
    } catch (e) {
      return null;
    }
  }
}

/// Department entity
class ResourceDepartment extends BaseEntity {
  final String departmentCode;
  final String departmentName;
  final String description;
  final DepartmentType type;
  final CommonStatus status;
  final String facilityId;
  final String facilityName;
  final String? managerId;
  final String? managerName;
  final String? supervisorId;
  final String? supervisorName;
  final String? locationId;
  final String? locationName;
  final DepartmentCapacity capacity;
  final List<String> workerIds;
  final List<String> machineIds;
  final List<String> skillsRequired;
  final double budgetAllocated;
  final double budgetUsed;
  final Map<String, dynamic> metadata;
  final OperatingHours operatingHours;

  const ResourceDepartment({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.departmentCode,
    required this.departmentName,
    required this.description,
    required this.type,
    required this.status,
    required this.facilityId,
    required this.facilityName,
    this.managerId,
    this.managerName,
    this.supervisorId,
    this.supervisorName,
    this.locationId,
    this.locationName,
    required this.capacity,
    this.workerIds = const [],
    this.machineIds = const [],
    this.skillsRequired = const [],
    this.budgetAllocated = 0.0,
    this.budgetUsed = 0.0,
    this.metadata = const {},
    this.operatingHours = const OperatingHours(
      startTime: '09:00',
      endTime: '17:00',
      breakDuration: 60,
      isActive: true,
    ),
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        departmentCode,
        departmentName,
        description,
        type,
        status,
        facilityId,
        facilityName,
        managerId,
        managerName,
        supervisorId,
        supervisorName,
        locationId,
        locationName,
        capacity,
        workerIds,
        machineIds,
        skillsRequired,
        budgetAllocated,
        budgetUsed,
        metadata,
        operatingHours,
      ];

  /// Check if department is active
  bool get isActive => status == CommonStatus.active;

  /// Get worker count
  int get workerCount => workerIds.length;

  /// Get machine count
  int get machineCount => machineIds.length;

  /// Get budget utilization percentage
  double get budgetUtilization {
    if (budgetAllocated == 0) return 0.0;
    return (budgetUsed / budgetAllocated) * 100;
  }

  /// Get remaining budget
  double get remainingBudget => budgetAllocated - budgetUsed;
}

/// Department capacity entity
class DepartmentCapacity extends Equatable {
  final int maxWorkers;
  final int currentWorkers;
  final int maxMachines;
  final int currentMachines;
  final double maxProductionCapacity;
  final double currentProductionCapacity;
  final String capacityUnit;

  const DepartmentCapacity({
    required this.maxWorkers,
    this.currentWorkers = 0,
    required this.maxMachines,
    this.currentMachines = 0,
    required this.maxProductionCapacity,
    this.currentProductionCapacity = 0.0,
    required this.capacityUnit,
  });

  @override
  List<Object?> get props => [
        maxWorkers,
        currentWorkers,
        maxMachines,
        currentMachines,
        maxProductionCapacity,
        currentProductionCapacity,
        capacityUnit,
      ];

  /// Get worker utilization percentage
  double get workerUtilization {
    if (maxWorkers == 0) return 0.0;
    return (currentWorkers / maxWorkers) * 100;
  }

  /// Get machine utilization percentage
  double get machineUtilization {
    if (maxMachines == 0) return 0.0;
    return (currentMachines / maxMachines) * 100;
  }

  /// Get production utilization percentage
  double get productionUtilization {
    if (maxProductionCapacity == 0) return 0.0;
    return (currentProductionCapacity / maxProductionCapacity) * 100;
  }
}

// Enums

/// Facility type enum
enum FacilityType {
  manufacturing,
  warehouse,
  office,
  mixed,
}

/// Facility type extension
extension FacilityTypeExtension on FacilityType {
  String get displayName {
    switch (this) {
      case FacilityType.manufacturing:
        return 'Manufacturing Facility';
      case FacilityType.warehouse:
        return 'Warehouse';
      case FacilityType.office:
        return 'Office';
      case FacilityType.mixed:
        return 'Mixed Use';
    }
  }

  String get value => name;
}

/// Facility status enum
enum FacilityStatus {
  operational,
  maintenance,
  construction,
  closed,
  decommissioned,
}

/// Facility status extension
extension FacilityStatusExtension on FacilityStatus {
  String get displayName {
    switch (this) {
      case FacilityStatus.operational:
        return 'Operational';
      case FacilityStatus.maintenance:
        return 'Under Maintenance';
      case FacilityStatus.construction:
        return 'Under Construction';
      case FacilityStatus.closed:
        return 'Closed';
      case FacilityStatus.decommissioned:
        return 'Decommissioned';
    }
  }

  String get value => name;

  bool get isOperational => this == FacilityStatus.operational;
}

/// Department type enum
enum DepartmentType {
  /// Fabric department
  fabric,
  
  /// Stitching department
  stitching,
  
  /// Quality control department
  quality,
  
  /// Packing department
  packing,
  
  /// Finishing department
  finishing,
  
  /// Cutting department
  cutting,
  
  /// Production department
  production,
  
  /// Maintenance department
  maintenance,
  
  /// Warehouse department
  warehouse,
  
  /// Administration department
  administration,
  
  /// Research and development department
  research, other,
}

/// Department type extension
extension DepartmentTypeExtension on DepartmentType {
  String get displayName {
    switch (this) {
      case DepartmentType.fabric:
        return 'Fabric';
      case DepartmentType.cutting:
        return 'Cutting';
      case DepartmentType.stitching:
        return 'Stitching';
      case DepartmentType.quality:
        return 'Quality Control';
      case DepartmentType.production:
        return 'Production';
      case DepartmentType.maintenance:
        return 'Maintenance';
      case DepartmentType.warehouse:
        return 'Warehouse';
      case DepartmentType.other:
        return 'Other';
      case DepartmentType.administration:
        return 'Administration';
      case DepartmentType.research:
        return 'Research & Development';
      case DepartmentType.packing:
        // TODO: Handle this case.
        throw UnimplementedError();
      case DepartmentType.finishing:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }

  String get value => name;
}

// Request classes

class CreateFacilityRequest extends Equatable {
  final String facilityCode;
  final String facilityName;
  final String description;
  final FacilityType type;
  final String address;
  final String city;
  final String state;
  final String country;
  final String postalCode;

  const CreateFacilityRequest({
    required this.facilityCode,
    required this.facilityName,
    required this.description,
    required this.type,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
  });

  @override
  List<Object?> get props => [
        facilityCode,
        facilityName,
        description,
        type,
        address,
        city,
        state,
        country,
        postalCode,
      ];
}

class UpdateFacilityRequest extends Equatable {
  final String id;
  final String? facilityCode;
  final String? facilityName;
  final String? description;
  final FacilityType? type;
  final FacilityStatus? status;
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;

  const UpdateFacilityRequest({
    required this.id,
    this.facilityCode,
    this.facilityName,
    this.description,
    this.type,
    this.status,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  @override
  List<Object?> get props => [
        id,
        facilityCode,
        facilityName,
        description,
        type,
        status,
        address,
        city,
        state,
        country,
        postalCode,
      ];
}

class CreateDepartmentRequest extends Equatable {
  final String departmentCode;
  final String departmentName;
  final String description;
  final DepartmentType type;
  final String facilityId;

  const CreateDepartmentRequest({
    required this.departmentCode,
    required this.departmentName,
    required this.description,
    required this.type,
    required this.facilityId,
  });

  @override
  List<Object?> get props =>
      [departmentCode, departmentName, description, type, facilityId];
}

// Report classes

class FacilityCapacityReport extends Equatable {
  final String facilityId;
  final String facilityName;
  final DateTime reportDate;
  final int maxWorkers;
  final int currentWorkers;
  final int maxMachines;
  final int currentMachines;
  final double maxProductionCapacity;
  final double currentProductionCapacity;
  final String capacityUnit;

  const FacilityCapacityReport({
    required this.facilityId,
    required this.facilityName,
    required this.reportDate,
    required this.maxWorkers,
    required this.currentWorkers,
    required this.maxMachines,
    required this.currentMachines,
    required this.maxProductionCapacity,
    required this.currentProductionCapacity,
    required this.capacityUnit,
  });

  @override
  List<Object?> get props => [
        facilityId,
        facilityName,
        reportDate,
        maxWorkers,
        currentWorkers,
        maxMachines,
        currentMachines,
        maxProductionCapacity,
        currentProductionCapacity,
        capacityUnit,
      ];
}

class FacilityUtilizationReport extends Equatable {
  final String facilityId;
  final String facilityName;
  final DateTime startDate;
  final DateTime endDate;
  final double averageWorkerUtilization;
  final double averageMachineUtilization;
  final double averageProductionUtilization;
  final Map<String, double> utilizationByDepartment;

  const FacilityUtilizationReport({
    required this.facilityId,
    required this.facilityName,
    required this.startDate,
    required this.endDate,
    required this.averageWorkerUtilization,
    required this.averageMachineUtilization,
    required this.averageProductionUtilization,
    this.utilizationByDepartment = const {},
  });

  @override
  List<Object?> get props => [
        facilityId,
        facilityName,
        startDate,
        endDate,
        averageWorkerUtilization,
        averageMachineUtilization,
        averageProductionUtilization,
        utilizationByDepartment,
      ];
}
