import 'package:equatable/equatable.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import 'manufacturing_task_entities.dart';

/// Manufacturing Task Analytics entity
class ManufacturingTaskAnalytics extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final int totalOrders;
  final int completedOrders;
  final int inProgressOrders;
  final int overdueOrders;
  final double averageCompletionTime;
  final double overallEfficiency;
  final double qualityScore;
  final double onTimeDeliveryRate;
  final List<StageAnalytics> stageAnalytics;
  final List<DepartmentAnalytics> departmentAnalytics;
  final List<TrendData> efficiencyTrend;
  final List<TrendData> qualityTrend;
  final List<TrendData> throughputTrend;

  const ManufacturingTaskAnalytics({
    required this.startDate,
    required this.endDate,
    required this.totalOrders,
    required this.completedOrders,
    required this.inProgressOrders,
    required this.overdueOrders,
    required this.averageCompletionTime,
    required this.overallEfficiency,
    required this.qualityScore,
    required this.onTimeDeliveryRate,
    required this.stageAnalytics,
    required this.departmentAnalytics,
    required this.efficiencyTrend,
    required this.qualityTrend,
    required this.throughputTrend,
  });

  @override
  List<Object?> get props => [
        startDate,
        endDate,
        totalOrders,
        completedOrders,
        inProgressOrders,
        overdueOrders,
        averageCompletionTime,
        overallEfficiency,
        qualityScore,
        onTimeDeliveryRate,
        stageAnalytics,
        departmentAnalytics,
        efficiencyTrend,
        qualityTrend,
        throughputTrend,
      ];

  /// Calculate completion rate
  double get completionRate {
    if (totalOrders == 0) return 0.0;
    return (completedOrders / totalOrders) * 100;
  }

  /// Calculate overdue rate
  double get overdueRate {
    if (totalOrders == 0) return 0.0;
    return (overdueOrders / totalOrders) * 100;
  }
}

/// Stage Analytics entity
class StageAnalytics extends Equatable {
  final ManufacturingStage stage;
  final int totalTasks;
  final int completedTasks;
  final int inProgressTasks;
  final int overdueTasks;
  final double averageProcessingTime;
  final double efficiency;
  final double qualityScore;
  final int bottleneckScore;
  final List<String> commonIssues;

  const StageAnalytics({
    required this.stage,
    required this.totalTasks,
    required this.completedTasks,
    required this.inProgressTasks,
    required this.overdueTasks,
    required this.averageProcessingTime,
    required this.efficiency,
    required this.qualityScore,
    required this.bottleneckScore,
    required this.commonIssues,
  });

  @override
  List<Object?> get props => [
        stage,
        totalTasks,
        completedTasks,
        inProgressTasks,
        overdueTasks,
        averageProcessingTime,
        efficiency,
        qualityScore,
        bottleneckScore,
        commonIssues,
      ];

  /// Calculate completion rate
  double get completionRate {
    if (totalTasks == 0) return 0.0;
    return (completedTasks / totalTasks) * 100;
  }

  /// Check if this stage is a bottleneck
  bool get isBottleneck => bottleneckScore > 70;
}

/// Department Analytics entity
class DepartmentAnalytics extends Equatable {
  final DepartmentType department;
  final int totalTasks;
  final int completedTasks;
  final int inProgressTasks;
  final int overdueTasks;
  final double averageProcessingTime;
  final double efficiency;
  final double qualityScore;
  final double resourceUtilization;
  final int activeWorkers;
  final List<ManufacturingStage> stages;

  const DepartmentAnalytics({
    required this.department,
    required this.totalTasks,
    required this.completedTasks,
    required this.inProgressTasks,
    required this.overdueTasks,
    required this.averageProcessingTime,
    required this.efficiency,
    required this.qualityScore,
    required this.resourceUtilization,
    required this.activeWorkers,
    required this.stages,
  });

  @override
  List<Object?> get props => [
        department,
        totalTasks,
        completedTasks,
        inProgressTasks,
        overdueTasks,
        averageProcessingTime,
        efficiency,
        qualityScore,
        resourceUtilization,
        activeWorkers,
        stages,
      ];

  /// Calculate completion rate
  double get completionRate {
    if (totalTasks == 0) return 0.0;
    return (completedTasks / totalTasks) * 100;
  }

  /// Calculate tasks per worker
  double get tasksPerWorker {
    if (activeWorkers == 0) return 0.0;
    return totalTasks / activeWorkers;
  }
}

/// Trend Data entity
class TrendData extends Equatable {
  final DateTime date;
  final double value;
  final String label;

  const TrendData({
    required this.date,
    required this.value,
    required this.label,
  });

  @override
  List<Object?> get props => [date, value, label];
}

/// Stage Performance Metrics entity
class StagePerformanceMetrics extends Equatable {
  final ManufacturingStage stage;
  final DepartmentType department;
  final double averageProcessingTime;
  final double plannedProcessingTime;
  final double efficiency;
  final double qualityScore;
  final int totalTasks;
  final int completedTasks;
  final int overdueTasks;
  final double throughput;
  final List<PerformanceIssue> issues;

  const StagePerformanceMetrics({
    required this.stage,
    required this.department,
    required this.averageProcessingTime,
    required this.plannedProcessingTime,
    required this.efficiency,
    required this.qualityScore,
    required this.totalTasks,
    required this.completedTasks,
    required this.overdueTasks,
    required this.throughput,
    required this.issues,
  });

  @override
  List<Object?> get props => [
        stage,
        department,
        averageProcessingTime,
        plannedProcessingTime,
        efficiency,
        qualityScore,
        totalTasks,
        completedTasks,
        overdueTasks,
        throughput,
        issues,
      ];

  /// Calculate time variance
  double get timeVariance {
    if (plannedProcessingTime == 0) return 0.0;
    return ((averageProcessingTime - plannedProcessingTime) / plannedProcessingTime) * 100;
  }

  /// Check if performance is below threshold
  bool get isBelowThreshold => efficiency < 80.0 || qualityScore < 90.0;
}

/// Performance Issue entity
class PerformanceIssue extends Equatable {
  final String issue;
  final String description;
  final IssueSeverity severity;
  final int frequency;
  final List<String> suggestedActions;

  const PerformanceIssue({
    required this.issue,
    required this.description,
    required this.severity,
    required this.frequency,
    required this.suggestedActions,
  });

  @override
  List<Object?> get props => [issue, description, severity, frequency, suggestedActions];
}

/// Manufacturing Bottleneck entity
class ManufacturingBottleneck extends Equatable {
  final ManufacturingStage stage;
  final DepartmentType department;
  final String description;
  final BottleneckSeverity severity;
  final double impactScore;
  final int affectedOrders;
  final double averageDelay;
  final List<String> causes;
  final List<String> recommendations;
  final DateTime identifiedAt;

  const ManufacturingBottleneck({
    required this.stage,
    required this.department,
    required this.description,
    required this.severity,
    required this.impactScore,
    required this.affectedOrders,
    required this.averageDelay,
    required this.causes,
    required this.recommendations,
    required this.identifiedAt,
  });

  @override
  List<Object?> get props => [
        stage,
        department,
        description,
        severity,
        impactScore,
        affectedOrders,
        averageDelay,
        causes,
        recommendations,
        identifiedAt,
      ];

  /// Check if bottleneck is critical
  bool get isCritical => severity == BottleneckSeverity.critical;
}

/// Manufacturing Efficiency Metrics entity
class ManufacturingEfficiencyMetrics extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final double overallEfficiency;
  final double plannedEfficiency;
  final double actualThroughput;
  final double plannedThroughput;
  final double resourceUtilization;
  final double qualityScore;
  final double onTimeDeliveryRate;
  final List<EfficiencyByStage> stageEfficiencies;
  final List<EfficiencyByDepartment> departmentEfficiencies;
  final List<EfficiencyTrend> trends;

  const ManufacturingEfficiencyMetrics({
    required this.startDate,
    required this.endDate,
    required this.overallEfficiency,
    required this.plannedEfficiency,
    required this.actualThroughput,
    required this.plannedThroughput,
    required this.resourceUtilization,
    required this.qualityScore,
    required this.onTimeDeliveryRate,
    required this.stageEfficiencies,
    required this.departmentEfficiencies,
    required this.trends,
  });

  @override
  List<Object?> get props => [
        startDate,
        endDate,
        overallEfficiency,
        plannedEfficiency,
        actualThroughput,
        plannedThroughput,
        resourceUtilization,
        qualityScore,
        onTimeDeliveryRate,
        stageEfficiencies,
        departmentEfficiencies,
        trends,
      ];

  /// Calculate efficiency variance
  double get efficiencyVariance {
    if (plannedEfficiency == 0) return 0.0;
    return ((overallEfficiency - plannedEfficiency) / plannedEfficiency) * 100;
  }

  /// Calculate throughput variance
  double get throughputVariance {
    if (plannedThroughput == 0) return 0.0;
    return ((actualThroughput - plannedThroughput) / plannedThroughput) * 100;
  }
}

/// Efficiency by Stage entity
class EfficiencyByStage extends Equatable {
  final ManufacturingStage stage;
  final double efficiency;
  final double plannedEfficiency;
  final double throughput;
  final double qualityScore;

  const EfficiencyByStage({
    required this.stage,
    required this.efficiency,
    required this.plannedEfficiency,
    required this.throughput,
    required this.qualityScore,
  });

  @override
  List<Object?> get props => [stage, efficiency, plannedEfficiency, throughput, qualityScore];
}

/// Efficiency by Department entity
class EfficiencyByDepartment extends Equatable {
  final DepartmentType department;
  final double efficiency;
  final double plannedEfficiency;
  final double resourceUtilization;
  final double qualityScore;

  const EfficiencyByDepartment({
    required this.department,
    required this.efficiency,
    required this.plannedEfficiency,
    required this.resourceUtilization,
    required this.qualityScore,
  });

  @override
  List<Object?> get props => [department, efficiency, plannedEfficiency, resourceUtilization, qualityScore];
}

/// Efficiency Trend entity
class EfficiencyTrend extends Equatable {
  final DateTime date;
  final double efficiency;
  final double throughput;
  final double qualityScore;

  const EfficiencyTrend({
    required this.date,
    required this.efficiency,
    required this.throughput,
    required this.qualityScore,
  });

  @override
  List<Object?> get props => [date, efficiency, throughput, qualityScore];
}

// Enums

/// Issue Severity enum
enum IssueSeverity {
  low,
  medium,
  high,
  critical,
}

/// Bottleneck Severity enum
enum BottleneckSeverity {
  minor,
  moderate,
  major,
  critical,
}
