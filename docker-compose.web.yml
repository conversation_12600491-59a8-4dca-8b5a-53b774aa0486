version: '3.8'

services:
  # HM Collection Web Application
  hm-collection-web:
    build:
      context: .
      dockerfile: Dockerfile.web
      args:
        - BUILD_DATE=${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        - VCS_REF=${VCS_REF:-$(git rev-parse --short HEAD)}
    image: hm-collection-web:latest
    container_name: hm-collection-web
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    volumes:
      # Mount custom nginx configuration if needed
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/default.conf:/etc/nginx/conf.d/default.conf:ro
      # Mount SSL certificates (uncomment if using HTTPS)
      # - ./ssl:/etc/nginx/ssl:ro
      # Mount logs for debugging
      - nginx-logs:/var/log/nginx
    networks:
      - hm-collection-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.hm-collection.rule=Host(`localhost`)"
      - "traefik.http.routers.hm-collection.entrypoints=web"
      - "traefik.http.services.hm-collection.loadbalancer.server.port=80"

  # Reverse Proxy (Traefik) - Optional
  traefik:
    image: traefik:v2.10
    container_name: hm-collection-traefik
    restart: unless-stopped
    ports:
      - "8080:8080"  # Traefik dashboard
      - "8000:80"    # HTTP
      - "8443:443"   # HTTPS
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      # Uncomment for automatic HTTPS with Let's Encrypt
      # - --certificatesresolvers.letsencrypt.acme.httpchallenge=true
      # - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
      # - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      # - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      # - ./letsencrypt:/letsencrypt
    networks:
      - hm-collection-network
    profiles:
      - traefik

  # Redis for caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: hm-collection-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - hm-collection-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - cache

  # PostgreSQL Database (Optional)
  postgres:
    image: postgres:15-alpine
    container_name: hm-collection-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: hm_collection
      POSTGRES_USER: hm_user
      POSTGRES_PASSWORD: hm_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    networks:
      - hm-collection-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hm_user -d hm_collection"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - database

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: hm-collection-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - hm-collection-network
    profiles:
      - monitoring

  # Grafana for dashboards (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: hm-collection-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - hm-collection-network
    profiles:
      - monitoring

networks:
  hm-collection-network:
    driver: bridge
    name: hm-collection-network

volumes:
  nginx-logs:
    name: hm-collection-nginx-logs
  redis-data:
    name: hm-collection-redis-data
  postgres-data:
    name: hm-collection-postgres-data
  prometheus-data:
    name: hm-collection-prometheus-data
  grafana-data:
    name: hm-collection-grafana-data
