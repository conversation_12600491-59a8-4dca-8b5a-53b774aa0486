import 'package:flutter/material.dart';

import '../../../../shared/enums/common_enums.dart';


/// Dialog for filtering orders
class OrderFilterDialog extends StatefulWidget {
  final OrderFilter? initialFilter;
  final Function(OrderFilter) onApplyFilter;

  const OrderFilterDialog({
    Key? key,
    this.initialFilter,
    required this.onApplyFilter,
  }) : super(key: key);

  @override
  State<OrderFilterDialog> createState() => _OrderFilterDialogState();
}

class _OrderFilterDialogState extends State<OrderFilterDialog> {
  late OrderStatus? selectedStatus;
  late OrderPriority? selectedPriority;
  late DateTime? startDate;
  late DateTime? endDate;
  late String searchQuery;

  @override
  void initState() {
    super.initState();
    selectedStatus = widget.initialFilter?.status;
    selectedPriority = widget.initialFilter?.priority;
    startDate = widget.initialFilter?.startDate;
    endDate = widget.initialFilter?.endDate;
    searchQuery = widget.initialFilter?.searchQuery ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Orders'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Query
            TextField(
              decoration: const InputDecoration(
                labelText: 'Search',
                hintText: 'Order number, customer name...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) => searchQuery = value,
              controller: TextEditingController(text: searchQuery),
            ),
            const SizedBox(height: 16),

            // Status Filter
            DropdownButtonFormField<OrderStatus?>(
              decoration: const InputDecoration(labelText: 'Status'),
              value: selectedStatus,
              items: [
                const DropdownMenuItem<OrderStatus?>(
                  value: null,
                  child: Text('All Statuses'),
                ),
                ...OrderStatus.values.map((status) => DropdownMenuItem(
                  value: status,
                  child: Text(status.displayName),
                )),
              ],
              onChanged: (value) => setState(() => selectedStatus = value),
            ),
            const SizedBox(height: 16),

            // Priority Filter
            DropdownButtonFormField<OrderPriority?>(
              decoration: const InputDecoration(labelText: 'Priority'),
              value: selectedPriority,
              items: [
                const DropdownMenuItem<OrderPriority?>(
                  value: null,
                  child: Text('All Priorities'),
                ),
                ...OrderPriority.values.map((priority) => DropdownMenuItem(
                  value: priority,
                  child: Text(priority.displayName),
                )),
              ],
              onChanged: (value) => setState(() => selectedPriority = value),
            ),
            const SizedBox(height: 16),

            // Date Range
            Row(
              children: [
                Expanded(
                  child: TextButton.icon(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: startDate ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setState(() => startDate = date);
                      }
                    },
                    icon: const Icon(Icons.calendar_today),
                    label: Text(
                      startDate != null
                          ? 'From: ${startDate!.day}/${startDate!.month}/${startDate!.year}'
                          : 'Start Date',
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextButton.icon(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: endDate ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setState(() => endDate = date);
                      }
                    },
                    icon: const Icon(Icons.calendar_today),
                    label: Text(
                      endDate != null
                          ? 'To: ${endDate!.day}/${endDate!.month}/${endDate!.year}'
                          : 'End Date',
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            // Clear all filters
            setState(() {
              selectedStatus = null;
              selectedPriority = null;
              startDate = null;
              endDate = null;
              searchQuery = '';
            });
          },
          child: const Text('Clear'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final filter = OrderFilter(
              status: selectedStatus,
              priority: selectedPriority,
              startDate: startDate,
              endDate: endDate,
              searchQuery: searchQuery.isEmpty ? null : searchQuery,
            );
            widget.onApplyFilter(filter);
            Navigator.of(context).pop();
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }
}



class OrderFilter {
  final OrderStatus? status;
  final OrderPriority? priority;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  const OrderFilter({
    this.status,
    this.priority,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });
}