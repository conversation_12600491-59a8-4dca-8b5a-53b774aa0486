import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../domain/repositories/employee_repository.dart';
import '../bloc/employee_bloc.dart';

/// Manufacturing statistics widget with beautiful cards and animations
class ManufacturingStatsWidget extends StatefulWidget {
  const ManufacturingStatsWidget({Key? key}) : super(key: key);

  @override
  State<ManufacturingStatsWidget> createState() => _ManufacturingStatsWidgetState();
}

class _ManufacturingStatsWidgetState extends State<ManufacturingStatsWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _cardAnimations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Create staggered animations for cards
    _cardAnimations = List.generate(4, (index) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.2,
          0.8 + (index * 0.05),
          curve: Curves.easeOutBack,
        ),
      ));
    });

    // Load statistics and start animation
    context.read<EmployeeBloc>().add(const LoadEmployeeStatisticsRequested());
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Manufacturing Statistics',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        BlocBuilder<EmployeeBloc, EmployeeState>(
          builder: (context, state) {
            if (state is EmployeeStatisticsLoaded) {
              return _buildStatsGrid(state.statistics);
            } else if (state is EmployeeLoading) {
              return _buildLoadingStats();
            } else {
              return _buildDefaultStats();
            }
          },
        ),
      ],
    );
  }

  Widget _buildStatsGrid(EmployeeStatistics statistics) {
    final stats = [
      _StatData(
        title: 'Total Employees',
        value: statistics.totalEmployees.toString(),
        icon: Icons.people,
        color: Colors.blue,
        trend: '+5%',
      ),
      _StatData(
        title: 'Active Workers',
        value: statistics.activeEmployees.toString(),
        icon: Icons.work,
        color: Colors.green,
        trend: '+2%',
      ),
      _StatData(
        title: 'Departments',
        value: statistics.employeesByDepartment.length.toString(),
        icon: Icons.business,
        color: Colors.orange,
        trend: 'Stable',
      ),
      _StatData(
        title: 'New Hires',
        value: statistics.newHiresThisMonth.toString(),
        icon: Icons.person_add,
        color: Colors.purple,
        trend: '+12%',
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        return AnimatedBuilder(
          animation: _cardAnimations[index],
          builder: (context, child) {
            return Transform.scale(
              scale: _cardAnimations[index].value,
              child: _StatCard(stat: stats[index]),
            );
          },
        );
      },
    );
  }

  Widget _buildLoadingStats() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: 4,
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }

  Widget _buildDefaultStats() {
    return _buildStatsGrid(const EmployeeStatistics(
      totalEmployees: 0,
      activeEmployees: 0,
      inactiveEmployees: 0,
      newHiresThisMonth: 0,
      terminationsThisMonth: 0,
      averageYearsOfService: 0,
      employeesByDepartment: {},
      employeesByRole: {},
      employeesByStatus: {},
    ));
  }
}

class _StatData {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String trend;

  _StatData({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.trend,
  });
}

class _StatCard extends StatefulWidget {
  final _StatData stat;

  const _StatCard({required this.stat});

  @override
  State<_StatCard> createState() => _StatCardState();
}

class _StatCardState extends State<_StatCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              widget.stat.color.withOpacity(0.7),
              widget.stat.color,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: _isHovered
                  ? widget.stat.color.withOpacity(0.4)
                  : widget.stat.color.withOpacity(0.2),
              blurRadius: _isHovered ? 20 : 10,
              offset: _isHovered ? const Offset(0, 8) : const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      widget.stat.icon,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      widget.stat.trend,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Text(
                widget.stat.value,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                widget.stat.title,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
