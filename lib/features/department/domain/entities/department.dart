import 'package:hm_collection/shared/models/base_entity.dart';
import 'package:hm_collection/features/resource_management/domain/entities/facility_entities.dart';

class Department extends AuditableEntity {
  final String name;
  final String? description;
  final DepartmentType type;

  const Department({
    required String id,
    required this.name,
    this.description,
    required this.type,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? createdBy,
    String? updatedBy,
    int version = 1,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          version: version,
        );

  @override
  List<Object?> get props => [id, name, description, createdAt, updatedAt, createdBy, updatedBy, version];
}
