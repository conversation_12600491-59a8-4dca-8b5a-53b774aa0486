import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/employee.dart';
import '../../domain/repositories/employee_repository.dart';

part 'employee_event.dart';
part 'employee_state.dart';

/// Employee BLoC for managing employee-related state
@injectable
class EmployeeBloc extends Bloc<EmployeeEvent, EmployeeState> {
  final EmployeeRepository _employeeRepository;
  StreamSubscription<List<Employee>>? _employeesSubscription;

  EmployeeBloc(this._employeeRepository) : super(const EmployeeInitial()) {
    // Register event handlers
    on<LoadEmployeesRequested>(_onLoadEmployeesRequested);
    on<RefreshEmployeesRequested>(_onRefreshEmployeesRequested);
    on<LoadEmployeeDetailsRequested>(_onLoadEmployeeDetailsRequested);
    on<CreateEmployeeRequested>(_onCreateEmployeeRequested);
    on<UpdateEmployeeRequested>(_onUpdateEmployeeRequested);
    on<DeleteEmployeeRequested>(_onDeleteEmployeeRequested);
    on<SearchEmployeesRequested>(_onSearchEmployeesRequested);
    on<FilterEmployeesRequested>(_onFilterEmployeesRequested);
    on<LoadEmployeesByDepartmentRequested>(_onLoadEmployeesByDepartmentRequested);
    on<LoadEmployeesByRoleRequested>(_onLoadEmployeesByRoleRequested);
    on<LoadEmployeesBySupervisorRequested>(_onLoadEmployeesBySupervisorRequested);
    on<ActivateEmployeeRequested>(_onActivateEmployeeRequested);
    on<DeactivateEmployeeRequested>(_onDeactivateEmployeeRequested);
    on<TransferEmployeeRequested>(_onTransferEmployeeRequested);
    on<PromoteEmployeeRequested>(_onPromoteEmployeeRequested);
    on<LoadEmployeeStatisticsRequested>(_onLoadEmployeeStatisticsRequested);
    on<StartEmployeeListeningRequested>(_onStartEmployeeListeningRequested);
    on<StopEmployeeListeningRequested>(_onStopEmployeeListeningRequested);
    on<EmployeesUpdatedFromStream>(_onEmployeesUpdatedFromStream);
    on<ClearEmployeeState>(_onClearEmployeeState);
    on<NavigateToCreateEmployeeRequested>(_onNavigateToCreateEmployeeRequested);
    on<NavigateToEmployeeDetailsRequested>(_onNavigateToEmployeeDetailsRequested);
    on<NavigateToEditEmployeeRequested>(_onNavigateToEditEmployeeRequested);
    on<ExportEmployeesRequested>(_onExportEmployeesRequested);
    on<ImportEmployeesRequested>(_onImportEmployeesRequested);
    on<BulkUpdateEmployeesRequested>(_onBulkUpdateEmployeesRequested);
    on<BulkDeleteEmployeesRequested>(_onBulkDeleteEmployeesRequested);
  }

  @override
  Future<void> close() {
    _employeesSubscription?.cancel();
    return super.close();
  }

  /// Load employees
  Future<void> _onLoadEmployeesRequested(
    LoadEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.getEmployees(
      filter: event.filter,
      limit: event.limit,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeesLoaded(
            employees: response.data!,
            filter: event.filter,
            totalCount: response.data!.length,
          ));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to load employees'));
        }
      },
    );
  }

  /// Refresh employees
  Future<void> _onRefreshEmployeesRequested(
    RefreshEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeRefreshing());

    final result = await _employeeRepository.getEmployees(
      filter: event.filter,
      limit: event.limit,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeesLoaded(
            employees: response.data!,
            filter: event.filter,
            totalCount: response.data!.length,
          ));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to refresh employees'));
        }
      },
    );
  }

  /// Load employee details
  Future<void> _onLoadEmployeeDetailsRequested(
    LoadEmployeeDetailsRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.getEmployeeById(event.employeeId);

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeDetailsLoaded(response.data!));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to load employee details'));
        }
      },
    );
  }

  /// Create employee
  Future<void> _onCreateEmployeeRequested(
    CreateEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.createEmployee(event.request);

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeCreated(response.data!));
          // Refresh the employees list
          add(const LoadEmployeesRequested());
        } else {
          emit(EmployeeError(response.message ?? 'Failed to create employee'));
        }
      },
    );
  }

  /// Update employee
  Future<void> _onUpdateEmployeeRequested(
    UpdateEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.updateEmployee(event.request);

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeUpdated(response.data!));
          // Refresh the employees list
          add(const LoadEmployeesRequested());
        } else {
          emit(EmployeeError(response.message ?? 'Failed to update employee'));
        }
      },
    );
  }

  /// Delete employee
  Future<void> _onDeleteEmployeeRequested(
    DeleteEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.deleteEmployee(
      event.employeeId,
      reason: event.reason,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success) {
          emit(EmployeeDeleted(event.employeeId));
          // Refresh the employees list
          add(const LoadEmployeesRequested());
        } else {
          emit(EmployeeError(response.message ?? 'Failed to delete employee'));
        }
      },
    );
  }

  /// Search employees
  Future<void> _onSearchEmployeesRequested(
    SearchEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.searchEmployees(
      event.query,
      filter: event.filter,
      limit: event.limit,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeSearchResults(
            employees: response.data!,
            query: event.query,
            filter: event.filter,
          ));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to search employees'));
        }
      },
    );
  }

  /// Filter employees
  Future<void> _onFilterEmployeesRequested(
    FilterEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.getEmployees(
      filter: event.filter,
      limit: event.limit,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeesLoaded(
            employees: response.data!,
            filter: event.filter,
            totalCount: response.data!.length,
          ));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to filter employees'));
        }
      },
    );
  }

  /// Load employees by department
  Future<void> _onLoadEmployeesByDepartmentRequested(
    LoadEmployeesByDepartmentRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.getEmployeesByDepartment(
      event.department.value,
      limit: event.limit,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeesLoaded(
            employees: response.data!,
            filter: EmployeeFilterCriteria(department: event.department),
            totalCount: response.data!.length,
          ));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to load employees by department'));
        }
      },
    );
  }

  /// Load employees by role
  Future<void> _onLoadEmployeesByRoleRequested(
    LoadEmployeesByRoleRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.getEmployeesByRole(
      event.role.value,
      limit: event.limit,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeesLoaded(
            employees: response.data!,
            filter: EmployeeFilterCriteria(role: event.role),
            totalCount: response.data!.length,
          ));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to load employees by role'));
        }
      },
    );
  }

  /// Load employees by supervisor
  Future<void> _onLoadEmployeesBySupervisorRequested(
    LoadEmployeesBySupervisorRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.getEmployeesBySupervisor(
      event.supervisorId,
      limit: event.limit,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeesLoaded(
            employees: response.data!,
            filter: EmployeeFilterCriteria(supervisorId: event.supervisorId),
            totalCount: response.data!.length,
          ));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to load employees by supervisor'));
        }
      },
    );
  }

  /// Activate employee
  Future<void> _onActivateEmployeeRequested(
    ActivateEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.activateEmployee(event.employeeId);

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeActivated(response.data!));
          // Refresh the employees list
          add(const LoadEmployeesRequested());
        } else {
          emit(EmployeeError(response.message ?? 'Failed to activate employee'));
        }
      },
    );
  }

  /// Deactivate employee
  Future<void> _onDeactivateEmployeeRequested(
    DeactivateEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.deactivateEmployee(
      event.employeeId,
      reason: event.reason,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeDeactivated(response.data!));
          // Refresh the employees list
          add(const LoadEmployeesRequested());
        } else {
          emit(EmployeeError(response.message ?? 'Failed to deactivate employee'));
        }
      },
    );
  }

  /// Transfer employee
  Future<void> _onTransferEmployeeRequested(
    TransferEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.transferEmployee(
      event.employeeId,
      event.newDepartment.value,
      newSupervisorId: event.newSupervisorId,
      reason: event.reason,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeTransferred(response.data!, event.newDepartment));
          // Refresh the employees list
          add(const LoadEmployeesRequested());
        } else {
          emit(EmployeeError(response.message ?? 'Failed to transfer employee'));
        }
      },
    );
  }

  /// Promote employee
  Future<void> _onPromoteEmployeeRequested(
    PromoteEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.promoteEmployee(
      event.employeeId,
      event.newRole.value,
      reason: event.reason,
    );

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeePromoted(response.data!, event.newRole));
          // Refresh the employees list
          add(const LoadEmployeesRequested());
        } else {
          emit(EmployeeError(response.message ?? 'Failed to promote employee'));
        }
      },
    );
  }

  /// Load employee statistics
  Future<void> _onLoadEmployeeStatisticsRequested(
    LoadEmployeeStatisticsRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());

    final result = await _employeeRepository.getEmployeeStatistics();

    result.fold(
      (failure) => emit(EmployeeError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(EmployeeStatisticsLoaded(response.data!));
        } else {
          emit(EmployeeError(response.message ?? 'Failed to load statistics'));
        }
      },
    );
  }

  /// Start real-time employee listening
  Future<void> _onStartEmployeeListeningRequested(
    StartEmployeeListeningRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    await _employeesSubscription?.cancel();
    
    emit(EmployeeListeningStarted(filter: event.filter));

    _employeesSubscription = _employeeRepository
        .getEmployeesStream(
          filter: event.filter,
          limit: event.limit,
        )
        .listen(
          (employees) => add(EmployeesUpdatedFromStream(employees)),
          onError: (error) => emit(EmployeeError('Real-time update failed: $error')),
        );
  }

  /// Stop real-time employee listening
  Future<void> _onStopEmployeeListeningRequested(
    StopEmployeeListeningRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    await _employeesSubscription?.cancel();
    _employeesSubscription = null;
    emit(const EmployeeListeningStopped());
  }

  /// Handle real-time employees update
  void _onEmployeesUpdatedFromStream(
    EmployeesUpdatedFromStream event,
    Emitter<EmployeeState> emit,
  ) {
    if (_employeesSubscription != null) {
      emit(EmployeesRealTimeUpdated(employees: event.employees));
    }
  }

  /// Clear employee state
  void _onClearEmployeeState(
    ClearEmployeeState event,
    Emitter<EmployeeState> emit,
  ) {
    emit(const EmployeeInitial());
  }

  /// Navigate to create employee
  void _onNavigateToCreateEmployeeRequested(
    NavigateToCreateEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) {
    // This would typically trigger navigation in the UI layer
    // For now, we'll just emit a state that the UI can listen to
    emit(const EmployeeOperationSuccess('Navigate to create employee'));
  }

  /// Navigate to employee details
  void _onNavigateToEmployeeDetailsRequested(
    NavigateToEmployeeDetailsRequested event,
    Emitter<EmployeeState> emit,
  ) {
    emit(EmployeeDetailsLoaded(event.employee));
  }

  /// Navigate to edit employee
  void _onNavigateToEditEmployeeRequested(
    NavigateToEditEmployeeRequested event,
    Emitter<EmployeeState> emit,
  ) {
    emit(EmployeeDetailsLoaded(event.employee));
  }

  /// Export employees (placeholder implementation)
  Future<void> _onExportEmployeesRequested(
    ExportEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());
    
    // TODO: Implement actual export functionality
    await Future.delayed(const Duration(seconds: 2));
    
    emit(const EmployeesExported(
      filePath: '/path/to/exported/file.csv',
      format: 'csv',
      count: 100,
    ));
  }

  /// Import employees (placeholder implementation)
  Future<void> _onImportEmployeesRequested(
    ImportEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());
    
    // TODO: Implement actual import functionality
    await Future.delayed(const Duration(seconds: 3));
    
    emit(const EmployeesImported(
      successCount: 95,
      failureCount: 5,
      errors: ['Invalid email format in row 10', 'Duplicate employee ID in row 25'],
    ));
  }

  /// Bulk update employees (placeholder implementation)
  Future<void> _onBulkUpdateEmployeesRequested(
    BulkUpdateEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());
    
    // TODO: Implement actual bulk update functionality
    await Future.delayed(const Duration(seconds: 2));
    
    emit(BulkOperationCompleted(
      operation: 'update',
      successCount: event.employeeIds.length,
      failureCount: 0,
      errors: const [],
    ));
  }

  /// Bulk delete employees (placeholder implementation)
  Future<void> _onBulkDeleteEmployeesRequested(
    BulkDeleteEmployeesRequested event,
    Emitter<EmployeeState> emit,
  ) async {
    emit(const EmployeeLoading());
    
    // TODO: Implement actual bulk delete functionality
    await Future.delayed(const Duration(seconds: 2));
    
    emit(BulkOperationCompleted(
      operation: 'delete',
      successCount: event.employeeIds.length,
      failureCount: 0,
      errors: const [],
    ));
  }
}
