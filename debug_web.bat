@echo off
echo ========================================
echo HM Collection Web Debug Helper
echo ========================================
echo.

REM Check if Flutter is installed
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Flutter is not installed or not in PATH
    echo Please install Flutter and add it to your PATH
    pause
    exit /b 1
)

echo [INFO] Flutter version:
flutter --version
echo.

REM Check if we're in the right directory
if not exist "pubspec.yaml" (
    echo [ERROR] pubspec.yaml not found. Please run this script from the project root.
    pause
    exit /b 1
)

echo [INFO] Cleaning previous builds...
flutter clean

echo [INFO] Getting dependencies...
flutter pub get

if %errorlevel% neq 0 (
    echo [ERROR] Failed to get dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo [INFO] Checking for syntax errors...
flutter analyze

if %errorlevel% neq 0 (
    echo [WARNING] There are analysis issues. Check the output above.
    echo You can continue, but fixing these issues is recommended.
    pause
)

echo.
echo [INFO] Available debug options:
echo 1. Run in Chrome (Debug Mode)
echo 2. Run in Chrome (Profile Mode)
echo 3. Build for Web (Release)
echo 4. Run with Hot Reload
echo 5. Check Dependencies
echo 6. Exit
echo.

set /p choice="Choose an option (1-6): "

if "%choice%"=="1" goto run_debug
if "%choice%"=="2" goto run_profile
if "%choice%"=="3" goto build_release
if "%choice%"=="4" goto run_hot_reload
if "%choice%"=="5" goto check_deps
if "%choice%"=="6" goto exit
goto invalid_choice

:run_debug
echo.
echo [INFO] Starting Flutter web app in debug mode...
echo [INFO] This will open Chrome with debugging enabled
echo [INFO] Press Ctrl+C to stop the server
echo [INFO] DevTools will be available at the URL shown below
echo.
flutter run -d chrome --web-port=3000 --web-hostname=localhost
goto end

:run_profile
echo.
echo [INFO] Starting Flutter web app in profile mode...
echo [INFO] This mode provides better performance than debug mode
echo.
flutter run -d chrome --profile --web-port=3000 --web-hostname=localhost
goto end

:build_release
echo.
echo [INFO] Building Flutter web app for release...
flutter build web --release --web-renderer canvaskit
if %errorlevel% equ 0 (
    echo [SUCCESS] Build completed successfully!
    echo [INFO] Output directory: build\web
    echo [INFO] You can serve this directory with any web server
    echo.
    echo [INFO] To test locally, you can use:
    echo cd build\web
    echo python -m http.server 8000
    echo Then open http://localhost:8000
) else (
    echo [ERROR] Build failed! Check the errors above.
)
goto end

:run_hot_reload
echo.
echo [INFO] Starting Flutter web app with hot reload...
echo [INFO] You can make changes to your code and see them instantly
echo [INFO] Press 'r' to hot reload, 'R' to hot restart
echo.
flutter run -d chrome --web-port=3000 --web-hostname=localhost --hot
goto end

:check_deps
echo.
echo [INFO] Checking dependencies...
flutter pub deps
echo.
echo [INFO] Checking for outdated packages...
flutter pub outdated
goto end

:invalid_choice
echo [ERROR] Invalid choice. Please select 1-6.
pause
goto end

:exit
echo Goodbye!
goto end

:end
echo.
echo ========================================
echo Debug session ended
echo ========================================
pause
