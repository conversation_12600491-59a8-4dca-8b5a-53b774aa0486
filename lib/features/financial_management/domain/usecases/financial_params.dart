import 'package:equatable/equatable.dart';
import 'package:hm_collection/features/financial_management/domain/entities/financial_entities.dart';

import '../repositories/financial_repository.dart';

// Params classes

class GetCostCentersParams extends Equatable {
  final CostCenterFilterCriteria? filter;
  final dynamic pagination;
  const GetCostCentersParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class SearchCostCentersParams extends Equatable {
  final String query;
  final CostCenterFilterCriteria? filter;
  final dynamic pagination;
  const SearchCostCentersParams({required this.query, this.filter, this.pagination});
  @override
  List<Object?> get props => [query, filter, pagination];
}

class GetTransactionsParams extends Equatable {
  final TransactionFilterCriteria? filter;
  final dynamic pagination;
  const GetTransactionsParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class ApproveTransactionParams extends Equatable {
  final String transactionId;
  final String approverId;
  const ApproveTransactionParams({required this.transactionId, required this.approverId});
  @override
  List<Object?> get props => [transactionId, approverId];
}

class RejectTransactionParams extends Equatable {
  final String transactionId;
  final String rejectedBy;
  final String reason;
  const RejectTransactionParams(
      {required this.transactionId, required this.rejectedBy, required this.reason});
  @override
  List<Object?> get props => [transactionId, rejectedBy, reason];
}

class SearchTransactionsParams extends Equatable {
  final String query;
  final TransactionFilterCriteria? filter;
  final dynamic pagination;
  const SearchTransactionsParams({required this.query, this.filter, this.pagination});
  @override
  List<Object?> get props => [query, filter, pagination];
}

class GetBudgetsParams extends Equatable {
  final BudgetFilterCriteria? filter;
  final dynamic pagination;
  const GetBudgetsParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class ApproveBudgetParams extends Equatable {
  final String budgetId;
  final String approverId;
  const ApproveBudgetParams({required this.budgetId, required this.approverId});
  @override
  List<Object?> get props => [budgetId, approverId];
}

class RejectBudgetParams extends Equatable {
  final String budgetId;
  final String rejectedBy;
  final String reason;
  const RejectBudgetParams(
      {required this.budgetId, required this.rejectedBy, required this.reason});
  @override
  List<Object?> get props => [budgetId, rejectedBy, reason];
}

class SearchBudgetsParams extends Equatable {
  final String query;
  final BudgetFilterCriteria? filter;
  final dynamic pagination;
  const SearchBudgetsParams({required this.query, this.filter, this.pagination});
  @override
  List<Object?> get props => [query, filter, pagination];
}

class GetReportsParams extends Equatable {
  final ReportFilterCriteria? filter;
  final dynamic pagination;
  const GetReportsParams({this.filter, this.pagination});
  @override
  List<Object?> get props => [filter, pagination];
}

class GetProfitLossStatementParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  final String? departmentId;
  const GetProfitLossStatementParams(
      {required this.startDate, required this.endDate, this.costCenterId, this.departmentId});
  @override
  List<Object?> get props => [startDate, endDate, costCenterId, departmentId];
}

class GetCostAnalysisReportParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  final String? departmentId;
  const GetCostAnalysisReportParams(
      {required this.startDate, required this.endDate, this.costCenterId, this.departmentId});
  @override
  List<Object?> get props => [startDate, endDate, costCenterId, departmentId];
}

class GetBudgetVarianceReportParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? budgetId;
  final String? costCenterId;
  const GetBudgetVarianceReportParams(
      {required this.startDate, required this.endDate, this.budgetId, this.costCenterId});
  @override
  List<Object?> get props => [startDate, endDate, budgetId, costCenterId];
}

class GetFinancialSummaryParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  const GetFinancialSummaryParams(
      {required this.startDate, required this.endDate, this.costCenterId});
  @override
  List<Object?> get props => [startDate, endDate, costCenterId];
}

class GetCostTrendsParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final CostCategory? category;
  final String? costCenterId;
  const GetCostTrendsParams(
      {required this.startDate, required this.endDate, this.category, this.costCenterId});
  @override
  List<Object?> get props => [startDate, endDate, category, costCenterId];
}
