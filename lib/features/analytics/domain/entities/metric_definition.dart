import 'package:equatable/equatable.dart';

/// Represents the definition of a metric that can be used in analytics.
class MetricDefinition extends Equatable {
  /// Unique identifier for the metric
  final String id;

  /// Display name of the metric
  final String name;

  /// Description of what the metric measures
  final String description;

  /// The unit of measurement for this metric (e.g., 'kg', '%', 'units')
  final String unit;

  /// The category this metric belongs to (e.g., 'financial', 'inventory', 'efficiency')
  final String category;

  /// Whether this metric is currently active/enabled
  final bool isActive;

  /// Creates a new [MetricDefinition]
  const MetricDefinition({
    required this.id,
    required this.name,
    required this.description,
    required this.unit,
    required this.category,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [id, name, description, unit, category, isActive];

  /// Creates a copy of this metric definition with the given fields replaced with the new values
  MetricDefinition copyWith({
    String? id,
    String? name,
    String? description,
    String? unit,
    String? category,
    bool? isActive,
  }) {
    return MetricDefinition(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      unit: unit ?? this.unit,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
    );
  }
}
