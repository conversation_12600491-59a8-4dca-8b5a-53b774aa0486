import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/entities/analytics_entities.dart';

/// Dashboard grid widget
class DashboardGridWidget extends StatelessWidget {
  final List<AnalyticsDashboard> dashboards;
  final Function(AnalyticsDashboard) onDashboardTap;
  final Function(AnalyticsDashboard)? onDashboardEdit;
  final Function(AnalyticsDashboard)? onDashboardDelete;
  final Function(AnalyticsDashboard)? onDashboardClone;
  final Function(AnalyticsDashboard)? onDashboardShare;

  const DashboardGridWidget({
    super.key,
    required this.dashboards,
    required this.onDashboardTap,
    this.onDashboardEdit,
    this.onDashboardDelete,
    this.onDashboardClone,
    this.onDashboardShare,
  });

  @override
  Widget build(BuildContext context) {
    if (dashboards.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      // Ensure this grid always has a concrete size in all parent layouts (e.g., inside Columns/Tab views)
      shrinkWrap: true,
      primary: false,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: dashboards.length,
      itemBuilder: (context, index) {
        final dashboard = dashboards[index];
        return _buildDashboardCard(dashboard);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Dashboards Available',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first dashboard to start visualizing data',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () => _createDashboard(),
            icon: const Icon(Icons.add),
            label: const Text('Create Dashboard'),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(AnalyticsDashboard dashboard) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => onDashboardTap(dashboard),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getDashboardTypeColor(dashboard.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getDashboardTypeIcon(dashboard.type),
                      color: _getDashboardTypeColor(dashboard.type),
                      size: 20,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value, dashboard),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'clone',
                        child: Row(
                          children: [
                            Icon(Icons.copy, size: 16),
                            SizedBox(width: 8),
                            Text('Clone'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share, size: 16),
                            SizedBox(width: 8),
                            Text('Share'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Text(
                dashboard.dashboardName,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 4),
              
              Text(
                dashboard.description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const Spacer(),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.widgets,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${dashboard.widgetCount} widgets',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      if (dashboard.isDefault)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Default',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      if (dashboard.isPublic) ...[
                        if (dashboard.isDefault) const SizedBox(width: 4),
                        Icon(
                          Icons.public,
                          size: 14,
                          color: AppColors.success,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              Row(
                children: [
                  CircleAvatar(
                    radius: 8,
                    backgroundColor: AppColors.primary,
                    child: Text(
                      dashboard.ownerName.substring(0, 1).toUpperCase(),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      dashboard.ownerName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    _formatDate(dashboard.updatedAt),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getDashboardTypeColor(DashboardType type) {
    switch (type) {
      case DashboardType.executive:
        return AppColors.primary;
      case DashboardType.operational:
        return AppColors.success;
      case DashboardType.financial:
        return AppColors.warning;
      case DashboardType.production:
        return AppColors.error;
      case DashboardType.quality:
        return Colors.purple;
      case DashboardType.inventory:
        return Colors.orange;
      case DashboardType.sales:
        return Colors.green;
      case DashboardType.custom:
        return AppColors.textSecondary;
    }
  }

  IconData _getDashboardTypeIcon(DashboardType type) {
    switch (type) {
      case DashboardType.executive:
        return Icons.business;
      case DashboardType.operational:
        return Icons.settings;
      case DashboardType.financial:
        return Icons.attach_money;
      case DashboardType.production:
        return Icons.precision_manufacturing;
      case DashboardType.quality:
        return Icons.verified;
      case DashboardType.inventory:
        return Icons.inventory;
      case DashboardType.sales:
        return Icons.trending_up;
      case DashboardType.custom:
        return Icons.dashboard_customize;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) return 'Today';
    if (difference == 1) return 'Yesterday';
    if (difference < 7) return '${difference}d ago';
    if (difference < 30) return '${(difference / 7).floor()}w ago';
    
    return '${date.day}/${date.month}/${date.year}';
  }

  void _handleMenuAction(String action, AnalyticsDashboard dashboard) {
    switch (action) {
      case 'edit':
        onDashboardEdit?.call(dashboard);
        break;
      case 'clone':
        onDashboardClone?.call(dashboard);
        break;
      case 'share':
        onDashboardShare?.call(dashboard);
        break;
      case 'delete':
        onDashboardDelete?.call(dashboard);
        break;
    }
  }

  void _createDashboard() {
    // Navigate to create dashboard page
  }
}

/// Real-time metrics widget
class RealTimeMetricsWidget extends StatelessWidget {
  final Map<String, dynamic> metrics;
  final Function(String)? onMetricTap;

  const RealTimeMetricsWidget({
    super.key,
    required this.metrics,
    this.onMetricTap,
  });

  @override
  Widget build(BuildContext context) {
    if (metrics.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      // Avoid unbounded height when placed inside Column/Expanded
      shrinkWrap: true,
      primary: false,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5,
      ),
      itemCount: metrics.length,
      itemBuilder: (context, index) {
        final metric = metrics.keys.elementAt(index);
        final data = metrics[metric] as Map<String, dynamic>;
        return _buildMetricCard(metric, data);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.speed_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Real-time Data',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Real-time metrics will appear here when available',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String metric, Map<String, dynamic> data) {
    final value = (data['value'] as num?)?.toDouble() ?? 0.0;
    final status = data['status'] as String? ?? 'normal';
    final trend = data['trend'] as String? ?? 'stable';
    final timestamp = data['timestamp'] as String? ?? '';

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => onMetricTap?.call(metric),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _getStatusColor(status),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Row(
                    children: [
                      Icon(
                        _getTrendIcon(trend),
                        size: 16,
                        color: _getTrendColor(trend),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Live',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Text(
                _formatMetricName(metric),
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                _formatMetricValue(metric, value),
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getStatusColor(status),
                ),
              ),
              
              const Spacer(),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getStatusColor(status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      status.toUpperCase(),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: _getStatusColor(status),
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                      ),
                    ),
                  ),
                  Text(
                    _formatTimestamp(timestamp),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'normal':
        return AppColors.success;
      case 'warning':
        return AppColors.warning;
      case 'critical':
      case 'error':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getTrendIcon(String trend) {
    switch (trend.toLowerCase()) {
      case 'up':
        return Icons.trending_up;
      case 'down':
        return Icons.trending_down;
      case 'stable':
        return Icons.trending_flat;
      default:
        return Icons.remove;
    }
  }

  Color _getTrendColor(String trend) {
    switch (trend.toLowerCase()) {
      case 'up':
        return AppColors.success;
      case 'down':
        return AppColors.error;
      case 'stable':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatMetricName(String metric) {
    return metric
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.substring(0, 1).toUpperCase() + word.substring(1))
        .join(' ');
  }

  String _formatMetricValue(String metric, double value) {
    switch (metric) {
      case 'production_volume':
        return '${value.toStringAsFixed(0)} units';
      case 'quality_score':
      case 'efficiency_rate':
      case 'machine_utilization':
        return '${value.toStringAsFixed(1)}%';
      case 'revenue':
      case 'cost_per_unit':
        return '\$${value.toStringAsFixed(2)}';
      default:
        return value.toStringAsFixed(1);
    }
  }

  String _formatTimestamp(String timestamp) {
    if (timestamp.isEmpty) return 'Now';
    
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inSeconds < 60) return 'Now';
      if (difference.inMinutes < 60) return '${difference.inMinutes}m ago';
      if (difference.inHours < 24) return '${difference.inHours}h ago';
      
      return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Now';
    }
  }
}
