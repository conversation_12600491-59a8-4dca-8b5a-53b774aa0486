part of 'production_bloc.dart';

/// Base production event
abstract class ProductionEvent extends Equatable {
  const ProductionEvent();

  @override
  List<Object?> get props => [];
}

/// Load production orders
class LoadProductionOrdersRequested extends ProductionEvent {
  final ProductionFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadProductionOrdersRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Refresh production orders
class RefreshProductionOrdersRequested extends ProductionEvent {
  const RefreshProductionOrdersRequested();
}

/// Load production order details
class LoadProductionOrderDetailsRequested extends ProductionEvent {
  final String productionOrderId;

  const LoadProductionOrderDetailsRequested(this.productionOrderId);

  @override
  List<Object?> get props => [productionOrderId];
}

/// Create production order
class CreateProductionOrderRequested extends ProductionEvent {
  final CreateProductionOrderRequest request;

  const CreateProductionOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update production order
class UpdateProductionOrderRequested extends ProductionEvent {
  final UpdateProductionOrderRequest request;

  const UpdateProductionOrderRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update production order status
class UpdateProductionOrderStatusRequested extends ProductionEvent {
  final String productionOrderId;
  final ProductionStatus status;
  final String? reason;

  const UpdateProductionOrderStatusRequested(
    this.productionOrderId,
    this.status, {
    this.reason,
  });

  @override
  List<Object?> get props => [productionOrderId, status, reason];
}

/// Start production order
class StartProductionOrderRequested extends ProductionEvent {
  final String productionOrderId;

  const StartProductionOrderRequested(this.productionOrderId);

  @override
  List<Object?> get props => [productionOrderId];
}

/// Pause production order
class PauseProductionOrderRequested extends ProductionEvent {
  final String productionOrderId;
  final String reason;

  const PauseProductionOrderRequested(this.productionOrderId, this.reason);

  @override
  List<Object?> get props => [productionOrderId, reason];
}

/// Resume production order
class ResumeProductionOrderRequested extends ProductionEvent {
  final String productionOrderId;

  const ResumeProductionOrderRequested(this.productionOrderId);

  @override
  List<Object?> get props => [productionOrderId];
}

/// Complete production order
class CompleteProductionOrderRequested extends ProductionEvent {
  final String productionOrderId;

  const CompleteProductionOrderRequested(this.productionOrderId);

  @override
  List<Object?> get props => [productionOrderId];
}

/// Load production tasks
class LoadProductionTasksRequested extends ProductionEvent {
  final TaskFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadProductionTasksRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Update task status
class UpdateTaskStatusRequested extends ProductionEvent {
  final String taskId;
  final TaskStatus status;
  final String? reason;

  const UpdateTaskStatusRequested(
    this.taskId,
    this.status, {
    this.reason,
  });

  @override
  List<Object?> get props => [taskId, status, reason];
}

/// Assign task to workers
class AssignTaskToWorkersRequested extends ProductionEvent {
  final String taskId;
  final List<String> workerIds;

  const AssignTaskToWorkersRequested(this.taskId, this.workerIds);

  @override
  List<Object?> get props => [taskId, workerIds];
}

/// Update task progress
class UpdateTaskProgressRequested extends ProductionEvent {
  final String taskId;
  final double completionPercentage;
  final String? notes;

  const UpdateTaskProgressRequested(
    this.taskId,
    this.completionPercentage, {
    this.notes,
  });

  @override
  List<Object?> get props => [taskId, completionPercentage, notes];
}

/// Load resource allocations
class LoadResourceAllocationsRequested extends ProductionEvent {
  final ResourceAllocationFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadResourceAllocationsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Allocate resource
class AllocateResourceRequested extends ProductionEvent {
  final AllocateResourceRequest request;

  const AllocateResourceRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Release resource
class ReleaseResourceRequested extends ProductionEvent {
  final String allocationId;
  final String? reason;

  const ReleaseResourceRequested(this.allocationId, {this.reason});

  @override
  List<Object?> get props => [allocationId, reason];
}

/// Load production statistics
class LoadProductionStatisticsRequested extends ProductionEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? departmentId;

  const LoadProductionStatisticsRequested({
    this.startDate,
    this.endDate,
    this.departmentId,
  });

  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

/// Load production capacity
class LoadProductionCapacityRequested extends ProductionEvent {
  final DateTime? date;
  final String? departmentId;

  const LoadProductionCapacityRequested({
    this.date,
    this.departmentId,
  });

  @override
  List<Object?> get props => [date, departmentId];
}

/// Load production bottlenecks
class LoadProductionBottlenecksRequested extends ProductionEvent {
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadProductionBottlenecksRequested({
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];
}

/// Load overdue production orders
class LoadOverdueProductionOrdersRequested extends ProductionEvent {
  final PaginationParams? pagination;

  const LoadOverdueProductionOrdersRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Load urgent production orders
class LoadUrgentProductionOrdersRequested extends ProductionEvent {
  final PaginationParams? pagination;

  const LoadUrgentProductionOrdersRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Search production orders
class SearchProductionOrdersRequested extends ProductionEvent {
  final String query;
  final ProductionFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchProductionOrdersRequested(
    this.query, {
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Filter production orders
class FilterProductionOrdersRequested extends ProductionEvent {
  final ProductionFilterCriteria filter;
  final PaginationParams? pagination;

  const FilterProductionOrdersRequested(
    this.filter, {
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Clear production state
class ClearProductionState extends ProductionEvent {
  const ClearProductionState();
}

/// Load more production orders (pagination)
class LoadMoreProductionOrdersRequested extends ProductionEvent {
  const LoadMoreProductionOrdersRequested();
}

/// Sort production orders
class SortProductionOrdersRequested extends ProductionEvent {
  final String sortBy;
  final bool ascending;

  const SortProductionOrdersRequested(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Select production order
class SelectProductionOrderRequested extends ProductionEvent {
  final String productionOrderId;

  const SelectProductionOrderRequested(this.productionOrderId);

  @override
  List<Object?> get props => [productionOrderId];
}

/// Change production view mode
class ChangeProductionViewModeRequested extends ProductionEvent {
  final ProductionViewMode viewMode;

  const ChangeProductionViewModeRequested(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Delete production order
class DeleteProductionOrderRequested extends ProductionEvent {
  final String orderId;
  final String? reason;

  const DeleteProductionOrderRequested(this.orderId, {this.reason});

  @override
  List<Object?> get props => [orderId, reason];
}

/// Navigate to create production order
class NavigateToCreateProductionOrderRequested extends ProductionEvent {
  const NavigateToCreateProductionOrderRequested();
}

/// Production view mode enum
enum ProductionViewMode {
  list,
  kanban,
  timeline,
  calendar,
}

/// Production view mode extension
extension ProductionViewModeExtension on ProductionViewMode {
  String get displayName {
    switch (this) {
      case ProductionViewMode.list:
        return 'List';
      case ProductionViewMode.kanban:
        return 'Kanban';
      case ProductionViewMode.timeline:
        return 'Timeline';
      case ProductionViewMode.calendar:
        return 'Calendar';
    }
  }

  IconData get icon {
    switch (this) {
      case ProductionViewMode.list:
        return Icons.list;
      case ProductionViewMode.kanban:
        return Icons.view_kanban;
      case ProductionViewMode.timeline:
        return Icons.timeline;
      case ProductionViewMode.calendar:
        return Icons.calendar_view_month;
    }
  }
}
