import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../constants/api_constants.dart';
import '../constants/app_constants.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/error_interceptor.dart';
import 'interceptors/logging_interceptor.dart';
import 'package:injectable/injectable.dart';

/// HTTP client for API communication
@lazySingleton
class ApiClient {
  late final Dio _dio;
  final Logger _logger = Logger();
  
  ApiClient({
    String? baseUrl,
    int? connectTimeout,
    int? receiveTimeout,
    int? sendTimeout,
  }) {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl ?? ApiConstants.fullBaseUrl,
        connectTimeout: Duration(seconds: connectTimeout ?? AppConstants.timeoutDuration),
        receiveTimeout: Duration(seconds: receiveTimeout ?? AppConstants.timeoutDuration),
        sendTimeout: Duration(seconds: sendTimeout ?? AppConstants.timeoutDuration),
        headers: {
          ApiConstants.contentTypeHeader: ApiConstants.contentType<PERSON>son,
          ApiConstants.acceptHeader: ApiConstants.contentTypeJson,
          ApiConstants.userAgentHeader: '${AppConstants.appName}/${AppConstants.appVersion}',
        },
      ),
    );
    
    _setupInterceptors();
  }
  
  /// Setup interceptors for the Dio client
  void _setupInterceptors() {
    _dio.interceptors.clear();
    
    // Add logging interceptor (only in debug mode)
    if (AppConstants.enableDebugMode) {
      _dio.interceptors.add(LoggingInterceptor());
    }
    
    // Add authentication interceptor
    _dio.interceptors.add(AuthInterceptor());
    
    // Add error interceptor
    _dio.interceptors.add(ErrorInterceptor());
  }
  
  /// GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      _logger.e('GET request failed: $path', error: e);
      rethrow;
    }
  }
  
  /// POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      _logger.e('POST request failed: $path', error: e);
      rethrow;
    }
  }
  
  /// PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      _logger.e('PUT request failed: $path', error: e);
      rethrow;
    }
  }
  
  /// PATCH request
  Future<Response<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.patch<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      _logger.e('PATCH request failed: $path', error: e);
      rethrow;
    }
  }
  
  /// DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response;
    } catch (e) {
      _logger.e('DELETE request failed: $path', error: e);
      rethrow;
    }
  }
  
  /// GET request that returns a Stream of data
  Stream<Response<T>> getStream<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async* {
    try {
      final response = await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      yield response;
    } catch (e) {
      _logger.e('GET stream request failed: $path', error: e);
      rethrow;
    }
  }

  /// Upload file
  Future<Response<T>> uploadFile<T>(
    String path,
    String filePath, {
    String? fileName,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          filePath,
          filename: fileName,
        ),
        if (data != null) ...data,
      });
      
      final response = await _dio.post<T>(
        path,
        data: formData,
        queryParameters: queryParameters,
        options: options?.copyWith(
          contentType: ApiConstants.contentTypeFormData,
        ) ?? Options(
          contentType: ApiConstants.contentTypeFormData,
        ),
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
      );
      return response;
    } catch (e) {
      _logger.e('File upload failed: $path', error: e);
      rethrow;
    }
  }
  
  /// Download file
  Future<Response> downloadFile(
    String path,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.download(
        path,
        savePath,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      _logger.e('File download failed: $path', error: e);
      rethrow;
    }
  }
  
  /// Update base URL
  void updateBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
  }
  
  /// Add header
  void addHeader(String key, String value) {
    _dio.options.headers[key] = value;
  }
  
  /// Remove header
  void removeHeader(String key) {
    _dio.options.headers.remove(key);
  }
  
  /// Clear all headers
  void clearHeaders() {
    _dio.options.headers.clear();
  }
  
  /// Get current headers
  Map<String, dynamic> get headers => _dio.options.headers;
  
  /// Close the client
  void close() {
    _dio.close();
  }
}
