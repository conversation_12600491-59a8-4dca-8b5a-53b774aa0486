import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/numbering_task.dart';
import '../models/numbering_task_model.dart';

/// Local data source for numbering tasks using SharedPreferences
@injectable
class NumberingLocalDataSource {
  final SharedPreferences _prefs;
  static const String _tasksKey = 'numbering_tasks';
  static const String _defaultRateKey = 'default_rate_per_piece';
  static const String _lastSyncKey = 'numbering_last_sync';

  NumberingLocalDataSource(this._prefs);

  /// Get all cached numbering tasks
  Future<List<NumberingTaskModel>> getAllTasks() async {
    try {
      final tasksJson = _prefs.getString(_tasksKey);
      if (tasksJson == null) return [];

      final List<dynamic> tasksList = json.decode(tasksJson);
      return tasksList
          .map((taskJson) => NumberingTaskModel.fromJson(taskJson))
          .toList();
    } catch (e) {
      throw CacheException('Failed to load cached tasks: $e');
    }
  }

  /// Get a specific task by ID from cache
  Future<NumberingTaskModel?> getTaskById(String id) async {
    try {
      final tasks = await getAllTasks();
      return tasks.where((task) => task.id == id).firstOrNull;
    } catch (e) {
      throw CacheException('Failed to get cached task: $e');
    }
  }

  /// Cache a list of tasks
  Future<void> cacheTasks(List<NumberingTaskModel> tasks) async {
    try {
      final tasksJson = json.encode(tasks.map((task) => task.toJson()).toList());
      await _prefs.setString(_tasksKey, tasksJson);
      await _prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());
    } catch (e) {
      throw CacheException('Failed to cache tasks: $e');
    }
  }

  /// Cache a single task (add or update)
  Future<void> cacheTask(NumberingTaskModel task) async {
    try {
      final tasks = await getAllTasks();
      final existingIndex = tasks.indexWhere((t) => t.id == task.id);
      
      if (existingIndex >= 0) {
        tasks[existingIndex] = task;
      } else {
        tasks.add(task);
      }

      await cacheTasks(tasks);
    } catch (e) {
      throw CacheException('Failed to cache task: $e');
    }
  }

  /// Remove a task from cache
  Future<void> removeTask(String id) async {
    try {
      final tasks = await getAllTasks();
      tasks.removeWhere((task) => task.id == id);
      await cacheTasks(tasks);
    } catch (e) {
      throw CacheException('Failed to remove cached task: $e');
    }
  }

  /// Get tasks by status from cache
  Future<List<NumberingTaskModel>> getTasksByStatus(NumberingTaskStatus status) async {
    try {
      final tasks = await getAllTasks();
      return tasks.where((task) => task.status == status).toList();
    } catch (e) {
      throw CacheException('Failed to get tasks by status: $e');
    }
  }

  /// Get tasks assigned to a user from cache
  Future<List<NumberingTaskModel>> getTasksAssignedToUser(String userId) async {
    try {
      final tasks = await getAllTasks();
      return tasks.where((task) => task.assignedTo == userId).toList();
    } catch (e) {
      throw CacheException('Failed to get user tasks: $e');
    }
  }

  /// Search tasks in cache
  Future<List<NumberingTaskModel>> searchTasks({
    String? searchQuery,
    NumberingTaskStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final tasks = await getAllTasks();
      
      return tasks.where((task) {
        // Status filter
        if (status != null && task.status != status) return false;
        
        // Date range filter
        if (startDate != null && task.date.isBefore(startDate)) return false;
        if (endDate != null && task.date.isAfter(endDate)) return false;
        
        // Text search filter
        if (searchQuery != null && searchQuery.isNotEmpty) {
          final lowerQuery = searchQuery.toLowerCase();
          return task.orderNo.toLowerCase().contains(lowerQuery) ||
                 task.clientName.toLowerCase().contains(lowerQuery) ||
                 task.designNo.toLowerCase().contains(lowerQuery) ||
                 task.bundleNo.toLowerCase().contains(lowerQuery) ||
                 task.checkedBy.toLowerCase().contains(lowerQuery);
        }
        
        return true;
      }).toList();
    } catch (e) {
      throw CacheException('Failed to search cached tasks: $e');
    }
  }

  /// Get cached default rate per piece
  Future<double> getDefaultRatePerPiece() async {
    try {
      return _prefs.getDouble(_defaultRateKey) ?? 2.0;
    } catch (e) {
      return 2.0; // Return default rate on error
    }
  }

  /// Cache default rate per piece
  Future<void> cacheDefaultRatePerPiece(double rate) async {
    try {
      await _prefs.setDouble(_defaultRateKey, rate);
    } catch (e) {
      throw CacheException('Failed to cache default rate: $e');
    }
  }

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime() async {
    try {
      final syncTimeString = _prefs.getString(_lastSyncKey);
      if (syncTimeString == null) return null;
      return DateTime.parse(syncTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      await _prefs.remove(_tasksKey);
      await _prefs.remove(_lastSyncKey);
    } catch (e) {
      throw CacheException('Failed to clear cache: $e');
    }
  }

  /// Check if cache is stale (older than specified duration)
  Future<bool> isCacheStale({Duration maxAge = const Duration(hours: 1)}) async {
    try {
      final lastSync = await getLastSyncTime();
      if (lastSync == null) return true;
      
      return DateTime.now().difference(lastSync) > maxAge;
    } catch (e) {
      return true; // Consider stale on error
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final tasks = await getAllTasks();
      final lastSync = await getLastSyncTime();
      final defaultRate = await getDefaultRatePerPiece();
      
      return {
        'total_tasks': tasks.length,
        'pending_tasks': tasks.where((t) => t.status == NumberingTaskStatus.pending).length,
        'in_progress_tasks': tasks.where((t) => t.status == NumberingTaskStatus.inProgress).length,
        'completed_tasks': tasks.where((t) => t.status == NumberingTaskStatus.completed).length,
        'sent_to_munda_tasks': tasks.where((t) => t.status == NumberingTaskStatus.sentToMunda).length,
        'last_sync': lastSync?.toIso8601String(),
        'default_rate': defaultRate,
        'cache_size_kb': _calculateCacheSize(),
      };
    } catch (e) {
      return {
        'error': 'Failed to get cache stats: $e',
      };
    }
  }

  /// Calculate approximate cache size in KB
  int _calculateCacheSize() {
    try {
      final tasksJson = _prefs.getString(_tasksKey) ?? '';
      return (tasksJson.length / 1024).round();
    } catch (e) {
      return 0;
    }
  }

  /// Batch operations for better performance
  Future<void> batchUpdateTasks(List<NumberingTaskModel> tasks) async {
    try {
      final allTasks = await getAllTasks();
      
      for (final task in tasks) {
        final existingIndex = allTasks.indexWhere((t) => t.id == task.id);
        if (existingIndex >= 0) {
          allTasks[existingIndex] = task;
        } else {
          allTasks.add(task);
        }
      }
      
      await cacheTasks(allTasks);
    } catch (e) {
      throw CacheException('Failed to batch update tasks: $e');
    }
  }

  /// Get tasks that need to be synced (modified locally)
  Future<List<NumberingTaskModel>> getTasksNeedingSync() async {
    try {
      // For now, return empty list as we don't track local modifications
      // This could be enhanced to track which tasks were modified offline
      return [];
    } catch (e) {
      throw CacheException('Failed to get tasks needing sync: $e');
    }
  }

  /// Mark task as needing sync
  Future<void> markTaskForSync(String taskId) async {
    try {
      // Implementation for tracking tasks that need sync
      // This could use a separate key to store task IDs that need syncing
      final syncTasksKey = 'tasks_needing_sync';
      final syncTasksJson = _prefs.getString(syncTasksKey) ?? '[]';
      final List<dynamic> syncTasks = json.decode(syncTasksJson);
      
      if (!syncTasks.contains(taskId)) {
        syncTasks.add(taskId);
        await _prefs.setString(syncTasksKey, json.encode(syncTasks));
      }
    } catch (e) {
      throw CacheException('Failed to mark task for sync: $e');
    }
  }

  /// Remove task from sync queue
  Future<void> removeTaskFromSyncQueue(String taskId) async {
    try {
      final syncTasksKey = 'tasks_needing_sync';
      final syncTasksJson = _prefs.getString(syncTasksKey) ?? '[]';
      final List<dynamic> syncTasks = json.decode(syncTasksJson);
      
      syncTasks.remove(taskId);
      await _prefs.setString(syncTasksKey, json.encode(syncTasks));
    } catch (e) {
      throw CacheException('Failed to remove task from sync queue: $e');
    }
  }
}
