import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hm_collection/core/injection/injection_container.dart';
import 'package:hm_collection/features/auth/presentation/bloc/firebase_auth_bloc.dart';
import 'package:hm_collection/features/department/presentation/bloc/department_bloc.dart';
import 'package:hm_collection/features/financial_management/presentation/bloc/financial_bloc.dart';
import 'package:hm_collection/features/production_planning/presentation/bloc/production_bloc.dart';
import 'package:hm_collection/features/quality_control/presentation/bloc/quality_bloc.dart';
import '../../../resource_management/presentation/bloc/resource_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../financial_management/presentation/pages/financial_dashboard_page.dart';
import '../../../inventory_management/presentation/pages/inventory_page.dart';
import '../../../manufacturing/presentation/pages/manufacturing_dashboard_page.dart';
import '../../../order_management/presentation/pages/orders_page.dart';
import '../../../production_planning/presentation/pages/production_page.dart';
import '../../../quality_control/presentation/pages/quality_page.dart';
import '../../../resource_management/presentation/pages/resource_page.dart';

/// Dashboard navigation drawer
class DashboardDrawer extends StatelessWidget {
  final User user;
  final void Function(String route)? onNavigate;

  const DashboardDrawer({
    super.key,
    required this.user,
    this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<DepartmentBloc>()..add(FetchDepartments()),
      child: Drawer(
        child: Column(
          children: [
            _buildDrawerHeader(),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  _buildNavigationSection(context),
                  const Divider(),
                  _buildCoreModulesSection(context),
                  const Divider(),
                  _buildManagementSection(context),
                  const Divider(),
                  _buildReportsSection(context),
                  const Divider(),
                  _buildSettingsSection(context),
                ],
              ),
            ),
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return DrawerHeader(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // Reduced vertical padding
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withOpacity(0.8),
          ],
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min, // Use min to prevent overflow
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CircleAvatar(
                      radius: 25, // Slightly smaller avatar
                      backgroundColor: Colors.white,
                      backgroundImage: user.profileImageUrl != null
                          ? NetworkImage(user.profileImageUrl!)
                          : null,
                      child: user.profileImageUrl == null
                          ? Text(
                              user.initials,
                              style: const TextStyle(
                                fontSize: 16, // Slightly smaller font
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user.displayName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16, // Slightly smaller font
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            user.role.name,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 13, // Slightly smaller font
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            user.department.name,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNavigationSection(BuildContext context) {
    return Column(
      children: [
        _buildSectionHeader('Navigation'),
        _buildDrawerItem(
          context,
          icon: Icons.dashboard,
          title: 'Dashboard',
          onTap: () => _navigateTo(context, '/dashboard'),
          isSelected: true,
        ),
      ],
    );
  }

  Widget _buildManagementSection(BuildContext context) {
    if (!_hasAnyManagementPermission()) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        _buildSectionHeader('Administration'),
        if (_hasPermission('users.read'))
          _buildDrawerItem(
            context,
            icon: Icons.people,
            title: 'User Management',
            onTap: () => _navigateTo(context, '/users'),
          ),
        if (_hasPermission('departments.read'))
          BlocBuilder<DepartmentBloc, DepartmentState>(
            builder: (context, state) {
              if (state is DepartmentLoaded) {
                return ExpansionTile(
                  leading: const Icon(Icons.business),
                  title: const Text('Departments'),
                  children: state.departments
                      .map((dept) => _buildDrawerItem(
                            context,
                            icon: Icons.circle_outlined, // Smaller icon
                            title: dept.name,
                            onTap: () => _navigateTo(context, '/departments/${dept.id}'),
                          ))
                      .toList(),
                );
              } else if (state is DepartmentLoading) {
                return const ListTile(
                  leading: CircularProgressIndicator(),
                  title: Text('Loading Departments...'),
                );
              } else {
                return _buildDrawerItem(
                  context,
                  icon: Icons.business,
                  title: 'Departments',
                  onTap: () => _navigateTo(context, '/departments'),
                );
              }
            },
          ),
        if (_hasPermission('roles.read'))
          _buildDrawerItem(
            context,
            icon: Icons.admin_panel_settings,
            title: 'Roles & Permissions',
            onTap: () => _navigateTo(context, '/roles'),
          ),
        if (_hasPermission('audit.read'))
          _buildDrawerItem(
            context,
            icon: Icons.history,
            title: 'Audit Logs',
            onTap: () => _navigateTo(context, '/audit-logs'),
          ),
      ],
    );
  }

  Widget _buildReportsSection(BuildContext context) {
    if (!_hasPermission('reports.read')) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        _buildSectionHeader('Reports & Analytics'),
        _buildDrawerItem(
          context,
          icon: Icons.assessment,
          title: 'Reports',
          onTap: () => _navigateTo(context, '/analytics/reports'),
        ),
        _buildDrawerItem(
          context,
          icon: Icons.analytics_outlined,
          title: 'Analytics',
          onTap: () => _navigateTo(context, '/analytics'),
        ),
        _buildDrawerItem(
          context,
          icon: Icons.trending_up,
          title: 'KPIs & Dashboards',
          onTap: () => _navigateTo(context, '/analytics/dashboards'),
        ),
      ],
    );
  }

  Widget _buildSettingsSection(BuildContext context) {
    return Column(
      children: [
        _buildSectionHeader('Settings'),
        _buildDrawerItem(
          context,
          icon: Icons.settings,
          title: 'Settings',
          onTap: () => _navigateTo(context, '/settings'),
        ),
        _buildDrawerItem(
          context,
          icon: Icons.security,
          title: 'Two-Factor Authentication',
          onTap: () => _navigateTo(context, '/setup-2fa'),
        ),
        _buildDrawerItem(
          context,
          icon: Icons.translate,
          title: 'Language',
          onTap: () => _navigateTo(context, '/language'),
        ),
        _buildDrawerItem(
          context,
          icon: Icons.dark_mode,
          title: 'Appearance',
          onTap: () => _navigateTo(context, '/appearance'),
        ),
        _buildDrawerItem(
          context,
          icon: Icons.help_outline,
          title: 'Help & Support',
          onTap: () => _navigateTo(context, '/help'),
        ),
        _buildDrawerItem(
          context,
          icon: Icons.info_outline,
          title: 'About',
          onTap: () => _navigateTo(context, '/about'),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppColors.primary : AppColors.textSecondary,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? AppColors.primary : AppColors.textPrimary,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: AppColors.primary.withOpacity(0.1),
      onTap: () {
        Navigator.of(context).pop(); // Close drawer
        onTap();
      },
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.border.withOpacity(0.3),
          ),
        ),
      ),
      child: Column(
        children: [
          ListTile(
            leading: const Icon(
              Icons.logout,
              color: AppColors.error,
            ),
            title: const Text(
              'Logout',
              style: TextStyle(
                color: AppColors.error,
                fontWeight: FontWeight.w500,
              ),
            ),
            onTap: () => _showLogoutDialog(context),
          ),
          const SizedBox(height: 8),
          const Text(
            'HM Collection v1.0.0',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  bool _hasPermission(String permission) {
    return user.permissions.contains(permission);
  }

  Widget _buildCoreModulesSection(BuildContext context) {
    return Column(
      children: [
        _buildSectionHeader('Core Modules'),
        // if (_hasPermission('orders.read'))
          _buildDrawerItem(
            context,
            icon: Icons.shopping_bag,
            title: 'Order Management',
            onTap: () => Navigator.of(context).push(MaterialPageRoute(builder: (context) => const OrdersPage())),
          ),
        // if (_hasPermission('production.read'))fl
          _buildDrawerItem(
            context,
            icon: Icons.factory_outlined,
            title: 'Production Planning',
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => BlocProvider(
                  create: (context) => getIt<ProductionBloc>()..add(const LoadProductionOrdersRequested()),
                  child: const ProductionPage(),
                ),
              ),
            ),
          ),
        // if (_hasPermission('inventory.read'))
          _buildDrawerItem(
            context,
            icon: Icons.inventory_2,
            title: 'Inventory Management',
            onTap: () => Navigator.of(context).push(MaterialPageRoute(builder: (context) => const InventoryPage())),
          ),
        // if (_hasPermission('quality.read'))
          _buildDrawerItem(
            context,
            icon: Icons.verified,
            title: 'Quality Control',
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => BlocProvider(
                  create: (context) => getIt<QualityBloc>()..add(const LoadQualityInspectionsRequested()),
                  child: const QualityPage(),
                ),
              ),
            ),
          ),
        // if (_hasPermission('manufacturing.read'))
          _buildDrawerItem(
            context,
            icon: Icons.precision_manufacturing,
            title: 'Manufacturing',
            onTap: () => Navigator.of(context).push(MaterialPageRoute(builder: (context) => const ManufacturingDashboardPage())),
          ),
        // if (_hasPermission('resources.read'))
          _buildDrawerItem(
            context,
            icon: Icons.workspaces_outline,
            title: 'Resource Management',
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => BlocProvider(
                  create: (context) => getIt<ResourceBloc>()..add(const LoadMachinesRequested()),
                  child: const ResourcePage(),
                ),
              ),
            ),
          ),
        // if (_hasPermission('finance.read'))
          _buildDrawerItem(
            context,
            icon: Icons.account_balance_wallet,
            title: 'Financial Management',
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => BlocProvider(
                  create: (context) => getIt<FinancialBloc>()..add(const GetFinancialSummaryRequested()),
                  child: const FinancialDashboardPage(),
                ),
              ),
            ),
          ),
      ],
    );
  }

  bool _hasAnyManagementPermission() {
    return _hasPermission('users.read') ||
           _hasPermission('departments.read') ||
           _hasPermission('roles.read');
  }

  void _navigateTo(BuildContext context, String route) {
    if (onNavigate != null) {
      onNavigate!(route);
      return;
    }
    // Default behavior: show a toast/snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Navigation to $route coming soon')),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              context.read<FirebaseAuthBloc>().add(const FirebaseSignOutRequested());
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
