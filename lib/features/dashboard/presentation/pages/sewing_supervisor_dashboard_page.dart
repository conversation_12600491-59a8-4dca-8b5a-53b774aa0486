import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Sewing Supervisor Dashboard Page - Sewing line supervision
class SewingSupervisorDashboardPage extends StatelessWidget {
  const SewingSupervisorDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sewing Supervisor Dashboard'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildSewingSupervisorDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildSewingSupervisorDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildLineOverview(),
          const SizedBox(height: 24),
          _buildSupervisorActions(context),
          const SizedBox(height: 24),
          _buildMyTeam(),
          const SizedBox(height: 24),
          _buildProductionTargets(),
          const SizedBox(height: 24),
          _buildQualityAlerts(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.teal,
            Colors.teal.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Supervisor'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Sewing line supervision',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.supervisor_account,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildLineOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'My Line Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Line Efficiency',
              value: '92%',
              icon: Icons.trending_up,
              color: Colors.green,
              trend: '+5%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Operators',
              value: '12',
              icon: Icons.people,
              color: Colors.blue,
            ),
            DashboardMetricCard(
              title: 'Daily Target',
              value: '180',
              icon: Icons.flag,
              color: Colors.purple,
            ),
            DashboardMetricCard(
              title: 'Completed',
              value: '165',
              icon: Icons.check_circle,
              color: Colors.orange,
              trend: '92%',
              isPositiveTrend: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSupervisorActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Supervisor Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Assign Tasks',
              subtitle: 'Assign work to operators',
              icon: Icons.assignment_ind,
              color: Colors.blue,
              onTap: () {
                // Navigate to task assignment
              },
            ),
            DashboardCard(
              title: 'Monitor Progress',
              subtitle: 'Track line performance',
              icon: Icons.monitor,
              color: Colors.green,
              onTap: () {
                // Navigate to progress monitoring
              },
            ),
            DashboardCard(
              title: 'Quality Check',
              subtitle: 'Inspect work quality',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () {
                // Navigate to quality check
              },
            ),
            DashboardCard(
              title: 'Team Schedule',
              subtitle: 'Manage operator schedules',
              icon: Icons.schedule,
              color: Colors.purple,
              onTap: () {
                // Navigate to scheduling
              },
            ),
            DashboardCard(
              title: 'Training',
              subtitle: 'Provide operator training',
              icon: Icons.school,
              color: Colors.teal,
              onTap: () {
                // Navigate to training
              },
            ),
            DashboardCard(
              title: 'Reports',
              subtitle: 'Generate line reports',
              icon: Icons.analytics,
              color: Colors.indigo,
              onTap: () {
                // Navigate to reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMyTeam() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.people,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'My Team Performance',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to team details
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildOperatorItem('Alice Johnson', 'Operator', '95%', 'Excellent', Colors.green),
            _buildOperatorItem('Bob Smith', 'Operator', '88%', 'Good', Colors.blue),
            _buildOperatorItem('Carol Davis', 'Operator', '92%', 'Excellent', Colors.green),
            _buildOperatorItem('David Wilson', 'Operator', '85%', 'Good', Colors.blue),
            _buildOperatorItem('Eva Brown', 'Operator', '78%', 'Needs Improvement', Colors.orange),
          ],
        ),
      ),
    );
  }

  Widget _buildOperatorItem(String name, String role, String efficiency, String performance, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: color.withOpacity(0.2),
            child: Text(
              name[0],
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  role,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                efficiency,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                performance,
                style: TextStyle(
                  color: color,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductionTargets() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.flag,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Production Targets',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'On Track',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTargetItem('Hourly Target', '15 pieces', '14 pieces', 0.93, Colors.green),
            const SizedBox(height: 8),
            _buildTargetItem('Daily Target', '180 pieces', '165 pieces', 0.92, Colors.blue),
            const SizedBox(height: 8),
            _buildTargetItem('Weekly Target', '900 pieces', '825 pieces', 0.92, Colors.purple),
            const SizedBox(height: 8),
            _buildTargetItem('Quality Target', '98%', '96%', 0.98, Colors.orange),
          ],
        ),
      ),
    );
  }

  Widget _buildTargetItem(String target, String goal, String actual, double progress, Color color) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            target,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            '$actual / $goal',
            style: const TextStyle(fontSize: 12),
          ),
        ),
        Expanded(
          flex: 2,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${(progress * 100).toInt()}%',
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildQualityAlerts() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.warning,
                  color: Colors.orange,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Quality Alerts',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '2 Issues',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildAlertItem(
              'Stitch Quality',
              'Operator Alice - Uneven stitching detected',
              'High',
              Colors.red,
              Icons.error,
            ),
            _buildAlertItem(
              'Thread Tension',
              'Machine #5 - Thread tension needs adjustment',
              'Medium',
              Colors.orange,
              Icons.warning,
            ),
            _buildAlertItem(
              'Seam Alignment',
              'Operator Bob - Seam alignment issue resolved',
              'Resolved',
              Colors.green,
              Icons.check_circle,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(String issue, String description, String priority, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  issue,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              priority,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
