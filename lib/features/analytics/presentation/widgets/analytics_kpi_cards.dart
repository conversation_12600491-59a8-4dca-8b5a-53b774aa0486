import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/animated_counter.dart';

/// Analytics KPI cards widget
class AnalyticsKpiCards extends StatelessWidget {
  final Map<String, dynamic> data;

  const AnalyticsKpiCards({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Primary KPIs
        Row(
          children: [
            Expanded(
              child: _buildKpiCard(
                'Production Volume',
                data['production_volume']?.toDouble() ?? 0.0,
                Icons.precision_manufacturing,
                AppColors.primary,
                'units',
                _getProductionTrend(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKpiCard(
                'Quality Score',
                data['quality_score']?.toDouble() ?? 0.0,
                Icons.verified,
                AppColors.success,
                '%',
                _getQualityTrend(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKpiCard(
                'Efficiency Rate',
                data['efficiency_rate']?.toDouble() ?? 0.0,
                Icons.speed,
                AppColors.warning,
                '%',
                _getEfficiencyTrend(),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Financial KPIs
        Row(
          children: [
            Expanded(
              child: _buildKpiCard(
                'Revenue',
                data['revenue']?.toDouble() ?? 0.0,
                Icons.attach_money,
                AppColors.success,
                '\$',
                _getRevenueTrend(),
                isLarge: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKpiCard(
                'Profit Margin',
                data['profit_margin']?.toDouble() ?? 0.0,
                Icons.trending_up,
                AppColors.primary,
                '%',
                _getProfitMarginTrend(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKpiCard(
                'Cost per Unit',
                data['cost_per_unit']?.toDouble() ?? 0.0,
                Icons.account_balance,
                AppColors.error,
                '\$',
                _getCostTrend(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKpiCard(
    String title,
    double value,
    IconData icon,
    Color color,
    String unit,
    TrendData trend, {
    bool isLarge = false,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 24 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: isLarge ? 32 : 24,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: trend.isPositive ? AppColors.success.withOpacity(0.1) : AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        trend.isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                        color: trend.isPositive ? AppColors.success : AppColors.error,
                        size: 12,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        '${trend.percentage.toStringAsFixed(1)}%',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: trend.isPositive ? AppColors.success : AppColors.error,
                          fontWeight: FontWeight.w600,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: isLarge ? 16 : 12),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            AnimatedCounter(
              value: value,
              duration: const Duration(milliseconds: 1500),
              builder: (context, animatedValue) {
                return Text(
                  '${unit == '\$' ? unit : ''}${_formatValue(animatedValue)}${unit != '\$' ? unit : ''}',
                  style: (isLarge ? AppTextStyles.headlineMedium : AppTextStyles.headlineSmall).copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                );
              },
            ),
            if (isLarge) ...[
              const SizedBox(height: 8),
              Text(
                trend.description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatValue(double value) {
    if (value.abs() >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value.abs() >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(value % 1 == 0 ? 0 : 1);
    }
  }

  TrendData _getProductionTrend() {
    return TrendData(
      percentage: 5.2,
      isPositive: true,
      description: 'vs last month',
    );
  }

  TrendData _getQualityTrend() {
    return TrendData(
      percentage: 2.1,
      isPositive: true,
      description: 'vs last month',
    );
  }

  TrendData _getEfficiencyTrend() {
    return TrendData(
      percentage: 1.8,
      isPositive: true,
      description: 'vs last month',
    );
  }

  TrendData _getRevenueTrend() {
    return TrendData(
      percentage: 8.5,
      isPositive: true,
      description: 'Strong growth this quarter',
    );
  }

  TrendData _getProfitMarginTrend() {
    return TrendData(
      percentage: 3.2,
      isPositive: true,
      description: 'vs last month',
    );
  }

  TrendData _getCostTrend() {
    return TrendData(
      percentage: 2.1,
      isPositive: false,
      description: 'vs last month',
    );
  }
}

/// Trend data class
class TrendData {
  final double percentage;
  final bool isPositive;
  final String description;

  const TrendData({
    required this.percentage,
    required this.isPositive,
    required this.description,
  });
}

/// Analytics chart widget
class AnalyticsChartWidget extends StatelessWidget {
  final String title;
  final String chartType;
  final Map<String, dynamic> data;
  final VoidCallback? onRefresh;

  const AnalyticsChartWidget({
    super.key,
    required this.title,
    required this.chartType,
    required this.data,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    _buildChartTypeButton('Line', chartType == 'line'),
                    const SizedBox(width: 8),
                    _buildChartTypeButton('Bar', chartType == 'bar'),
                    const SizedBox(width: 8),
                    _buildChartTypeButton('Area', chartType == 'area'),
                    const SizedBox(width: 8),
                    if (onRefresh != null)
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: onRefresh,
                        tooltip: 'Refresh Data',
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Mock chart representation
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildMockChart(),
            ),
            
            const SizedBox(height: 16),
            
            // Chart legend and stats
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildChartStat('Avg', '85.2'),
                _buildChartStat('Max', '98.5'),
                _buildChartStat('Min', '72.1'),
                _buildChartStat('Trend', '+5.2%'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartTypeButton(String type, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary : AppColors.surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        type,
        style: AppTextStyles.bodySmall.copyWith(
          color: isSelected ? Colors.white : AppColors.textSecondary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMockChart() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(12, (index) {
          final height = 50 + (index * 10) + (index % 3 * 20);
          return Container(
            width: 12,
            height: height.toDouble(),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.7 + (index % 3 * 0.1)),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(2)),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildChartStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}

/// Analytics table widget
class AnalyticsTableWidget extends StatelessWidget {
  final String title;
  final List<String> columns;
  final List<Map<String, dynamic>> rows;
  final VoidCallback? onExport;

  const AnalyticsTableWidget({
    super.key,
    required this.title,
    required this.columns,
    required this.rows,
    this.onExport,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.filter_list),
                      onPressed: () => _showFilters(),
                      tooltip: 'Filter',
                    ),
                    IconButton(
                      icon: const Icon(Icons.sort),
                      onPressed: () => _showSortOptions(),
                      tooltip: 'Sort',
                    ),
                    if (onExport != null)
                      IconButton(
                        icon: const Icon(Icons.download),
                        onPressed: onExport,
                        tooltip: 'Export',
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Table
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.surface),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                    ),
                    child: Row(
                      children: columns.map((column) => Expanded(
                        child: Text(
                          column,
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      )).toList(),
                    ),
                  ),
                  
                  // Rows
                  ...rows.take(5).map((row) => Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: AppColors.surface),
                      ),
                    ),
                    child: Row(
                      children: columns.map((column) => Expanded(
                        child: Text(
                          row[column]?.toString() ?? '-',
                          style: AppTextStyles.bodyMedium,
                        ),
                      )).toList(),
                    ),
                  )),
                ],
              ),
            ),
            
            if (rows.length > 5) ...[
              const SizedBox(height: 12),
              Center(
                child: TextButton(
                  onPressed: () => _showAllRows(),
                  child: Text(
                    'View All ${rows.length} Rows',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showFilters() {
    // Show filters dialog
  }

  void _showSortOptions() {
    // Show sort options dialog
  }

  void _showAllRows() {
    // Navigate to full table view
  }
}
