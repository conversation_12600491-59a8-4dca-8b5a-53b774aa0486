import 'package:equatable/equatable.dart';

/// Represents the definition of a dimension that can be used for data segmentation in analytics.
class DimensionDefinition extends Equatable {
  /// Unique identifier for the dimension
  final String id;

  /// Display name of the dimension
  final String name;

  /// Description of what the dimension represents
  final String description;

  /// The data type of the dimension (e.g., 'string', 'number', 'date', 'boolean')
  final String dataType;

  /// The category this dimension belongs to (e.g., 'time', 'location', 'product')
  final String category;

  /// Whether this dimension is currently active/enabled
  final bool isActive;

  /// Creates a new [DimensionDefinition]
  const DimensionDefinition({
    required this.id,
    required this.name,
    required this.description,
    required this.dataType,
    required this.category,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [id, name, description, dataType, category, isActive];

  /// Creates a copy of this dimension definition with the given fields replaced with the new values
  DimensionDefinition copyWith({
    String? id,
    String? name,
    String? description,
    String? dataType,
    String? category,
    bool? isActive,
  }) {
    return DimensionDefinition(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      dataType: dataType ?? this.dataType,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
    );
  }
}
