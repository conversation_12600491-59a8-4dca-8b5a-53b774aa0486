import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/animated_counter.dart';
import '../../../../shared/widgets/progress_indicator.dart';
import '../../domain/entities/financial_entities.dart';

/// Budget overview card widget
class BudgetOverviewCard extends StatelessWidget {
  final List<Budget> budgets;

  const BudgetOverviewCard({
    super.key,
    required this.budgets,
  });

  @override
  Widget build(BuildContext context) {
    if (budgets.isEmpty) {
      return _buildEmptyState();
    }

    final totalAllocated = budgets.fold(0.0, (sum, b) => sum + b.totalAmount);
    final totalSpent = budgets.fold(0.0, (sum, b) => sum + b.metrics.spentAmount);
    final totalRemaining = budgets.fold(0.0, (sum, b) => sum + b.metrics.remainingAmount);
    final averageUtilization = budgets.fold(0.0, (sum, b) => sum + b.metrics.utilizationPercentage) / budgets.length;
    final exceededCount = budgets.where((b) => b.metrics.isExceeded).length;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Budget Overview',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (exceededCount > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.error.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '$exceededCount Exceeded',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.error,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Budget Summary
            Row(
              children: [
                Expanded(
                  child: _buildBudgetSummaryItem(
                    'Total Allocated',
                    totalAllocated,
                    Icons.account_balance_wallet,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildBudgetSummaryItem(
                    'Total Spent',
                    totalSpent,
                    Icons.money_off,
                    AppColors.error,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildBudgetSummaryItem(
                    'Remaining',
                    totalRemaining,
                    Icons.savings,
                    AppColors.success,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Overall Utilization
            Text(
              'Overall Utilization',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: CustomProgressIndicator(
                    progress: averageUtilization / 100,
                    backgroundColor: AppColors.surface,
                    color: _getUtilizationColor(averageUtilization),
                    height: 8,
                  ),
                ),
                const SizedBox(width: 12),
                AnimatedPercentageCounter(
                  percentage: averageUtilization,
                  duration: const Duration(milliseconds: 1000),
                  textStyle: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getUtilizationColor(averageUtilization),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Budget List
            Text(
              'Active Budgets',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            
            ...budgets.take(3).map((budget) => _buildBudgetItem(budget)),
            
            if (budgets.length > 3)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: TextButton(
                  onPressed: () => _viewAllBudgets(context),
                  child: Text(
                    'View All ${budgets.length} Budgets',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No Budgets Available',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first budget to start tracking expenses',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: () => _createBudget(),
              icon: const Icon(Icons.add),
              label: const Text('Create Budget'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetSummaryItem(
    String label,
    double value,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        AnimatedCounter(
          value: value,
          duration: const Duration(milliseconds: 1200),
          prefix: '\$',
          decimalPlaces: 0,
          textStyle: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetItem(Budget budget) {
    final utilizationColor = _getUtilizationColor(budget.metrics.utilizationPercentage);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: budget.metrics.isExceeded 
            ? Border.all(color: AppColors.error.withOpacity(0.3))
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  budget.budgetName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (budget.metrics.isExceeded)
                const Icon(
                  Icons.warning,
                  color: AppColors.error,
                  size: 16,
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${_budgetTypeDisplayName(budget.type)} • ${_budgetPeriodDisplayName(budget.period)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '\$${_formatCurrency(budget.metrics.spentAmount)} / \$${_formatCurrency(budget.totalAmount)}',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${budget.metrics.utilizationPercentage.toStringAsFixed(1)}%',
                style: AppTextStyles.bodySmall.copyWith(
                  color: utilizationColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          
          CustomProgressIndicator(
            progress: (budget.metrics.utilizationPercentage / 100).clamp(0.0, 1.0),
            backgroundColor: AppColors.background,
            color: utilizationColor,
            height: 4,
          ),
        ],
      ),
    );
  }

  Color _getUtilizationColor(double utilization) {
    if (utilization > 100) return AppColors.error;
    if (utilization > 90) return AppColors.warning;
    if (utilization > 75) return AppColors.warning.withOpacity(0.8);
    return AppColors.success;
  }

  String _formatCurrency(double value) {
    if (value.abs() >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value.abs() >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  void _viewAllBudgets(BuildContext context) {
    // Navigate to all budgets page
  }

  void _createBudget() {
    // Navigate to create budget page
  }

  // Helpers to display enum values (since BudgetType/BudgetPeriod don't define displayName)
  String _budgetTypeDisplayName(BudgetType type) {
    switch (type) {
      case BudgetType.operational:
        return 'Operational';
      case BudgetType.capital:
        return 'Capital';
      case BudgetType.project:
        return 'Project';
      case BudgetType.department:
        return 'Department';
      case BudgetType.costCenter:
        return 'Cost Center';
    }
  }

  String _budgetPeriodDisplayName(BudgetPeriod period) {
    switch (period) {
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.quarterly:
        return 'Quarterly';
      case BudgetPeriod.semiAnnual:
        return 'Semi-Annual';
      case BudgetPeriod.annual:
        return 'Annual';
      case BudgetPeriod.custom:
        return 'Custom';
    }
  }
}

/// Recent transactions list widget
class RecentTransactionsList extends StatelessWidget {
  final List<FinancialTransaction> transactions;
  final VoidCallback? onViewAll;

  const RecentTransactionsList({
    super.key,
    required this.transactions,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    if (transactions.isEmpty) {
      return _buildEmptyState();
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Transactions',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: Text(
                      'View All',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            ...transactions.map((transaction) => _buildTransactionItem(transaction)),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No Transactions Yet',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start recording your financial transactions',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(FinancialTransaction transaction) {
    final isRevenue = transaction.type == TransactionType.revenue;
    final color = isRevenue ? AppColors.success : AppColors.error;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              isRevenue ? Icons.arrow_upward : Icons.arrow_downward,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  '${_transactionCategoryDisplayName(transaction.category)} • ${_formatDate(transaction.transactionDate)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isRevenue ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 2),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getStatusColor(transaction.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _transactionStatusDisplayName(transaction.status),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _getStatusColor(transaction.status),
                    fontWeight: FontWeight.w500,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _transactionCategoryDisplayName(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.sales:
        return 'Sales';
      case TransactionCategory.materialPurchase:
        return 'Material Purchase';
      case TransactionCategory.laborCost:
        return 'Labor Cost';
      case TransactionCategory.overhead:
        return 'Overhead';
      case TransactionCategory.equipment:
        return 'Equipment';
      case TransactionCategory.maintenance:
        return 'Maintenance';
      case TransactionCategory.utilities:
        return 'Utilities';
      case TransactionCategory.rent:
        return 'Rent';
      case TransactionCategory.insurance:
        return 'Insurance';
      case TransactionCategory.marketing:
        return 'Marketing';
      case TransactionCategory.administration:
        return 'Administration';
      case TransactionCategory.other:
        return 'Other';
    }
  }

  String _transactionStatusDisplayName(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.draft:
        return 'Draft';
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.approved:
        return 'Approved';
      case TransactionStatus.rejected:
        return 'Rejected';
      case TransactionStatus.posted:
        return 'Posted';
      case TransactionStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color _getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.approved:
      case TransactionStatus.posted:
        return AppColors.success;
      case TransactionStatus.pending:
        return AppColors.warning;
      case TransactionStatus.rejected:
      case TransactionStatus.cancelled:
        return AppColors.error;
      case TransactionStatus.draft:
        return AppColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) return 'Today';
    if (difference == 1) return 'Yesterday';
    if (difference < 7) return '${difference}d ago';
    
    return '${date.day}/${date.month}/${date.year}';
  }
}
