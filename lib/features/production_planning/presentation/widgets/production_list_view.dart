import 'package:flutter/material.dart';

import '../../domain/entities/production_entities.dart';

/// List view for production orders
class ProductionListView extends StatelessWidget {
  final List<ProductionOrder> orders;
  final List<String> selectedOrderIds;
  final void Function(String orderId)? onOrderTap;
  final void Function(String orderId)? onOrderLongPress;
  final void Function(List<String> selectedIds)? onSelectionChanged;
  final void Function(String orderId, ProductionStatus status)? onStatusChanged;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const ProductionListView({
    Key? key,
    required this.orders,
    this.selectedOrderIds = const [],
    this.onOrderTap,
    this.onOrderLongPress,
    this.onSelectionChanged,
    this.onStatusChanged,
    this.onLoadMore,
    this.hasMore = false,
    this.isLoading = false,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading && orders.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.precision_manufacturing, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No production orders found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Production orders will appear here when they are created',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final itemCount = orders.length + (hasMore ? 1 : 0);

    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: ListView.builder(
        itemCount: itemCount,
        itemBuilder: (context, index) {
          if (hasMore && index == orders.length) {
            return _LoadMoreFooter(onLoadMore: onLoadMore);
          }

          final order = orders[index];
          final isSelected = selectedOrderIds.contains(order.id);

          return ProductionOrderListTile(
            order: order,
            selected: isSelected,
            onTap: () => onOrderTap?.call(order.id),
            onLongPress: () => onOrderLongPress?.call(order.id),
            onSelectionToggled: (selected) {
              if (onSelectionChanged == null) return;
              final updated = [...selectedOrderIds];
              if (selected) {
                if (!updated.contains(order.id)) updated.add(order.id);
              } else {
                updated.remove(order.id);
              }
              onSelectionChanged!(updated);
            },
            onStatusSelected: (status) => onStatusChanged?.call(order.id, status),
          );
        },
      ),
    );
  }
}

/// Individual production order list tile
class ProductionOrderListTile extends StatelessWidget {
  final ProductionOrder order;
  final bool selected;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ValueChanged<bool>? onSelectionToggled;
  final ValueChanged<ProductionStatus>? onStatusSelected;

  const ProductionOrderListTile({
    Key? key,
    required this.order,
    this.selected = false,
    this.onTap,
    this.onLongPress,
    this.onSelectionToggled,
    this.onStatusSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        onTap: onTap,
        onLongPress: onLongPress,
        leading: Stack(
          alignment: Alignment.bottomRight,
          children: [
            CircleAvatar(
              backgroundColor: _getStatusColor(order.status),
              child: Icon(_getStatusIcon(order.status), color: Colors.white, size: 20),
            ),
            Checkbox(
              value: selected,
              onChanged: (v) => onSelectionToggled?.call(v ?? false),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ],
        ),
        title: Text(order.orderNumber, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Client: ${order.clientName}')
                .applyTextStyle(context, Theme.of(context).textTheme.bodyMedium),
            Text('Quantity: ${order.totalQuantity}')
                .applyTextStyle(context, Theme.of(context).textTheme.bodyMedium),
            Text('Status: ${order.status.displayName}')
                .applyTextStyle(context, Theme.of(context).textTheme.bodyMedium),
            Text('Due: ${_formatDate(order.plannedEndDate)}')
                .applyTextStyle(context, Theme.of(context).textTheme.bodyMedium),
          ],
        ),
        trailing: PopupMenuButton<ProductionStatus>(
          onSelected: onStatusSelected,
          itemBuilder: (context) => [
            const PopupMenuItem(value: ProductionStatus.planned, child: Text('Mark as Planned')),
            const PopupMenuItem(value: ProductionStatus.inProgress, child: Text('Mark as In Progress')),
            const PopupMenuItem(value: ProductionStatus.completed, child: Text('Mark as Completed')),
            const PopupMenuItem(value: ProductionStatus.cancelled, child: Text('Mark as Cancelled')),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  Color _getStatusColor(ProductionStatus status) {
    switch (status) {
      case ProductionStatus.planned:
        return Colors.blue;
      case ProductionStatus.scheduled:
        return Colors.indigo;
      case ProductionStatus.inProgress:
        return Colors.orange;
      case ProductionStatus.paused:
        return Colors.amber;
      case ProductionStatus.completed:
        return Colors.green;
      case ProductionStatus.cancelled:
        return Colors.red;
      case ProductionStatus.onHold:
        return Colors.purple;
    }
  }

  IconData _getStatusIcon(ProductionStatus status) {
    switch (status) {
      case ProductionStatus.planned:
        return Icons.schedule;
      case ProductionStatus.scheduled:
        return Icons.event;
      case ProductionStatus.inProgress:
        return Icons.play_arrow;
      case ProductionStatus.paused:
        return Icons.pause_circle_filled;
      case ProductionStatus.completed:
        return Icons.check;
      case ProductionStatus.cancelled:
        return Icons.cancel;
      case ProductionStatus.onHold:
        return Icons.pause_circle_outline;
    }
  }

  String _formatDate(DateTime date) => '${date.day}/${date.month}/${date.year}';
}

class _LoadMoreFooter extends StatelessWidget {
  final VoidCallback? onLoadMore;
  const _LoadMoreFooter({this.onLoadMore});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Center(
        child: OutlinedButton.icon(
          onPressed: onLoadMore,
          icon: const Icon(Icons.expand_more),
          label: const Text('Load more'),
        ),
      ),
    );
  }
}

extension _TextExt on Text {
  Text applyTextStyle(BuildContext context, TextStyle? style) => Text(data ?? '', style: style);
}