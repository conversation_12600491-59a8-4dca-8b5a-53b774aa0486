import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/analytics_entities.dart';

/// Analytics repository interface
abstract class AnalyticsRepository {
  // Dashboard Management
  Future<Either<Failure, ApiListResponse<AnalyticsDashboard>>> getDashboards({
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> getDashboardById(String dashboardId);

  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> createDashboard(CreateDashboardRequest request);

  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> updateDashboard(UpdateDashboardRequest request);

  Future<Either<Failure, ApiVoidResponse>> deleteDashboard(String dashboardId);

  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> cloneDashboard(String dashboardId, String newName);

  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> shareDashboard(ShareDashboardRequest request);

  // Widget Management
  Future<Either<Failure, ApiResponse<DashboardWidget>>> addWidget(AddWidgetRequest request);

  Future<Either<Failure, ApiResponse<DashboardWidget>>> updateWidget(UpdateWidgetRequest request);

  Future<Either<Failure, ApiVoidResponse>> removeWidget(String dashboardId, String widgetId);

  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> reorderWidgets(ReorderWidgetsRequest request);

  // Report Management
  Future<Either<Failure, ApiListResponse<AnalyticsReport>>> getReports({
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiResponse<AnalyticsReport>>> getReportById(String reportId);

  Future<Either<Failure, ApiResponse<AnalyticsReport>>> generateReport(GenerateReportRequest request);

  Future<Either<Failure, ApiResponse<AnalyticsReport>>> scheduleReport(ScheduleReportRequest request);

  Future<Either<Failure, ApiVoidResponse>> cancelReport(String reportId);

  Future<Either<Failure, ApiVoidResponse>> deleteReport(String reportId);

  // Data Analytics
  Future<Either<Failure, Map<String, dynamic>>> getKpiData({
    required List<String> metrics,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  });

  Future<Either<Failure, ChartData>> getChartData({
    required String chartType,
    required List<String> metrics,
    required List<String> dimensions,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  });

  Future<Either<Failure, TableData>> getTableData({
    required List<String> columns,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
    String? sortBy,
    SortOrder? sortOrder,
    PaginationParams? pagination,
  });

  // Performance Analytics
  Future<Either<Failure, Map<String, dynamic>>> getProductionMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  });

  Future<Either<Failure, Map<String, dynamic>>> getQualityMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  });

  Future<Either<Failure, Map<String, dynamic>>> getFinancialMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  });

  Future<Either<Failure, Map<String, dynamic>>> getInventoryMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? warehouseId,
    String? categoryId,
  });

  Future<Either<Failure, Map<String, dynamic>>> getEfficiencyMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? machineId,
  });

  // Trend Analysis
  Future<Either<Failure, List<DataPoint>>> getTrendData({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required String granularity, // hour, day, week, month
    Map<String, dynamic>? filters,
  });

  Future<Either<Failure, Map<String, dynamic>>> getComparativeAnalysis({
    required List<String> metrics,
    required DateTime currentPeriodStart,
    required DateTime currentPeriodEnd,
    required DateTime previousPeriodStart,
    required DateTime previousPeriodEnd,
    Map<String, dynamic>? filters,
  });

  // Predictive Analytics
  Future<Either<Failure, Map<String, dynamic>>> getForecast({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required int forecastPeriods,
    Map<String, dynamic>? parameters,
  });

  Future<Either<Failure, Map<String, dynamic>>> getAnomalyDetection({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? parameters,
  });

  // Real-time Analytics
  Future<Either<Failure, Map<String, dynamic>>> getRealTimeMetrics({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  });

  Stream<Map<String, dynamic>> getRealTimeUpdates({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  });

  // Export and Sharing
  Future<Either<Failure, String>> exportDashboard({
    required String dashboardId,
    required String format, // pdf, excel, csv, json
    Map<String, dynamic>? options,
  });

  Future<Either<Failure, String>> exportReport({
    required String reportId,
    required String format,
    Map<String, dynamic>? options,
  });

  Future<Either<Failure, String>> exportData({
    required String dataType,
    required Map<String, dynamic> parameters,
    required String format,
  });

  // Search and Discovery
  Future<Either<Failure, ApiListResponse<AnalyticsDashboard>>> searchDashboards({
    required String query,
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, ApiListResponse<AnalyticsReport>>> searchReports({
    required String query,
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  });

  Future<Either<Failure, List<String>>> getAvailableMetrics({
    String? category,
    String? dataSource,
  });

  Future<Either<Failure, List<String>>> getAvailableDimensions({
    String? category,
    String? dataSource,
  });
}

// Request classes

/// Create dashboard request
class CreateDashboardRequest {
  final String dashboardName;
  final String description;
  final DashboardType type;
  final DashboardLayout layout;
  final DashboardSettings settings;
  final List<String> sharedWithUserIds;
  final List<String> sharedWithRoleIds;
  final bool isPublic;

  const CreateDashboardRequest({
    required this.dashboardName,
    required this.description,
    required this.type,
    required this.layout,
    required this.settings,
    this.sharedWithUserIds = const [],
    this.sharedWithRoleIds = const [],
    this.isPublic = false,
  });
}

/// Update dashboard request
class UpdateDashboardRequest {
  final String dashboardId;
  final String? dashboardName;
  final String? description;
  final DashboardLayout? layout;
  final DashboardSettings? settings;
  final List<String>? sharedWithUserIds;
  final List<String>? sharedWithRoleIds;
  final bool? isPublic;

  const UpdateDashboardRequest({
    required this.dashboardId,
    this.dashboardName,
    this.description,
    this.layout,
    this.settings,
    this.sharedWithUserIds,
    this.sharedWithRoleIds,
    this.isPublic,
  });
}

/// Share dashboard request
class ShareDashboardRequest {
  final String dashboardId;
  final List<String> userIds;
  final List<String> roleIds;
  final bool makePublic;

  const ShareDashboardRequest({
    required this.dashboardId,
    this.userIds = const [],
    this.roleIds = const [],
    this.makePublic = false,
  });
}

/// Add widget request
class AddWidgetRequest {
  final String dashboardId;
  final String widgetType;
  final String title;
  final String? description;
  final WidgetPosition position;
  final WidgetSize size;
  final WidgetConfiguration configuration;
  final Map<String, dynamic> dataSource;
  final WidgetRefreshSettings refreshSettings;

  const AddWidgetRequest({
    required this.dashboardId,
    required this.widgetType,
    required this.title,
    this.description,
    required this.position,
    required this.size,
    required this.configuration,
    this.dataSource = const {},
    required this.refreshSettings,
  });
}

/// Update widget request
class UpdateWidgetRequest {
  final String dashboardId;
  final String widgetId;
  final String? title;
  final String? description;
  final WidgetPosition? position;
  final WidgetSize? size;
  final WidgetConfiguration? configuration;
  final Map<String, dynamic>? dataSource;
  final WidgetRefreshSettings? refreshSettings;
  final bool? isVisible;

  const UpdateWidgetRequest({
    required this.dashboardId,
    required this.widgetId,
    this.title,
    this.description,
    this.position,
    this.size,
    this.configuration,
    this.dataSource,
    this.refreshSettings,
    this.isVisible,
  });
}

/// Reorder widgets request
class ReorderWidgetsRequest {
  final String dashboardId;
  final List<WidgetOrder> widgetOrders;

  const ReorderWidgetsRequest({
    required this.dashboardId,
    required this.widgetOrders,
  });
}

/// Widget order
class WidgetOrder {
  final String widgetId;
  final int order;
  final WidgetPosition? position;

  const WidgetOrder({
    required this.widgetId,
    required this.order,
    this.position,
  });
}

/// Generate report request
class GenerateReportRequest {
  final String reportName;
  final String description;
  final AnalyticsReportType type;
  final ReportCategory category;
  final ReportParameters parameters;
  final ReportSettings settings;
  final List<String> recipients;
  final List<String> tags;

  const GenerateReportRequest({
    required this.reportName,
    required this.description,
    required this.type,
    required this.category,
    required this.parameters,
    required this.settings,
    this.recipients = const [],
    this.tags = const [],
  });
}

/// Schedule report request
class ScheduleReportRequest {
  final String reportName;
  final String description;
  final AnalyticsReportType type;
  final ReportCategory category;
  final ReportParameters parameters;
  final ReportSettings settings;
  final ScheduleConfiguration scheduleConfig;
  final List<String> recipients;
  final List<String> tags;

  const ScheduleReportRequest({
    required this.reportName,
    required this.description,
    required this.type,
    required this.category,
    required this.parameters,
    required this.settings,
    required this.scheduleConfig,
    this.recipients = const [],
    this.tags = const [],
  });
}

// Filter criteria classes

/// Dashboard filter criteria
class DashboardFilterCriteria {
  final DashboardType? type;
  final String? ownerId;
  final bool? isPublic;
  final bool? isDefault;
  final List<String>? tags;
  final DateTime? createdAfter;
  final DateTime? createdBefore;

  const DashboardFilterCriteria({
    this.type,
    this.ownerId,
    this.isPublic,
    this.isDefault,
    this.tags,
    this.createdAfter,
    this.createdBefore,
  });
}

/// Report filter criteria
class ReportFilterCriteria {
  final AnalyticsReportType? type;
  final ReportCategory? category;
  final ReportStatus? status;
  final String? generatedBy;
  final bool? isScheduled;
  final List<String>? tags;
  final DateTime? generatedAfter;
  final DateTime? generatedBefore;

  const ReportFilterCriteria({
    this.type,
    this.category,
    this.status,
    this.generatedBy,
    this.isScheduled,
    this.tags,
    this.generatedAfter,
    this.generatedBefore,
  });
}
