part of 'resource_bloc.dart';

/// Base resource state
abstract class ResourceState extends Equatable {
  const ResourceState();

  @override
  List<Object?> get props => [];
}

/// Initial resource state
class ResourceInitial extends ResourceState {
  const ResourceInitial();
}

/// Resource loading state
class ResourceLoading extends ResourceState {
  const ResourceLoading();
}

// Machine States

/// Machines loaded state
class MachinesLoaded extends ResourceState {
  final List<Machine> machines;
  final Pagination? pagination;
  final MachineFilterCriteria? filter;
  final bool isRefreshing;
  final List<String> selectedMachineIds;
  final ResourceViewMode viewMode;
  final String? sortBy;
  final bool sortAscending;

  const MachinesLoaded({
    required this.machines,
    this.pagination,
    this.filter,
    this.isRefreshing = false,
    this.selectedMachineIds = const [],
    this.viewMode = ResourceViewMode.list,
    this.sortBy,
    this.sortAscending = true,
  });

  /// Copy with new values
  MachinesLoaded copyWith({
    List<Machine>? machines,
    Pagination? pagination,
    MachineFilterCriteria? filter,
    bool? isRefreshing,
    List<String>? selectedMachineIds,
    ResourceViewMode? viewMode,
    String? sortBy,
    bool? sortAscending,
  }) {
    return MachinesLoaded(
      machines: machines ?? this.machines,
      pagination: pagination ?? this.pagination,
      filter: filter ?? this.filter,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      selectedMachineIds: selectedMachineIds ?? this.selectedMachineIds,
      viewMode: viewMode ?? this.viewMode,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  List<Object?> get props => [
        machines,
        pagination,
        filter,
        isRefreshing,
        selectedMachineIds,
        viewMode,
        sortBy,
        sortAscending,
      ];

  /// Check if machine is selected
  bool isMachineSelected(String machineId) {
    return selectedMachineIds.contains(machineId);
  }

  /// Get selected machines
  List<Machine> get selectedMachines {
    return machines.where((machine) => selectedMachineIds.contains(machine.id)).toList();
  }

  /// Get available machines count
  int get availableMachinesCount {
    return machines.where((machine) => machine.isAvailable).length;
  }

  /// Get machines in use count
  int get machinesInUseCount {
    return machines.where((machine) => machine.isInUse).length;
  }

  /// Get machines under maintenance count
  int get machinesUnderMaintenanceCount {
    return machines.where((machine) => machine.isUnderMaintenance).length;
  }

  /// Get machines due for maintenance count
  int get machinesDueForMaintenanceCount {
    return machines.where((machine) => machine.isMaintenanceDue).length;
  }
}

/// Machine details loaded state
class MachineDetailsLoaded extends ResourceState {
  final Machine machine;

  const MachineDetailsLoaded(this.machine);

  @override
  List<Object?> get props => [machine];
}

/// Machine created state
class MachineCreated extends ResourceState {
  final Machine machine;

  const MachineCreated(this.machine);

  @override
  List<Object?> get props => [machine];
}

/// Machine updated state
class MachineUpdated extends ResourceState {
  final Machine machine;

  const MachineUpdated(this.machine);

  @override
  List<Object?> get props => [machine];
}

/// Machine status updated state
class MachineStatusUpdated extends ResourceState {
  final Machine machine;

  const MachineStatusUpdated(this.machine);

  @override
  List<Object?> get props => [machine];
}

/// Machine schedules loaded state
class MachineSchedulesLoaded extends ResourceState {
  final List<MachineSchedule> schedules;
  final Pagination? pagination;
  final MachineScheduleFilterCriteria? filter;

  const MachineSchedulesLoaded({
    required this.schedules,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [schedules, pagination, filter];
}

/// Machine schedule created state
class MachineScheduleCreated extends ResourceState {
  final MachineSchedule schedule;

  const MachineScheduleCreated(this.schedule);

  @override
  List<Object?> get props => [schedule];
}

/// Machine maintenance records loaded state
class MachineMaintenanceRecordsLoaded extends ResourceState {
  final List<MaintenanceRecord> maintenanceRecords;
  final Pagination? pagination;
  final MachineMaintenanceFilterCriteria? filter;

  const MachineMaintenanceRecordsLoaded({
    required this.maintenanceRecords,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [maintenanceRecords, pagination, filter];
}

/// Maintenance record created state
class MaintenanceRecordCreated extends ResourceState {
  final MaintenanceRecord maintenanceRecord;

  const MaintenanceRecordCreated(this.maintenanceRecord);

  @override
  List<Object?> get props => [maintenanceRecord];
}

/// Maintenance completed state
class MaintenanceCompleted extends ResourceState {
  final MaintenanceRecord maintenanceRecord;

  const MaintenanceCompleted(this.maintenanceRecord);

  @override
  List<Object?> get props => [maintenanceRecord];
}

/// Available machines loaded state
class AvailableMachinesLoaded extends ResourceState {
  final List<Machine> machines;
  final Pagination? pagination;

  const AvailableMachinesLoaded({
    required this.machines,
    this.pagination,
  });

  @override
  List<Object?> get props => [machines, pagination];
}

/// Machines due for maintenance loaded state
class MachinesDueForMaintenanceLoaded extends ResourceState {
  final List<Machine> machines;
  final Pagination? pagination;
  final int daysAhead;

  const MachinesDueForMaintenanceLoaded({
    required this.machines,
    this.pagination,
    required this.daysAhead,
  });

  @override
  List<Object?> get props => [machines, pagination, daysAhead];
}

/// Machine utilization report loaded state
class MachineUtilizationReportLoaded extends ResourceState {
  final MachineUtilizationReport report;

  const MachineUtilizationReportLoaded(this.report);

  @override
  List<Object?> get props => [report];
}

/// Machines searched state
class MachinesSearched extends ResourceState {
  final List<Machine> machines;
  final Pagination? pagination;
  final String query;
  final MachineFilterCriteria? filter;

  const MachinesSearched({
    required this.machines,
    this.pagination,
    required this.query,
    this.filter,
  });

  @override
  List<Object?> get props => [machines, pagination, query, filter];
}

// Worker States

/// Workers loaded state
class WorkersLoaded extends ResourceState {
  final List<Worker> workers;
  final Pagination? pagination;
  final WorkerFilterCriteria? filter;
  final bool isRefreshing;
  final List<String> selectedWorkerIds;
  final ResourceViewMode viewMode;
  final String? sortBy;
  final bool sortAscending;

  const WorkersLoaded({
    required this.workers,
    this.pagination,
    this.filter,
    this.isRefreshing = false,
    this.selectedWorkerIds = const [],
    this.viewMode = ResourceViewMode.list,
    this.sortBy,
    this.sortAscending = true,
  });

  /// Copy with new values
  WorkersLoaded copyWith({
    List<Worker>? workers,
    Pagination? pagination,
    WorkerFilterCriteria? filter,
    bool? isRefreshing,
    List<String>? selectedWorkerIds,
    ResourceViewMode? viewMode,
    String? sortBy,
    bool? sortAscending,
  }) {
    return WorkersLoaded(
      workers: workers ?? this.workers,
      pagination: pagination ?? this.pagination,
      filter: filter ?? this.filter,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      selectedWorkerIds: selectedWorkerIds ?? this.selectedWorkerIds,
      viewMode: viewMode ?? this.viewMode,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  List<Object?> get props => [
        workers,
        pagination,
        filter,
        isRefreshing,
        selectedWorkerIds,
        viewMode,
        sortBy,
        sortAscending,
      ];

  /// Check if worker is selected
  bool isWorkerSelected(String workerId) {
    return selectedWorkerIds.contains(workerId);
  }

  /// Get selected workers
  List<Worker> get selectedWorkers {
    return workers.where((worker) => selectedWorkerIds.contains(worker.id)).toList();
  }

  /// Get active workers count
  int get activeWorkersCount {
    return workers.where((worker) => worker.isActive).length;
  }

  /// Get available workers count
  int get availableWorkersCount {
    return workers.where((worker) => worker.isAvailable).length;
  }

  /// Get workers on leave count
  int get workersOnLeaveCount {
    return workers.where((worker) => worker.isOnLeave).length;
  }
}

/// Worker details loaded state
class WorkerDetailsLoaded extends ResourceState {
  final Worker worker;

  const WorkerDetailsLoaded(this.worker);

  @override
  List<Object?> get props => [worker];
}

/// Worker created state
class WorkerCreated extends ResourceState {
  final Worker worker;

  const WorkerCreated(this.worker);

  @override
  List<Object?> get props => [worker];
}

/// Worker updated state
class WorkerUpdated extends ResourceState {
  final Worker worker;

  const WorkerUpdated(this.worker);

  @override
  List<Object?> get props => [worker];
}

/// Worker status updated state
class WorkerStatusUpdated extends ResourceState {
  final Worker worker;

  const WorkerStatusUpdated(this.worker);

  @override
  List<Object?> get props => [worker];
}

/// Worker schedules loaded state
class WorkerSchedulesLoaded extends ResourceState {
  final List<WorkerSchedule> schedules;
  final Pagination? pagination;
  final WorkerScheduleFilterCriteria? filter;

  const WorkerSchedulesLoaded({
    required this.schedules,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [schedules, pagination, filter];
}

/// Worker schedule created state
class WorkerScheduleCreated extends ResourceState {
  final WorkerSchedule schedule;

  const WorkerScheduleCreated(this.schedule);

  @override
  List<Object?> get props => [schedule];
}

/// Worker attendance loaded state
class WorkerAttendanceLoaded extends ResourceState {
  final List<WorkerAttendance> attendance;
  final Pagination? pagination;
  final WorkerAttendanceFilterCriteria? filter;

  const WorkerAttendanceLoaded({
    required this.attendance,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [attendance, pagination, filter];
}

/// Worker attendance recorded state
class WorkerAttendanceRecorded extends ResourceState {
  final WorkerAttendance attendance;

  const WorkerAttendanceRecorded(this.attendance);

  @override
  List<Object?> get props => [attendance];
}

/// Worker leave requests loaded state
class WorkerLeaveRequestsLoaded extends ResourceState {
  final List<WorkerLeave> leaveRequests;
  final Pagination? pagination;
  final WorkerLeaveFilterCriteria? filter;

  const WorkerLeaveRequestsLoaded({
    required this.leaveRequests,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [leaveRequests, pagination, filter];
}

/// Worker leave created state
class WorkerLeaveCreated extends ResourceState {
  final WorkerLeave leaveRequest;

  const WorkerLeaveCreated(this.leaveRequest);

  @override
  List<Object?> get props => [leaveRequest];
}

/// Worker leave approved state
class WorkerLeaveApproved extends ResourceState {
  final WorkerLeave leaveRequest;

  const WorkerLeaveApproved(this.leaveRequest);

  @override
  List<Object?> get props => [leaveRequest];
}

/// Available workers loaded state
class AvailableWorkersLoaded extends ResourceState {
  final List<Worker> workers;
  final Pagination? pagination;

  const AvailableWorkersLoaded({
    required this.workers,
    this.pagination,
  });

  @override
  List<Object?> get props => [workers, pagination];
}

/// Worker performance report loaded state
class WorkerPerformanceReportLoaded extends ResourceState {
  final WorkerPerformanceReport report;

  const WorkerPerformanceReportLoaded(this.report);

  @override
  List<Object?> get props => [report];
}

/// Workers searched state
class WorkersSearched extends ResourceState {
  final List<Worker> workers;
  final Pagination? pagination;
  final String query;
  final WorkerFilterCriteria? filter;

  const WorkersSearched({
    required this.workers,
    this.pagination,
    required this.query,
    this.filter,
  });

  @override
  List<Object?> get props => [workers, pagination, query, filter];
}

// Facility States

/// Facilities loaded state
class FacilitiesLoaded extends ResourceState {
  final List<Facility> facilities;
  final Pagination? pagination;
  final FacilityFilterCriteria? filter;

  const FacilitiesLoaded({
    required this.facilities,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [facilities, pagination, filter];
}

/// Facility details loaded state
class FacilityDetailsLoaded extends ResourceState {
  final Facility facility;

  const FacilityDetailsLoaded(this.facility);

  @override
  List<Object?> get props => [facility];
}

/// Facility created state
class FacilityCreated extends ResourceState {
  final Facility facility;

  const FacilityCreated(this.facility);

  @override
  List<Object?> get props => [facility];
}

/// Departments loaded state
class DepartmentsLoaded extends ResourceState {
  final List<Department> departments;
  final Pagination? pagination;
  final DepartmentFilterCriteria? filter;

  const DepartmentsLoaded({
    required this.departments,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [departments, pagination, filter];
}

/// Department created state
class DepartmentCreated extends ResourceState {
  final Department department;

  const DepartmentCreated(this.department);

  @override
  List<Object?> get props => [department];
}

/// Facility capacity report loaded state
class FacilityCapacityReportLoaded extends ResourceState {
  final FacilityCapacityReport report;

  const FacilityCapacityReportLoaded(this.report);

  @override
  List<Object?> get props => [report];
}

/// Facility utilization report loaded state
class FacilityUtilizationReportLoaded extends ResourceState {
  final FacilityUtilizationReport report;

  const FacilityUtilizationReportLoaded(this.report);

  @override
  List<Object?> get props => [report];
}

// General States

/// Resource error state
class ResourceError extends ResourceState {
  final String message;

  const ResourceError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Resource validation error state
class ResourceValidationError extends ResourceState {
  final Map<String, String> errors;

  const ResourceValidationError(this.errors);

  @override
  List<Object?> get props => [errors];
}

/// Resource operation success state
class ResourceOperationSuccess extends ResourceState {
  final String message;

  const ResourceOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Resource view mode changed state
class ResourceViewModeChanged extends ResourceState {
  final ResourceType resourceType;
  final ResourceViewMode viewMode;

  const ResourceViewModeChanged(this.resourceType, this.viewMode);

  @override
  List<Object?> get props => [resourceType, viewMode];
}

/// Resources sorted state
class ResourcesSorted extends ResourceState {
  final ResourceType resourceType;
  final String sortBy;
  final bool ascending;

  const ResourcesSorted(this.resourceType, this.sortBy, this.ascending);

  @override
  List<Object?> get props => [resourceType, sortBy, ascending];
}

/// Resource loading more state
class ResourceLoadingMore extends ResourceState {
  final ResourceType resourceType;

  const ResourceLoadingMore(this.resourceType);

  @override
  List<Object?> get props => [resourceType];
}

/// Resource refreshing state
class ResourceRefreshing extends ResourceState {
  final ResourceType resourceType;

  const ResourceRefreshing(this.resourceType);

  @override
  List<Object?> get props => [resourceType];
}
