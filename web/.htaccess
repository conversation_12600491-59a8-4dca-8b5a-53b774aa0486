# .htaccess for HM Collection Flutter Web App
# This configuration ensures proper routing, MIME types, and performance optimizations

# Enable rewrite engine
RewriteEngine On

# Handle Flutter web routing (SPA)
# Redirect all requests to index.html except for actual files and directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(api|assets|icons|favicon)
RewriteRule ^.*$ /index.html [L]

# MIME Types
<IfModule mod_mime.c>
    # WASM files
    AddType application/wasm .wasm
    
    # Dart files
    AddType application/dart .dart
    
    # JSON files
    AddType application/json .json
    
    # Web manifest
    AddType application/manifest+json .webmanifest
    
    # Font files
    AddType font/woff2 .woff2
    AddType font/woff .woff
    AddType font/ttf .ttf
    AddType font/otf .otf
    
    # JavaScript source maps
    AddType application/json .map
</IfModule>

# Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/dart
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml

    # Remove browser bugs (only needed for really old browsers)
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
    Header append Vary User-Agent
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on

    # Cache static assets for 1 year
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType application/wasm "access plus 1 year"
    ExpiresByType application/dart "access plus 1 year"

    # Cache HTML files for 1 hour
    ExpiresByType text/html "access plus 1 hour"

    # Cache JSON files for 1 day
    ExpiresByType application/json "access plus 1 day"

    # Cache manifest for 1 week
    ExpiresByType application/manifest+json "access plus 1 week"
</IfModule>

# Alternative caching with mod_headers
<IfModule mod_headers.c>
    # Cache static assets for 1 year
    <FilesMatch "\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|otf|wasm|dart)$">
        Header set Cache-Control "max-age=31536000, public, immutable"
    </FilesMatch>

    # Cache HTML files for 1 hour
    <FilesMatch "\.html$">
        Header set Cache-Control "max-age=3600, public"
    </FilesMatch>

    # Cache JSON files for 1 day
    <FilesMatch "\.json$">
        Header set Cache-Control "max-age=86400, public"
    </FilesMatch>

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # CORS headers (adjust as needed for your API)
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Prevent access to .dart files in production (optional)
<FilesMatch "\.dart$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Force HTTPS (uncomment if you have SSL certificate)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Custom error pages
ErrorDocument 404 /index.html
ErrorDocument 403 /index.html
ErrorDocument 500 /index.html

# Disable server signature
ServerSignature Off

# Prevent access to .htaccess file
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Enable FollowSymLinks
Options +FollowSymLinks

# Disable directory browsing
Options -Indexes

# Performance optimizations
<IfModule mod_headers.c>
    # Remove ETags
    Header unset ETag
    FileETag None

    # Enable Keep-Alive
    Header set Connection keep-alive
</IfModule>

# Preload critical resources (optional)
<IfModule mod_headers.c>
    <FilesMatch "index\.html$">
        Header add Link "</main.dart.js>; rel=preload; as=script"
        Header add Link "</assets/fonts/MaterialIcons-Regular.otf>; rel=preload; as=font; type=font/otf; crossorigin"
    </FilesMatch>
</IfModule>
