part of 'analytics_bloc.dart';


/// Base analytics state
abstract class AnalyticsState extends Equatable {
  const AnalyticsState();

  @override
  List<Object?> get props => [];
}

/// Initial analytics state
class AnalyticsInitial extends AnalyticsState {
  const AnalyticsInitial();
}

/// Analytics loading state
class AnalyticsLoading extends AnalyticsState {
  const AnalyticsLoading();
}

/// Analytics error state
class AnalyticsError extends AnalyticsState {
  final String message;
  final dynamic error;

  const AnalyticsError(this.message, [this.error]);

  @override
  List<Object?> get props => [message, error];
}

// Dashboard States
/// Dashboards loaded state
class AnalyticsDashboardsLoaded extends AnalyticsState {
  final ApiListResponse<AnalyticsDashboard> response;

  const AnalyticsDashboardsLoaded(this.response);

  @override
  List<Object?> get props => [response];

  /// Get dashboards list
  List<AnalyticsDashboard> get dashboards => response.data ?? [];

  /// Get pagination info
  Pagination get pagination => response.pagination ?? Pagination.empty();
  bool get hasNextPage => pagination.hasNextPage;
  bool get hasPreviousPage => pagination.hasPreviousPage;
}

/// Dashboard loaded state
class AnalyticsDashboardLoaded extends AnalyticsState {
  final AnalyticsDashboard dashboard;

  const AnalyticsDashboardLoaded(this.dashboard);

  @override
  List<Object?> get props => [dashboard];
}

/// Dashboard created state
class AnalyticsDashboardCreated extends AnalyticsState {
  final AnalyticsDashboard dashboard;

  const AnalyticsDashboardCreated(this.dashboard);

  @override
  List<Object?> get props => [dashboard];
}

/// Dashboard updated state
class AnalyticsDashboardUpdated extends AnalyticsState {
  final AnalyticsDashboard dashboard;

  const AnalyticsDashboardUpdated(this.dashboard);

  @override
  List<Object?> get props => [dashboard];
}

/// Dashboard deleted state
class AnalyticsDashboardDeleted extends AnalyticsState {
  final String dashboardId;

  const AnalyticsDashboardDeleted(this.dashboardId);

  @override
  List<Object?> get props => [dashboardId];
}

/// Dashboard cloned state
class AnalyticsDashboardCloned extends AnalyticsState {
  final AnalyticsDashboard dashboard;

  const AnalyticsDashboardCloned(this.dashboard);

  @override
  List<Object?> get props => [dashboard];
}

/// Dashboard shared state
class AnalyticsDashboardShared extends AnalyticsState {
  final AnalyticsDashboard dashboard;

  const AnalyticsDashboardShared(this.dashboard);

  @override
  List<Object?> get props => [dashboard];
}

/// Dashboards searched state
class AnalyticsDashboardsSearched extends AnalyticsState {
  final ApiListResponse<AnalyticsDashboard> response;
  final String query;

  const AnalyticsDashboardsSearched(this.response, this.query);

  @override
  List<Object?> get props => [response, query];

  /// Get dashboards list
  List<AnalyticsDashboard> get dashboards => response.data ?? [];

  /// Get pagination info
  Pagination get pagination => response.pagination ?? Pagination.empty();
  bool get hasNextPage => pagination.hasNextPage;
  bool get hasPreviousPage => pagination.hasPreviousPage;
}

// Widget States
/// Widget added state
class AnalyticsWidgetAdded extends AnalyticsState {
  final AnalyticsDashboard dashboard;

  const AnalyticsWidgetAdded(this.dashboard);

  @override
  List<Object?> get props => [dashboard];
}

/// Widget updated state
class AnalyticsWidgetUpdated extends AnalyticsState {
  final DashboardWidget widget;

  const AnalyticsWidgetUpdated(this.widget);

  @override
  List<Object?> get props => [widget];
}

/// Widget removed state
class AnalyticsWidgetRemoved extends AnalyticsState {
  final String dashboardId;
  final String widgetId;

  const AnalyticsWidgetRemoved(this.dashboardId, this.widgetId);

  @override
  List<Object?> get props => [dashboardId, widgetId];
}

/// Widgets reordered state
class AnalyticsWidgetsReordered extends AnalyticsState {
  final AnalyticsDashboard dashboard;

  const AnalyticsWidgetsReordered(this.dashboard);

  @override
  List<Object?> get props => [dashboard];
}

// Report States
/// Reports loaded state
class AnalyticsReportsLoaded extends AnalyticsState {
  final ApiListResponse<AnalyticsReport> response;

  const AnalyticsReportsLoaded(this.response);

  @override
  List<Object?> get props => [response];

  /// Get reports list
  List<AnalyticsReport> get reports => response.data ?? [];

  /// Get pagination info
  Pagination get pagination => response.pagination ?? Pagination.empty();
  bool get hasNextPage => pagination.hasNextPage;
  bool get hasPreviousPage => pagination.hasPreviousPage;
  
  /// Get reports grouped by type
  Map<AnalyticsReportType, List<AnalyticsReport>> get reportsByType {
    final Map<AnalyticsReportType, List<AnalyticsReport>> grouped = {};
    for (final report in reports) {
      grouped.putIfAbsent(report.type, () => []).add(report);
    }
    return grouped;
  }
  
  /// Get reports grouped by status
  Map<ReportStatus, List<AnalyticsReport>> get reportsByStatus {
    final Map<ReportStatus, List<AnalyticsReport>> grouped = {};
    for (final report in reports) {
      grouped.putIfAbsent(report.status, () => []).add(report);
    }
    return grouped;
  }
  
  /// Get count of completed reports
  int get completedReportsCount => reports.where((r) => r.isReady).length;
  
  /// Get count of scheduled reports
  int get scheduledReportsCount => reports.where((r) => r.isScheduled).length;
  
  /// Get count of failed reports
  int get failedReportsCount => reports.where((r) => r.hasFailed).length;
}

/// Report loaded state
class AnalyticsReportLoaded extends AnalyticsState {
  final AnalyticsReport report;

  const AnalyticsReportLoaded(this.report);

  @override
  List<Object?> get props => [report];
}

/// Report generated state
class AnalyticsReportGenerated extends AnalyticsState {
  final AnalyticsReport report;

  const AnalyticsReportGenerated(this.report);

  @override
  List<Object?> get props => [report];
}

/// Report scheduled state
class AnalyticsReportScheduled extends AnalyticsState {
  final AnalyticsReport report;

  const AnalyticsReportScheduled(this.report);

  @override
  List<Object?> get props => [report];
}

/// Report cancelled state
class AnalyticsReportCancelled extends AnalyticsState {
  final String reportId;

  const AnalyticsReportCancelled(this.reportId);

  @override
  List<Object?> get props => [reportId];
}

/// Report deleted state
class AnalyticsReportDeleted extends AnalyticsState {
  final String reportId;

  const AnalyticsReportDeleted(this.reportId);

  @override
  List<Object?> get props => [reportId];
}

/// Reports searched state
class AnalyticsReportsSearched extends AnalyticsState {
  final ApiListResponse<AnalyticsReport> response;
  final String query;

  const AnalyticsReportsSearched(this.response, this.query);

  @override
  List<Object?> get props => [response, query];

  /// Get reports list
  List<AnalyticsReport> get reports => response.data ?? [];

  /// Get pagination info
  Pagination get pagination => response.pagination ?? Pagination.empty();
  bool get hasNextPage => pagination.hasNextPage;
  bool get hasPreviousPage => pagination.hasPreviousPage;
}

// Data Analytics States
/// KPI data loaded state
class AnalyticsKpiDataLoaded extends AnalyticsState {
  final Map<String, dynamic> data;

  const AnalyticsKpiDataLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Chart data loaded state
class AnalyticsChartDataLoaded extends AnalyticsState {
  final ChartData data;

  const AnalyticsChartDataLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Table data loaded state
class AnalyticsTableDataLoaded extends AnalyticsState {
  final TableData data;

  const AnalyticsTableDataLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

// Performance Metrics States
/// Production metrics loaded state
class AnalyticsProductionMetricsLoaded extends AnalyticsState {
  final ProductionMetrics data;

  const AnalyticsProductionMetricsLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Quality metrics loaded state
class AnalyticsQualityMetricsLoaded extends AnalyticsState {
  final QualityMetrics data;

  const AnalyticsQualityMetricsLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Financial metrics loaded state
class AnalyticsFinancialMetricsLoaded extends AnalyticsState {
  final FinancialMetrics data;

  const AnalyticsFinancialMetricsLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Inventory metrics loaded state
class AnalyticsInventoryMetricsLoaded extends AnalyticsState {
  final InventoryMetrics data;

  const AnalyticsInventoryMetricsLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Efficiency metrics loaded state
class AnalyticsEfficiencyMetricsLoaded extends AnalyticsState {
  final EfficiencyMetrics data;

  const AnalyticsEfficiencyMetricsLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

// Advanced Analytics States
/// Trend data loaded state
class AnalyticsTrendDataLoaded extends AnalyticsState {
  final TrendData data;

  const AnalyticsTrendDataLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Comparative analysis loaded state
class AnalyticsComparativeAnalysisLoaded extends AnalyticsState {
  final ComparativeAnalysis data;

  const AnalyticsComparativeAnalysisLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Forecast loaded state
class AnalyticsForecastLoaded extends AnalyticsState {
  final ForecastData data;

  const AnalyticsForecastLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Anomaly detection loaded state
class AnalyticsAnomalyDetectionLoaded extends AnalyticsState {
  final AnomalyDetectionData data;

  const AnalyticsAnomalyDetectionLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// Real-time metrics loaded state
class AnalyticsRealTimeMetricsLoaded extends AnalyticsState {
  final RealTimeMetrics data;

  const AnalyticsRealTimeMetricsLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

// Export States
/// Dashboard exported state
class AnalyticsDashboardExported extends AnalyticsState {
  final String url;

  const AnalyticsDashboardExported(this.url);

  @override
  List<Object?> get props => [url];
}

/// Report exported state
class AnalyticsReportExported extends AnalyticsState {
  final String url;

  const AnalyticsReportExported(this.url);

  @override
  List<Object?> get props => [url];
}

// Metadata States
/// Available metrics loaded state
class AnalyticsAvailableMetricsLoaded extends AnalyticsState {
  final List<MetricDefinition> metrics;

  const AnalyticsAvailableMetricsLoaded(this.metrics);

  @override
  List<Object?> get props => [metrics];
}

/// Available dimensions loaded state
class AnalyticsAvailableDimensionsLoaded extends AnalyticsState {
  final List<DimensionDefinition> dimensions;

  const AnalyticsAvailableDimensionsLoaded(this.dimensions);

  @override
  List<Object?> get props => [dimensions];
}