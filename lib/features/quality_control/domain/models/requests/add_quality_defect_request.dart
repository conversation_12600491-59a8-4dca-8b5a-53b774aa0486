import 'package:equatable/equatable.dart';

class AddQualityDefectRequest extends Equatable {
  final String inspectionId;
  final String defectType;
  final String description;
  final String severity;
  final String? imageUrl;
  final String? notes;
  final Map<String, dynamic>? customFields;

  const AddQualityDefectRequest({
    required this.inspectionId,
    required this.defectType,
    required this.description,
    required this.severity,
    this.imageUrl,
    this.notes,
    this.customFields,
  });

  Map<String, dynamic> toJson() => {
        'inspectionId': inspectionId,
        'defectType': defectType,
        'description': description,
        'severity': severity,
        if (imageUrl != null) 'imageUrl': imageUrl,
        if (notes != null) 'notes': notes,
        if (customFields != null) ...customFields!,
      };

  @override
  List<Object?> get props => [
        inspectionId,
        defectType,
        description,
        severity,
        imageUrl,
        notes,
        customFields,
      ];
}
