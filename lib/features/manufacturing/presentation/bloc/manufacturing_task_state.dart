part of 'manufacturing_task_bloc.dart';

/// Base manufacturing task state
abstract class ManufacturingTaskState extends Equatable {
  const ManufacturingTaskState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ManufacturingTaskInitial extends ManufacturingTaskState {
  const ManufacturingTaskInitial();
}

/// Loading state
class ManufacturingTaskLoading extends ManufacturingTaskState {
  const ManufacturingTaskLoading();
}

/// Error state
class ManufacturingTaskError extends ManufacturingTaskState {
  final String message;

  const ManufacturingTaskError(this.message);

  @override
  List<Object?> get props => [message];
}

// Manufacturing Task Order States

/// Manufacturing task orders loaded
class ManufacturingTaskOrdersLoaded extends ManufacturingTaskState {
  final List<ManufacturingTaskOrder> orders;
  final Pagination? pagination;

  const ManufacturingTaskOrdersLoaded({
    required this.orders,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, pagination];
}

/// Manufacturing task order loaded
class ManufacturingTaskOrderLoaded extends ManufacturingTaskState {
  final ManufacturingTaskOrder order;

  const ManufacturingTaskOrderLoaded({required this.order});

  @override
  List<Object?> get props => [order];
}

/// Manufacturing task order created
class ManufacturingTaskOrderCreated extends ManufacturingTaskState {
  final ManufacturingTaskOrder order;

  const ManufacturingTaskOrderCreated({required this.order});

  @override
  List<Object?> get props => [order];
}

/// Manufacturing task order updated
class ManufacturingTaskOrderUpdated extends ManufacturingTaskState {
  final ManufacturingTaskOrder order;

  const ManufacturingTaskOrderUpdated({required this.order});

  @override
  List<Object?> get props => [order];
}

/// Manufacturing task order deleted
class ManufacturingTaskOrderDeleted extends ManufacturingTaskState {
  final String orderId;

  const ManufacturingTaskOrderDeleted({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

/// Manufacturing task order started
class ManufacturingTaskOrderStarted extends ManufacturingTaskState {
  final ManufacturingTaskOrder order;

  const ManufacturingTaskOrderStarted({required this.order});

  @override
  List<Object?> get props => [order];
}

/// Manufacturing task order paused
class ManufacturingTaskOrderPaused extends ManufacturingTaskState {
  final ManufacturingTaskOrder order;

  const ManufacturingTaskOrderPaused({required this.order});

  @override
  List<Object?> get props => [order];
}

/// Manufacturing task order resumed
class ManufacturingTaskOrderResumed extends ManufacturingTaskState {
  final ManufacturingTaskOrder order;

  const ManufacturingTaskOrderResumed({required this.order});

  @override
  List<Object?> get props => [order];
}

/// Manufacturing task order completed
class ManufacturingTaskOrderCompleted extends ManufacturingTaskState {
  final ManufacturingTaskOrder order;

  const ManufacturingTaskOrderCompleted({required this.order});

  @override
  List<Object?> get props => [order];
}

// Stage Task States

/// Stage tasks loaded
class StageTasksLoaded extends ManufacturingTaskState {
  final List<ManufacturingStageTask> tasks;
  final Pagination? pagination;

  const StageTasksLoaded({
    required this.tasks,
    this.pagination,
  });

  @override
  List<Object?> get props => [tasks, pagination];
}

/// Stage task loaded
class StageTaskLoaded extends ManufacturingTaskState {
  final ManufacturingStageTask task;

  const StageTaskLoaded({required this.task});

  @override
  List<Object?> get props => [task];
}

/// Stage task created
class StageTaskCreated extends ManufacturingTaskState {
  final ManufacturingStageTask task;

  const StageTaskCreated({required this.task});

  @override
  List<Object?> get props => [task];
}

/// Stage task updated
class StageTaskUpdated extends ManufacturingTaskState {
  final ManufacturingStageTask task;

  const StageTaskUpdated({required this.task});

  @override
  List<Object?> get props => [task];
}

/// Stage task started
class StageTaskStarted extends ManufacturingTaskState {
  final ManufacturingStageTask task;

  const StageTaskStarted({required this.task});

  @override
  List<Object?> get props => [task];
}

/// Stage task completed
class StageTaskCompleted extends ManufacturingTaskState {
  final ManufacturingStageTask task;

  const StageTaskCompleted({required this.task});

  @override
  List<Object?> get props => [task];
}

/// Workers assigned to stage task
class WorkersAssignedToStageTask extends ManufacturingTaskState {
  final ManufacturingStageTask task;

  const WorkersAssignedToStageTask({required this.task});

  @override
  List<Object?> get props => [task];
}

/// Stage task progress updated
class StageTaskProgressUpdated extends ManufacturingTaskState {
  final ManufacturingStageTask task;

  const StageTaskProgressUpdated({required this.task});

  @override
  List<Object?> get props => [task];
}

// Stage Transfer States

/// Stage transfers loaded
class StageTransfersLoaded extends ManufacturingTaskState {
  final List<ManufacturingStageTransfer> transfers;
  final Pagination? pagination;

  const StageTransfersLoaded({
    required this.transfers,
    this.pagination,
  });

  @override
  List<Object?> get props => [transfers, pagination];
}

/// Stage transfer created
class StageTransferCreated extends ManufacturingTaskState {
  final ManufacturingStageTransfer transfer;

  const StageTransferCreated({required this.transfer});

  @override
  List<Object?> get props => [transfer];
}

/// Stage transfer accepted
class StageTransferAccepted extends ManufacturingTaskState {
  final ManufacturingStageTransfer transfer;

  const StageTransferAccepted({required this.transfer});

  @override
  List<Object?> get props => [transfer];
}

/// Stage transfer rejected
class StageTransferRejected extends ManufacturingTaskState {
  final ManufacturingStageTransfer transfer;

  const StageTransferRejected({required this.transfer});

  @override
  List<Object?> get props => [transfer];
}

/// Stage transfer completed
class StageTransferCompleted extends ManufacturingTaskState {
  final ManufacturingStageTransfer transfer;

  const StageTransferCompleted({required this.transfer});

  @override
  List<Object?> get props => [transfer];
}

// Notes States

/// Notes loaded
class NotesLoaded extends ManufacturingTaskState {
  final List<ManufacturingTaskNote> notes;
  final Pagination? pagination;

  const NotesLoaded({
    required this.notes,
    this.pagination,
  });

  @override
  List<Object?> get props => [notes];
}

/// Note added
class NoteAdded extends ManufacturingTaskState {
  final ManufacturingTaskNote note;

  const NoteAdded({required this.note});

  @override
  List<Object?> get props => [note];
}

/// Note updated
class NoteUpdated extends ManufacturingTaskState {
  final ManufacturingTaskNote note;

  const NoteUpdated({required this.note});

  @override
  List<Object?> get props => [note];
}

/// Note deleted
class NoteDeleted extends ManufacturingTaskState {
  final String noteId;

  const NoteDeleted({required this.noteId});

  @override
  List<Object?> get props => [noteId];
}

// Analytics States

/// Manufacturing task analytics loaded
class ManufacturingTaskAnalyticsLoaded extends ManufacturingTaskState {
  final ManufacturingTaskAnalytics analytics;

  const ManufacturingTaskAnalyticsLoaded({required this.analytics});

  @override
  List<Object?> get props => [analytics];
}

/// Stage performance metrics loaded
class StagePerformanceMetricsLoaded extends ManufacturingTaskState {
  final List<StagePerformanceMetrics> metrics;

  const StagePerformanceMetricsLoaded({required this.metrics});

  @override
  List<Object?> get props => [metrics];
}

/// Bottleneck analysis loaded
class BottleneckAnalysisLoaded extends ManufacturingTaskState {
  final List<ManufacturingBottleneck> bottlenecks;

  const BottleneckAnalysisLoaded({required this.bottlenecks});

  @override
  List<Object?> get props => [bottlenecks];
}

/// Efficiency metrics loaded
class EfficiencyMetricsLoaded extends ManufacturingTaskState {
  final ManufacturingEfficiencyMetrics metrics;

  const EfficiencyMetricsLoaded({required this.metrics});

  @override
  List<Object?> get props => [metrics];
}

// Search and Filter States

/// Manufacturing task orders search results loaded
class ManufacturingTaskOrdersSearchResultsLoaded extends ManufacturingTaskState {
  final List<ManufacturingTaskOrder> orders;
  final String query;
  final Pagination? pagination;

  const ManufacturingTaskOrdersSearchResultsLoaded({
    required this.orders,
    required this.query,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, query, pagination];
}

/// Overdue manufacturing task orders loaded
class OverdueManufacturingTaskOrdersLoaded extends ManufacturingTaskState {
  final List<ManufacturingTaskOrder> orders;
  final Pagination? pagination;

  const OverdueManufacturingTaskOrdersLoaded({
    required this.orders,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, pagination];
}

/// Urgent manufacturing task orders loaded
class UrgentManufacturingTaskOrdersLoaded extends ManufacturingTaskState {
  final List<ManufacturingTaskOrder> orders;
  final Pagination? pagination;

  const UrgentManufacturingTaskOrdersLoaded({
    required this.orders,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, pagination];
}

/// Manufacturing task orders by status loaded
class ManufacturingTaskOrdersByStatusLoaded extends ManufacturingTaskState {
  final List<ManufacturingTaskOrder> orders;
  final List<ManufacturingOrderStatus> statuses;
  final Pagination? pagination;

  const ManufacturingTaskOrdersByStatusLoaded({
    required this.orders,
    required this.statuses,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, statuses, pagination];
}

/// Stage tasks by worker loaded
class StageTasksByWorkerLoaded extends ManufacturingTaskState {
  final List<ManufacturingStageTask> tasks;
  final String workerId;
  final Pagination? pagination;

  const StageTasksByWorkerLoaded({
    required this.tasks,
    required this.workerId,
    this.pagination,
  });

  @override
  List<Object?> get props => [tasks, workerId, pagination];
}

/// Stage tasks by status loaded
class StageTasksByStatusLoaded extends ManufacturingTaskState {
  final List<ManufacturingStageTask> tasks;
  final List<TaskStatus> statuses;
  final Pagination? pagination;

  const StageTasksByStatusLoaded({
    required this.tasks,
    required this.statuses,
    this.pagination,
  });

  @override
  List<Object?> get props => [tasks, statuses, pagination];
}

// Utility States

/// Manufacturing task data refreshed
class ManufacturingTaskDataRefreshed extends ManufacturingTaskState {
  const ManufacturingTaskDataRefreshed();
}

/// Manufacturing task state cleared
class ManufacturingTaskStateCleared extends ManufacturingTaskState {
  const ManufacturingTaskStateCleared();
}
