import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/constants/api_constants.dart';
import '../../../../core/errors/error_handler.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/api_client.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/dashboard_entities.dart';
import '../../domain/repositories/dashboard_repository.dart';

/// Dashboard repository implementation
@LazySingleton(as: DashboardRepository)
class DashboardRepositoryImpl implements DashboardRepository {
  final ApiClient _apiClient;

  const DashboardRepositoryImpl(this._apiClient);

  @override
  Future<Either<Failure, DashboardLayout>> getDashboardLayout(String userId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.dashboard}/layout/$userId',
      );

      if (response.statusCode == 200 && response.data != null) {
        // For now, return a mock layout since we don't have backend implementation
        return Right(_getMockDashboardLayout(userId));
      } else {
        return Left(ServerFailure(
          'Failed to get dashboard layout',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      // Return mock data for development
      return Right(_getMockDashboardLayout(userId));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> updateDashboardLayout(
    DashboardLayout layout,
  ) async {
    try {
      final requestData = {
        'user_id': layout.userId,
        'role': layout.role.value,
        'department': layout.department.value,
        'widgets': layout.widgets.map((w) => {
          'id': w.id,
          'title': w.title,
          'type': w.type.name,
          'configuration': w.configuration,
          'order': w.order,
          'is_enabled': w.isEnabled,
          'size': w.size.name,
        }).toList(),
        'preferences': layout.preferences,
      };

      final response = await _apiClient.put<Map<String, dynamic>>(
        '${ApiConstants.dashboard}/layout',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to update dashboard layout',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<DashboardStatistics>>> getDashboardStatistics(
    UserRole role,
    Department department,
  ) async {
    try {
      // Return mock statistics for development
      return Right(_getMockStatistics(role, department));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<DashboardChartData>>> getDashboardCharts(
    UserRole role,
    Department department, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Return mock chart data for development
      return Right(_getMockChartData(role, department));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<DashboardActivity>>> getRecentActivities({
    String? userId,
    UserRole? role,
    Department? department,
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock activities for development
      final activities = _getMockActivities();
      return Right(ApiListResponse<DashboardActivity>(
        success: true,
        data: activities,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 10,
          total: 5,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<DashboardNotification>>> getNotifications({
    String? userId,
    UserRole? role,
    Department? department,
    bool? unreadOnly,
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock notifications for development
      final notifications = _getMockNotifications();
      return Right(ApiListResponse<DashboardNotification>(
        success: true,
        data: notifications,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 10,
          total: 3,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> markNotificationAsRead(
    String notificationId,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.notifications}/$notificationId/read',
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to mark notification as read',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> markAllNotificationsAsRead(
    String userId,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.notifications}/mark-all-read',
        data: {'user_id': userId},
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to mark all notifications as read',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<QuickAction>>> getQuickActions(
    UserRole role,
    Department department,
  ) async {
    try {
      // Return mock quick actions for development
      return Right(_getMockQuickActions(role, department));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  // Mock data methods for development
  DashboardLayout _getMockDashboardLayout(String userId) {
    return DashboardLayout(
      userId: userId,
      role: UserRole.administrator, // This should come from user data
      department: Department.administration,
      widgets: [
        const DashboardWidget(
          id: 'statistics',
          title: 'Key Statistics',
          type: DashboardWidgetType.statistics,
          configuration: {},
          allowedRoles: UserRole.values,
          allowedDepartments: Department.values,
          order: 1,
          size: DashboardWidgetSize.large,
        ),
        const DashboardWidget(
          id: 'recent_activity',
          title: 'Recent Activity',
          type: DashboardWidgetType.recentActivity,
          configuration: {},
          allowedRoles: UserRole.values,
          allowedDepartments: Department.values,
          order: 2,
          size: DashboardWidgetSize.medium,
        ),
      ],
    );
  }

  List<DashboardStatistics> _getMockStatistics(UserRole role, Department department) {
    return [
      const DashboardStatistics(
        title: 'Total Orders',
        value: '156',
        subtitle: 'This month',
        trend: 'vs last month',
        trendPercentage: 12.5,
        trendDirection: StatisticTrend.up,
        icon: 'orders',
        color: '#2563EB',
      ),
      const DashboardStatistics(
        title: 'Production Rate',
        value: '89%',
        subtitle: 'Efficiency',
        trend: 'vs last week',
        trendPercentage: 3.2,
        trendDirection: StatisticTrend.up,
        icon: 'production',
        color: '#059669',
      ),
      const DashboardStatistics(
        title: 'Quality Score',
        value: '94.2%',
        subtitle: 'Average',
        trend: 'vs last month',
        trendPercentage: 1.8,
        trendDirection: StatisticTrend.down,
        icon: 'quality',
        color: '#DC2626',
      ),
      const DashboardStatistics(
        title: 'Active Users',
        value: '42',
        subtitle: 'Online now',
        trend: 'vs yesterday',
        trendPercentage: 0.0,
        trendDirection: StatisticTrend.neutral,
        icon: 'users',
        color: '#7C3AED',
      ),
    ];
  }

  List<DashboardChartData> _getMockChartData(UserRole role, Department department) {
    return [
      DashboardChartData(
        title: 'Production Trend',
        type: ChartType.line,
        data: [
          const ChartDataPoint(label: 'Mon', value: 85),
          const ChartDataPoint(label: 'Tue', value: 92),
          const ChartDataPoint(label: 'Wed', value: 78),
          const ChartDataPoint(label: 'Thu', value: 96),
          const ChartDataPoint(label: 'Fri', value: 89),
        ],
      ),
    ];
  }

  List<DashboardActivity> _getMockActivities() {
    final now = DateTime.now();
    return [
      DashboardActivity(
        id: '1',
        createdAt: now.subtract(const Duration(minutes: 15)),
        updatedAt: now.subtract(const Duration(minutes: 15)),
        userId: 'user1',
        userName: 'John Doe',
        action: 'order_created',
        description: 'Created new order #ORD-2024-001',
        type: ActivityType.orderCreated,
      ),
      DashboardActivity(
        id: '2',
        createdAt: now.subtract(const Duration(hours: 1)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        userId: 'user2',
        userName: 'Jane Smith',
        action: 'production_started',
        description: 'Started production for batch #B-001',
        type: ActivityType.productionStarted,
      ),
      DashboardActivity(
        id: '3',
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        userId: 'user3',
        userName: 'Mike Johnson',
        action: 'quality_check',
        description: 'Completed quality check for order #ORD-2024-002',
        type: ActivityType.qualityCheck,
      ),
    ];
  }

  List<DashboardNotification> _getMockNotifications() {
    final now = DateTime.now();
    return [
      DashboardNotification(
        id: '1',
        createdAt: now.subtract(const Duration(minutes: 30)),
        updatedAt: now.subtract(const Duration(minutes: 30)),
        title: 'Low Inventory Alert',
        message: 'Cotton fabric stock is running low. Only 50 units remaining.',
        type: NotificationType.warning,
        priority: NotificationPriority.high,
        isRead: false,
      ),
      DashboardNotification(
        id: '2',
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        title: 'Order Completed',
        message: 'Order #ORD-2024-001 has been completed and is ready for shipping.',
        type: NotificationType.success,
        priority: NotificationPriority.medium,
        isRead: false,
      ),
      DashboardNotification(
        id: '3',
        createdAt: now.subtract(const Duration(hours: 4)),
        updatedAt: now.subtract(const Duration(hours: 4)),
        title: 'System Maintenance',
        message: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM.',
        type: NotificationType.info,
        priority: NotificationPriority.low,
        isRead: true,
      ),
    ];
  }

  List<QuickAction> _getMockQuickActions(UserRole role, Department department) {
    return [
      const QuickAction(
        id: 'create_order',
        title: 'Create Order',
        description: 'Create a new manufacturing order',
        icon: 'add_shopping_cart',
        route: '/orders/create',
        allowedRoles: [UserRole.merchandiser, UserRole.administrator],
        allowedDepartments: [Department.merchandising, Department.administration],
        color: '#2563EB',
      ),
      const QuickAction(
        id: 'view_production',
        title: 'Production',
        description: 'View production status',
        icon: 'precision_manufacturing',
        route: '/production',
        allowedRoles: UserRole.values,
        allowedDepartments: Department.values,
        color: '#059669',
      ),
      const QuickAction(
        id: 'check_inventory',
        title: 'Inventory',
        description: 'Check inventory levels',
        icon: 'inventory',
        route: '/inventory',
        allowedRoles: [UserRole.inventoryManager, UserRole.administrator],
        allowedDepartments: [Department.warehouse, Department.administration],
        color: '#D97706',
      ),
      const QuickAction(
        id: 'quality_report',
        title: 'Quality',
        description: 'View quality reports',
        icon: 'verified',
        route: '/quality',
        allowedRoles: [UserRole.qualityController, UserRole.administrator],
        allowedDepartments: [Department.quality, Department.administration],
        color: '#DC2626',
      ),
    ];
  }

  // Unimplemented methods - return mock data or not implemented errors
  @override
  Future<Either<Failure, List<DashboardWidget>>> getAvailableWidgets(
    UserRole role,
    Department department,
  ) async {
    return const Left(UnimplementedFailure('Get available widgets not implemented'));
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getWidgetData(
    String widgetId,
    Map<String, dynamic> configuration,
  ) async {
    return const Left(UnimplementedFailure('Get widget data not implemented'));
  }

  @override
  Future<Either<Failure, DashboardWidget>> createCustomWidget(
    DashboardWidget widget,
  ) async {
    return const Left(UnimplementedFailure('Create custom widget not implemented'));
  }

  @override
  Future<Either<Failure, DashboardWidget>> updateWidget(
    DashboardWidget widget,
  ) async {
    return const Left(UnimplementedFailure('Update widget not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteWidget(String widgetId) async {
    return const Left(UnimplementedFailure('Delete widget not implemented'));
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getDashboardPreferences(
    String userId,
  ) async {
    return const Left(UnimplementedFailure('Get dashboard preferences not implemented'));
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> updateDashboardPreferences(
    String userId,
    Map<String, dynamic> preferences,
  ) async {
    return const Left(UnimplementedFailure('Update dashboard preferences not implemented'));
  }

  @override
  Future<Either<Failure, String>> exportDashboardData(
    UserRole role,
    Department department,
    {
    DateTime? startDate,
    DateTime? endDate,
    String format = 'pdf',
  }) async {
    return const Left(UnimplementedFailure('Export dashboard data not implemented'));
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getSystemHealth() async {
    return const Left(UnimplementedFailure('Get system health not implemented'));
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getPerformanceMetrics(
    UserRole role,
    Department department,
    {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return const Left(UnimplementedFailure('Get performance metrics not implemented'));
  }
}
