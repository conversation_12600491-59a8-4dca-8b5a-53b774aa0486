import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bundle.dart';
import '../entities/bundle_input.dart';
import '../entities/bundle_statistics.dart';

/// Repository interface for bundling operations
abstract class BundlingRepository {
  /// Get all bundles with optional filtering
  Future<Either<Failure, List<Bundle>>> getAllBundles({
    BundleStatus? status,
    String? designNo,
    String? lotNo,
    String? rollNo,
    String? altNo,
    GarmentSize? size,
    String? assignedTo,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  });

  /// Get a specific bundle by ID
  Future<Either<Failure, Bundle>> getBundleById(String id);

  /// Get bundle by bundle number
  Future<Either<Failure, Bundle>> getBundleByNumber(String bundleNo);

  /// Create bundles from input data
  Future<Either<Failure, List<Bundle>>> createBundles(
    BundleInput input,
    String createdBy,
  );

  /// Update an existing bundle
  Future<Either<Failure, Bundle>> updateBundle(Bundle bundle);

  /// Delete a bundle (soft delete)
  Future<Either<Failure, void>> deleteBundle(String id, String deletedBy);

  /// Issue bundle to sewing
  Future<Either<Failure, Bundle>> issueBundleToSewing(
    String bundleId,
    String assignedTo,
    String issuedBy,
  );

  /// Start bundle work
  Future<Either<Failure, Bundle>> startBundleWork(
    String bundleId,
    String startedBy,
  );

  /// Complete bundle work
  Future<Either<Failure, Bundle>> completeBundleWork(
    String bundleId,
    String completedBy,
    String? remarks,
  );

  /// Cancel bundle
  Future<Either<Failure, Bundle>> cancelBundle(
    String bundleId,
    String cancelledBy,
    String reason,
  );

  /// Get bundles assigned to a specific user
  Future<Either<Failure, List<Bundle>>> getBundlesAssignedTo(String userId);

  /// Get bundles by status
  Future<Either<Failure, List<Bundle>>> getBundlesByStatus(BundleStatus status);

  /// Get bundles by design
  Future<Either<Failure, List<Bundle>>> getBundlesByDesign(String designNo);

  /// Get bundles by lot and roll
  Future<Either<Failure, List<Bundle>>> getBundlesByLotAndRoll(
    String lotNo,
    String rollNo,
  );

  /// Search bundles
  Future<Either<Failure, List<Bundle>>> searchBundles(String query);

  /// Get bundle statistics
  Future<Either<Failure, BundleStatistics>> getBundleStatistics({
    DateTime? fromDate,
    DateTime? toDate,
    String? designNo,
    GarmentSize? size,
    BundleStatus? status,
  });

  /// Get bundles for export
  Future<Either<Failure, List<Bundle>>> getBundlesForExport({
    List<String>? bundleIds,
    BundleStatus? status,
    String? designNo,
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Merge bundles (combine multiple bundles into one)
  Future<Either<Failure, Bundle>> mergeBundles(
    List<String> bundleIds,
    String mergedBy,
    String? remarks,
  );

  /// Split bundle (divide one bundle into multiple)
  Future<Either<Failure, List<Bundle>>> splitBundle(
    String bundleId,
    List<int> splitPoints,
    String splitBy,
    String? remarks,
  );

  /// Get next bundle number
  Future<Either<Failure, String>> getNextBundleNumber();

  /// Validate bundle input
  Future<Either<Failure, bool>> validateBundleInput(BundleInput input);

  /// Check if bundle number exists
  Future<Either<Failure, bool>> bundleNumberExists(String bundleNo);

  /// Get bundles stream for real-time updates
  Stream<Either<Failure, List<Bundle>>> getBundlesStream({
    BundleStatus? status,
    String? assignedTo,
  });

  /// Get bundle statistics stream
  Stream<Either<Failure, BundleStatistics>> getBundleStatisticsStream();

  /// Sync offline bundles
  Future<Either<Failure, void>> syncOfflineBundles();

  /// Get offline bundles count
  Future<Either<Failure, int>> getOfflineBundlesCount();
}

/// Request models for repository operations
class CreateBundlesRequest {
  final BundleInput input;
  final String createdBy;

  const CreateBundlesRequest({
    required this.input,
    required this.createdBy,
  });
}

class UpdateBundleRequest {
  final String bundleId;
  final String? designNo;
  final String? lotNo;
  final String? rollNo;
  final String? altNo;
  final GarmentSize? size;
  final int? pieceStartNo;
  final int? pieceEndNo;
  final int? totalPieces;
  final BundleStatus? status;
  final String? assignedTo;
  final String? remarks;
  final String? color;
  final String? orderNo;
  final String? clientName;
  final String updatedBy;

  const UpdateBundleRequest({
    required this.bundleId,
    this.designNo,
    this.lotNo,
    this.rollNo,
    this.altNo,
    this.size,
    this.pieceStartNo,
    this.pieceEndNo,
    this.totalPieces,
    this.status,
    this.assignedTo,
    this.remarks,
    this.color,
    this.orderNo,
    this.clientName,
    required this.updatedBy,
  });
}

class BundleFilterRequest {
  final BundleStatus? status;
  final String? designNo;
  final String? lotNo;
  final String? rollNo;
  final String? altNo;
  final GarmentSize? size;
  final String? assignedTo;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? limit;
  final int? offset;
  final String? sortBy;
  final bool? sortAscending;

  const BundleFilterRequest({
    this.status,
    this.designNo,
    this.lotNo,
    this.rollNo,
    this.altNo,
    this.size,
    this.assignedTo,
    this.startDate,
    this.endDate,
    this.limit,
    this.offset,
    this.sortBy,
    this.sortAscending,
  });
}

class BundleStatisticsRequest {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? designNo;
  final GarmentSize? size;
  final BundleStatus? status;
  final bool includeDesignStats;
  final bool includeSizeStats;
  final bool includeStatusStats;

  const BundleStatisticsRequest({
    this.fromDate,
    this.toDate,
    this.designNo,
    this.size,
    this.status,
    this.includeDesignStats = true,
    this.includeSizeStats = true,
    this.includeStatusStats = true,
  });
}
