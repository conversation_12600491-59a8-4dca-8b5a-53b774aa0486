import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/employee.dart';
import '../../domain/repositories/employee_repository.dart';
import '../bloc/employee_bloc.dart';
import '../widgets/employee_form.dart';
import '../widgets/real_time_employee_list.dart';
import '../widgets/manufacturing_dashboard_card.dart';
import '../widgets/manufacturing_stats_widget.dart';
import '../widgets/production_overview_widget.dart';

class ManufacturingDashboardPage extends StatefulWidget {
  const ManufacturingDashboardPage({Key? key}) : super(key: key);

  @override
  State<ManufacturingDashboardPage> createState() => _ManufacturingDashboardPageState();
}

class _ManufacturingDashboardPageState extends State<ManufacturingDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EmployeeBloc(GetIt.instance<EmployeeRepository>()),
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text(
            'Manufacturing Dashboard',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
          ),
          backgroundColor: Colors.lightBlue,
          foregroundColor: Colors.white,
          elevation: 0,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(60),
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(color: Colors.grey, width: 0.2),
                ),
              ),
              child: TabBar(
                controller: _tabController,
                indicatorColor: AppColors.primary,
                indicatorWeight: 3,
                labelColor: AppColors.primary,
                unselectedLabelColor: Colors.grey[600],
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
                tabs: const [
                  Tab(
                    icon: Icon(Icons.dashboard_outlined),
                    text: 'Overview',
                  ),
                  Tab(
                    icon: Icon(Icons.people_outline),
                    text: 'Employees',
                  ),
                  Tab(
                    icon: Icon(Icons.analytics_outlined),
                    text: 'Analytics',
                  ),
                ],
              ),
            ),
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(),
           const RealTimeEmployeeList(),
            _buildAnalyticsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Welcome to Manufacturing Dashboard',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Monitor and manage your manufacturing operations',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Quick Stats
          const ManufacturingStatsWidget(),
          const SizedBox(height: 24),

          // Production Overview
          const ProductionOverviewWidget(),
          const SizedBox(height: 24),

          // Quick Actions Grid
          _buildQuickActionsGrid(),
        ],
      ),
    );
  }

  Widget _buildQuickActionsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 2.0,
          children: [
            ManufacturingDashboardCard(
              title: 'Add Employee',
              subtitle: 'Register new employee',
              icon: Icons.person_add,
              color: Colors.blue,
              onTap: _navigateToCreateEmployee,
            ),
            ManufacturingDashboardCard(
              title: 'Production Orders',
              subtitle: 'Manage orders',
              icon: Icons.assignment,
              color: Colors.green,
              onTap: () {
                // Navigate to production orders
              },
            ),
            ManufacturingDashboardCard(
              title: 'Quality Control',
              subtitle: 'Check quality metrics',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () {
                // Navigate to quality control
              },
            ),
            ManufacturingDashboardCard(
              title: 'Reports',
              subtitle: 'View analytics',
              icon: Icons.bar_chart,
              color: Colors.purple,
              onTap: () {
                _tabController.animateTo(2); // Switch to analytics tab
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmployeesTab() {
    // Use RealTimeEmployeeList directly so it can manage its own internal scrolling
    return RealTimeEmployeeList(
      onEmployeeTap: _navigateToEmployeeDetails,
      onEmployeeEdit: _navigateToEditEmployee,
      onEmployeeDelete: _handleDeleteEmployee,
      onAddEmployee: _navigateToCreateEmployee,
    );
  }

  Widget _buildAnalyticsTab() {
    return BlocBuilder<EmployeeBloc, EmployeeState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.analytics,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Employee Analytics',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Employee statistics and analytics will be displayed here',
                    style: TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      context.read<EmployeeBloc>().add(const LoadEmployeeStatisticsRequested());
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Load Statistics'),
                  ),
                  if (state is EmployeeStatisticsLoaded) ...[
                    const SizedBox(height: 24),
                    _buildStatisticsCards(state.statistics),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatisticsCards(EmployeeStatistics statistics) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Employees',
                  statistics.totalEmployees.toString(),
                  Icons.people,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Active',
                  statistics.activeEmployees.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'New Hires',
                  statistics.newHiresThisMonth.toString(),
                  Icons.person_add,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Avg. Years',
                  statistics.averageYearsOfService.toStringAsFixed(1),
                  Icons.timeline,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToEmployeeDetails(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(employee.fullName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Employee ID: ${employee.employeeId}'),
            Text('Email: ${employee.email}'),
            Text('Role: ${employee.role.displayName}'),
            Text('Department: ${employee.department.displayName}'),
            Text('Hire Date: ${employee.hireDate.toString().split(' ')[0]}'),
            Text('Status: ${employee.status.value}'),
            if (employee.phoneNumber != null)
              Text('Phone: ${employee.phoneNumber}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToEditEmployee(employee);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateEmployee() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<EmployeeBloc>(),
          child: EmployeeForm(
            onSuccess: () {
              Navigator.of(context).pop();
              _showSuccessSnackBar('Employee created successfully!');
            },
            onCancel: () => Navigator.of(context).pop(),
          ),
        ),
      ),
    );
  }

  void _navigateToEditEmployee(Employee employee) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<EmployeeBloc>(),
          child: EmployeeForm(
            existingEmployee: employee,
            onSuccess: () {
              Navigator.of(context).pop();
              _showSuccessSnackBar('Employee updated successfully!');
            },
            onCancel: () => Navigator.of(context).pop(),
          ),
        ),
      ),
    );
  }

  void _handleDeleteEmployee(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Employee'),
        content: Text(
          'Are you sure you want to delete ${employee.fullName}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<EmployeeBloc>().add(DeleteEmployeeRequested(
                employee.id,
                reason: 'Deleted by user',
              ));
              _showSuccessSnackBar('Employee deleted successfully!');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
