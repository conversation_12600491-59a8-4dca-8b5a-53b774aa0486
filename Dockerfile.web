# Multi-stage Dockerfile for HM Collection Flutter Web App
# Stage 1: Build the Flutter web application
FROM cirrusci/flutter:stable AS build

# Set working directory
WORKDIR /app

# Copy pubspec files
COPY pubspec.yaml pubspec.lock ./

# Enable web support
RUN flutter config --enable-web

# Get dependencies
RUN flutter pub get

# Copy source code
COPY . .

# Build web application
RUN flutter build web \
    --release \
    --web-renderer canvaskit \
    --base-href "/" \
    --source-maps \
    --tree-shake-icons \
    --dart-define=FLUTTER_WEB_USE_SKIA=true \
    --dart-define=FLUTTER_WEB_AUTO_DETECT=true

# Stage 2: Serve the application with Nginx
FROM nginx:alpine AS production

# Install additional packages
RUN apk add --no-cache \
    gzip \
    brotli

# Copy custom nginx configuration
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/default.conf /etc/nginx/conf.d/default.conf

# Copy built web application
COPY --from=build /app/build/web /usr/share/nginx/html

# Copy additional configuration files
COPY web/.htaccess /usr/share/nginx/html/
COPY web/web.config /usr/share/nginx/html/

# Create gzipped versions of static files
RUN find /usr/share/nginx/html -type f \( -name "*.js" -o -name "*.css" -o -name "*.html" -o -name "*.json" \) \
    -exec gzip -k {} \; \
    -exec brotli -k {} \;

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Expose port
EXPOSE 80

# Add labels for better container management
LABEL maintainer="HM Collection Team"
LABEL description="HM Collection Manufacturing Management Web Application"
LABEL version="1.0.0"

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
