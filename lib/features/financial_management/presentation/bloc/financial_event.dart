part of 'financial_bloc.dart';


/// Base class for all financial events
abstract class FinancialEvent extends Equatable {
  const FinancialEvent();

  @override
  List<Object?> get props => [];
}

/// Event to get financial summary
class GetFinancialSummaryRequested extends FinancialEvent {
  final DateRange? dateRange;
  final String? departmentId;

  const GetFinancialSummaryRequested({
    this.dateRange,
    this.departmentId,
  });

  @override
  List<Object?> get props => [dateRange, departmentId];
}

/// Event to get profit and loss data
class GetProfitLossDataRequested extends FinancialEvent {
  final DateRange dateRange;
  final String? departmentId;

  const GetProfitLossDataRequested({
    required this.dateRange,
    this.departmentId,
  });

  @override
  List<Object?> get props => [dateRange, departmentId];
}

/// Event to get budget overview
class GetBudgetOverviewRequested extends FinancialEvent {
  final String? departmentId;
  final int? year;

  const GetBudgetOverviewRequested({
    this.departmentId,
    this.year,
  });

  @override
  List<Object?> get props => [departmentId, year];
}

/// Event to get financial KPIs
class GetFinancialKPIsRequested extends FinancialEvent {
  final DateRange? dateRange;

  const GetFinancialKPIsRequested({
    this.dateRange,
  });

  @override
  List<Object?> get props => [dateRange];
}

/// Event to get expense breakdown
class GetExpenseBreakdownRequested extends FinancialEvent {
  final DateRange dateRange;
  final String? categoryId;

  const GetExpenseBreakdownRequested({
    required this.dateRange,
    this.categoryId,
  });

  @override
  List<Object?> get props => [dateRange, categoryId];
}

/// Event to get revenue analysis
class GetRevenueAnalysisRequested extends FinancialEvent {
  final DateRange dateRange;
  final String? productId;

  const GetRevenueAnalysisRequested({
    required this.dateRange,
    this.productId,
  });

  @override
  List<Object?> get props => [dateRange, productId];
}

/// Event to create budget
class CreateBudgetRequested extends FinancialEvent {
  final CreateBudgetRequest request;

  const CreateBudgetRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Event to get budgets
class GetBudgetsRequested extends FinancialEvent {
  final BudgetFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetBudgetsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Get budget by ID event
class GetBudgetByIdRequested extends FinancialEvent {
  final String id;
  final bool loadDetails;

  const GetBudgetByIdRequested({
    required this.id,
    this.loadDetails = false,
  });

  @override
  List<Object?> get props => [id, loadDetails];
}

/// Event to update budget
class UpdateBudgetRequested extends FinancialEvent {
  final UpdateBudgetRequest request;

  const UpdateBudgetRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Event to delete budget
class DeleteBudgetRequested extends FinancialEvent {
  final String budgetId;

  const DeleteBudgetRequested(this.budgetId);

  @override
  List<Object?> get props => [budgetId];
}

/// Event to get financial reports
class GetFinancialReportsRequested extends FinancialEvent {
  final ReportType reportType;
  final DateRange dateRange;
  final ReportFilterCriteria? filters;
  final PaginationParams? pagination;

  const GetFinancialReportsRequested({
    required this.reportType,
    required this.dateRange,
    this.filters,
    this.pagination,
  });

  @override
  List<Object?> get props => [reportType, dateRange, filters, pagination];
}

/// Event to export financial data
class ExportFinancialDataRequested extends FinancialEvent {
  final ExportFormat format;
  final DateRange dateRange;
  final List<String> dataTypes;

  const ExportFinancialDataRequested({
    required this.format,
    required this.dateRange,
    required this.dataTypes,
  });

  @override
  List<Object?> get props => [format, dateRange, dataTypes];
}

// Placeholder classes - these should be defined in financial_entities.dart
class DateRange {
  final DateTime startDate;
  final DateTime endDate;

  const DateRange({
    required this.startDate,
    required this.endDate,
  });
}

enum ReportType { profitLoss, balanceSheet, cashFlow, budget }

/// Event to generate a financial report
class GenerateReportRequested extends FinancialEvent {
  final GenerateReportRequest request;

  const GenerateReportRequested(this.request);

  @override
  List<Object?> get props => [request];
}

enum ExportFormat { pdf, excel, csv }

/// Get cost centers event
class GetCostCentersRequested extends FinancialEvent {
  final CostCenterFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetCostCentersRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Get cost center by ID event
class GetCostCenterByIdRequested extends FinancialEvent {
  final String costCenterId;

  const GetCostCenterByIdRequested(this.costCenterId);

  @override
  List<Object?> get props => [costCenterId];
}

/// Create cost center event
class CreateCostCenterRequested extends FinancialEvent {
  final CreateCostCenterRequest request;

  const CreateCostCenterRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update cost center event
class UpdateCostCenterRequested extends FinancialEvent {
  final UpdateCostCenterRequest request;

  const UpdateCostCenterRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete cost center event
class DeleteCostCenterRequested extends FinancialEvent {
  final String costCenterId;

  const DeleteCostCenterRequested(this.costCenterId);

  @override
  List<Object?> get props => [costCenterId];
}

/// Search cost centers event
class SearchCostCentersRequested extends FinancialEvent {
  final String query;
  final CostCenterFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchCostCentersRequested(this.query, {this.filter, this.pagination});

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Get transactions event
class GetTransactionsRequested extends FinancialEvent {
  final PaginationParams? pagination;
  final TransactionFilterCriteria? filter;

  const GetTransactionsRequested({this.pagination, this.filter});

  @override
  List<Object?> get props => [pagination, filter];
}

/// Get transaction by ID event
class GetTransactionByIdRequested extends FinancialEvent {
  final String transactionId;

  const GetTransactionByIdRequested(this.transactionId);

  @override
  List<Object?> get props => [transactionId];
}

/// Create transaction event
class CreateTransactionRequested extends FinancialEvent {
  final CreateTransactionRequest request;

  const CreateTransactionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update transaction event
class UpdateTransactionRequested extends FinancialEvent {
  final UpdateTransactionRequest request;

  const UpdateTransactionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete transaction event
class DeleteTransactionRequested extends FinancialEvent {
  final String transactionId;

  const DeleteTransactionRequested(this.transactionId);

  @override
  List<Object?> get props => [transactionId];
}

/// Approve transaction event
class ApproveTransactionRequested extends FinancialEvent {
  final String transactionId;
  final String approverId;

  const ApproveTransactionRequested(this.transactionId, this.approverId);

  @override
  List<Object?> get props => [transactionId, approverId];
}

/// Reject transaction event
class RejectTransactionRequested extends FinancialEvent {
  final String transactionId;
  final String reason;
  final String rejectedBy;

  const RejectTransactionRequested({
    required this.transactionId,
    required this.reason,
    required this.rejectedBy,
  });

  @override
  List<Object> get props => [transactionId, reason, rejectedBy];
}

/// Approve budget event
class ApproveBudgetRequested extends FinancialEvent {
  final String budgetId;
  final String approverId;
  final String? notes;

  const ApproveBudgetRequested({
    required this.budgetId,
    required this.approverId,
    this.notes,
  });

  @override
  List<Object?> get props => [budgetId, approverId, notes];
}

/// Reject budget event
class RejectBudgetRequested extends FinancialEvent {
  final String budgetId;
  final String reason;
  final String? rejectedBy;

  const RejectBudgetRequested({
    required this.budgetId,
    required this.reason,
    this.rejectedBy,
  });

  @override
  List<Object?> get props => [budgetId, reason, rejectedBy];
}

/// Search budgets event
class SearchBudgetsRequested extends FinancialEvent {
  final String query;
  final BudgetFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchBudgetsRequested({
    required this.query,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Search transactions event
class SearchTransactionsRequested extends FinancialEvent {
  final String query;
  final PaginationParams? pagination;
  final TransactionFilterCriteria? filter;

  const SearchTransactionsRequested(this.query, {this.pagination, this.filter});

  @override
  List<Object?> get props => [query, pagination, filter];
}

/// Get cost analysis report event
class GetCostAnalysisReportRequested extends FinancialEvent {
  final DateRange dateRange;
  final String? departmentId;
  final String? costCenterId;
  final CostCategory? category;

  const GetCostAnalysisReportRequested({
    required this.dateRange,
    this.departmentId,
    this.costCenterId,
    this.category,
  });

  @override
  List<Object?> get props => [dateRange, departmentId, costCenterId, category];
}

/// Get cost trends event
class GetCostTrendsRequested extends FinancialEvent {
  final DateRange dateRange;
  final String? departmentId;
  final String? costCenterId;
  final CostCategory? category;
  final String? interval; // e.g., 'day', 'week', 'month', 'quarter', 'year'

  const GetCostTrendsRequested({
    required this.dateRange,
    this.departmentId,
    this.costCenterId,
    this.category,
    this.interval = 'month', // Default to monthly intervals
  });

  @override
  List<Object?> get props => [dateRange, departmentId, costCenterId, category, interval];
}

/// Get profit loss statement event
class GetProfitLossStatementRequested extends FinancialEvent {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  final String? departmentId;

  const GetProfitLossStatementRequested({
    required this.startDate,
    required this.endDate,
    this.costCenterId,
    this.departmentId,
  });

  @override
  List<Object?> get props => [startDate, endDate, costCenterId, departmentId];
}

/// Get budget variance report event
class GetBudgetVarianceReportRequested extends FinancialEvent {
  final DateTime startDate;
  final DateTime endDate;
  final String? budgetId;
  final String? costCenterId;

  const GetBudgetVarianceReportRequested({
    required this.startDate,
    required this.endDate,
    this.budgetId,
    this.costCenterId,
  });

  @override
  List<Object?> get props => [startDate, endDate, budgetId, costCenterId];
}
