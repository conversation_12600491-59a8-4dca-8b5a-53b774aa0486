import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/dashboard_entities.dart';
import '../repositories/dashboard_repository.dart';

/// Get dashboard layout use case
@injectable
class GetDashboardLayoutUseCase implements UseCase<DashboardLayout, IdParams> {
  final DashboardRepository _repository;

  const GetDashboardLayoutUseCase(this._repository);

  @override
  Future<Either<Failure, DashboardLayout>> call(IdParams params) async {
    return await _repository.getDashboardLayout(params.id);
  }
}

/// Update dashboard layout use case
@injectable
class UpdateDashboardLayoutUseCase implements UseCase<ApiVoidResponse, UpdateDashboardLayoutParams> {
  final DashboardRepository _repository;

  const UpdateDashboardLayoutUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(UpdateDashboardLayoutParams params) async {
    return await _repository.updateDashboardLayout(params.layout);
  }
}

/// Get dashboard statistics use case
@injectable
class GetDashboardStatisticsUseCase implements UseCase<List<DashboardStatistics>, GetDashboardDataParams> {
  final DashboardRepository _repository;

  const GetDashboardStatisticsUseCase(this._repository);

  @override
  Future<Either<Failure, List<DashboardStatistics>>> call(GetDashboardDataParams params) async {
    return await _repository.getDashboardStatistics(params.role, params.department);
  }
}

/// Get dashboard charts use case
@injectable
class GetDashboardChartsUseCase implements UseCase<List<DashboardChartData>, GetDashboardChartsParams> {
  final DashboardRepository _repository;

  const GetDashboardChartsUseCase(this._repository);

  @override
  Future<Either<Failure, List<DashboardChartData>>> call(GetDashboardChartsParams params) async {
    return await _repository.getDashboardCharts(
      params.role,
      params.department,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

/// Get recent activities use case
@injectable
class GetRecentActivitiesUseCase implements UseCase<ApiListResponse<DashboardActivity>, GetActivitiesParams> {
  final DashboardRepository _repository;

  const GetRecentActivitiesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<DashboardActivity>>> call(GetActivitiesParams params) async {
    return await _repository.getRecentActivities(
      userId: params.userId,
      role: params.role,
      department: params.department,
      pagination: params.pagination,
    );
  }
}

/// Get notifications use case
@injectable
class GetNotificationsUseCase implements UseCase<ApiListResponse<DashboardNotification>, GetNotificationsParams> {
  final DashboardRepository _repository;

  const GetNotificationsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<DashboardNotification>>> call(GetNotificationsParams params) async {
    return await _repository.getNotifications(
      userId: params.userId,
      role: params.role,
      department: params.department,
      unreadOnly: params.unreadOnly,
      pagination: params.pagination,
    );
  }
}

/// Mark notification as read use case
@injectable
class MarkNotificationAsReadUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final DashboardRepository _repository;

  const MarkNotificationAsReadUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.markNotificationAsRead(params.id);
  }
}

/// Get quick actions use case
@injectable
class GetQuickActionsUseCase implements UseCase<List<QuickAction>, GetDashboardDataParams> {
  final DashboardRepository _repository;

  const GetQuickActionsUseCase(this._repository);

  @override
  Future<Either<Failure, List<QuickAction>>> call(GetDashboardDataParams params) async {
    return await _repository.getQuickActions(params.role, params.department);
  }
}

/// Get widget data use case
@injectable
class GetWidgetDataUseCase implements UseCase<Map<String, dynamic>, GetWidgetDataParams> {
  final DashboardRepository _repository;

  const GetWidgetDataUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetWidgetDataParams params) async {
    return await _repository.getWidgetData(params.widgetId, params.configuration);
  }
}

/// Get analytics use case
@injectable
class GetAnalyticsUseCase implements UseCase<Map<String, dynamic>, GetAnalyticsParams> {
  final AnalyticsRepository _repository;

  const GetAnalyticsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetAnalyticsParams params) async {
    switch (params.type) {
      case AnalyticsType.production:
        return await _repository.getProductionAnalytics(
          department: params.department,
          startDate: params.startDate,
          endDate: params.endDate,
        );
      case AnalyticsType.quality:
        return await _repository.getQualityAnalytics(
          department: params.department,
          startDate: params.startDate,
          endDate: params.endDate,
        );
      case AnalyticsType.inventory:
        return await _repository.getInventoryAnalytics(
          startDate: params.startDate,
          endDate: params.endDate,
        );
      case AnalyticsType.orders:
        return await _repository.getOrderAnalytics(
          startDate: params.startDate,
          endDate: params.endDate,
        );
      case AnalyticsType.userActivity:
        return await _repository.getUserActivityAnalytics(
          department: params.department,
          startDate: params.startDate,
          endDate: params.endDate,
        );
      case AnalyticsType.financial:
        return await _repository.getFinancialAnalytics(
          startDate: params.startDate,
          endDate: params.endDate,
        );
      case AnalyticsType.efficiency:
        return await _repository.getEfficiencyAnalytics(
          department: params.department,
          startDate: params.startDate,
          endDate: params.endDate,
        );
    }
  }
}

// Parameter classes

class UpdateDashboardLayoutParams {
  final DashboardLayout layout;

  const UpdateDashboardLayoutParams(this.layout);
}

class GetDashboardDataParams {
  final UserRole role;
  final Department department;

  const GetDashboardDataParams(this.role, this.department);
}

class GetDashboardChartsParams {
  final UserRole role;
  final Department department;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetDashboardChartsParams(
    this.role,
    this.department, {
    this.startDate,
    this.endDate,
  });
}

class GetActivitiesParams {
  final String? userId;
  final UserRole? role;
  final Department? department;
  final PaginationParams? pagination;

  const GetActivitiesParams({
    this.userId,
    this.role,
    this.department,
    this.pagination,
  });
}

class GetNotificationsParams {
  final String? userId;
  final UserRole? role;
  final Department? department;
  final bool? unreadOnly;
  final PaginationParams? pagination;

  const GetNotificationsParams({
    this.userId,
    this.role,
    this.department,
    this.unreadOnly,
    this.pagination,
  });
}

class GetWidgetDataParams {
  final String widgetId;
  final Map<String, dynamic> configuration;

  const GetWidgetDataParams(this.widgetId, this.configuration);
}

class GetAnalyticsParams {
  final AnalyticsType type;
  final Department? department;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetAnalyticsParams(
    this.type, {
    this.department,
    this.startDate,
    this.endDate,
  });
}

enum AnalyticsType {
  production,
  quality,
  inventory,
  orders,
  userActivity,
  financial,
  efficiency,
}
