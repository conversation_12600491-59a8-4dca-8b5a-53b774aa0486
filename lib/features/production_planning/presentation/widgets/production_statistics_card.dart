import 'package:flutter/material.dart';

import '../../domain/entities/production_entities.dart';

/// A card widget that displays production statistics
class ProductionStatisticsCard extends StatelessWidget {
  final ProductionStatistics statistics;
  final VoidCallback? onTap;

  const ProductionStatisticsCard({
    Key? key,
    required this.statistics,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Production Statistics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildStatisticRow(
                context,
                'Total Orders',
                statistics.totalOrders.toString(),
                Icons.assignment,
                Colors.blue,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'In Progress',
                statistics.inProgressOrders.toString(),
                Icons.play_arrow,
                Colors.orange,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Completed',
                statistics.completedOrders.toString(),
                Icons.check_circle,
                Colors.green,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'On-time Delivery',
                '${(statistics.onTimeDeliveryRate * 100).toStringAsFixed(1)}%',
                Icons.trending_up,
                Colors.purple,
              ),
              const SizedBox(height: 16),
              _buildProgressIndicator(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    final completionRate = statistics.totalOrders > 0
        ? statistics.completedOrders / statistics.totalOrders
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Completion Rate',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${(completionRate * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: completionRate,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            completionRate >= 0.8 ? Colors.green : Colors.orange,
          ),
        ),
      ],
    );
  }
}

/// A grid of production statistics cards
class ProductionStatisticsGrid extends StatelessWidget {
  final ProductionStatistics statistics;

  const ProductionStatisticsGrid({
    Key? key,
    required this.statistics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildStatCard(
          context,
          'Total Orders',
          statistics.totalOrders.toString(),
          Icons.assignment,
          Colors.blue,
        ),
        _buildStatCard(
          context,
          'In Progress',
          statistics.inProgressOrders.toString(),
          Icons.play_arrow,
          Colors.orange,
        ),
        _buildStatCard(
          context,
          'Completed',
          statistics.completedOrders.toString(),
          Icons.check_circle,
          Colors.green,
        ),
        _buildStatCard(
          context,
          'On-time Delivery',
          '${(statistics.onTimeDeliveryRate * 100).toStringAsFixed(1)}%',
          Icons.trending_up,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Production KPI cards
class ProductionKPICards extends StatelessWidget {
  final List<ProductionKPI> kpis;

  const ProductionKPICards({
    Key? key,
    required this.kpis,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: kpis.length,
        itemBuilder: (context, index) {
          final kpi = kpis[index];
          return Container(
            width: 160,
            margin: EdgeInsets.only(
              left: index == 0 ? 16 : 8,
              right: index == kpis.length - 1 ? 16 : 8,
            ),
            child: Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      kpi.value,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: kpi.colorCode != null 
                            ? Color(int.parse(kpi.colorCode!.replaceFirst('#', '0xFF')))
                            : Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      kpi.title,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (kpi.trend != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            kpi.trend! > 0 ? Icons.trending_up : Icons.trending_down,
                            size: 16,
                            color: kpi.trend! > 0 ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${kpi.trend!.abs().toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 12,
                              color: kpi.trend! > 0 ? Colors.green : Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

