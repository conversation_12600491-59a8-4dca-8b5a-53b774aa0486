import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'pagination.dart';

part 'api_response.g.dart';

/// Generic API response wrapper
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> extends Equatable {
  @Json<PERSON>ey(name: 'success')
  final bool success;
  
  @Json<PERSON>ey(name: 'data')
  final T? data;
  
  @Json<PERSON>ey(name: 'message')
  final String? message;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'error')
  final String? error;
  
  @Json<PERSON><PERSON>(name: 'errors')
  final Map<String, List<String>>? errors;
  
  @Json<PERSON><PERSON>(name: 'pagination')
  final Pagination? pagination;
  
  @Json<PERSON>ey(name: 'meta')
  final Map<String, dynamic>? meta;
  
  @Json<PERSON><PERSON>(name: 'timestamp')
  final DateTime? timestamp;
  
  @Json<PERSON>ey(name: 'request_id')
  final String? requestId;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.errors,
    this.pagination,
    this.meta,
    this.timestamp,
    this.requestId,
  });

  /// Factory constructor for JSON deserialization
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  /// Convert to JSON
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// Create a successful response
  factory ApiResponse.success({
    T? data,
    String? message,
    Pagination? pagination,
    Map<String, dynamic>? meta,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      pagination: pagination,
      meta: meta,
      timestamp: DateTime.now(),
    );
  }

  /// Create an error response
  factory ApiResponse.error({
    String? error,
    Map<String, List<String>>? errors,
    String? message,
    Map<String, dynamic>? meta,
  }) {
    return ApiResponse<T>(
      success: false,
      error: error,
      errors: errors,
      message: message,
      meta: meta,
      timestamp: DateTime.now(),
    );
  }

  /// Check if response has data
  bool get hasData => data != null;

  /// Check if response has errors
  bool get hasErrors => error != null || (errors != null && errors!.isNotEmpty);

  /// Get first error message
  String? get firstError {
    if (error != null) return error;
    if (errors != null && errors!.isNotEmpty) {
      final firstErrorList = errors!.values.first;
      return firstErrorList.isNotEmpty ? firstErrorList.first : null;
    }
    return null;
  }

  /// Get all error messages as a flat list
  List<String> get allErrors {
    final List<String> allErrorMessages = [];
    
    if (error != null) {
      allErrorMessages.add(error!);
    }
    
    if (errors != null) {
      for (final errorList in errors!.values) {
        allErrorMessages.addAll(errorList);
      }
    }
    
    return allErrorMessages;
  }

  /// Copy with new values
  ApiResponse<T> copyWith({
    bool? success,
    T? data,
    String? message,
    String? error,
    Map<String, List<String>>? errors,
    Pagination? pagination,
    Map<String, dynamic>? meta,
    DateTime? timestamp,
    String? requestId,
  }) {
    return ApiResponse<T>(
      success: success ?? this.success,
      data: data ?? this.data,
      message: message ?? this.message,
      error: error ?? this.error,
      errors: errors ?? this.errors,
      pagination: pagination ?? this.pagination,
      meta: meta ?? this.meta,
      timestamp: timestamp ?? this.timestamp,
      requestId: requestId ?? this.requestId,
    );
  }

  @override
  List<Object?> get props => [
        success,
        data,
        message,
        error,
        errors,
        pagination,
        meta,
        timestamp,
        requestId,
      ];

  @override
  String toString() {
    return 'ApiResponse(success: $success, data: $data, message: $message, error: $error)';
  }
}

/// Specialized response for list data
@JsonSerializable(genericArgumentFactories: true)
class ApiListResponse<T> extends ApiResponse<List<T>> {
  const ApiListResponse({
    required bool success,
    List<T>? data,
    String? message,
    String? error,
    Map<String, List<String>>? errors,
    Pagination? pagination,
    Map<String, dynamic>? meta,
    DateTime? timestamp,
    String? requestId,
  }) : super(
          success: success,
          data: data,
          message: message,
          error: error,
          errors: errors,
          pagination: pagination,
          meta: meta,
          timestamp: timestamp,
          requestId: requestId,
        );

  /// Factory constructor for JSON deserialization
  factory ApiListResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) {
    final response = ApiResponse.fromJson(json, (json) {
      if (json is List) {
        return json.map((item) => fromJsonT(item)).toList();
      }
      return <T>[];
    });

    return ApiListResponse<T>(
      success: response.success,
      data: response.data,
      message: response.message,
      error: response.error,
      errors: response.errors,
      pagination: response.pagination,
      meta: response.meta,
      timestamp: response.timestamp,
      requestId: response.requestId,
    );
  }

  /// Get the count of items
  int get count => data?.length ?? 0;

  /// Check if the list is empty
  bool get isEmpty => data?.isEmpty ?? true;

  /// Check if the list is not empty
  bool get isNotEmpty => data?.isNotEmpty ?? false;
}

/// Response for operations that don't return data
class ApiVoidResponse extends ApiResponse<void> {
  const ApiVoidResponse({
     bool? success,
    String? message,
    String? error,
    Map<String, List<String>>? errors,
    Map<String, dynamic>? meta,
    DateTime? timestamp,
    String? requestId,
  }) : super(
          success: true,
          data: null,
          message: message,
          error: error,
          errors: errors,
          pagination: null,
          meta: meta,
          timestamp: timestamp,
          requestId: requestId,
        );

  /// Factory constructor for successful response
  factory ApiVoidResponse.success({
    String? message,
    Map<String, dynamic>? meta,
  }) {
    return ApiVoidResponse(
      success: true,
      message: message,
      meta: meta,
      timestamp: DateTime.now(),
    );
  }

  /// Factory constructor for JSON deserialization
  factory ApiVoidResponse.fromJson(Map<String, dynamic> json) {
    return ApiVoidResponse(
      success: json['success'] ?? false,
      message: json['message'],
      error: json['error'],
      errors: json['errors'] != null
          ? Map<String, List<String>>.from(
              json['errors'].map(
                (key, value) => MapEntry(
                  key,
                  List<String>.from(value),
                ),
              ),
            )
          : null,
      meta: json['meta'],
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : null,
      requestId: json['request_id'],
    );
  }
}
