import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/features/resource_management/domain/entities/facility_entities.dart';
import 'package:hm_collection/features/resource_management/domain/entities/machine_entities.dart';
import 'package:hm_collection/features/resource_management/domain/entities/worker_entities.dart';
import 'package:hm_collection/features/resource_management/domain/usecases/resource_params.dart';

import '../../../../shared/enums/common_enums.dart';

abstract class ResourceRepository {
  // Machine
  Future<Either<Failure, ApiListResponse<Machine>>> getMachines(
      GetMachinesParams params);
  Future<Either<Failure, ApiResponse<Machine>>> getMachineById(String machineId);
  Future<Either<Failure, ApiResponse<Machine>>> createMachine(
      CreateMachineRequest request);
  Future<Either<Failure, ApiResponse<Machine>>> updateMachine(
      UpdateMachineRequest request);
  Future<Either<Failure, ApiResponse<Machine>>> updateMachineStatus(
      UpdateMachineStatusParams params);
  Future<Either<Failure, ApiListResponse<MachineSchedule>>> getMachineSchedules(
      GetMachineSchedulesParams params);
  Future<Either<Failure, ApiResponse<MachineSchedule>>> createMachineSchedule(
      CreateMachineScheduleRequest request);
  Future<Either<Failure, ApiListResponse<MaintenanceRecord>>>
      getMachineMaintenanceRecords(GetMachineMaintenanceRecordsParams params);
  Future<Either<Failure, ApiResponse<MaintenanceRecord>>> createMaintenanceRecord(
      CreateMaintenanceRecordRequest request);
  Future<Either<Failure, ApiResponse<MaintenanceRecord>>> completeMaintenance(
      CompleteMaintenanceParams params);
  Future<Either<Failure, ApiListResponse<Machine>>> getAvailableMachines(
      GetAvailableMachinesParams params);
  Future<Either<Failure, ApiListResponse<Machine>>> getMachinesDueForMaintenance(
      GetMachinesDueForMaintenanceParams params);
  Future<Either<Failure, MachineUtilizationReport>>
      getMachineUtilizationReport(GetMachineUtilizationReportParams params);
  Future<Either<Failure, ApiListResponse<Machine>>> searchMachines(
      SearchMachinesParams params);

  // Worker
  Future<Either<Failure, ApiListResponse<Worker>>> getWorkers(
      GetWorkersParams params);
  Future<Either<Failure, ApiResponse<Worker>>> getWorkerById(String workerId);
  Future<Either<Failure, ApiResponse<Worker>>> createWorker(
      CreateWorkerRequest request);
  Future<Either<Failure, ApiResponse<Worker>>> updateWorker(
      UpdateWorkerRequest request);
  Future<Either<Failure, ApiResponse<Worker>>> updateWorkerStatus(
      UpdateWorkerStatusParams params);
  Future<Either<Failure, ApiListResponse<WorkerSchedule>>> getWorkerSchedules(
      GetWorkerSchedulesParams params);
  Future<Either<Failure, ApiResponse<WorkerSchedule>>> createWorkerSchedule(
      CreateWorkerScheduleRequest request);
  Future<Either<Failure, ApiListResponse<WorkerAttendance>>> getWorkerAttendance(
      GetWorkerAttendanceParams params);
  Future<Either<Failure, ApiResponse<WorkerAttendance>>> recordAttendance(
      RecordAttendanceRequest request);
  Future<Either<Failure, ApiListResponse<WorkerLeave>>> getWorkerLeaveRequests(
      GetWorkerLeaveRequestsParams params);
  Future<Either<Failure, ApiResponse<WorkerLeave>>> createLeaveRequest(
      CreateLeaveRequestRequest request);
  Future<Either<Failure, ApiResponse<WorkerLeave>>> approveLeaveRequest(
      ApproveLeaveRequestParams params);
  Future<Either<Failure, ApiListResponse<Worker>>> getAvailableWorkers(
      GetAvailableWorkersParams params);
  Future<Either<Failure, WorkerPerformanceReport>> getWorkerPerformanceReport(
      GetWorkerPerformanceReportParams params);
  Future<Either<Failure, ApiListResponse<Worker>>> searchWorkers(
      SearchWorkersParams params);

  // Facility
  Future<Either<Failure, ApiListResponse<Facility>>> getFacilities(
      GetFacilitiesParams params);
  Future<Either<Failure, ApiResponse<Facility>>> getFacilityById(String facilityId);
  Future<Either<Failure, ApiResponse<Facility>>> createFacility(
      CreateFacilityRequest request);
  Future<Either<Failure, ApiListResponse<Department>>> getDepartments(
      GetDepartmentsParams params);
  Future<Either<Failure, ApiResponse<Department>>> createDepartment(
      CreateDepartmentRequest request);
  Future<Either<Failure, FacilityCapacityReport>> getFacilityCapacityReport(
      GetFacilityCapacityReportParams params);
  Future<Either<Failure, FacilityUtilizationReport>>
      getFacilityUtilizationReport(GetFacilityUtilizationReportParams params);
}
