import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../entities/order_entities.dart';

/// Request model for creating a new manufacturing order
class CreateOrderRequest extends Equatable {
  /// ID of the client placing the order
  final String clientId;
  
  /// Order number (unique identifier)
  final String orderNumber;
  
  /// Type of the order
  final OrderType type;
  
  /// Priority level of the order
  final OrderPriority priority;
  
  /// Date by which the order is required
  final DateTime? requiredDate;
  
  /// List of items in the order
  final List<OrderItem> items;
  
  /// Additional notes for the order
  final String? notes;
  
  /// List of attachment URLs or IDs
  final List<String>? attachments;
  
  /// ID of the merchandiser assigned to this order
  final String? assignedMerchandiser;
  
  /// Additional metadata for the order
  final Map<String, dynamic>? metadata;

  /// Creates a new [CreateOrderRequest] instance
  const CreateOrderRequest({
    required this.clientId,
    required this.orderNumber,
    required this.type,
    required this.priority,
    this.requiredDate,
    required this.items,
    this.notes,
    this.attachments,
    this.assignedMerchandiser,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        clientId,
        orderNumber,
        type,
        priority,
        requiredDate,
        items,
        notes,
        attachments,
        assignedMerchandiser,
        metadata,
      ];

  /// Validates that the request contains all required fields
  bool get isValid {
    return clientId.isNotEmpty &&
           orderNumber.isNotEmpty &&
           items.isNotEmpty &&
           items.every((item) => item.isValid);
  }
}
