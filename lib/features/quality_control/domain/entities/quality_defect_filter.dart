import 'package:equatable/equatable.dart';

class QualityDefectFilter extends Equatable {
  final String? status;
  final String? severity;
  final String? assignedTo;
  final String? reportedBy;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? inspectionId;
  final String? referenceType;
  final String? referenceId;

  const QualityDefectFilter({
    this.status,
    this.severity,
    this.assignedTo,
    this.reportedBy,
    this.fromDate,
    this.toDate,
    this.inspectionId,
    this.referenceType,
    this.referenceId,
  });

  @override
  List<Object?> get props => [
        status,
        severity,
        assignedTo,
        reportedBy,
        fromDate,
        toDate,
        inspectionId,
        referenceType,
        referenceId,
      ];

  Map<String, dynamic> toJson() {
    return {
      if (status != null) 'status': status,
      if (severity != null) 'severity': severity,
      if (assignedTo != null) 'assignedTo': assignedTo,
      if (reportedBy != null) 'reportedBy': reportedBy,
      if (fromDate != null) 'fromDate': fromDate?.toIso8601String(),
      if (toDate != null) 'toDate': toDate?.toIso8601String(),
      if (inspectionId != null) 'inspectionId': inspectionId,
      if (referenceType != null) 'referenceType': referenceType,
      if (referenceId != null) 'referenceId': referenceId,
    }..removeWhere((key, value) => value == null);
  }
}
