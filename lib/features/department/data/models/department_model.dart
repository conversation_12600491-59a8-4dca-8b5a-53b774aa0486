import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hm_collection/features/department/domain/entities/department.dart';
import 'package:hm_collection/features/resource_management/domain/entities/facility_entities.dart';

class DepartmentModel extends Department {
  const DepartmentModel({
    required String id,
    required String name,
    String? description,
    required DepartmentType type,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? createdBy,
    String? updatedBy,
    int version = 1,
  }) : super(
          id: id,
          name: name,
          description: description,
          type: type,
          createdAt: createdAt,
          updatedAt: updatedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          version: version,
        );

  factory DepartmentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DepartmentModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'],
      type: DepartmentType.values.firstWhere(
        (e) => e.toString().split('.').last == data['type'],
        orElse: () => DepartmentType.other,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
      version: (data['version'] ?? 1).toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'version': version,
    };
  }

  Map<String, dynamic> toFirestore() {
    return toJson();
  }
}
