import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/dashboard_entities.dart';

/// Dashboard repository interface
abstract class DashboardRepository {
  /// Get dashboard layout for user
  Future<Either<Failure, DashboardLayout>> getDashboardLayout(
    String userId,
  );

  /// Update dashboard layout
  Future<Either<Failure, ApiVoidResponse>> updateDashboardLayout(
    DashboardLayout layout,
  );

  /// Get dashboard statistics
  Future<Either<Failure, List<DashboardStatistics>>> getDashboardStatistics(
    UserRole role,
    Department department,
  );

  /// Get dashboard chart data
  Future<Either<Failure, List<DashboardChartData>>> getDashboardCharts(
    UserRole role,
    Department department,
    {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get recent activities
  Future<Either<Failure, ApiListResponse<DashboardActivity>>> getRecentActivities({
    String? userId,
    UserRole? role,
    Department? department,
    PaginationParams? pagination,
  });

  /// Get notifications
  Future<Either<Failure, ApiListResponse<DashboardNotification>>> getNotifications({
    String? userId,
    UserRole? role,
    Department? department,
    bool? unreadOnly,
    PaginationParams? pagination,
  });

  /// Mark notification as read
  Future<Either<Failure, ApiVoidResponse>> markNotificationAsRead(
    String notificationId,
  );

  /// Mark all notifications as read
  Future<Either<Failure, ApiVoidResponse>> markAllNotificationsAsRead(
    String userId,
  );

  /// Get quick actions for user
  Future<Either<Failure, List<QuickAction>>> getQuickActions(
    UserRole role,
    Department department,
  );

  /// Get available widgets
  Future<Either<Failure, List<DashboardWidget>>> getAvailableWidgets(
    UserRole role,
    Department department,
  );

  /// Get widget data
  Future<Either<Failure, Map<String, dynamic>>> getWidgetData(
    String widgetId,
    Map<String, dynamic> configuration,
  );

  /// Create custom widget
  Future<Either<Failure, DashboardWidget>> createCustomWidget(
    DashboardWidget widget,
  );

  /// Update widget configuration
  Future<Either<Failure, DashboardWidget>> updateWidget(
    DashboardWidget widget,
  );

  /// Delete custom widget
  Future<Either<Failure, ApiVoidResponse>> deleteWidget(
    String widgetId,
  );

  /// Get dashboard preferences
  Future<Either<Failure, Map<String, dynamic>>> getDashboardPreferences(
    String userId,
  );

  /// Update dashboard preferences
  Future<Either<Failure, ApiVoidResponse>> updateDashboardPreferences(
    String userId,
    Map<String, dynamic> preferences,
  );

  /// Export dashboard data
  Future<Either<Failure, String>> exportDashboardData(
    UserRole role,
    Department department, {
    DateTime? startDate,
    DateTime? endDate,
    String format = 'pdf',
  });

  /// Get system health metrics
  Future<Either<Failure, Map<String, dynamic>>> getSystemHealth();

  /// Get performance metrics
  Future<Either<Failure, Map<String, dynamic>>> getPerformanceMetrics(
    UserRole role,
    Department department, {
    DateTime? startDate,
    DateTime? endDate,
  });
}

/// Analytics repository interface
abstract class AnalyticsRepository {
  /// Get production analytics
  Future<Either<Failure, Map<String, dynamic>>> getProductionAnalytics({
    Department? department,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get quality analytics
  Future<Either<Failure, Map<String, dynamic>>> getQualityAnalytics({
    Department? department,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get inventory analytics
  Future<Either<Failure, Map<String, dynamic>>> getInventoryAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get order analytics
  Future<Either<Failure, Map<String, dynamic>>> getOrderAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get user activity analytics
  Future<Either<Failure, Map<String, dynamic>>> getUserActivityAnalytics({
    Department? department,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get financial analytics
  Future<Either<Failure, Map<String, dynamic>>> getFinancialAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get efficiency analytics
  Future<Either<Failure, Map<String, dynamic>>> getEfficiencyAnalytics({
    Department? department,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get trend analysis
  Future<Either<Failure, Map<String, dynamic>>> getTrendAnalysis({
    String? metric,
    Department? department,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get comparative analysis
  Future<Either<Failure, Map<String, dynamic>>> getComparativeAnalysis({
    String? metric,
    List<Department>? departments,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get predictive analytics
  Future<Either<Failure, Map<String, dynamic>>> getPredictiveAnalytics({
    String? metric,
    Department? department,
    int forecastDays = 30,
  });
}

/// Real-time data repository interface
abstract class RealTimeDataRepository {
  /// Get real-time production status
  Stream<Map<String, dynamic>> getProductionStatusStream(
    Department department,
  );

  /// Get real-time quality metrics
  Stream<Map<String, dynamic>> getQualityMetricsStream(
    Department department,
  );

  /// Get real-time inventory levels
  Stream<Map<String, dynamic>> getInventoryLevelsStream();

  /// Get real-time system alerts
  Stream<List<DashboardNotification>> getSystemAlertsStream(
    UserRole role,
    Department department,
  );

  /// Get real-time user activity
  Stream<List<DashboardActivity>> getUserActivityStream(
    Department department,
  );

  /// Subscribe to widget updates
  Stream<Map<String, dynamic>> subscribeToWidgetUpdates(
    String widgetId,
  );

  /// Subscribe to dashboard notifications
  Stream<DashboardNotification> subscribeToDashboardNotifications(
    String userId,
  );
}
