// import 'package:dartz/dartz.dart';
// import 'package:injectable/injectable.dart';
//
// import '../../../../core/constants/app_constants.dart';
// import '../../../../core/errors/failures.dart';
// import '../../../../shared/enums/common_enums.dart';
// import '../../../../shared/models/api_response.dart';
// import '../../domain/entities/auth_tokens.dart';
// import '../../domain/entities/user.dart';
// import '../../domain/repositories/auth_repository.dart';
//
// /// Mock authentication repository for development
// @LazySingleton(as: AuthRepository, env: [Environment.dev])
// class AuthMockRepository implements AuthRepository {
// //
//   // Mock users for testing
//   final List<User> _mockUsers = [
//     User(
//       id: 'user_1',
//       createdAt: DateTime.now().subtract(const Duration(days: 30)),
//       updatedAt: DateTime.now(),
//       username: 'admin',
//       email: '<EMAIL>',
//       firstName: 'Admin',
//       lastName: 'User',
//       role: UserRole.administrator,
//       department: 'Management',
//       status: CommonStatus.active,
//       isActive: true,
//       isEmailVerified: true,
//       permissions: ['all'],
//     ),
//     User(
//       id: 'user_2',
//       createdAt: DateTime.now().subtract(const Duration(days: 20)),
//       updatedAt: DateTime.now(),
//       username: 'supervisor',
//       email: '<EMAIL>',
//       firstName: 'John',
//       lastName: 'Supervisor',
//       role: UserRole.sewingSupervisor,
//       department: 'Production',
//       status: CommonStatus.active,
//       isActive: true,
//       isEmailVerified: true,
//       permissions: ['production.view', 'production.manage', 'quality.view'],
//     ),
//     User(
//       id: 'user_3',
//       createdAt: DateTime.now().subtract(const Duration(days: 15)),
//       updatedAt: DateTime.now(),
//       username: 'operator',
//       email: '<EMAIL>',
//       firstName: 'Jane',
//       lastName: 'Operator',
//       role: UserRole.sewingOperator,
//       department: 'Production',
//       status: CommonStatus.active,
//       isActive: true,
//       isEmailVerified: true,
//       permissions: ['production.view', 'tasks.manage'],
//     ),
//   ];
//
//   // Mock tokens
//   final _mockTokens = AuthTokens(
//     accessToken: 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
//     refreshToken: 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
//     tokenType: 'Bearer',
//     expiresIn: 3600, // 1 hour
//     issuedAt: DateTime.now(),
//     expiresAt: DateTime.now().add(const Duration(hours: 1)),
//     scopes: ['all'],
//   );
//
//   @override
//   Future<Either<Failure, ApiResponse<AuthTokens>>> login(
//     LoginCredentials credentials,
//   ) async {
//     // Simulate network delay
//     await Future.delayed(const Duration(milliseconds: 1000));
//
//     // Check credentials
//     final user = _mockUsers.firstWhere(
//       (u) => u.email == credentials.email || u.username == credentials.email,
//       orElse: () => throw Exception('User not found'),
//     );
//
//     // For demo purposes, accept any password for existing users
//     if (credentials.password.isEmpty) {
//       return Left(ValidationFailure('Password is required'));
//     }
//
//     // Generate mock tokens
//     final tokens = _mockTokens.copyWith(scopes: user.permissions);
//
//     return Right(ApiResponse.success(
//       message: 'Login successful',
//       data: tokens,
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> logout() async {
//     await Future.delayed(const Duration(milliseconds: 500));
//     return Right(ApiVoidResponse.success(message: 'Logout successful'));
//   }
//
//   @override
//   Future<Either<Failure, ApiResponse<AuthTokens>>> authenticateWithBiometrics(
//     BiometricAuthData data,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiResponse.success(
//       message: 'Biometric authentication successful',
//       data: _mockTokens,
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> changePassword(
//     ChangePasswordRequest request,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Password changed successfully',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> confirmPasswordReset(
//     PasswordResetConfirmRequest confirmation,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Password reset confirmed successfully',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> disableTwoFactorAuth() async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Two-factor authentication disabled',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiResponse<AuthTokens>>> refreshToken(
//     String refreshToken,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiResponse.success(
//       message: 'Token refreshed successfully',
//       data: _mockTokens.copyWith(
//         accessToken: 'new_mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
//         refreshToken: 'new_mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
//       ),
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiResponse<User>>> getCurrentUser() async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiResponse.success(
//       message: 'User retrieved successfully',
//       data: _mockUsers.first,
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiResponse<User>>> updateProfile(User user) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiResponse.success(
//       message: 'Profile updated successfully',
//       data: user,
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> requestPasswordReset(
//     PasswordResetRequest request,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Password reset email sent',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> verifyEmail(String token) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Email verified successfully',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> revokeSession(
//     String sessionId,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Session revoked successfully',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> revokeAllOtherSessions() async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'All other sessions revoked',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, bool>> userExists(String username) async {
//     await Future.delayed(const Duration(milliseconds: 500));
//     return Right(_mockUsers.any((user) => user.username == username));
//   }
//
//   @override
//   Future<Either<Failure, bool>> validateToken(String token) async {
//     await Future.delayed(const Duration(milliseconds: 500));
//     return Right(token == _mockTokens.accessToken);
//   }
//
//   @override
//   Future<Either<Failure, List<String>>> getUserPermissions() async {
//     await Future.delayed(const Duration(milliseconds: 500));
//     return const Right([
//       'read:users',
//       'write:users',
//       'manage:settings',
//     ]);
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> updateUserPreferences(
//     UserPreferences preferences,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Preferences updated successfully',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiResponse<UserPreferences>>> getUserPreferences() async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiResponse.success(
//       message: 'Preferences retrieved successfully',
//       data: const UserPreferences(
//         theme: 'light',
//         language: 'en',
//         notificationsEnabled: true,
//       ),
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiResponse>> register(RegisterRequest request) async {
//     await Future.delayed(const Duration(seconds: 2));
//     return Right(ApiVoidResponse.success(
//       message: 'Registration successful. Please check your email to verify your account.',
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiListResponse<UserSession>>> getUserSessions() async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiListResponse.success(
//       message: 'Sessions retrieved successfully',
//       data: [
//         UserSession(
//           id: 'session_1',
//           userId: 'user_1',
//           deviceInfo: const {'os': 'Windows', 'browser': 'Chrome'},
//           lastActive: DateTime.now(),
//           isCurrent: true,
//         ),
//       ],
//       total: 1,
//       page: 1,
//       limit: 10,
//     ));
//   }
//
//   @override
//   Future<Either<Failure, ApiVoidResponse>> removeBiometrics(
//     String biometricType,
//   ) async {
//     await Future.delayed(const Duration(seconds: 1));
//     return Right(ApiVoidResponse.success(
//       message: 'Biometric authentication removed',
//     ));
//   }
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiResponse<AuthTokens>>> refreshToken(
// //     String refreshToken,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 800));
// //
// //     // Generate new mock tokens
// //     final tokens = AuthTokens(
// //       accessToken: 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
// //       refreshToken: 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
// //       tokenType: 'Bearer',
// //       expiresIn: 3600,
// //       issuedAt: DateTime.now(),
// //       expiresAt: DateTime.now().add(const Duration(hours: 1)),
// //       scopes: ['all'],
// //     );
// //
// //     return Right(ApiResponse.success(data: tokens));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiResponse<User>>> getCurrentUser() async {
// //     await Future.delayed(const Duration(milliseconds: 600));
// //
// //     // Return the admin user as current user for demo
// //     final user = _mockUsers.first;
// //     return Right(ApiResponse.success(data: user));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiResponse<User>>> register(
// //     RegisterRequest request,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 1200));
// //
// //     // Create new mock user
// //     final newUser = User(
// //       id: 'user_${DateTime.now().millisecondsSinceEpoch}',
// //       createdAt: DateTime.now(),
// //       updatedAt: DateTime.now(),
// //       username: request.email.split('@').first,
// //       email: request.email,
// //       firstName: request.firstName,
// //       lastName: request.lastName,
// //       role: request.role,
// //       department: 'Production',
// //       status: CommonStatus.pending,
// //       isActive: false,
// //       isEmailVerified: false,
// //       permissions: ['production.view'],
// //     );
// //
// //     _mockUsers.add(newUser);
// //     return Right(ApiResponse.success(data: newUser));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiVoidResponse>> requestPasswordReset(
// //     PasswordResetRequest request,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 1000));
// //     return Right(ApiVoidResponse.success(
// //       message: 'Password reset email sent successfully',
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiVoidResponse>> resetPassword(
// //     PasswordResetConfirmRequest request,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 1000));
// //     return Right(ApiVoidResponse.success(
// //       message: 'Password reset successfully',
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiVoidResponse>> changePassword(
// //     ChangePasswordRequest request,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 800));
// //     return Right(ApiVoidResponse.success(
// //       message: 'Password changed successfully',
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiVoidResponse>> verifyEmail(
// //     String token,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 600));
// //     return Right(ApiVoidResponse.success(
// //       message: 'Email verified successfully',
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiVoidResponse>> resendVerificationEmail() async {
// //     await Future.delayed(const Duration(milliseconds: 500));
// //     return Right(ApiVoidResponse.success(
// //       message: 'Verification email sent successfully',
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiResponse<AuthTokens>>> authenticateWithBiometrics(
// //     BiometricAuthRequest request,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 1500));
// //
// //     // Generate mock tokens for biometric auth
// //     final tokens = AuthTokens(
// //       accessToken: 'mock_biometric_token_${DateTime.now().millisecondsSinceEpoch}',
// //       refreshToken: 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
// //       tokenType: 'Bearer',
// //       expiresIn: 3600,
// //       issuedAt: DateTime.now(),
// //       expiresAt: DateTime.now().add(const Duration(hours: 1)),
// //       scopes: ['all'],
// //     );
// //
// //     return Right(ApiResponse.success(data: tokens));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiVoidResponse>> enableTwoFactor() async {
// //     await Future.delayed(const Duration(milliseconds: 800));
// //     return Right(ApiVoidResponse.success(
// //       message: 'Two-factor authentication enabled',
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiVoidResponse>> disableTwoFactor() async {
// //     await Future.delayed(const Duration(milliseconds: 600));
// //     return Right(ApiVoidResponse.success(
// //       message: 'Two-factor authentication disabled',
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiResponse<AuthTokens>>> verifyTwoFactor(
// //     TwoFactorVerificationRequest request,
// //   ) async {
// //     await Future.delayed(const Duration(milliseconds: 1000));
// //
// //     // Generate mock tokens for 2FA
// //     final tokens = AuthTokens(
// //       accessToken: 'mock_2fa_token_${DateTime.now().millisecondsSinceEpoch}',
// //       refreshToken: 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
// //       tokenType: 'Bearer',
// //       expiresIn: 3600,
// //       issuedAt: DateTime.now(),
// //       expiresAt: DateTime.now().add(const Duration(hours: 1)),
// //       scopes: ['all'],
// //     );
// //
// //     return Right(ApiResponse.success(data: tokens));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiListResponse<User>>> getUsers({
// //     UserRole? role,
// //     CommonStatus? status,
// //     String? search,
// //     int? page,
// //     int? limit,
// //   }) async {
// //     await Future.delayed(const Duration(milliseconds: 800));
// //
// //     var filteredUsers = _mockUsers.where((user) {
// //       if (role != null && user.role != role) return false;
// //       if (status != null && user.status != status) return false;
// //       if (search != null && search.isNotEmpty) {
// //         final searchLower = search.toLowerCase();
// //         return user.firstName.toLowerCase().contains(searchLower) ||
// //                user.lastName.toLowerCase().contains(searchLower) ||
// //                user.email.toLowerCase().contains(searchLower);
// //       }
// //       return true;
// //     }).toList();
// //
// //     return Right(ApiListResponse.success(
// //       data: filteredUsers,
// //       pagination: Pagination(
// //         currentPage: page ?? 1,
// //         perPage: limit ?? AppConstants.pageSize,
// //         total: filteredUsers.length,
// //         totalPages: 1,
// //         hasNextPage: false,
// //         hasPreviousPage: false,
// //       ),
// //     ));
// //   }
// //
// //   @override
// //   Future<Either<Failure, ApiResponse<User>>> updateUserStatus({
// //     required String userId,
// //     required CommonStatus status,
// //   }) async {
// //     await Future.delayed(const Duration(milliseconds: 600));
// //
// //     final userIndex = _mockUsers.indexWhere((u) => u.id == userId);
// //     if (userIndex == -1) {
// //       return Left(NotFoundFailure('User not found'));
// //     }
// //
// //     final updatedUser = _mockUsers[userIndex].copyWith(
// //       status: status,
// //       updatedAt: DateTime.now(),
// //     );
// //     _mockUsers[userIndex] = updatedUser;
// //
// //     return Right(ApiResponse.success(data: updatedUser));
// //   }
// // }
