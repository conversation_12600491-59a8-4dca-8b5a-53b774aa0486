import 'package:equatable/equatable.dart';

class AddProductionNoteRequest extends Equatable {
  final String productionOrderId;
  final String note;
  final String? createdBy;
  final String? attachmentUrl;

  const AddProductionNoteRequest({
    required this.productionOrderId,
    required this.note,
    this.createdBy,
    this.attachmentUrl,
  });

  Map<String, dynamic> toJson() => {
        'productionOrderId': productionOrderId,
        'note': note,
        if (createdBy != null) 'createdBy': createdBy,
        if (attachmentUrl != null) 'attachmentUrl': attachmentUrl,
      };

  @override
  List<Object?> get props => [productionOrderId, note, createdBy, attachmentUrl];
}
