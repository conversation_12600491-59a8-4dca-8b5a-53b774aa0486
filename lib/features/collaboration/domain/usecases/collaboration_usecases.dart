import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/collaboration_entities.dart';
import '../repositories/collaboration_repository.dart';

/// Get workspaces use case
@injectable
class GetWorkspacesUseCase implements UseCase<ApiListResponse<CollaborationWorkspace>, GetWorkspacesParams> {
  final CollaborationRepository _repository;

  const GetWorkspacesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationWorkspace>>> call(GetWorkspacesParams params) async {
    return await _repository.getWorkspaces(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get workspace by ID use case
@injectable
class GetWorkspaceByIdUseCase implements UseCase<ApiResponse<CollaborationWorkspace>, IdParams> {
  final CollaborationRepository _repository;

  const GetWorkspaceByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> call(IdParams params) async {
    return await _repository.getWorkspaceById(params.id);
  }
}

/// Create workspace use case
@injectable
class CreateWorkspaceUseCase implements UseCase<ApiResponse<CollaborationWorkspace>, CreateWorkspaceRequest> {
  final CollaborationRepository _repository;

  const CreateWorkspaceUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> call(CreateWorkspaceRequest params) async {
    return await _repository.createWorkspace(params);
  }
}

/// Update workspace use case
@injectable
class UpdateWorkspaceUseCase implements UseCase<ApiResponse<CollaborationWorkspace>, UpdateWorkspaceRequest> {
  final CollaborationRepository _repository;

  const UpdateWorkspaceUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> call(UpdateWorkspaceRequest params) async {
    return await _repository.updateWorkspace(params);
  }
}

/// Delete workspace use case
@injectable
class DeleteWorkspaceUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const DeleteWorkspaceUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.deleteWorkspace(params.id);
  }
}

/// Archive workspace use case
@injectable
class ArchiveWorkspaceUseCase implements UseCase<ApiResponse<CollaborationWorkspace>, IdParams> {
  final CollaborationRepository _repository;

  const ArchiveWorkspaceUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> call(IdParams params) async {
    return await _repository.archiveWorkspace(params.id);
  }
}

/// Restore workspace use case
@injectable
class RestoreWorkspaceUseCase implements UseCase<ApiResponse<CollaborationWorkspace>, IdParams> {
  final CollaborationRepository _repository;

  const RestoreWorkspaceUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationWorkspace>>> call(IdParams params) async {
    return await _repository.restoreWorkspace(params.id);
  }
}

/// Add member use case
@injectable
class AddMemberUseCase implements UseCase<ApiResponse<WorkspaceMember>, AddMemberRequest> {
  final CollaborationRepository _repository;

  const AddMemberUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<WorkspaceMember>>> call(AddMemberRequest params) async {
    return await _repository.addMember(params);
  }
}

/// Update member use case
@injectable
class UpdateMemberUseCase implements UseCase<ApiResponse<WorkspaceMember>, UpdateMemberRequest> {
  final CollaborationRepository _repository;

  const UpdateMemberUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<WorkspaceMember>>> call(UpdateMemberRequest params) async {
    return await _repository.updateMember(params);
  }
}

/// Remove member use case
@injectable
class RemoveMemberUseCase implements UseCase<ApiVoidResponse, RemoveMemberParams> {
  final CollaborationRepository _repository;

  const RemoveMemberUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(RemoveMemberParams params) async {
    return await _repository.removeMember(params.workspaceId, params.userId);
  }
}

/// Get workspace members use case
@injectable
class GetWorkspaceMembersUseCase implements UseCase<ApiListResponse<WorkspaceMember>, GetWorkspaceMembersParams> {
  final CollaborationRepository _repository;

  const GetWorkspaceMembersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<WorkspaceMember>>> call(GetWorkspaceMembersParams params) async {
    return await _repository.getWorkspaceMembers(
      workspaceId: params.workspaceId,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Invite member use case
@injectable
class InviteMemberUseCase implements UseCase<ApiResponse<WorkspaceMember>, InviteMemberRequest> {
  final CollaborationRepository _repository;

  const InviteMemberUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<WorkspaceMember>>> call(InviteMemberRequest params) async {
    return await _repository.inviteMember(params);
  }
}

/// Accept invitation use case
@injectable
class AcceptInvitationUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const AcceptInvitationUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.acceptInvitation(params.id);
  }
}

/// Decline invitation use case
@injectable
class DeclineInvitationUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const DeclineInvitationUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.declineInvitation(params.id);
  }
}

/// Get channels use case
@injectable
class GetChannelsUseCase implements UseCase<ApiListResponse<CollaborationChannel>, GetChannelsParams> {
  final CollaborationRepository _repository;

  const GetChannelsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationChannel>>> call(GetChannelsParams params) async {
    return await _repository.getChannels(
      workspaceId: params.workspaceId,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get channel by ID use case
@injectable
class GetChannelByIdUseCase implements UseCase<ApiResponse<CollaborationChannel>, IdParams> {
  final CollaborationRepository _repository;

  const GetChannelByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> call(IdParams params) async {
    return await _repository.getChannelById(params.id);
  }
}

/// Create channel use case
@injectable
class CreateChannelUseCase implements UseCase<ApiResponse<CollaborationChannel>, CreateChannelRequest> {
  final CollaborationRepository _repository;

  const CreateChannelUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> call(CreateChannelRequest params) async {
    return await _repository.createChannel(params);
  }
}

/// Update channel use case
@injectable
class UpdateChannelUseCase implements UseCase<ApiResponse<CollaborationChannel>, UpdateChannelRequest> {
  final CollaborationRepository _repository;

  const UpdateChannelUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> call(UpdateChannelRequest params) async {
    return await _repository.updateChannel(params);
  }
}

/// Delete channel use case
@injectable
class DeleteChannelUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const DeleteChannelUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.deleteChannel(params.id);
  }
}

/// Archive channel use case
@injectable
class ArchiveChannelUseCase implements UseCase<ApiResponse<CollaborationChannel>, IdParams> {
  final CollaborationRepository _repository;

  const ArchiveChannelUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> call(IdParams params) async {
    return await _repository.archiveChannel(params.id);
  }
}

/// Join channel use case
@injectable
class JoinChannelUseCase implements UseCase<ApiResponse<CollaborationChannel>, IdParams> {
  final CollaborationRepository _repository;

  const JoinChannelUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationChannel>>> call(IdParams params) async {
    return await _repository.joinChannel(params.id);
  }
}

/// Leave channel use case
@injectable
class LeaveChannelUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const LeaveChannelUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.leaveChannel(params.id);
  }
}

/// Get messages use case
@injectable
class GetMessagesUseCase implements UseCase<ApiListResponse<CollaborationMessage>, GetMessagesParams> {
  final CollaborationRepository _repository;

  const GetMessagesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> call(GetMessagesParams params) async {
    return await _repository.getMessages(
      channelId: params.channelId,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get message by ID use case
@injectable
class GetMessageByIdUseCase implements UseCase<ApiResponse<CollaborationMessage>, IdParams> {
  final CollaborationRepository _repository;

  const GetMessageByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> call(IdParams params) async {
    return await _repository.getMessageById(params.id);
  }
}

/// Send message use case
@injectable
class SendMessageUseCase implements UseCase<ApiResponse<CollaborationMessage>, SendMessageRequest> {
  final CollaborationRepository _repository;

  const SendMessageUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> call(SendMessageRequest params) async {
    return await _repository.sendMessage(params);
  }
}

/// Edit message use case
@injectable
class EditMessageUseCase implements UseCase<ApiResponse<CollaborationMessage>, EditMessageRequest> {
  final CollaborationRepository _repository;

  const EditMessageUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> call(EditMessageRequest params) async {
    return await _repository.editMessage(params);
  }
}

/// Delete message use case
@injectable
class DeleteMessageUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const DeleteMessageUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.deleteMessage(params.id);
  }
}

/// Pin message use case
@injectable
class PinMessageUseCase implements UseCase<ApiResponse<CollaborationMessage>, IdParams> {
  final CollaborationRepository _repository;

  const PinMessageUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> call(IdParams params) async {
    return await _repository.pinMessage(params.id);
  }
}

/// Unpin message use case
@injectable
class UnpinMessageUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const UnpinMessageUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.unpinMessage(params.id);
  }
}

/// Add reaction use case
@injectable
class AddReactionUseCase implements UseCase<ApiResponse<CollaborationMessage>, AddReactionRequest> {
  final CollaborationRepository _repository;

  const AddReactionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> call(AddReactionRequest params) async {
    return await _repository.addReaction(params);
  }
}

/// Remove reaction use case
@injectable
class RemoveReactionUseCase implements UseCase<ApiVoidResponse, RemoveReactionRequest> {
  final CollaborationRepository _repository;

  const RemoveReactionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(RemoveReactionRequest params) async {
    return await _repository.removeReaction(params);
  }
}

/// Mark message as read use case
@injectable
class MarkMessageAsReadUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const MarkMessageAsReadUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.markMessageAsRead(params.id);
  }
}

/// Mark channel as read use case
@injectable
class MarkChannelAsReadUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const MarkChannelAsReadUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.markChannelAsRead(params.id);
  }
}

/// Get thread messages use case
@injectable
class GetThreadMessagesUseCase implements UseCase<ApiListResponse<CollaborationMessage>, GetThreadMessagesParams> {
  final CollaborationRepository _repository;

  const GetThreadMessagesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> call(GetThreadMessagesParams params) async {
    return await _repository.getThreadMessages(
      parentMessageId: params.parentMessageId,
      pagination: params.pagination,
    );
  }
}

/// Reply to thread use case
@injectable
class ReplyToThreadUseCase implements UseCase<ApiResponse<CollaborationMessage>, ReplyToThreadRequest> {
  final CollaborationRepository _repository;

  const ReplyToThreadUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationMessage>>> call(ReplyToThreadRequest params) async {
    return await _repository.replyToThread(params);
  }
}

/// Upload file use case
@injectable
class UploadFileUseCase implements UseCase<ApiResponse<MessageAttachment>, UploadFileRequest> {
  final CollaborationRepository _repository;

  const UploadFileUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<MessageAttachment>>> call(UploadFileRequest params) async {
    return await _repository.uploadFile(params);
  }
}

/// Get file download URL use case
@injectable
class GetFileDownloadUrlUseCase implements UseCase<String, IdParams> {
  final CollaborationRepository _repository;

  const GetFileDownloadUrlUseCase(this._repository);

  @override
  Future<Either<Failure, String>> call(IdParams params) async {
    return await _repository.getFileDownloadUrl(params.id);
  }
}

/// Delete file use case
@injectable
class DeleteFileUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const DeleteFileUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.deleteFile(params.id);
  }
}

/// Get channel files use case
@injectable
class GetChannelFilesUseCase implements UseCase<ApiListResponse<MessageAttachment>, GetChannelFilesParams> {
  final CollaborationRepository _repository;

  const GetChannelFilesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<MessageAttachment>>> call(GetChannelFilesParams params) async {
    return await _repository.getChannelFiles(
      channelId: params.channelId,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Update user presence use case
@injectable
class UpdateUserPresenceUseCase implements UseCase<ApiVoidResponse, UpdatePresenceRequest> {
  final CollaborationRepository _repository;

  const UpdateUserPresenceUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(UpdatePresenceRequest params) async {
    return await _repository.updateUserPresence(params);
  }
}

/// Send typing indicator use case
@injectable
class SendTypingIndicatorUseCase implements UseCase<ApiVoidResponse, TypingIndicatorRequest> {
  final CollaborationRepository _repository;

  const SendTypingIndicatorUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(TypingIndicatorRequest params) async {
    return await _repository.sendTypingIndicator(params);
  }
}

/// Get sessions use case
@injectable
class GetSessionsUseCase implements UseCase<ApiListResponse<CollaborationSession>, GetSessionsParams> {
  final CollaborationRepository _repository;

  const GetSessionsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationSession>>> call(GetSessionsParams params) async {
    return await _repository.getSessions(
      workspaceId: params.workspaceId,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get session by ID use case
@injectable
class GetSessionByIdUseCase implements UseCase<ApiResponse<CollaborationSession>, IdParams> {
  final CollaborationRepository _repository;

  const GetSessionByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> call(IdParams params) async {
    return await _repository.getSessionById(params.id);
  }
}

/// Create session use case
@injectable
class CreateSessionUseCase implements UseCase<ApiResponse<CollaborationSession>, CreateSessionRequest> {
  final CollaborationRepository _repository;

  const CreateSessionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> call(CreateSessionRequest params) async {
    return await _repository.createSession(params);
  }
}

/// Update session use case
@injectable
class UpdateSessionUseCase implements UseCase<ApiResponse<CollaborationSession>, UpdateSessionRequest> {
  final CollaborationRepository _repository;

  const UpdateSessionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> call(UpdateSessionRequest params) async {
    return await _repository.updateSession(params);
  }
}

/// Delete session use case
@injectable
class DeleteSessionUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const DeleteSessionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.deleteSession(params.id);
  }
}

/// Start session use case
@injectable
class StartSessionUseCase implements UseCase<ApiResponse<CollaborationSession>, IdParams> {
  final CollaborationRepository _repository;

  const StartSessionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> call(IdParams params) async {
    return await _repository.startSession(params.id);
  }
}

/// End session use case
@injectable
class EndSessionUseCase implements UseCase<ApiResponse<CollaborationSession>, IdParams> {
  final CollaborationRepository _repository;

  const EndSessionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<CollaborationSession>>> call(IdParams params) async {
    return await _repository.endSession(params.id);
  }
}

/// Join session use case
@injectable
class JoinSessionUseCase implements UseCase<ApiResponse<SessionParticipant>, JoinSessionRequest> {
  final CollaborationRepository _repository;

  const JoinSessionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<SessionParticipant>>> call(JoinSessionRequest params) async {
    return await _repository.joinSession(params);
  }
}

/// Leave session use case
@injectable
class LeaveSessionUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const LeaveSessionUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.leaveSession(params.id);
  }
}

/// Update participant use case
@injectable
class UpdateParticipantUseCase implements UseCase<ApiResponse<SessionParticipant>, UpdateParticipantRequest> {
  final CollaborationRepository _repository;

  const UpdateParticipantUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<SessionParticipant>>> call(UpdateParticipantRequest params) async {
    return await _repository.updateParticipant(params);
  }
}

/// Search workspaces use case
@injectable
class SearchWorkspacesUseCase implements UseCase<ApiListResponse<CollaborationWorkspace>, SearchWorkspacesParams> {
  final CollaborationRepository _repository;

  const SearchWorkspacesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationWorkspace>>> call(SearchWorkspacesParams params) async {
    return await _repository.searchWorkspaces(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Search messages use case
@injectable
class SearchMessagesUseCase implements UseCase<ApiListResponse<CollaborationMessage>, SearchMessagesParams> {
  final CollaborationRepository _repository;

  const SearchMessagesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<CollaborationMessage>>> call(SearchMessagesParams params) async {
    return await _repository.searchMessages(
      query: params.query,
      workspaceId: params.workspaceId,
      channelId: params.channelId,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Search files use case
@injectable
class SearchFilesUseCase implements UseCase<ApiListResponse<MessageAttachment>, SearchFilesParams> {
  final CollaborationRepository _repository;

  const SearchFilesUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<MessageAttachment>>> call(SearchFilesParams params) async {
    return await _repository.searchFiles(
      query: params.query,
      workspaceId: params.workspaceId,
      channelId: params.channelId,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get workspace analytics use case
@injectable
class GetWorkspaceAnalyticsUseCase implements UseCase<Map<String, dynamic>, GetWorkspaceAnalyticsParams> {
  final CollaborationRepository _repository;

  const GetWorkspaceAnalyticsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetWorkspaceAnalyticsParams params) async {
    return await _repository.getWorkspaceAnalytics(
      workspaceId: params.workspaceId,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

/// Get channel analytics use case
@injectable
class GetChannelAnalyticsUseCase implements UseCase<Map<String, dynamic>, GetChannelAnalyticsParams> {
  final CollaborationRepository _repository;

  const GetChannelAnalyticsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetChannelAnalyticsParams params) async {
    return await _repository.getChannelAnalytics(
      channelId: params.channelId,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

/// Get user activity analytics use case
@injectable
class GetUserActivityAnalyticsUseCase implements UseCase<Map<String, dynamic>, GetUserActivityAnalyticsParams> {
  final CollaborationRepository _repository;

  const GetUserActivityAnalyticsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetUserActivityAnalyticsParams params) async {
    return await _repository.getUserActivityAnalytics(
      userId: params.userId,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

/// Get notifications use case
@injectable
class GetNotificationsUseCase implements UseCase<ApiListResponse<RealTimeActivity>, GetNotificationsParams> {
  final CollaborationRepository _repository;

  const GetNotificationsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<RealTimeActivity>>> call(GetNotificationsParams params) async {
    return await _repository.getNotifications(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Mark notification as read use case
@injectable
class MarkNotificationAsReadUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final CollaborationRepository _repository;

  const MarkNotificationAsReadUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.markNotificationAsRead(params.id);
  }
}

/// Mark all notifications as read use case
@injectable
class MarkAllNotificationsAsReadUseCase implements UseCase<ApiVoidResponse, NoParams> {
  final CollaborationRepository _repository;

  const MarkAllNotificationsAsReadUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(NoParams params) async {
    return await _repository.markAllNotificationsAsRead();
  }
}

/// Update notification settings use case
@injectable
class UpdateNotificationSettingsUseCase implements UseCase<ApiVoidResponse, UpdateNotificationSettingsRequest> {
  final CollaborationRepository _repository;

  const UpdateNotificationSettingsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(UpdateNotificationSettingsRequest params) async {
    return await _repository.updateNotificationSettings(params);
  }
}

// Parameter classes

/// Get workspaces parameters
class GetWorkspacesParams {
  final WorkspaceFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetWorkspacesParams({
    this.filter,
    this.pagination,
  });
}

/// Remove member parameters
class RemoveMemberParams {
  final String workspaceId;
  final String userId;

  const RemoveMemberParams({
    required this.workspaceId,
    required this.userId,
  });
}

/// Get workspace members parameters
class GetWorkspaceMembersParams {
  final String workspaceId;
  final MemberFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetWorkspaceMembersParams({
    required this.workspaceId,
    this.filter,
    this.pagination,
  });
}

/// Get channels parameters
class GetChannelsParams {
  final String workspaceId;
  final ChannelFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetChannelsParams({
    required this.workspaceId,
    this.filter,
    this.pagination,
  });
}

/// Get messages parameters
class GetMessagesParams {
  final String channelId;
  final MessageFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetMessagesParams({
    required this.channelId,
    this.filter,
    this.pagination,
  });
}

/// Get thread messages parameters
class GetThreadMessagesParams {
  final String parentMessageId;
  final PaginationParams? pagination;

  const GetThreadMessagesParams({
    required this.parentMessageId,
    this.pagination,
  });
}

/// Get channel files parameters
class GetChannelFilesParams {
  final String channelId;
  final FileFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetChannelFilesParams({
    required this.channelId,
    this.filter,
    this.pagination,
  });
}

/// Get sessions parameters
class GetSessionsParams {
  final String workspaceId;
  final SessionFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetSessionsParams({
    required this.workspaceId,
    this.filter,
    this.pagination,
  });
}

/// Search workspaces parameters
class SearchWorkspacesParams {
  final String query;
  final WorkspaceFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchWorkspacesParams({
    required this.query,
    this.filter,
    this.pagination,
  });
}

/// Search messages parameters
class SearchMessagesParams {
  final String query;
  final String workspaceId;
  final String? channelId;
  final MessageFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchMessagesParams({
    required this.query,
    required this.workspaceId,
    this.channelId,
    this.filter,
    this.pagination,
  });
}

/// Search files parameters
class SearchFilesParams {
  final String query;
  final String workspaceId;
  final String? channelId;
  final FileFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchFilesParams({
    required this.query,
    required this.workspaceId,
    this.channelId,
    this.filter,
    this.pagination,
  });
}

/// Get workspace analytics parameters
class GetWorkspaceAnalyticsParams {
  final String workspaceId;
  final DateTime startDate;
  final DateTime endDate;

  const GetWorkspaceAnalyticsParams({
    required this.workspaceId,
    required this.startDate,
    required this.endDate,
  });
}

/// Get channel analytics parameters
class GetChannelAnalyticsParams {
  final String channelId;
  final DateTime startDate;
  final DateTime endDate;

  const GetChannelAnalyticsParams({
    required this.channelId,
    required this.startDate,
    required this.endDate,
  });
}

/// Get user activity analytics parameters
class GetUserActivityAnalyticsParams {
  final String userId;
  final DateTime startDate;
  final DateTime endDate;

  const GetUserActivityAnalyticsParams({
    required this.userId,
    required this.startDate,
    required this.endDate,
  });
}

/// Get notifications parameters
class GetNotificationsParams {
  final NotificationFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetNotificationsParams({
    this.filter,
    this.pagination,
  });
}
