import 'package:flutter/material.dart';

/// Custom app bar for inventory management pages
class InventoryAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onAddPressed;
  final VoidCallback? onScanPressed;
  final bool showSearch;
  final bool showFilter;
  final bool showAdd;
  final bool showScan;

  const InventoryAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.onSearchPressed,
    this.onFilterPressed,
    this.onAddPressed,
    this.onScanPressed,
    this.showSearch = true,
    this.showFilter = true,
    this.showAdd = true,
    this.showScan = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (showSearch)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: onSearchPressed,
            tooltip: 'Search',
          ),
        if (showFilter)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: onFilterPressed,
            tooltip: 'Filter',
          ),
        if (showScan)
          IconButton(
            icon: const Icon(Icons.qr_code_scanner),
            onPressed: onScanPressed,
            tooltip: 'Scan Barcode',
          ),
        if (showAdd)
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: onAddPressed,
            tooltip: 'Add Item',
          ),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Inventory app bar with search functionality
class InventorySearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchClear;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onScanPressed;
  final bool isSearching;
  final TextEditingController? searchController;
  final bool showScan;

  const InventorySearchAppBar({
    Key? key,
    required this.title,
    this.hintText = 'Search inventory items...',
    this.onSearchChanged,
    this.onSearchClear,
    this.onFilterPressed,
    this.onScanPressed,
    this.isSearching = false,
    this.searchController,
    this.showScan = true,
  }) : super(key: key);

  @override
  State<InventorySearchAppBar> createState() => _InventorySearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _InventorySearchAppBarState extends State<InventorySearchAppBar> {
  late bool _isSearching;
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _isSearching = widget.isSearching;
    _searchController = widget.searchController ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.searchController == null) {
      _searchController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: _isSearching
          ? TextField(
              controller: _searchController,
              onChanged: widget.onSearchChanged,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
                border: InputBorder.none,
              ),
              autofocus: true,
            )
          : Text(
              widget.title,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (_isSearching)
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              widget.onSearchClear?.call();
              setState(() => _isSearching = false);
            },
            tooltip: 'Clear Search',
          )
        else
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => setState(() => _isSearching = true),
            tooltip: 'Search',
          ),
        if (widget.showScan)
          IconButton(
            icon: const Icon(Icons.qr_code_scanner),
            onPressed: widget.onScanPressed,
            tooltip: 'Scan Barcode',
          ),
        if (widget.onFilterPressed != null)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: widget.onFilterPressed,
            tooltip: 'Filter',
          ),
      ],
    );
  }
}

/// Inventory app bar with tabs
class InventoryTabAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Tab> tabs;
  final TabController? controller;
  final List<Widget>? actions;

  const InventoryTabAppBar({
    Key? key,
    required this.title,
    required this.tabs,
    this.controller,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: actions,
      bottom: TabBar(
        controller: controller,
        tabs: tabs,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withOpacity(0.7),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + kTextTabBarHeight,
  );
}

/// Inventory app bar with stock alerts
class InventoryAlertAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final int lowStockCount;
  final int outOfStockCount;
  final VoidCallback? onAlertsPressed;
  final List<Widget>? actions;

  const InventoryAlertAppBar({
    Key? key,
    required this.title,
    this.lowStockCount = 0,
    this.outOfStockCount = 0,
    this.onAlertsPressed,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final hasAlerts = lowStockCount > 0 || outOfStockCount > 0;

    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (hasAlerts)
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: onAlertsPressed,
                tooltip: 'Stock Alerts',
              ),
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '${lowStockCount + outOfStockCount}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}