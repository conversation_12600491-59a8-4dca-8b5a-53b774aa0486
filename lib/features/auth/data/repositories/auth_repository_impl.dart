import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

import '../../../../core/auth/entities/user_entities.dart' as user_entities_models;
import '../../../../features/auth/domain/entities/user.dart' as auth_entities;
import '../../../../core/errors/error_handler.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/constants/api_constants.dart';
import '../../../../core/constants/app_constants.dart';
import 'dart:async';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import 'package:dio/dio.dart';
import '../../domain/entities/auth_tokens.dart';
import '../../domain/entities/biometric_auth_data.dart';
import '../../domain/entities/login_credentials.dart';
import '../../domain/entities/password_reset_confirm_request.dart';
import '../../domain/entities/two_factor_auth_data.dart';
import '../../domain/entities/user.dart' as auth_entities_models;
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../models/auth_tokens_model.dart';
import '../models/user_model.dart';

/// Authentication repository implementation
@LazySingleton(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final ApiClient _apiClient;

  const AuthRepositoryImpl(this._apiClient);

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> adminLogin(
    LoginCredentials credentials,
  ) async {
    try {
      // First, verify admin credentials with Firebase
      final userCredential = await firebase_auth.FirebaseAuth.instance
          .signInWithEmailAndPassword(
        email: credentials.email,
        password: credentials.password,
      );

      // Get the ID token
      final idToken = await userCredential.user?.getIdToken();
      if (idToken == null) {
        return Left(AuthFailure('Failed to get ID token'));
      }

      // Call your admin authentication endpoint
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.adminLogin,
        data: {
          'token': idToken,
          'email': credentials.email,
        },
      );

      return Right(ApiResponse<AuthTokens>.fromJson(
        response.data!,
        (json) => AuthTokensModel.fromJson(json as Map<String, dynamic>),
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(e.message ?? 'Authentication failed'));
    } on DioException catch (e) {
      return Left(ErrorHandler.handleException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> isEmailVerified() async {
    try {
      final user = firebase_auth.FirebaseAuth.instance.currentUser;
      if (user == null) {
        return Left(AuthFailure('No user is currently signed in'));
      }
      
      // Refresh the user's data to get the latest email verification status
      await user.reload();
      final refreshedUser = firebase_auth.FirebaseAuth.instance.currentUser;
      
      return Right(refreshedUser?.emailVerified ?? false);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(e.message ?? 'Failed to check email verification status'));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Stream<user_entities_models.AppUser?> get authStateChanges {
    return firebase_auth.FirebaseAuth.instance.authStateChanges().map((user) {
      if (user == null) {
        return null;
      }
      return user_entities_models.AppUser(
        id: user.uid,
        uid: user.uid,
        email: user.email ?? '',
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        phoneNumber: user.phoneNumber,
        // You might need to fetch role and status from your backend or a local cache
        role: UserRole.sewingOperator, // Placeholder
        status: CommonStatus.active, // Placeholder
        profile: user_entities_models.UserProfile(
          firstName: user.displayName?.split(' ').first ?? '',
          lastName: user.displayName?.split(' ').last ?? '',
          phoneNumber: user.phoneNumber ?? '',
          profileImageUrl: user.photoURL,
        ),
        permissions: [], // Placeholder
        departmentIds: [], // Placeholder
        currentDepartmentId: null, // Placeholder
        lastLoginAt: user.metadata.lastSignInTime,
        createdAt: user.metadata.creationTime ?? DateTime.now(),
        updatedAt: user.metadata.lastSignInTime ?? DateTime.now(),
        deletedAt: null, // Firebase User does not directly provide this
        metadata: {
          'creationTime': user.metadata.creationTime?.toIso8601String(),
          'lastSignInTime': user.metadata.lastSignInTime?.toIso8601String(),
        },
      );
    });
  }

  @override
  Future<Either<Failure, void>> sendEmailVerification() async {
    try {
      final user = firebase_auth.FirebaseAuth.instance.currentUser;
      if (user == null) {
        return Left(AuthFailure('No user is currently signed in'));
      }
      
      await user.sendEmailVerification();
      return const Right(unit);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(e.message ?? 'Failed to send email verification'));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Stream<user_entities_models.AppUser?> get userDataChanges => _apiClient
      .getStream<Map<String, dynamic>>('${ApiConstants.users}/me')
      .map<user_entities_models.AppUser?>((response) {
        if (response is Map<String, dynamic>) {
          final userModel = UserModel.fromJson(response as Map<String, dynamic>);
          return user_entities_models.AppUser(
            id: userModel.id,
            uid: userModel.id,
            email: userModel.email,
            displayName: '${userModel.firstName} ${userModel.lastName}',
            photoURL: userModel.profileImageUrl,
            emailVerified: userModel.isEmailVerified,
            phoneNumber: userModel.phoneNumber,
            role: userModel.role,
            status: userModel.status,
            profile: user_entities_models.UserProfile(
              firstName: userModel.firstName,
              lastName: userModel.lastName,
              phoneNumber: userModel.phoneNumber ?? '',
              profileImageUrl: userModel.profileImageUrl,
            ),
            permissions: userModel.permissions ?? [],
            departmentIds: userModel.department != null ? [userModel.department!.value] : [],
            currentDepartmentId: userModel.department?.value,
            lastLoginAt: userModel.lastLoginAt,
            createdAt: userModel.createdAt,
            updatedAt: userModel.updatedAt,
            deletedAt: userModel.deletedAt,
            metadata: {},
          );
        }
        return null;
      });

  @override
  Future<Either<Failure, ApiResponse>> register(RegisterRequest request) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.register, // Make sure this constant exists in your ApiConstants
        data: {
          'email': request.email,
          'password': request.password,
          'firstName': request.firstName,
          'lastName': request.lastName,
          'role': request.role.toString().split('.').last, // Convert enum to string
        },
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        return Right(ApiResponse.success(data: response.data));
      } else {
        return Left(ServerFailure(
          response.data?['message'] ?? 'Registration failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> login(
    LoginCredentials credentials,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.login,
        data: credentials.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final tokens = AuthTokensModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: tokens));
      } else {
        return Left(ServerFailure(
          'Login failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> logout() async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.logout,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Logout failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> refreshToken(
    String refreshToken,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.refreshToken,
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200 && response.data != null) {
        final tokens = AuthTokensModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: tokens));
      } else {
        return Left(ServerFailure(
          'Token refresh failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> getCurrentUser() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        ApiConstants.userProfile,
      );

      if (response.statusCode == 200 && response.data != null) {
        final user = UserModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: user));
      } else {
        return Left(ServerFailure(
          'Failed to get user profile',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<User>>> updateProfile(User user) async {
    try {
      final userModel = UserModel.fromEntity(user);
      final response = await _apiClient.put<Map<String, dynamic>>(
        ApiConstants.updateProfile,
        data: userModel.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final updatedUser = UserModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: updatedUser));
      } else {
        return Left(ServerFailure(
          'Failed to update profile',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> changePassword(
    ChangePasswordRequest request,
  ) async {
    try {
      final requestModel = ChangePasswordRequestModel(
        currentPassword: request.currentPassword,
        newPassword: request.newPassword,
      );
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.changePassword,
        data: requestModel.toJson(),
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Password change failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> requestPasswordReset(
    PasswordResetRequest request,
  ) async {
    try {
      final requestModel = PasswordResetRequestModel(
        email: request.email,
      );
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.forgotPassword,
        data: requestModel.toJson(),
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Password reset request failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> confirmPasswordReset(
    PasswordResetConfirmRequest confirmation,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.resetPassword,
        data: {
          'token': confirmation.token,
          'password': confirmation.newPassword,
        },
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Password reset confirmation failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> verifyEmail(String token) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.auth}/verify-email',
        data: {'token': token},
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Email verification failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> resendEmailVerification() async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.auth}/resend-verification',
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to resend email verification',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<String>>> enableTwoFactorAuth() async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.auth}/2fa/enable',
      );

      if (response.statusCode == 200 && response.data != null) {
        final qrCode = response.data!['qr_code'] as String;
        return Right(ApiResponse.success(data: qrCode));
      } else {
        return Left(ServerFailure(
          'Failed to enable two-factor authentication',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> disableTwoFactorAuth(
    String code,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.auth}/2fa/disable',
        data: {'code': code},
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to disable two-factor authentication',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> verifyTwoFactorAuth(
    TwoFactorAuthData data,
  ) async {
    try {
      final requestModel = TwoFactorAuthDataModel(
        code: data.code,
        method: data.method,
        timestamp: data.timestamp,
      );
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.auth}/2fa/verify',
        data: requestModel.toJson(),
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Two-factor authentication verification failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> authenticateWithBiometrics(
    BiometricAuthData data,
  ) async {
    try {
      final dataModel = BiometricAuthDataModel.fromEntity(data);
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.auth}/biometric/authenticate',
        data: dataModel.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final tokens = AuthTokensModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: tokens));
      } else {
        return Left(ServerFailure(
          'Biometric authentication failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> registerBiometrics(
    BiometricAuthData data,
  ) async {
    try {
      final dataModel = BiometricAuthDataModel.fromEntity(data);
      final response = await _apiClient.post<Map<String, dynamic>>(
        '${ApiConstants.auth}/biometric/register',
        data: dataModel.toJson(),
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Biometric registration failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> removeBiometrics(
    String biometricType,
  ) async {
    try {
      final response = await _apiClient.delete<Map<String, dynamic>>(
        '${ApiConstants.auth}/biometric/remove',
        data: {'biometric_type': biometricType},
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to remove biometric authentication',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<auth_entities.UserSession>>> getUserSessions() async {
    try {
      final response = await _apiClient.get<List<dynamic>>(
        '${ApiConstants.auth}/sessions',
      );

      if (response.statusCode == 200 && response.data != null) {
        final sessions = response.data!
            .map((json) => UserSessionModel.fromJson(json as Map<String, dynamic>).toEntity())
            .toList();
        return Right(ApiListResponse<auth_entities.UserSession>(
          success: true,
          data: sessions,
        ));
      } else {
        return Left(ServerFailure(
          'Failed to get user sessions',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> revokeSession(String sessionId) async {
    try {
      final response = await _apiClient.delete<Map<String, dynamic>>(
        '${ApiConstants.auth}/sessions/$sessionId',
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to revoke session',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> revokeAllOtherSessions() async {
    try {
      final response = await _apiClient.delete<Map<String, dynamic>>(
        '${ApiConstants.auth}/sessions/others',
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to revoke all other sessions',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> userExists(String username) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '${ApiConstants.auth}/check-username',
        queryParameters: {'username': username},
      );

      if (response.statusCode == 200 && response.data != null) {
        final exists = response.data!['exists'] as bool? ?? false;
        return Right(exists);
      } else {
        return Left(ServerFailure(
          'Failed to check if user exists',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> validateToken(String token) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.verifyToken,
        data: {'token': token},
      );

      if (response.statusCode == 200 && response.data != null) {
        final valid = response.data!['valid'] as bool? ?? false;
        return Right(valid);
      } else {
        return Left(ServerFailure(
          'Token validation failed',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getUserPermissions() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        ApiConstants.userPermissions,
      );

      if (response.statusCode == 200 && response.data != null) {
        final permissions = List<String>.from(response.data!['permissions'] ?? []);
        return Right(permissions);
      } else {
        return Left(ServerFailure(
          'Failed to get user permissions',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> updateUserPreferences(
    UserPreferences preferences,
  ) async {
    try {
      final preferencesModel = UserPreferencesModel.fromEntity(preferences);
      final response = await _apiClient.put<Map<String, dynamic>>(
        ApiConstants.userSettings,
        data: preferencesModel.toJson(),
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to update user preferences',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<UserPreferences>>> getUserPreferences() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        ApiConstants.userSettings,
      );

      if (response.statusCode == 200 && response.data != null) {
        final preferences = UserPreferencesModel.fromJson(response.data!).toEntity();
        return Right(ApiResponse.success(data: preferences));
      } else {
        return Left(ServerFailure(
          'Failed to get user preferences',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }
}
