import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../auth/domain/entities/user.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../features/auth/presentation/bloc/firebase_auth_bloc.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../../shared/widgets/quick_stats_widget.dart';

/// Manager Dashboard Page
class ManagerDashboardPage extends StatelessWidget {
  const ManagerDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manager Dashboard'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildManagerDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildManagerDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildKPIOverview(),
          const SizedBox(height: 24),
          _buildManagerActions(context),
          const SizedBox(height: 24),
          _buildTeamPerformance(),
          const SizedBox(height: 24),
          _buildOrdersOverview(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.purple,
              child: Text(
                user.initials,
                style: AppTextStyles.titleLarge.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Good morning, ${user.fullName.split(' ').first}!',
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Manager',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.purple,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Department performance is looking great today',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKPIOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Key Performance Indicators',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        const QuickStatsWidget(
          stats: [
            QuickStat(
              title: 'Monthly Revenue',
              value: '\$125K',
              icon: Icons.attach_money,
              color: Colors.green,
              trend: '+8%',
            ),
            QuickStat(
              title: 'Orders Completed',
              value: '89',
              icon: Icons.check_circle,
              color: Colors.blue,
              trend: '+12%',
            ),
            QuickStat(
              title: 'Team Efficiency',
              value: '94%',
              icon: Icons.trending_up,
              color: Colors.purple,
              trend: '+3%',
            ),
            QuickStat(
              title: 'Customer Satisfaction',
              value: '4.8',
              icon: Icons.star,
              color: Colors.orange,
              trend: '+0.2',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildManagerActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Management Tools',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Team Management',
              subtitle: 'Manage team members and roles',
              icon: Icons.people,
              color: Colors.blue,
              onTap: () {
                // Navigate to team management
              },
            ),
            DashboardCard(
              title: 'Order Planning',
              subtitle: 'Plan and schedule orders',
              icon: Icons.calendar_today,
              color: Colors.green,
              onTap: () {
                // Navigate to order planning
              },
            ),
            DashboardCard(
              title: 'Performance Reports',
              subtitle: 'View detailed analytics',
              icon: Icons.analytics,
              color: Colors.purple,
              onTap: () {
                // Navigate to reports
              },
            ),
            DashboardCard(
              title: 'Resource Allocation',
              subtitle: 'Manage resources and capacity',
              icon: Icons.assignment,
              color: Colors.orange,
              onTap: () {
                // Navigate to resource management
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTeamPerformance() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Team Performance',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to detailed team view
                  },
                  child: const Text('View Details'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTeamMemberItem(
              'John Smith',
              'Senior Operator',
              '98%',
              Colors.green,
              'Excellent performance this week',
            ),
            const SizedBox(height: 12),
            _buildTeamMemberItem(
              'Sarah Johnson',
              'Quality Inspector',
              '95%',
              Colors.green,
              'Consistent quality checks',
            ),
            const SizedBox(height: 12),
            _buildTeamMemberItem(
              'Mike Wilson',
              'Machine Operator',
              '87%',
              Colors.orange,
              'Needs improvement in efficiency',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamMemberItem(
    String name,
    String role,
    String performance,
    Color performanceColor,
    String note,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: performanceColor.withValues(alpha: 0.1),
            child: Text(
              name.split(' ').map((n) => n[0]).join(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: performanceColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  role,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  note,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: performanceColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              performance,
              style: AppTextStyles.bodySmall.copyWith(
                color: performanceColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Orders Overview',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to orders
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildOrderStatusCard(
                    'In Progress',
                    '24',
                    Colors.blue,
                    Icons.work_outline,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildOrderStatusCard(
                    'Completed',
                    '89',
                    Colors.green,
                    Icons.check_circle_outline,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildOrderStatusCard(
                    'Pending',
                    '12',
                    Colors.orange,
                    Icons.pending_outlined,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildOrderStatusCard(
                    'Delayed',
                    '3',
                    Colors.red,
                    Icons.warning_outlined,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderStatusCard(
    String status,
    String count,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            count,
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            status,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
