import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

@injectable
class SendEmailVerificationUseCase implements UseCase<Unit, NoParams> {
  final AuthRepository _authRepository;

  const SendEmailVerificationUseCase(this._authRepository);

  @override
  Future<Either<Failure, Unit>> call(NoParams params) async {
    return (await _authRepository.sendEmailVerification())
        .map((_) => unit);
  }
}
