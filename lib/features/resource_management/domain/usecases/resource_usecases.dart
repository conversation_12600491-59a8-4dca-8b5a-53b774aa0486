import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:hm_collection/core/usecases/usecase.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import 'package:hm_collection/features/resource_management/domain/entities/facility_entities.dart';
import 'package:hm_collection/features/resource_management/domain/entities/machine_entities.dart';
import 'package:hm_collection/features/resource_management/domain/entities/worker_entities.dart';
import 'package:hm_collection/features/resource_management/domain/repositories/resource_repository.dart';
import 'package:hm_collection/features/resource_management/domain/usecases/resource_params.dart';

import '../../../../shared/enums/common_enums.dart';

// Machine UseCases
class GetMachinesUseCase
    implements UseCase<ApiListResponse<Machine>, GetMachinesParams> {
  final ResourceRepository repository;
  GetMachinesUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Machine>>> call(
      GetMachinesParams params) async {
    return await repository.getMachines(params);
  }
}

class GetMachineByIdUseCase implements UseCase<ApiResponse<Machine>, IdParams> {
  final ResourceRepository repository;
  GetMachineByIdUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Machine>>> call(IdParams params) async {
    return await repository.getMachineById(params.id);
  }
}

class CreateMachineUseCase
    implements UseCase<ApiResponse<Machine>, CreateMachineRequest> {
  final ResourceRepository repository;
  CreateMachineUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Machine>>> call(
      CreateMachineRequest params) async {
    return await repository.createMachine(params);
  }
}

class UpdateMachineUseCase
    implements UseCase<ApiResponse<Machine>, UpdateMachineRequest> {
  final ResourceRepository repository;
  UpdateMachineUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Machine>>> call(
      UpdateMachineRequest params) async {
    return await repository.updateMachine(params);
  }
}

class UpdateMachineStatusUseCase
    implements UseCase<ApiResponse<Machine>, UpdateMachineStatusParams> {
  final ResourceRepository repository;
  UpdateMachineStatusUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Machine>>> call(
      UpdateMachineStatusParams params) async {
    return await repository.updateMachineStatus(params);
  }
}

class GetMachineSchedulesUseCase
    implements
        UseCase<ApiListResponse<MachineSchedule>, GetMachineSchedulesParams> {
  final ResourceRepository repository;
  GetMachineSchedulesUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<MachineSchedule>>> call(
      GetMachineSchedulesParams params) async {
    return await repository.getMachineSchedules(params);
  }
}

class CreateMachineScheduleUseCase
    implements
        UseCase<ApiResponse<MachineSchedule>, CreateMachineScheduleRequest> {
  final ResourceRepository repository;
  CreateMachineScheduleUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<MachineSchedule>>> call(
      CreateMachineScheduleRequest params) async {
    return await repository.createMachineSchedule(params);
  }
}

class GetMachineMaintenanceRecordsUseCase
    implements
        UseCase<ApiListResponse<MaintenanceRecord>,
            GetMachineMaintenanceRecordsParams> {
  final ResourceRepository repository;
  GetMachineMaintenanceRecordsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<MaintenanceRecord>>> call(
      GetMachineMaintenanceRecordsParams params) async {
    return await repository.getMachineMaintenanceRecords(params);
  }
}

class CreateMaintenanceRecordUseCase
    implements
        UseCase<ApiResponse<MaintenanceRecord>, CreateMaintenanceRecordRequest> {
  final ResourceRepository repository;
  CreateMaintenanceRecordUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<MaintenanceRecord>>> call(
      CreateMaintenanceRecordRequest params) async {
    return await repository.createMaintenanceRecord(params);
  }
}

class CompleteMaintenanceUseCase
    implements UseCase<ApiResponse<MaintenanceRecord>, CompleteMaintenanceParams> {
  final ResourceRepository repository;
  CompleteMaintenanceUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<MaintenanceRecord>>> call(
      CompleteMaintenanceParams params) async {
    return await repository.completeMaintenance(params);
  }
}

class GetAvailableMachinesUseCase
    implements
        UseCase<ApiListResponse<Machine>, GetAvailableMachinesParams> {
  final ResourceRepository repository;
  GetAvailableMachinesUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Machine>>> call(
      GetAvailableMachinesParams params) async {
    return await repository.getAvailableMachines(params);
  }
}

class GetMachinesDueForMaintenanceUseCase
    implements
        UseCase<ApiListResponse<Machine>, GetMachinesDueForMaintenanceParams> {
  final ResourceRepository repository;
  GetMachinesDueForMaintenanceUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Machine>>> call(
      GetMachinesDueForMaintenanceParams params) async {
    return await repository.getMachinesDueForMaintenance(params);
  }
}

class GetMachineUtilizationReportUseCase
    implements
        UseCase<MachineUtilizationReport, GetMachineUtilizationReportParams> {
  final ResourceRepository repository;
  GetMachineUtilizationReportUseCase(this.repository);
  @override
  Future<Either<Failure, MachineUtilizationReport>> call(
      GetMachineUtilizationReportParams params) async {
    return await repository.getMachineUtilizationReport(params);
  }
}

class SearchMachinesUseCase
    implements UseCase<ApiListResponse<Machine>, SearchMachinesParams> {
  final ResourceRepository repository;
  SearchMachinesUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Machine>>> call(
      SearchMachinesParams params) async {
    return await repository.searchMachines(params);
  }
}

// Worker UseCases
class GetWorkersUseCase
    implements UseCase<ApiListResponse<Worker>, GetWorkersParams> {
  final ResourceRepository repository;
  GetWorkersUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Worker>>> call(
      GetWorkersParams params) async {
    return await repository.getWorkers(params);
  }
}

class GetWorkerByIdUseCase implements UseCase<ApiResponse<Worker>, IdParams> {
  final ResourceRepository repository;
  GetWorkerByIdUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Worker>>> call(IdParams params) async {
    return await repository.getWorkerById(params.id);
  }
}

class CreateWorkerUseCase
    implements UseCase<ApiResponse<Worker>, CreateWorkerRequest> {
  final ResourceRepository repository;
  CreateWorkerUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Worker>>> call(
      CreateWorkerRequest params) async {
    return await repository.createWorker(params);
  }
}

class UpdateWorkerUseCase
    implements UseCase<ApiResponse<Worker>, UpdateWorkerRequest> {
  final ResourceRepository repository;
  UpdateWorkerUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Worker>>> call(
      UpdateWorkerRequest params) async {
    return await repository.updateWorker(params);
  }
}

class UpdateWorkerStatusUseCase
    implements UseCase<ApiResponse<Worker>, UpdateWorkerStatusParams> {
  final ResourceRepository repository;
  UpdateWorkerStatusUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Worker>>> call(
      UpdateWorkerStatusParams params) async {
    return await repository.updateWorkerStatus(params);
  }
}

class GetWorkerSchedulesUseCase
    implements
        UseCase<ApiListResponse<WorkerSchedule>, GetWorkerSchedulesParams> {
  final ResourceRepository repository;
  GetWorkerSchedulesUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<WorkerSchedule>>> call(
      GetWorkerSchedulesParams params) async {
    return await repository.getWorkerSchedules(params);
  }
}

class CreateWorkerScheduleUseCase
    implements
        UseCase<ApiResponse<WorkerSchedule>, CreateWorkerScheduleRequest> {
  final ResourceRepository repository;
  CreateWorkerScheduleUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<WorkerSchedule>>> call(
      CreateWorkerScheduleRequest params) async {
    return await repository.createWorkerSchedule(params);
  }
}

class GetWorkerAttendanceUseCase
    implements
        UseCase<ApiListResponse<WorkerAttendance>, GetWorkerAttendanceParams> {
  final ResourceRepository repository;
  GetWorkerAttendanceUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<WorkerAttendance>>> call(
      GetWorkerAttendanceParams params) async {
    return await repository.getWorkerAttendance(params);
  }
}

class RecordAttendanceUseCase
    implements UseCase<ApiResponse<WorkerAttendance>, RecordAttendanceRequest> {
  final ResourceRepository repository;
  RecordAttendanceUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<WorkerAttendance>>> call(
      RecordAttendanceRequest params) async {
    return await repository.recordAttendance(params);
  }
}

class GetWorkerLeaveRequestsUseCase
    implements
        UseCase<ApiListResponse<WorkerLeave>, GetWorkerLeaveRequestsParams> {
  final ResourceRepository repository;
  GetWorkerLeaveRequestsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<WorkerLeave>>> call(
      GetWorkerLeaveRequestsParams params) async {
    return await repository.getWorkerLeaveRequests(params);
  }
}

class CreateLeaveRequestUseCase
    implements UseCase<ApiResponse<WorkerLeave>, CreateLeaveRequestRequest> {
  final ResourceRepository repository;
  CreateLeaveRequestUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<WorkerLeave>>> call(
      CreateLeaveRequestRequest params) async {
    return await repository.createLeaveRequest(params);
  }
}

class ApproveLeaveRequestUseCase
    implements UseCase<ApiResponse<WorkerLeave>, ApproveLeaveRequestParams> {
  final ResourceRepository repository;
  ApproveLeaveRequestUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<WorkerLeave>>> call(
      ApproveLeaveRequestParams params) async {
    return await repository.approveLeaveRequest(params);
  }
}

class GetAvailableWorkersUseCase
    implements UseCase<ApiListResponse<Worker>, GetAvailableWorkersParams> {
  final ResourceRepository repository;
  GetAvailableWorkersUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Worker>>> call(
      GetAvailableWorkersParams params) async {
    return await repository.getAvailableWorkers(params);
  }
}

class GetWorkerPerformanceReportUseCase
    implements
        UseCase<WorkerPerformanceReport, GetWorkerPerformanceReportParams> {
  final ResourceRepository repository;
  GetWorkerPerformanceReportUseCase(this.repository);
  @override
  Future<Either<Failure, WorkerPerformanceReport>> call(
      GetWorkerPerformanceReportParams params) async {
    return await repository.getWorkerPerformanceReport(params);
  }
}

class SearchWorkersUseCase
    implements UseCase<ApiListResponse<Worker>, SearchWorkersParams> {
  final ResourceRepository repository;
  SearchWorkersUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Worker>>> call(
      SearchWorkersParams params) async {
    return await repository.searchWorkers(params);
  }
}

// Facility UseCases
class GetFacilitiesUseCase
    implements UseCase<ApiListResponse<Facility>, GetFacilitiesParams> {
  final ResourceRepository repository;
  GetFacilitiesUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Facility>>> call(
      GetFacilitiesParams params) async {
    return await repository.getFacilities(params);
  }
}

class GetFacilityByIdUseCase implements UseCase<ApiResponse<Facility>, IdParams> {
  final ResourceRepository repository;
  GetFacilityByIdUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Facility>>> call(IdParams params) async {
    return await repository.getFacilityById(params.id);
  }
}

class CreateFacilityUseCase
    implements UseCase<ApiResponse<Facility>, CreateFacilityRequest> {
  final ResourceRepository repository;
  CreateFacilityUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Facility>>> call(
      CreateFacilityRequest params) async {
    return await repository.createFacility(params);
  }
}

class GetDepartmentsUseCase
    implements UseCase<ApiListResponse<Department>, GetDepartmentsParams> {
  final ResourceRepository repository;
  GetDepartmentsUseCase(this.repository);
  @override
  Future<Either<Failure, ApiListResponse<Department>>> call(
      GetDepartmentsParams params) async {
    return await repository.getDepartments(params);
  }
}

class CreateDepartmentUseCase
    implements UseCase<ApiResponse<Department>, CreateDepartmentRequest> {
  final ResourceRepository repository;
  CreateDepartmentUseCase(this.repository);
  @override
  Future<Either<Failure, ApiResponse<Department>>> call(
      CreateDepartmentRequest params) async {
    return await repository.createDepartment(params);
  }
}

class GetFacilityCapacityReportUseCase
    implements
        UseCase<FacilityCapacityReport, GetFacilityCapacityReportParams> {
  final ResourceRepository repository;
  GetFacilityCapacityReportUseCase(this.repository);
  @override
  Future<Either<Failure, FacilityCapacityReport>> call(
      GetFacilityCapacityReportParams params) async {
    return await repository.getFacilityCapacityReport(params);
  }
}

class GetFacilityUtilizationReportUseCase
    implements
        UseCase<FacilityUtilizationReport, GetFacilityUtilizationReportParams> {
  final ResourceRepository repository;
  GetFacilityUtilizationReportUseCase(this.repository);
  @override
  Future<Either<Failure, FacilityUtilizationReport>> call(
      GetFacilityUtilizationReportParams params) async {
    return await repository.getFacilityUtilizationReport(params);
  }
}
