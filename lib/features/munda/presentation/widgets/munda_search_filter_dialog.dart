import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/munda_requests.dart';

/// Dialog for searching and filtering Munda records
class MundaSearchFilterDialog extends StatefulWidget {
  final MundaRecordFilterCriteria? initialFilter;
  final Function(MundaRecordFilterCriteria?) onApplyFilter;

  const MundaSearchFilterDialog({
    super.key,
    this.initialFilter,
    required this.onApplyFilter,
  });

  @override
  State<MundaSearchFilterDialog> createState() => _MundaSearchFilterDialogState();
}

class _MundaSearchFilterDialogState extends State<MundaSearchFilterDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  // Search controllers
  final _searchController = TextEditingController();
  
  // Filter controllers
  final _designNumberController = TextEditingController();
  final _lotNumberController = TextEditingController();
  final _rollNumberController = TextEditingController();
  final _checkedByController = TextEditingController();
  final _minPiecesController = TextEditingController();
  final _maxPiecesController = TextEditingController();
  final _minRateController = TextEditingController();
  final _maxRateController = TextEditingController();
  final _minAmountController = TextEditingController();
  final _maxAmountController = TextEditingController();
  
  // Filter state
  DateTime? _fromDate;
  DateTime? _toDate;
  ThreadType? _selectedThread;
  CommonStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeFromFilter();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _designNumberController.dispose();
    _lotNumberController.dispose();
    _rollNumberController.dispose();
    _checkedByController.dispose();
    _minPiecesController.dispose();
    _maxPiecesController.dispose();
    _minRateController.dispose();
    _maxRateController.dispose();
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  void _initializeFromFilter() {
    if (widget.initialFilter != null) {
      final filter = widget.initialFilter!;
      _searchController.text = filter.searchQuery ?? '';
      _designNumberController.text = filter.designNumber ?? '';
      _lotNumberController.text = filter.lotNumber ?? '';
      _rollNumberController.text = filter.rollNumber ?? '';
      _checkedByController.text = filter.checkedBy ?? '';
      _fromDate = filter.fromDate;
      _toDate = filter.toDate;
      _selectedThread = filter.threadType;
      _selectedStatus = filter.status;
      
      if (filter.minPieces != null) {
        _minPiecesController.text = filter.minPieces.toString();
      }
      if (filter.maxPieces != null) {
        _maxPiecesController.text = filter.maxPieces.toString();
      }
      if (filter.minRate != null) {
        _minRateController.text = filter.minRate.toString();
      }
      if (filter.maxRate != null) {
        _maxRateController.text = filter.maxRate.toString();
      }
      if (filter.minAmount != null) {
        _minAmountController.text = filter.minAmount.toString();
      }
      if (filter.maxAmount != null) {
        _maxAmountController.text = filter.maxAmount.toString();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            _buildHeader(),
            const SizedBox(height: 20),
            
            // Tab bar
            _buildTabBar(),
            const SizedBox(height: 20),
            
            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildSearchTab(),
                  _buildFilterTab(),
                ],
              ),
            ),
            
            // Actions
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.search,
          color: AppColors.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          'Search & Filter Records',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.close,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicator: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.search),
            text: 'Search',
          ),
          Tab(
            icon: Icon(Icons.filter_list),
            text: 'Advanced Filter',
          ),
        ],
      ),
    );
  }

  Widget _buildSearchTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Search',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          CustomTextField(
            controller: _searchController,
            label: 'Search Records',
            hintText: 'Enter design number, lot number, roll number, or remarks',
            prefixIcon: Icons.search,
            maxLines: 1,
          ),
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.info.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: AppColors.info,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Search Tips',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.info,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• Search across design numbers, lot numbers, roll numbers, and remarks\n'
                  '• Use partial matches (e.g., "ABC" will find "ABC123")\n'
                  '• Search is case-insensitive\n'
                  '• Use advanced filter for more specific criteria',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.info,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date range
          _buildDateRangeSection(),
          const SizedBox(height: 20),
          
          // Record details
          _buildRecordDetailsSection(),
          const SizedBox(height: 20),
          
          // Numeric ranges
          _buildNumericRangesSection(),
          const SizedBox(height: 20),
          
          // Status and thread
          _buildStatusThreadSection(),
        ],
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                'From Date',
                _fromDate,
                (date) => setState(() => _fromDate = date),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateField(
                'To Date',
                _toDate,
                (date) => setState(() => _toDate = date),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecordDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Record Details',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        CustomTextField(
          controller: _designNumberController,
          label: 'Design Number',
          hintText: 'Enter design number',
          prefixIcon: Icons.design_services,
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _lotNumberController,
                label: 'Lot Number',
                hintText: 'Enter lot number',
                prefixIcon: Icons.inventory,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _rollNumberController,
                label: 'Roll Number',
                hintText: 'Enter roll number',
                prefixIcon: Icons.straighten,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        CustomTextField(
          controller: _checkedByController,
          label: 'Checked By',
          hintText: 'Enter checker name',
          prefixIcon: Icons.person,
        ),
      ],
    );
  }

  Widget _buildNumericRangesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Numeric Ranges',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        // Pieces range
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _minPiecesController,
                label: 'Min Pieces',
                hintText: '0',
                prefixIcon: Icons.format_list_numbered,
                keyboardType: TextInputType.number,
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                // ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _maxPiecesController,
                label: 'Max Pieces',
                hintText: '999',
                prefixIcon: Icons.format_list_numbered,
                keyboardType: TextInputType.number,
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                // ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Rate range
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _minRateController,
                label: 'Min Rate (₹)',
                hintText: '0.00',
                prefixIcon: Icons.currency_rupee,
                keyboardType: TextInputType.number,
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                // ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _maxRateController,
                label: 'Max Rate (₹)',
                hintText: '999.99',
                prefixIcon: Icons.currency_rupee,
                keyboardType: TextInputType.number,
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                // ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Amount range
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _minAmountController,
                label: 'Min Amount (₹)',
                hintText: '0.00',
                prefixIcon: Icons.calculate,
                keyboardType: TextInputType.number,
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                // ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _maxAmountController,
                label: 'Max Amount (₹)',
                hintText: '9999.99',
                prefixIcon: Icons.calculate,
                keyboardType: TextInputType.number,
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                // ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusThreadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildDropdownField<ThreadType>(
                'Thread Type',
                _selectedThread,
                ThreadType.values,
                (value) => setState(() => _selectedThread = value),
                (thread) => thread.displayName,
                Icons.fiber_manual_record,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDropdownField<CommonStatus>(
                'Status',
                _selectedStatus,
                CommonStatus.values,
                (value) => setState(() => _selectedStatus = value),
                (status) => status.displayName,
                Icons.flag,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField(String label, DateTime? date, Function(DateTime?) onChanged) {
    return GestureDetector(
      onTap: () => _selectDate(onChanged),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withAlpha(50)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.calendar_today,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    date != null 
                        ? '${date.day}/${date.month}/${date.year}'
                        : 'Select date',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: date != null 
                          ? AppColors.textPrimary 
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (date != null)
              IconButton(
                onPressed: () => onChanged(null),
                icon: const Icon(
                  Icons.clear,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownField<T>(
    String label,
    T? value,
    List<T> items,
    Function(T?) onChanged,
    String Function(T) getDisplayName,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withAlpha(50)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              isExpanded: true,
              hint: Row(
                children: [
                  Icon(icon, color: AppColors.textSecondary, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Select $label',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              onChanged: onChanged,
              items: [
                DropdownMenuItem<T>(
                  value: null,
                  child: Text(
                    'All ${label}s',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                ...items.map<DropdownMenuItem<T>>((T item) {
                  return DropdownMenuItem<T>(
                    value: item,
                    child: Row(
                      children: [
                        Icon(icon, color: AppColors.primary, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          getDisplayName(item),
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _clearFilters,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              side: BorderSide(color: Colors.grey.withAlpha(50)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Clear All'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Apply Filters'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(Function(DateTime?) onChanged) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      onChanged(picked);
    }
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _designNumberController.clear();
      _lotNumberController.clear();
      _rollNumberController.clear();
      _checkedByController.clear();
      _minPiecesController.clear();
      _maxPiecesController.clear();
      _minRateController.clear();
      _maxRateController.clear();
      _minAmountController.clear();
      _maxAmountController.clear();
      _fromDate = null;
      _toDate = null;
      _selectedThread = null;
      _selectedStatus = null;
    });
  }

  void _applyFilters() {
    final filter = _buildFilterCriteria();
    widget.onApplyFilter(filter);
    Navigator.of(context).pop();
  }

  MundaRecordFilterCriteria? _buildFilterCriteria() {
    // Check if any filter is applied
    final hasSearchQuery = _searchController.text.trim().isNotEmpty;
    final hasDesignNumber = _designNumberController.text.trim().isNotEmpty;
    final hasLotNumber = _lotNumberController.text.trim().isNotEmpty;
    final hasRollNumber = _rollNumberController.text.trim().isNotEmpty;
    final hasCheckedBy = _checkedByController.text.trim().isNotEmpty;
    final hasMinPieces = _minPiecesController.text.trim().isNotEmpty;
    final hasMaxPieces = _maxPiecesController.text.trim().isNotEmpty;
    final hasMinRate = _minRateController.text.trim().isNotEmpty;
    final hasMaxRate = _maxRateController.text.trim().isNotEmpty;
    final hasMinAmount = _minAmountController.text.trim().isNotEmpty;
    final hasMaxAmount = _maxAmountController.text.trim().isNotEmpty;
    final hasDateRange = _fromDate != null || _toDate != null;
    final hasThread = _selectedThread != null;
    final hasStatus = _selectedStatus != null;

    if (!hasSearchQuery && !hasDesignNumber && !hasLotNumber && !hasRollNumber &&
        !hasCheckedBy && !hasMinPieces && !hasMaxPieces && !hasMinRate &&
        !hasMaxRate && !hasMinAmount && !hasMaxAmount && !hasDateRange &&
        !hasThread && !hasStatus) {
      return null; // No filters applied
    }

    return MundaRecordFilterCriteria(
      searchQuery: hasSearchQuery ? _searchController.text.trim() : null,
      designNumber: hasDesignNumber ? _designNumberController.text.trim() : null,
      lotNumber: hasLotNumber ? _lotNumberController.text.trim() : null,
      rollNumber: hasRollNumber ? _rollNumberController.text.trim() : null,
      checkedBy: hasCheckedBy ? _checkedByController.text.trim() : null,
      fromDate: _fromDate,
      toDate: _toDate,
      threadType: _selectedThread,
      status: _selectedStatus,
      minPieces: hasMinPieces ? int.tryParse(_minPiecesController.text) : null,
      maxPieces: hasMaxPieces ? int.tryParse(_maxPiecesController.text) : null,
      minRate: hasMinRate ? double.tryParse(_minRateController.text) : null,
      maxRate: hasMaxRate ? double.tryParse(_maxRateController.text) : null,
      minAmount: hasMinAmount ? double.tryParse(_minAmountController.text) : null,
      maxAmount: hasMaxAmount ? double.tryParse(_maxAmountController.text) : null,
    );
  }
}
