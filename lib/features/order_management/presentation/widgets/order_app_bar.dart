import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../bloc/order_bloc.dart';

/// Order app bar
class OrderAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Function(String) onSearch;
  final VoidCallback onFilter;
  final VoidCallback onAdd;
  final Function(OrderViewMode) onViewModeChanged;
  final VoidCallback? onSort;
  final VoidCallback? onExport;
  final VoidCallback? onImport;

  const OrderAppBar({
    super.key,
    required this.onSearch,
    required this.onFilter,
    required this.onAdd,
    required this.onViewModeChanged,
    this.onSort,
    this.onExport,
    this.onImport,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: const Text('Orders'),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _showSearchDialog(context),
          tooltip: 'Search Orders',
        ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: onFilter,
          tooltip: 'Filter Orders',
        ),
        PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, context),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view_list',
              child: ListTile(
                leading: Icon(Icons.list),
                title: Text('List View'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'view_grid',
              child: ListTile(
                leading: Icon(Icons.grid_view),
                title: Text('Grid View'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'view_kanban',
              child: ListTile(
                leading: Icon(Icons.view_kanban),
                title: Text('Kanban View'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'sort',
              child: ListTile(
                leading: Icon(Icons.sort),
                title: Text('Sort'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.upload),
                title: Text('Import'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'refresh',
              child: ListTile(
                leading: Icon(Icons.refresh),
                title: Text('Refresh'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
          tooltip: 'More Options',
        ),
      ],
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _SearchDialog(onSearch: onSearch),
    );
  }

  void _handleMenuAction(String action, BuildContext context) {
    switch (action) {
      case 'view_list':
        onViewModeChanged(OrderViewMode.list);
        break;
      case 'view_grid':
        onViewModeChanged(OrderViewMode.grid);
        break;
      case 'view_kanban':
        onViewModeChanged(OrderViewMode.kanban);
        break;
      case 'sort':
        onSort?.call();
        break;
      case 'export':
        onExport?.call();
        break;
      case 'import':
        onImport?.call();
        break;
      case 'refresh':
        // Trigger refresh
        break;
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Search dialog
class _SearchDialog extends StatefulWidget {
  final Function(String) onSearch;

  const _SearchDialog({required this.onSearch});

  @override
  State<_SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<_SearchDialog> {
  final _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Search Orders'),
      content: TextField(
        controller: _controller,
        decoration: const InputDecoration(
          hintText: 'Enter order number, client name, or product...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        autofocus: true,
        onSubmitted: (value) {
          widget.onSearch(value);
          Navigator.of(context).pop();
        },
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onSearch(_controller.text);
            Navigator.of(context).pop();
          },
          child: const Text('Search'),
        ),
      ],
    );
  }
}

/// Order view mode selector
class OrderViewModeSelector extends StatelessWidget {
  final OrderViewMode currentMode;
  final Function(OrderViewMode) onModeChanged;

  const OrderViewModeSelector({
    super.key,
    required this.currentMode,
    required this.onModeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: OrderViewMode.values.map((mode) {
        final isSelected = mode == currentMode;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2),
          child: IconButton(
            icon: Icon(mode.icon),
            onPressed: () => onModeChanged(mode),
            color: isSelected ? AppColors.primary : AppColors.textSecondary,
            tooltip: mode.displayName,
          ),
        );
      }).toList(),
    );
  }
}

/// Order action bar for bulk operations
class OrderActionBar extends StatelessWidget {
  final int selectedCount;
  final VoidCallback onSelectAll;
  final VoidCallback onDeselectAll;
  final VoidCallback onBulkEdit;
  final VoidCallback onBulkDelete;
  final VoidCallback onBulkExport;

  const OrderActionBar({
    super.key,
    required this.selectedCount,
    required this.onSelectAll,
    required this.onDeselectAll,
    required this.onBulkEdit,
    required this.onBulkDelete,
    required this.onBulkExport,
  });

  @override
  Widget build(BuildContext context) {
    if (selectedCount == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppColors.primary.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '$selectedCount selected',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: onSelectAll,
            tooltip: 'Select All',
          ),
          IconButton(
            icon: const Icon(Icons.deselect),
            onPressed: onDeselectAll,
            tooltip: 'Deselect All',
          ),
          const VerticalDivider(),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: onBulkEdit,
            tooltip: 'Bulk Edit',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: onBulkExport,
            tooltip: 'Export Selected',
          ),
          IconButton(
            icon: Icon(Icons.delete, color: AppColors.error),
            onPressed: onBulkDelete,
            tooltip: 'Delete Selected',
          ),
        ],
      ),
    );
  }
}

/// Order sort dialog
class OrderSortDialog extends StatefulWidget {
  final String? currentSortBy;
  final bool currentAscending;
  final Function(String, bool) onSort;

  const OrderSortDialog({
    super.key,
    this.currentSortBy,
    required this.currentAscending,
    required this.onSort,
  });

  @override
  State<OrderSortDialog> createState() => _OrderSortDialogState();
}

class _OrderSortDialogState extends State<OrderSortDialog> {
  String? _sortBy;
  bool _ascending = true;

  final _sortOptions = [
    {'key': 'order_number', 'label': 'Order Number'},
    {'key': 'client_name', 'label': 'Client Name'},
    {'key': 'order_date', 'label': 'Order Date'},
    {'key': 'required_date', 'label': 'Required Date'},
    {'key': 'status', 'label': 'Status'},
    {'key': 'priority', 'label': 'Priority'},
    {'key': 'total_value', 'label': 'Total Value'},
    {'key': 'completion_percentage', 'label': 'Completion'},
  ];

  @override
  void initState() {
    super.initState();
    _sortBy = widget.currentSortBy;
    _ascending = widget.currentAscending;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Sort Orders'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DropdownButtonFormField<String>(
            value: _sortBy,
            decoration: const InputDecoration(
              labelText: 'Sort by',
              border: OutlineInputBorder(),
            ),
            items: _sortOptions.map((option) {
              return DropdownMenuItem<String>(
                value: option['key'] as String,
                child: Text(option['label'] as String),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _sortBy = value;
              });
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: RadioListTile<bool>(
                  title: const Text('Ascending'),
                  value: true,
                  groupValue: _ascending,
                  onChanged: (value) {
                    setState(() {
                      _ascending = value ?? true;
                    });
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<bool>(
                  title: const Text('Descending'),
                  value: false,
                  groupValue: _ascending,
                  onChanged: (value) {
                    setState(() {
                      _ascending = value ?? false;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _sortBy != null
              ? () {
                  widget.onSort(_sortBy!, _ascending);
                  Navigator.of(context).pop();
                }
              : null,
          child: const Text('Apply'),
        ),
      ],
    );
  }
}
