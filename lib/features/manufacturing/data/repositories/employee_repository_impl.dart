import 'package:dartz/dartz.dart';
import 'package:hm_collection/features/manufacturing/domain/entities/manufacturing_entities.dart';
import 'package:hm_collection/features/manufacturing/domain/repositories/employee_repository.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../domain/entities/employee.dart';
import '../datasources/employee_remote_data_source.dart';

class EmployeeRepositoryImpl implements EmployeeRepository {
  final EmployeeRemoteDataSource remoteDataSource;

  EmployeeRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployees({
    EmployeeFilterCriteria? filter,
    int limit = 50,
  }) async {
    try {
      final response = await remoteDataSource.getEmployees(filter: filter, limit: limit);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> getEmployeeById(String id) async {
    try {
      final response = await remoteDataSource.getEmployeeById(id);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> getEmployeeByEmployeeId(String employeeId) async {
    try {
      final response = await remoteDataSource.getEmployeeByEmployeeId(employeeId);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> createEmployee(CreateEmployeeRequest request) async {
    try {
      final response = await remoteDataSource.createEmployee(request);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> updateEmployee(UpdateEmployeeRequest request) async {
    try {
      final response = await remoteDataSource.updateEmployee(request);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<void>>> deleteEmployee(String id, {String? reason}) async {
    try {
      final response = await remoteDataSource.deleteEmployee(id, reason: reason);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> searchEmployees(
    String query, {
    EmployeeFilterCriteria? filter,
    int limit = 20,
  }) async {
    try {
      final response = await remoteDataSource.searchEmployees(
        query,
        filter: filter,
        limit: limit,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesByDepartment(
    String departmentId, {
    int limit = 50,
  }) async {
    try {
      final response = await remoteDataSource.getEmployeesByDepartment(
        departmentId,
        limit: limit,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesByRole(
    String roleId, {
    int limit = 50,
  }) async {
    try {
      final response = await remoteDataSource.getEmployeesByRole(
        roleId,
        limit: limit,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<List<Employee>>>> getEmployeesBySupervisor(
    String supervisorId, {
    int limit = 50,
  }) async {
    try {
      final response = await remoteDataSource.getEmployeesBySupervisor(
        supervisorId,
        limit: limit,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Stream<List<Employee>> getEmployeesStream({
    EmployeeFilterCriteria? filter,
    int limit = 50,
  }) {
    try {
      return remoteDataSource.getEmployeesStream(
        filter: filter,
        limit: limit,
      );
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Stream<Employee?> getEmployeeStream(String id) {
    try {
      return remoteDataSource.getEmployeeStream(id);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> activateEmployee(String id) async {
    try {
      final response = await remoteDataSource.activateEmployee(id);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> deactivateEmployee(
    String id, {
    String? reason,
  }) async {
    try {
      final response = await remoteDataSource.deactivateEmployee(id, reason: reason);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> transferEmployee(
    String id,
    String newDepartmentId, {
    String? newSupervisorId,
    String? reason,
  }) async {
    try {
      final response = await remoteDataSource.transferEmployee(
        id,
        newDepartmentId,
        newSupervisorId: newSupervisorId,
        reason: reason,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Employee>>> promoteEmployee(
    String id,
    String newRoleId, {
    String? reason,
  }) async {
    try {
      final response = await remoteDataSource.promoteEmployee(
        id,
        newRoleId,
        reason: reason,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<EmployeeStatistics>>> getEmployeeStatistics() async {
    try {
      final response = await remoteDataSource.getEmployeeStatistics();
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
