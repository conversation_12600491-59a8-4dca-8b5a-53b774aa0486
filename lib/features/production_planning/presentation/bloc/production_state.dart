part of 'production_bloc.dart';

/// Base production state
abstract class ProductionState extends Equatable {
  const ProductionState();

  @override
  List<Object?> get props => [];
}

/// Initial production state
class ProductionInitial extends ProductionState {
  const ProductionInitial();
}

/// Production loading state
class ProductionLoading extends ProductionState {
  const ProductionLoading();
}

/// Production orders loaded state
class ProductionOrdersLoaded extends ProductionState {
  final List<ProductionOrder> orders;
  final Pagination? pagination;
  final ProductionFilterCriteria? filter;
  final bool isRefreshing;
  final List<String> selectedOrderIds;
  final ProductionViewMode viewMode;
  final String? sortBy;
  final bool sortAscending;

  const ProductionOrdersLoaded({
    required this.orders,
    this.pagination,
    this.filter,
    this.isRefreshing = false,
    this.selectedOrderIds = const [],
    this.viewMode = ProductionViewMode.list,
    this.sortBy,
    this.sortAscending = true,
  });

  /// Copy with new values
  ProductionOrdersLoaded copyWith({
    List<ProductionOrder>? orders,
    Pagination? pagination,
    ProductionFilterCriteria? filter,
    bool? isRefreshing,
    List<String>? selectedOrderIds,
    ProductionViewMode? viewMode,
    String? sortBy,
    bool? sortAscending,
  }) {
    return ProductionOrdersLoaded(
      orders: orders ?? this.orders,
      pagination: pagination ?? this.pagination,
      filter: filter ?? this.filter,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      selectedOrderIds: selectedOrderIds ?? this.selectedOrderIds,
      viewMode: viewMode ?? this.viewMode,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  List<Object?> get props => [
        orders,
        pagination,
        filter,
        isRefreshing,
        selectedOrderIds,
        viewMode,
        sortBy,
        sortAscending,
      ];

  /// Check if order is selected
  bool isOrderSelected(String orderId) {
    return selectedOrderIds.contains(orderId);
  }

  /// Get selected orders
  List<ProductionOrder> get selectedOrders {
    return orders.where((order) => selectedOrderIds.contains(order.id)).toList();
  }
}

/// Production order details loaded state
class ProductionOrderDetailsLoaded extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderDetailsLoaded(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production order created state
class ProductionOrderCreated extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderCreated(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production order updated state
class ProductionOrderUpdated extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderUpdated(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production order status updated state
class ProductionOrderStatusUpdated extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderStatusUpdated(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production order started state
class ProductionOrderStarted extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderStarted(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production order paused state
class ProductionOrderPaused extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderPaused(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production order resumed state
class ProductionOrderResumed extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderResumed(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production order completed state
class ProductionOrderCompleted extends ProductionState {
  final ProductionOrder order;

  const ProductionOrderCompleted(this.order);

  @override
  List<Object?> get props => [order];
}

/// Production tasks loaded state
class ProductionTasksLoaded extends ProductionState {
  final List<ProductionTask> tasks;
  final Pagination? pagination;
  final TaskFilterCriteria? filter;

  const ProductionTasksLoaded({
    required this.tasks,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [tasks, pagination, filter];
}

/// Task status updated state
class TaskStatusUpdated extends ProductionState {
  final ProductionTask task;

  const TaskStatusUpdated(this.task);

  @override
  List<Object?> get props => [task];
}

/// Task assigned to workers state
class TaskAssignedToWorkers extends ProductionState {
  final ProductionTask task;

  const TaskAssignedToWorkers(this.task);

  @override
  List<Object?> get props => [task];
}

/// Task progress updated state
class TaskProgressUpdated extends ProductionState {
  final ProductionTask task;

  const TaskProgressUpdated(this.task);

  @override
  List<Object?> get props => [task];
}

/// Resource allocations loaded state
class ResourceAllocationsLoaded extends ProductionState {
  final List<ResourceAllocation> allocations;
  final Pagination? pagination;
  final ResourceAllocationFilterCriteria? filter;

  const ResourceAllocationsLoaded({
    required this.allocations,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [allocations, pagination, filter];
}

/// Resource allocated state
class ResourceAllocated extends ProductionState {
  final ResourceAllocation allocation;

  const ResourceAllocated(this.allocation);

  @override
  List<Object?> get props => [allocation];
}

/// Resource released state
class ResourceReleased extends ProductionState {
  final String allocationId;

  const ResourceReleased(this.allocationId);

  @override
  List<Object?> get props => [allocationId];
}

/// Production statistics loaded state
class ProductionStatisticsLoaded extends ProductionState {
  final ProductionStatistics statistics;

  const ProductionStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Production capacity loaded state
class ProductionCapacityLoaded extends ProductionState {
  final ProductionCapacity capacity;

  const ProductionCapacityLoaded(this.capacity);

  @override
  List<Object?> get props => [capacity];
}

/// Production bottlenecks loaded state
class ProductionBottlenecksLoaded extends ProductionState {
  final List<ProductionBottleneck> bottlenecks;

  const ProductionBottlenecksLoaded(this.bottlenecks);

  @override
  List<Object?> get props => [bottlenecks];
}

/// Overdue production orders loaded state
class OverdueProductionOrdersLoaded extends ProductionState {
  final List<ProductionOrder> orders;
  final Pagination? pagination;

  const OverdueProductionOrdersLoaded({
    required this.orders,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, pagination];
}

/// Urgent production orders loaded state
class UrgentProductionOrdersLoaded extends ProductionState {
  final List<ProductionOrder> orders;
  final Pagination? pagination;

  const UrgentProductionOrdersLoaded({
    required this.orders,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, pagination];
}

/// Production orders searched state
class ProductionOrdersSearched extends ProductionState {
  final List<ProductionOrder> orders;
  final Pagination? pagination;
  final String query;
  final ProductionFilterCriteria? filter;

  const ProductionOrdersSearched({
    required this.orders,
    this.pagination,
    required this.query,
    this.filter,
  });

  @override
  List<Object?> get props => [orders, pagination, query, filter];
}

/// Production orders filtered state
class ProductionOrdersFiltered extends ProductionState {
  final List<ProductionOrder> orders;
  final Pagination? pagination;
  final ProductionFilterCriteria filter;

  const ProductionOrdersFiltered({
    required this.orders,
    this.pagination,
    required this.filter,
  });

  @override
  List<Object?> get props => [orders, pagination, filter];
}

/// Production error state
class ProductionError extends ProductionState {
  final String message;

  const ProductionError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Production validation error state
class ProductionValidationError extends ProductionState {
  final Map<String, String> errors;

  const ProductionValidationError(this.errors);

  @override
  List<Object?> get props => [errors];
}

/// Production operation success state
class ProductionOperationSuccess extends ProductionState {
  final String message;

  const ProductionOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Production view mode changed state
class ProductionViewModeChanged extends ProductionState {
  final ProductionViewMode viewMode;

  const ProductionViewModeChanged(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Production orders sorted state
class ProductionOrdersSorted extends ProductionState {
  final String sortBy;
  final bool ascending;

  const ProductionOrdersSorted(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Production loading more state
class ProductionLoadingMore extends ProductionState {
  const ProductionLoadingMore();
}

/// Production refreshing state
class ProductionRefreshing extends ProductionState {
  const ProductionRefreshing();
}

/// Production order created state

/// Production order deleted state
class ProductionOrderDeleted extends ProductionState {
  final String orderId;

  const ProductionOrderDeleted(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Production order operation success state
class ProductionOrderOperationSuccess extends ProductionState {
  final String message;
  final ProductionOrder? order;

  const ProductionOrderOperationSuccess(this.message, {this.order});

  @override
  List<Object?> get props => [message, order];
}
