import 'package:equatable/equatable.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/munda_requests.dart';

/// Base class for all Munda events
abstract class Munda<PERSON>vent extends Equatable {
  const MundaEvent();

  @override
  List<Object?> get props => [];
}

// ==================== RECORD EVENTS ====================

/// Event to load records
class LoadRecords extends MundaEvent {
  final MundaRecordFilterCriteria? filter;
  final PaginationParams? pagination;
  final String? userId;
  final bool refresh;

  const LoadRecords({
    this.filter,
    this.pagination,
    this.userId,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [filter, pagination, userId, refresh];
}

/// Event to load more records (pagination)
class LoadMoreRecords extends MundaEvent {
  const LoadMoreRecords();
}

/// Event to create a new record
class CreateR<PERSON>ord extends MundaEvent {
  final CreateMundaRecordRequest request;
  final String userId;

  const CreateRecord({
    required this.request,
    required this.userId,
  });

  @override
  List<Object?> get props => [request, userId];
}

/// Event to update a record
class UpdateRecord extends MundaEvent {
  final UpdateMundaRecordRequest request;
  final String userId;

  const UpdateRecord({
    required this.request,
    required this.userId,
  });

  @override
  List<Object?> get props => [request, userId];
}

/// Event to delete a record
class DeleteRecord extends MundaEvent {
  final String recordId;
  final String userId;

  const DeleteRecord({
    required this.recordId,
    required this.userId,
  });

  @override
  List<Object?> get props => [recordId, userId];
}

/// Event to load records summary
class LoadRecordsSummary extends MundaEvent {
  final MundaRecordFilterCriteria? filter;
  final String? userId;

  const LoadRecordsSummary({
    this.filter,
    this.userId,
  });

  @override
  List<Object?> get props => [filter, userId];
}

/// Event to apply filters to records
class ApplyRecordFilter extends MundaEvent {
  final MundaRecordFilterCriteria filter;

  const ApplyRecordFilter({
    required this.filter,
  });

  @override
  List<Object?> get props => [filter];
}

/// Event to clear record filters
class ClearRecordFilter extends MundaEvent {
  const ClearRecordFilter();
}

/// Event to search records
class SearchRecords extends MundaEvent {
  final String query;

  const SearchRecords({
    required this.query,
  });

  @override
  List<Object?> get props => [query];
}

// ==================== TASK EVENTS ====================

/// Event to load tasks
class LoadTasks extends MundaEvent {
  final MundaTaskFilterCriteria? filter;
  final PaginationParams? pagination;
  final bool refresh;

  const LoadTasks({
    this.filter,
    this.pagination,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [filter, pagination, refresh];
}

/// Event to load user tasks
class LoadUserTasks extends MundaEvent {
  final String userId;
  final MundaTaskFilterCriteria? filter;
  final bool refresh;

  const LoadUserTasks({
    required this.userId,
    this.filter,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [userId, filter, refresh];
}

/// Event to create a new task
class CreateTask extends MundaEvent {
  final CreateMundaTaskRequest request;
  final String createdBy;

  const CreateTask({
    required this.request,
    required this.createdBy,
  });

  @override
  List<Object?> get props => [request, createdBy];
}

/// Event to update a task
class UpdateTask extends MundaEvent {
  final UpdateMundaTaskRequest request;
  final String updatedBy;

  const UpdateTask({
    required this.request,
    required this.updatedBy,
  });

  @override
  List<Object?> get props => [request, updatedBy];
}

/// Event to assign a task
class AssignTask extends MundaEvent {
  final String taskId;
  final String assignedTo;
  final String assignedBy;

  const AssignTask({
    required this.taskId,
    required this.assignedTo,
    required this.assignedBy,
  });

  @override
  List<Object?> get props => [taskId, assignedTo, assignedBy];
}

/// Event to start a task
class StartTask extends MundaEvent {
  final String taskId;
  final String userId;

  const StartTask({
    required this.taskId,
    required this.userId,
  });

  @override
  List<Object?> get props => [taskId, userId];
}

/// Event to complete a task
class CompleteTask extends MundaEvent {
  final String taskId;
  final String userId;
  final String? notes;

  const CompleteTask({
    required this.taskId,
    required this.userId,
    this.notes,
  });

  @override
  List<Object?> get props => [taskId, userId, notes];
}

/// Event to send task to bundling
class SendTaskToBundling extends MundaEvent {
  final String taskId;
  final String userId;
  final String? notes;

  const SendTaskToBundling({
    required this.taskId,
    required this.userId,
    this.notes,
  });

  @override
  List<Object?> get props => [taskId, userId, notes];
}

/// Event to cancel a task
class CancelTask extends MundaEvent {
  final String taskId;
  final String userId;
  final String reason;

  const CancelTask({
    required this.taskId,
    required this.userId,
    required this.reason,
  });

  @override
  List<Object?> get props => [taskId, userId, reason];
}

/// Event to delete a task
class DeleteTask extends MundaEvent {
  final String taskId;
  final String deletedBy;

  const DeleteTask({
    required this.taskId,
    required this.deletedBy,
  });

  @override
  List<Object?> get props => [taskId, deletedBy];
}

/// Event to apply filters to tasks
class ApplyTaskFilter extends MundaEvent {
  final MundaTaskFilterCriteria filter;

  const ApplyTaskFilter({
    required this.filter,
  });

  @override
  List<Object?> get props => [filter];
}

/// Event to clear task filters
class ClearTaskFilter extends MundaEvent {
  const ClearTaskFilter();
}

/// Event to search tasks
class SearchTasks extends MundaEvent {
  final String query;

  const SearchTasks({
    required this.query,
  });

  @override
  List<Object?> get props => [query];
}

// ==================== GENERAL EVENTS ====================

/// Event to reset the state
class ResetMundaState extends MundaEvent {
  const ResetMundaState();
}

/// Event to refresh all data
class RefreshAllData extends MundaEvent {
  final String? userId;

  const RefreshAllData({
    this.userId,
  });

  @override
  List<Object?> get props => [userId];
}

/// Event to sync offline data
class SyncOfflineData extends MundaEvent {
  final String userId;

  const SyncOfflineData({
    required this.userId,
  });

  @override
  List<Object?> get props => [userId];
}

/// Event to load analytics data
class LoadAnalytics extends MundaEvent {
  final String? userId;
  final DateTime? fromDate;
  final DateTime? toDate;

  const LoadAnalytics({
    this.userId,
    this.fromDate,
    this.toDate,
  });

  @override
  List<Object?> get props => [userId, fromDate, toDate];
}
