import 'dart:async';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/manufacturing_task_analytics.dart';
import '../entities/manufacturing_task_entities.dart';
import '../models/manufacturing_task_requests.dart';

/// Repository interface for manufacturing task operations
abstract class ManufacturingTaskRepository {
  // Manufacturing Task Order operations
  
  /// Get all manufacturing task orders with optional filtering and pagination
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> getManufacturingTaskOrders({
    ManufacturingTaskOrderFilter? filter,
    PaginationParams? pagination,
  });

  /// Get a specific manufacturing task order by ID
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> getManufacturingTaskOrderById(
    String orderId,
  );

  /// Create a new manufacturing task order
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> createManufacturingTaskOrder(
    CreateManufacturingTaskOrderRequest request,
  );

  /// Update an existing manufacturing task order
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> updateManufacturingTaskOrder(
    UpdateManufacturingTaskOrderRequest request,
  );

  /// Delete a manufacturing task order
  Future<Either<Failure, ApiVoidResponse>> deleteManufacturingTaskOrder(
    String orderId,
  );

  /// Start a manufacturing task order
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> startManufacturingTaskOrder(
    String orderId,
  );

  /// Pause a manufacturing task order
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> pauseManufacturingTaskOrder(
    PauseManufacturingTaskOrderRequest request,
  );

  /// Resume a manufacturing task order
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> resumeManufacturingTaskOrder(
    String orderId,
  );

  /// Complete a manufacturing task order
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> completeManufacturingTaskOrder(
    String orderId,
  );

  // Manufacturing Stage Task operations

  /// Get stage tasks for a manufacturing task order
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> getStageTasksForOrder(
    GetStageTasksRequest request,
  );

  /// Get stage tasks by department
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> getStageTasksByDepartment(
    GetStageTasksByDepartmentRequest request,
  );

  /// Get stage tasks by stage
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> getStageTasksByStage(
    GetStageTasksByStageRequest request,
  );

  /// Get a specific stage task by ID
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> getStageTaskById(
    String taskId,
  );

  /// Create a new stage task
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> createStageTask(
    CreateStageTaskRequest request,
  );

  /// Update a stage task
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> updateStageTask(
    UpdateStageTaskRequest request,
  );

  /// Start a stage task
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> startStageTask(
    String taskId,
  );

  /// Complete a stage task
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> completeStageTask(
    CompleteStageTaskRequest request,
  );

  /// Assign workers to a stage task
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> assignWorkersToStageTask(
    AssignWorkersToStageTaskRequest request,
  );

  /// Update stage task progress
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> updateStageTaskProgress(
    UpdateStageTaskProgressRequest request,
  );

  // Manufacturing Stage Transfer operations

  /// Create a stage transfer
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> createStageTransfer(
    CreateStageTransferRequest request,
  );

  /// Get transfers for a manufacturing task order
  Future<Either<Failure, ApiListResponse<ManufacturingStageTransfer>>> getTransfersForOrder(
    String orderId,
  );

  /// Get pending transfers for a department
  Future<Either<Failure, ApiListResponse<ManufacturingStageTransfer>>> getPendingTransfersForDepartment(
    GetPendingTransfersRequest request,
  );

  /// Accept a stage transfer
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> acceptStageTransfer(
    AcceptStageTransferRequest request,
  );

  /// Reject a stage transfer
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> rejectStageTransfer(
    RejectStageTransferRequest request,
  );

  /// Complete a stage transfer
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> completeStageTransfer(
    String transferId,
  );

  // Notes operations

  /// Add a note to a manufacturing task order
  Future<Either<Failure, ApiResponse<ManufacturingTaskNote>>> addNoteToOrder(
    AddNoteToOrderRequest request,
  );

  /// Add a note to a stage task
  Future<Either<Failure, ApiResponse<ManufacturingTaskNote>>> addNoteToStageTask(
    AddNoteToStageTaskRequest request,
  );

  /// Get notes for a manufacturing task order
  Future<Either<Failure, ApiListResponse<ManufacturingTaskNote>>> getNotesForOrder(
    String orderId,
  );

  /// Get notes for a stage task
  Future<Either<Failure, ApiListResponse<ManufacturingTaskNote>>> getNotesForStageTask(
    String taskId,
  );

  /// Update a note
  Future<Either<Failure, ApiResponse<ManufacturingTaskNote>>> updateNote(
    UpdateNoteRequest request,
  );

  /// Delete a note
  Future<Either<Failure, ApiVoidResponse>> deleteNote(
    String noteId,
  );

  // Analytics and reporting

  /// Get manufacturing task analytics
  Future<Either<Failure, ManufacturingTaskAnalytics>> getManufacturingTaskAnalytics(
    GetManufacturingTaskAnalyticsRequest request,
  );

  /// Get stage performance metrics
  Future<Either<Failure, List<StagePerformanceMetrics>>> getStagePerformanceMetrics(
    GetStagePerformanceMetricsRequest request,
  );

  /// Get bottleneck analysis
  Future<Either<Failure, ApiResponse<List<ManufacturingBottleneck>>>> getBottleneckAnalysis(
    GetBottleneckAnalysisRequest request,
  );

  /// Get efficiency metrics
  Future<Either<Failure, ManufacturingEfficiencyMetrics>> getEfficiencyMetrics(
    GetEfficiencyMetricsRequest request,
  );

  // Search and filtering

  /// Search manufacturing task orders
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> searchManufacturingTaskOrders(
    SearchManufacturingTaskOrdersRequest request,
  );

  /// Get overdue manufacturing task orders
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> getOverdueManufacturingTaskOrders(
    GetOverdueManufacturingTaskOrdersRequest request,
  );

  /// Get urgent manufacturing task orders
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> getUrgentManufacturingTaskOrders(
    GetUrgentManufacturingTaskOrdersRequest request,
  );

  /// Get manufacturing task orders by status
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> getManufacturingTaskOrdersByStatus(
    GetManufacturingTaskOrdersByStatusRequest request,
  );

  /// Get stage tasks by worker
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> getStageTasksByWorker(
    GetStageTasksByWorkerRequest request,
  );

  /// Get stage tasks by status
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> getStageTasksByStatus(
    GetStageTasksByStatusRequest request,
  );
}
