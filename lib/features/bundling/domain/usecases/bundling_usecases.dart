import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/bundle.dart';
import '../entities/bundle_input.dart';
import '../entities/bundle_statistics.dart';
import '../repositories/bundling_repository.dart';

/// Use case for getting all bundles
@injectable
class GetAllBundlesUseCase implements UseCase<List<Bundle>, GetAllBundlesParams> {
  final BundlingRepository _repository;

  GetAllBundlesUseCase(this._repository);

  @override
  Future<Either<Failure, List<Bundle>>> call(GetAllBundlesParams params) async {
    return await _repository.getAllBundles(
      status: params.status,
      designNo: params.designNo,
      lotNo: params.lotNo,
      rollNo: params.rollNo,
      altNo: params.altNo,
      size: params.size,
      assignedTo: params.assignedTo,
      startDate: params.startDate,
      endDate: params.endDate,
      limit: params.limit,
      offset: params.offset,
    );
  }
}

/// Use case for getting a bundle by ID
@injectable
class GetBundleByIdUseCase implements UseCase<Bundle, String> {
  final BundlingRepository _repository;

  GetBundleByIdUseCase(this._repository);

  @override
  Future<Either<Failure, Bundle>> call(String params) async {
    return await _repository.getBundleById(params);
  }
}

/// Use case for creating bundles from input
@injectable
class CreateBundlesUseCase implements UseCase<List<Bundle>, CreateBundlesParams> {
  final BundlingRepository _repository;

  CreateBundlesUseCase(this._repository);

  @override
  Future<Either<Failure, List<Bundle>>> call(CreateBundlesParams params) async {
    // Validate input first
    final validationResult = await _repository.validateBundleInput(params.input);
    return validationResult.fold(
      (failure) => Left(failure),
      (isValid) async {
        if (!isValid) {
          return Left(ValidationFailure('Invalid bundle input data'));
        }
        return await _repository.createBundles(params.input, params.createdBy);
      },
    );
  }
}

/// Use case for updating a bundle
@injectable
class UpdateBundleUseCase implements UseCase<Bundle, UpdateBundleParams> {
  final BundlingRepository _repository;

  UpdateBundleUseCase(this._repository);

  @override
  Future<Either<Failure, Bundle>> call(UpdateBundleParams params) async {
    return await _repository.updateBundle(params.bundle);
  }
}

/// Use case for deleting a bundle
@injectable
class DeleteBundleUseCase implements UseCase<void, DeleteBundleParams> {
  final BundlingRepository _repository;

  DeleteBundleUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(DeleteBundleParams params) async {
    return await _repository.deleteBundle(params.bundleId, params.deletedBy);
  }
}

/// Use case for issuing bundle to sewing
@injectable
class IssueBundleToSewingUseCase implements UseCase<Bundle, IssueBundleParams> {
  final BundlingRepository _repository;

  IssueBundleToSewingUseCase(this._repository);

  @override
  Future<Either<Failure, Bundle>> call(IssueBundleParams params) async {
    return await _repository.issueBundleToSewing(
      params.bundleId,
      params.assignedTo,
      params.issuedBy,
    );
  }
}

/// Use case for starting bundle work
@injectable
class StartBundleWorkUseCase implements UseCase<Bundle, StartBundleParams> {
  final BundlingRepository _repository;

  StartBundleWorkUseCase(this._repository);

  @override
  Future<Either<Failure, Bundle>> call(StartBundleParams params) async {
    return await _repository.startBundleWork(params.bundleId, params.startedBy);
  }
}

/// Use case for completing bundle work
@injectable
class CompleteBundleWorkUseCase implements UseCase<Bundle, CompleteBundleParams> {
  final BundlingRepository _repository;

  CompleteBundleWorkUseCase(this._repository);

  @override
  Future<Either<Failure, Bundle>> call(CompleteBundleParams params) async {
    return await _repository.completeBundleWork(
      params.bundleId,
      params.completedBy,
      params.remarks,
    );
  }
}

/// Use case for cancelling a bundle
@injectable
class CancelBundleUseCase implements UseCase<Bundle, CancelBundleParams> {
  final BundlingRepository _repository;

  CancelBundleUseCase(this._repository);

  @override
  Future<Either<Failure, Bundle>> call(CancelBundleParams params) async {
    return await _repository.cancelBundle(
      params.bundleId,
      params.cancelledBy,
      params.reason,
    );
  }
}

/// Use case for getting bundles assigned to a user
@injectable
class GetBundlesAssignedToUserUseCase implements UseCase<List<Bundle>, String> {
  final BundlingRepository _repository;

  GetBundlesAssignedToUserUseCase(this._repository);

  @override
  Future<Either<Failure, List<Bundle>>> call(String params) async {
    return await _repository.getBundlesAssignedTo(params);
  }
}

/// Use case for getting bundles by status
@injectable
class GetBundlesByStatusUseCase implements UseCase<List<Bundle>, BundleStatus> {
  final BundlingRepository _repository;

  GetBundlesByStatusUseCase(this._repository);

  @override
  Future<Either<Failure, List<Bundle>>> call(BundleStatus params) async {
    return await _repository.getBundlesByStatus(params);
  }
}

/// Use case for searching bundles
@injectable
class SearchBundlesUseCase implements UseCase<List<Bundle>, String> {
  final BundlingRepository _repository;

  SearchBundlesUseCase(this._repository);

  @override
  Future<Either<Failure, List<Bundle>>> call(String params) async {
    return await _repository.searchBundles(params);
  }
}

/// Use case for getting bundle statistics
@injectable
class GetBundleStatisticsUseCase implements UseCase<BundleStatistics, GetBundleStatisticsParams> {
  final BundlingRepository _repository;

  GetBundleStatisticsUseCase(this._repository);

  @override
  Future<Either<Failure, BundleStatistics>> call(GetBundleStatisticsParams params) async {
    return await _repository.getBundleStatistics(
      fromDate: params.fromDate,
      toDate: params.toDate,
      designNo: params.designNo,
      size: params.size,
      status: params.status,
    );
  }
}

/// Use case for merging bundles
@injectable
class MergeBundlesUseCase implements UseCase<Bundle, MergeBundlesParams> {
  final BundlingRepository _repository;

  MergeBundlesUseCase(this._repository);

  @override
  Future<Either<Failure, Bundle>> call(MergeBundlesParams params) async {
    return await _repository.mergeBundles(
      params.bundleIds,
      params.mergedBy,
      params.remarks,
    );
  }
}

/// Use case for splitting a bundle
@injectable
class SplitBundleUseCase implements UseCase<List<Bundle>, SplitBundleParams> {
  final BundlingRepository _repository;

  SplitBundleUseCase(this._repository);

  @override
  Future<Either<Failure, List<Bundle>>> call(SplitBundleParams params) async {
    return await _repository.splitBundle(
      params.bundleId,
      params.splitPoints,
      params.splitBy,
      params.remarks,
    );
  }
}

/// Use case for getting next bundle number
@injectable
class GetNextBundleNumberUseCase implements UseCase<String, NoParams> {
  final BundlingRepository _repository;

  GetNextBundleNumberUseCase(this._repository);

  @override
  Future<Either<Failure, String>> call(NoParams params) async {
    return await _repository.getNextBundleNumber();
  }
}

// Parameter classes for use cases
class GetAllBundlesParams {
  final BundleStatus? status;
  final String? designNo;
  final String? lotNo;
  final String? rollNo;
  final String? altNo;
  final GarmentSize? size;
  final String? assignedTo;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? limit;
  final int? offset;

  const GetAllBundlesParams({
    this.status,
    this.designNo,
    this.lotNo,
    this.rollNo,
    this.altNo,
    this.size,
    this.assignedTo,
    this.startDate,
    this.endDate,
    this.limit,
    this.offset,
  });
}

class CreateBundlesParams {
  final BundleInput input;
  final String createdBy;

  const CreateBundlesParams({
    required this.input,
    required this.createdBy,
  });
}

class UpdateBundleParams {
  final Bundle bundle;

  const UpdateBundleParams({required this.bundle});
}

class DeleteBundleParams {
  final String bundleId;
  final String deletedBy;

  const DeleteBundleParams({
    required this.bundleId,
    required this.deletedBy,
  });
}

class IssueBundleParams {
  final String bundleId;
  final String assignedTo;
  final String issuedBy;

  const IssueBundleParams({
    required this.bundleId,
    required this.assignedTo,
    required this.issuedBy,
  });
}

class StartBundleParams {
  final String bundleId;
  final String startedBy;

  const StartBundleParams({
    required this.bundleId,
    required this.startedBy,
  });
}

class CompleteBundleParams {
  final String bundleId;
  final String completedBy;
  final String? remarks;

  const CompleteBundleParams({
    required this.bundleId,
    required this.completedBy,
    this.remarks,
  });
}

class CancelBundleParams {
  final String bundleId;
  final String cancelledBy;
  final String reason;

  const CancelBundleParams({
    required this.bundleId,
    required this.cancelledBy,
    required this.reason,
  });
}

class GetBundleStatisticsParams {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? designNo;
  final GarmentSize? size;
  final BundleStatus? status;

  const GetBundleStatisticsParams({
    this.fromDate,
    this.toDate,
    this.designNo,
    this.size,
    this.status,
  });
}

class MergeBundlesParams {
  final List<String> bundleIds;
  final String mergedBy;
  final String? remarks;

  const MergeBundlesParams({
    required this.bundleIds,
    required this.mergedBy,
    this.remarks,
  });
}

class SplitBundleParams {
  final String bundleId;
  final List<int> splitPoints;
  final String splitBy;
  final String? remarks;

  const SplitBundleParams({
    required this.bundleId,
    required this.splitPoints,
    required this.splitBy,
    this.remarks,
  });
}
