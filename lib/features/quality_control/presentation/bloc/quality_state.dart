part of 'quality_bloc.dart';

/// Base quality state
abstract class QualityState extends Equatable {
  const QualityState();

  @override
  List<Object?> get props => [];
}

/// Initial quality state
class QualityInitial extends QualityState {
  const QualityInitial();
}

/// Quality loading state
class QualityLoading extends QualityState {
  const QualityLoading();
}

/// Quality inspections loaded state
class QualityInspectionsLoaded extends QualityState {
  final List<QualityInspection> inspections;
  final Pagination? pagination;
  final QualityInspectionFilter? filter;
  final bool isRefreshing;
  final List<String> selectedInspectionIds;
  final QualityViewMode viewMode;
  final String? sortBy;
  final bool sortAscending;

  const QualityInspectionsLoaded({
    required this.inspections,
    this.pagination,
    this.filter,
    this.isRefreshing = false,
    this.selectedInspectionIds = const [],
    this.viewMode = QualityViewMode.list,
    this.sortBy,
    this.sortAscending = true,
  });

  /// Copy with new values
  QualityInspectionsLoaded copyWith({
    List<QualityInspection>? inspections,
    Pagination? pagination,
    QualityInspectionFilter? filter,
    bool? isRefreshing,
    List<String>? selectedInspectionIds,
    QualityViewMode? viewMode,
    String? sortBy,
    bool? sortAscending,
  }) {
    return QualityInspectionsLoaded(
      inspections: inspections ?? this.inspections,
      pagination: pagination ?? this.pagination,
      filter: filter ?? this.filter,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      selectedInspectionIds: selectedInspectionIds ?? this.selectedInspectionIds,
      viewMode: viewMode ?? this.viewMode,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  List<Object?> get props => [
        inspections,
        pagination,
        filter,
        isRefreshing,
        selectedInspectionIds,
        viewMode,
        sortBy,
        sortAscending,
      ];

  /// Check if inspection is selected
  bool isInspectionSelected(String inspectionId) {
    return selectedInspectionIds.contains(inspectionId);
  }

  /// Get selected inspections
  List<QualityInspection> get selectedInspections {
    return inspections.where((inspection) => selectedInspectionIds.contains(inspection.id)).toList();
  }

  /// Get pending inspections count
  int get pendingInspectionsCount {
    return inspections.where((inspection) => inspection.status == InspectionStatus.scheduled).length;
  }

  /// Get failed inspections count
  int get failedInspectionsCount {
    return inspections.where((inspection) => inspection.isFailed).length;
  }

  /// Get inspections with critical defects count
  int get criticalDefectsCount {
    return inspections.fold(0, (sum, inspection) => sum + inspection.criticalDefectsCount);
  }
}

/// Quality inspection details loaded state
class QualityInspectionDetailsLoaded extends QualityState {
  final QualityInspection inspection;

  const QualityInspectionDetailsLoaded(this.inspection);

  @override
  List<Object?> get props => [inspection];
}

/// Quality inspection created state
class QualityInspectionCreated extends QualityState {
  final QualityInspection inspection;

  const QualityInspectionCreated(this.inspection);

  @override
  List<Object?> get props => [inspection];
}

/// Quality inspection updated state
class QualityInspectionUpdated extends QualityState {
  final QualityInspection inspection;

  const QualityInspectionUpdated(this.inspection);

  @override
  List<Object?> get props => [inspection];
}

/// Quality inspection started state
class QualityInspectionStarted extends QualityState {
  final QualityInspection inspection;

  const QualityInspectionStarted(this.inspection);

  @override
  List<Object?> get props => [inspection];
}

/// Quality inspection completed state
class QualityInspectionCompleted extends QualityState {
  final QualityInspection inspection;

  const QualityInspectionCompleted(this.inspection);

  @override
  List<Object?> get props => [inspection];
}

/// Checkpoint result updated state
class CheckpointResultUpdated extends QualityState {
  final QualityCheckpoint checkpoint;

  const CheckpointResultUpdated(this.checkpoint);

  @override
  List<Object?> get props => [checkpoint];
}

/// Quality defect added state
class QualityDefectAdded extends QualityState {
  final QualityDefect defect;

  const QualityDefectAdded(this.defect);

  @override
  List<Object?> get props => [defect];
}

/// Quality defect updated state
class QualityDefectUpdated extends QualityState {
  final QualityDefect defect;

  const QualityDefectUpdated(this.defect);

  @override
  List<Object?> get props => [defect];
}

/// Quality defect resolved state
class QualityDefectResolved extends QualityState {
  final QualityDefect defect;

  const QualityDefectResolved(this.defect);

  @override
  List<Object?> get props => [defect];
}

/// Quality defects loaded state
class QualityDefectsLoaded extends QualityState {
  final List<QualityDefect> defects;
  final Pagination? pagination;
  final QualityDefectFilter? filter;

  const QualityDefectsLoaded({
    required this.defects,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [defects, pagination, filter];
}

/// Quality standards loaded state
class QualityStandardsLoaded extends QualityState {
  final List<QualityStandard> standards;
  final Pagination? pagination;
  final QualityStandardFilter? filter;

  const QualityStandardsLoaded({
    required this.standards,
    this.pagination,
    this.filter,
  });

  @override
  List<Object?> get props => [standards, pagination, filter];
}

/// Quality standard created state
class QualityStandardCreated extends QualityState {
  final QualityStandard standard;

  const QualityStandardCreated(this.standard);

  @override
  List<Object?> get props => [standard];
}

/// Quality standard approved state
class QualityStandardApproved extends QualityState {
  final QualityStandard standard;

  const QualityStandardApproved(this.standard);

  @override
  List<Object?> get props => [standard];
}

/// Quality statistics loaded state
class QualityStatisticsLoaded extends QualityState {
  final QualityStatistics statistics;

  const QualityStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Quality trends loaded state
class QualityTrendsLoaded extends QualityState {
  final QualityTrends trends;

  const QualityTrendsLoaded(this.trends);

  @override
  List<Object?> get props => [trends];
}

/// Defect analysis loaded state
class DefectAnalysisLoaded extends QualityState {
  final DefectAnalysis analysis;

  const DefectAnalysisLoaded(this.analysis);

  @override
  List<Object?> get props => [analysis];
}

/// Quality inspections searched state
class QualityInspectionsSearched extends QualityState {
  final List<QualityInspection> inspections;
  final Pagination? pagination;
  final String query;
  final QualityInspectionFilter? filter;

  const QualityInspectionsSearched({
    required this.inspections,
    this.pagination,
    required this.query,
    this.filter,
  });

  @override
  List<Object?> get props => [inspections, pagination, query, filter];
}

/// Pending inspections loaded state
class PendingInspectionsLoaded extends QualityState {
  final List<QualityInspection> inspections;
  final Pagination? pagination;

  const PendingInspectionsLoaded({
    required this.inspections,
    this.pagination,
  });

  @override
  List<Object?> get props => [inspections, pagination];
}

/// Failed inspections loaded state
class FailedInspectionsLoaded extends QualityState {
  final List<QualityInspection> inspections;
  final Pagination? pagination;

  const FailedInspectionsLoaded({
    required this.inspections,
    this.pagination,
  });

  @override
  List<Object?> get props => [inspections, pagination];
}

/// Quality inspections filtered state
class QualityInspectionsFiltered extends QualityState {
  final List<QualityInspection> inspections;
  final Pagination? pagination;
  final QualityInspectionFilter filter;

  const QualityInspectionsFiltered({
    required this.inspections,
    this.pagination,
    required this.filter,
  });

  @override
  List<Object?> get props => [inspections, pagination, filter];
}

/// Quality error state
class QualityError extends QualityState {
  final String message;

  const QualityError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Quality validation error state
class QualityValidationError extends QualityState {
  final Map<String, String> errors;

  const QualityValidationError(this.errors);

  @override
  List<Object?> get props => [errors];
}

/// Quality operation success state
class QualityOperationSuccess extends QualityState {
  final String message;

  const QualityOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Quality view mode changed state
class QualityViewModeChanged extends QualityState {
  final QualityViewMode viewMode;

  const QualityViewModeChanged(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Quality inspections sorted state
class QualityInspectionsSorted extends QualityState {
  final String sortBy;
  final bool ascending;

  const QualityInspectionsSorted(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Quality loading more state
class QualityLoadingMore extends QualityState {
  const QualityLoadingMore();
}

/// Quality refreshing state
class QualityRefreshing extends QualityState {
  const QualityRefreshing();
}
