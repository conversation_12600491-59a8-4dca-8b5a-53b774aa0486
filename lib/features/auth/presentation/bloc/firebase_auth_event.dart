part of 'firebase_auth_bloc.dart';

/// Base Firebase authentication event
abstract class FirebaseAuthEvent extends Equatable {
  const FirebaseAuthEvent();

  @override
  List<Object?> get props => [];
}

/// Firebase authentication started
class FirebaseAuthStarted extends FirebaseAuthEvent {
  const FirebaseAuthStarted();
}

/// Firebase sign in requested
class FirebaseSignInRequested extends FirebaseAuthEvent {
  final String email;
  final String password;

  const FirebaseSignInRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

/// Firebase Google sign in requested
class FirebaseAuthGoogleSignInRequested extends FirebaseAuthEvent {
  const FirebaseAuthGoogleSignInRequested();
}

/// Firebase sign up requested
class FirebaseSignUpRequested extends FirebaseAuthEvent {
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final UserRole role;

  const FirebaseSignUpRequested({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.role = UserRole.sewingOperator,
  });

  @override
  List<Object?> get props => [email, password, firstName, lastName, role];
}

/// Firebase sign out requested
class FirebaseSignOutRequested extends FirebaseAuthEvent {
  const FirebaseSignOutRequested();
}

/// Send email verification requested
class FirebaseSendEmailVerificationRequested extends FirebaseAuthEvent {
  const FirebaseSendEmailVerificationRequested();
}

/// Check email verification requested
class FirebaseCheckEmailVerifiedRequested extends FirebaseAuthEvent {
  const FirebaseCheckEmailVerifiedRequested();
}

/// Send password reset email requested
class SendPasswordResetEmailRequested extends FirebaseAuthEvent {
  final String email;

  const SendPasswordResetEmailRequested(this.email);

  @override
  List<Object?> get props => [email];
}

/// Update password requested
class UpdatePasswordRequested extends FirebaseAuthEvent {
  final String currentPassword;
  final String newPassword;

  const UpdatePasswordRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

/// Update user profile requested
class UpdateUserProfileRequested extends FirebaseAuthEvent {
  final UserProfile profile;

  const UpdateUserProfileRequested(this.profile);

  @override
  List<Object?> get props => [profile];
}


/// Refresh user data requested
class RefreshUserDataRequested extends FirebaseAuthEvent {
  const RefreshUserDataRequested();
}

/// Update last active requested
class UpdateLastActiveRequested extends FirebaseAuthEvent {
  const UpdateLastActiveRequested();
}

// Internal Events

/// Firebase auth state changed (internal)
class _FirebaseAuthStateChanged extends FirebaseAuthEvent {
  final AppUser? user;

  const _FirebaseAuthStateChanged(this.user);

  @override
  List<Object?> get props => [user];
}

/// Firebase user data changed (internal)
class _FirebaseUserDataChanged extends FirebaseAuthEvent {
  final AppUser? user;

  const _FirebaseUserDataChanged(this.user);

  @override
  List<Object?> get props => [user];
}
