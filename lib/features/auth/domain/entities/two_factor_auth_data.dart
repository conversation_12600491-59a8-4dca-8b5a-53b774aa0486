import 'package:equatable/equatable.dart';

/// Represents two-factor authentication data
class TwoFactorAuthData extends Equatable {
  final String code;
  final String method;
  final DateTime timestamp;

  const TwoFactorAuthData({
    required this.code,
    required this.method,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [code, method, timestamp];

  @override
  bool? get stringify => true;
}