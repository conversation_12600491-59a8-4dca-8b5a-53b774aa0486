import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/enums/purchase_status_enum.dart';
import '../../../../shared/enums/return_status_enum.dart';
import '../../../../shared/enums/unit_enum.dart';
import '../../domain/entities/denim_inventory_entities.dart';
import '../../domain/repositories/denim_inventory_repository.dart';
import '../../domain/requests/denim_inventory_requests.dart';
import '../models/denim_inventory_models.dart';

@LazySingleton(as: DenimInventoryRepository)
class FirebaseDenimInventoryRepository implements DenimInventoryRepository {
  final FirebaseFirestore _firestore;

  // Collection names
  static const String _denimTypesCollection = 'denim_types';
  static const String _denimRollsCollection = 'denim_rolls';
  static const String _purchaseRecordsCollection = 'denim_purchase_records';
  static const String _returnRecordsCollection = 'denim_return_records';
  static const String _countersCollection = 'counters';

  const FirebaseDenimInventoryRepository(this._firestore);

  // Denim Types Management
  @override
  Future<Either<Failure, List<DenimType>>> getDenimTypes({
    bool activeOnly = true,
  }) async {
    try {
      Query query = _firestore.collection(_denimTypesCollection);
      
      if (activeOnly) {
        query = query.where('isActive', isEqualTo: true);
      }
      
      query = query.orderBy('denimCode');

      final snapshot = await query.get();
      final denimTypes = snapshot.docs
          .map((doc) => DenimTypeModel.fromFirestore(doc))
          .toList();

      return Right(denimTypes);
    } catch (e) {
      return Left(ServerFailure('Failed to get denim types: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimType>> getDenimTypeById(String id) async {
    try {
      final doc = await _firestore
          .collection(_denimTypesCollection)
          .doc(id)
          .get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Denim type not found'));
      }

      final denimType = DenimTypeModel.fromFirestore(doc);
      return Right(denimType);
    } catch (e) {
      return Left(ServerFailure('Failed to get denim type: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimType>> createDenimType(
    CreateDenimTypeRequest request,
  ) async {
    try {
      // Check if denim code already exists
      final existingQuery = await _firestore
          .collection(_denimTypesCollection)
          .where('denimCode', isEqualTo: request.denimCode)
          .get();

      if (existingQuery.docs.isNotEmpty) {
        return const Left(ValidationFailure('Denim code already exists'));
      }

      final docRef = _firestore.collection(_denimTypesCollection).doc();
      final now = DateTime.now();

      final denimType = DenimTypeModel(
        id: docRef.id,
        denimCode: request.denimCode,
        type: request.type,
        description: request.description,
        gsm: request.gsm,
        width: request.width,
        supplierName: request.supplierName,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      );

      await docRef.set(denimType.toFirestore());
      return Right(denimType);
    } catch (e) {
      return Left(ServerFailure('Failed to create denim type: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimType>> updateDenimType(
    UpdateDenimTypeRequest request,
  ) async {
    try {
      final docRef = _firestore.collection(_denimTypesCollection).doc(request.id);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Denim type not found'));
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      // Check if new denim code already exists
      final existingQuery = await _firestore
          .collection(_denimTypesCollection)
          .where('denimCode', isEqualTo: request.denimCode)
          .get();

      if (existingQuery.docs.isNotEmpty &&
          existingQuery.docs.first.id != request.id) {
        return const Left(ValidationFailure('Denim code already exists'));
      }
      updateData['denimCode'] = request.denimCode;

      updateData['type'] = request.type;
      updateData['description'] = request.description;
      updateData['gsm'] = request.gsm;
      updateData['width'] = request.width;
      if (request.supplierName != null) updateData['supplierName'] = request.supplierName;

      await docRef.update(updateData);

      // Get updated document
      final updatedDoc = await docRef.get();
      final updatedDenimType = DenimTypeModel.fromFirestore(updatedDoc);

      return Right(updatedDenimType);
    } catch (e) {
      return Left(ServerFailure('Failed to update denim type: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteDenimType(String id) async {
    try {
      // Check if denim type is being used in any rolls
      final rollsQuery = await _firestore
          .collection(_denimRollsCollection)
          .where('denimTypeId', isEqualTo: id)
          .limit(1)
          .get();

      if (rollsQuery.docs.isNotEmpty) {
        return const Left(ValidationFailure(
          'Cannot delete denim type as it is being used in inventory rolls'
        ));
      }

      await _firestore.collection(_denimTypesCollection).doc(id).delete();
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to delete denim type: ${e.toString()}'));
    }
  }

  // Denim Rolls Management
  @override
  Future<Either<Failure, List<DenimRoll>>> getDenimRolls({
    DenimInventoryFilterRequest? filter,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_denimRollsCollection);

      // Apply filters
      if (filter != null) {
        if (filter.denimTypeId != null) {
          query = query.where('denimTypeId', isEqualTo: filter.denimTypeId);
        }
        if (filter.color != null) {
          query = query.where('color', isEqualTo: filter.color);
        }
        if (filter.status != null) {
          query = query.where('status', isEqualTo: filter.status!.value);
        }
        if (filter.location != null) {
          query = query.where('location', isEqualTo: filter.location);
        }
        if (filter.supplierId != null) {
          query = query.where('supplierId', isEqualTo: filter.supplierId);
        }
        if (filter.hasStock == true) {
          query = query.where('balanceQty', isGreaterThan: 0);
        }
      }

      // Apply pagination
      if (lastDocumentId != null) {
        final lastDoc = await _firestore
            .collection(_denimRollsCollection)
            .doc(lastDocumentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.orderBy('createdAt', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      final denimRolls = snapshot.docs
          .map((doc) => DenimRollModel.fromFirestore(doc))
          .toList();

      return Right(denimRolls);
    } catch (e) {
      return Left(ServerFailure('Failed to get denim rolls: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimRoll>> getDenimRollById(String id) async {
    try {
      final doc = await _firestore
          .collection(_denimRollsCollection)
          .doc(id)
          .get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Denim roll not found'));
      }

      final denimRoll = DenimRollModel.fromFirestore(doc);
      return Right(denimRoll);
    } catch (e) {
      return Left(ServerFailure('Failed to get denim roll: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimRoll?>> getDenimRollByRollId(String rollId) async {
    try {
      final query = await _firestore
          .collection(_denimRollsCollection)
          .where('rollId', isEqualTo: rollId)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return const Right(null);
      }

      final denimRoll = DenimRollModel.fromFirestore(query.docs.first);
      return Right(denimRoll);
    } catch (e) {
      return Left(ServerFailure('Failed to get denim roll by roll ID: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DenimRoll>>> searchDenimRolls(
    SearchDenimInventoryRequest request,
  ) async {
    try {
      Query query = _firestore.collection(_denimRollsCollection);

      // For now, we'll do a simple search on rollId and denimType
      // In a production app, you might want to use Algolia or similar for better search
      query = query.where('rollId', isGreaterThanOrEqualTo: request.query)
                   .where('rollId', isLessThanOrEqualTo: '${request.query}\uf8ff');

      // Apply additional filters if provided
      if (request.filter != null) {
        final filter = request.filter!;
        if (filter.denimTypeId != null) {
          query = query.where('denimTypeId', isEqualTo: filter.denimTypeId);
        }
        if (filter.status != null) {
          query = query.where('status', isEqualTo: filter.status!.value);
        }
      }

      if (request.limit != null) {
        query = query.limit(request.limit!);
      }

      final snapshot = await query.get();
      final denimRolls = snapshot.docs
          .map((doc) => DenimRollModel.fromFirestore(doc))
          .toList();

      return Right(denimRolls);
    } catch (e) {
      return Left(ServerFailure('Failed to search denim rolls: ${e.toString()}'));
    }
  }

  // Purchase Management
  @override
  Future<Either<Failure, DenimPurchaseRecord>> createPurchaseRecord(
    CreatePurchaseRequest request,
  ) async {
    try {
      // Start a batch write for atomic operations
      final batch = _firestore.batch();

      // Generate serial number
      final srNoResult = await getNextSerialNumber('purchase');
      if (srNoResult.isLeft()) {
        return Left(srNoResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }
      final srNo = srNoResult.fold((l) => 0, (r) => r);

      // Check if roll ID already exists
      final existingRollResult = await getDenimRollByRollId(request.rollId);
      if (existingRollResult.isLeft()) {
        return Left(existingRollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }

      final existingRoll = existingRollResult.fold((l) => null, (r) => r);
      if (existingRoll != null) {
        return const Left(ValidationFailure('Roll ID already exists'));
      }

      // Get denim type details
      final denimTypeResult = await getDenimTypeById(request.denimTypeId);
      if (denimTypeResult.isLeft()) {
        return Left(denimTypeResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }

      final denimType = denimTypeResult.fold((l) => null, (r) => r);
      if (denimType == null) {
        return const Left(ValidationFailure('Invalid denim type'));
      }

      final now = DateTime.now();

      // Create purchase record
      final purchaseDocRef = _firestore.collection(_purchaseRecordsCollection).doc();
      final purchaseRecord = DenimPurchaseRecordModel(
        id: purchaseDocRef.id,
        srNo: srNo,
        purchaseDate: request.purchaseDate,
        supplierName: request.supplierName,
        supplierId: request.supplierId,
        rollId: request.rollId,
        denimTypeId: request.denimTypeId,
        denimType: denimType.type,
        color: request.color,
        width: request.width,
        length: request.length,
        unit: request.unit,
        gsm: request.gsm,
        ratePerUnit: request.ratePerUnit,
        totalCost: request.totalCost,
        invoiceNo: request.invoiceNo,
        remarks: request.remarks,
        status: PurchaseStatus.completed,
        createdAt: now,
        updatedAt: now,
        driverName: request.driverName,
        vehicleNumber: request.vehicleNumber,
      );

      // Create denim roll
      final rollDocRef = _firestore.collection(_denimRollsCollection).doc();
      final denimRoll = DenimRollModel(
        id: rollDocRef.id,
        rollId: request.rollId,
        denimTypeId: request.denimTypeId,
        denimCode: denimType.denimCode,
        denimType: denimType.type,
        color: request.color,
        width: request.width,
        totalPurchasedQty: request.length,
        returnedQty: 0.0,
        usedQty: 0.0,
        balanceQty: request.length,
        location: 'Warehouse', // Default location
        status: DenimRollStatus.inStock,
        unit: UnitUtils.fromString(request.unit),
        gsm: request.gsm,
        supplierId: request.supplierId,
        supplierName: request.supplierName,
        metadata: {
          'purchaseRecordId': purchaseDocRef.id,
          'invoiceNo': request.invoiceNo,
        },
        createdAt: now,
        updatedAt: now,
      );

      // Add to batch
      batch.set(purchaseDocRef, purchaseRecord.toFirestore());
      batch.set(rollDocRef, denimRoll.toFirestore());

      // Commit batch
      await batch.commit();

      return Right(purchaseRecord);
    } catch (e) {
      return Left(ServerFailure('Failed to create purchase record: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DenimPurchaseRecord>>> getPurchaseRecords({
    String? supplierId,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_purchaseRecordsCollection);

      // Apply filters
      if (supplierId != null) {
        query = query.where('supplierId', isEqualTo: supplierId);
      }
      if (fromDate != null) {
        query = query.where('purchaseDate', isGreaterThanOrEqualTo: Timestamp.fromDate(fromDate));
      }
      if (toDate != null) {
        query = query.where('purchaseDate', isLessThanOrEqualTo: Timestamp.fromDate(toDate));
      }

      // Apply pagination
      if (lastDocumentId != null) {
        final lastDoc = await _firestore
            .collection(_purchaseRecordsCollection)
            .doc(lastDocumentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.orderBy('purchaseDate', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      final purchaseRecords = snapshot.docs
          .map((doc) => DenimPurchaseRecordModel.fromFirestore(doc))
          .toList();

      return Right(purchaseRecords);
    } catch (e) {
      return Left(ServerFailure('Failed to get purchase records: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimPurchaseRecord>> getPurchaseRecordById(String id) async {
    try {
      final doc = await _firestore
          .collection(_purchaseRecordsCollection)
          .doc(id)
          .get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Purchase record not found'));
      }

      final purchaseRecord = DenimPurchaseRecordModel.fromFirestore(doc);
      return Right(purchaseRecord);
    } catch (e) {
      return Left(ServerFailure('Failed to get purchase record: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DenimPurchaseRecord>>> getPurchaseRecordsByRollId(
    String rollId,
  ) async {
    try {
      final query = await _firestore
          .collection(_purchaseRecordsCollection)
          .where('rollId', isEqualTo: rollId)
          .orderBy('purchaseDate', descending: true)
          .get();

      final purchaseRecords = query.docs
          .map((doc) => DenimPurchaseRecordModel.fromFirestore(doc))
          .toList();

      return Right(purchaseRecords);
    } catch (e) {
      return Left(ServerFailure('Failed to get purchase records by roll ID: ${e.toString()}'));
    }
  }

  // Utility methods
  @override
  Future<Either<Failure, String>> generateRollId(String denimTypeId) async {
    try {
      final denimTypeResult = await getDenimTypeById(denimTypeId);
      if (denimTypeResult.isLeft()) {
        return Left(denimTypeResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }

      final denimType = denimTypeResult.fold((l) => null, (r) => r);
      if (denimType == null) {
        return const Left(ValidationFailure('Invalid denim type'));
      }

      // Generate roll ID format: DENIM_CODE-YYYYMMDD-XXX
      final now = DateTime.now();
      final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';

      // Get next sequence number for today
      final counterDoc = await _firestore
          .collection(_countersCollection)
          .doc('roll_id_${denimType.denimCode}_$dateStr')
          .get();

      int nextSeq = 1;
      if (counterDoc.exists) {
        nextSeq = (counterDoc.data()?['count'] ?? 0) + 1;
      }

      final rollId = '${denimType.denimCode}-$dateStr-${nextSeq.toString().padLeft(3, '0')}';

      // Update counter
      await _firestore
          .collection(_countersCollection)
          .doc('roll_id_${denimType.denimCode}_$dateStr')
          .set({'count': nextSeq}, SetOptions(merge: true));

      return Right(rollId);
    } catch (e) {
      return Left(ServerFailure('Failed to generate roll ID: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, int>> getNextSerialNumber(String type) async {
    try {
      final counterDoc = await _firestore
          .collection(_countersCollection)
          .doc('${type}_serial')
          .get();

      int nextSerial = 1;
      if (counterDoc.exists) {
        nextSerial = (counterDoc.data()?['count'] ?? 0) + 1;
      }

      // Update counter
      await _firestore
          .collection(_countersCollection)
          .doc('${type}_serial')
          .set({'count': nextSerial}, SetOptions(merge: true));

      return Right(nextSerial);
    } catch (e) {
      return Left(ServerFailure('Failed to get next serial number: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, bool>> isRollIdUnique(String rollId) async {
    try {
      final query = await _firestore
          .collection(_denimRollsCollection)
          .where('rollId', isEqualTo: rollId)
          .limit(1)
          .get();

      return Right(query.docs.isEmpty);
    } catch (e) {
      return Left(ServerFailure('Failed to check roll ID uniqueness: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableLocations() async {
    try {
      final query = await _firestore
          .collection(_denimRollsCollection)
          .get();

      final locations = <String>{};
      for (final doc in query.docs) {
        final location = doc.data()['location'] as String?;
        if (location != null && location.isNotEmpty) {
          locations.add(location);
        }
      }

      return Right(locations.toList()..sort());
    } catch (e) {
      return Left(ServerFailure('Failed to get available locations: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableColors() async {
    try {
      final query = await _firestore
          .collection(_denimRollsCollection)
          .get();

      final colors = <String>{};
      for (final doc in query.docs) {
        final color = doc.data()['color'] as String?;
        if (color != null && color.isNotEmpty) {
          colors.add(color);
        }
      }

      return Right(colors.toList()..sort());
    } catch (e) {
      return Left(ServerFailure('Failed to get available colors: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> validatePurchaseData(
    CreatePurchaseRequest request,
  ) async {
    try {
      final errors = <String, String>{};

      // Check if roll ID already exists
      final rollIdCheck = await isRollIdUnique(request.rollId);
      if (rollIdCheck.isLeft()) {
        return Left(rollIdCheck.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }

      final isUnique = rollIdCheck.fold((l) => false, (r) => r);
      if (!isUnique) {
        errors['rollId'] = 'Roll ID already exists';
      }

      // Validate denim type
      final denimTypeResult = await getDenimTypeById(request.denimTypeId);
      if (denimTypeResult.isLeft()) {
        errors['denimTypeId'] = 'Invalid denim type';
      }

      // Validate numeric values
      if (request.width <= 0) {
        errors['width'] = 'Width must be greater than 0';
      }
      if (request.length <= 0) {
        errors['length'] = 'Length must be greater than 0';
      }
      if (request.gsm <= 0) {
        errors['gsm'] = 'GSM must be greater than 0';
      }
      if (request.ratePerUnit <= 0) {
        errors['ratePerUnit'] = 'Rate per unit must be greater than 0';
      }

      return Right({
        'isValid': errors.isEmpty,
        'errors': errors,
      });
    } catch (e) {
      return Left(ServerFailure('Failed to validate purchase data: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> validateReturnData(
    CreateReturnRequest request,
  ) async {
    try {
      final errors = <String, String>{};

      // Check if roll exists
      final rollResult = await getDenimRollByRollId(request.rollId);
      if (rollResult.isLeft()) {
        errors['rollId'] = 'Roll ID not found';
      } else {
        final roll = rollResult.fold((l) => null, (r) => r);
        if (roll != null) {
          // Validate return quantity
          if (request.quantityReturned <= 0) {
            errors['quantityReturned'] = 'Return quantity must be greater than 0';
          } else if (request.quantityReturned > roll.balanceQty) {
            errors['quantityReturned'] = 'Return quantity cannot exceed available balance (${roll.balanceQty})';
          }
        }
      }

      return Right({
        'isValid': errors.isEmpty,
        'errors': errors,
      });
    } catch (e) {
      return Left(ServerFailure('Failed to validate return data: ${e.toString()}'));
    }
  }

  // Note: The remaining methods (analytics, streams, etc.) would be implemented here
  // For brevity, I'm showing the core CRUD operations and utility methods

  // Placeholder implementations for remaining abstract methods
  @override
  Future<Either<Failure, Map<String, dynamic>>> getStockSummary({
    String? denimTypeId,
    String? location,
  }) async {
    return const Left(UnimplementedFailure('Stock summary not implemented yet'));
  }

  @override
  Future<Either<Failure, List<DenimRoll>>> getLowStockRolls({
    double threshold = 10.0,
  }) async {
    return const Left(UnimplementedFailure('Low stock rolls not implemented yet'));
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getPurchaseAnalytics({
    DateTime? fromDate,
    DateTime? toDate,
    String? supplierId,
  }) async {
    return const Left(UnimplementedFailure('Purchase analytics not implemented yet'));
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getReturnAnalytics({
    DateTime? fromDate,
    DateTime? toDate,
    String? supplierId,
  }) async {
    return const Left(UnimplementedFailure('Return analytics not implemented yet'));
  }

  @override
  Stream<List<DenimRoll>> watchDenimRolls({
    DenimInventoryFilterRequest? filter,
  }) {
    return const Stream.empty();
  }

  @override
  Stream<List<DenimType>> watchDenimTypes() {
    return const Stream.empty();
  }

  @override
  Stream<DenimRoll?> watchDenimRollByRollId(String rollId) {
    return const Stream.empty();
  }

  @override
  Stream<List<DenimPurchaseRecord>> watchPurchaseRecords({
    String? supplierId,
    DateTime? fromDate,
  }) {
    return const Stream.empty();
  }

  @override
  Stream<List<DenimReturnRecord>> watchReturnRecords({
    String? supplierId,
    ReturnStatus? status,
  }) {
    return const Stream.empty();
  }

  @override
  Future<Either<Failure, DenimReturnRecord>> createReturnRecord(
    CreateReturnRequest request,
  ) async {
    try {
      // Start a batch write for atomic operations
      final batch = _firestore.batch();
      
      // Generate serial number
      final srNoResult = await getNextSerialNumber('return');
      if (srNoResult.isLeft()) {
        return Left(srNoResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }
      final srNo = srNoResult.fold((l) => 0, (r) => r);

      // Get existing denim roll
      final rollResult = await getDenimRollByRollId(request.rollId);
      if (rollResult.isLeft()) {
        return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }
      
      final existingRoll = rollResult.fold((l) => null, (r) => r);
      if (existingRoll == null) {
        return const Left(ValidationFailure('Roll ID not found'));
      }

      // Validate return quantity
      if (request.quantityReturned > existingRoll.balanceQty) {
        return const Left(ValidationFailure('Return quantity cannot exceed available balance'));
      }

      final now = DateTime.now();

      // Create return record
      final returnDocRef = _firestore.collection(_returnRecordsCollection).doc();
      final returnRecord = DenimReturnRecordModel(
        id: returnDocRef.id,
        srNo: srNo,
        returnDate: request.returnDate,
        rollId: request.rollId,
        supplierName: request.supplierName,
        supplierId: request.supplierId,
        reasonForReturn: request.reasonForReturn,
        quantityReturned: request.quantityReturned,
        unit: request.unit,
        debitNoteNo: request.debitNoteNo,
        remarks: request.remarks,
        status: ReturnStatus.pending,
        createdAt: now,
        updatedAt: now,
      );

      // Update denim roll with returned quantity
      final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
      final updatedRoll = (existingRoll as DenimRollModel).updateStock(
        returnedQty: existingRoll.returnedQty + request.quantityReturned,
        status: existingRoll.balanceQty - request.quantityReturned <= 0 
            ? DenimRollStatus.returned 
            : existingRoll.status,
      );

      // Add to batch
      batch.set(returnDocRef, returnRecord.toFirestore());
      batch.update(rollDocRef, updatedRoll.toFirestore());

      // Commit batch
      await batch.commit();

      return Right(returnRecord);
    } catch (e) {
      return Left(ServerFailure('Failed to create return record: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DenimReturnRecord>>> getReturnRecords({
    String? supplierId,
    DateTime? fromDate,
    DateTime? toDate,
    ReturnStatus? status,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_returnRecordsCollection);

      // Apply filters
      if (supplierId != null) {
        query = query.where('supplierId', isEqualTo: supplierId);
      }
      if (fromDate != null) {
        query = query.where('returnDate', isGreaterThanOrEqualTo: Timestamp.fromDate(fromDate));
      }
      if (toDate != null) {
        query = query.where('returnDate', isLessThanOrEqualTo: Timestamp.fromDate(toDate));
      }
      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }

      // Apply pagination
      if (lastDocumentId != null) {
        final lastDoc = await _firestore
            .collection(_returnRecordsCollection)
            .doc(lastDocumentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.orderBy('returnDate', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      final returnRecords = snapshot.docs
          .map((doc) => DenimReturnRecordModel.fromFirestore(doc))
          .toList();

      return Right(returnRecords);
    } catch (e) {
      return Left(ServerFailure('Failed to get return records: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimReturnRecord>> getReturnRecordById(String id) async {
    try {
      final doc = await _firestore
          .collection(_returnRecordsCollection)
          .doc(id)
          .get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Return record not found'));
      }

      final returnRecord = DenimReturnRecordModel.fromFirestore(doc);
      return Right(returnRecord);
    } catch (e) {
      return Left(ServerFailure('Failed to get return record: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DenimReturnRecord>>> getReturnRecordsByRollId(
    String rollId,
  ) async {
    try {
      final query = await _firestore
          .collection(_returnRecordsCollection)
          .where('rollId', isEqualTo: rollId)
          .orderBy('returnDate', descending: true)
          .get();

      final returnRecords = query.docs
          .map((doc) => DenimReturnRecordModel.fromFirestore(doc))
          .toList();

      return Right(returnRecords);
    } catch (e) {
      return Left(ServerFailure('Failed to get return records by roll ID: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimReturnRecord>> updateReturnStatus(
    String id,
    ReturnStatus status,
    String? remarks,
  ) async {
    try {
      final docRef = _firestore.collection(_returnRecordsCollection).doc(id);
      final updateData = {
        'status': status.value,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      if (remarks != null) {
        updateData['remarks'] = remarks;
      }

      await docRef.update(updateData);

      // Get updated document
      final updatedDoc = await docRef.get();
      final updatedReturnRecord = DenimReturnRecordModel.fromFirestore(updatedDoc);

      return Right(updatedReturnRecord);
    } catch (e) {
      return Left(ServerFailure('Failed to update return status: ${e.toString()}'));
    }
  }

  // Stock Management
  @override
  Future<Either<Failure, DenimRoll>> updateStock(
    UpdateStockRequest request,
  ) async {
    try {
      // Get existing denim roll
      final rollResult = await getDenimRollByRollId(request.rollId);
      if (rollResult.isLeft()) {
        return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }
      
      final existingRoll = rollResult.fold((l) => null, (r) => r);
      if (existingRoll == null) {
        return const Left(ValidationFailure('Roll ID not found'));
      }

      // Update stock quantities
      final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
      final updatedRoll = (existingRoll as DenimRollModel).updateStock(
        usedQty: request.usedQty,
        returnedQty: request.returnedQty,
        status: request.status,
        location: request.location,
      );

      await rollDocRef.update(updatedRoll.toFirestore());

      return Right(updatedRoll);
    } catch (e) {
      return Left(ServerFailure('Failed to update stock: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DenimRoll>>> bulkUpdateStock(
    BulkStockUpdateRequest request,
  ) async {
    try {
      final batch = _firestore.batch();
      final updatedRolls = <DenimRoll>[];

      for (final updateRequest in request.updates) {
        // Get existing denim roll
        final rollResult = await getDenimRollByRollId(updateRequest.rollId);
        if (rollResult.isLeft()) {
          return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
        }
        
        final existingRoll = rollResult.fold((l) => null, (r) => r);
        if (existingRoll == null) {
          return Left(ValidationFailure('Roll ID ${updateRequest.rollId} not found'));
        }

        // Update stock quantities
        final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
        final updatedRoll = (existingRoll as DenimRollModel).updateStock(
          usedQty: updateRequest.usedQty,
          returnedQty: updateRequest.returnedQty,
          status: updateRequest.status,
          location: updateRequest.location,
        );

        batch.update(rollDocRef, updatedRoll.toFirestore());
        updatedRolls.add(updatedRoll);
      }

      await batch.commit();
      return Right(updatedRolls);
    } catch (e) {
      return Left(ServerFailure('Failed to bulk update stock: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DenimRoll>> transferStock(
    StockTransferRequest request,
  ) async {
    try {
      // Get existing denim roll
      final rollResult = await getDenimRollByRollId(request.rollId);
      if (rollResult.isLeft()) {
        return Left(rollResult.fold((l) => l, (r) => const ServerFailure('Unknown error')));
      }
      
      final existingRoll = rollResult.fold((l) => null, (r) => r);
      if (existingRoll == null) {
        return const Left(ValidationFailure('Roll ID not found'));
      }

      // Validate transfer quantity
      if (request.quantity > existingRoll.balanceQty) {
        return const Left(ValidationFailure('Transfer quantity cannot exceed available balance'));
      }

      // Update location
      final rollDocRef = _firestore.collection(_denimRollsCollection).doc(existingRoll.id);
      final updatedRoll = (existingRoll as DenimRollModel).updateStock(
        location: request.toLocation,
      );

      await rollDocRef.update(updatedRoll.toFirestore());

      return Right(updatedRoll);
    } catch (e) {
      return Left(ServerFailure('Failed to transfer stock: ${e.toString()}'));
    }
  }
}
