import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../auth/domain/entities/user.dart';
import '../../../../features/auth/presentation/bloc/firebase_auth_bloc.dart';
import '../../../../shared/widgets/loading_screen.dart';
import '../../../../shared/enums/common_enums.dart';
import 'dashboard_page.dart';
import 'administrator_dashboard_page.dart';
import 'merchandiser_dashboard_page.dart';
import 'inventory_manager_dashboard_page.dart';
import 'cutting_head_dashboard_page.dart';
import 'cutting_master_dashboard_page.dart';
import 'cutting_helper_dashboard_page.dart';
import 'sewing_head_dashboard_page.dart';
import 'sewing_supervisor_dashboard_page.dart';
import 'sewing_operator_dashboard_page.dart';
import 'quality_controller_dashboard_page.dart';
import 'finishing_head_dashboard_page.dart';
import 'finishing_operator_dashboard_page.dart';
import 'warehouse_manager_dashboard_page.dart';
import 'viewer_dashboard_page.dart';

/// Role-based dashboard that shows different dashboards based on user role
class RoleBasedDashboard extends StatelessWidget {
  const RoleBasedDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          return _buildDashboardForRole(state.user.role);
        } else if (state is FirebaseAuthLoading) {
          return const LoadingScreen();
        } else {
          // Fallback to default dashboard
          return const AdministratorDashboardPage();
        }
      },
    );
  }

  Widget _buildDashboardForRole(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return const AdministratorDashboardPage();
      case UserRole.merchandiser:
        return const MerchandiserDashboardPage();
      case UserRole.inventoryManager:
        return const InventoryManagerDashboardPage();
      case UserRole.cuttingHead:
        return const CuttingHeadDashboardPage();
      case UserRole.cuttingMaster:
        return const CuttingMasterDashboardPage();
      case UserRole.cuttingHelper:
        return const CuttingHelperDashboardPage();
      case UserRole.sewingHead:
        return const SewingHeadDashboardPage();
      case UserRole.sewingSupervisor:
        return const SewingSupervisorDashboardPage();
      case UserRole.sewingOperator:
        return const SewingOperatorDashboardPage();
      case UserRole.qualityController:
        return const QualityControllerDashboardPage();
      case UserRole.finishingHead:
        return const FinishingHeadDashboardPage();
      case UserRole.finishingOperator:
        return const FinishingOperatorDashboardPage();
      case UserRole.warehouseManager:
        return const WarehouseManagerDashboardPage();
      case UserRole.viewer:
        return const ViewerDashboardPage();
      default:
        return const AdministratorDashboardPage();
    }
  }
}



/// Dashboard navigation wrapper with role-based navigation
class DashboardNavigationWrapper extends StatefulWidget {
  final Widget child;

  const DashboardNavigationWrapper({
    super.key,
    required this.child,
  });

  @override
  State<DashboardNavigationWrapper> createState() =>
      _DashboardNavigationWrapperState();
}

class _DashboardNavigationWrapperState
    extends State<DashboardNavigationWrapper> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          return Scaffold(
            body: widget.child,
            bottomNavigationBar: _buildBottomNavigationBar(state.user),
            drawer: _buildNavigationDrawer(state.user),
          );
        }
        return widget.child;
      },
    );
  }

  Widget? _buildBottomNavigationBar(User user) {
    final items = _getNavigationItemsForRole(user.role);

    if (items.length <= 1) return null;

    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
        // Handle navigation
      },
      type: BottomNavigationBarType.fixed,
      items: items
          .take(5)
          .map((item) => BottomNavigationBarItem(
                icon: Icon(item.icon),
                label: item.label,
              ))
          .toList(),
    );
  }

  Widget _buildNavigationDrawer(User user) {
    final items = _getNavigationItemsForRole(user.role);

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            accountName: Text(user.fullName),
            accountEmail: Text(user.email),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                user.initials,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue, Colors.purple],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          ...items.map((item) => ListTile(
                leading: Icon(item.icon),
                title: Text(item.label),
                onTap: () {
                  Navigator.pop(context);
                  // Handle navigation
                },
              )),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              // Handle settings navigation
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Logout'),
            onTap: () {
              Navigator.pop(context);
              context.read<FirebaseAuthBloc>().add(
                    const FirebaseSignOutRequested(),
                  );
            },
          ),
        ],
      ),
    );
  }

  List<NavigationItem> _getNavigationItemsForRole(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.people, 'Users'),
          NavigationItem(Icons.settings, 'System'),
          NavigationItem(Icons.analytics, 'Analytics'),
          NavigationItem(Icons.security, 'Security'),
        ];
      case UserRole.merchandiser:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.assignment, 'Orders'),
          NavigationItem(Icons.people, 'Customers'),
          NavigationItem(Icons.analytics, 'Reports'),
        ];
      case UserRole.inventoryManager:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.inventory, 'Inventory'),
          NavigationItem(Icons.local_shipping, 'Materials'),
          NavigationItem(Icons.analytics, 'Reports'),
        ];
      case UserRole.cuttingHead:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.content_cut, 'Cutting'),
          NavigationItem(Icons.people, 'Team'),
          NavigationItem(Icons.analytics, 'Reports'),
        ];
      case UserRole.cuttingMaster:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.content_cut, 'Cutting'),
          NavigationItem(Icons.design_services, 'Patterns'),
          NavigationItem(Icons.schedule, 'Schedule'),
        ];
      case UserRole.cuttingHelper:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.work, 'My Tasks'),
          NavigationItem(Icons.help, 'Help'),
        ];
      case UserRole.sewingHead:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.design_services, 'Sewing'),
          NavigationItem(Icons.people, 'Team'),
          NavigationItem(Icons.analytics, 'Reports'),
        ];
      case UserRole.sewingSupervisor:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.assignment, 'Tasks'),
          NavigationItem(Icons.people, 'Workers'),
          NavigationItem(Icons.timeline, 'Schedule'),
        ];
      case UserRole.sewingOperator:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.work, 'My Tasks'),
          NavigationItem(Icons.schedule, 'Schedule'),
          NavigationItem(Icons.help, 'Help'),
        ];
      case UserRole.qualityController:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.verified, 'Inspections'),
          NavigationItem(Icons.report, 'Reports'),
          NavigationItem(Icons.history, 'History'),
        ];
      case UserRole.finishingHead:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.check_circle, 'Finishing'),
          NavigationItem(Icons.people, 'Team'),
          NavigationItem(Icons.analytics, 'Reports'),
        ];
      case UserRole.finishingOperator:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.work, 'My Tasks'),
          NavigationItem(Icons.help, 'Help'),
        ];
      case UserRole.warehouseManager:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.warehouse, 'Warehouse'),
          NavigationItem(Icons.local_shipping, 'Shipping'),
          NavigationItem(Icons.analytics, 'Reports'),
        ];
      case UserRole.viewer:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
          NavigationItem(Icons.visibility, 'View Data'),
          NavigationItem(Icons.analytics, 'Reports'),
        ];
      default:
        return [
          NavigationItem(Icons.dashboard, 'Dashboard'),
        ];
    }
  }
}

/// Navigation item model
class NavigationItem {
  final IconData icon;
  final String label;

  NavigationItem(this.icon, this.label);
}
