import 'package:dartz/dartz.dart';
import 'package:hm_collection/features/department/data/datasources/department_remote_data_source.dart';
import 'package:hm_collection/features/department/domain/entities/department.dart';
import 'package:hm_collection/features/department/domain/repositories/department_repository.dart';

import '../../../../core/errors/failures.dart';

class DepartmentRepositoryImpl implements DepartmentRepository {
  final DepartmentRemoteDataSource remoteDataSource;

  DepartmentRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<Department>>> getDepartments() async {
    try {
      final departmentModels = await remoteDataSource.getDepartments();
      return Right(departmentModels);
    } catch (e) {
      return const Left(ServerFailure('Failed to fetch departments'));
    }
  }
}
