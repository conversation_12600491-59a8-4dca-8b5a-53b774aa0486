import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AllMarkersPage extends StatelessWidget {
  const AllMarkersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Markers'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance.collection('markers').snapshots(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return const Center(child: Text('Something went wrong'));
          }

          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          return ListView.builder(
            itemCount: snapshot.data!.docs.length,
            itemBuilder: (context, index) {
              final doc = snapshot.data!.docs[index];
              return _buildMarkerListItem(context, doc);
            },
          );
        },
      ),
    );
  }

  Widget _buildMarkerListItem(BuildContext context, QueryDocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final designNumber = data['designNumber'] ?? 'No Design Number';
    final status = data['status'] ?? 'N/A';
    final createdAt = (data['createdAt'] as Timestamp).toDate();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(designNumber),
        subtitle: Text('Status: $status\nCreated: ${createdAt.toLocal()}'),
        trailing: TextButton(
          onPressed: () => _showMarkerViewDialog(context, doc),
          child: const Text('View'),
        ),
      ),
    );
  }

  void _showMarkerViewDialog(BuildContext context, QueryDocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final designNumber = data['designNumber'] ?? 'No Design Number';
    final notes = data['notes'] ?? '';
    final markerItems = List<Map<String, dynamic>>.from(data['markerItems'] ?? []);
    final createdByName = data['createdByName'] ?? 'N/A';
    final createdAt = (data['createdAt'] as Timestamp).toDate();
    final totalPcs = markerItems.fold(0, (sum, item) => sum + (int.tryParse(item['pcs'] ?? '0') ?? 0));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Marker Details: $designNumber'),
        content: SizedBox(
          width: 800,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCompanyHeader(),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Date: ${createdAt.toLocal()}'),
                    Text('Created By: $createdByName'),
                  ],
                ),
                const SizedBox(height: 20),
                DataTable(
                  columns: const [
                    DataColumn(label: Text('Sort')),
                    DataColumn(label: Text('Roll')),
                    DataColumn(label: Text('MTR')),
                    DataColumn(label: Text('Panna')),
                    DataColumn(label: Text('Palla')),
                    DataColumn(label: Text('Pcs')),
                    DataColumn(label: Text('Length')),
                  ],
                  rows: markerItems.map((item) {
                    return DataRow(
                      cells: [
                        DataCell(Text(item['sort'] ?? '')),
                        DataCell(Text(item['roll'] ?? '')),
                        DataCell(Text(item['mtr'] ?? '')),
                        DataCell(Text(item['panna'] ?? '')),
                        DataCell(Text(item['palla'] ?? '')),
                        DataCell(Text(item['pcs'] ?? '')),
                        DataCell(Text(item['length'] ?? '')),
                      ],
                    );
                  }).toList(),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text('Total Pcs: $totalPcs', style: const TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
                if (notes.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Text('Notes: $notes'),
                  ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyHeader() {
    return Row(
      children: [
        // Replace with your company logo
        const Icon(Icons.business, size: 50),
        const SizedBox(width: 20),
        const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('HM Collection', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            Text('Your Company Address'),
            Text('Your Company Contact Info'),
          ],
        ),
      ],
    );
  }
}
