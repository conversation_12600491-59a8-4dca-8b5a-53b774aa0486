import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/entities/numbering_task.dart';

/// Widget to display numbering tasks in a table format
class NumberingTaskTable extends StatefulWidget {
  final List<NumberingTask> tasks;
  final Set<String> selectedTaskIds;
  final Function(String) onTaskSelected;
  final VoidCallback onSelectAll;
  final VoidCallback onDeselectAll;
  final Function(String, NumberingTask) onTaskAction;
  final Function(NumberingTask) onEditTask;
  final Function(NumberingTask) onDeleteTask;
  final bool showMyTasksActions;

  const NumberingTaskTable({
    super.key,
    required this.tasks,
    required this.selectedTaskIds,
    required this.onTaskSelected,
    required this.onSelectAll,
    required this.onDeselectAll,
    required this.onTaskAction,
    required this.onEditTask,
    required this.onDeleteTask,
    this.showMyTasksActions = false,
  });

  @override
  State<NumberingTaskTable> createState() => _NumberingTaskTableState();
}

class _NumberingTaskTableState extends State<NumberingTaskTable> {
  final ScrollController _horizontalScrollController = ScrollController();
  final ScrollController _verticalScrollController = ScrollController();

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Table header with selection controls
        _buildTableHeader(),
        
        // Table content
        Expanded(
          child: Scrollbar(
            controller: _verticalScrollController,
            child: Scrollbar(
              controller: _horizontalScrollController,
              notificationPredicate: (notification) => notification.depth == 1,
              child: SingleChildScrollView(
                controller: _verticalScrollController,
                child: SingleChildScrollView(
                  controller: _horizontalScrollController,
                  scrollDirection: Axis.horizontal,
                  child: _buildDataTable(),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build table header with selection controls
  Widget _buildTableHeader() {
    final allSelected = widget.tasks.isNotEmpty && 
        widget.selectedTaskIds.length == widget.tasks.length;
    final someSelected = widget.selectedTaskIds.isNotEmpty;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(bottom: BorderSide(color: AppColors.gray300)),
      ),
      child: Row(
        children: [
          Checkbox(
            value: allSelected,
            tristate: true,
            onChanged: (value) {
              if (allSelected || someSelected) {
                widget.onDeselectAll();
              } else {
                widget.onSelectAll();
              }
            },
          ),
          const SizedBox(width: 8),
          Text(
            '${widget.selectedTaskIds.length} of ${widget.tasks.length} selected',
            style: AppTextStyles.bodyMedium,
          ),
          const Spacer(),
          if (widget.selectedTaskIds.isNotEmpty) ...[
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _showBulkDeleteDialog,
              tooltip: 'Delete Selected',
            ),
            IconButton(
              icon: const Icon(Icons.check_circle),
              onPressed: _showBulkCompleteDialog,
              tooltip: 'Complete Selected',
            ),
          ],
        ],
      ),
    );
  }

  /// Build the data table
  Widget _buildDataTable() {
    return DataTable(
      columnSpacing: 16,
      horizontalMargin: 16,
      headingRowHeight: 56,
      dataRowHeight: 72,
      showCheckboxColumn: false,
      columns: _buildColumns(),
      rows: _buildRows(),
    );
  }

  /// Build table columns
  List<DataColumn> _buildColumns() {
    return [
      const DataColumn(label: Text('Sr. No.')),
      const DataColumn(label: Text('Date')),
      const DataColumn(label: Text('Order No.')),
      const DataColumn(label: Text('Client Name')),
      const DataColumn(label: Text('Design No.')),
      const DataColumn(label: Text('Size')),
      const DataColumn(label: Text('Color')),
      const DataColumn(label: Text('Bundle No.')),
      const DataColumn(label: Text('Start No.')),
      const DataColumn(label: Text('End No.')),
      const DataColumn(label: Text('Total Pieces')),
      const DataColumn(label: Text('Rate/Piece')),
      const DataColumn(label: Text('Total Amount')),
      const DataColumn(label: Text('Checked By')),
      const DataColumn(label: Text('Status')),
      const DataColumn(label: Text('Actions')),
    ];
  }

  /// Build table rows
  List<DataRow> _buildRows() {
    return widget.tasks.asMap().entries.map((entry) {
      final index = entry.key;
      final task = entry.value;
      final isSelected = widget.selectedTaskIds.contains(task.id);

      return DataRow(
        selected: isSelected,
        onSelectChanged: (selected) => widget.onTaskSelected(task.id),
        cells: [
          DataCell(Text('${index + 1}')),
          DataCell(Text(DateFormat('dd/MM/yyyy').format(task.date))),
          DataCell(Text(task.orderNo)),
          DataCell(Text(task.clientName)),
          DataCell(Text(task.designNo)),
          DataCell(Text(task.size.displayName)),
          DataCell(Text(task.color)),
          DataCell(Text(task.bundleNo)),
          DataCell(Text('${task.pieceStartNo}')),
          DataCell(Text('${task.pieceEndNo}')),
          DataCell(Text('${task.totalPieces}')),
          DataCell(Text('₹${task.ratePerPiece.toStringAsFixed(2)}')),
          DataCell(Text('₹${task.totalAmount.toStringAsFixed(2)}')),
          DataCell(Text(task.checkedBy)),
          DataCell(_buildStatusChip(task.status)),
          DataCell(_buildActionButtons(task)),
        ],
      );
    }).toList();
  }

  /// Build status chip
  Widget _buildStatusChip(NumberingTaskStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Color(int.parse(status.colorCode.substring(1), radix: 16) + 0xFF000000),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.displayName,
        style: AppTextStyles.labelSmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build action buttons for each task
  Widget _buildActionButtons(NumberingTask task) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showMyTasksActions) ...[
          if (task.canBeCompleted)
            IconButton(
              icon: const Icon(Icons.check_circle, color: Colors.green),
              onPressed: () => widget.onTaskAction('complete', task),
              tooltip: 'Complete Task',
            ),
          if (task.canBeSentToMunda)
            IconButton(
              icon: const Icon(Icons.send, color: Colors.blue),
              onPressed: () => widget.onTaskAction('send_to_munda', task),
              tooltip: 'Send to Munda',
            ),
        ],
        if (task.isEditable)
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.orange),
            onPressed: () => widget.onEditTask(task),
            tooltip: 'Edit Task',
          ),
        IconButton(
          icon: const Icon(Icons.delete, color: Colors.red),
          onPressed: () => widget.onDeleteTask(task),
          tooltip: 'Delete Task',
        ),
        PopupMenuButton<String>(
          onSelected: (action) => widget.onTaskAction(action, task),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view_details',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('View Details'),
                ],
              ),
            ),
            if (task.status == NumberingTaskStatus.pending)
              const PopupMenuItem(
                value: 'start_task',
                child: Row(
                  children: [
                    Icon(Icons.play_arrow),
                    SizedBox(width: 8),
                    Text('Start Task'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'duplicate',
              child: Row(
                children: [
                  Icon(Icons.copy),
                  SizedBox(width: 8),
                  Text('Duplicate'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Show bulk delete confirmation dialog
  void _showBulkDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Selected Tasks'),
        content: Text(
          'Are you sure you want to delete ${widget.selectedTaskIds.length} selected tasks? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Handle bulk delete
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Bulk delete functionality coming soon')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Show bulk complete confirmation dialog
  void _showBulkCompleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Selected Tasks'),
        content: Text(
          'Are you sure you want to mark ${widget.selectedTaskIds.length} selected tasks as completed?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Handle bulk complete
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Bulk complete functionality coming soon')),
              );
            },
            child: const Text('Complete'),
          ),
        ],
      ),
    );
  }
}
