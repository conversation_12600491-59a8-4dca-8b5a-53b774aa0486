import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Supplier entity
class Supplier extends BaseEntity {
  final String supplierCode;
  final String companyName;
  final String? displayName;
  final SupplierType type;
  final CommonStatus status;
  final SupplierContact primaryContact;
  final List<SupplierContact> contacts;
  final SupplierAddress address;
  final SupplierBusinessInfo businessInfo;
  final SupplierTerms terms;
  final SupplierRating rating;
  final List<String> categories;
  final List<String> tags;
  final Map<String, dynamic> metadata;
  final double creditLimit;
  final double currentBalance;
  final String? assignedBuyer;

  const Supplier({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.supplierCode,
    required this.companyName,
    this.displayName,
    required this.type,
    required this.status,
    required this.primaryContact,
    this.contacts = const [],
    required this.address,
    required this.businessInfo,
    required this.terms,
    required this.rating,
    this.categories = const [],
    this.tags = const [],
    this.metadata = const {},
    this.creditLimit = 0.0,
    this.currentBalance = 0.0,
    this.assignedBuyer,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        supplierCode,
        companyName,
        displayName,
        type,
        status,
        primaryContact,
        contacts,
        address,
        businessInfo,
        terms,
        rating,
        categories,
        tags,
        metadata,
        creditLimit,
        currentBalance,
        assignedBuyer,
      ];

  /// Get effective display name
  String get effectiveDisplayName => displayName ?? companyName;

  /// Check if supplier is active
  bool get isActive => status == CommonStatus.active;

  /// Get available credit
  double get availableCredit => creditLimit - currentBalance;

  /// Check if supplier has credit available
  bool get hasCreditAvailable => availableCredit > 0;

  /// Get all contact emails
  List<String> get allEmails {
    final emails = <String>[primaryContact.email];
    emails.addAll(contacts.map((c) => c.email));
    return emails.where((email) => email.isNotEmpty).toList();
  }

  /// Get all contact phones
  List<String> get allPhones {
    final phones = <String>[primaryContact.phone];
    phones.addAll(contacts.map((c) => c.phone));
    return phones.where((phone) => phone.isNotEmpty).toList();
  }
}

/// Supplier contact information
class SupplierContact extends Equatable {
  final String name;
  final String title;
  final String email;
  final String phone;
  final String? mobile;
  final bool isPrimary;
  final List<String> roles;

  const SupplierContact({
    required this.name,
    required this.title,
    required this.email,
    required this.phone,
    this.mobile,
    this.isPrimary = false,
    this.roles = const [],
  });

  @override
  List<Object?> get props => [
        name,
        title,
        email,
        phone,
        mobile,
        isPrimary,
        roles,
      ];
}

/// Supplier address information
class SupplierAddress extends Equatable {
  final String street;
  final String city;
  final String state;
  final String country;
  final String postalCode;
  final String? landmark;
  final double? latitude;
  final double? longitude;

  const SupplierAddress({
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    this.landmark,
    this.latitude,
    this.longitude,
  });

  @override
  List<Object?> get props => [
        street,
        city,
        state,
        country,
        postalCode,
        landmark,
        latitude,
        longitude,
      ];

  /// Get formatted address
  String get formattedAddress {
    final parts = <String>[
      street,
      city,
      state,
      country,
      postalCode,
    ];
    return parts.where((part) => part.isNotEmpty).join(', ');
  }
}

/// Supplier business information
class SupplierBusinessInfo extends Equatable {
  final String? taxId;
  final String? registrationNumber;
  final String industry;
  final String? website;
  final int? employeeCount;
  final double? annualRevenue;
  final DateTime? establishedDate;
  final List<String> certifications;
  final List<String> capabilities;

  const SupplierBusinessInfo({
    this.taxId,
    this.registrationNumber,
    required this.industry,
    this.website,
    this.employeeCount,
    this.annualRevenue,
    this.establishedDate,
    this.certifications = const [],
    this.capabilities = const [],
  });

  @override
  List<Object?> get props => [
        taxId,
        registrationNumber,
        industry,
        website,
        employeeCount,
        annualRevenue,
        establishedDate,
        certifications,
        capabilities,
      ];
}

/// Supplier terms
class SupplierTerms extends Equatable {
  final String currency;
  final int paymentTerms; // days
  final List<String> paymentMethods;
  final double minimumOrderValue;
  final String? shippingTerms;
  final int leadTimeDays;
  final String? warrantyTerms;
  final String? returnPolicy;
  final Map<String, dynamic> customTerms;

  const SupplierTerms({
    required this.currency,
    this.paymentTerms = 30,
    this.paymentMethods = const [],
    this.minimumOrderValue = 0.0,
    this.shippingTerms,
    this.leadTimeDays = 7,
    this.warrantyTerms,
    this.returnPolicy,
    this.customTerms = const {},
  });

  @override
  List<Object?> get props => [
        currency,
        paymentTerms,
        paymentMethods,
        minimumOrderValue,
        shippingTerms,
        leadTimeDays,
        warrantyTerms,
        returnPolicy,
        customTerms,
      ];
}

/// Supplier rating information
class SupplierRating extends Equatable {
  final double overallRating;
  final double qualityRating;
  final double deliveryRating;
  final double serviceRating;
  final double priceRating;
  final int totalOrders;
  final double totalValue;
  final DateTime? lastOrderDate;
  final int onTimeDeliveries;
  final int lateDeliveries;

  const SupplierRating({
    required this.overallRating,
    required this.qualityRating,
    required this.deliveryRating,
    required this.serviceRating,
    required this.priceRating,
    this.totalOrders = 0,
    this.totalValue = 0.0,
    this.lastOrderDate,
    this.onTimeDeliveries = 0,
    this.lateDeliveries = 0,
  });

  @override
  List<Object?> get props => [
        overallRating,
        qualityRating,
        deliveryRating,
        serviceRating,
        priceRating,
        totalOrders,
        totalValue,
        lastOrderDate,
        onTimeDeliveries,
        lateDeliveries,
      ];

  /// Get rating category
  SupplierRatingCategory get category {
    if (overallRating >= 4.5) return SupplierRatingCategory.excellent;
    if (overallRating >= 4.0) return SupplierRatingCategory.good;
    if (overallRating >= 3.0) return SupplierRatingCategory.average;
    if (overallRating >= 2.0) return SupplierRatingCategory.poor;
    return SupplierRatingCategory.bad;
  }

  /// Get on-time delivery percentage
  double get onTimeDeliveryPercentage {
    final totalDeliveries = onTimeDeliveries + lateDeliveries;
    if (totalDeliveries == 0) return 0.0;
    return (onTimeDeliveries / totalDeliveries) * 100;
  }
}

/// Supplier quotation entity
class SupplierQuotation extends BaseEntity {
  final String quotationNumber;
  final String supplierId;
  final String supplierName;
  final QuotationStatus status;
  final DateTime quotationDate;
  final DateTime validUntil;
  final List<QuotationItem> items;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final String currency;
  final String? notes;
  final String? terms;
  final String requestedBy;
  final String? approvedBy;
  final DateTime? approvedAt;

  const SupplierQuotation({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.quotationNumber,
    required this.supplierId,
    required this.supplierName,
    required this.status,
    required this.quotationDate,
    required this.validUntil,
    required this.items,
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.totalAmount,
    required this.currency,
    this.notes,
    this.terms,
    required this.requestedBy,
    this.approvedBy,
    this.approvedAt,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        quotationNumber,
        supplierId,
        supplierName,
        status,
        quotationDate,
        validUntil,
        items,
        subtotal,
        taxAmount,
        discountAmount,
        totalAmount,
        currency,
        notes,
        terms,
        requestedBy,
        approvedBy,
        approvedAt,
      ];

  /// Check if quotation is expired
  bool get isExpired => DateTime.now().isAfter(validUntil);

  /// Check if quotation is approved
  bool get isApproved => approvedAt != null && approvedBy != null;

  /// Get total quantity of all items
  double get totalQuantity => items.fold(0.0, (sum, item) => sum + item.quantity);
}

/// Quotation item entity
class QuotationItem extends Equatable {
  final String itemId;
  final String itemCode;
  final String itemName;
  final double quantity;
  final String unit;
  final double unitPrice;
  final double totalPrice;
  final int leadTimeDays;
  final String? notes;

  const QuotationItem({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.quantity,
    required this.unit,
    required this.unitPrice,
    required this.totalPrice,
    this.leadTimeDays = 7,
    this.notes,
  });

  @override
  List<Object?> get props => [
        itemId,
        itemCode,
        itemName,
        quantity,
        unit,
        unitPrice,
        totalPrice,
        leadTimeDays,
        notes,
      ];
}

// Enums

/// Supplier type enum
enum SupplierType {
  manufacturer,
  distributor,
  wholesaler,
  retailer,
  agent,
  broker,
}

/// Supplier type extension
extension SupplierTypeExtension on SupplierType {
  String get displayName {
    switch (this) {
      case SupplierType.manufacturer:
        return 'Manufacturer';
      case SupplierType.distributor:
        return 'Distributor';
      case SupplierType.wholesaler:
        return 'Wholesaler';
      case SupplierType.retailer:
        return 'Retailer';
      case SupplierType.agent:
        return 'Agent';
      case SupplierType.broker:
        return 'Broker';
    }
  }

  String get value => name;
}

/// Supplier rating category enum
enum SupplierRatingCategory {
  excellent,
  good,
  average,
  poor,
  bad,
}

/// Supplier rating category extension
extension SupplierRatingCategoryExtension on SupplierRatingCategory {
  String get displayName {
    switch (this) {
      case SupplierRatingCategory.excellent:
        return 'Excellent';
      case SupplierRatingCategory.good:
        return 'Good';
      case SupplierRatingCategory.average:
        return 'Average';
      case SupplierRatingCategory.poor:
        return 'Poor';
      case SupplierRatingCategory.bad:
        return 'Bad';
    }
  }

  String get value => name;
}

/// Quotation status enum
enum QuotationStatus {
  draft,
  sent,
  received,
  approved,
  rejected,
  expired,
  converted,
}

/// Quotation status extension
extension QuotationStatusExtension on QuotationStatus {
  String get displayName {
    switch (this) {
      case QuotationStatus.draft:
        return 'Draft';
      case QuotationStatus.sent:
        return 'Sent';
      case QuotationStatus.received:
        return 'Received';
      case QuotationStatus.approved:
        return 'Approved';
      case QuotationStatus.rejected:
        return 'Rejected';
      case QuotationStatus.expired:
        return 'Expired';
      case QuotationStatus.converted:
        return 'Converted';
    }
  }

  String get value => name;

  bool get canEdit {
    return this == QuotationStatus.draft ||
           this == QuotationStatus.received;
  }

  bool get canApprove {
    return this == QuotationStatus.received;
  }

  bool get canConvert {
    return this == QuotationStatus.approved;
  }
}
