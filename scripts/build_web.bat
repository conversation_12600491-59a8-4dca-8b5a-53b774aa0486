@echo off
setlocal enabledelayedexpansion

REM HM Collection Web Build Script for Windows
REM This script builds the Flutter web application with optimizations

echo.
echo 🚀 Starting HM Collection Web Build Process...
echo.

REM Check if Flutter is installed
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Flutter is not installed or not in PATH
    pause
    exit /b 1
)

echo [INFO] Flutter version:
flutter --version
echo.

REM Clean previous builds
echo [INFO] Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo [ERROR] Failed to clean project
    pause
    exit /b 1
)

REM Get dependencies
echo [INFO] Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo [ERROR] Failed to get dependencies
    pause
    exit /b 1
)

REM Run code generation
echo [INFO] Running code generation...
where dart >nul 2>nul
if %errorlevel% equ 0 (
    dart run build_runner build --delete-conflicting-outputs
) else (
    echo [WARNING] build_runner not available, skipping code generation
)

REM Check for web support
echo [INFO] Checking web support...
flutter devices | findstr "Chrome" >nul
if %errorlevel% neq 0 (
    echo [WARNING] Chrome not detected, but continuing with web build
)

REM Build for web with optimizations
echo [INFO] Building web application...
echo.

flutter build web ^
    --release ^
    --web-renderer canvaskit ^
    --base-href "/" ^
    --source-maps ^
    --tree-shake-icons ^
    --dart-define=FLUTTER_WEB_USE_SKIA=true ^
    --dart-define=FLUTTER_WEB_AUTO_DETECT=true

if %errorlevel% neq 0 (
    echo [ERROR] Web build failed!
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Web build completed successfully!
echo.

REM Build directory
set BUILD_DIR=build\web

if exist "%BUILD_DIR%" (
    echo [INFO] Build output directory: %BUILD_DIR%
    
    REM Show build directory contents
    echo [INFO] Main build files:
    dir "%BUILD_DIR%" | findstr /v "^$"
    echo.
    
    REM Check for critical files
    echo [INFO] Checking critical files:
    
    if exist "%BUILD_DIR%\index.html" (
        echo [SUCCESS] ✓ index.html found
    ) else (
        echo [WARNING] ✗ index.html missing
    )
    
    if exist "%BUILD_DIR%\main.dart.js" (
        echo [SUCCESS] ✓ main.dart.js found
    ) else (
        echo [WARNING] ✗ main.dart.js missing
    )
    
    if exist "%BUILD_DIR%\flutter_service_worker.js" (
        echo [SUCCESS] ✓ flutter_service_worker.js found
    ) else (
        echo [WARNING] ✗ flutter_service_worker.js missing
    )
    
    if exist "%BUILD_DIR%\manifest.json" (
        echo [SUCCESS] ✓ manifest.json found
    ) else (
        echo [WARNING] ✗ manifest.json missing
    )
    
    echo.
    
    REM Create deployment info
    echo [INFO] Creating build info...
    (
        echo {
        echo     "buildTime": "%date% %time%",
        echo     "version": "1.0.0",
        echo     "buildType": "production",
        echo     "renderer": "canvaskit",
        echo     "platform": "windows"
        echo }
    ) > "%BUILD_DIR%\build-info.json"
    
    echo [SUCCESS] Build info created
    echo.
    
) else (
    echo [ERROR] Build directory not found!
    pause
    exit /b 1
)

REM Performance recommendations
echo [INFO] Performance Recommendations:
echo   • Enable gzip compression on your web server
echo   • Set proper cache headers for static assets
echo   • Use HTTPS for better performance
echo   • Consider using a CDN for global distribution
echo   • Monitor Core Web Vitals
echo.

REM Deployment instructions
echo [INFO] Deployment Instructions:
echo   1. Upload the contents of '%BUILD_DIR%' to your web server
echo   2. Configure your server to serve index.html for all routes
echo   3. Set up proper MIME types for .wasm files
echo   4. Enable compression for .js, .css, and .html files
echo   5. Test the deployment thoroughly
echo.

REM Local testing
echo [INFO] Local Testing:
echo   To test locally, run:
echo   cd %BUILD_DIR% ^&^& python -m http.server 8000
echo   Then open http://localhost:8000 in your browser
echo.

echo [SUCCESS] 🎉 HM Collection web build process completed!
echo [INFO] Ready for deployment to production!
echo.

REM Optional: Start local server for testing
set /p choice="Would you like to start a local server for testing? (y/n): "
if /i "%choice%"=="y" (
    echo [INFO] Starting local server on port 8000...
    cd "%BUILD_DIR%"
    
    where python >nul 2>nul
    if %errorlevel% equ 0 (
        python -m http.server 8000
    ) else (
        where python3 >nul 2>nul
        if %errorlevel% equ 0 (
            python3 -m http.server 8000
        ) else (
            echo [WARNING] Python not found. Please serve the files manually.
            echo [INFO] You can use any static file server to serve the '%BUILD_DIR%' directory
        )
    )
)

pause
