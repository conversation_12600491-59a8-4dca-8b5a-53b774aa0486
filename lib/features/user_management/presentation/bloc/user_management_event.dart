part of 'user_management_bloc.dart';

/// Base user management event
abstract class UserManagementEvent extends Equatable {
  const UserManagementEvent();

  @override
  List<Object?> get props => [];
}

/// Load users with optional filtering and pagination
class LoadUsersRequested extends UserManagementEvent {
  final UserFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadUsersRequested({this.filter, this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load user details by ID
class LoadUserDetailsRequested extends UserManagementEvent {
  final String userId;

  const LoadUserDetailsRequested(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Create new user
class CreateUserRequested extends UserManagementEvent {
  final CreateUserRequest request;

  const CreateUserRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update existing user
class UpdateUserRequested extends UserManagementEvent {
  final UpdateUserRequest request;

  const UpdateUserRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Delete user
class DeleteUserRequested extends UserManagementEvent {
  final String userId;
  final String? reason;

  const DeleteUserRequested(this.userId, {this.reason});

  @override
  List<Object?> get props => [userId, reason];
}

/// Activate user
class ActivateUserRequested extends UserManagementEvent {
  final String userId;

  const ActivateUserRequested(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Deactivate user
class DeactivateUserRequested extends UserManagementEvent {
  final String userId;
  final String? reason;

  const DeactivateUserRequested(this.userId, {this.reason});

  @override
  List<Object?> get props => [userId, reason];
}

/// Assign role to user
class AssignRoleRequested extends UserManagementEvent {
  final RoleAssignmentRequest request;

  const AssignRoleRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Grant permissions to user
class GrantPermissionsRequested extends UserManagementEvent {
  final String userId;
  final List<String> permissions;
  final String? reason;

  const GrantPermissionsRequested(this.userId, this.permissions, {this.reason});

  @override
  List<Object?> get props => [userId, permissions, reason];
}

/// Revoke permissions from user
class RevokePermissionsRequested extends UserManagementEvent {
  final String userId;
  final List<String> permissions;
  final String? reason;

  const RevokePermissionsRequested(this.userId, this.permissions, {this.reason});

  @override
  List<Object?> get props => [userId, permissions, reason];
}

/// Search users
class SearchUsersRequested extends UserManagementEvent {
  final String query;
  final UserFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchUsersRequested(this.query, {this.filter, this.pagination});

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Load user statistics
class LoadUserStatisticsRequested extends UserManagementEvent {
  const LoadUserStatisticsRequested();
}

/// Load available roles
class LoadAvailableRolesRequested extends UserManagementEvent {
  const LoadAvailableRolesRequested();
}

/// Load available permissions
class LoadAvailablePermissionsRequested extends UserManagementEvent {
  const LoadAvailablePermissionsRequested();
}

/// Filter users
class FilterUsersRequested extends UserManagementEvent {
  final UserFilterCriteria filter;
  final PaginationParams? pagination;

  const FilterUsersRequested(this.filter, {this.pagination});

  @override
  List<Object?> get props => [filter, pagination];
}

/// Refresh users list
class RefreshUsersRequested extends UserManagementEvent {
  const RefreshUsersRequested();
}

/// Reset user password
class ResetUserPasswordRequested extends UserManagementEvent {
  final String userId;
  final String newPassword;

  const ResetUserPasswordRequested(this.userId, this.newPassword);

  @override
  List<Object?> get props => [userId, newPassword];
}

/// Force password change
class ForcePasswordChangeRequested extends UserManagementEvent {
  final String userId;

  const ForcePasswordChangeRequested(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Lock user account
class LockUserRequested extends UserManagementEvent {
  final String userId;
  final String reason;

  const LockUserRequested(this.userId, this.reason);

  @override
  List<Object?> get props => [userId, reason];
}

/// Unlock user account
class UnlockUserRequested extends UserManagementEvent {
  final String userId;

  const UnlockUserRequested(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Bulk update users
class BulkUpdateUsersRequested extends UserManagementEvent {
  final List<UpdateUserRequest> requests;

  const BulkUpdateUsersRequested(this.requests);

  @override
  List<Object?> get props => [requests];
}

/// Bulk delete users
class BulkDeleteUsersRequested extends UserManagementEvent {
  final List<String> userIds;
  final String? reason;

  const BulkDeleteUsersRequested(this.userIds, {this.reason});

  @override
  List<Object?> get props => [userIds, reason];
}

/// Export users
class ExportUsersRequested extends UserManagementEvent {
  final UserFilterCriteria? filter;
  final String format;

  const ExportUsersRequested({this.filter, this.format = 'csv'});

  @override
  List<Object?> get props => [filter, format];
}

/// Import users
class ImportUsersRequested extends UserManagementEvent {
  final String filePath;
  final bool validateOnly;

  const ImportUsersRequested(this.filePath, {this.validateOnly = false});

  @override
  List<Object?> get props => [filePath, validateOnly];
}

/// Load user activity logs
class LoadUserActivityLogsRequested extends UserManagementEvent {
  final String userId;
  final PaginationParams? pagination;

  const LoadUserActivityLogsRequested(this.userId, {this.pagination});

  @override
  List<Object?> get props => [userId, pagination];
}

/// Clear user management state
class ClearUserManagementState extends UserManagementEvent {
  const ClearUserManagementState();
}
