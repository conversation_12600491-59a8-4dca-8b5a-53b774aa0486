import 'package:flutter/material.dart';

class WorkerStatsWidget extends StatelessWidget {
  final int totalWorkers;
  final int activeWorkers;
  final int onLeave;
  final double attendanceRate;

  const WorkerStatsWidget({
    super.key,
    this.totalWorkers = 0,
    this.activeWorkers = 0,
    this.onLeave = 0,
    this.attendanceRate = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Worker Statistics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'Total Workers',
                  totalWorkers.toString(),
                  Icons.people,
                  Colors.blue,
                ),
                _buildStatItem(
                  'Active',
                  activeWorkers.toString(),
                  Icons.person,
                  Colors.green,
                ),
                _buildStatItem(
                  'On Leave',
                  onLeave.toString(),
                  Icons.beach_access,
                  Colors.orange,
                ),
                _buildStatItem(
                  'Attendance',
                  '${(attendanceRate * 100).toStringAsFixed(1)}%',
                  Icons.verified_user,
                  Colors.purple,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, size: 28, color: color),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
