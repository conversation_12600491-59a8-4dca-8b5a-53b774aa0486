import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/analytics_entities.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../datasources/analytics_datasource.dart';

/// Analytics repository implementation
@LazySingleton(as: AnalyticsRepository)
class AnalyticsRepositoryImpl implements AnalyticsRepository {
  final AnalyticsDataSource _dataSource;

  const AnalyticsRepositoryImpl(this._dataSource);

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsDashboard>>> getDashboards({
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getDashboards(
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> getDashboardById(String dashboardId) async {
    try {
      final result = await _dataSource.getDashboardById(dashboardId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> createDashboard(CreateDashboardRequest request) async {
    try {
      final result = await _dataSource.createDashboard(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> updateDashboard(UpdateDashboardRequest request) async {
    try {
      final result = await _dataSource.updateDashboard(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteDashboard(String dashboardId) async {
    try {
      final result = await _dataSource.deleteDashboard(dashboardId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> cloneDashboard(String dashboardId, String newName) async {
    try {
      final result = await _dataSource.cloneDashboard(dashboardId, newName);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> shareDashboard(ShareDashboardRequest request) async {
    try {
      final result = await _dataSource.shareDashboard(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<DashboardWidget>>> addWidget(AddWidgetRequest request) async {
    try {
      final result = await _dataSource.addWidget(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<DashboardWidget>>> updateWidget(UpdateWidgetRequest request) async {
    try {
      final result = await _dataSource.updateWidget(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> removeWidget(String dashboardId, String widgetId) async {
    try {
      final result = await _dataSource.removeWidget(dashboardId, widgetId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> reorderWidgets(ReorderWidgetsRequest request) async {
    try {
      final result = await _dataSource.reorderWidgets(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsReport>>> getReports({
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getReports(
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsReport>>> getReportById(String reportId) async {
    try {
      final result = await _dataSource.getReportById(reportId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsReport>>> generateReport(GenerateReportRequest request) async {
    try {
      final result = await _dataSource.generateReport(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<AnalyticsReport>>> scheduleReport(ScheduleReportRequest request) async {
    try {
      final result = await _dataSource.scheduleReport(request);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> cancelReport(String reportId) async {
    try {
      final result = await _dataSource.cancelReport(reportId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteReport(String reportId) async {
    try {
      final result = await _dataSource.deleteReport(reportId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getKpiData({
    required List<String> metrics,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final result = await _dataSource.getKpiData(
        metrics: metrics,
        startDate: startDate,
        endDate: endDate,
        filters: filters,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ChartData>> getChartData({
    required String chartType,
    required List<String> metrics,
    required List<String> dimensions,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final result = await _dataSource.getChartData(
        chartType: chartType,
        metrics: metrics,
        dimensions: dimensions,
        startDate: startDate,
        endDate: endDate,
        filters: filters,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, TableData>> getTableData({
    required List<String> columns,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
    String? sortBy,
    SortOrder? sortOrder,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getTableData(
        columns: columns,
        startDate: startDate,
        endDate: endDate,
        filters: filters,
        sortBy: sortBy,
        sortOrder: sortOrder,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getProductionMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  }) async {
    try {
      final result = await _dataSource.getProductionMetrics(
        startDate: startDate,
        endDate: endDate,
        departmentId: departmentId,
        productId: productId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getQualityMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  }) async {
    try {
      final result = await _dataSource.getQualityMetrics(
        startDate: startDate,
        endDate: endDate,
        departmentId: departmentId,
        productId: productId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getFinancialMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  }) async {
    try {
      final result = await _dataSource.getFinancialMetrics(
        startDate: startDate,
        endDate: endDate,
        costCenterId: costCenterId,
        departmentId: departmentId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getInventoryMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? warehouseId,
    String? categoryId,
  }) async {
    try {
      final result = await _dataSource.getInventoryMetrics(
        startDate: startDate,
        endDate: endDate,
        warehouseId: warehouseId,
        categoryId: categoryId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getEfficiencyMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? machineId,
  }) async {
    try {
      final result = await _dataSource.getEfficiencyMetrics(
        startDate: startDate,
        endDate: endDate,
        departmentId: departmentId,
        machineId: machineId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DataPoint>>> getTrendData({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required String granularity,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final result = await _dataSource.getTrendData(
        metric: metric,
        startDate: startDate,
        endDate: endDate,
        granularity: granularity,
        filters: filters,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getComparativeAnalysis({
    required List<String> metrics,
    required DateTime currentPeriodStart,
    required DateTime currentPeriodEnd,
    required DateTime previousPeriodStart,
    required DateTime previousPeriodEnd,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final result = await _dataSource.getComparativeAnalysis(
        metrics: metrics,
        currentPeriodStart: currentPeriodStart,
        currentPeriodEnd: currentPeriodEnd,
        previousPeriodStart: previousPeriodStart,
        previousPeriodEnd: previousPeriodEnd,
        filters: filters,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getForecast({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required int forecastPeriods,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final result = await _dataSource.getForecast(
        metric: metric,
        startDate: startDate,
        endDate: endDate,
        forecastPeriods: forecastPeriods,
        parameters: parameters,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getAnomalyDetection({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final result = await _dataSource.getAnomalyDetection(
        metric: metric,
        startDate: startDate,
        endDate: endDate,
        parameters: parameters,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getRealTimeMetrics({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final result = await _dataSource.getRealTimeMetrics(
        metrics: metrics,
        filters: filters,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Stream<Map<String, dynamic>> getRealTimeUpdates({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  }) {
    return _dataSource.getRealTimeUpdates(
      metrics: metrics,
      filters: filters,
    );
  }

  @override
  Future<Either<Failure, String>> exportDashboard({
    required String dashboardId,
    required String format,
    Map<String, dynamic>? options,
  }) async {
    try {
      final result = await _dataSource.exportDashboard(
        dashboardId: dashboardId,
        format: format,
        options: options,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> exportReport({
    required String reportId,
    required String format,
    Map<String, dynamic>? options,
  }) async {
    try {
      final result = await _dataSource.exportReport(
        reportId: reportId,
        format: format,
        options: options,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> exportData({
    required String dataType,
    required Map<String, dynamic> parameters,
    required String format,
  }) async {
    try {
      final result = await _dataSource.exportData(
        dataType: dataType,
        parameters: parameters,
        format: format,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsDashboard>>> searchDashboards({
    required String query,
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.searchDashboards(
        query: query,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsReport>>> searchReports({
    required String query,
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.searchReports(
        query: query,
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableMetrics({
    String? category,
    String? dataSource,
  }) async {
    try {
      final result = await _dataSource.getAvailableMetrics(
        category: category,
        dataSource: dataSource,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableDimensions({
    String? category,
    String? dataSource,
  }) async {
    try {
      final result = await _dataSource.getAvailableDimensions(
        category: category,
        dataSource: dataSource,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
