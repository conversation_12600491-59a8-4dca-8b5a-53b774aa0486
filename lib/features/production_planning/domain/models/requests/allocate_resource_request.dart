import 'package:equatable/equatable.dart';
import 'package:hm_collection/shared/enums/common_enums.dart';

import '../../../../resource_management/presentation/bloc/resource_bloc.dart';

/// Request model for allocating a resource to a task
class AllocateResourceRequest extends Equatable {
  final String taskId;
  final String resourceId;
  final ResourceType resourceType;
  final DateTime startTime;
  final DateTime endTime;
  final double quantity;
  final String? notes;

  const AllocateResourceRequest({
    required this.taskId,
    required this.resourceId,
    required this.resourceType,
    required this.startTime,
    required this.endTime,
    required this.quantity,
    this.notes,
  });

  @override
  List<Object?> get props => [
        taskId,
        resourceId,
        resourceType,
        startTime,
        endTime,
        quantity,
        notes,
      ];
}
