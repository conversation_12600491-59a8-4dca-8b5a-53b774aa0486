import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/auth/entities/user_entities.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../shared/enums/common_enums.dart';
import '../bloc/firebase_auth_bloc.dart';

class AdminLoginPage extends StatefulWidget {
  const AdminLoginPage({super.key});

  @override
  State<AdminLoginPage> createState() => _AdminLoginPageState();
}

class _AdminLoginPageState extends State<AdminLoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Provide a local FirebaseAuthBloc so this screen works standalone
    return BlocProvider<FirebaseAuthBloc>(
      create: (_) => GetIt.instance<FirebaseAuthBloc>(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Admin Login'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: BlocListener<FirebaseAuthBloc, FirebaseAuthState>(
          listener: (context, state) {
            if (state is FirebaseAuthAuthenticated) {
              if (state.user.role == UserRole.administrator) {
                _showSuccessMessage('Login successful! Redirecting...');
                // Redirect admins to the admin dashboard route
                // Note: Route path uses underscore as defined in AppRouter
                context.go('/admin_dashboard');
              } else {
                _showErrorMessage(
                    'You do not have permission to access the admin panel.');
                context.read<FirebaseAuthBloc>().add(const FirebaseSignOutRequested());
              }
            } else if (state is FirebaseAuthError) {
              _showErrorMessage(state.message);
            }
          },
          child: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
            builder: (context, state) {
              final isLoading = state is FirebaseAuthLoading;

              return SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildHeader(),
                      const SizedBox(height: 32),
                      _buildForm(isLoading),
                      const SizedBox(height: 24),
                      _buildSubmitButton(isLoading),
                      const SizedBox(height: 16),
                      _buildForgotPassword(isLoading),
                      const SizedBox(height: 24),
                      _buildGoogleSignInButton(isLoading),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        const Icon(
          Icons.shield_outlined,
          size: 64,
          color: AppColors.primary,
        ),
        const SizedBox(height: 16),
        Text(
          'Administrator Access',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Please sign in to manage the dashboard.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildForm(bool isLoading) {
    return Column(
      children: [
        CustomTextField(
          controller: _emailController,
          label: 'Email Address',
          hintText: 'Enter your admin email',
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          validator: _validateEmail,
          textInputAction: TextInputAction.next,
          enabled: !isLoading,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _passwordController,
          label: 'Password',
          hintText: 'Enter your password',
          prefixIcon: Icons.lock_outline,
          obscureText: _obscurePassword,
          enabled: !isLoading,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
          validator: _validatePassword,
          textInputAction: TextInputAction.done,
          onFieldSubmitted: (_) => _handleLogin(),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(bool isLoading) {
    return ElevatedButton(
      onPressed: isLoading ? null : _handleLogin,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
      child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Text(
              'Login',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
    );
  }

  Widget _buildForgotPassword(bool isLoading) {
    return Center(
      child: TextButton(
        onPressed: isLoading ? null : () {
          // TODO: Implement forgot password flow if needed
          // context.push('/forgot-password');
        },
        child: const Text('Forgot Password?'),
      ),
    );
  }

  Widget _buildGoogleSignInButton(bool isLoading) {
    return OutlinedButton.icon(
      //icon: Image.asset('assets/images/google_logo.png', height: 24.0),
      label: const Text('Sign in with Google'),
      onPressed: isLoading ? null : _handleGoogleSignIn,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
      ),
    );
  }

  void _handleLogin() {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    context.read<FirebaseAuthBloc>().add(
          FirebaseSignInRequested(
            email: _emailController.text.trim(),
            password: _passwordController.text,
          ),
        );
  }

  void _handleGoogleSignIn() {
    context.read<FirebaseAuthBloc>().add(const FirebaseAuthGoogleSignInRequested());
  }

  void _showSuccessMessage(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success ?? Colors.green,
      ),
    );
  }

  void _showErrorMessage(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error ?? Colors.red,
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    return null;
  }
}
