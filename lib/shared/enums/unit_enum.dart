/// Represents the unit of measurement for denim rolls
enum Unit {
  meters,
  yards,
}

/// Extension to add methods to the Unit enum
extension UnitExtension on Unit {
  /// Returns the string representation of the unit
  String get displayName {
    switch (this) {
      case Unit.meters:
        return 'meters';
      case Unit.yards:
        return 'yards';
    }
  }
}

/// Extension to add static methods to the Unit enum
extension UnitUtils on Unit {
  /// Converts a string to a Unit enum
  static Unit fromString(String value) {
    return Unit.values.firstWhere(
      (unit) => unit.toString().split('.').last == value || unit.displayName == value,
      orElse: () => Unit.meters, // Default to meters if not found
    );
  }
}
