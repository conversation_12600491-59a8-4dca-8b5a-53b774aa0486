part of 'bundling_bloc.dart';

/// Base bundling state
abstract class BundlingState extends Equatable {
  const BundlingState();

  @override
  List<Object?> get props => [];
}

/// Initial bundling state
class BundlingInitial extends BundlingState {
  const BundlingInitial();
}

/// Bundling loading state
class BundlingLoading extends BundlingState {
  const BundlingLoading();
}

/// Bundles loaded successfully
class BundlesLoaded extends BundlingState {
  final List<Bundle> bundles;
  final bool hasReachedMax;
  final BundleStatus? currentFilter;

  const BundlesLoaded({
    required this.bundles,
    this.hasReachedMax = false,
    this.currentFilter,
  });

  @override
  List<Object?> get props => [bundles, hasReachedMax, currentFilter];

  BundlesLoaded copyWith({
    List<Bundle>? bundles,
    bool? hasReachedMax,
    BundleStatus? currentFilter,
  }) {
    return BundlesLoaded(
      bundles: bundles ?? this.bundles,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentFilter: currentFilter ?? this.currentFilter,
    );
  }
}

/// Single bundle loaded successfully
class BundleLoaded extends BundlingState {
  final Bundle bundle;

  const BundleLoaded(this.bundle);

  @override
  List<Object?> get props => [bundle];
}

/// Bundle statistics loaded successfully
class BundleStatisticsLoaded extends BundlingState {
  final BundleStatistics statistics;

  const BundleStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Bundle creation preview state
class BundleCreationPreview extends BundlingState {
  final BundleInput input;
  final List<BundlePreview> previews;

  const BundleCreationPreview({
    required this.input,
    required this.previews,
  });

  @override
  List<Object?> get props => [input, previews];
}

/// Bundles created successfully
class BundlesCreated extends BundlingState {
  final List<Bundle> bundles;
  final String message;

  const BundlesCreated({
    required this.bundles,
    required this.message,
  });

  @override
  List<Object?> get props => [bundles, message];
}

/// Bundle updated successfully
class BundleUpdated extends BundlingState {
  final Bundle bundle;
  final String message;

  const BundleUpdated({
    required this.bundle,
    required this.message,
  });

  @override
  List<Object?> get props => [bundle, message];
}

/// Bundle deleted successfully
class BundleDeleted extends BundlingState {
  final String bundleId;
  final String message;

  const BundleDeleted({
    required this.bundleId,
    required this.message,
  });

  @override
  List<Object?> get props => [bundleId, message];
}

/// Bundle issued to sewing successfully
class BundleIssuedToSewing extends BundlingState {
  final Bundle bundle;
  final String message;

  const BundleIssuedToSewing({
    required this.bundle,
    required this.message,
  });

  @override
  List<Object?> get props => [bundle, message];
}

/// Bundle work started successfully
class BundleWorkStarted extends BundlingState {
  final Bundle bundle;
  final String message;

  const BundleWorkStarted({
    required this.bundle,
    required this.message,
  });

  @override
  List<Object?> get props => [bundle, message];
}

/// Bundle work completed successfully
class BundleWorkCompleted extends BundlingState {
  final Bundle bundle;
  final String message;

  const BundleWorkCompleted({
    required this.bundle,
    required this.message,
  });

  @override
  List<Object?> get props => [bundle, message];
}

/// Bundle cancelled successfully
class BundleCancelled extends BundlingState {
  final Bundle bundle;
  final String message;

  const BundleCancelled({
    required this.bundle,
    required this.message,
  });

  @override
  List<Object?> get props => [bundle, message];
}

/// Bundle search results loaded
class BundleSearchResults extends BundlingState {
  final List<Bundle> bundles;
  final String query;

  const BundleSearchResults({
    required this.bundles,
    required this.query,
  });

  @override
  List<Object?> get props => [bundles, query];
}

/// Bundle input validation result
class BundleInputValidated extends BundlingState {
  final bool isValid;
  final List<String> errors;

  const BundleInputValidated({
    required this.isValid,
    required this.errors,
  });

  @override
  List<Object?> get props => [isValid, errors];
}

/// Bundle export completed
class BundleExportCompleted extends BundlingState {
  final String filePath;
  final String format;
  final String message;

  const BundleExportCompleted({
    required this.filePath,
    required this.format,
    required this.message,
  });

  @override
  List<Object?> get props => [filePath, format, message];
}

/// Offline bundles synced successfully
class OfflineBundlesSynced extends BundlingState {
  final int syncedCount;
  final String message;

  const OfflineBundlesSynced({
    required this.syncedCount,
    required this.message,
  });

  @override
  List<Object?> get props => [syncedCount, message];
}

/// Bundling operation in progress
class BundlingOperationInProgress extends BundlingState {
  final String operation;
  final String? message;

  const BundlingOperationInProgress({
    required this.operation,
    this.message,
  });

  @override
  List<Object?> get props => [operation, message];
}

/// Bundling error state
class BundlingError extends BundlingState {
  final String message;
  final String? errorCode;

  const BundlingError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// Bundling validation error state
class BundlingValidationError extends BundlingState {
  final Map<String, String> errors;

  const BundlingValidationError(this.errors);

  @override
  List<Object?> get props => [errors];
}

/// Bundling network error state
class BundlingNetworkError extends BundlingState {
  final String message;

  const BundlingNetworkError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Bundling permission error state
class BundlingPermissionError extends BundlingState {
  final String message;

  const BundlingPermissionError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Bundle filters applied state
class BundleFiltersApplied extends BundlingState {
  final BundleStatus? status;
  final String? designNo;
  final String? lotNo;
  final String? rollNo;
  final GarmentSize? size;
  final DateTime? startDate;
  final DateTime? endDate;

  const BundleFiltersApplied({
    this.status,
    this.designNo,
    this.lotNo,
    this.rollNo,
    this.size,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [
        status,
        designNo,
        lotNo,
        rollNo,
        size,
        startDate,
        endDate,
      ];
}

/// Bundle filters cleared state
class BundleFiltersCleared extends BundlingState {
  const BundleFiltersCleared();
}

/// Bundle operation success state
class BundlingOperationSuccess extends BundlingState {
  final String message;
  final String operation;

  const BundlingOperationSuccess({
    required this.message,
    required this.operation,
  });

  @override
  List<Object?> get props => [message, operation];
}
