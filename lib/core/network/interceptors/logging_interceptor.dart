import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

/// Interceptor for logging HTTP requests and responses
class LoggingInterceptor extends Interceptor {
  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 75,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _logRequest(options);
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _logResponse(response);
    handler.next(response);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _logError(err);
    handler.next(err);
  }
  
  /// Log HTTP request details
  void _logRequest(RequestOptions options) {
    final uri = options.uri;
    final method = options.method.toUpperCase();
    
    _logger.d('🚀 $method Request: $uri');
    
    // Log headers (excluding sensitive information)
    if (options.headers.isNotEmpty) {
      final sanitizedHeaders = _sanitizeHeaders(options.headers);
      _logger.d('📋 Headers: $sanitizedHeaders');
    }
    
    // Log query parameters
    if (options.queryParameters.isNotEmpty) {
      _logger.d('🔍 Query Parameters: ${options.queryParameters}');
    }
    
    // Log request data (excluding sensitive information)
    if (options.data != null) {
      final sanitizedData = _sanitizeData(options.data);
      _logger.d('📦 Request Data: $sanitizedData');
    }
  }
  
  /// Log HTTP response details
  void _logResponse(Response response) {
    final uri = response.requestOptions.uri;
    final method = response.requestOptions.method.toUpperCase();
    final statusCode = response.statusCode;
    final statusMessage = response.statusMessage;
    
    _logger.i('✅ $method Response: $uri');
    _logger.i('📊 Status: $statusCode $statusMessage');
    
    // Log response headers
    if (response.headers.map.isNotEmpty) {
      _logger.d('📋 Response Headers: ${response.headers.map}');
    }
    
    // Log response data (truncated if too long)
    final responseData = response.data;
    if (responseData != null) {
      final truncatedData = _truncateData(responseData);
      _logger.d('📦 Response Data: $truncatedData');
    }
  }
  
  /// Log HTTP error details
  void _logError(DioException err) {
    final uri = err.requestOptions.uri;
    final method = err.requestOptions.method.toUpperCase();
    
    _logger.e('❌ $method Error: $uri');
    _logger.e('🔥 Error Type: ${err.type}');
    _logger.e('💬 Error Message: ${err.message}');
    
    if (err.response != null) {
      final statusCode = err.response!.statusCode;
      final statusMessage = err.response!.statusMessage;
      _logger.e('📊 Status: $statusCode $statusMessage');
      
      if (err.response!.data != null) {
        final errorData = _truncateData(err.response!.data);
        _logger.e('📦 Error Data: $errorData');
      }
    }
    
    if (err.stackTrace != null) {
      _logger.e('📚 Stack Trace:', stackTrace: err.stackTrace);
    }
  }
  
  /// Sanitize headers by removing sensitive information
  Map<String, dynamic> _sanitizeHeaders(Map<String, dynamic> headers) {
    final sanitized = Map<String, dynamic>.from(headers);
    
    // List of sensitive header keys to mask
    final sensitiveKeys = [
      'authorization',
      'cookie',
      'set-cookie',
      'x-api-key',
      'x-auth-token',
      'bearer',
    ];
    
    for (final key in sensitiveKeys) {
      final lowerKey = key.toLowerCase();
      final foundKey = sanitized.keys.firstWhere(
        (k) => k.toString().toLowerCase() == lowerKey,
        orElse: () => '',
      );
      
      if (foundKey.isNotEmpty) {
        sanitized[foundKey] = '***MASKED***';
      }
    }
    
    return sanitized;
  }
  
  /// Sanitize request data by removing sensitive information
  dynamic _sanitizeData(dynamic data) {
    if (data == null) return null;
    
    if (data is Map<String, dynamic>) {
      final sanitized = Map<String, dynamic>.from(data);
      
      // List of sensitive field keys to mask
      final sensitiveKeys = [
        'password',
        'token',
        'secret',
        'key',
        'auth',
        'credential',
        'pin',
        'ssn',
        'social_security',
        'credit_card',
        'card_number',
      ];
      
      for (final key in sensitiveKeys) {
        final lowerKey = key.toLowerCase();
        final foundKey = sanitized.keys.firstWhere(
          (k) => k.toString().toLowerCase().contains(lowerKey),
          orElse: () => '',
        );
        
        if (foundKey.isNotEmpty) {
          sanitized[foundKey] = '***MASKED***';
        }
      }
      
      return sanitized;
    }
    
    if (data is FormData) {
      return 'FormData(${data.fields.length} fields, ${data.files.length} files)';
    }
    
    return data;
  }
  
  /// Truncate data if it's too long for logging
  String _truncateData(dynamic data) {
    const maxLength = 1000;
    final dataString = data.toString();
    
    if (dataString.length <= maxLength) {
      return dataString;
    }
    
    return '${dataString.substring(0, maxLength)}... (truncated)';
  }
}
