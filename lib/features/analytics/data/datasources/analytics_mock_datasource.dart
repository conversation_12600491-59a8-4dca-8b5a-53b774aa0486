import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/analytics_entities.dart';
import '../../domain/repositories/analytics_repository.dart';
import 'analytics_datasource.dart';

/// Mock analytics data source implementation
@LazySingleton(as: AnalyticsDataSource)
class AnalyticsMockDataSource implements AnalyticsDataSource {
  // Mock data
  final List<AnalyticsDashboard> _dashboards = _generateMockDashboards();
  final List<AnalyticsReport> _reports = _generateMockReports();
  final Random _random = Random();

  @override
  Future<ApiListResponse<AnalyticsDashboard>> getDashboards({
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredDashboards = _dashboards.where((dashboard) {
      if (filter?.type != null && dashboard.type != filter!.type) return false;
      if (filter?.ownerId != null && dashboard.ownerId != filter!.ownerId) return false;
      if (filter?.isPublic != null && dashboard.isPublic != filter!.isPublic) return false;
      if (filter?.isDefault != null && dashboard.isDefault != filter!.isDefault) return false;
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedDashboards = filteredDashboards.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedDashboards,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        totalPages: (filteredDashboards.length / (pagination?.perPage ?? 20)).ceil(),
        total: filteredDashboards.length,
        perPage: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredDashboards.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<ApiResponse<AnalyticsDashboard>> getDashboardById(String dashboardId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final dashboard = _dashboards.firstWhere(
      (d) => d.id == dashboardId,
      orElse: () => throw Exception('Dashboard not found'),
    );

    return ApiResponse(data: dashboard, success: true);
  }

  @override
  Future<ApiResponse<AnalyticsDashboard>> createDashboard(CreateDashboardRequest request) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final newDashboard = AnalyticsDashboard(
      id: 'dashboard_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      dashboardCode: 'DASH-${DateTime.now().millisecondsSinceEpoch}',
      dashboardName: request.dashboardName,
      description: request.description,
      type: request.type,
      status: CommonStatus.active,
      ownerId: 'current_user', // Mock
      ownerName: 'Current User', // Mock
      sharedWithUserIds: request.sharedWithUserIds,
      sharedWithRoleIds: request.sharedWithRoleIds,
      layout: request.layout,
      widgets: const [],
      settings: request.settings,
      isPublic: request.isPublic,
    );

    _dashboards.add(newDashboard);
    return ApiResponse(data: newDashboard, success: true);
  }

  @override
  Future<ApiResponse<AnalyticsDashboard>> updateDashboard(UpdateDashboardRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final index = _dashboards.indexWhere((d) => d.id == request.dashboardId);
    if (index == -1) throw Exception('Dashboard not found');

    final existingDashboard = _dashboards[index];
    final updatedDashboard = AnalyticsDashboard(
      id: existingDashboard.id,
      createdAt: existingDashboard.createdAt,
      updatedAt: DateTime.now(),
      dashboardCode: existingDashboard.dashboardCode,
      dashboardName: request.dashboardName ?? existingDashboard.dashboardName,
      description: request.description ?? existingDashboard.description,
      type: existingDashboard.type,
      status: existingDashboard.status,
      ownerId: existingDashboard.ownerId,
      ownerName: existingDashboard.ownerName,
      sharedWithUserIds: request.sharedWithUserIds ?? existingDashboard.sharedWithUserIds,
      sharedWithRoleIds: request.sharedWithRoleIds ?? existingDashboard.sharedWithRoleIds,
      layout: request.layout ?? existingDashboard.layout,
      widgets: existingDashboard.widgets,
      settings: request.settings ?? existingDashboard.settings,
      filters: existingDashboard.filters,
      lastViewedAt: existingDashboard.lastViewedAt,
      viewCount: existingDashboard.viewCount,
      isDefault: existingDashboard.isDefault,
      isPublic: request.isPublic ?? existingDashboard.isPublic,
    );

    _dashboards[index] = updatedDashboard;
    return ApiResponse(data: updatedDashboard, success: true);
  }

  @override
  Future<ApiVoidResponse> deleteDashboard(String dashboardId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    _dashboards.removeWhere((d) => d.id == dashboardId);
    return const ApiVoidResponse();
  }

  @override
  Future<ApiResponse<AnalyticsDashboard>> cloneDashboard(String dashboardId, String newName) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final originalDashboard = _dashboards.firstWhere(
      (d) => d.id == dashboardId,
      orElse: () => throw Exception('Dashboard not found'),
    );

    final clonedDashboard = AnalyticsDashboard(
      id: 'dashboard_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      dashboardCode: 'DASH-${DateTime.now().millisecondsSinceEpoch}',
      dashboardName: newName,
      description: '${originalDashboard.description} (Copy)',
      type: originalDashboard.type,
      status: CommonStatus.active,
      ownerId: 'current_user', // Mock
      ownerName: 'Current User', // Mock
      layout: originalDashboard.layout,
      widgets: originalDashboard.widgets,
      settings: originalDashboard.settings,
      isPublic: false,
    );

    _dashboards.add(clonedDashboard);
    return ApiResponse(data: clonedDashboard, success: true);
  }

  @override
  Future<ApiResponse<AnalyticsDashboard>> shareDashboard(ShareDashboardRequest request) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _dashboards.indexWhere((d) => d.id == request.dashboardId);
    if (index == -1) throw Exception('Dashboard not found');

    final existingDashboard = _dashboards[index];
    final updatedDashboard = AnalyticsDashboard(
      id: existingDashboard.id,
      createdAt: existingDashboard.createdAt,
      updatedAt: DateTime.now(),
      dashboardCode: existingDashboard.dashboardCode,
      dashboardName: existingDashboard.dashboardName,
      description: existingDashboard.description,
      type: existingDashboard.type,
      status: existingDashboard.status,
      ownerId: existingDashboard.ownerId,
      ownerName: existingDashboard.ownerName,
      sharedWithUserIds: [...existingDashboard.sharedWithUserIds, ...request.userIds],
      sharedWithRoleIds: [...existingDashboard.sharedWithRoleIds, ...request.roleIds],
      layout: existingDashboard.layout,
      widgets: existingDashboard.widgets,
      settings: existingDashboard.settings,
      filters: existingDashboard.filters,
      lastViewedAt: existingDashboard.lastViewedAt,
      viewCount: existingDashboard.viewCount,
      isDefault: existingDashboard.isDefault,
      isPublic: request.makePublic || existingDashboard.isPublic,
    );

    _dashboards[index] = updatedDashboard;
    return ApiResponse(data: updatedDashboard, success: true);
  }

  @override
  Future<ApiResponse<DashboardWidget>> addWidget(AddWidgetRequest request) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final newWidget = DashboardWidget(
      widgetId: 'widget_${DateTime.now().millisecondsSinceEpoch}',
      widgetType: request.widgetType,
      title: request.title,
      description: request.description,
      position: request.position,
      size: request.size,
      configuration: request.configuration,
      dataSource: request.dataSource,
      refreshSettings: request.refreshSettings,
    );

    // Update dashboard with new widget
    final dashboardIndex = _dashboards.indexWhere((d) => d.id == request.dashboardId);
    if (dashboardIndex != -1) {
      final dashboard = _dashboards[dashboardIndex];
      final updatedDashboard = AnalyticsDashboard(
        id: dashboard.id,
        createdAt: dashboard.createdAt,
        updatedAt: DateTime.now(),
        dashboardCode: dashboard.dashboardCode,
        dashboardName: dashboard.dashboardName,
        description: dashboard.description,
        type: dashboard.type,
        status: dashboard.status,
        ownerId: dashboard.ownerId,
        ownerName: dashboard.ownerName,
        sharedWithUserIds: dashboard.sharedWithUserIds,
        sharedWithRoleIds: dashboard.sharedWithRoleIds,
        layout: dashboard.layout,
        widgets: [...dashboard.widgets, newWidget],
        settings: dashboard.settings,
        filters: dashboard.filters,
        lastViewedAt: dashboard.lastViewedAt,
        viewCount: dashboard.viewCount,
        isDefault: dashboard.isDefault,
        isPublic: dashboard.isPublic,
      );
      _dashboards[dashboardIndex] = updatedDashboard;
    }

    return ApiResponse(data: newWidget, success: true);
  }

  @override
  Future<ApiResponse<DashboardWidget>> updateWidget(UpdateWidgetRequest request) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final dashboardIndex = _dashboards.indexWhere((d) => d.id == request.dashboardId);
    if (dashboardIndex == -1) throw Exception('Dashboard not found');

    final dashboard = _dashboards[dashboardIndex];
    final widgetIndex = dashboard.widgets.indexWhere((w) => w.widgetId == request.widgetId);
    if (widgetIndex == -1) throw Exception('Widget not found');

    final existingWidget = dashboard.widgets[widgetIndex];
    final updatedWidget = DashboardWidget(
      widgetId: existingWidget.widgetId,
      widgetType: existingWidget.widgetType,
      title: request.title ?? existingWidget.title,
      description: request.description ?? existingWidget.description,
      position: request.position ?? existingWidget.position,
      size: request.size ?? existingWidget.size,
      configuration: request.configuration ?? existingWidget.configuration,
      dataSource: request.dataSource ?? existingWidget.dataSource,
      refreshSettings: request.refreshSettings ?? existingWidget.refreshSettings,
      isVisible: request.isVisible ?? existingWidget.isVisible,
      order: existingWidget.order,
    );

    final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets);
    updatedWidgets[widgetIndex] = updatedWidget;

    final updatedDashboard = AnalyticsDashboard(
      id: dashboard.id,
      createdAt: dashboard.createdAt,
      updatedAt: DateTime.now(),
      dashboardCode: dashboard.dashboardCode,
      dashboardName: dashboard.dashboardName,
      description: dashboard.description,
      type: dashboard.type,
      status: dashboard.status,
      ownerId: dashboard.ownerId,
      ownerName: dashboard.ownerName,
      sharedWithUserIds: dashboard.sharedWithUserIds,
      sharedWithRoleIds: dashboard.sharedWithRoleIds,
      layout: dashboard.layout,
      widgets: updatedWidgets,
      settings: dashboard.settings,
      filters: dashboard.filters,
      lastViewedAt: dashboard.lastViewedAt,
      viewCount: dashboard.viewCount,
      isDefault: dashboard.isDefault,
      isPublic: dashboard.isPublic,
    );

    _dashboards[dashboardIndex] = updatedDashboard;
    return ApiResponse(data: updatedWidget, success: true);
  }

  @override
  Future<ApiVoidResponse> removeWidget(String dashboardId, String widgetId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    final dashboardIndex = _dashboards.indexWhere((d) => d.id == dashboardId);
    if (dashboardIndex == -1) throw Exception('Dashboard not found');

    final dashboard = _dashboards[dashboardIndex];
    final updatedWidgets = dashboard.widgets.where((w) => w.widgetId != widgetId).toList();

    final updatedDashboard = AnalyticsDashboard(
      id: dashboard.id,
      createdAt: dashboard.createdAt,
      updatedAt: DateTime.now(),
      dashboardCode: dashboard.dashboardCode,
      dashboardName: dashboard.dashboardName,
      description: dashboard.description,
      type: dashboard.type,
      status: dashboard.status,
      ownerId: dashboard.ownerId,
      ownerName: dashboard.ownerName,
      sharedWithUserIds: dashboard.sharedWithUserIds,
      sharedWithRoleIds: dashboard.sharedWithRoleIds,
      layout: dashboard.layout,
      widgets: updatedWidgets,
      settings: dashboard.settings,
      filters: dashboard.filters,
      lastViewedAt: dashboard.lastViewedAt,
      viewCount: dashboard.viewCount,
      isDefault: dashboard.isDefault,
      isPublic: dashboard.isPublic,
    );

    _dashboards[dashboardIndex] = updatedDashboard;
    return const ApiVoidResponse();
  }

  @override
  Future<ApiResponse<AnalyticsDashboard>> reorderWidgets(ReorderWidgetsRequest request) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final dashboardIndex = _dashboards.indexWhere((d) => d.id == request.dashboardId);
    if (dashboardIndex == -1) throw Exception('Dashboard not found');

    final dashboard = _dashboards[dashboardIndex];
    final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets);

    // Update widget orders
    for (final widgetOrder in request.widgetOrders) {
      final widgetIndex = updatedWidgets.indexWhere((w) => w.widgetId == widgetOrder.widgetId);
      if (widgetIndex != -1) {
        final widget = updatedWidgets[widgetIndex];
        updatedWidgets[widgetIndex] = DashboardWidget(
          widgetId: widget.widgetId,
          widgetType: widget.widgetType,
          title: widget.title,
          description: widget.description,
          position: widgetOrder.position ?? widget.position,
          size: widget.size,
          configuration: widget.configuration,
          dataSource: widget.dataSource,
          refreshSettings: widget.refreshSettings,
          isVisible: widget.isVisible,
          order: widgetOrder.order,
        );
      }
    }

    // Sort by order
    updatedWidgets.sort((a, b) => a.order.compareTo(b.order));

    final updatedDashboard = AnalyticsDashboard(
      id: dashboard.id,
      createdAt: dashboard.createdAt,
      updatedAt: DateTime.now(),
      dashboardCode: dashboard.dashboardCode,
      dashboardName: dashboard.dashboardName,
      description: dashboard.description,
      type: dashboard.type,
      status: dashboard.status,
      ownerId: dashboard.ownerId,
      ownerName: dashboard.ownerName,
      sharedWithUserIds: dashboard.sharedWithUserIds,
      sharedWithRoleIds: dashboard.sharedWithRoleIds,
      layout: dashboard.layout,
      widgets: updatedWidgets,
      settings: dashboard.settings,
      filters: dashboard.filters,
      lastViewedAt: dashboard.lastViewedAt,
      viewCount: dashboard.viewCount,
      isDefault: dashboard.isDefault,
      isPublic: dashboard.isPublic,
    );

    _dashboards[dashboardIndex] = updatedDashboard;
    return ApiResponse(data: updatedDashboard, success: true);
  }

  @override
  Future<ApiListResponse<AnalyticsReport>> getReports({
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredReports = _reports.where((report) {
      if (filter?.type != null && report.type != filter!.type) return false;
      if (filter?.category != null && report.category != filter!.category) return false;
      if (filter?.status != null && report.status != filter!.status) return false;
      if (filter?.generatedBy != null && report.generatedBy != filter!.generatedBy) return false;
      if (filter?.isScheduled != null && report.isScheduled != filter!.isScheduled) return false;
      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedReports = filteredReports.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedReports,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        totalPages: (filteredReports.length / (pagination?.perPage ?? 20)).ceil(),
        total: filteredReports.length,
        perPage: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredReports.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<ApiResponse<AnalyticsReport>> getReportById(String reportId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final report = _reports.firstWhere(
      (r) => r.id == reportId,
      orElse: () => throw Exception('Report not found'),
    );

    return ApiResponse(data: report, success: true);
  }

  @override
  Future<ApiResponse<AnalyticsReport>> generateReport(GenerateReportRequest request) async {
    await Future.delayed(const Duration(milliseconds: 2000)); // Simulate report generation

    final newReport = AnalyticsReport(
      id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      reportCode: 'RPT-${DateTime.now().millisecondsSinceEpoch}',
      reportName: request.reportName,
      description: request.description,
      type: request.type,
      category: request.category,
      status: ReportStatus.completed,
      generatedBy: 'current_user', // Mock
      generatedAt: DateTime.now(),
      parameters: request.parameters,
      data: _generateMockReportData(),
      settings: request.settings,
      recipients: request.recipients,
      tags: request.tags,
    );

    _reports.add(newReport);
    return ApiResponse(data: newReport, success: true);
  }

  @override
  Future<ApiResponse<AnalyticsReport>> scheduleReport(ScheduleReportRequest request) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final newReport = AnalyticsReport(
      id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      reportCode: 'RPT-${DateTime.now().millisecondsSinceEpoch}',
      reportName: request.reportName,
      description: request.description,
      type: request.type,
      category: request.category,
      status: ReportStatus.scheduled,
      generatedBy: 'current_user', // Mock
      parameters: request.parameters,
      data: const ReportData(),
      settings: request.settings,
      recipients: request.recipients,
      tags: request.tags,
      isScheduled: true,
      scheduleConfig: request.scheduleConfig,
    );

    _reports.add(newReport);
    return ApiResponse(data: newReport, success: true);
  }

  @override
  Future<ApiVoidResponse> cancelReport(String reportId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final index = _reports.indexWhere((r) => r.id == reportId);
    if (index != -1) {
      final report = _reports[index];
      final updatedReport = AnalyticsReport(
        id: report.id,
        createdAt: report.createdAt,
        updatedAt: DateTime.now(),
        reportCode: report.reportCode,
        reportName: report.reportName,
        description: report.description,
        type: report.type,
        category: report.category,
        status: ReportStatus.cancelled,
        generatedBy: report.generatedBy,
        generatedAt: report.generatedAt,
        scheduledAt: report.scheduledAt,
        parameters: report.parameters,
        data: report.data,
        settings: report.settings,
        recipients: report.recipients,
        tags: report.tags,
        isScheduled: report.isScheduled,
        scheduleConfig: report.scheduleConfig,
      );
      _reports[index] = updatedReport;
    }

    return const ApiVoidResponse();
  }

  @override
  Future<ApiVoidResponse> deleteReport(String reportId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    _reports.removeWhere((r) => r.id == reportId);
    return const ApiVoidResponse();
  }

  @override
  Future<Map<String, dynamic>> getKpiData({
    required List<String> metrics,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    // Generate mock KPI data
    final kpiData = <String, dynamic>{};

    for (final metric in metrics) {
      switch (metric) {
        case 'production_volume':
          kpiData[metric] = 15000 + _random.nextInt(5000);
          break;
        case 'quality_score':
          kpiData[metric] = 85.0 + _random.nextDouble() * 15;
          break;
        case 'efficiency_rate':
          kpiData[metric] = 75.0 + _random.nextDouble() * 20;
          break;
        case 'cost_per_unit':
          kpiData[metric] = 12.50 + _random.nextDouble() * 5;
          break;
        case 'revenue':
          kpiData[metric] = 500000 + _random.nextInt(200000);
          break;
        case 'profit_margin':
          kpiData[metric] = 15.0 + _random.nextDouble() * 10;
          break;
        default:
          kpiData[metric] = _random.nextDouble() * 100;
      }
    }

    return kpiData;
  }

  @override
  Future<ChartData> getChartData({
    required String chartType,
    required List<String> metrics,
    required List<String> dimensions,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));

    // Generate mock chart data
    final series = <DataSeries>[];
    final categories = <String>[];

    // Generate categories based on date range
    final days = endDate.difference(startDate).inDays;
    for (int i = 0; i <= days; i += 7) {
      final date = startDate.add(Duration(days: i));
      categories.add('Week ${i ~/ 7 + 1}');
    }

    // Generate series for each metric
    for (int i = 0; i < metrics.length; i++) {
      final metric = metrics[i];
      final dataPoints = <DataPoint>[];

      for (int j = 0; j < categories.length; j++) {
        final baseValue = _getBaseValueForMetric(metric);
        final value = baseValue + (_random.nextDouble() - 0.5) * baseValue * 0.3;
        dataPoints.add(DataPoint(
          label: categories[j],
          value: value,
          timestamp: startDate.add(Duration(days: j * 7)),
        ));
      }

      series.add(DataSeries(
        seriesId: 'series_$i',
        name: metric,
        dataPoints: dataPoints,
        color: _getColorForSeries(i),
        type: SeriesType.line,
      ));
    }

    return ChartData(
      chartId: 'chart_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Analytics Chart',
      chartType: ChartType.values.firstWhere(
        (type) => type.name == chartType,
        orElse: () => ChartType.line,
      ),
      series: series,
      categories: categories,
      configuration: const ChartConfiguration(),
    );
  }

  @override
  Future<TableData> getTableData({
    required List<String> columns,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? filters,
    String? sortBy,
    SortOrder? sortOrder,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));

    // Generate mock table data
    final tableColumns = columns.map((column) => TableColumn(
      columnId: column,
      header: column.replaceAll('_', ' ').toUpperCase(),
      dataType: 'string',
    )).toList();

    final rowData = <Map<String, dynamic>>[];
    final rowCount = pagination?.perPage ?? 25;

    for (int i = 0; i < rowCount; i++) {
      final row = <String, dynamic>{'id': 'row_$i'};
      for (final column in columns) {
        row[column] = _generateMockCellValue(column, i);
      }
      rowData.add(row);
    }

    // Convert row data to AnalyticsTableRow entities
    final tableRows = rowData.map<AnalyticsTableRow>((row) {
      return AnalyticsTableRow(
        rowId: row['id']?.toString() ?? '',
        cells: row,
        metadata: {},
      );
    }).toList();

    return TableData(
      tableId: 'table_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Analytics Table',
      columns: tableColumns,
      rows: tableRows,
      configuration: const TableConfiguration(),
    );
  }

  // Continue with remaining methods...
  @override
  Future<Map<String, dynamic>> getProductionMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return {
      'total_production': 15000 + _random.nextInt(5000),
      'production_rate': 85.5 + _random.nextDouble() * 10,
      'efficiency': 78.2 + _random.nextDouble() * 15,
      'downtime_hours': 12 + _random.nextInt(8),
      'quality_rate': 96.5 + _random.nextDouble() * 3,
      'waste_percentage': 2.1 + _random.nextDouble() * 2,
      'oee_score': 72.8 + _random.nextDouble() * 20,
    };
  }

  @override
  Future<Map<String, dynamic>> getQualityMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? productId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return {
      'defect_rate': 2.5 + _random.nextDouble() * 2,
      'first_pass_yield': 94.2 + _random.nextDouble() * 5,
      'customer_complaints': 5 + _random.nextInt(10),
      'inspection_pass_rate': 97.8 + _random.nextDouble() * 2,
      'rework_percentage': 3.2 + _random.nextDouble() * 2,
      'quality_score': 88.5 + _random.nextDouble() * 10,
    };
  }

  @override
  Future<Map<String, dynamic>> getFinancialMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? costCenterId,
    String? departmentId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return {
      'revenue': 750000 + _random.nextInt(250000),
      'costs': 600000 + _random.nextInt(150000),
      'profit': 150000 + _random.nextInt(100000),
      'profit_margin': 15.5 + _random.nextDouble() * 10,
      'cost_per_unit': 12.75 + _random.nextDouble() * 3,
      'roi': 18.2 + _random.nextDouble() * 12,
    };
  }

  @override
  Future<Map<String, dynamic>> getInventoryMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? warehouseId,
    String? categoryId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return {
      'total_value': 2500000 + _random.nextInt(500000),
      'turnover_rate': 6.5 + _random.nextDouble() * 3,
      'stockout_incidents': 8 + _random.nextInt(12),
      'carrying_cost': 125000 + _random.nextInt(50000),
      'accuracy_rate': 98.5 + _random.nextDouble() * 1.5,
      'obsolete_inventory': 45000 + _random.nextInt(20000),
    };
  }

  @override
  Future<Map<String, dynamic>> getEfficiencyMetrics({
    required DateTime startDate,
    required DateTime endDate,
    String? departmentId,
    String? machineId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return {
      'overall_efficiency': 82.3 + _random.nextDouble() * 15,
      'machine_utilization': 75.8 + _random.nextDouble() * 20,
      'labor_efficiency': 88.2 + _random.nextDouble() * 10,
      'energy_efficiency': 79.5 + _random.nextDouble() * 15,
      'material_efficiency': 91.2 + _random.nextDouble() * 8,
      'time_efficiency': 85.7 + _random.nextDouble() * 12,
    };
  }

  @override
  Future<List<DataPoint>> getTrendData({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required String granularity,
    Map<String, dynamic>? filters,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final dataPoints = <DataPoint>[];
    final baseValue = _getBaseValueForMetric(metric);

    // Generate trend data based on granularity
    int intervalDays;
    switch (granularity) {
      case 'hour':
        intervalDays = 0;
        break;
      case 'day':
        intervalDays = 1;
        break;
      case 'week':
        intervalDays = 7;
        break;
      case 'month':
        intervalDays = 30;
        break;
      default:
        intervalDays = 1;
    }

    final totalDays = endDate.difference(startDate).inDays;
    final points = granularity == 'hour' ? 24 : (totalDays / intervalDays).ceil();

    for (int i = 0; i < points; i++) {
      final date = granularity == 'hour'
          ? startDate.add(Duration(hours: i))
          : startDate.add(Duration(days: i * intervalDays));

      final trend = 1 + (i / points) * 0.2; // Slight upward trend
      final noise = (_random.nextDouble() - 0.5) * 0.3;
      final value = baseValue * trend * (1 + noise);

      dataPoints.add(DataPoint(
        value: value,
        timestamp: date,
      ));
    }

    return dataPoints;
  }

  @override
  Future<Map<String, dynamic>> getComparativeAnalysis({
    required List<String> metrics,
    required DateTime currentPeriodStart,
    required DateTime currentPeriodEnd,
    required DateTime previousPeriodStart,
    required DateTime previousPeriodEnd,
    Map<String, dynamic>? filters,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final analysis = <String, dynamic>{};

    for (final metric in metrics) {
      final currentValue = _getBaseValueForMetric(metric) * (1 + _random.nextDouble() * 0.2);
      final previousValue = _getBaseValueForMetric(metric) * (1 + _random.nextDouble() * 0.2);
      final change = currentValue - previousValue;
      final changePercentage = (change / previousValue) * 100;

      analysis[metric] = {
        'current': currentValue,
        'previous': previousValue,
        'change': change,
        'change_percentage': changePercentage,
        'trend': changePercentage > 0 ? 'up' : changePercentage < 0 ? 'down' : 'stable',
      };
    }

    return analysis;
  }

  @override
  Future<Map<String, dynamic>> getForecast({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    required int forecastPeriods,
    Map<String, dynamic>? parameters,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1000));

    final baseValue = _getBaseValueForMetric(metric);
    final historicalData = <DataPoint>[];
    final forecastData = <DataPoint>[];

    // Generate historical data
    final days = endDate.difference(startDate).inDays;
    for (int i = 0; i < days; i++) {
      final date = startDate.add(Duration(days: i));
      final trend = 1 + (i / days) * 0.15;
      final value = baseValue * trend * (1 + (_random.nextDouble() - 0.5) * 0.2);

      historicalData.add(DataPoint(
        value: value,
        timestamp: date,
      ));
    }

    // Generate forecast data
    final lastValue = historicalData.last.value;
    for (int i = 1; i <= forecastPeriods; i++) {
      final date = endDate.add(Duration(days: i));
      final trend = 1 + (i / forecastPeriods) * 0.1;
      final value = lastValue * trend * (1 + (_random.nextDouble() - 0.5) * 0.15);

      forecastData.add(DataPoint(
        value: value,
        timestamp: date,
      ));
    }

    return {
      'metric': metric,
      'historical_data': historicalData,
      'forecast_data': forecastData,
      'confidence_interval': {
        'lower': forecastData.map((p) => p.value * 0.9).toList(),
        'upper': forecastData.map((p) => p.value * 1.1).toList(),
      },
      'accuracy_score': 85.5 + _random.nextDouble() * 10,
    };
  }

  @override
  Future<Map<String, dynamic>> getAnomalyDetection({
    required String metric,
    required DateTime startDate,
    required DateTime endDate,
    Map<String, dynamic>? parameters,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));

    final anomalies = <Map<String, dynamic>>[];
    final baseValue = _getBaseValueForMetric(metric);

    // Generate some random anomalies
    final anomalyCount = 2 + _random.nextInt(5);
    for (int i = 0; i < anomalyCount; i++) {
      final randomDay = _random.nextInt(endDate.difference(startDate).inDays);
      final anomalyDate = startDate.add(Duration(days: randomDay));
      final anomalyValue = baseValue * (0.3 + _random.nextDouble() * 1.4); // Significant deviation

      anomalies.add({
        'timestamp': anomalyDate.toIso8601String(),
        'value': anomalyValue,
        'expected_value': baseValue,
        'deviation': (anomalyValue - baseValue).abs(),
        'severity': anomalyValue > baseValue * 1.5 || anomalyValue < baseValue * 0.5 ? 'high' : 'medium',
        'type': anomalyValue > baseValue ? 'spike' : 'dip',
      });
    }

    return {
      'metric': metric,
      'anomalies': anomalies,
      'total_anomalies': anomalies.length,
      'anomaly_rate': (anomalies.length / endDate.difference(startDate).inDays) * 100,
      'detection_accuracy': 92.3 + _random.nextDouble() * 5,
    };
  }

  @override
  Future<Map<String, dynamic>> getRealTimeMetrics({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final realTimeData = <String, dynamic>{};

    for (final metric in metrics) {
      realTimeData[metric] = {
        'value': _getBaseValueForMetric(metric) * (1 + (_random.nextDouble() - 0.5) * 0.1),
        'timestamp': DateTime.now().toIso8601String(),
        'status': _random.nextBool() ? 'normal' : 'warning',
        'trend': _random.nextBool() ? 'up' : 'down',
      };
    }

    return realTimeData;
  }

  @override
  Stream<Map<String, dynamic>> getRealTimeUpdates({
    required List<String> metrics,
    Map<String, dynamic>? filters,
  }) {
    return Stream.periodic(const Duration(seconds: 5), (count) {
      final updates = <String, dynamic>{};

      for (final metric in metrics) {
        updates[metric] = {
          'value': _getBaseValueForMetric(metric) * (1 + (_random.nextDouble() - 0.5) * 0.1),
          'timestamp': DateTime.now().toIso8601String(),
          'status': _random.nextBool() ? 'normal' : 'warning',
          'trend': _random.nextBool() ? 'up' : 'down',
        };
      }

      return updates;
    });
  }

  @override
  Future<String> exportDashboard({
    required String dashboardId,
    required String format,
    Map<String, dynamic>? options,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1500));

    // Mock export URL
    return 'https://example.com/exports/dashboard_${dashboardId}_${DateTime.now().millisecondsSinceEpoch}.$format';
  }

  @override
  Future<String> exportReport({
    required String reportId,
    required String format,
    Map<String, dynamic>? options,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1200));

    // Mock export URL
    return 'https://example.com/exports/report_${reportId}_${DateTime.now().millisecondsSinceEpoch}.$format';
  }

  @override
  Future<String> exportData({
    required String dataType,
    required Map<String, dynamic> parameters,
    required String format,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1000));

    // Mock export URL
    return 'https://example.com/exports/data_${dataType}_${DateTime.now().millisecondsSinceEpoch}.$format';
  }

  @override
  Future<ApiListResponse<AnalyticsDashboard>> searchDashboards({
    required String query,
    DashboardFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredDashboards = _dashboards.where((dashboard) {
      final matchesQuery = dashboard.dashboardName.toLowerCase().contains(query.toLowerCase()) ||
          dashboard.description.toLowerCase().contains(query.toLowerCase());

      if (!matchesQuery) return false;

      if (filter?.type != null && dashboard.type != filter!.type) return false;
      if (filter?.ownerId != null && dashboard.ownerId != filter!.ownerId) return false;
      if (filter?.isPublic != null && dashboard.isPublic != filter!.isPublic) return false;

      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedDashboards = filteredDashboards.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedDashboards,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        totalPages: (filteredDashboards.length / (pagination?.perPage ?? 20)).ceil(),
        total: filteredDashboards.length,
        perPage: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredDashboards.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<ApiListResponse<AnalyticsReport>> searchReports({
    required String query,
    ReportFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredReports = _reports.where((report) {
      final matchesQuery = report.reportName.toLowerCase().contains(query.toLowerCase()) ||
          report.description.toLowerCase().contains(query.toLowerCase());

      if (!matchesQuery) return false;

      if (filter?.type != null && report.type != filter!.type) return false;
      if (filter?.category != null && report.category != filter!.category) return false;
      if (filter?.status != null && report.status != filter!.status) return false;

      return true;
    }).toList();

    final startIndex = (pagination?.page ?? 0) * (pagination?.perPage ?? 20);
    final paginatedReports = filteredReports.skip(startIndex).take(pagination?.perPage ?? 20).toList();

    return ApiListResponse(
      data: paginatedReports,
      pagination: Pagination(
        currentPage: pagination?.page ?? 0,
        totalPages: (filteredReports.length / (pagination?.perPage ?? 20)).ceil(),
        total: filteredReports.length,
        perPage: pagination?.perPage ?? 20,
        hasNextPage: startIndex + (pagination?.perPage ?? 20) < filteredReports.length,
        hasPreviousPage: (pagination?.page ?? 0) > 0,
      ), success: true,
    );
  }

  @override
  Future<List<String>> getAvailableMetrics({
    String? category,
    String? dataSource,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final allMetrics = [
      'production_volume',
      'quality_score',
      'efficiency_rate',
      'cost_per_unit',
      'revenue',
      'profit_margin',
      'defect_rate',
      'first_pass_yield',
      'machine_utilization',
      'labor_efficiency',
      'inventory_turnover',
      'customer_satisfaction',
      'on_time_delivery',
      'waste_percentage',
      'energy_consumption',
      'safety_incidents',
    ];

    if (category != null) {
      switch (category) {
        case 'production':
          return ['production_volume', 'efficiency_rate', 'machine_utilization', 'waste_percentage'];
        case 'quality':
          return ['quality_score', 'defect_rate', 'first_pass_yield'];
        case 'financial':
          return ['revenue', 'profit_margin', 'cost_per_unit'];
        case 'inventory':
          return ['inventory_turnover'];
        default:
          return allMetrics;
      }
    }

    return allMetrics;
  }

  @override
  Future<List<String>> getAvailableDimensions({
    String? category,
    String? dataSource,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));

    return [
      'department',
      'product',
      'customer',
      'supplier',
      'machine',
      'shift',
      'date',
      'week',
      'month',
      'quarter',
      'year',
      'cost_center',
      'employee',
      'location',
    ];
  }

  // Helper methods
  double _getBaseValueForMetric(String metric) {
    switch (metric) {
      case 'production_volume':
        return 15000;
      case 'quality_score':
        return 90;
      case 'efficiency_rate':
        return 85;
      case 'cost_per_unit':
        return 12.5;
      case 'revenue':
        return 750000;
      case 'profit_margin':
        return 18;
      case 'defect_rate':
        return 2.5;
      case 'first_pass_yield':
        return 95;
      case 'machine_utilization':
        return 80;
      case 'labor_efficiency':
        return 88;
      case 'inventory_turnover':
        return 6.5;
      default:
        return 100;
    }
  }

  String _getColorForSeries(int index) {
    final colors = [
      '#3B82F6', // Blue
      '#10B981', // Green
      '#F59E0B', // Yellow
      '#EF4444', // Red
      '#8B5CF6', // Purple
      '#06B6D4', // Cyan
      '#F97316', // Orange
      '#84CC16', // Lime
    ];
    return colors[index % colors.length];
  }

  dynamic _generateMockCellValue(String column, int rowIndex) {
    switch (column) {
      case 'department':
        final departments = ['Cutting', 'Sewing', 'Finishing', 'Quality', 'Packaging'];
        return departments[rowIndex % departments.length];
      case 'product':
        final products = ['T-Shirt', 'Jeans', 'Dress', 'Jacket', 'Shorts'];
        return products[rowIndex % products.length];
      case 'quantity':
        return 100 + _random.nextInt(500);
      case 'cost':
        return (10 + _random.nextDouble() * 20).toStringAsFixed(2);
      case 'date':
        return DateTime.now().subtract(Duration(days: rowIndex)).toIso8601String().split('T')[0];
      default:
        return 'Value $rowIndex';
    }
  }

  ReportData _generateMockReportData() {
    return ReportData(
      summary: {
        'total_records': 1250,
        'total_value': 875000,
        'average_value': 700,
        'growth_rate': 12.5,
      },
      sections: [
        ReportSection(
          sectionId: 'summary',
          title: 'Executive Summary',
          description: 'Key performance indicators and metrics',
          type: SectionType.summary,
          items: [
            ReportItem(
              itemId: 'revenue',
              label: 'Total Revenue',
              value: 875000,
              unit: 'USD',
              previousValue: 780000,
              changePercentage: 12.2,
              trend: TrendDirection.up,
            ),
          ],
        ),
      ],
      charts: [
        ChartData(
          chartId: 'chart_1',
          title: 'Performance Trend',
          chartType: ChartType.line,
          series: [
            DataSeries(
              seriesId: 'series_1',
              name: 'Performance',
              dataPoints: [
                DataPoint(label: 'Jan', value: 85),
                DataPoint(label: 'Feb', value: 88),
                DataPoint(label: 'Mar', value: 92),
              ],
              color: '#3B82F6',
              type: SeriesType.line,
            ),
          ],
          configuration: const ChartConfiguration(),
        ),
      ],
    );
  }
}

// Mock data generators
List<AnalyticsDashboard> _generateMockDashboards() {
  return [
    AnalyticsDashboard(
      id: 'dashboard_001',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      dashboardCode: 'EXEC-DASH-001',
      dashboardName: 'Executive Dashboard',
      description: 'High-level overview of key business metrics',
      type: DashboardType.executive,
      status: CommonStatus.active,
      ownerId: 'user_001',
      ownerName: 'John Smith',
      layout: const DashboardLayout(
        columns: 4,
        rows: 3,
        spacing: 16,
        padding: const CustomEdgeInsets.all(20),
      ),
      widgets: [
        DashboardWidget(
          widgetId: 'widget_001',
          widgetType: 'kpi_card',
          title: 'Total Revenue',
          position: const WidgetPosition(x: 0, y: 0),
          size: const WidgetSize(width: 1, height: 1),
          configuration: const WidgetConfiguration(),
          refreshSettings: const WidgetRefreshSettings(autoRefresh: true),
        ),
      ],
      settings: const DashboardSettings(),
      isDefault: true,
      isPublic: false,
    ),
    AnalyticsDashboard(
      id: 'dashboard_002',
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      dashboardCode: 'PROD-DASH-001',
      dashboardName: 'Production Dashboard',
      description: 'Real-time production metrics and KPIs',
      type: DashboardType.production,
      status: CommonStatus.active,
      ownerId: 'user_002',
      ownerName: 'Sarah Johnson',
      layout: const DashboardLayout(
        columns: 3,
        rows: 4,
        spacing: 12,
        padding: const CustomEdgeInsets.all(16),
      ),
      widgets: [],
      settings: const DashboardSettings(),
      isDefault: false,
      isPublic: true,
    ),
  ];
}

List<AnalyticsReport> _generateMockReports() {
  return [
    AnalyticsReport(
      id: 'report_001',
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      reportCode: 'RPT-PERF-001',
      reportName: 'Monthly Performance Report',
      description: 'Comprehensive performance analysis for the month',
      type: AnalyticsReportType.performance,
      category: ReportCategory.scheduled,
      status: ReportStatus.completed,
      generatedBy: 'user_001',
      generatedAt: DateTime.now().subtract(const Duration(days: 7)),
      parameters: ReportParameters(
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now(),
        metrics: ['production_volume', 'quality_score', 'efficiency_rate'],
      ),
      data: const ReportData(),
      settings: const ReportSettings(),
      tags: const ['monthly', 'performance', 'kpi'],
      isScheduled: true,
      scheduleConfig: const ScheduleConfiguration(
        frequency: ScheduleFrequency.monthly,
      ),
    ),
  ];
}
