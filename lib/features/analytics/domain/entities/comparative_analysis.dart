import 'package:equatable/equatable.dart';

/// Represents the result of a comparative analysis between two time periods
class ComparativeAnalysis extends Equatable {
  /// The metric being compared
  final String metric;
  
  /// Current period value
  final double currentValue;
  
  /// Previous period value
  final double previousValue;
  
  /// The difference between current and previous values
  final double difference;
  
  /// The percentage change between periods
  final double percentageChange;
  
  /// Whether the change is positive (improvement)
  final bool isPositive;
  
  /// Additional metadata about the comparison
  final Map<String, dynamic> metadata;

  const ComparativeAnalysis({
    required this.metric,
    required this.currentValue,
    required this.previousValue,
    required this.difference,
    required this.percentageChange,
    required this.isPositive,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [
        metric,
        currentValue,
        previousValue,
        difference,
        percentageChange,
        isPositive,
        metadata,
      ];

  /// Creates a [ComparativeAnalysis] from JSON data
  factory ComparativeAnalysis.fromJson(Map<String, dynamic> json) {
    return ComparativeAnalysis(
      metric: json['metric'] as String,
      currentValue: (json['current_value'] as num).toDouble(),
      previousValue: (json['previous_value'] as num).toDouble(),
      difference: (json['difference'] as num).toDouble(),
      percentageChange: (json['percentage_change'] as num).toDouble(),
      isPositive: json['is_positive'] as bool,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  /// Converts the [ComparativeAnalysis] to JSON
  Map<String, dynamic> toJson() {
    return {
      'metric': metric,
      'current_value': currentValue,
      'previous_value': previousValue,
      'difference': difference,
      'percentage_change': percentageChange,
      'is_positive': isPositive,
      'metadata': metadata,
    };
  }
}
