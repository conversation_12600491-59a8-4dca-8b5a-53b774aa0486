import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/analytics_entities.dart';
import '../repositories/analytics_repository.dart';

/// Get dashboards use case
@injectable
class GetDashboardsUseCase implements UseCase<ApiListResponse<AnalyticsDashboard>, GetDashboardsParams> {
  final AnalyticsRepository _repository;

  const GetDashboardsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsDashboard>>> call(GetDashboardsParams params) async {
    return await _repository.getDashboards(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get dashboard by ID use case
@injectable
class GetDashboardByIdUseCase implements UseCase<ApiResponse<AnalyticsDashboard>, IdParams> {
  final AnalyticsRepository _repository;

  const GetDashboardByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> call(IdParams params) async {
    return await _repository.getDashboardById(params.id);
  }
}

/// Create dashboard use case
@injectable
class CreateDashboardUseCase implements UseCase<ApiResponse<AnalyticsDashboard>, CreateDashboardRequest> {
  final AnalyticsRepository _repository;

  const CreateDashboardUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> call(CreateDashboardRequest params) async {
    return await _repository.createDashboard(params);
  }
}

/// Update dashboard use case
@injectable
class UpdateDashboardUseCase implements UseCase<ApiResponse<AnalyticsDashboard>, UpdateDashboardRequest> {
  final AnalyticsRepository _repository;

  const UpdateDashboardUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> call(UpdateDashboardRequest params) async {
    return await _repository.updateDashboard(params);
  }
}

/// Delete dashboard use case
@injectable
class DeleteDashboardUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final AnalyticsRepository _repository;

  const DeleteDashboardUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.deleteDashboard(params.id);
  }
}

/// Clone dashboard use case
@injectable
class CloneDashboardUseCase implements UseCase<ApiResponse<AnalyticsDashboard>, CloneDashboardParams> {
  final AnalyticsRepository _repository;

  const CloneDashboardUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> call(CloneDashboardParams params) async {
    return await _repository.cloneDashboard(params.dashboardId, params.newName);
  }
}

/// Share dashboard use case
@injectable
class ShareDashboardUseCase implements UseCase<ApiResponse<AnalyticsDashboard>, ShareDashboardRequest> {
  final AnalyticsRepository _repository;

  const ShareDashboardUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> call(ShareDashboardRequest params) async {
    return await _repository.shareDashboard(params);
  }
}

/// Add widget use case
@injectable
class AddWidgetUseCase implements UseCase<ApiResponse<DashboardWidget>, AddWidgetRequest> {
  final AnalyticsRepository _repository;

  const AddWidgetUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<DashboardWidget>>> call(AddWidgetRequest params) async {
    return await _repository.addWidget(params);
  }
}

/// Update widget use case
@injectable
class UpdateWidgetUseCase implements UseCase<ApiResponse<DashboardWidget>, UpdateWidgetRequest> {
  final AnalyticsRepository _repository;

  const UpdateWidgetUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<DashboardWidget>>> call(UpdateWidgetRequest params) async {
    return await _repository.updateWidget(params);
  }
}

/// Remove widget use case
@injectable
class RemoveWidgetUseCase implements UseCase<ApiVoidResponse, RemoveWidgetParams> {
  final AnalyticsRepository _repository;

  const RemoveWidgetUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(RemoveWidgetParams params) async {
    return await _repository.removeWidget(params.dashboardId, params.widgetId);
  }
}

/// Reorder widgets use case
@injectable
class ReorderWidgetsUseCase implements UseCase<ApiResponse<AnalyticsDashboard>, ReorderWidgetsRequest> {
  final AnalyticsRepository _repository;

  const ReorderWidgetsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsDashboard>>> call(ReorderWidgetsRequest params) async {
    return await _repository.reorderWidgets(params);
  }
}

/// Get reports use case
@injectable
class GetReportsUseCase implements UseCase<ApiListResponse<AnalyticsReport>, GetReportsParams> {
  final AnalyticsRepository _repository;

  const GetReportsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsReport>>> call(GetReportsParams params) async {
    return await _repository.getReports(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get report by ID use case
@injectable
class GetReportByIdUseCase implements UseCase<ApiResponse<AnalyticsReport>, IdParams> {
  final AnalyticsRepository _repository;

  const GetReportByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsReport>>> call(IdParams params) async {
    return await _repository.getReportById(params.id);
  }
}

/// Generate report use case
@injectable
class GenerateReportUseCase implements UseCase<ApiResponse<AnalyticsReport>, GenerateReportRequest> {
  final AnalyticsRepository _repository;

  const GenerateReportUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsReport>>> call(GenerateReportRequest params) async {
    return await _repository.generateReport(params);
  }
}

/// Schedule report use case
@injectable
class ScheduleReportUseCase implements UseCase<ApiResponse<AnalyticsReport>, ScheduleReportRequest> {
  final AnalyticsRepository _repository;

  const ScheduleReportUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AnalyticsReport>>> call(ScheduleReportRequest params) async {
    return await _repository.scheduleReport(params);
  }
}

/// Cancel report use case
@injectable
class CancelReportUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final AnalyticsRepository _repository;

  const CancelReportUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.cancelReport(params.id);
  }
}

/// Delete report use case
@injectable
class DeleteReportUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final AnalyticsRepository _repository;

  const DeleteReportUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.deleteReport(params.id);
  }
}

/// Get KPI data use case
@injectable
class GetKpiDataUseCase implements UseCase<Map<String, dynamic>, GetKpiDataParams> {
  final AnalyticsRepository _repository;

  const GetKpiDataUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetKpiDataParams params) async {
    return await _repository.getKpiData(
      metrics: params.metrics,
      startDate: params.startDate,
      endDate: params.endDate,
      filters: params.filters,
    );
  }
}

/// Get chart data use case
@injectable
class GetChartDataUseCase implements UseCase<ChartData, GetChartDataParams> {
  final AnalyticsRepository _repository;

  const GetChartDataUseCase(this._repository);

  @override
  Future<Either<Failure, ChartData>> call(GetChartDataParams params) async {
    return await _repository.getChartData(
      chartType: params.chartType,
      metrics: params.metrics,
      dimensions: params.dimensions,
      startDate: params.startDate,
      endDate: params.endDate,
      filters: params.filters,
    );
  }
}

/// Get table data use case
@injectable
class GetTableDataUseCase implements UseCase<TableData, GetTableDataParams> {
  final AnalyticsRepository _repository;

  const GetTableDataUseCase(this._repository);

  @override
  Future<Either<Failure, TableData>> call(GetTableDataParams params) async {
    return await _repository.getTableData(
      columns: params.columns,
      startDate: params.startDate,
      endDate: params.endDate,
      filters: params.filters,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
      pagination: params.pagination,
    );
  }
}

/// Get production metrics use case
@injectable
class GetProductionMetricsUseCase implements UseCase<Map<String, dynamic>, GetProductionMetricsParams> {
  final AnalyticsRepository _repository;

  const GetProductionMetricsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetProductionMetricsParams params) async {
    return await _repository.getProductionMetrics(
      startDate: params.startDate,
      endDate: params.endDate,
      departmentId: params.departmentId,
      productId: params.productId,
    );
  }
}

/// Get quality metrics use case
@injectable
class GetQualityMetricsUseCase implements UseCase<Map<String, dynamic>, GetQualityMetricsParams> {
  final AnalyticsRepository _repository;

  const GetQualityMetricsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetQualityMetricsParams params) async {
    return await _repository.getQualityMetrics(
      startDate: params.startDate,
      endDate: params.endDate,
      departmentId: params.departmentId,
      productId: params.productId,
    );
  }
}

/// Get financial metrics use case
@injectable
class GetFinancialMetricsUseCase implements UseCase<Map<String, dynamic>, GetFinancialMetricsParams> {
  final AnalyticsRepository _repository;

  const GetFinancialMetricsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetFinancialMetricsParams params) async {
    return await _repository.getFinancialMetrics(
      startDate: params.startDate,
      endDate: params.endDate,
      costCenterId: params.costCenterId,
      departmentId: params.departmentId,
    );
  }
}

/// Get inventory metrics use case
@injectable
class GetInventoryMetricsUseCase implements UseCase<Map<String, dynamic>, GetInventoryMetricsParams> {
  final AnalyticsRepository _repository;

  const GetInventoryMetricsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetInventoryMetricsParams params) async {
    return await _repository.getInventoryMetrics(
      startDate: params.startDate,
      endDate: params.endDate,
      warehouseId: params.warehouseId,
      categoryId: params.categoryId,
    );
  }
}

/// Get efficiency metrics use case
@injectable
class GetEfficiencyMetricsUseCase implements UseCase<Map<String, dynamic>, GetEfficiencyMetricsParams> {
  final AnalyticsRepository _repository;

  const GetEfficiencyMetricsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetEfficiencyMetricsParams params) async {
    return await _repository.getEfficiencyMetrics(
      startDate: params.startDate,
      endDate: params.endDate,
      departmentId: params.departmentId,
      machineId: params.machineId,
    );
  }
}

/// Get trend data use case
@injectable
class GetTrendDataUseCase implements UseCase<List<DataPoint>, GetTrendDataParams> {
  final AnalyticsRepository _repository;

  const GetTrendDataUseCase(this._repository);

  @override
  Future<Either<Failure, List<DataPoint>>> call(GetTrendDataParams params) async {
    return await _repository.getTrendData(
      metric: params.metric,
      startDate: params.startDate,
      endDate: params.endDate,
      granularity: params.granularity,
      filters: params.filters,
    );
  }
}

/// Get comparative analysis use case
@injectable
class GetComparativeAnalysisUseCase implements UseCase<Map<String, dynamic>, GetComparativeAnalysisParams> {
  final AnalyticsRepository _repository;

  const GetComparativeAnalysisUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetComparativeAnalysisParams params) async {
    return await _repository.getComparativeAnalysis(
      metrics: params.metrics,
      currentPeriodStart: params.currentPeriodStart,
      currentPeriodEnd: params.currentPeriodEnd,
      previousPeriodStart: params.previousPeriodStart,
      previousPeriodEnd: params.previousPeriodEnd,
      filters: params.filters,
    );
  }
}

/// Get forecast use case
@injectable
class GetForecastUseCase implements UseCase<Map<String, dynamic>, GetForecastParams> {
  final AnalyticsRepository _repository;

  const GetForecastUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetForecastParams params) async {
    return await _repository.getForecast(
      metric: params.metric,
      startDate: params.startDate,
      endDate: params.endDate,
      forecastPeriods: params.forecastPeriods,
      parameters: params.parameters,
    );
  }
}

/// Get anomaly detection use case
@injectable
class GetAnomalyDetectionUseCase implements UseCase<Map<String, dynamic>, GetAnomalyDetectionParams> {
  final AnalyticsRepository _repository;

  const GetAnomalyDetectionUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetAnomalyDetectionParams params) async {
    return await _repository.getAnomalyDetection(
      metric: params.metric,
      startDate: params.startDate,
      endDate: params.endDate,
      parameters: params.parameters,
    );
  }
}

/// Get real-time metrics use case
@injectable
class GetRealTimeMetricsUseCase implements UseCase<Map<String, dynamic>, GetRealTimeMetricsParams> {
  final AnalyticsRepository _repository;

  const GetRealTimeMetricsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetRealTimeMetricsParams params) async {
    return await _repository.getRealTimeMetrics(
      metrics: params.metrics,
      filters: params.filters,
    );
  }
}

/// Export dashboard use case
@injectable
class ExportDashboardUseCase implements UseCase<String, ExportDashboardParams> {
  final AnalyticsRepository _repository;

  const ExportDashboardUseCase(this._repository);

  @override
  Future<Either<Failure, String>> call(ExportDashboardParams params) async {
    return await _repository.exportDashboard(
      dashboardId: params.dashboardId,
      format: params.format,
      options: params.options,
    );
  }
}

/// Export report use case
@injectable
class ExportReportUseCase implements UseCase<String, ExportReportParams> {
  final AnalyticsRepository _repository;

  const ExportReportUseCase(this._repository);

  @override
  Future<Either<Failure, String>> call(ExportReportParams params) async {
    return await _repository.exportReport(
      reportId: params.reportId,
      format: params.format,
      options: params.options,
    );
  }
}

/// Search dashboards use case
@injectable
class SearchDashboardsUseCase implements UseCase<ApiListResponse<AnalyticsDashboard>, SearchDashboardsParams> {
  final AnalyticsRepository _repository;

  const SearchDashboardsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsDashboard>>> call(SearchDashboardsParams params) async {
    return await _repository.searchDashboards(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Search reports use case
@injectable
class SearchReportsUseCase implements UseCase<ApiListResponse<AnalyticsReport>, SearchReportsParams> {
  final AnalyticsRepository _repository;

  const SearchReportsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<AnalyticsReport>>> call(SearchReportsParams params) async {
    return await _repository.searchReports(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get available metrics use case
@injectable
class GetAvailableMetricsUseCase implements UseCase<List<String>, GetAvailableMetricsParams> {
  final AnalyticsRepository _repository;

  const GetAvailableMetricsUseCase(this._repository);

  @override
  Future<Either<Failure, List<String>>> call(GetAvailableMetricsParams params) async {
    return await _repository.getAvailableMetrics(
      category: params.category,
      dataSource: params.dataSource,
    );
  }
}

/// Get available dimensions use case
@injectable
class GetAvailableDimensionsUseCase implements UseCase<List<String>, GetAvailableDimensionsParams> {
  final AnalyticsRepository _repository;

  const GetAvailableDimensionsUseCase(this._repository);

  @override
  Future<Either<Failure, List<String>>> call(GetAvailableDimensionsParams params) async {
    return await _repository.getAvailableDimensions(
      category: params.category,
      dataSource: params.dataSource,
    );
  }
}

// Parameter classes

/// Get dashboards parameters
class GetDashboardsParams {
  final DashboardFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetDashboardsParams({
    this.filter,
    this.pagination,
  });
}

/// Clone dashboard parameters
class CloneDashboardParams {
  final String dashboardId;
  final String newName;

  const CloneDashboardParams({
    required this.dashboardId,
    required this.newName,
  });
}

/// Remove widget parameters
class RemoveWidgetParams {
  final String dashboardId;
  final String widgetId;

  const RemoveWidgetParams({
    required this.dashboardId,
    required this.widgetId,
  });
}

/// Get reports parameters
class GetReportsParams {
  final ReportFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetReportsParams({
    this.filter,
    this.pagination,
  });
}

/// Get KPI data parameters
class GetKpiDataParams {
  final List<String> metrics;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? filters;

  const GetKpiDataParams({
    required this.metrics,
    required this.startDate,
    required this.endDate,
    this.filters,
  });
}

/// Get chart data parameters
class GetChartDataParams {
  final String chartType;
  final List<String> metrics;
  final List<String> dimensions;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? filters;

  const GetChartDataParams({
    required this.chartType,
    required this.metrics,
    required this.dimensions,
    required this.startDate,
    required this.endDate,
    this.filters,
  });
}

/// Get table data parameters
class GetTableDataParams {
  final List<String> columns;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? filters;
  final String? sortBy;
  final SortOrder? sortOrder;
  final PaginationParams? pagination;

  const GetTableDataParams({
    required this.columns,
    required this.startDate,
    required this.endDate,
    this.filters,
    this.sortBy,
    this.sortOrder,
    this.pagination,
  });
}

/// Get production metrics parameters
class GetProductionMetricsParams {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  final String? productId;

  const GetProductionMetricsParams({
    required this.startDate,
    required this.endDate,
    this.departmentId,
    this.productId,
  });
}

/// Get quality metrics parameters
class GetQualityMetricsParams {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  final String? productId;

  const GetQualityMetricsParams({
    required this.startDate,
    required this.endDate,
    this.departmentId,
    this.productId,
  });
}

/// Get financial metrics parameters
class GetFinancialMetricsParams {
  final DateTime startDate;
  final DateTime endDate;
  final String? costCenterId;
  final String? departmentId;

  const GetFinancialMetricsParams({
    required this.startDate,
    required this.endDate,
    this.costCenterId,
    this.departmentId,
  });
}

/// Get inventory metrics parameters
class GetInventoryMetricsParams {
  final DateTime startDate;
  final DateTime endDate;
  final String? warehouseId;
  final String? categoryId;

  const GetInventoryMetricsParams({
    required this.startDate,
    required this.endDate,
    this.warehouseId,
    this.categoryId,
  });
}

/// Get efficiency metrics parameters
class GetEfficiencyMetricsParams {
  final DateTime startDate;
  final DateTime endDate;
  final String? departmentId;
  final String? machineId;

  const GetEfficiencyMetricsParams({
    required this.startDate,
    required this.endDate,
    this.departmentId,
    this.machineId,
  });
}

/// Get trend data parameters
class GetTrendDataParams {
  final String metric;
  final DateTime startDate;
  final DateTime endDate;
  final String granularity;
  final Map<String, dynamic>? filters;

  const GetTrendDataParams({
    required this.metric,
    required this.startDate,
    required this.endDate,
    required this.granularity,
    this.filters,
  });
}

/// Get comparative analysis parameters
class GetComparativeAnalysisParams {
  final List<String> metrics;
  final DateTime currentPeriodStart;
  final DateTime currentPeriodEnd;
  final DateTime previousPeriodStart;
  final DateTime previousPeriodEnd;
  final Map<String, dynamic>? filters;

  const GetComparativeAnalysisParams({
    required this.metrics,
    required this.currentPeriodStart,
    required this.currentPeriodEnd,
    required this.previousPeriodStart,
    required this.previousPeriodEnd,
    this.filters,
  });
}

/// Get forecast parameters
class GetForecastParams {
  final String metric;
  final DateTime startDate;
  final DateTime endDate;
  final int forecastPeriods;
  final Map<String, dynamic>? parameters;

  const GetForecastParams({
    required this.metric,
    required this.startDate,
    required this.endDate,
    required this.forecastPeriods,
    this.parameters,
  });
}

/// Get anomaly detection parameters
class GetAnomalyDetectionParams {
  final String metric;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic>? parameters;

  const GetAnomalyDetectionParams({
    required this.metric,
    required this.startDate,
    required this.endDate,
    this.parameters,
  });
}

/// Get real-time metrics parameters
class GetRealTimeMetricsParams {
  final List<String> metrics;
  final Map<String, dynamic>? filters;

  const GetRealTimeMetricsParams({
    required this.metrics,
    this.filters,
  });
}

/// Export dashboard parameters
class ExportDashboardParams {
  final String dashboardId;
  final String format;
  final Map<String, dynamic>? options;

  const ExportDashboardParams({
    required this.dashboardId,
    required this.format,
    this.options,
  });
}

/// Export report parameters
class ExportReportParams {
  final String reportId;
  final String format;
  final Map<String, dynamic>? options;

  const ExportReportParams({
    required this.reportId,
    required this.format,
    this.options,
  });
}

/// Search dashboards parameters
class SearchDashboardsParams {
  final String query;
  final DashboardFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchDashboardsParams({
    required this.query,
    this.filter,
    this.pagination,
  });
}

/// Search reports parameters
class SearchReportsParams {
  final String query;
  final ReportFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchReportsParams({
    required this.query,
    this.filter,
    this.pagination,
  });
}

/// Get available metrics parameters
class GetAvailableMetricsParams {
  final String? category;
  final String? dataSource;

  const GetAvailableMetricsParams({
    this.category,
    this.dataSource,
  });
}

/// Get available dimensions parameters
class GetAvailableDimensionsParams {
  final String? category;
  final String? dataSource;

  const GetAvailableDimensionsParams({
    this.category,
    this.dataSource,
  });
}
