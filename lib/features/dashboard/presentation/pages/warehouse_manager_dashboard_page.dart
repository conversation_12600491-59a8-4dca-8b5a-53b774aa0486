import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Warehouse Manager Dashboard Page - Warehouse operations and shipping
class WarehouseManagerDashboardPage extends StatelessWidget {
  const WarehouseManagerDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Warehouse Manager Dashboard'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildWarehouseManagerDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildWarehouseManagerDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildWarehouseOverview(),
          const SizedBox(height: 24),
          _buildWarehouseActions(context),
          const SizedBox(height: 24),
          _buildInventoryStatus(),
          const SizedBox(height: 24),
          _buildShippingQueue(),
          const SizedBox(height: 24),
          _buildStorageUtilization(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.brown,
            Colors.brown.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Warehouse Manager'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Warehouse operations and shipping',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.warehouse,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Warehouse Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Total Items',
              value: '2,450',
              icon: Icons.inventory,
              color: Colors.blue,
              trend: '+125',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Pending Shipments',
              value: '18',
              icon: Icons.local_shipping,
              color: Colors.orange,
            ),
            DashboardMetricCard(
              title: 'Storage Utilization',
              value: '78%',
              icon: Icons.storage,
              color: Colors.green,
              trend: '+5%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Orders Shipped',
              value: '45',
              icon: Icons.check_circle,
              color: Colors.purple,
              trend: '+12',
              isPositiveTrend: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWarehouseActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Warehouse Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            DashboardCard(
              title: 'Receive Goods',
              subtitle: 'Process incoming items',
              icon: Icons.input,
              color: Colors.blue,
              onTap: () {
                // Navigate to receive goods
              },
            ),
            DashboardCard(
              title: 'Ship Orders',
              subtitle: 'Process outgoing orders',
              icon: Icons.local_shipping,
              color: Colors.green,
              onTap: () {
                // Navigate to ship orders
              },
            ),
            DashboardCard(
              title: 'Inventory Check',
              subtitle: 'Verify stock levels',
              icon: Icons.inventory_2,
              color: Colors.orange,
              onTap: () {
                // Navigate to inventory check
              },
            ),
            DashboardCard(
              title: 'Storage Management',
              subtitle: 'Organize warehouse space',
              icon: Icons.storage,
              color: Colors.purple,
              onTap: () {
                // Navigate to storage management
              },
            ),
            DashboardCard(
              title: 'Staff Management',
              subtitle: 'Manage warehouse staff',
              icon: Icons.people,
              color: Colors.teal,
              onTap: () {
                // Navigate to staff management
              },
            ),
            DashboardCard(
              title: 'Reports',
              subtitle: 'View warehouse reports',
              icon: Icons.analytics,
              color: Colors.indigo,
              onTap: () {
                // Navigate to reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInventoryStatus() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.inventory,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Inventory Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Healthy',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInventoryItem('Finished Goods', '1,850', 'High Stock', Colors.green),
            _buildInventoryItem('Raw Materials', '450', 'Medium Stock', Colors.blue),
            _buildInventoryItem('Work in Progress', '150', 'Low Stock', Colors.orange),
            _buildInventoryItem('Packaging Materials', '320', 'High Stock', Colors.green),
            _buildInventoryItem('Accessories', '180', 'Critical Low', Colors.red),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryItem(String category, String quantity, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              category,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              quantity,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShippingQueue() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.local_shipping,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Shipping Queue',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '18 Pending',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildShippingItem(
              'Order #1234',
              'Express Delivery - 100 shirts',
              'High',
              'Ready to Ship',
              Colors.green,
            ),
            _buildShippingItem(
              'Order #1235',
              'Standard Delivery - 75 trousers',
              'Medium',
              'Packing',
              Colors.blue,
            ),
            _buildShippingItem(
              'Order #1236',
              'Express Delivery - 50 dresses',
              'High',
              'Awaiting Pickup',
              Colors.orange,
            ),
            _buildShippingItem(
              'Order #1237',
              'Standard Delivery - 25 jackets',
              'Low',
              'Processing',
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShippingItem(String order, String description, String priority, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getPriorityColor(priority).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              priority,
              style: TextStyle(
                color: _getPriorityColor(priority),
                fontWeight: FontWeight.w500,
                fontSize: 10,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget _buildStorageUtilization() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.storage,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Storage Utilization',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '78% Used',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStorageItem('Zone A - Finished Goods', '85%', 0.85, Colors.orange),
            const SizedBox(height: 8),
            _buildStorageItem('Zone B - Raw Materials', '72%', 0.72, Colors.green),
            const SizedBox(height: 8),
            _buildStorageItem('Zone C - Work in Progress', '65%', 0.65, Colors.blue),
            const SizedBox(height: 8),
            _buildStorageItem('Zone D - Packaging', '90%', 0.90, Colors.red),
          ],
        ),
      ),
    );
  }

  Widget _buildStorageItem(String zone, String percentage, double progress, Color color) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            zone,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 2,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          percentage,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
