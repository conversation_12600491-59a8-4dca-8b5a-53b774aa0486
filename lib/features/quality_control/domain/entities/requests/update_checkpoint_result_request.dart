import 'package:equatable/equatable.dart';

class UpdateCheckpointResultRequest extends Equatable {
  final String status; // e.g., 'passed', 'failed', 'pending'
  final String? notes;
  final List<String>? attachmentUrls;
  final Map<String, dynamic>? customFields;

  const UpdateCheckpointResultRequest({
    required this.status,
    this.notes,
    this.attachmentUrls,
    this.customFields,
  });

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      if (notes != null) 'notes': notes,
      if (attachmentUrls != null) 'attachmentUrls': attachmentUrls,
      if (customFields != null) ...?customFields,
    };
  }

  @override
  List<Object?> get props => [
        status,
        notes,
        attachmentUrls,
        customFields,
      ];
}
