import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/munda_record.dart';
import '../../domain/entities/munda_task.dart';
import '../../domain/entities/munda_requests.dart';

/// Base class for all Munda states
abstract class MundaState extends Equatable {
  const MundaState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class MundaInitial extends MundaState {
  const MundaInitial();
}

/// Loading state
class MundaLoading extends MundaState {
  final String? message;

  const MundaLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// Records loaded state
class RecordsLoaded extends MundaState {
  final PaginatedResult<MundaRecord> records;
  final MundaRecordSummary? summary;
  final MundaRecordFilterCriteria? currentFilter;
  final bool isLoadingMore;
  final bool hasReachedMax;

  const RecordsLoaded({
    required this.records,
    this.summary,
    this.currentFilter,
    this.isLoadingMore = false,
    this.hasReachedMax = false,
  });

  @override
  List<Object?> get props => [
        records,
        summary,
        currentFilter,
        isLoadingMore,
        hasReachedMax,
      ];

  /// Copy with new values
  RecordsLoaded copyWith({
    PaginatedResult<MundaRecord>? records,
    MundaRecordSummary? summary,
    MundaRecordFilterCriteria? currentFilter,
    bool? isLoadingMore,
    bool? hasReachedMax,
  }) {
    return RecordsLoaded(
      records: records ?? this.records,
      summary: summary ?? this.summary,
      currentFilter: currentFilter ?? this.currentFilter,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }

  /// Add more records to the current list
  RecordsLoaded addMoreRecords(PaginatedResult<MundaRecord> newRecords) {
    final allRecords = [...records.data, ...newRecords.data];
    final updatedPagination = newRecords.pagination;
    
    return copyWith(
      records: PaginatedResult<MundaRecord>(
        data: allRecords,
        pagination: updatedPagination,
        meta: newRecords.meta,
      ),
      isLoadingMore: false,
      hasReachedMax: !updatedPagination.hasNextPage,
    );
  }

  /// Update a specific record in the list
  RecordsLoaded updateRecord(MundaRecord updatedRecord) {
    final updatedRecords = records.data.map((record) {
      return record.id == updatedRecord.id ? updatedRecord : record;
    }).toList();

    return copyWith(
      records: records.copyWith(data: updatedRecords),
    );
  }

  /// Remove a record from the list
  RecordsLoaded removeRecord(String recordId) {
    final updatedRecords = records.data
        .where((record) => record.id != recordId)
        .toList();

    return copyWith(
      records: records.copyWith(data: updatedRecords),
    );
  }

  /// Add a new record to the beginning of the list
  RecordsLoaded addRecord(MundaRecord newRecord) {
    final updatedRecords = [newRecord, ...records.data];

    return copyWith(
      records: records.copyWith(data: updatedRecords),
    );
  }
}

/// Tasks loaded state
class TasksLoaded extends MundaState {
  final PaginatedResult<MundaTask> tasks;
  final List<MundaTask> userTasks;
  final MundaTaskFilterCriteria? currentFilter;
  final bool isLoadingMore;
  final bool hasReachedMax;

  const TasksLoaded({
    required this.tasks,
    this.userTasks = const [],
    this.currentFilter,
    this.isLoadingMore = false,
    this.hasReachedMax = false,
  });

  @override
  List<Object?> get props => [
        tasks,
        userTasks,
        currentFilter,
        isLoadingMore,
        hasReachedMax,
      ];

  /// Copy with new values
  TasksLoaded copyWith({
    PaginatedResult<MundaTask>? tasks,
    List<MundaTask>? userTasks,
    MundaTaskFilterCriteria? currentFilter,
    bool? isLoadingMore,
    bool? hasReachedMax,
  }) {
    return TasksLoaded(
      tasks: tasks ?? this.tasks,
      userTasks: userTasks ?? this.userTasks,
      currentFilter: currentFilter ?? this.currentFilter,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }

  /// Update a specific task in the list
  TasksLoaded updateTask(MundaTask updatedTask) {
    final updatedTasks = tasks.data.map((task) {
      return task.id == updatedTask.id ? updatedTask : task;
    }).toList();

    final updatedUserTasks = userTasks.map((task) {
      return task.id == updatedTask.id ? updatedTask : task;
    }).toList();

    return copyWith(
      tasks: tasks.copyWith(data: updatedTasks),
      userTasks: updatedUserTasks,
    );
  }

  /// Remove a task from the list
  TasksLoaded removeTask(String taskId) {
    final updatedTasks = tasks.data
        .where((task) => task.id != taskId)
        .toList();

    final updatedUserTasks = userTasks
        .where((task) => task.id != taskId)
        .toList();

    return copyWith(
      tasks: tasks.copyWith(data: updatedTasks),
      userTasks: updatedUserTasks,
    );
  }

  /// Add a new task to the beginning of the list
  TasksLoaded addTask(MundaTask newTask) {
    final updatedTasks = [newTask, ...tasks.data];

    return copyWith(
      tasks: tasks.copyWith(data: updatedTasks),
    );
  }
}

/// Combined state with both records and tasks
class MundaDataLoaded extends MundaState {
  final PaginatedResult<MundaRecord> records;
  final PaginatedResult<MundaTask> tasks;
  final List<MundaTask> userTasks;
  final MundaRecordSummary? recordsSummary;
  final MundaRecordFilterCriteria? recordFilter;
  final MundaTaskFilterCriteria? taskFilter;
  final bool isLoadingRecords;
  final bool isLoadingTasks;
  final bool isLoadingMore;

  const MundaDataLoaded({
    required this.records,
    required this.tasks,
    this.userTasks = const [],
    this.recordsSummary,
    this.recordFilter,
    this.taskFilter,
    this.isLoadingRecords = false,
    this.isLoadingTasks = false,
    this.isLoadingMore = false,
  });

  @override
  List<Object?> get props => [
        records,
        tasks,
        userTasks,
        recordsSummary,
        recordFilter,
        taskFilter,
        isLoadingRecords,
        isLoadingTasks,
        isLoadingMore,
      ];

  /// Copy with new values
  MundaDataLoaded copyWith({
    PaginatedResult<MundaRecord>? records,
    PaginatedResult<MundaTask>? tasks,
    List<MundaTask>? userTasks,
    MundaRecordSummary? recordsSummary,
    MundaRecordFilterCriteria? recordFilter,
    MundaTaskFilterCriteria? taskFilter,
    bool? isLoadingRecords,
    bool? isLoadingTasks,
    bool? isLoadingMore,
  }) {
    return MundaDataLoaded(
      records: records ?? this.records,
      tasks: tasks ?? this.tasks,
      userTasks: userTasks ?? this.userTasks,
      recordsSummary: recordsSummary ?? this.recordsSummary,
      recordFilter: recordFilter ?? this.recordFilter,
      taskFilter: taskFilter ?? this.taskFilter,
      isLoadingRecords: isLoadingRecords ?? this.isLoadingRecords,
      isLoadingTasks: isLoadingTasks ?? this.isLoadingTasks,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }
}

/// Success state for operations
class MundaOperationSuccess extends MundaState {
  final String message;
  final dynamic data;

  const MundaOperationSuccess({
    required this.message,
    this.data,
  });

  @override
  List<Object?> get props => [message, data];
}

/// Error state
class MundaError extends MundaState {
  final Failure failure;
  final String? context;

  const MundaError({
    required this.failure,
    this.context,
  });

  @override
  List<Object?> get props => [failure, context];

  /// Get user-friendly error message
  String get userMessage {
    switch (failure.runtimeType) {
      case ValidationFailure:
        return failure.message;
      case NotFoundFailure:
        return 'The requested item was not found.';
      case NetworkFailure:
        return 'Please check your internet connection and try again.';
      case DatabaseFailure:
        return 'A database error occurred. Please try again.';
      case AuthFailure:
        return 'Authentication failed. Please log in again.';
      case AuthorizationFailure:
        return 'You do not have permission to perform this action.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Check if error is recoverable
  bool get isRecoverable {
    return failure is! AuthFailure && failure is! AuthorizationFailure;
  }
}

/// Analytics loaded state
class AnalyticsLoaded extends MundaState {
  final Map<String, dynamic> userAnalytics;
  final Map<String, dynamic> departmentAnalytics;
  final Map<String, dynamic> qualityMetrics;
  final Map<String, dynamic> taskStatistics;

  const AnalyticsLoaded({
    this.userAnalytics = const {},
    this.departmentAnalytics = const {},
    this.qualityMetrics = const {},
    this.taskStatistics = const {},
  });

  @override
  List<Object?> get props => [
        userAnalytics,
        departmentAnalytics,
        qualityMetrics,
        taskStatistics,
      ];

  /// Copy with new values
  AnalyticsLoaded copyWith({
    Map<String, dynamic>? userAnalytics,
    Map<String, dynamic>? departmentAnalytics,
    Map<String, dynamic>? qualityMetrics,
    Map<String, dynamic>? taskStatistics,
  }) {
    return AnalyticsLoaded(
      userAnalytics: userAnalytics ?? this.userAnalytics,
      departmentAnalytics: departmentAnalytics ?? this.departmentAnalytics,
      qualityMetrics: qualityMetrics ?? this.qualityMetrics,
      taskStatistics: taskStatistics ?? this.taskStatistics,
    );
  }
}

/// Offline sync state
class OfflineSyncInProgress extends MundaState {
  final int totalItems;
  final int syncedItems;
  final String? currentItem;

  const OfflineSyncInProgress({
    required this.totalItems,
    required this.syncedItems,
    this.currentItem,
  });

  @override
  List<Object?> get props => [totalItems, syncedItems, currentItem];

  /// Get sync progress percentage
  double get progress {
    if (totalItems == 0) return 0.0;
    return (syncedItems / totalItems) * 100;
  }

  /// Check if sync is complete
  bool get isComplete => syncedItems >= totalItems;
}

/// Offline sync completed state
class OfflineSyncCompleted extends MundaState {
  final int syncedItems;
  final List<String> errors;

  const OfflineSyncCompleted({
    required this.syncedItems,
    this.errors = const [],
  });

  @override
  List<Object?> get props => [syncedItems, errors];

  /// Check if sync had errors
  bool get hasErrors => errors.isNotEmpty;
}
