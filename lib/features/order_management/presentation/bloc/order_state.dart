part of 'order_bloc.dart';

//import '../../domain/entities/order_entities.dart' show OrderItem;

/// Base order state
abstract class OrderState extends Equatable {
  const OrderState();

  @override
  List<Object?> get props => [];
}

/// Initial order state
class OrderInitial extends OrderState {
  const OrderInitial();
}

/// Order loading state
class OrderLoading extends OrderState {
  const OrderLoading();
}

/// Orders loaded state
class OrdersLoaded extends OrderState {
  final List<ManufacturingOrder> orders;
  final Pagination? pagination;
  final OrderFilterCriteria? filter;
  final bool isRefreshing;
  final List<String> selectedOrderIds;
  final OrderViewMode viewMode;
  final String? sortBy;
  final bool sortAscending;

  const OrdersLoaded({
    required this.orders,
    this.pagination,
    this.filter,
    this.isRefreshing = false,
    this.selectedOrderIds = const [],
    this.viewMode = OrderViewMode.list,
    this.sortBy,
    this.sortAscending = true,
  });

  /// Copy with new values
  OrdersLoaded copyWith({
    List<ManufacturingOrder>? orders,
    Pagination? pagination,
    OrderFilterCriteria? filter,
    bool? isRefreshing,
    List<String>? selectedOrderIds,
    OrderViewMode? viewMode,
    String? sortBy,
    bool? sortAscending,
  }) {
    return OrdersLoaded(
      orders: orders ?? this.orders,
      pagination: pagination ?? this.pagination,
      filter: filter ?? this.filter,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      selectedOrderIds: selectedOrderIds ?? this.selectedOrderIds,
      viewMode: viewMode ?? this.viewMode,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  List<Object?> get props => [
        orders,
        pagination,
        filter,
        isRefreshing,
        selectedOrderIds,
        viewMode,
        sortBy,
        sortAscending,
      ];

  /// Check if order is selected
  bool isOrderSelected(String orderId) {
    return selectedOrderIds.contains(orderId);
  }

  /// Check if all orders are selected
  bool get areAllOrdersSelected {
    return orders.isNotEmpty && selectedOrderIds.length == orders.length;
  }

  /// Check if any orders are selected
  bool get hasSelectedOrders {
    return selectedOrderIds.isNotEmpty;
  }

  /// Get selected orders
  List<ManufacturingOrder> get selectedOrders {
    return orders.where((order) => selectedOrderIds.contains(order.id)).toList();
  }
}

/// Order details loaded state
class OrderDetailsLoaded extends OrderState {
  final ManufacturingOrder order;

  const OrderDetailsLoaded(this.order);

  @override
  List<Object?> get props => [order];
}

/// Orders searched state
class OrdersSearched extends OrderState {
  final List<ManufacturingOrder> orders;
  final Pagination? pagination;
  final String query;
  final OrderFilterCriteria? filter;

  const OrdersSearched({
    required this.orders,
    this.pagination,
    required this.query,
    this.filter,
  });

  @override
  List<Object?> get props => [orders, pagination, query, filter];
}

/// Orders filtered state
class OrdersFiltered extends OrderState {
  final List<ManufacturingOrder> orders;
  final Pagination? pagination;
  final OrderFilterCriteria filter;

  const OrdersFiltered({
    required this.orders,
    this.pagination,
    required this.filter,
  });

  @override
  List<Object?> get props => [orders, pagination, filter];
}

/// Orders by client loaded state
class OrdersByClientLoaded extends OrderState {
  final List<ManufacturingOrder> orders;
  final Pagination? pagination;
  final String clientId;

  const OrdersByClientLoaded({
    required this.orders,
    this.pagination,
    required this.clientId,
  });

  @override
  List<Object?> get props => [orders, pagination, clientId];
}

/// Overdue orders loaded state
class OverdueOrdersLoaded extends OrderState {
  final List<ManufacturingOrder> orders;
  final Pagination? pagination;

  const OverdueOrdersLoaded({
    required this.orders,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, pagination];
}

/// Urgent orders loaded state
class UrgentOrdersLoaded extends OrderState {
  final List<ManufacturingOrder> orders;
  final Pagination? pagination;

  const UrgentOrdersLoaded({
    required this.orders,
    this.pagination,
  });

  @override
  List<Object?> get props => [orders, pagination];
}

/// Order created state
class OrderCreated extends OrderState {
  final ManufacturingOrder order;

  const OrderCreated(this.order);

  @override
  List<Object?> get props => [order];
}

/// Order updated state
class OrderUpdated extends OrderState {
  final ManufacturingOrder order;

  const OrderUpdated(this.order);

  @override
  List<Object?> get props => [order];
}

/// Order deleted state
class OrderDeleted extends OrderState {
  final String orderId;

  const OrderDeleted(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Order status updated state
class OrderStatusUpdated extends OrderState {
  final ManufacturingOrder order;

  const OrderStatusUpdated(this.order);

  @override
  List<Object?> get props => [order];
}

/// Order cancelled state
class OrderCancelled extends OrderState {
  final ManufacturingOrder order;

  const OrderCancelled(this.order);

  @override
  List<Object?> get props => [order];
}

/// Order duplicated state
class OrderDuplicated extends OrderState {
  final ManufacturingOrder order;

  const OrderDuplicated(this.order);

  @override
  List<Object?> get props => [order];
}

/// Order statistics loaded state
class OrderStatisticsLoaded extends OrderState {
  final OrderStatistics statistics;

  const OrderStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Order timeline loaded state
class OrderTimelineLoaded extends OrderState {
  final String orderId;
  final List<OrderTimelineEvent> timeline;

  const OrderTimelineLoaded(this.orderId, this.timeline);

  @override
  List<Object?> get props => [orderId, timeline];
}

/// Order note added state
class OrderNoteAdded extends OrderState {
  final OrderNote note;

  const OrderNoteAdded(this.note);

  @override
  List<Object?> get props => [note];
}

/// Order note updated state
class OrderNoteUpdated extends OrderState {
  final OrderNote note;

  const OrderNoteUpdated(this.note);

  @override
  List<Object?> get props => [note];
}

/// Order note deleted state
class OrderNoteDeleted extends OrderState {
  final String noteId;

  const OrderNoteDeleted(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

/// Order attachment added state
class OrderAttachmentAdded extends OrderState {
  final OrderAttachment attachment;

  const OrderAttachmentAdded(this.attachment);

  @override
  List<Object?> get props => [attachment];
}

/// Order attachment deleted state
class OrderAttachmentDeleted extends OrderState {
  final String attachmentId;

  const OrderAttachmentDeleted(this.attachmentId);

  @override
  List<Object?> get props => [attachmentId];
}

/// Order item status updated state
class OrderItemStatusUpdated extends OrderState {
  final OrderItem item;

  const OrderItemStatusUpdated(this.item);

  @override
  List<Object?> get props => [item];
}

/// Order held state
class OrderHeld extends OrderState {
  final ManufacturingOrder order;

  const OrderHeld(this.order);

  @override
  List<Object?> get props => [order];
}

/// Order resumed state
class OrderResumed extends OrderState {
  final ManufacturingOrder order;

  const OrderResumed(this.order);

  @override
  List<Object?> get props => [order];
}

/// Orders exported state
class OrdersExported extends OrderState {
  final String filePath;
  final String format;

  const OrdersExported(this.filePath, this.format);

  @override
  List<Object?> get props => [filePath, format];
}

/// Orders imported state
class OrdersImported extends OrderState {
  final OrderImportResult result;

  const OrdersImported(this.result);

  @override
  List<Object?> get props => [result];
}

/// Orders bulk updated state
class OrdersBulkUpdated extends OrderState {
  final BulkUpdateResult result;

  const OrdersBulkUpdated(this.result);

  @override
  List<Object?> get props => [result];
}

/// Order production schedule loaded state
class OrderProductionScheduleLoaded extends OrderState {
  final String orderId;
  final List<OrderProductionSchedule> schedule;

  const OrderProductionScheduleLoaded(this.orderId, this.schedule);

  @override
  List<Object?> get props => [orderId, schedule];
}

/// Order production schedule updated state
class OrderProductionScheduleUpdated extends OrderState {
  final String orderId;

  const OrderProductionScheduleUpdated(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

/// Order error state
class OrderError extends OrderState {
  final String message;

  const OrderError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Order validation error state
class OrderValidationError extends OrderState {
  final Map<String, String> errors;

  const OrderValidationError(this.errors);

  @override
  List<Object?> get props => [errors];
}

/// Order operation success state
class OrderOperationSuccess extends OrderState {
  final String message;

  const OrderOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Order selection changed state
class OrderSelectionChanged extends OrderState {
  final List<String> selectedOrderIds;

  const OrderSelectionChanged(this.selectedOrderIds);

  @override
  List<Object?> get props => [selectedOrderIds];
}

/// Order view mode changed state
class OrderViewModeChanged extends OrderState {
  final OrderViewMode viewMode;

  const OrderViewModeChanged(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Orders sorted state
class OrdersSorted extends OrderState {
  final String sortBy;
  final bool ascending;

  const OrdersSorted(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Order loading more state
class OrderLoadingMore extends OrderState {
  const OrderLoadingMore();
}

/// Order refreshing state
class OrderRefreshing extends OrderState {
  const OrderRefreshing();
}
