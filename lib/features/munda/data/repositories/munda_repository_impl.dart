import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/munda_record.dart';
import '../../domain/entities/munda_task.dart';
import '../../domain/entities/munda_requests.dart';
import '../../domain/repositories/munda_repository.dart';
import '../datasources/munda_firebase_datasource.dart';

/// Implementation of MundaRepository using Firebase
class MundaRepositoryImpl implements MundaRepository {
  final MundaFirebaseDataSource _dataSource;

  MundaRepositoryImpl({
    required MundaFirebaseDataSource dataSource,
  }) : _dataSource = dataSource;

  // ==================== MUNDA RECORDS ====================

  @override
  Future<Either<Failure, MundaRecord>> createRecord(
    CreateMundaRecordRequest request,
    String userId,
  ) async {
    try {
      // Validate request
      final validationResult = await validateRecord(request);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => throw Exception('Unexpected validation result'),
        );
      }

      // Check for duplicates
      final duplicateResult = await checkDuplicateRecord(
        request.designNumber,
        request.lotNumber,
        request.rollNumber,
        userId,
      );
      
      if (duplicateResult.isLeft()) {
        return duplicateResult.fold(
          (failure) => Left(failure),
          (_) => throw Exception('Unexpected duplicate check result'),
        );
      }

      final isDuplicate = duplicateResult.fold(
        (_) => false,
        (isDup) => isDup,
      );

      if (isDuplicate) {
        return const Left(ValidationFailure(
          'Duplicate record found with same design number, lot number, and roll number',
          code: 'DUPLICATE_RECORD',
        ));
      }

      final record = await _dataSource.createRecord(request, userId);
      return Right(record);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to create record: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaRecord>> getRecord(String recordId) async {
    try {
      final record = await _dataSource.getRecord(recordId);
      return Right(record);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get record: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaRecord>> updateRecord(
    UpdateMundaRecordRequest request,
    String userId,
  ) async {
    try {
      final record = await _dataSource.updateRecord(request, userId);
      return Right(record);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to update record: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteRecord(String recordId, String userId) async {
    try {
      await _dataSource.deleteRecord(recordId, userId);
      return const Right(null);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to delete record: $e'));
    }
  }

  @override
  Future<Either<Failure, PaginatedResult<MundaRecord>>> getRecords({
    MundaRecordFilterCriteria? filter,
    PaginationParams? pagination,
    String? userId,
  }) async {
    try {
      final result = await _dataSource.getRecords(
        filter: filter,
        pagination: pagination,
        userId: userId,
      );
      return Right(result);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get records: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MundaRecord>>> getRecordsByTask(String taskId) async {
    try {
      final records = await _dataSource.getRecordsByTask(taskId);
      return Right(records);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get records by task: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MundaRecord>>> getRecordsByUser(
    String userId, {
    MundaRecordFilterCriteria? filter,
  }) async {
    try {
      final result = await _dataSource.getRecords(
        filter: filter,
        userId: userId,
      );
      return Right(result.data);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get records by user: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaRecordSummary>> getRecordsSummary({
    MundaRecordFilterCriteria? filter,
    String? userId,
  }) async {
    try {
      final summary = await _dataSource.getRecordsSummary(
        filter: filter,
        userId: userId,
      );
      return Right(summary);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get records summary: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getNextSerialNumber(String userId) async {
    try {
      final serialNumber = await _dataSource.getNextSerialNumber(userId);
      return Right(serialNumber);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get next serial number: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MundaRecord>>> bulkCreateRecords(
    List<CreateMundaRecordRequest> requests,
    String userId,
  ) async {
    try {
      final List<MundaRecord> createdRecords = [];
      
      for (final request in requests) {
        final result = await createRecord(request, userId);
        if (result.isLeft()) {
          return result.fold(
            (failure) => Left(failure),
            (_) => throw Exception('Unexpected result'),
          );
        }
        
        final record = result.fold(
          (_) => throw Exception('Unexpected failure'),
          (record) => record,
        );
        
        createdRecords.add(record);
      }
      
      return Right(createdRecords);
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to bulk create records: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> exportRecords({
    MundaRecordFilterCriteria? filter,
    String? userId,
    String format = 'csv',
  }) async {
    try {
      // This would be implemented based on the specific export requirements
      // For now, return a placeholder
      return const Left(UnimplementedFailure(
        'Export functionality not yet implemented',
        code: 'EXPORT_NOT_IMPLEMENTED',
      ));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to export records: $e'));
    }
  }

  // ==================== MUNDA TASKS ====================

  @override
  Future<Either<Failure, MundaTask>> createTask(
    CreateMundaTaskRequest request,
    String createdBy,
  ) async {
    try {
      // Validate request
      final validationResult = await validateTask(request);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => throw Exception('Unexpected validation result'),
        );
      }

      final task = await _dataSource.createTask(request, createdBy);
      return Right(task);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to create task: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> getTask(String taskId) async {
    try {
      final task = await _dataSource.getTask(taskId);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get task: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> updateTask(
    UpdateMundaTaskRequest request,
    String updatedBy,
  ) async {
    try {
      final task = await _dataSource.updateTask(request, updatedBy);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to update task: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteTask(String taskId, String deletedBy) async {
    try {
      await _dataSource.deleteTask(taskId, deletedBy);
      return const Right(null);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to delete task: $e'));
    }
  }

  @override
  Future<Either<Failure, PaginatedResult<MundaTask>>> getTasks({
    MundaTaskFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      final result = await _dataSource.getTasks(
        filter: filter,
        pagination: pagination,
      );
      return Right(result);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get tasks: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MundaTask>>> getTasksByUser(
    String userId, {
    MundaTaskFilterCriteria? filter,
  }) async {
    try {
      final tasks = await _dataSource.getTasksByUser(userId, filter: filter);
      return Right(tasks);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get tasks by user: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> assignTask(
    String taskId,
    String assignedTo,
    String assignedBy,
  ) async {
    try {
      final request = UpdateMundaTaskRequest(
        taskId: taskId,
        assignedTo: assignedTo,
        status: MundaTaskStatus.assigned,
      );

      final task = await _dataSource.updateTask(request, assignedBy);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to assign task: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> startTask(String taskId, String userId) async {
    try {
      final request = UpdateMundaTaskRequest(
        taskId: taskId,
        status: MundaTaskStatus.inProgress,
      );

      final task = await _dataSource.updateTask(request, userId);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to start task: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> completeTask(
    String taskId,
    String userId, {
    String? notes,
  }) async {
    try {
      final request = UpdateMundaTaskRequest(
        taskId: taskId,
        status: MundaTaskStatus.completed,
        operatorNotes: notes,
      );

      final task = await _dataSource.updateTask(request, userId);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to complete task: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> sendTaskToBundling(
    String taskId,
    String userId, {
    String? notes,
  }) async {
    try {
      final request = UpdateMundaTaskRequest(
        taskId: taskId,
        status: MundaTaskStatus.sentToBundling,
        operatorNotes: notes,
      );

      final task = await _dataSource.updateTask(request, userId);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to send task to bundling: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> cancelTask(
    String taskId,
    String userId,
    String reason,
  ) async {
    try {
      final request = UpdateMundaTaskRequest(
        taskId: taskId,
        status: MundaTaskStatus.cancelled,
        operatorNotes: reason,
      );

      final task = await _dataSource.updateTask(request, userId);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to cancel task: $e'));
    }
  }

  @override
  Future<Either<Failure, MundaTask>> updateTaskProgress(String taskId) async {
    try {
      // This is handled automatically by the data source when records are modified
      final task = await _dataSource.getTask(taskId);
      return Right(task);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message, code: e.code, details: e.details));
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to update task progress: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getTaskStatistics({
    String? userId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // This would be implemented based on specific analytics requirements
      return const Left(UnimplementedFailure(
        'Task statistics not yet implemented',
        code: 'TASK_STATISTICS_NOT_IMPLEMENTED',
      ));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get task statistics: $e'));
    }
  }

  // ==================== REAL-TIME UPDATES ====================

  @override
  Stream<List<MundaRecord>> watchUserRecords(String userId) {
    // This would be implemented using Firestore streams
    throw UnimplementedError('Real-time updates not yet implemented');
  }

  @override
  Stream<List<MundaTask>> watchUserTasks(String userId) {
    // This would be implemented using Firestore streams
    throw UnimplementedError('Real-time updates not yet implemented');
  }

  @override
  Stream<MundaRecordSummary> watchRecordsSummary({
    String? userId,
    MundaRecordFilterCriteria? filter,
  }) {
    // This would be implemented using Firestore streams
    throw UnimplementedError('Real-time updates not yet implemented');
  }

  @override
  Stream<MundaTask> watchTask(String taskId) {
    // This would be implemented using Firestore streams
    throw UnimplementedError('Real-time updates not yet implemented');
  }

  // ==================== OFFLINE SUPPORT ====================

  @override
  Future<Either<Failure, void>> syncOfflineData(String userId) async {
    try {
      // This would be implemented based on offline storage requirements
      return const Left(UnimplementedFailure(
        'Offline sync not yet implemented',
        code: 'OFFLINE_SYNC_NOT_IMPLEMENTED',
      ));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to sync offline data: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getOfflineRecordsCount(String userId) async {
    try {
      // This would be implemented based on offline storage requirements
      return const Right(0);
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get offline records count: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearOfflineData(String userId) async {
    try {
      // This would be implemented based on offline storage requirements
      return const Right(null);
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to clear offline data: $e'));
    }
  }

  // ==================== VALIDATION ====================

  @override
  Future<Either<Failure, bool>> validateRecord(
    CreateMundaRecordRequest request,
  ) async {
    try {
      if (!request.isValid) {
        return const Left(ValidationFailure(
          'Invalid record data',
          code: 'INVALID_RECORD_DATA',
        ));
      }

      return const Right(true);
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to validate record: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDuplicateRecord(
    String designNumber,
    String lotNumber,
    String rollNumber,
    String userId,
  ) async {
    try {
      final filter = MundaRecordFilterCriteria(
        designNumber: designNumber,
        lotNumber: lotNumber,
        rollNumber: rollNumber,
      );

      final result = await _dataSource.getRecords(
        filter: filter,
        userId: userId,
        pagination: const PaginationParams(perPage: 1),
      );

      return Right(result.data.isNotEmpty);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message, code: e.code, details: e.details));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to check duplicate record: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> validateTask(CreateMundaTaskRequest request) async {
    try {
      if (!request.isValid) {
        return const Left(ValidationFailure(
          'Invalid task data',
          code: 'INVALID_TASK_DATA',
        ));
      }

      return const Right(true);
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to validate task: $e'));
    }
  }

  // ==================== ANALYTICS ====================

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUserProductivityAnalytics(
    String userId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // This would be implemented based on specific analytics requirements
      return const Left(UnimplementedFailure(
        'User productivity analytics not yet implemented',
        code: 'USER_ANALYTICS_NOT_IMPLEMENTED',
      ));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get user productivity analytics: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getDepartmentAnalytics({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // This would be implemented based on specific analytics requirements
      return const Left(UnimplementedFailure(
        'Department analytics not yet implemented',
        code: 'DEPARTMENT_ANALYTICS_NOT_IMPLEMENTED',
      ));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get department analytics: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getQualityMetrics({
    String? userId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // This would be implemented based on specific quality metrics requirements
      return const Left(UnimplementedFailure(
        'Quality metrics not yet implemented',
        code: 'QUALITY_METRICS_NOT_IMPLEMENTED',
      ));
    } on Exception catch (e) {
      return Left(UnknownFailure('Failed to get quality metrics: $e'));
    }
  }
}
