import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hm_collection/features/auth/domain/entities/login_credentials.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/auth_tokens.dart';
import '../../domain/entities/biometric_auth_data.dart';
import '../../domain/entities/password_reset_confirm_request.dart';
import '../../domain/entities/two_factor_auth_data.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';

part 'auth_event.dart';
part 'auth_state.dart';

/// Authentication Bloc
@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final BiometricLoginUseCase _biometricLoginUseCase;
  final AutoLoginUseCase _autoLoginUseCase;
  final LogoutUseCase _logoutUseCase;
  final LogoutAllSessionsUseCase _logoutAllSessionsUseCase;

  AuthBloc(
    this._loginUseCase,
    this._biometricLoginUseCase,
    this._autoLoginUseCase,
    this._logoutUseCase,
    this._logoutAllSessionsUseCase,
  ) : super(const AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthBiometricLoginRequested>(_onAuthBiometricLoginRequested);
    on<AuthAutoLoginRequested>(_onAuthAutoLoginRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthLogoutAllSessionsRequested>(_onAuthLogoutAllSessionsRequested);
    on<AuthUserUpdated>(_onAuthUserUpdated);
    on<AuthTokensUpdated>(_onAuthTokensUpdated);
    on<AuthSessionExpired>(_onAuthSessionExpired);
  }

  /// Handle authentication check
  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _autoLoginUseCase(const NoParams());

    result.fold(
      (failure) => emit(const AuthUnauthenticated()),
      (loginResult) {
        if (loginResult != null) {
          emit(AuthAuthenticated(
            user: loginResult.user,
            tokens: loginResult.tokens,
            isFirstLogin: loginResult.isFirstLogin,
            requiresPasswordChange: loginResult.requiresPasswordChange,
            requiresTwoFactorSetup: loginResult.requiresTwoFactorSetup,
          ));
        } else {
          emit(const AuthUnauthenticated());
        }
      },
    );
  }

  /// Handle login request
  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _loginUseCase(LoginParams(
      credentials: event.credentials,
      saveCredentials: event.saveCredentials,
      enableBiometric: event.enableBiometric,
    ));

    result.fold(
      (failure) => emit(AuthError(failure.message, failure)),
      (loginResult) => emit(AuthAuthenticated(
        user: loginResult.user,
        tokens: loginResult.tokens,
        isFirstLogin: loginResult.isFirstLogin,
        requiresPasswordChange: loginResult.requiresPasswordChange,
        requiresTwoFactorSetup: loginResult.requiresTwoFactorSetup,
      )),
    );
  }

  /// Handle biometric login request
  Future<void> _onAuthBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _biometricLoginUseCase(BiometricLoginParams(
      reason: event.reason,
    ));

    result.fold(
      (failure) => emit(AuthError(failure.message, failure)),
      (loginResult) => emit(AuthAuthenticated(
        user: loginResult.user,
        tokens: loginResult.tokens,
        isFirstLogin: loginResult.isFirstLogin,
        requiresPasswordChange: loginResult.requiresPasswordChange,
        requiresTwoFactorSetup: loginResult.requiresTwoFactorSetup,
      )),
    );
  }

  /// Handle auto login request
  Future<void> _onAuthAutoLoginRequested(
    AuthAutoLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _autoLoginUseCase(const NoParams());

    result.fold(
      (failure) => emit(const AuthUnauthenticated()),
      (loginResult) {
        if (loginResult != null) {
          emit(AuthAuthenticated(
            user: loginResult.user,
            tokens: loginResult.tokens,
            isFirstLogin: loginResult.isFirstLogin,
            requiresPasswordChange: loginResult.requiresPasswordChange,
            requiresTwoFactorSetup: loginResult.requiresTwoFactorSetup,
          ));
        } else {
          emit(const AuthUnauthenticated());
        }
      },
    );
  }

  /// Handle logout request
  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _logoutUseCase(event.params);

    result.fold(
      (failure) => emit(AuthError(failure.message, failure)),
      (success) => emit(const AuthUnauthenticated()),
    );
  }

  /// Handle logout all sessions request
  Future<void> _onAuthLogoutAllSessionsRequested(
    AuthLogoutAllSessionsRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _logoutAllSessionsUseCase(const NoParams());

    result.fold(
      (failure) => emit(AuthError(failure.message, failure)),
      (success) => emit(const AuthUnauthenticated()),
    );
  }

  /// Handle user update
  Future<void> _onAuthUserUpdated(
    AuthUserUpdated event,
    Emitter<AuthState> emit,
  ) async {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      emit(currentState.copyWith(user: event.user));
    }
  }

  /// Handle tokens update
  Future<void> _onAuthTokensUpdated(
    AuthTokensUpdated event,
    Emitter<AuthState> emit,
  ) async {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      emit(currentState.copyWith(tokens: event.tokens));
    }
  }

  /// Handle session expired
  Future<void> _onAuthSessionExpired(
    AuthSessionExpired event,
    Emitter<AuthState> emit,
  ) async {
    // Force logout with timeout params
    final result = await _logoutUseCase(LogoutParams.timeout());

    result.fold(
      (failure) => emit(AuthError(failure.message, failure)),
      (success) => emit(AuthSessionExpiredState(reason: event.reason)),
    );
  }
}

/// Authentication status helper
extension AuthBlocExtension on AuthBloc {
  /// Check if user is authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  /// Check if user is unauthenticated
  bool get isUnauthenticated => state is AuthUnauthenticated;

  /// Check if authentication is loading
  bool get isLoading => state is AuthLoading;

  /// Check if there's an authentication error
  bool get hasError => state is AuthError;

  /// Get current user (null if not authenticated)
  User? get currentUser {
    final currentState = state;
    return currentState is AuthAuthenticated ? currentState.user : null;
  }

  /// Get current tokens (null if not authenticated)
  AuthTokens? get currentTokens {
    final currentState = state;
    return currentState is AuthAuthenticated ? currentState.tokens : null;
  }

  /// Check if user has specific permission
  bool hasPermission(String permission) {
    final user = currentUser;
    return user?.hasPermission(permission) ?? false;
  }

  /// Check if user has any of the specified permissions
  bool hasAnyPermission(List<String> permissions) {
    final user = currentUser;
    return user?.hasAnyPermission(permissions) ?? false;
  }

  /// Check if user has all specified permissions
  bool hasAllPermissions(List<String> permissions) {
    final user = currentUser;
    return user?.hasAllPermissions(permissions) ?? false;
  }

  /// Check if user has specific role
  bool hasRole(String role) {
    final user = currentUser;
    return user?.role.value == role;
  }

  /// Check if user is admin
  bool get isAdmin {
    final user = currentUser;
    return user?.isAdmin ?? false;
  }

  /// Check if user is department head
  bool get isDepartmentHead {
    final user = currentUser;
    return user?.isDepartmentHead ?? false;
  }

  /// Check if user is supervisor
  bool get isSupervisor {
    final user = currentUser;
    return user?.isSupervisor ?? false;
  }

  /// Check if user is operator
  bool get isOperator {
    final user = currentUser;
    return user?.isOperator ?? false;
  }

  /// Get user's department
  String? get userDepartment {
    final user = currentUser;
    return user?.department.value;
  }

  /// Get user's role
  String? get userRole {
    final user = currentUser;
    return user?.role.value;
  }

  /// Get user's display name
  String? get userDisplayName {
    final user = currentUser;
    return user?.displayName;
  }

  /// Check if tokens will expire soon
  bool get tokensWillExpireSoon {
    final tokens = currentTokens;
    return tokens?.willExpireSoon ?? false;
  }

  /// Check if tokens are expired
  bool get tokensExpired {
    final tokens = currentTokens;
    return tokens?.isExpired ?? true;
  }
}
