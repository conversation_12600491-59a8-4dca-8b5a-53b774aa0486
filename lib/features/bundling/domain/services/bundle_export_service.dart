import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/bundle.dart';

/// Service for exporting bundles to various formats
abstract class BundleExportService {
  /// Export bundles to Excel format
  Future<Either<Failure, String>> exportToExcel(
    List<Bundle> bundles, {
    String? fileName,
    bool includeStatistics = true,
  });

  /// Export bundles to PDF format
  Future<Either<Failure, String>> exportToPdf(
    List<Bundle> bundles, {
    String? fileName,
    bool includeStatistics = true,
    bool includeQrCodes = true,
  });

  /// Export single bundle sheet for floor use
  Future<Either<Failure, String>> exportBundleSheet(
    Bundle bundle, {
    String format = 'pdf', // 'pdf' or 'excel'
    bool includeQrCode = true,
    bool includeInstructions = true,
  });

  /// Export bundle summary report
  Future<Either<Failure, String>> exportSummaryReport(
    List<Bundle> bundles, {
    String format = 'pdf',
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get available export formats
  List<String> getSupportedFormats();

  /// Get default export directory
  Future<String> getDefaultExportDirectory();

  /// Share exported file
  Future<Either<Failure, void>> shareFile(String filePath);
}

/// Export format enumeration
enum ExportFormat {
  excel('excel', 'Excel (.xlsx)', '.xlsx'),
  pdf('pdf', 'PDF (.pdf)', '.pdf'),
  csv('csv', 'CSV (.csv)', '.csv');

  const ExportFormat(this.value, this.displayName, this.extension);

  final String value;
  final String displayName;
  final String extension;

  static ExportFormat fromString(String value) {
    return ExportFormat.values.firstWhere(
      (format) => format.value == value,
      orElse: () => ExportFormat.pdf,
    );
  }
}

/// Export options for customizing export output
class ExportOptions {
  final ExportFormat format;
  final String? fileName;
  final bool includeStatistics;
  final bool includeQrCodes;
  final bool includeInstructions;
  final bool includeImages;
  final DateTime? fromDate;
  final DateTime? toDate;
  final List<String>? selectedFields;
  final String? templatePath;
  final Map<String, dynamic>? customData;

  const ExportOptions({
    this.format = ExportFormat.pdf,
    this.fileName,
    this.includeStatistics = true,
    this.includeQrCodes = true,
    this.includeInstructions = true,
    this.includeImages = false,
    this.fromDate,
    this.toDate,
    this.selectedFields,
    this.templatePath,
    this.customData,
  });

  ExportOptions copyWith({
    ExportFormat? format,
    String? fileName,
    bool? includeStatistics,
    bool? includeQrCodes,
    bool? includeInstructions,
    bool? includeImages,
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? selectedFields,
    String? templatePath,
    Map<String, dynamic>? customData,
  }) {
    return ExportOptions(
      format: format ?? this.format,
      fileName: fileName ?? this.fileName,
      includeStatistics: includeStatistics ?? this.includeStatistics,
      includeQrCodes: includeQrCodes ?? this.includeQrCodes,
      includeInstructions: includeInstructions ?? this.includeInstructions,
      includeImages: includeImages ?? this.includeImages,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      selectedFields: selectedFields ?? this.selectedFields,
      templatePath: templatePath ?? this.templatePath,
      customData: customData ?? this.customData,
    );
  }
}

/// Export result containing file information
class ExportResult {
  final String filePath;
  final String fileName;
  final ExportFormat format;
  final int fileSize;
  final DateTime createdAt;
  final int bundleCount;
  final Map<String, dynamic>? metadata;

  const ExportResult({
    required this.filePath,
    required this.fileName,
    required this.format,
    required this.fileSize,
    required this.createdAt,
    required this.bundleCount,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'filePath': filePath,
      'fileName': fileName,
      'format': format.value,
      'fileSize': fileSize,
      'createdAt': createdAt.toIso8601String(),
      'bundleCount': bundleCount,
      'metadata': metadata,
    };
  }

  factory ExportResult.fromJson(Map<String, dynamic> json) {
    return ExportResult(
      filePath: json['filePath'] as String,
      fileName: json['fileName'] as String,
      format: ExportFormat.fromString(json['format'] as String),
      fileSize: json['fileSize'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      bundleCount: json['bundleCount'] as int,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Template for bundle sheet export
class BundleSheetTemplate {
  final String name;
  final String description;
  final ExportFormat format;
  final List<String> requiredFields;
  final Map<String, dynamic> layout;
  final String? templatePath;

  const BundleSheetTemplate({
    required this.name,
    required this.description,
    required this.format,
    required this.requiredFields,
    required this.layout,
    this.templatePath,
  });

  static const BundleSheetTemplate defaultPdfTemplate = BundleSheetTemplate(
    name: 'Default PDF Template',
    description: 'Standard bundle sheet for floor use',
    format: ExportFormat.pdf,
    requiredFields: [
      'bundleNo',
      'designNo',
      'lotNo',
      'rollNo',
      'altNo',
      'size',
      'pieceRange',
      'totalPieces',
      'status',
      'createdAt',
    ],
    layout: {
      'pageSize': 'A4',
      'orientation': 'portrait',
      'margins': {'top': 20, 'bottom': 20, 'left': 20, 'right': 20},
      'header': {'height': 80, 'includeCompanyLogo': true},
      'footer': {'height': 40, 'includePageNumbers': true},
      'qrCodeSize': 100,
      'fontSize': {'title': 16, 'subtitle': 12, 'body': 10},
    },
  );

  static const BundleSheetTemplate defaultExcelTemplate = BundleSheetTemplate(
    name: 'Default Excel Template',
    description: 'Standard bundle sheet in Excel format',
    format: ExportFormat.excel,
    requiredFields: [
      'bundleNo',
      'designNo',
      'lotNo',
      'rollNo',
      'altNo',
      'size',
      'pieceRange',
      'totalPieces',
      'status',
      'createdAt',
      'assignedTo',
      'remarks',
    ],
    layout: {
      'sheetName': 'Bundle Sheet',
      'includeHeader': true,
      'freezeHeader': true,
      'autoFitColumns': true,
      'includeFilters': true,
      'headerStyle': {
        'bold': true,
        'backgroundColor': '#4472C4',
        'fontColor': '#FFFFFF',
      },
    },
  );
}
