import 'package:flutter/material.dart';

/// Widget that displays quick actions for collaboration
class QuickActionsWidget extends StatelessWidget {
  final VoidCallback? onCreateWorkspace;
  final VoidCallback? onJoinWorkspace;
  final VoidCallback? onStartMeeting;
  final VoidCallback? onShareScreen;
  final VoidCallback? onCreateChannel;
  final VoidCallback? onInviteMembers;

  const QuickActionsWidget({
    Key? key,
    this.onCreateWorkspace,
    this.onJoinWorkspace,
    this.onStartMeeting,
    this.onShareScreen,
    this.onCreateChannel,
    this.onInviteMembers,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1.2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              children: [
                _buildActionButton(
                  context,
                  'Create\nWorkspace',
                  Icons.add_business,
                  Colors.blue,
                  onCreateWorkspace,
                ),
                _buildActionButton(
                  context,
                  'Join\nWorkspace',
                  Icons.group_add,
                  Colors.green,
                  onJoinWorkspace,
                ),
                _buildActionButton(
                  context,
                  'Start\nMeeting',
                  Icons.video_call,
                  Colors.purple,
                  onStartMeeting,
                ),
                _buildActionButton(
                  context,
                  'Share\nScreen',
                  Icons.screen_share,
                  Colors.orange,
                  onShareScreen,
                ),
                _buildActionButton(
                  context,
                  'Create\nChannel',
                  Icons.add_comment,
                  Colors.teal,
                  onCreateChannel,
                ),
                _buildActionButton(
                  context,
                  'Invite\nMembers',
                  Icons.person_add,
                  Colors.indigo,
                  onInviteMembers,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback? onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Horizontal quick actions bar
class QuickActionsBar extends StatelessWidget {
  final List<QuickAction> actions;

  const QuickActionsBar({
    Key? key,
    required this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: actions.length,
        itemBuilder: (context, index) {
          final action = actions[index];
          return Container(
            width: 80,
            margin: EdgeInsets.only(
              left: index == 0 ? 16 : 8,
              right: index == actions.length - 1 ? 16 : 8,
            ),
            child: InkWell(
              onTap: action.onPressed,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                decoration: BoxDecoration(
                  color: action.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: action.color.withOpacity(0.3)),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      action.icon,
                      color: action.color,
                      size: 20,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      action.label,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: action.color,
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Floating action button with quick actions
class QuickActionsFAB extends StatefulWidget {
  final List<QuickAction> actions;

  const QuickActionsFAB({
    Key? key,
    required this.actions,
  }) : super(key: key);

  @override
  State<QuickActionsFAB> createState() => _QuickActionsFABState();
}

class _QuickActionsFABState extends State<QuickActionsFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...widget.actions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;
          return AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.scale(
                scale: _animation.value,
                child: Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: FloatingActionButton.small(
                    onPressed: _animation.value > 0.5 ? action.onPressed : null,
                    backgroundColor: action.color,
                    heroTag: 'quick_action_$index',
                    child: Icon(
                      action.icon,
                      color: Colors.white,
                    ),
                  ),
                ),
              );
            },
          );
        }).toList().reversed,
        FloatingActionButton(
          onPressed: _toggleExpanded,
          child: AnimatedRotation(
            turns: _isExpanded ? 0.125 : 0,
            duration: const Duration(milliseconds: 300),
            child: const Icon(Icons.add),
          ),
        ),
      ],
    );
  }
}

/// Model class for quick actions
class QuickAction {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback? onPressed;

  const QuickAction({
    required this.label,
    required this.icon,
    required this.color,
    this.onPressed,
  });
}