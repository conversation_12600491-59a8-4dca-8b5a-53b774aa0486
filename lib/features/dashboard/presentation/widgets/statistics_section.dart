import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../domain/entities/dashboard_entities.dart';

/// Statistics section widget
class StatisticsSection extends StatelessWidget {
  final List<DashboardStatistics> statistics;

  const StatisticsSection({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    if (statistics.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _getCrossAxisCount(context),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
          ),
          itemCount: statistics.length,
          itemBuilder: (context, index) {
            return StatisticCard(statistic: statistics[index]);
          },
        ),
      ],
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }
}

/// Individual statistic card
class StatisticCard extends StatelessWidget {
  final DashboardStatistics statistic;

  const StatisticCard({
    super.key,
    required this.statistic,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (statistic.icon != null) ...[
                  Icon(
                    _getIconData(statistic.icon!),
                    color: _getColor(),
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    statistic.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              statistic.value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getColor(),
              ),
            ),
            if (statistic.subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                statistic.subtitle!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
            if (statistic.trend != null && statistic.trendPercentage != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    _getTrendIcon(),
                    size: 16,
                    color: _getTrendColor(),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${statistic.trendPercentage!.abs().toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getTrendColor(),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    statistic.trend!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getColor() {
    if (statistic.color != null) {
      return Color(int.parse(statistic.color!.replaceFirst('#', '0xFF')));
    }
    return AppColors.primary;
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'orders':
        return Icons.shopping_bag_outlined;
      case 'production':
        return Icons.precision_manufacturing_outlined;
      case 'quality':
        return Icons.verified_outlined;
      case 'inventory':
        return Icons.inventory_2_outlined;
      case 'users':
        return Icons.people_outline;
      case 'revenue':
        return Icons.attach_money_outlined;
      case 'efficiency':
        return Icons.trending_up_outlined;
      case 'alerts':
        return Icons.warning_amber_outlined;
      default:
        return Icons.analytics_outlined;
    }
  }

  IconData _getTrendIcon() {
    switch (statistic.trendDirection) {
      case StatisticTrend.up:
        return Icons.trending_up;
      case StatisticTrend.down:
        return Icons.trending_down;
      case StatisticTrend.neutral:
        return Icons.trending_flat;
    }
  }

  Color _getTrendColor() {
    switch (statistic.trendDirection) {
      case StatisticTrend.up:
        return AppColors.success;
      case StatisticTrend.down:
        return AppColors.error;
      case StatisticTrend.neutral:
        return AppColors.textSecondary;
    }
  }
}

/// Animated statistic card with loading state
class AnimatedStatisticCard extends StatefulWidget {
  final DashboardStatistics statistic;
  final bool isLoading;

  const AnimatedStatisticCard({
    super.key,
    required this.statistic,
    this.isLoading = false,
  });

  @override
  State<AnimatedStatisticCard> createState() => _AnimatedStatisticCardState();
}

class _AnimatedStatisticCardState extends State<AnimatedStatisticCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: widget.isLoading
                ? _buildLoadingCard()
                : StatisticCard(statistic: widget.statistic),
          ),
        );
      },
    );
  }

  Widget _buildLoadingCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.gray200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    height: 16,
                    decoration: BoxDecoration(
                      color: AppColors.gray200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 32,
              width: 80,
              decoration: BoxDecoration(
                color: AppColors.gray200,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 12,
              width: 120,
              decoration: BoxDecoration(
                color: AppColors.gray200,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
