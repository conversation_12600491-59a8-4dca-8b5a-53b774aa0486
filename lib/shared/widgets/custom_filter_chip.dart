import 'package:flutter/material.dart';

/// Custom filter chip widget with consistent styling
class CustomFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final void Function(bool)? onSelected;
  final Widget? avatar;
  final Color? selectedColor;
  final Color? backgroundColor;
  final bool enabled;

  const CustomFilterChip({
    Key? key,
    required this.label,
    required this.isSelected,
    this.onSelected,
    this.avatar,
    this.selectedColor,
    this.backgroundColor,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected 
              ? Colors.white
              : Theme.of(context).textTheme.bodyMedium?.color,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      avatar: avatar,
      selected: isSelected,
      onSelected: enabled ? onSelected : null,
      selectedColor: selectedColor ?? Theme.of(context).primaryColor,
      backgroundColor: backgroundColor ?? Theme.of(context).cardColor,
      checkmarkColor: Colors.white,
      elevation: isSelected ? 2 : 0,
      pressElevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected 
              ? (selectedColor ?? Theme.of(context).primaryColor)
              : Colors.grey.shade300,
          width: 1,
        ),
      ),
    );
  }
}
