import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Compliance audit entity
class ComplianceAudit extends BaseEntity {
  final String auditNumber;
  final String auditName;
  final AuditType type;
  final AuditScope scope;
  final AuditStatus status;
  final String auditorId;
  final String auditorName;
  final String? externalAuditor;
  final DateTime scheduledDate;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<ComplianceRequirement> requirements;
  final List<AuditFinding> findings;
  final AuditResult result;
  final String? summary;
  final List<String> attachments;
  final String? nextAuditDate;

  const ComplianceAudit({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.auditNumber,
    required this.auditName,
    required this.type,
    required this.scope,
    required this.status,
    required this.auditorId,
    required this.auditorName,
    this.externalAuditor,
    required this.scheduledDate,
    this.startDate,
    this.endDate,
    required this.requirements,
    this.findings = const [],
    required this.result,
    this.summary,
    this.attachments = const [],
    this.nextAuditDate,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        auditNumber,
        auditName,
        type,
        scope,
        status,
        auditorId,
        auditorName,
        externalAuditor,
        scheduledDate,
        startDate,
        endDate,
        requirements,
        findings,
        result,
        summary,
        attachments,
        nextAuditDate,
      ];

  /// Check if audit is completed
  bool get isCompleted => status == AuditStatus.completed;

  /// Check if audit passed
  bool get isPassed => result.overallResult == ComplianceResultType.compliant;

  /// Get audit duration
  Duration? get auditDuration {
    if (startDate == null || endDate == null) return null;
    return endDate!.difference(startDate!);
  }

  /// Get critical findings count
  int get criticalFindingsCount => findings.where((f) => f.severity == FindingSeverity.critical).length;

  /// Get major findings count
  int get majorFindingsCount => findings.where((f) => f.severity == FindingSeverity.major).length;

  /// Get minor findings count
  int get minorFindingsCount => findings.where((f) => f.severity == FindingSeverity.minor).length;
}

/// Compliance requirement entity
class ComplianceRequirement extends BaseEntity {
  final String requirementCode;
  final String requirementName;
  final String description;
  final RequirementType type;
  final RequirementCategory category;
  final RequirementPriority priority;
  final CommonStatus status;
  final String? regulatoryBody;
  final String? standard;
  final DateTime? effectiveDate;
  final DateTime? expiryDate;
  final List<ComplianceControl> controls;
  final String? documentation;
  final bool isApplicable;

  const ComplianceRequirement({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.requirementCode,
    required this.requirementName,
    required this.description,
    required this.type,
    required this.category,
    required this.priority,
    required this.status,
    this.regulatoryBody,
    this.standard,
    this.effectiveDate,
    this.expiryDate,
    required this.controls,
    this.documentation,
    this.isApplicable = true,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        requirementCode,
        requirementName,
        description,
        type,
        category,
        priority,
        status,
        regulatoryBody,
        standard,
        effectiveDate,
        expiryDate,
        controls,
        documentation,
        isApplicable,
      ];

  /// Check if requirement is active
  bool get isActive => status == CommonStatus.active;

  /// Check if requirement is expired
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }
}

/// Compliance control entity
class ComplianceControl extends BaseEntity {
  final String controlCode;
  final String controlName;
  final String description;
  final ControlType type;
  final ControlFrequency frequency;
  final String? responsibleRole;
  final String? responsiblePerson;
  final DateTime? lastExecuted;
  final DateTime? nextDue;
  final ControlStatus status;
  final String? evidence;
  final List<String> attachments;

  const ComplianceControl({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.controlCode,
    required this.controlName,
    required this.description,
    required this.type,
    required this.frequency,
    this.responsibleRole,
    this.responsiblePerson,
    this.lastExecuted,
    this.nextDue,
    required this.status,
    this.evidence,
    this.attachments = const [],
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        controlCode,
        controlName,
        description,
        type,
        frequency,
        responsibleRole,
        responsiblePerson,
        lastExecuted,
        nextDue,
        status,
        evidence,
        attachments,
      ];

  /// Check if control is overdue
  bool get isOverdue {
    if (nextDue == null) return false;
    return DateTime.now().isAfter(nextDue!);
  }

  /// Check if control is due soon
  bool get isDueSoon {
    if (nextDue == null) return false;
    final daysUntilDue = nextDue!.difference(DateTime.now()).inDays;
    return daysUntilDue <= 7 && daysUntilDue > 0;
  }
}

/// Audit finding entity
class AuditFinding extends BaseEntity {
  final String auditId;
  final String findingNumber;
  final String title;
  final String description;
  final FindingType type;
  final FindingSeverity severity;
  final FindingStatus status;
  final String? requirementId;
  final String? controlId;
  final String location;
  final String detectedBy;
  final DateTime detectedAt;
  final String? rootCause;
  final String? correctiveAction;
  final String? preventiveAction;
  final String? assignedTo;
  final DateTime? dueDate;
  final DateTime? resolvedAt;
  final List<String> attachments;

  const AuditFinding({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.auditId,
    required this.findingNumber,
    required this.title,
    required this.description,
    required this.type,
    required this.severity,
    required this.status,
    this.requirementId,
    this.controlId,
    required this.location,
    required this.detectedBy,
    required this.detectedAt,
    this.rootCause,
    this.correctiveAction,
    this.preventiveAction,
    this.assignedTo,
    this.dueDate,
    this.resolvedAt,
    this.attachments = const [],
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        auditId,
        findingNumber,
        title,
        description,
        type,
        severity,
        status,
        requirementId,
        controlId,
        location,
        detectedBy,
        detectedAt,
        rootCause,
        correctiveAction,
        preventiveAction,
        assignedTo,
        dueDate,
        resolvedAt,
        attachments,
      ];

  /// Check if finding is resolved
  bool get isResolved => status == FindingStatus.resolved;

  /// Check if finding is overdue
  bool get isOverdue {
    if (dueDate == null) return false;
    return DateTime.now().isAfter(dueDate!) && !isResolved;
  }

  /// Get resolution time
  Duration? get resolutionTime {
    if (resolvedAt == null) return null;
    return resolvedAt!.difference(detectedAt);
  }
}

/// Certification entity
class Certification extends BaseEntity {
  final String certificationNumber;
  final String certificationName;
  final CertificationType type;
  final String issuingBody;
  final DateTime issuedDate;
  final DateTime expiryDate;
  final CertificationStatus status;
  final String? scope;
  final List<String> coveredProducts;
  final List<String> coveredProcesses;
  final String? certificateFile;
  final DateTime? lastAuditDate;
  final DateTime? nextAuditDate;
  final List<String> attachments;

  const Certification({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.certificationNumber,
    required this.certificationName,
    required this.type,
    required this.issuingBody,
    required this.issuedDate,
    required this.expiryDate,
    required this.status,
    this.scope,
    this.coveredProducts = const [],
    this.coveredProcesses = const [],
    this.certificateFile,
    this.lastAuditDate,
    this.nextAuditDate,
    this.attachments = const [],
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        certificationNumber,
        certificationName,
        type,
        issuingBody,
        issuedDate,
        expiryDate,
        status,
        scope,
        coveredProducts,
        coveredProcesses,
        certificateFile,
        lastAuditDate,
        nextAuditDate,
        attachments,
      ];

  /// Check if certification is valid
  bool get isValid => status == CertificationStatus.valid && !isExpired;

  /// Check if certification is expired
  bool get isExpired => DateTime.now().isAfter(expiryDate);

  /// Check if certification is expiring soon
  bool get isExpiringSoon {
    final daysUntilExpiry = expiryDate.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 90 && daysUntilExpiry > 0;
  }

  /// Get days until expiry
  int get daysUntilExpiry => expiryDate.difference(DateTime.now()).inDays;
}

/// Audit result entity
class AuditResult extends Equatable {
  final ComplianceResultType overallResult;
  final double complianceScore;
  final int totalRequirements;
  final int compliantRequirements;
  final int nonCompliantRequirements;
  final int totalFindings;
  final int criticalFindings;
  final int majorFindings;
  final int minorFindings;
  final String? resultSummary;
  final Map<String, dynamic> detailedResults;

  const AuditResult({
    required this.overallResult,
    required this.complianceScore,
    required this.totalRequirements,
    required this.compliantRequirements,
    required this.nonCompliantRequirements,
    required this.totalFindings,
    required this.criticalFindings,
    required this.majorFindings,
    required this.minorFindings,
    this.resultSummary,
    this.detailedResults = const {},
  });

  @override
  List<Object?> get props => [
        overallResult,
        complianceScore,
        totalRequirements,
        compliantRequirements,
        nonCompliantRequirements,
        totalFindings,
        criticalFindings,
        majorFindings,
        minorFindings,
        resultSummary,
        detailedResults,
      ];

  /// Get compliance rate percentage
  double get complianceRate {
    if (totalRequirements == 0) return 0.0;
    return (compliantRequirements / totalRequirements) * 100;
  }

  /// Get non-compliance rate
  double get nonComplianceRate {
    if (totalRequirements == 0) return 0.0;
    return (nonCompliantRequirements / totalRequirements) * 100;
  }
}

// Enums

/// Audit type enum
enum AuditType {
  internal,
  external,
  customer,
  regulatory,
  certification,
}

/// Audit scope enum
enum AuditScope {
  full,
  partial,
  followUp,
  surveillance,
}

/// Audit status enum
enum AuditStatus {
  scheduled,
  inProgress,
  completed,
  cancelled,
  postponed,
}

/// Compliance result type enum
enum ComplianceResultType {
  compliant,
  nonCompliant,
  partiallyCompliant,
  pending,
}

/// Requirement type enum
enum RequirementType {
  mandatory,
  optional,
  recommended,
  contractual,
}

/// Requirement category enum
enum RequirementCategory {
  quality,
  safety,
  environmental,
  social,
  ethical,
}

/// Requirement priority enum
enum RequirementPriority {
  critical,
  high,
  medium,
  low,
}

/// Control type enum
enum ControlType {
  preventive,
  detective,
  corrective,
  directive,
}

/// Control frequency enum
enum ControlFrequency {
  daily,
  weekly,
  monthly,
  quarterly,
  annually,
  asNeeded,
}

/// Control status enum
enum ControlStatus {
  active,
  inactive,
  pending,
  overdue,
}

/// Finding type enum
enum FindingType {
  nonConformity,
  observation,
  opportunity,
  positive,
}

/// Finding severity enum
enum FindingSeverity {
  critical,
  major,
  minor,
}

/// Finding status enum
enum FindingStatus {
  open,
  inProgress,
  resolved,
  closed,
  verified,
}

/// Certification type enum
enum CertificationType {
  iso9001,
  iso14001,
  iso45001,
  oeko_tex,
  gots,
  bci,
  cradle2cradle,
  custom,
}

/// Certification type extension
extension CertificationTypeExtension on CertificationType {
  String get displayName {
    switch (this) {
      case CertificationType.iso9001:
        return 'ISO 9001';
      case CertificationType.iso14001:
        return 'ISO 14001';
      case CertificationType.iso45001:
        return 'ISO 45001';
      case CertificationType.oeko_tex:
        return 'OEKO-TEX';
      case CertificationType.gots:
        return 'GOTS';
      case CertificationType.bci:
        return 'BCI';
      case CertificationType.cradle2cradle:
        return 'Cradle to Cradle';
      case CertificationType.custom:
        return 'Custom';
    }
  }

  String get value => name;
}

/// Certification status enum
enum CertificationStatus {
  valid,
  expired,
  suspended,
  revoked,
  pending,
}
