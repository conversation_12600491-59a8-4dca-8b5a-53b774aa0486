part of 'quality_bloc.dart';




/// Base quality event
abstract class QualityEvent extends Equatable {
  const QualityEvent();

  @override
  List<Object?> get props => [];
}

/// Load quality inspections
class LoadQualityInspectionsRequested extends QualityEvent {
  final QualityInspectionFilter? filter;
  final PaginationParams? pagination;

  const LoadQualityInspectionsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Refresh quality inspections
class RefreshQualityInspectionsRequested extends QualityEvent {
  const RefreshQualityInspectionsRequested();
}

/// Load quality inspection details
class LoadQualityInspectionDetailsRequested extends QualityEvent {
  final String inspectionId;

  const LoadQualityInspectionDetailsRequested(this.inspectionId);

  @override
  List<Object?> get props => [inspectionId];
}

/// Create quality inspection
class CreateQualityInspectionRequested extends QualityEvent {
  final CreateQualityInspectionRequest request;

  const CreateQualityInspectionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update quality inspection
class UpdateQualityInspectionRequested extends QualityEvent {
  final UpdateQualityInspectionRequest request;

  const UpdateQualityInspectionRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Start quality inspection
class StartQualityInspectionRequested extends QualityEvent {
  final String inspectionId;

  const StartQualityInspectionRequested(this.inspectionId);

  @override
  List<Object?> get props => [inspectionId];
}

/// Complete quality inspection
class CompleteQualityInspectionRequested extends QualityEvent {
  final String inspectionId;
  final UpdateQualityInspectionRequest request;

  const CompleteQualityInspectionRequested(
    this.inspectionId,
    this.request,
  );

  @override
  List<Object?> get props => [inspectionId, request];
}

/// Update checkpoint result
class UpdateCheckpointResultRequested extends QualityEvent {
  final String checkpointId;
  final UpdateCheckpointResultRequest request;

  const UpdateCheckpointResultRequested(
    this.checkpointId,
    this.request,
  );

  @override
  List<Object?> get props => [checkpointId, request];
}

/// Add quality defect
class AddQualityDefectRequested extends QualityEvent {
  final AddQualityDefectRequest request;

  const AddQualityDefectRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update quality defect
class UpdateQualityDefectRequested extends QualityEvent {
  final UpdateQualityDefectRequest request;

  const UpdateQualityDefectRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Resolve quality defect
class ResolveQualityDefectRequested extends QualityEvent {
  final String defectId;
  final ResolveQualityDefectRequest request;

  const ResolveQualityDefectRequested(
    this.defectId,
    this.request,
  );

  @override
  List<Object?> get props => [defectId, request];
}

/// Load quality defects
class LoadQualityDefectsRequested extends QualityEvent {
  final QualityDefectFilter? filter;
  final PaginationParams? pagination;

  const LoadQualityDefectsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load quality standards
class LoadQualityStandardsRequested extends QualityEvent {
  final QualityStandardFilter? filter;
  final PaginationParams? pagination;

  const LoadQualityStandardsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Create quality standard
class CreateQualityStandardRequested extends QualityEvent {
  final CreateQualityStandardRequest request;

  const CreateQualityStandardRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Approve quality standard
class ApproveQualityStandardRequested extends QualityEvent {
  final String standardId;
  final String approverId;

  const ApproveQualityStandardRequested(
    this.standardId,
    this.approverId,
  );

  @override
  List<Object?> get props => [standardId, approverId];
}

/// Load quality statistics
class LoadQualityStatisticsRequested extends QualityEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? departmentId;

  const LoadQualityStatisticsRequested({
    this.startDate,
    this.endDate,
    this.departmentId,
  });

  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

/// Load quality trends
class LoadQualityTrendsRequested extends QualityEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? productId;

  const LoadQualityTrendsRequested({
    this.startDate,
    this.endDate,
    this.productId,
  });

  @override
  List<Object?> get props => [startDate, endDate, productId];
}

/// Load defect analysis
class LoadDefectAnalysisRequested extends QualityEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? categoryId;

  const LoadDefectAnalysisRequested({
    this.startDate,
    this.endDate,
    this.categoryId,
  });

  @override
  List<Object?> get props => [startDate, endDate, categoryId];
}

/// Search quality inspections
class SearchQualityInspectionsRequested extends QualityEvent {
  final String query;
  final QualityInspectionFilter? filter;
  final PaginationParams? pagination;

  const SearchQualityInspectionsRequested(
    this.query, {
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Load pending inspections
class LoadPendingInspectionsRequested extends QualityEvent {
  final PaginationParams? pagination;

  const LoadPendingInspectionsRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Load failed inspections
class LoadFailedInspectionsRequested extends QualityEvent {
  final PaginationParams? pagination;

  const LoadFailedInspectionsRequested({this.pagination});

  @override
  List<Object?> get props => [pagination];
}

/// Filter quality inspections
class FilterQualityInspectionsRequested extends QualityEvent {
  final QualityInspectionFilter filter;
  final PaginationParams? pagination;

  const FilterQualityInspectionsRequested(
    this.filter, {
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Clear quality state
class ClearQualityState extends QualityEvent {
  const ClearQualityState();
}

/// Load more quality inspections (pagination)
class LoadMoreQualityInspectionsRequested extends QualityEvent {
  const LoadMoreQualityInspectionsRequested();
}

/// Sort quality inspections
class SortQualityInspectionsRequested extends QualityEvent {
  final String sortBy;
  final bool ascending;

  const SortQualityInspectionsRequested(this.sortBy, this.ascending);

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Select quality inspection
class SelectQualityInspectionRequested extends QualityEvent {
  final String inspectionId;

  const SelectQualityInspectionRequested(this.inspectionId);

  @override
  List<Object?> get props => [inspectionId];
}

/// Change quality view mode
class ChangeQualityViewModeRequested extends QualityEvent {
  final QualityViewMode viewMode;

  const ChangeQualityViewModeRequested(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

/// Quality view mode enum
enum QualityViewMode {
  list,
  kanban,
  timeline,
  dashboard,
}

/// Quality view mode extension
extension QualityViewModeExtension on QualityViewMode {
  String get displayName {
    switch (this) {
      case QualityViewMode.list:
        return 'List';
      case QualityViewMode.kanban:
        return 'Kanban';
      case QualityViewMode.timeline:
        return 'Timeline';
      case QualityViewMode.dashboard:
        return 'Dashboard';
    }
  }

  IconData get icon {
    switch (this) {
      case QualityViewMode.list:
        return Icons.list;
      case QualityViewMode.kanban:
        return Icons.view_kanban;
      case QualityViewMode.timeline:
        return Icons.timeline;
      case QualityViewMode.dashboard:
        return Icons.dashboard;
    }
  }
}
