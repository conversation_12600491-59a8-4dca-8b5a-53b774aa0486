import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/custom_search_bar.dart';
import '../../../../shared/widgets/custom_filter_chip.dart';
import '../../domain/entities/employee.dart';
import '../bloc/employee_bloc.dart';

/// Real-time employee list widget with search and filter capabilities
class RealTimeEmployeeList extends StatefulWidget {
  final EmployeeFilterCriteria? initialFilter;
  final Function(Employee)? onEmployeeTap;
  final Function(Employee)? onEmployeeEdit;
  final Function(Employee)? onEmployeeDelete;
  final VoidCallback? onAddEmployee;

  const RealTimeEmployeeList({
    Key? key,
    this.initialFilter,
    this.onEmployeeTap,
    this.onEmployeeEdit,
    this.onEmployeeDelete,
    this.onAddEmployee,
  }) : super(key: key);

  @override
  State<RealTimeEmployeeList> createState() => _RealTimeEmployeeListState();
}

class _RealTimeEmployeeListState extends State<RealTimeEmployeeList> {
  final _searchController = TextEditingController();
  EmployeeFilterCriteria? _currentFilter;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.initialFilter;
    
    // Start real-time listening
    context.read<EmployeeBloc>().add(StartEmployeeListeningRequested(
      filter: _currentFilter,
    ));
  }

  @override
  void dispose() {
    _searchController.dispose();
    // Stop real-time listening
    context.read<EmployeeBloc>().add(const StopEmployeeListeningRequested());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: BlocBuilder<EmployeeBloc, EmployeeState>(
              builder: (context, state) {
                if (state is EmployeeLoading) {
                  return Container(
                    color: Colors.grey[50],
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text(
                            'Loading employees...',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                } else if (state is EmployeesRealTimeUpdated) {
                  return _buildEmployeeList(state.employees);
                } else if (state is EmployeesLoaded) {
                  return _buildEmployeeList(state.employees);
                } else if (state is EmployeeSearchResults) {
                  return _buildEmployeeList(state.employees);
                } else if (state is EmployeeError) {
                  return _buildErrorWidget(state.message);
                } else {
                  return Container(
                    color: Colors.grey[50],
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No employees found',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add your first employee to get started',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                          const SizedBox(height: 24),
                          if (widget.onAddEmployee != null)
                            ElevatedButton.icon(
                              onPressed: widget.onAddEmployee,
                              icon: const Icon(Icons.add),
                              label: const Text('Add Employee'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: CustomSearchBar(
                  controller: _searchController,
                  hintText: 'Search employees...',
                  onChanged: _handleSearch,
                  onClear: () {
                    _searchController.clear();
                    _handleSearch('');
                  },
                ),
              ),
              const SizedBox(width: 16),
              if (widget.onAddEmployee != null)
                ElevatedButton.icon(
                  onPressed: widget.onAddEmployee,
                  icon: const Icon(Icons.add),
                  label: const Text('Add'),
                ),
            ],
          ),
          const SizedBox(height: 16),
          _buildFilterChips(),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          CustomFilterChip(
            label: 'All',
            isSelected: _currentFilter == null,
            onSelected: (selected) {
              if (selected) {
                _applyFilter(null);
              }
            },
          ),
          const SizedBox(width: 8),
          ...Department.values.map((dept) => Padding(
            padding: const EdgeInsets.only(right: 8),
            child: CustomFilterChip(
              label: dept.displayName,
              isSelected: _currentFilter?.department == dept,
              onSelected: (selected) {
                if (selected) {
                  _applyFilter(EmployeeFilterCriteria(department: dept));
                } else {
                  _applyFilter(null);
                }
              },
            ),
          )),
          const SizedBox(width: 16),
          CustomFilterChip(
            label: 'Active',
            isSelected: _currentFilter?.isActive == true,
            onSelected: (selected) {
              if (selected) {
                _applyFilter(const EmployeeFilterCriteria(isActive: true));
              } else {
                _applyFilter(null);
              }
            },
          ),
          const SizedBox(width: 8),
          CustomFilterChip(
            label: 'Inactive',
            isSelected: _currentFilter?.isActive == false,
            onSelected: (selected) {
              if (selected) {
                _applyFilter(const EmployeeFilterCriteria(isActive: false));
              } else {
                _applyFilter(null);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeList(List<Employee> employees) {
    if (employees.isEmpty) {
      return _buildEmptyState();
    }

    // Filter employees based on search query
    final filteredEmployees = _searchQuery.isEmpty
        ? employees
        : employees.where((employee) {
            final searchLower = _searchQuery.toLowerCase();
            return employee.fullName.toLowerCase().contains(searchLower) ||
                   employee.email.toLowerCase().contains(searchLower) ||
                   employee.employeeId.toLowerCase().contains(searchLower) ||
                   employee.role.displayName.toLowerCase().contains(searchLower) ||
                   employee.department.displayName.toLowerCase().contains(searchLower);
          }).toList();

    return RefreshIndicator(
      onRefresh: () async {
        context.read<EmployeeBloc>().add(RefreshEmployeesRequested(
          filter: _currentFilter,
        ));
      },
      child: ListView.builder(
        itemCount: filteredEmployees.length,
        itemBuilder: (context, index) {
          final employee = filteredEmployees[index];
          return _buildEmployeeCard(employee);
        },
      ),
    );
  }

  Widget _buildEmployeeCard(Employee employee) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(employee.status),
          child: Text(
            employee.initials,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          employee.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${employee.role.displayName} • ${employee.department.displayName}'),
            Text(
              'ID: ${employee.employeeId} • ${employee.email}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatusBadge(employee.status, employee.isActive),
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(value, employee),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'view',
                  child: ListTile(
                    leading: Icon(Icons.visibility),
                    title: Text('View Details'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Edit'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                if (employee.isActive)
                  const PopupMenuItem(
                    value: 'deactivate',
                    child: ListTile(
                      leading: Icon(Icons.block, color: Colors.orange),
                      title: Text('Deactivate'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  )
                else
                  const PopupMenuItem(
                    value: 'activate',
                    child: ListTile(
                      leading: Icon(Icons.check_circle, color: Colors.green),
                      title: Text('Activate'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('Delete'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => widget.onEmployeeTap?.call(employee),
      ),
    );
  }

  Widget _buildStatusBadge(CommonStatus status, bool isActive) {
    Color color;
    String text;

    if (!isActive) {
      color = Colors.grey;
      text = 'Inactive';
    } else {
      switch (status) {
        case CommonStatus.active:
          color = Colors.green;
          text = 'Active';
          break;
        case CommonStatus.inactive:
          color = Colors.orange;
          text = 'Inactive';
          break;
        case CommonStatus.pending:
          color = Colors.blue;
          text = 'Pending';
          break;
        case CommonStatus.suspended:
          color = Colors.red;
          text = 'Suspended';
          break;
        case CommonStatus.archived:
          color = Colors.grey;
          text = 'Archived';
          break;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No employees found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'Try adjusting your search or filters'
                : 'Add your first employee to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          if (widget.onAddEmployee != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: widget.onAddEmployee,
              icon: const Icon(Icons.add),
              label: const Text('Add Employee'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading employees',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.red[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.read<EmployeeBloc>().add(RefreshEmployeesRequested(
                filter: _currentFilter,
              ));
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _handleSearch(String query) {
    setState(() => _searchQuery = query);
    
    if (query.isNotEmpty) {
      context.read<EmployeeBloc>().add(SearchEmployeesRequested(
        query,
        filter: _currentFilter,
      ));
    } else {
      context.read<EmployeeBloc>().add(StartEmployeeListeningRequested(
        filter: _currentFilter,
      ));
    }
  }

  void _applyFilter(EmployeeFilterCriteria? filter) {
    setState(() => _currentFilter = filter);
    
    if (_searchQuery.isNotEmpty) {
      context.read<EmployeeBloc>().add(SearchEmployeesRequested(
        _searchQuery,
        filter: filter,
      ));
    } else {
      context.read<EmployeeBloc>().add(StartEmployeeListeningRequested(
        filter: filter,
      ));
    }
  }

  void _handleMenuAction(String action, Employee employee) {
    switch (action) {
      case 'view':
        widget.onEmployeeTap?.call(employee);
        break;
      case 'edit':
        widget.onEmployeeEdit?.call(employee);
        break;
      case 'activate':
        context.read<EmployeeBloc>().add(ActivateEmployeeRequested(employee.id));
        break;
      case 'deactivate':
        _showDeactivateDialog(employee);
        break;
      case 'delete':
        _showDeleteDialog(employee);
        break;
    }
  }

  void _showDeactivateDialog(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deactivate Employee'),
        content: Text('Are you sure you want to deactivate ${employee.fullName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<EmployeeBloc>().add(DeactivateEmployeeRequested(
                employee.id,
                reason: 'Deactivated by user',
              ));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Employee'),
        content: Text('Are you sure you want to delete ${employee.fullName}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onEmployeeDelete?.call(employee);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(CommonStatus status) {
    switch (status) {
      case CommonStatus.active:
        return Colors.green;
      case CommonStatus.inactive:
        return Colors.orange;
      case CommonStatus.pending:
        return Colors.blue;
      case CommonStatus.suspended:
        return Colors.red;
      case CommonStatus.archived:
        return Colors.grey;
    }
  }
}
