import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { UserRole, UserStatus } from '../types/user.types';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();
const auth = admin.auth();

/**
 * Cloud Function triggered when a new user is created
 * Sets up user profile and assigns default role
 */
export const onUserCreate = functions.auth.user().onCreate(async (user) => {
  try {
    const userData = {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || '',
      emailVerified: user.emailVerified,
      phoneNumber: user.phoneNumber || null,
      photoURL: user.photoURL || null,
      role: UserRole.OPERATOR, // Default role
      status: UserStatus.PENDING, // Requires admin approval
      profile: {
        firstName: '',
        lastName: '',
        employeeId: await generateEmployeeId(),
        department: '',
        position: '',
        hireDate: null,
        phoneNumber: user.phoneNumber || '',
        address: '',
        emergencyContact: {
          name: '',
          phone: '',
          relationship: '',
        },
      },
      permissions: getDefaultPermissions(UserRole.OPERATOR),
      departmentIds: [],
      currentDepartmentId: null,
      lastLoginAt: null,
      lastActiveAt: admin.firestore.FieldValue.serverTimestamp(),
      metadata: {
        createdBy: 'system',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        version: 1,
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    // Save user data to Firestore
    await db.collection('users').doc(user.uid).set(userData);

    // Set custom claims for role-based access
    await auth.setCustomUserClaims(user.uid, {
      role: UserRole.OPERATOR,
      status: UserStatus.PENDING,
      permissions: getDefaultPermissions(UserRole.OPERATOR),
    });

    // Send welcome email (optional)
    await sendWelcomeEmail(user.email!, userData.profile.firstName || 'User');

    // Log user creation
    await logUserActivity(user.uid, 'USER_CREATED', {
      email: user.email,
      role: UserRole.OPERATOR,
    });

    console.log(`User created successfully: ${user.uid}`);
  } catch (error) {
    console.error('Error creating user:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create user profile');
  }
});

/**
 * Cloud Function triggered when a user is deleted
 * Cleans up user data and logs the deletion
 */
export const onUserDelete = functions.auth.user().onDelete(async (user) => {
  try {
    // Archive user data instead of deleting
    const userDoc = await db.collection('users').doc(user.uid).get();
    if (userDoc.exists) {
      const userData = userDoc.data();
      
      // Move to archived users collection
      await db.collection('archivedUsers').doc(user.uid).set({
        ...userData,
        deletedAt: admin.firestore.FieldValue.serverTimestamp(),
        deletedBy: 'system',
      });

      // Remove from active users
      await db.collection('users').doc(user.uid).delete();
    }

    // Log user deletion
    await logUserActivity(user.uid, 'USER_DELETED', {
      email: user.email,
    });

    console.log(`User deleted successfully: ${user.uid}`);
  } catch (error) {
    console.error('Error deleting user:', error);
  }
});

/**
 * Update user role and permissions
 */
export const updateUserRole = functions.https.onCall(async (data, context) => {
  // Check if user is authenticated and has admin privileges
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const adminUid = context.auth.uid;
  const { userId, newRole, updatedBy } = data;

  try {
    // Verify admin has permission to update roles
    const adminDoc = await db.collection('users').doc(adminUid).get();
    const adminData = adminDoc.data();
    
    if (!adminData || adminData.role !== UserRole.ADMIN) {
      throw new functions.https.HttpsError('permission-denied', 'Only admins can update user roles');
    }

    // Get current user data
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User not found');
    }

    const userData = userDoc.data()!;
    const oldRole = userData.role;

    // Update user role and permissions
    const newPermissions = getDefaultPermissions(newRole);
    
    await db.collection('users').doc(userId).update({
      role: newRole,
      permissions: newPermissions,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      'metadata.updatedBy': updatedBy || adminUid,
      'metadata.version': admin.firestore.FieldValue.increment(1),
    });

    // Update custom claims
    await auth.setCustomUserClaims(userId, {
      role: newRole,
      status: userData.status,
      permissions: newPermissions,
    });

    // Log role change
    await logUserActivity(userId, 'ROLE_UPDATED', {
      oldRole,
      newRole,
      updatedBy: updatedBy || adminUid,
    });

    // Send notification to user about role change
    await sendRoleChangeNotification(userData.email, oldRole, newRole);

    return { success: true, message: 'User role updated successfully' };
  } catch (error) {
    console.error('Error updating user role:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update user role');
  }
});

/**
 * Update user status (active, inactive, suspended, pending)
 */
export const updateUserStatus = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const adminUid = context.auth.uid;
  const { userId, newStatus, reason, updatedBy } = data;

  try {
    // Verify admin permissions
    const adminDoc = await db.collection('users').doc(adminUid).get();
    const adminData = adminDoc.data();
    
    if (!adminData || adminData.role !== UserRole.ADMIN) {
      throw new functions.https.HttpsError('permission-denied', 'Only admins can update user status');
    }

    // Get current user data
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User not found');
    }

    const userData = userDoc.data()!;
    const oldStatus = userData.status;

    // Update user status
    await db.collection('users').doc(userId).update({
      status: newStatus,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      'metadata.updatedBy': updatedBy || adminUid,
      'metadata.version': admin.firestore.FieldValue.increment(1),
    });

    // Update custom claims
    await auth.setCustomUserClaims(userId, {
      role: userData.role,
      status: newStatus,
      permissions: userData.permissions,
    });

    // If user is suspended or deactivated, revoke all refresh tokens
    if (newStatus === UserStatus.SUSPENDED || newStatus === UserStatus.INACTIVE) {
      await auth.revokeRefreshTokens(userId);
    }

    // Log status change
    await logUserActivity(userId, 'STATUS_UPDATED', {
      oldStatus,
      newStatus,
      reason,
      updatedBy: updatedBy || adminUid,
    });

    // Send notification to user about status change
    await sendStatusChangeNotification(userData.email, oldStatus, newStatus, reason);

    return { success: true, message: 'User status updated successfully' };
  } catch (error) {
    console.error('Error updating user status:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update user status');
  }
});

/**
 * Update user's last active timestamp
 */
export const updateLastActive = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;

  try {
    await db.collection('users').doc(userId).update({
      lastActiveAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return { success: true };
  } catch (error) {
    console.error('Error updating last active:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update last active time');
  }
});

// Helper functions

async function generateEmployeeId(): Promise<string> {
  const prefix = 'EMP';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
}

function getDefaultPermissions(role: UserRole): string[] {
  switch (role) {
    case UserRole.ADMIN:
      return [
        'users.create', 'users.read', 'users.update', 'users.delete',
        'orders.create', 'orders.read', 'orders.update', 'orders.delete',
        'production.create', 'production.read', 'production.update', 'production.delete',
        'inventory.create', 'inventory.read', 'inventory.update', 'inventory.delete',
        'quality.create', 'quality.read', 'quality.update', 'quality.delete',
        'reports.read', 'settings.read', 'settings.update',
      ];
    case UserRole.MANAGER:
      return [
        'orders.create', 'orders.read', 'orders.update',
        'production.create', 'production.read', 'production.update',
        'inventory.read', 'quality.read', 'reports.read',
      ];
    case UserRole.SUPERVISOR:
      return [
        'orders.read', 'production.read', 'production.update',
        'quality.read', 'quality.update', 'reports.read',
      ];
    case UserRole.OPERATOR:
      return [
        'orders.read', 'production.read', 'quality.read',
      ];
    case UserRole.INSPECTOR:
      return [
        'orders.read', 'production.read', 'quality.create', 'quality.read', 'quality.update',
      ];
    case UserRole.VIEWER:
      return [
        'orders.read', 'production.read', 'inventory.read', 'quality.read', 'reports.read',
      ];
    default:
      return ['orders.read'];
  }
}

async function logUserActivity(userId: string, action: string, details: any) {
  try {
    await db.collection('userActivityLogs').add({
      userId,
      action,
      details,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error('Error logging user activity:', error);
  }
}

async function sendWelcomeEmail(email: string, firstName: string) {
  // Implement email sending logic here
  console.log(`Sending welcome email to ${email} for ${firstName}`);
}

async function sendRoleChangeNotification(email: string, oldRole: UserRole, newRole: UserRole) {
  // Implement email notification logic here
  console.log(`Sending role change notification to ${email}: ${oldRole} -> ${newRole}`);
}

async function sendStatusChangeNotification(email: string, oldStatus: UserStatus, newStatus: UserStatus, reason?: string) {
  // Implement email notification logic here
  console.log(`Sending status change notification to ${email}: ${oldStatus} -> ${newStatus}, reason: ${reason}`);
}
