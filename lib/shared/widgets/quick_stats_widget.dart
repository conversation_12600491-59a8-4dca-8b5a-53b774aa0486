import 'package:flutter/material.dart';

import '../../core/theme/app_text_styles.dart';

/// Quick stat data model
class QuickStat {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String? trend;
  final bool isPositiveTrend;
  final VoidCallback? onTap;
  
  /// Optional custom gradient. If null, a gradient will be generated from [color].
  final List<Color>? gradientColors;

  const QuickStat({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.trend,
    this.isPositiveTrend = true,
    this.onTap,
    this.gradientColors,
  });
}

/// Quick stats widget for displaying key metrics
class QuickStatsWidget extends StatelessWidget {
  final List<QuickStat> stats;
  final int crossAxisCount;
  final double spacing;
  final double childAspectRatio; // Allow size control

  const QuickStatsWidget({
    super.key,
    required this.stats,
    this.crossAxisCount = 4,
    this.spacing = 8,
    this.childAspectRatio = 1.6,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return QuickStatCard(stat: stat);
      },
    );
  }
}

/// Individual quick stat card with gradient background
class QuickStatCard extends StatelessWidget {
  final QuickStat stat;

  const QuickStatCard({
    super.key,
    required this.stat,
  });

  @override
  Widget build(BuildContext context) {
    final gradient = _resolveGradient(stat);

    return Card(
      elevation: 3,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: stat.onTap,
        child: Ink(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradient,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        stat.icon,
                        color: Colors.white,
                        size: 22,
                      ),
                    ),
                    const Spacer(),
                    if (stat.trend != null) _buildTrendIndicator(),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  stat.value,
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.w800,
                    color: Colors.white,
                  ),
                ),
                Text(
                  stat.title,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white70,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Color> _resolveGradient(QuickStat stat) {
    if (stat.gradientColors != null && stat.gradientColors!.length >= 2) {
      return stat.gradientColors!;
    }
    // Generate a pleasing gradient from the base color using HSL adjustments
    final base = HSLColor.fromColor(stat.color);
    final c1 = base.withLightness((base.lightness * 0.85).clamp(0.0, 1.0)).toColor();
    final c2 = base.withLightness((base.lightness * 0.65).clamp(0.0, 1.0)).toColor();
    return [c1, c2];
  }

  Widget _buildTrendIndicator() {
    if (stat.trend == null) return const SizedBox.shrink();

    final trendColor = stat.isPositiveTrend ? Colors.greenAccent : Colors.redAccent;
    final trendIcon = stat.isPositiveTrend ? Icons.trending_up : Icons.trending_down;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.12),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.18)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            trendIcon,
            size: 14,
            color: trendColor,
          ),
          const SizedBox(width: 4),
          Text(
            stat.trend!,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Horizontal quick stats widget
class HorizontalQuickStatsWidget extends StatelessWidget {
  final List<QuickStat> stats;
  final double spacing;
  final double height;
  final double itemWidth;

  const HorizontalQuickStatsWidget({
    super.key,
    required this.stats,
    this.spacing = 16,
    this.height = 130,
    this.itemWidth = 180,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: stats.length,
        separatorBuilder: (context, index) => SizedBox(width: spacing),
        itemBuilder: (context, index) {
          final stat = stats[index];
          return SizedBox(
            width: itemWidth,
            child: QuickStatCard(stat: stat),
          );
        },
      ),
    );
  }
}

/// Compact quick stat widget for smaller spaces
class CompactQuickStatWidget extends StatelessWidget {
  final QuickStat stat;

  const CompactQuickStatWidget({
    super.key,
    required this.stat,
  });

  @override
  Widget build(BuildContext context) {
    final gradient = _resolveGradient(stat);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: stat.onTap,
        child: Ink(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradient,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    stat.icon,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        stat.value,
                        style: AppTextStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.w800,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        stat.title,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white70,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                if (stat.trend != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.12),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      stat.trend!,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Color> _resolveGradient(QuickStat stat) {
    if (stat.gradientColors != null && stat.gradientColors!.length >= 2) {
      return stat.gradientColors!;
    }
    final base = HSLColor.fromColor(stat.color);
    final c1 = base.withLightness((base.lightness * 0.85).clamp(0.0, 1.0)).toColor();
    final c2 = base.withLightness((base.lightness * 0.65).clamp(0.0, 1.0)).toColor();
    return [c1, c2];
  }
}
