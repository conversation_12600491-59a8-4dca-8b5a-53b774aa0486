import 'package:flutter/material.dart';

/// A card widget that displays resource management statistics
class ResourceStatisticsCard extends StatelessWidget {
  final ResourceStatistics statistics;
  final VoidCallback? onTap;

  const ResourceStatisticsCard({
    Key? key,
    required this.statistics,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Resource Statistics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildStatisticRow(
                context,
                'Total Workers',
                statistics.totalWorkers.toString(),
                Icons.people,
                Colors.blue,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Active Workers',
                statistics.activeWorkers.toString(),
                Icons.person,
                Colors.green,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Total Facilities',
                statistics.totalFacilities.toString(),
                Icons.business,
                Colors.orange,
              ),
              const SizedBox(height: 12),
              _buildStatisticRow(
                context,
                'Departments',
                statistics.totalDepartments.toString(),
                Icons.domain,
                Colors.purple,
              ),
              const SizedBox(height: 16),
              _buildUtilizationIndicator(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUtilizationIndicator(BuildContext context) {
    final utilizationRate = statistics.totalWorkers > 0
        ? statistics.activeWorkers / statistics.totalWorkers
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Resource Utilization',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${(utilizationRate * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: utilizationRate,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            utilizationRate >= 0.8 ? Colors.green : utilizationRate >= 0.6 ? Colors.orange : Colors.red,
          ),
        ),
      ],
    );
  }
}

// Placeholder class - this should be defined in resource_entities.dart
class ResourceStatistics {
  final int totalWorkers;
  final int activeWorkers;
  final int totalFacilities;
  final int totalDepartments;

  const ResourceStatistics({
    required this.totalWorkers,
    required this.activeWorkers,
    required this.totalFacilities,
    required this.totalDepartments,
  });
}