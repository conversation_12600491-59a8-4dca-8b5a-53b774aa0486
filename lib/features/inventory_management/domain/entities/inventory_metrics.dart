import 'package:equatable/equatable.dart';

/// Represents metrics and analytics data for inventory management
class InventoryMetrics extends Equatable {
  /// Total number of unique items in inventory
  final int totalItems;
  
  /// Total value of all inventory items
  final double totalInventoryValue;
  
  /// Number of items that are below their reorder point
  final int lowStockItems;
  
  /// Number of items that are out of stock
  final int outOfStockItems;
  
  /// Number of items that are overstocked
  final int overstockedItems;
  
  /// Inventory turnover ratio
  final double inventoryTurnoverRatio;
  
  /// Average days to sell inventory
  final double daysSalesOfInventory;
  
  /// Map of category to count of items
  final Map<String, int> itemsByCategory;
  
  /// Map of status to count of items
  final Map<String, int> itemsByStatus;
  
  /// List of top selling items with their quantities
  final List<Map<String, dynamic>> topSellingItems;
  
  /// List of slow moving items with their days in inventory
  final List<Map<String, dynamic>> slowMovingItems;
  
  /// Timestamp when the metrics were calculated
  final DateTime timestamp;

  const InventoryMetrics({
    required this.totalItems,
    required this.totalInventoryValue,
    required this.lowStockItems,
    required this.outOfStockItems,
    required this.overstockedItems,
    required this.inventoryTurnoverRatio,
    required this.daysSalesOfInventory,
    required this.itemsByCategory,
    required this.itemsByStatus,
    required this.topSellingItems,
    required this.slowMovingItems,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [
        totalItems,
        totalInventoryValue,
        lowStockItems,
        outOfStockItems,
        overstockedItems,
        inventoryTurnoverRatio,
        daysSalesOfInventory,
        itemsByCategory,
        itemsByStatus,
        topSellingItems,
        slowMovingItems,
        timestamp,
      ];

  /// Creates a copy of the metrics with optional overrides
  InventoryMetrics copyWith({
    int? totalItems,
    double? totalInventoryValue,
    int? lowStockItems,
    int? outOfStockItems,
    int? overstockedItems,
    double? inventoryTurnoverRatio,
    double? daysSalesOfInventory,
    Map<String, int>? itemsByCategory,
    Map<String, int>? itemsByStatus,
    List<Map<String, dynamic>>? topSellingItems,
    List<Map<String, dynamic>>? slowMovingItems,
    DateTime? timestamp,
  }) {
    return InventoryMetrics(
      totalItems: totalItems ?? this.totalItems,
      totalInventoryValue: totalInventoryValue ?? this.totalInventoryValue,
      lowStockItems: lowStockItems ?? this.lowStockItems,
      outOfStockItems: outOfStockItems ?? this.outOfStockItems,
      overstockedItems: overstockedItems ?? this.overstockedItems,
      inventoryTurnoverRatio: inventoryTurnoverRatio ?? this.inventoryTurnoverRatio,
      daysSalesOfInventory: daysSalesOfInventory ?? this.daysSalesOfInventory,
      itemsByCategory: itemsByCategory ?? Map.from(this.itemsByCategory),
      itemsByStatus: itemsByStatus ?? Map.from(this.itemsByStatus),
      topSellingItems: topSellingItems ?? List.from(this.topSellingItems),
      slowMovingItems: slowMovingItems ?? List.from(this.slowMovingItems),
      timestamp: timestamp ?? this.timestamp,
    );
  }
  
  /// Creates an empty instance of InventoryMetrics
  factory InventoryMetrics.empty() {
    return  InventoryMetrics(
      totalItems: 0,
      totalInventoryValue: 0.0,
      lowStockItems: 0,
      outOfStockItems: 0,
      overstockedItems: 0,
      inventoryTurnoverRatio: 0.0,
      daysSalesOfInventory: 0.0,
      itemsByCategory: {},
      itemsByStatus: {},
      topSellingItems: [],
      slowMovingItems: [],
      timestamp: DateTime.now(),
    );
  }
}
