# HM Collection Web Deployment Guide

This guide provides comprehensive instructions for building and deploying the HM Collection Flutter web application.

## 🚀 Quick Start

### Prerequisites

- Flutter SDK (latest stable version)
- Dart SDK (included with Flutter)
- Web browser (Chrome recommended for development)
- Git

### Build for Web

#### Option 1: Using Build Scripts

**Linux/macOS:**
```bash
chmod +x scripts/build_web.sh
./scripts/build_web.sh
```

**Windows:**
```cmd
scripts\build_web.bat
```

#### Option 2: Manual Build

```bash
# Clean and get dependencies
flutter clean
flutter pub get

# Build for web
flutter build web --release --web-renderer canvaskit
```

## 📦 Deployment Options

### 1. Static File Hosting

#### Netlify Deployment

1. Build the web app:
   ```bash
   flutter build web --release
   ```

2. Deploy to Netlify:
   - Drag and drop the `build/web` folder to Netlify
   - Or connect your Git repository and set:
     - Build command: `flutter build web --release`
     - Publish directory: `build/web`

3. Configure redirects by adding `_redirects` file to `build/web`:
   ```
   /*    /index.html   200
   ```

#### Vercel Deployment

1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```

2. Deploy:
   ```bash
   vercel --prod
   ```

3. Configure `vercel.json`:
   ```json
   {
     "buildCommand": "flutter build web --release",
     "outputDirectory": "build/web",
     "routes": [
       { "handle": "filesystem" },
       { "src": "/.*", "dest": "/index.html" }
     ]
   }
   ```

#### Firebase Hosting

1. Install Firebase CLI:
   ```bash
   npm install -g firebase-tools
   ```

2. Initialize Firebase:
   ```bash
   firebase init hosting
   ```

3. Configure `firebase.json`:
   ```json
   {
     "hosting": {
       "public": "build/web",
       "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
       "rewrites": [
         {
           "source": "**",
           "destination": "/index.html"
         }
       ]
     }
   }
   ```

4. Deploy:
   ```bash
   flutter build web --release
   firebase deploy
   ```

### 2. Docker Deployment

#### Build Docker Image

```bash
# Build the Docker image
docker build -f Dockerfile.web -t hm-collection-web .

# Run the container
docker run -p 80:80 hm-collection-web
```

#### Docker Compose Deployment

```bash
# Start all services
docker-compose -f docker-compose.web.yml up -d

# Start with specific profiles
docker-compose -f docker-compose.web.yml --profile traefik up -d
docker-compose -f docker-compose.web.yml --profile database up -d
docker-compose -f docker-compose.web.yml --profile monitoring up -d
```

### 3. Traditional Web Server

#### Apache Configuration

1. Copy `build/web` contents to your web root
2. Use the provided `.htaccess` file for proper routing
3. Ensure mod_rewrite is enabled

#### Nginx Configuration

1. Copy `build/web` contents to your web root
2. Use the provided nginx configuration files
3. Configure server block for your domain

#### IIS Configuration

1. Copy `build/web` contents to your web root
2. Use the provided `web.config` file
3. Ensure URL Rewrite module is installed

## 🔧 Configuration

### Environment Variables

Create environment-specific builds:

```bash
# Development
flutter build web --dart-define=ENVIRONMENT=development

# Staging
flutter build web --dart-define=ENVIRONMENT=staging

# Production
flutter build web --dart-define=ENVIRONMENT=production
```

### Base URL Configuration

For subdirectory deployment:

```bash
flutter build web --base-href="/hm-collection/"
```

### Web Renderer Options

```bash
# CanvasKit (recommended for complex apps)
flutter build web --web-renderer canvaskit

# HTML (smaller bundle size)
flutter build web --web-renderer html

# Auto (Flutter chooses based on device)
flutter build web --web-renderer auto
```

## 🌐 Domain and SSL Setup

### Custom Domain

1. Update `web/CNAME` file with your domain
2. Configure DNS records:
   - A record pointing to your server IP
   - CNAME record for www subdomain

### SSL Certificate

#### Let's Encrypt with Certbot

```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### Cloudflare SSL

1. Add your domain to Cloudflare
2. Update nameservers
3. Enable SSL/TLS encryption mode: "Full (strict)"

## 📊 Performance Optimization

### Build Optimizations

```bash
flutter build web \
  --release \
  --web-renderer canvaskit \
  --source-maps \
  --tree-shake-icons \
  --dart-define=FLUTTER_WEB_USE_SKIA=true \
  --dart-define=FLUTTER_WEB_AUTO_DETECT=true
```

### CDN Configuration

1. Upload static assets to CDN
2. Update asset URLs in build
3. Configure proper cache headers

### Compression

Enable gzip/brotli compression on your server:

**Nginx:**
```nginx
gzip on;
gzip_types text/css application/javascript application/json;
```

**Apache:**
```apache
LoadModule deflate_module modules/mod_deflate.so
AddOutputFilterByType DEFLATE text/css application/javascript
```

## 🔍 Monitoring and Analytics

### Performance Monitoring

1. Enable Core Web Vitals tracking
2. Set up Google Analytics
3. Configure error tracking (Sentry, Bugsnag)

### Health Checks

Add health check endpoint:
```dart
// In your main.dart or router
app.get('/health', (req, res) => res.send('OK'));
```

## 🚨 Troubleshooting

### Common Issues

#### CORS Errors
- Configure proper CORS headers on your server
- Use proxy configuration for API calls

#### Routing Issues
- Ensure server redirects all routes to index.html
- Check base-href configuration

#### Large Bundle Size
- Use tree-shaking: `--tree-shake-icons`
- Analyze bundle: `flutter build web --analyze-size`
- Consider code splitting

#### Performance Issues
- Use CanvasKit renderer for better performance
- Enable compression on server
- Optimize images and assets

### Debug Mode

For debugging in production:
```bash
flutter build web --profile --source-maps
```

## 📋 Deployment Checklist

### Pre-deployment

- [ ] Run tests: `flutter test`
- [ ] Build successfully: `flutter build web --release`
- [ ] Test locally: Serve build/web directory
- [ ] Check responsive design
- [ ] Verify all routes work
- [ ] Test on different browsers

### Production Deployment

- [ ] Configure proper domain and SSL
- [ ] Set up monitoring and analytics
- [ ] Configure backup strategy
- [ ] Set up CI/CD pipeline
- [ ] Test performance metrics
- [ ] Configure error tracking

### Post-deployment

- [ ] Verify all functionality works
- [ ] Check performance metrics
- [ ] Monitor error rates
- [ ] Test user flows
- [ ] Update documentation

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy Web App
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.x'
      - run: flutter pub get
      - run: flutter build web --release
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./build/web
```

## 📞 Support

For deployment issues:
1. Check the troubleshooting section
2. Review server logs
3. Test in different browsers
4. Contact the development team

## 🔗 Useful Links

- [Flutter Web Documentation](https://docs.flutter.dev/platform-integration/web)
- [Web Deployment Guide](https://docs.flutter.dev/deployment/web)
- [Performance Best Practices](https://docs.flutter.dev/perf/web-performance)
- [PWA Configuration](https://docs.flutter.dev/platform-integration/web/pwa)
