import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:hm_collection/features/production_planning/domain/models/create_production_order_request.dart';
import 'package:hm_collection/features/production_planning/domain/models/update_production_order_request.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/production_entities.dart';
import '../../domain/repositories/production_repository.dart';
import '../bloc/production_bloc.dart';
import '../widgets/production_app_bar.dart';
import '../widgets/production_kanban_view.dart';
import '../widgets/production_list_view.dart';
import '../widgets/production_order_form.dart';
import '../widgets/production_search_bar.dart';
import '../widgets/production_statistics_card.dart';
import '../widgets/production_timeline_view.dart';

/// Production planning page
class ProductionPage extends StatefulWidget {
  const ProductionPage({super.key});

  @override
  State<ProductionPage> createState() => _ProductionPageState();
}

class _ProductionPageState extends State<ProductionPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ProductionFilterCriteria? _currentFilter;
  String? _searchQuery;
  ProductionViewMode _viewMode = ProductionViewMode.list;
  late ProductionRepository _productionRepository;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _productionRepository = GetIt.instance<ProductionRepository>();

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductionBloc>().add(const LoadProductionOrdersRequested());
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ProductionAppBar(
        title: 'Production Planning',
        onSearchPressed: () => _handleSearch(''),
        onFilterPressed: _showFilterDialog,
        onAddPressed: _navigateToCreateProductionOrder,
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllProductionOrdersTab(),
                _buildScheduledOrdersTab(),
                _buildInProgressOrdersTab(),
                _buildOverdueOrdersTab(),
                _buildUrgentOrdersTab(),
                _buildCompletedOrdersTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreateProductionOrder,
        tooltip: 'Create Production Order',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(text: 'All Orders'),
          Tab(text: 'Scheduled'),
          Tab(text: 'In Progress'),
          Tab(text: 'Overdue'),
          Tab(text: 'Urgent'),
          Tab(text: 'Completed'),
        ],
        onTap: _handleTabChanged,
      ),
    );
  }

  Widget _buildAllProductionOrdersTab() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            _buildStatisticsSection(),
            Expanded(
              child: _buildProductionOrdersList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildScheduledOrdersTab() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildProductionOrdersList(state, status: ProductionStatus.scheduled),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInProgressOrdersTab() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildProductionOrdersList(state, status: ProductionStatus.inProgress),
            ),
          ],
        );
      },
    );
  }

  Widget _buildOverdueOrdersTab() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildOverdueOrdersList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUrgentOrdersTab() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildUrgentOrdersList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCompletedOrdersTab() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildProductionOrdersList(state, status: ProductionStatus.completed),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: ProductionSearchBar(
        onSearch: _handleSearch,
        onClear: _clearSearch,
        initialQuery: _searchQuery,
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        if (state is ProductionOrdersLoaded && state.orders.isNotEmpty) {
          // Calculate statistics from orders
          final orders = state.orders;
          final totalOrders = orders.length;
          final completedOrders = orders
              .where((o) => o.status == ProductionStatus.completed)
              .length;
          final inProgressOrders = orders
              .where((o) => o.status == ProductionStatus.inProgress)
              .length;
          final delayedOrders = orders.where((o) => o.isOverdue).length;

          // Average completion time (in hours) for orders having both actual start and end
          final completedWithTimes = orders
              .where((o) => o.actualStartDate != null && o.actualEndDate != null)
              .toList();
          double averageCompletionTime = 0.0;
          if (completedWithTimes.isNotEmpty) {
            final totalHours = completedWithTimes.fold<double>(
              0.0,
              (sum, o) => sum +
                  o.actualEndDate!
                      .difference(o.actualStartDate!)
                      .inHours
                      .toDouble(),
            );
            averageCompletionTime = totalHours / completedWithTimes.length;
          }

          // On-time delivery rate among completed orders
          double onTimeDeliveryRate = 0.0;
          if (completedOrders > 0) {
            final onTimeCount = orders.where((o) =>
                o.status == ProductionStatus.completed &&
                o.actualEndDate != null &&
                // Allow equality: actual end on or before planned end
                !o.actualEndDate!.isAfter(o.plannedEndDate)).length;
            onTimeDeliveryRate = onTimeCount / completedOrders;
          }

          // Approximate resource utilization from allocations
          int totalAllocated = 0;
          int totalUsed = 0;
          for (final o in orders) {
            for (final a in o.resourceAllocations) {
              totalAllocated += a.allocatedQuantity;
              totalUsed += a.usedQuantity;
            }
          }
          final resourceUtilization =
              totalAllocated > 0 ? (totalUsed / totalAllocated) : 0.0;

          final statistics = ProductionStatistics(
            totalOrders: totalOrders,
            completedOrders: completedOrders,
            inProgressOrders: inProgressOrders,
            delayedOrders: delayedOrders,
            averageCompletionTime: averageCompletionTime,
            onTimeDeliveryRate: onTimeDeliveryRate,
            resourceUtilization: resourceUtilization,
          );

          return Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            child: ProductionStatisticsCard(
              statistics: statistics,
              onTap: () => _handleStatisticTap('overview'),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildProductionOrdersList(ProductionState state, {ProductionStatus? status}) {
    if (state is ProductionLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is ProductionError) {
      return _buildErrorWidget(state.message);
    }

    if (state is ProductionOrdersLoaded) {
      var orders = state.orders;
      
      // Filter by status if specified
      if (status != null) {
        orders = orders.where((order) => order.status == status).toList();
      }

      if (orders.isEmpty) {
        return _buildEmptyState(status);
      }

      return RefreshIndicator(
        onRefresh: () async => _refreshProductionOrders(),
        child: _buildOrdersView(orders, state),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildOverdueOrdersList(ProductionState state) {
    if (state is ProductionLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is ProductionError) {
      return _buildErrorWidget(state.message);
    }

    if (state is OverdueProductionOrdersLoaded) {
      if (state.orders.isEmpty) {
        return _buildEmptyState(null, isOverdue: true);
      }

      return RefreshIndicator(
        onRefresh: () async => _loadOverdueOrders(),
        child: _buildOrdersView(state.orders, null),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildUrgentOrdersList(ProductionState state) {
    if (state is ProductionLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is ProductionError) {
      return _buildErrorWidget(state.message);
    }

    if (state is UrgentProductionOrdersLoaded) {
      if (state.orders.isEmpty) {
        return _buildEmptyState(null, isUrgent: true);
      }

      return RefreshIndicator(
        onRefresh: () async => _loadUrgentOrders(),
        child: _buildOrdersView(state.orders, null),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildOrdersView(List<ProductionOrder> orders, ProductionOrdersLoaded? state) {
    switch (_viewMode) {
      case ProductionViewMode.list:
        return ProductionListView(
          orders: orders,
          selectedOrderIds: state?.selectedOrderIds ?? [],
          onOrderTap: _navigateToOrderDetailsById,
          onOrderLongPress: _handleOrderLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStatusChanged: _handleStatusChanged,
          onLoadMore: _loadMoreOrders,
          hasMore: state?.pagination?.hasNextPage ?? false,
          isLoading: state?.isRefreshing ?? false,
        );
      case ProductionViewMode.kanban:
        return ProductionKanbanView(
          orders: orders,
          onOrderTap: _navigateToOrderDetails,
          onStatusChanged: (order, status) => _handleStatusChanged(order.id, status),
        );
      case ProductionViewMode.timeline:
        return ProductionTimelineView(
          orders: orders,
          onOrderTap: _navigateToOrderDetails,
        );
      case ProductionViewMode.calendar:
        return _buildCalendarView(orders);
    }
  }

  Widget _buildCalendarView(List<ProductionOrder> orders) {
    return const Center(
      child: Text(
        'Calendar view coming soon',
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading production orders',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _refreshProductionOrders,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ProductionStatus? status, {bool isOverdue = false, bool isUrgent = false}) {
    String title;
    String subtitle;
    IconData icon;

    if (isOverdue) {
      title = 'No Overdue Orders';
      subtitle = 'Great! All production orders are on track.';
      icon = Icons.check_circle_outline;
    } else if (isUrgent) {
      title = 'No Urgent Orders';
      subtitle = 'No urgent production orders at the moment.';
      icon = Icons.priority_high;
    } else if (status != null) {
      title = 'No ${status.displayName} Orders';
      subtitle = 'No production orders found with ${status.displayName.toLowerCase()} status.';
      icon = Icons.inbox_outlined;
    } else {
      title = 'No Production Orders Found';
      subtitle = 'Create your first production order to get started.';
      icon = Icons.precision_manufacturing_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (!isOverdue && !isUrgent) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _navigateToCreateProductionOrder,
              child: const Text('Create Production Order'),
            ),
          ],
        ],
      ),
    );
  }

  void _handleTabChanged(int index) {
    switch (index) {
      case 0:
        context.read<ProductionBloc>().add(const LoadProductionOrdersRequested());
        break;
      case 1:
        context.read<ProductionBloc>().add(const FilterProductionOrdersRequested(
          ProductionFilterCriteria(status: ProductionStatus.scheduled),
        ));
        break;
      case 2:
        context.read<ProductionBloc>().add(const FilterProductionOrdersRequested(
          ProductionFilterCriteria(status: ProductionStatus.inProgress),
        ));
        break;
      case 3:
        _loadOverdueOrders();
        break;
      case 4:
        _loadUrgentOrders();
        break;
      case 5:
        context.read<ProductionBloc>().add(const FilterProductionOrdersRequested(
          ProductionFilterCriteria(status: ProductionStatus.completed),
        ));
        break;
    }
  }

  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query.isEmpty ? null : query;
    });

    if (query.isEmpty) {
      context.read<ProductionBloc>().add(LoadProductionOrdersRequested(filter: _currentFilter));
    } else {
      context.read<ProductionBloc>().add(SearchProductionOrdersRequested(
        query,
        filter: _currentFilter,
      ));
    }
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = null;
    });
    context.read<ProductionBloc>().add(LoadProductionOrdersRequested(filter: _currentFilter));
  }

  void _showFilterDialog() async {
    // TODO: Implement filter dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Filter dialog coming soon')),
    );
  }

  void _handleViewModeChanged(ProductionViewMode viewMode) {
    setState(() {
      _viewMode = viewMode;
    });
    context.read<ProductionBloc>().add(ChangeProductionViewModeRequested(viewMode));
  }

  void _handleStatisticTap(String statistic) {
    // Handle statistic tap for filtering or navigation
    switch (statistic) {
      case 'scheduled':
        _tabController.animateTo(1);
        break;
      case 'in_progress':
        _tabController.animateTo(2);
        break;
      case 'overdue':
        _tabController.animateTo(3);
        break;
      case 'urgent':
        _tabController.animateTo(4);
        break;
    }
  }

  void _navigateToOrderDetails(ProductionOrder order) {
    final productionBloc = context.read<ProductionBloc>();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (builderContext) => BlocProvider.value(
          value: productionBloc,
          child: Scaffold(
            appBar: AppBar(title: Text('Edit Order: ${order.orderNumber}')),
            body: ProductionOrderForm(
              initialOrder: order,
              onSave: (updatedOrder) {
                productionBloc.add(UpdateProductionOrderRequested(updatedOrder as UpdateProductionOrderRequest));
                Navigator.of(builderContext).pop();
              },
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToOrderDetailsById(String orderId) {
    // Get the order from current state and navigate to edit
    final state = context.read<ProductionBloc>().state;
    if (state is ProductionOrdersLoaded) {
      final order = state.orders.firstWhere(
        (o) => o.id == orderId,
        orElse: () => throw Exception('Order not found'),
      );
      _navigateToOrderDetails(order);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Unable to load order details')),
      );
    }
  }

  void _navigateToCreateProductionOrder() {
    final productionBloc = context.read<ProductionBloc>();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (builderContext) => BlocProvider.value(
          value: productionBloc,
          child: Scaffold(
            appBar: AppBar(title: const Text('Create Production Order')),
            body: ProductionOrderForm(
              onSave: (order) {
                productionBloc.add(CreateProductionOrderRequested(order as CreateProductionOrderRequest));
                Navigator.of(builderContext).pop();
                productionBloc.add(const RefreshProductionOrdersRequested());
              },
            ),
          ),
        ),
      ),
    );
  }

  void _handleOrderLongPress(String orderId) {
    context.read<ProductionBloc>().add(SelectProductionOrderRequested(orderId));
  }

  void _handleSelectionChanged(List<String> selectedIds) {
    // Handle selection change for bulk operations
  }

  void _handleStatusChanged(String orderId, ProductionStatus status) {
    context.read<ProductionBloc>().add(UpdateProductionOrderStatusRequested(orderId, status));
  }

  void _refreshProductionOrders() {
    context.read<ProductionBloc>().add(const RefreshProductionOrdersRequested());
  }

  void _loadMoreOrders() {
    context.read<ProductionBloc>().add(const LoadMoreProductionOrdersRequested());
  }

  void _loadOverdueOrders() {
    context.read<ProductionBloc>().add(const LoadOverdueProductionOrdersRequested());
  }

  void _loadUrgentOrders() {
    context.read<ProductionBloc>().add(const LoadUrgentProductionOrdersRequested());
  }
}
