import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hm_collection/features/department/data/models/department_model.dart';


abstract class DepartmentRemoteDataSource {
  Future<List<DepartmentModel>> getDepartments();
}

class DepartmentRemoteDataSourceImpl implements DepartmentRemoteDataSource {
  final FirebaseFirestore firestore;

  DepartmentRemoteDataSourceImpl({required this.firestore});

  @override
  Future<List<DepartmentModel>> getDepartments() async {
    try {
      final snapshot = await firestore.collection('departments').get();
      return snapshot.docs.map((doc) => DepartmentModel.fromFirestore(doc)).toList();
    } catch (e) {
      // Handle exceptions, e.g., log them or rethrow as a custom exception
      throw Exception('Failed to fetch departments: $e');
    }
  }
}
