import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:hm_collection/features/financial_management/presentation/widgets/profit_loss_chart.dart' as profit_loss;
import 'package:hm_collection/features/financial_management/presentation/widgets/cost_analysis_chart.dart' as cost_analysis;

import '../../../../core/auth/widgets/permission_guard.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/models/pagination.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../analytics/domain/entities/analytics_entities.dart';
import '../../domain/entities/financial_entities.dart';
import '../bloc/financial_bloc.dart';
import '../widgets/budget_overview_card.dart';
import '../widgets/cost_analysis_chart.dart';
import '../widgets/financial_kpi_cards.dart';
import '../widgets/financial_summary_card.dart' as summary_card;
import '../widgets/profit_loss_chart.dart';


/// Financial dashboard page
class FinancialDashboardPage extends StatefulWidget {
  const FinancialDashboardPage({super.key});

  @override
  State<FinancialDashboardPage> createState() => _FinancialDashboardPageState();
}

class _FinancialDashboardPageState extends State<FinancialDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late FinancialBloc _financialBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _financialBloc = GetIt.instance<FinancialBloc>();
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _financialBloc.close();
    super.dispose();
  }

  void _loadInitialData() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    // Load financial summary
    _financialBloc.add(GetFinancialSummaryRequested(
      dateRange: DateRange(
        startDate: startOfMonth,
        endDate: endOfMonth,
      ),
    ));

    // Load recent transactions
    _financialBloc.add(const GetTransactionsRequested(
      pagination: PaginationParams(page: 0, perPage: 10),
    ));

    // Load budgets
    _financialBloc.add(const GetBudgetsRequested(
      pagination: PaginationParams(page: 0, perPage: 5),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _financialBloc,
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'Financial Management',
          actions: [
            PermissionGuard(
              permission: 'financial.reports.generate',
              child: IconButton(
                icon: const Icon(Icons.assessment),
                onPressed: () => _navigateToReports(),
                tooltip: 'Generate Reports',
              ),
            ),
            PermissionGuard(
              permission: 'financial.transactions.create',
              child: IconButton(
                icon: const Icon(Icons.add),
                onPressed: () => _navigateToCreateTransaction(),
                tooltip: 'Add Transaction',
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildTransactionsTab(),
                  _buildBudgetsTab(),
                  _buildReportsTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: PermissionGuard(
          permission: 'financial.transactions.create',
          child: FloatingActionButton(
            onPressed: () => _navigateToCreateTransaction(),
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.receipt_long),
            text: 'Transactions',
          ),
          Tab(
            icon: Icon(Icons.account_balance_wallet),
            text: 'Budgets',
          ),
          Tab(
            icon: Icon(Icons.assessment),
            text: 'Reports',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Financial Overview',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Financial KPI Cards
          BlocBuilder<FinancialBloc, FinancialState>(
            builder: (context, state) {
              if (state is FinancialSummaryLoaded) {
                return FinancialKpiCards(summary: state.summary);
              } else if (state is FinancialLoading) {
                return const LoadingWidget();
              }
              return const SizedBox.shrink();
            },
          ),
          
          const SizedBox(height: 24),
          
          // Financial Summary Card
          BlocBuilder<FinancialBloc, FinancialState>(
            builder: (context, state) {
              if (state is FinancialSummaryLoaded) {
                return summary_card.FinancialSummaryCard(summary: state.summary);
              }
              return const SizedBox.shrink();
            },
          ),
          
          const SizedBox(height: 24),
          
          Row(
            children: [
              // Budget Overview
              Expanded(
                child: BlocBuilder<FinancialBloc, FinancialState>(
                  builder: (context, state) {
                    if (state is FinancialBudgetsLoaded) {
                      return BudgetOverviewCard(budgets: state.budgets);
                    }
                    return const LoadingWidget();
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Cost Analysis Chart
              Expanded(
                child: CostAnalysisChart(
                  onRefresh: () => _loadCostAnalysis(),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Profit & Loss Chart
          profit_loss.ProfitLossChart(
            onRefresh: () => _loadProfitLossData(), data: [],
          ),
          
          const SizedBox(height: 24),
          
          // Recent Transactions
          Text(
            'Recent Transactions',
            style: AppTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          BlocBuilder<FinancialBloc, FinancialState>(
            builder: (context, state) {
              if (state is FinancialTransactionsLoaded) {
                return RecentTransactionsList(
                  transactions: state.transactions.take(5).toList(),
                  onViewAll: () => _tabController.animateTo(1),
                );
              }
              return const LoadingWidget();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return PermissionGuard(
      permission: 'financial.transactions.read',
      fallback: const Center(
        child: Text('You do not have permission to view transactions'),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Transactions',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.filter_list),
                      onPressed: () => _showTransactionFilters(),
                      tooltip: 'Filter Transactions',
                    ),
                    IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: () => _showTransactionSearch(),
                      tooltip: 'Search Transactions',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: BlocBuilder<FinancialBloc, FinancialState>(
                builder: (context, state) {
                  if (state is FinancialTransactionsLoaded) {
                    return _buildTransactionsList(state);
                  } else if (state is FinancialLoading) {
                    return const LoadingWidget();
                  } else if (state is FinancialError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: AppColors.error,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading transactions',
                            style: AppTextStyles.bodyLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => _loadInitialData(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }
                  return const Center(
                    child: Text('No transactions available'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetsTab() {
    return PermissionGuard(
      permission: 'financial.budgets.read',
      fallback: const Center(
        child: Text('You do not have permission to view budgets'),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Budgets',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                PermissionGuard(
                  permission: 'financial.budgets.create',
                  child: ElevatedButton.icon(
                    onPressed: () => _navigateToCreateBudget(),
                    icon: const Icon(Icons.add),
                    label: const Text('Create Budget'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: BlocBuilder<FinancialBloc, FinancialState>(
                builder: (context, state) {
                  if (state is FinancialBudgetsLoaded) {
                    return _buildBudgetsList(state);
                  } else if (state is FinancialLoading) {
                    return const LoadingWidget();
                  }
                  return const Center(
                    child: Text('No budgets available'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab() {
    return PermissionGuard(
      permission: 'financial.reports.read',
      fallback: const Center(
        child: Text('You do not have permission to view reports'),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Financial Reports',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                PermissionGuard(
                  permission: 'financial.reports.generate',
                  child: ElevatedButton.icon(
                    onPressed: () => _navigateToGenerateReport(),
                    icon: const Icon(Icons.add),
                    label: const Text('Generate Report'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Quick Report Actions
            _buildQuickReportActions(),

            const SizedBox(height: 24),

            Expanded(
              child: BlocBuilder<FinancialBloc, FinancialState>(
                builder: (context, state) {
                  if (state is FinancialReportsLoaded) {
                    return _buildReportsList(state);
                  } else if (state is FinancialLoading) {
                    return const LoadingWidget();
                  }
                  return const Center(
                    child: Text('No reports available'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList(FinancialTransactionsLoaded state) {
    return ListView.builder(
      itemCount: state.transactions.length,
      itemBuilder: (context, index) {
        final transaction = state.transactions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: transaction.isRevenue 
                  ? AppColors.success.withOpacity(0.1)
                  : AppColors.error.withOpacity(0.1),
              child: Icon(
                transaction.isRevenue ? Icons.arrow_upward : Icons.arrow_downward,
                color: transaction.isRevenue ? AppColors.success : AppColors.error,
              ),
            ),
            title: Text(
              transaction.description,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              '${transaction.category.name} • ${transaction.costCenterName}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${transaction.isRevenue ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: transaction.isRevenue ? AppColors.success : AppColors.error,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStatusColor(transaction.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    transaction.status.name,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: _getStatusColor(transaction.status),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () => _navigateToTransactionDetails(transaction.id),
          ),
        );
      },
    );
  }

  Widget _buildBudgetsList(FinancialBudgetsLoaded state) {
    return ListView.builder(
      itemCount: state.budgets.length,
      itemBuilder: (context, index) {
        final budget = state.budgets[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: budget.metrics.isExceeded 
                  ? AppColors.error.withOpacity(0.1)
                  : AppColors.success.withOpacity(0.1),
              child: Icon(
                budget.metrics.isExceeded ? Icons.warning : Icons.account_balance_wallet,
                color: budget.metrics.isExceeded ? AppColors.error : AppColors.success,
              ),
            ),
            title: Text(
              budget.budgetName,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              '${budget.type.name} • ${budget.period.name}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${budget.totalAmount.toStringAsFixed(0)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${budget.metrics.utilizationPercentage.toStringAsFixed(1)}% used',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: budget.metrics.isExceeded ? AppColors.error : AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            onTap: () => _navigateToBudgetDetails(budget.id),
          ),
        );
      },
    );
  }

  Widget _buildReportsList(FinancialReportsLoaded state) {
    return ListView.builder(
      itemCount: state.reports.length,
      itemBuilder: (context, index) {
        final report = state.reports[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primary.withOpacity(0.1),
              child: Icon(
                Icons.assessment,
                color: AppColors.primary,
              ),
            ),
            title: Text(
              report.reportName,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              '${report.type.name} • ${report.period.name}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getReportStatusColor(report.status as ReportStatus).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                report.status.name,
                style: AppTextStyles.bodySmall.copyWith(
                  color: _getReportStatusColor(report.status as ReportStatus),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            onTap: () => _navigateToReportDetails(report.id),
          ),
        );
      },
    );
  }

  Widget _buildQuickReportActions() {
    return Row(
      children: [
        Expanded(
          child: _buildQuickReportCard(
            'Profit & Loss',
            Icons.trending_up,
            () => _generateProfitLossReport(),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickReportCard(
            'Cost Analysis',
            Icons.pie_chart,
            () => _generateCostAnalysisReport(),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickReportCard(
            'Budget Variance',
            Icons.compare_arrows,
            () => _generateBudgetVarianceReport(),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickReportCard(String title, IconData icon, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: AppColors.primary,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.approved:
      case TransactionStatus.posted:
        return AppColors.success;
      case TransactionStatus.pending:
        return AppColors.warning;
      case TransactionStatus.rejected:
      case TransactionStatus.cancelled:
        return AppColors.error;
      case TransactionStatus.draft:
        return AppColors.textSecondary;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getReportStatusColor(ReportStatus status) {
    switch (status) {
      case ReportStatus.completed:
        return AppColors.success;
      case ReportStatus.generating:
        return AppColors.warning;
      case ReportStatus.failed:
      case ReportStatus.cancelled:
        return AppColors.error;
      case ReportStatus.pending:
      case ReportStatus.scheduled:
        return AppColors.textSecondary;
    }
  }

  void _loadCostAnalysis() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    _financialBloc.add(GetCostAnalysisReportRequested(
      dateRange: DateRange(
        startDate: startOfMonth,
        endDate: endOfMonth,
      ),
    ));
  }

  void _loadProfitLossData() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    _financialBloc.add(GetProfitLossStatementRequested(
      startDate: startOfMonth,
      endDate: endOfMonth,
    ));
  }

  void _navigateToReports() {
    // Navigate to reports page
  }

  void _navigateToCreateTransaction() {
    // Navigate to create transaction page
  }

  void _navigateToCreateBudget() {
    // Navigate to create budget page
  }

  void _navigateToGenerateReport() {
    // Navigate to generate report page
  }

  void _navigateToTransactionDetails(String transactionId) {
    // Navigate to transaction details page
  }

  void _navigateToBudgetDetails(String budgetId) {
    // Navigate to budget details page
  }

  void _navigateToReportDetails(String reportId) {
    // Navigate to report details page
  }

  void _showTransactionFilters() {
    // Show transaction filters dialog
  }

  void _showTransactionSearch() {
    // Show transaction search dialog
  }

  void _generateProfitLossReport() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    _financialBloc.add(GetProfitLossStatementRequested(
      startDate: startOfMonth,
      endDate: endOfMonth,
    ));
  }

  void _generateCostAnalysisReport() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    _financialBloc.add(GetCostAnalysisReportRequested(
      dateRange: DateRange(
        startDate: startOfMonth,
        endDate: endOfMonth,
      ),
    ));
  }

  void _generateBudgetVarianceReport() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    _financialBloc.add(GetBudgetVarianceReportRequested(
      startDate: startOfMonth,
      endDate: endOfMonth,
    ));
  }
}
