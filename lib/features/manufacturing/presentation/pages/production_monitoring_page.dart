import 'package:flutter/material.dart';

import '../../../../core/auth/widgets/permission_guard.dart';
import '../widgets/production_line_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../widgets/production_metrics_widget.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../domain/entities/manufacturing_entities.dart';

/// Production monitoring page for cloth manufacturing
class ProductionMonitoringPage extends StatefulWidget {
  const ProductionMonitoringPage({super.key});

  @override
  State<ProductionMonitoringPage> createState() => _ProductionMonitoringPageState();
}

class _ProductionMonitoringPageState extends State<ProductionMonitoringPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedLineFilter;
  String? _selectedOrderFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Production Monitoring',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
            tooltip: 'Filter Production Lines',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshData(),
            tooltip: 'Refresh Data',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettings(),
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildProductionLinesTab(),
                _buildQualityTab(),
                _buildReportsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        isScrollable: true,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.precision_manufacturing),
            text: 'Production Lines',
          ),
          Tab(
            icon: Icon(Icons.verified),
            text: 'Quality Control',
          ),
          Tab(
            icon: Icon(Icons.assessment),
            text: 'Reports',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Production Overview',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Production Metrics
          const ProductionMetricsWidget(),
          
          const SizedBox(height: 24),
          
          // Real-time Production Status
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Real-time Production Status',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.success.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: AppColors.success,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'LIVE',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.success,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildProductionStatus(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Active Orders
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Active Production Orders',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => _viewAllOrders(),
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildActiveOrders(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Department Performance
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Department Performance Today',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDepartmentPerformance(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductionLinesTab() {
    return PermissionGuard(
      permission: 'manufacturing.production.read',
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Production Lines',
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    if (_selectedLineFilter != null)
                      Chip(
                        label: Text(_selectedLineFilter!),
                        onDeleted: () => setState(() => _selectedLineFilter = null),
                        deleteIcon: const Icon(Icons.close, size: 16),
                      ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () => _refreshData(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Refresh'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: _buildProductionLinesList(),
            ),
          ],
        ),
      ),
      fallback: const Center(
        child: Text('You do not have permission to view production data'),
      ),
    );
  }

  Widget _buildQualityTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quality Control Monitoring',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Quality Metrics
          Row(
            children: [
              Expanded(
                child: _buildQualityCard(
                  'Overall Quality',
                  '96.8%',
                  Icons.verified,
                  AppColors.success,
                  '+1.2% vs yesterday',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQualityCard(
                  'Defect Rate',
                  '3.2%',
                  Icons.error_outline,
                  AppColors.error,
                  '-0.5% improvement',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQualityCard(
                  'Inspections',
                  '245',
                  Icons.search,
                  AppColors.primary,
                  'Today',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQualityCard(
                  'Rework Items',
                  '18',
                  Icons.build,
                  AppColors.warning,
                  'Pending rework',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Quality Issues by Department
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quality Issues by Department',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildQualityIssuesByDepartment(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Recent Quality Alerts
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Recent Quality Alerts',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildQualityAlerts(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Production Reports',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Report Categories
          _buildReportCategory('Daily Reports', [
            'Daily Production Summary',
            'Shift Performance Report',
            'Quality Control Report',
            'Machine Utilization Report',
          ]),
          
          const SizedBox(height: 16),
          
          _buildReportCategory('Weekly Reports', [
            'Weekly Production Analysis',
            'Department Performance Report',
            'Efficiency Trends Report',
            'Cost Analysis Report',
          ]),
          
          const SizedBox(height: 16),
          
          _buildReportCategory('Monthly Reports', [
            'Monthly Production Summary',
            'Quality Metrics Report',
            'Worker Performance Report',
            'Inventory Consumption Report',
          ]),
        ],
      ),
    );
  }

  Widget _buildProductionStatus() {
    final productionLines = [
      {'name': 'Line A - Cutting', 'status': 'Running', 'efficiency': 92.5, 'target': 500, 'actual': 463},
      {'name': 'Line B - Stitching', 'status': 'Running', 'efficiency': 88.3, 'target': 300, 'actual': 265},
      {'name': 'Line C - Finishing', 'status': 'Maintenance', 'efficiency': 0.0, 'target': 200, 'actual': 0},
      {'name': 'Line D - Packing', 'status': 'Running', 'efficiency': 95.1, 'target': 400, 'actual': 380},
    ];

    return Column(
      children: productionLines.map((line) => Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: _getStatusColor(line['status'] as String).withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getStatusColor(line['status'] as String),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    line['name'] as String,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    line['status'] as String,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: _getStatusColor(line['status'] as String),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${line['actual']}/${line['target']}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${line['efficiency']}%',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildActiveOrders() {
    final activeOrders = [
      {'order': 'ORD-2024-001', 'product': 'Cotton T-Shirts', 'quantity': 1000, 'progress': 75, 'deadline': '2 days'},
      {'order': 'ORD-2024-002', 'product': 'Denim Jeans', 'quantity': 500, 'progress': 45, 'deadline': '5 days'},
      {'order': 'ORD-2024-003', 'product': 'Polo Shirts', 'quantity': 750, 'progress': 90, 'deadline': '1 day'},
    ];

    return Column(
      children: activeOrders.map((order) => ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withOpacity(0.1),
          child: Text(
            '${order['progress']}%',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        title: Text('${order['order']} - ${order['product']}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Quantity: ${order['quantity']} pieces'),
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: (order['progress'] as int) / 100,
              backgroundColor: AppColors.surface,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ],
        ),
        trailing: Text(
          'Due in ${order['deadline']}',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        onTap: () => _viewOrderDetails(order['order'] as String),
      )).toList(),
    );
  }

  Widget _buildDepartmentPerformance() {
    final departments = [
      {'name': 'Cutting', 'efficiency': 92.5, 'target': 95.0, 'status': 'Good'},
      {'name': 'Stitching', 'efficiency': 88.3, 'target': 90.0, 'status': 'Below Target'},
      {'name': 'Quality Control', 'efficiency': 96.8, 'target': 95.0, 'status': 'Excellent'},
      {'name': 'Packing', 'efficiency': 91.2, 'target': 90.0, 'status': 'Good'},
      {'name': 'Finishing', 'efficiency': 87.1, 'target': 90.0, 'status': 'Below Target'},
    ];

    return Column(
      children: departments.map((dept) => Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                dept['name'] as String,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: LinearProgressIndicator(
                value: (dept['efficiency'] as double) / 100,
                backgroundColor: AppColors.surface,
                valueColor: AlwaysStoppedAnimation<Color>(
                  (dept['efficiency'] as double) >= (dept['target'] as double)
                      ? AppColors.success
                      : AppColors.warning,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '${dept['efficiency']}%',
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getPerformanceColor(dept['status'] as String).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                dept['status'] as String,
                style: AppTextStyles.bodySmall.copyWith(
                  color: _getPerformanceColor(dept['status'] as String),
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildProductionLinesList() {
    return ListView.builder(
      itemCount: 4,
      itemBuilder: (context, index) {
        return ProductionLineWidget(
          lineId: 'line_${index + 1}',
          lineName: 'Production Line ${String.fromCharCode(65 + index)}',
          onTap: () => _viewLineDetails('line_${index + 1}'),
        );
      },
    );
  }

  Widget _buildQualityCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: AppTextStyles.headlineMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQualityIssuesByDepartment() {
    final qualityData = [
      {'department': 'Cutting', 'issues': 2, 'total': 45, 'rate': 4.4},
      {'department': 'Stitching', 'issues': 5, 'total': 67, 'rate': 7.5},
      {'department': 'Finishing', 'issues': 1, 'total': 34, 'rate': 2.9},
      {'department': 'Packing', 'issues': 0, 'total': 23, 'rate': 0.0},
    ];

    return Column(
      children: qualityData.map((data) => ListTile(
        leading: CircleAvatar(
          backgroundColor: _getQualityRateColor(data['rate'] as double).withOpacity(0.1),
          child: Text(
            '${data['issues']}',
            style: AppTextStyles.bodySmall.copyWith(
              color: _getQualityRateColor(data['rate'] as double),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        title: Text(data['department'] as String),
        subtitle: Text('${data['issues']} issues out of ${data['total']} items'),
        trailing: Text(
          '${data['rate']}%',
          style: AppTextStyles.bodyMedium.copyWith(
            color: _getQualityRateColor(data['rate'] as double),
            fontWeight: FontWeight.w600,
          ),
        ),
      )).toList(),
    );
  }

  Widget _buildQualityAlerts() {
    final alerts = [
      {'type': 'High Defect Rate', 'department': 'Stitching', 'severity': 'High', 'time': '10 min ago'},
      {'type': 'Material Quality Issue', 'department': 'Cutting', 'severity': 'Medium', 'time': '25 min ago'},
      {'type': 'Color Mismatch', 'department': 'Finishing', 'severity': 'Low', 'time': '1 hour ago'},
    ];

    return Column(
      children: alerts.map((alert) => ListTile(
        leading: Icon(
          Icons.warning,
          color: _getSeverityColor(alert['severity'] as String),
        ),
        title: Text(alert['type'] as String),
        subtitle: Text('${alert['department']} • ${alert['time']}'),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getSeverityColor(alert['severity'] as String).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            alert['severity'] as String,
            style: AppTextStyles.bodySmall.copyWith(
              color: _getSeverityColor(alert['severity'] as String),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      )).toList(),
    );
  }

  Widget _buildReportCategory(String title, List<String> reports) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...reports.map((report) => ListTile(
              leading: const Icon(Icons.description),
              title: Text(report),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.visibility),
                    onPressed: () => _viewReport(report),
                    tooltip: 'View Report',
                  ),
                  IconButton(
                    icon: const Icon(Icons.download),
                    onPressed: () => _downloadReport(report),
                    tooltip: 'Download Report',
                  ),
                ],
              ),
              onTap: () => _viewReport(report),
            )),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'running':
        return AppColors.success;
      case 'maintenance':
        return AppColors.warning;
      case 'stopped':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getPerformanceColor(String status) {
    switch (status.toLowerCase()) {
      case 'excellent':
        return AppColors.success;
      case 'good':
        return AppColors.primary;
      case 'below target':
        return AppColors.warning;
      case 'poor':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getQualityRateColor(double rate) {
    if (rate <= 2.0) return AppColors.success;
    if (rate <= 5.0) return AppColors.warning;
    return AppColors.error;
  }

  Color _getSeverityColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'high':
        return AppColors.error;
      case 'medium':
        return AppColors.warning;
      case 'low':
        return AppColors.primary;
      default:
        return AppColors.textSecondary;
    }
  }

  void _showFilterDialog() {
    // Implement filter dialog
  }

  void _refreshData() {
    // Implement data refresh
  }

  void _showSettings() {
    // Implement settings
  }

  void _viewAllOrders() {
    // Navigate to all orders page
  }

  void _viewOrderDetails(String orderId) {
    // Navigate to order details
  }

  void _viewLineDetails(String lineId) {
    // Navigate to production line details
  }

  void _viewReport(String report) {
    // View report
  }

  void _downloadReport(String report) {
    // Download report
  }
}
