import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../../../inventory_management/presentation/pages/denim_purchase_page.dart';
import '../../../inventory_management/presentation/pages/denim_return_page.dart';
import '../../../inventory_management/presentation/pages/denim_stock_tracking_page.dart';
import '../../../inventory_management/presentation/pages/denim_types_master_page.dart';

/// Inventory Manager Dashboard Page - Inventory and material management
class InventoryManagerDashboardPage extends StatelessWidget {
  const InventoryManagerDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Inventory Manager Dashboard'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildInventoryManagerDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildInventoryManagerDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildInventoryOverview(),
          const SizedBox(height: 24),
          _buildInventoryActions(context),
          const SizedBox(height: 24),
          _buildStockAlerts(),
          const SizedBox(height: 24),
          _buildMaterialCategories(),
          const SizedBox(height: 24),
          _buildRecentTransactions(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.amber,
            Colors.amber.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Inventory Manager'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Inventory and material management',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.warehouse,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Inventory Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: [
            DashboardMetricCard(
              title: 'Total Items',
              value: '2,456',
              icon: Icons.inventory,
              color: Colors.blue,
              trend: '+3%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Low Stock Items',
              value: '23',
              icon: Icons.warning,
              color: Colors.red,
              trend: '+5',
              isPositiveTrend: false,
            ),
            DashboardMetricCard(
              title: 'Total Value',
              value: '\$125K',
              icon: Icons.attach_money,
              color: Colors.green,
              trend: '+8%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Categories',
              value: '15',
              icon: Icons.category,
              color: Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInventoryActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Inventory Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            // Denim Inventory Management Section
            DashboardCard(
              title: 'Denim Purchase',
              subtitle: 'Add new denim purchases',
              icon: Icons.add_shopping_cart,
              color: Colors.blue,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const DenimPurchasePage(),
                  ),
                );
              },
            ),
            DashboardCard(
              title: 'Denim Returns',
              subtitle: 'Process damaged returns',
              icon: Icons.assignment_return,
              color: Colors.red,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const DenimReturnPage(),
                  ),
                );
              },
            ),
            DashboardCard(
              title: 'Stock Tracking',
              subtitle: 'Real-time stock monitoring',
              icon: Icons.inventory_2,
              color: Colors.green,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const DenimStockTrackingPage(),
                  ),
                );
              },
            ),
            DashboardCard(
              title: 'Denim Types Master',
              subtitle: 'Manage denim types',
              icon: Icons.category,
              color: Colors.purple,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const DenimTypesMasterPage(),
                  ),
                );
              },
            ),
            // General Inventory Management
            DashboardCard(
              title: 'Add Material',
              subtitle: 'Add new inventory items',
              icon: Icons.add_box,
              color: Colors.orange,
              onTap: () {
                // Navigate to add material
              },
            ),
            DashboardCard(
              title: 'Stock Adjustment',
              subtitle: 'Adjust stock quantities',
              icon: Icons.tune,
              color: Colors.amber,
              onTap: () {
                // Navigate to stock adjustment
              },
            ),
            DashboardCard(
              title: 'Supplier Management',
              subtitle: 'Manage suppliers',
              icon: Icons.business,
              color: Colors.teal,
              onTap: () {
                // Navigate to supplier management
              },
            ),
            DashboardCard(
              title: 'Material Reports',
              subtitle: 'View inventory reports',
              icon: Icons.analytics,
              color: Colors.indigo,
              onTap: () {
                // Navigate to material reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStockAlerts() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.warning,
                  color: Colors.red,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Stock Alerts',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '23 Items',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildAlertItem(
              'Cotton Fabric - White',
              'Only 15 yards left',
              Colors.red,
              Icons.warning,
            ),
            _buildAlertItem(
              'Polyester Thread - Black',
              'Only 8 spools left',
              Colors.orange,
              Icons.error_outline,
            ),
            _buildAlertItem(
              'Buttons - 15mm',
              'Only 50 pieces left',
              Colors.red,
              Icons.warning,
            ),
            _buildAlertItem(
              'Zipper - 12 inch',
              'Only 25 pieces left',
              Colors.orange,
              Icons.error_outline,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(String item, String message, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  message,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios, size: 16),
            onPressed: () {
              // Navigate to item details
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMaterialCategories() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.category,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Material Categories',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to all categories
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildCategoryCard('Fabrics', '456', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCategoryCard('Threads', '234', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCategoryCard('Accessories', '189', Colors.purple),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCategoryCard('Tools', '67', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String category, String count, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            count,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            category,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.history,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Recent Transactions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to all transactions
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTransactionItem(
              'Stock In',
              'Cotton Fabric - Blue (+50 yards)',
              '2 hours ago',
              Colors.green,
              Icons.add_circle,
            ),
            _buildTransactionItem(
              'Stock Out',
              'Polyester Thread - Red (-15 spools)',
              '4 hours ago',
              Colors.red,
              Icons.remove_circle,
            ),
            _buildTransactionItem(
              'Purchase Order',
              'PO-2024-001 created',
              '6 hours ago',
              Colors.blue,
              Icons.shopping_cart,
            ),
            _buildTransactionItem(
              'Stock Transfer',
              'Buttons transferred to Warehouse B',
              '1 day ago',
              Colors.purple,
              Icons.swap_horiz,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(String type, String description, String time, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
