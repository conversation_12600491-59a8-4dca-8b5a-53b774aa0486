import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/biometric_auth_data.dart';
import '../../domain/entities/two_factor_auth_data.dart';
import '../../domain/entities/auth_tokens.dart';
import '../../domain/entities/login_credentials.dart';
import '../../domain/entities/password_reset_confirm_request.dart';
import '../../domain/repositories/auth_repository.dart' show PasswordResetRequest, ChangePasswordRequest;

/// Authentication tokens data model
@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
  createFactory: true,
  createToJson: true,
)
class AuthTokensModel extends AuthTokens {
  const AuthTokensModel({
    required String accessToken,
    required String refreshToken,
    String tokenType = 'Bearer',
    required int expiresIn,
    required DateTime issuedAt,
    required DateTime expiresAt,
    List<String> scopes = const [],
  }) : super(
          accessToken: accessToken,
          refreshToken: refreshToken,
          tokenType: tokenType,
          expiresIn: expiresIn,
          issuedAt: issuedAt,
          expiresAt: expiresAt,
          scopes: scopes,
        );

  /// Factory constructor for JSON deserialization
  factory AuthTokensModel.fromJson(Map<String, dynamic> json) {
    return AuthTokensModel(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
      tokenType: json['token_type'] as String? ?? 'Bearer',
      expiresIn: json['expires_in'] as int,
      issuedAt: DateTime.parse(json['issued_at'] as String),
      expiresAt: DateTime.parse(json['expires_at'] as String),
      scopes: (json['scopes'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
    );
  }

  /// Convert to JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
      'issued_at': issuedAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'scopes': scopes,
    };
  }

  /// Create from domain entity
  factory AuthTokensModel.fromEntity(AuthTokens tokens) {
    return AuthTokensModel(
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      tokenType: tokens.tokenType,
      expiresIn: tokens.expiresIn,
      issuedAt: tokens.issuedAt,
      expiresAt: tokens.expiresAt,
      scopes: tokens.scopes,
    );
  }

  /// Convert to domain entity
  AuthTokens toEntity() {
    return AuthTokens(
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenType: tokenType,
      expiresIn: expiresIn,
      issuedAt: issuedAt,
      expiresAt: expiresAt,
      scopes: scopes,
    );
  }

  /// Copy with new values
  @override
  AuthTokensModel copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    DateTime? issuedAt,
    DateTime? expiresAt,
    List<String>? scopes,
  }) {
    return AuthTokensModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      issuedAt: issuedAt ?? this.issuedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      scopes: scopes ?? this.scopes,
    );
  }
}

/// Login credentials data model
@JsonSerializable()
class LoginCredentialsModel extends LoginCredentials {
  final String? deviceId;
  final String? deviceName;

  const LoginCredentialsModel({
    required String email,
    required String password,
    required bool rememberMe,
    this.deviceId,
    this.deviceName,
  }) : super(
          email: email,
          password: password,
          rememberMe: rememberMe,
        );

  /// Factory constructor for JSON deserialization
  factory LoginCredentialsModel.fromJson(Map<String, dynamic> json) {
    return LoginCredentialsModel(
      email: json['email'] as String,
      password: json['password'] as String,
      rememberMe: json['remember_me'] as bool? ?? false,
      deviceId: json['device_id'] as String?,
      deviceName: json['device_name'] as String?,
    );
  }

  /// Convert to JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'remember_me': rememberMe,
      if (deviceId != null) 'device_id': deviceId,
      if (deviceName != null) 'device_name': deviceName,
    };
  }

  /// Create from domain entity
  factory LoginCredentialsModel.fromEntity(LoginCredentials credentials) {
    return LoginCredentialsModel(
      email: credentials.email,
      password: credentials.password,
      rememberMe: credentials.rememberMe ?? false,
      // deviceId: credentials.deviceId,
      // deviceName: credentials.deviceName,
    );
  }

  /// Convert to domain entity
  LoginCredentials toEntity() {
    return LoginCredentials(
      email: email,  // Using username as email since that's what the model uses
      password: password,
      rememberMe: rememberMe,
    );
  }
}

/// Password change request data model
@JsonSerializable()
class ChangePasswordRequestModel extends ChangePasswordRequest {
  const ChangePasswordRequestModel({
    required String currentPassword,
    required String newPassword,
  }) : super(
          currentPassword: currentPassword,
          newPassword: newPassword,
        );

  /// Factory constructor for JSON deserialization
  factory ChangePasswordRequestModel.fromJson(Map<String, dynamic> json) {
    return ChangePasswordRequestModel(
      currentPassword: json['currentPassword'] as String,
      newPassword: json['newPassword'] as String,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'currentPassword': currentPassword,
      'newPassword': newPassword,
      'confirmPassword': newPassword,
    };
  }

  /// Create from domain entity
  factory ChangePasswordRequestModel.fromEntity(ChangePasswordRequest request) {
    return ChangePasswordRequestModel(
      currentPassword: request.currentPassword,
      newPassword: request.newPassword,
    );
  }

  /// Convert to domain entity
  ChangePasswordRequest toEntity() {
    return ChangePasswordRequest(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
  }
}

/// Password reset request data model
@JsonSerializable()
class PasswordResetRequestModel {
  final String email;

  const PasswordResetRequestModel({
    required this.email,
  });

  /// Factory constructor for JSON deserialization
  factory PasswordResetRequestModel.fromJson(Map<String, dynamic> json) {
    return PasswordResetRequestModel(
      email: json['email'] as String,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }

  /// Create from domain entity
  factory PasswordResetRequestModel.fromEntity(PasswordResetRequest request) {
    return PasswordResetRequestModel(email: request.email);
  }

  /// Convert to domain entity
  PasswordResetRequest toEntity() {
    return PasswordResetRequest(email: email);
  }
}

/// Password reset confirmation data model
@JsonSerializable(
  includeIfNull: false,
  explicitToJson: true,
)
class PasswordResetConfirmRequestModel extends PasswordResetConfirmRequest {
  const PasswordResetConfirmRequestModel({
    required String token,
    required String email,
    required String newPassword,
    required String confirmPassword,
  }) : super(
          token: token,
          email: email,
          newPassword: newPassword,
          confirmPassword: confirmPassword,
        );

  /// Factory constructor for JSON deserialization
  factory PasswordResetConfirmRequestModel.fromJson(Map<String, dynamic> json) {
    return PasswordResetConfirmRequestModel(
      token: json['token'] as String,
      email: json['email'] as String,
      newPassword: json['newPassword'] as String,
      confirmPassword: json['confirmPassword'] as String,
    );
  }

  /// Convert to JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'email': email,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
    };
  }

  /// Create from domain entity
  factory PasswordResetConfirmRequestModel.fromEntity(PasswordResetConfirmRequest confirmation) {
    return PasswordResetConfirmRequestModel(
      token: confirmation.token,
      email: confirmation.email,
      newPassword: confirmation.newPassword,
      confirmPassword: confirmation.confirmPassword,
    );
  }

  /// Convert to domain entity
  PasswordResetConfirmRequest toEntity() {
    return PasswordResetConfirmRequest(
      token: token,
      email: email,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }
}

/// Biometric authentication data model
@JsonSerializable()
class BiometricAuthDataModel extends BiometricAuthData {
  const BiometricAuthDataModel({
    required String userId,
    required String biometricType,
    required String signature,
    required DateTime timestamp,
  }) : super(
          userId: userId,
          biometricType: biometricType,
          signature: signature,
          timestamp: timestamp,
        );

  /// Factory constructor for JSON deserialization
  factory BiometricAuthDataModel.fromJson(Map<String, dynamic> json) {
    return BiometricAuthDataModel(
      userId: json['userId'] as String,
      biometricType: json['biometricType'] as String,
      signature: json['signature'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// Convert to JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': super.userId,
      'biometricType': super.biometricType,
      'signature': super.signature,
      'timestamp': super.timestamp.toIso8601String(),
    };
  }

  /// Create from domain entity
  factory BiometricAuthDataModel.fromEntity(BiometricAuthData data) {
    return BiometricAuthDataModel(
      userId: data.userId,
      biometricType: data.biometricType,
      signature: data.signature,
      timestamp: data.timestamp,
    );
  }

  /// Convert to domain entity
  BiometricAuthData toEntity() {
    return BiometricAuthData(
      userId: userId,
      biometricType: biometricType,
      signature: signature,
      timestamp: timestamp,
    );
  }
}

/// Two-factor authentication data model
@JsonSerializable(includeIfNull: false)
class TwoFactorAuthDataModel extends TwoFactorAuthData {
  @override
  final String code;
  @override
  final String method;
  @override
  final DateTime timestamp;
  const TwoFactorAuthDataModel({
    required this.code,
    required this.method,
    required this.timestamp,
  }) : super(
          code: code,
          method: method,
          timestamp: timestamp,
        );

  /// Factory constructor for JSON deserialization
  factory TwoFactorAuthDataModel.fromJson(Map<String, dynamic> json) {
    return TwoFactorAuthDataModel(
      code: json['code'] as String,
      method: json['method'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'method': method,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from domain entity
  factory TwoFactorAuthDataModel.fromEntity(TwoFactorAuthData data) {
    return TwoFactorAuthDataModel(
      code: data.code,
      method: data.method,
      timestamp: data.timestamp,
    );
  }

  /// Convert to domain entity
  TwoFactorAuthData toEntity() {
    return TwoFactorAuthData(
      code: code,
      method: method,
      timestamp: timestamp,
    );
  }
}
