import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../../auth/domain/entities/user.dart';
import '../entities/user_management.dart';
import '../repositories/user_management_repository.dart';

/// Get users use case
@injectable
class GetUsersUseCase implements UseCase<ApiListResponse<User>, GetUsersParams> {
  final UserManagementRepository _repository;

  const GetUsersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<User>>> call(GetUsersParams params) async {
    return await _repository.getUsers(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get user by ID use case
@injectable
class GetUserByIdUseCase implements UseCase<ApiResponse<User>, IdParams> {
  final UserManagementRepository _repository;

  const GetUserByIdUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<User>>> call(IdParams params) async {
    return await _repository.getUserById(params.id);
  }
}

/// Create user use case
@injectable
class CreateUserUseCase implements UseCase<ApiResponse<User>, CreateUserParams> {
  final UserManagementRepository _repository;

  const CreateUserUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<User>>> call(CreateUserParams params) async {
    // Validate request
    if (!params.request.isValid) {
      return const Left(ValidationFailure('Invalid user creation request'));
    }

    return await _repository.createUser(params.request);
  }
}

/// Create user use case
@injectable
class CreateUserWithoutPasswordUseCase implements UseCase<ApiResponse<User>, CreateUserWithoutPasswordParams> {
  final UserManagementRepository _repository;

  const CreateUserWithoutPasswordUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<User>>> call(CreateUserWithoutPasswordParams params) async {
    // Validate request
    if (!params.request.isValid) {
      return const Left(ValidationFailure('Invalid user creation request'));
    }

    return await _repository.createUser(params.request);
  }
}

/// Update user use case
@injectable
class UpdateUserUseCase implements UseCase<ApiResponse<User>, UpdateUserParams> {
  final UserManagementRepository _repository;

  const UpdateUserUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<User>>> call(UpdateUserParams params) async {
    // Validate request
    if (!params.request.hasUpdates) {
      return const Left(ValidationFailure('No updates provided'));
    }

    return await _repository.updateUser(params.request);
  }
}

/// Delete user use case
@injectable
class DeleteUserUseCase implements UseCase<ApiVoidResponse, DeleteUserParams> {
  final UserManagementRepository _repository;

  const DeleteUserUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(DeleteUserParams params) async {
    return await _repository.deleteUser(params.userId, params.reason);
  }
}

/// Activate user use case
@injectable
class ActivateUserUseCase implements UseCase<ApiVoidResponse, IdParams> {
  final UserManagementRepository _repository;

  const ActivateUserUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await _repository.activateUser(params.id);
  }
}

/// Deactivate user use case
@injectable
class DeactivateUserUseCase implements UseCase<ApiVoidResponse, DeactivateUserParams> {
  final UserManagementRepository _repository;

  const DeactivateUserUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(DeactivateUserParams params) async {
    return await _repository.deactivateUser(params.userId, params.reason);
  }
}

/// Assign role use case
@injectable
class AssignRoleUseCase implements UseCase<ApiVoidResponse, AssignRoleParams> {
  final UserManagementRepository _repository;

  const AssignRoleUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(AssignRoleParams params) async {
    return await _repository.assignRole(params.request);
  }
}

/// Grant permissions use case
@injectable
class GrantPermissionsUseCase implements UseCase<ApiVoidResponse, PermissionParams> {
  final UserManagementRepository _repository;

  const GrantPermissionsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(PermissionParams params) async {
    final request = PermissionAssignmentRequest(
      userId: params.userId,
      permissions: params.permissions,
      isGrant: true,
      reason: params.reason,
    );

    return await _repository.grantPermissions(request);
  }
}

/// Revoke permissions use case
@injectable
class RevokePermissionsUseCase implements UseCase<ApiVoidResponse, PermissionParams> {
  final UserManagementRepository _repository;

  const RevokePermissionsUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(PermissionParams params) async {
    final request = PermissionAssignmentRequest(
      userId: params.userId,
      permissions: params.permissions,
      isGrant: false,
      reason: params.reason,
    );

    return await _repository.revokePermissions(request);
  }
}

/// Get user permissions use case
@injectable
class GetUserPermissionsUseCase implements UseCase<List<String>, IdParams> {
  final UserManagementRepository _repository;

  const GetUserPermissionsUseCase(this._repository);

  @override
  Future<Either<Failure, List<String>>> call(IdParams params) async {
    return await _repository.getUserPermissions(params.id);
  }
}

/// Search users use case
@injectable
class SearchUsersUseCase implements UseCase<ApiListResponse<User>, SearchUsersParams> {
  final UserManagementRepository _repository;

  const SearchUsersUseCase(this._repository);

  @override
  Future<Either<Failure, ApiListResponse<User>>> call(SearchUsersParams params) async {
    return await _repository.searchUsers(
      query: params.query,
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

/// Get user statistics use case
@injectable
class GetUserStatisticsUseCase implements UseCase<UserStatistics, NoParams> {
  final UserManagementRepository _repository;

  const GetUserStatisticsUseCase(this._repository);

  @override
  Future<Either<Failure, UserStatistics>> call(NoParams params) async {
    return await _repository.getUserStatistics();
  }
}

/// Reset user password use case
@injectable
class ResetUserPasswordUseCase implements UseCase<ApiVoidResponse, ResetPasswordParams> {
  final UserManagementRepository _repository;

  const ResetUserPasswordUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(ResetPasswordParams params) async {
    return await _repository.resetUserPassword(params.userId, params.newPassword);
  }
}

/// Get available roles use case
@injectable
class GetAvailableRolesUseCase implements UseCase<List<RoleDefinition>, NoParams> {
  final UserManagementRepository _repository;

  const GetAvailableRolesUseCase(this._repository);

  @override
  Future<Either<Failure, List<RoleDefinition>>> call(NoParams params) async {
    return await _repository.getAvailableRoles();
  }
}

/// Get available permissions use case
@injectable
class GetAvailablePermissionsUseCase implements UseCase<List<Permission>, NoParams> {
  final UserManagementRepository _repository;

  const GetAvailablePermissionsUseCase(this._repository);

  @override
  Future<Either<Failure, List<Permission>>> call(NoParams params) async {
    return await _repository.getAvailablePermissions();
  }
}

// Parameter classes

class GetUsersParams {
  final UserFilterCriteria? filter;
  final PaginationParams? pagination;

  const GetUsersParams({this.filter, this.pagination});
}

class CreateUserParams {
  final CreateUserRequest request;

  const CreateUserParams(this.request);
}

class CreateUserWithoutPasswordParams {
  final CreateUserRequest request;

  const CreateUserWithoutPasswordParams(this.request);
}

class UpdateUserParams {
  final UpdateUserRequest request;

  const UpdateUserParams(this.request);
}

class DeleteUserParams {
  final String userId;
  final String? reason;

  const DeleteUserParams(this.userId, {this.reason});
}

class DeactivateUserParams {
  final String userId;
  final String? reason;

  const DeactivateUserParams(this.userId, {this.reason});
}

class AssignRoleParams {
  final RoleAssignmentRequest request;

  const AssignRoleParams(this.request);
}

class PermissionParams {
  final String userId;
  final List<String> permissions;
  final String? reason;

  const PermissionParams(this.userId, this.permissions, {this.reason});
}

class SearchUsersParams {
  final String query;
  final UserFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchUsersParams(this.query, {this.filter, this.pagination});
}

class ResetPasswordParams {
  final String userId;
  final String newPassword;

  const ResetPasswordParams(this.userId, this.newPassword);
}
