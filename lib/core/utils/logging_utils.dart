import 'package:flutter/foundation.dart';

class AppLogger {
  static const List<String> _filteredPatterns = [
    'X-Firebase-Locale',
    'VRI\[MainActivity\]',
    'empty reCAPTCHA token',
  ];

  static void log(String message, {String? tag}) {
    if (kDebugMode) {
      // Skip filtered messages
      for (final pattern in _filteredPatterns) {
        if (message.contains(pattern)) return;
      }
      
      // Add tag if provided
      final logMessage = tag != null ? '[$tag] $message' : message;
      
      // Print with timestamp
      final timestamp = DateTime.now().toIso8601String();
      debugPrint('$timestamp - $logMessage');
    }
  }
  
  static void error(dynamic error, StackTrace? stackTrace, {String? tag}) {
    if (kDebugMode) {
      final timestamp = DateTime.now().toIso8601String();
      final errorMessage = 'ERROR${tag != null ? ' [$tag]' : ''}: $error\n$stackTrace';
      debugPrint('$timestamp - $errorMessage');
    }
  }
}
