import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:hm_collection/features/quality_control/presentation/widgets/quality_statistics_card.dart';
import '../../../../core/theme/app_colors.dart';
import '../../domain/entities/quality_entities.dart';
import '../../domain/repositories/quality_repository.dart';

class QualityStatisticsCard extends StatelessWidget {
  final QualityStatistics statistics;
  final VoidCallback? onTap;

  const QualityStatisticsCard({
    Key? key,
    required this.statistics,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 16),
              _buildMetricsRow(context),
              const SizedBox(height: 24),
              _buildDefectsPieChart(),
              const SizedBox(height: 24),
              _buildStageDistribution(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.assessment, size: 24, color: AppColors.primary),
        const SizedBox(width: 8),
        Text(
          'Quality Overview',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const Spacer(),
        if (onTap != null)
          const Icon(Icons.chevron_right, color: Colors.grey),
      ],
    );
  }

  Widget _buildMetricsRow(BuildContext context) {
    return Row(
      children: [
        _buildMetricCard(
          context,
          'Pass Rate',
          '${statistics.passRate.toStringAsFixed(1)}%',
          Icons.check_circle,
          _getStatusColor(statistics.passRate, 90, 70),
        ),
        const SizedBox(width: 12),
        _buildMetricCard(
          context,
          'First Pass Yield',
          '${statistics.firstPassYield.toStringAsFixed(1)}%',
          Icons.done_all,
          _getStatusColor(statistics.firstPassYield, 85, 70),
        ),
        const SizedBox(width: 12),
        _buildMetricCard(
          context,
          'Defect Rate',
          '${statistics.defectRate.toStringAsFixed(1)}%',
          Icons.warning,
          _getStatusColor(100 - statistics.defectRate, 90, 80),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 16, color: color),
                const SizedBox(width: 4),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefectsPieChart() {
    final totalDefects = statistics.criticalDefects +
        statistics.majorDefects +
        statistics.minorDefects;

    if (totalDefects == 0) {
      return const Center(
        child: Text('No defects found'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Defect Distribution',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 180,
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: PieChart(
                  PieChartData(
                    sections: _generatePieChartSections(),
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                    startDegreeOffset: 90,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLegendItem('Critical', Colors.red, statistics.criticalDefects),
                    const SizedBox(height: 8),
                    _buildLegendItem('Major', Colors.orange, statistics.majorDefects),
                    const SizedBox(height: 8),
                    _buildLegendItem('Minor', Colors.amber, statistics.minorDefects),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<PieChartSectionData> _generatePieChartSections() {
    final total = statistics.criticalDefects +
        statistics.majorDefects +
        statistics.minorDefects;

    if (total == 0) return [];

    return [
      PieChartSectionData(
        color: Colors.red,
        value: (statistics.criticalDefects / total) * 100,
        title: '${(statistics.criticalDefects / total * 100).toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.orange,
        value: (statistics.majorDefects / total) * 100,
        title: '${(statistics.majorDefects / total * 100).toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.amber,
        value: (statistics.minorDefects / total) * 100,
        title: '${(statistics.minorDefects / total * 100).toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    ];
  }

  Widget _buildLegendItem(String label, Color color, int count) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
        const Spacer(),
        Text(
          '$count',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildStageDistribution() {
    if (statistics.inspectionsByStage.isEmpty) {
      return const SizedBox.shrink();
    }

    final entries = statistics.inspectionsByStage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Inspections by Stage',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 8),
        ...entries.map((entry) {
          final percentage = (entry.value / statistics.totalInspections) * 100;
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatStageName(entry.key),
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: percentage / 100,
                  minHeight: 6,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getStageColor(entry.key),
                  ),
                  borderRadius: BorderRadius.circular(3),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  String _formatStageName(String stage) {
    return stage.split('.').last.replaceAll('_', ' ').toTitleCase();
  }

  Color _getStatusColor(double value, double good, double warning) {
    if (value >= good) return Colors.green;
    if (value >= warning) return Colors.orange;
    return Colors.red;
  }

  Color _getStageColor(String stage) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
    ];
    return colors[stage.hashCode % colors.length];
  }
}

extension StringExtension on String {
  String toTitleCase() {
    return split(' ')
        .map((word) => word.isEmpty
            ? word
            : '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}')
        .join(' ');
  }
}
