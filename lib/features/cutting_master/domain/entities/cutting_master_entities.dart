import 'package:equatable/equatable.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Marker entity for cutting patterns
class Marker extends BaseEntity {
  final String markerNumber;
  final String productId;
  final String productName;
  final String fabricType;
  final double fabricWidth;
  final double markerLength;
  final int piecesPerMarker;
  final List<MarkerPiece> pieces;
  final MarkerStatus status;
  final String createdBy;
  final String createdByName;
  final String? approvedBy;
  final String? approvedByName;
  final DateTime? approvedAt;
  final double efficiency;
  final String? notes;
  final Map<String, dynamic> specifications;

  const Marker({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.markerNumber,
    required this.productId,
    required this.productName,
    required this.fabricType,
    required this.fabricWidth,
    required this.markerLength,
    required this.piecesPerMarker,
    required this.pieces,
    required this.status,
    required this.createdBy,
    required this.createdByName,
    this.approvedBy,
    this.approvedByName,
    this.approvedAt,
    required this.efficiency,
    this.notes,
    this.specifications = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        markerNumber,
        productId,
        productName,
        fabricType,
        fabricWidth,
        markerLength,
        piecesPerMarker,
        pieces,
        status,
        createdBy,
        createdByName,
        approvedBy,
        approvedByName,
        approvedAt,
        efficiency,
        notes,
        specifications,
      ];

  /// Calculate total fabric consumption
  double get totalFabricConsumption => markerLength * fabricWidth;

  /// Check if marker is approved
  bool get isApproved => status == MarkerStatus.approved;

  /// Check if marker can be used for cutting
  bool get canBeUsedForCutting => status == MarkerStatus.approved;
}

/// Individual piece within a marker
class MarkerPiece extends Equatable {
  final String pieceId;
  final String pieceName;
  final String size;
  final int quantity;
  final double xPosition;
  final double yPosition;
  final double width;
  final double height;
  final double rotation;

  const MarkerPiece({
    required this.pieceId,
    required this.pieceName,
    required this.size,
    required this.quantity,
    required this.xPosition,
    required this.yPosition,
    required this.width,
    required this.height,
    this.rotation = 0.0,
  });

  @override
  List<Object?> get props => [
        pieceId,
        pieceName,
        size,
        quantity,
        xPosition,
        yPosition,
        width,
        height,
        rotation,
      ];

  /// Calculate area of the piece
  double get area => width * height;
}

/// Cutting task entity
class CuttingTask extends BaseEntity {
  final String taskNumber;
  final String markerId;
  final String markerNumber;
  final String productId;
  final String productName;
  final String fabricType;
  final int plannedQuantity;
  final int actualQuantity;
  final CuttingTaskStatus status;
  final String assignedTo;
  final String assignedToName;
  final String assignedBy;
  final String assignedByName;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<CuttingPiece> cutPieces;
  final List<QualityCheck> qualityChecks;
  final String? notes;
  final Map<String, dynamic> metadata;

  const CuttingTask({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.taskNumber,
    required this.markerId,
    required this.markerNumber,
    required this.productId,
    required this.productName,
    required this.fabricType,
    required this.plannedQuantity,
    this.actualQuantity = 0,
    required this.status,
    required this.assignedTo,
    required this.assignedToName,
    required this.assignedBy,
    required this.assignedByName,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.cutPieces = const [],
    this.qualityChecks = const [],
    this.notes,
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        taskNumber,
        markerId,
        markerNumber,
        productId,
        productName,
        fabricType,
        plannedQuantity,
        actualQuantity,
        status,
        assignedTo,
        assignedToName,
        assignedBy,
        assignedByName,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        cutPieces,
        qualityChecks,
        notes,
        metadata,
      ];

  /// Calculate completion percentage
  double get completionPercentage {
    if (plannedQuantity == 0) return 0.0;
    return (actualQuantity / plannedQuantity) * 100;
  }

  /// Check if task is overdue
  bool get isOverdue {
    if (status == CuttingTaskStatus.completed) return false;
    return DateTime.now().isAfter(plannedEndDate);
  }

  /// Get duration in hours
  double? get actualDurationHours {
    if (actualStartDate == null || actualEndDate == null) return null;
    return actualEndDate!.difference(actualStartDate!).inMinutes / 60.0;
  }
}

/// Individual cut piece
class CuttingPiece extends Equatable {
  final String pieceId;
  final String pieceName;
  final String size;
  final int quantity;
  final CuttingPieceStatus status;
  final double? qualityScore;
  final String? qualityNotes;
  final DateTime cutAt;
  final String cutBy;

  const CuttingPiece({
    required this.pieceId,
    required this.pieceName,
    required this.size,
    required this.quantity,
    required this.status,
    this.qualityScore,
    this.qualityNotes,
    required this.cutAt,
    required this.cutBy,
  });

  @override
  List<Object?> get props => [
        pieceId,
        pieceName,
        size,
        quantity,
        status,
        qualityScore,
        qualityNotes,
        cutAt,
        cutBy,
      ];

  /// Check if piece passed quality check
  bool get passedQualityCheck => status == CuttingPieceStatus.approved;
}

/// Fabric calculation entity
class FabricCalculation extends BaseEntity {
  final String calculationNumber;
  final String productId;
  final String productName;
  final String fabricType;
  final double fabricWidth;
  final Map<String, int> sizeQuantities; // size -> quantity
  final double totalFabricRequired;
  final double wastagePercentage;
  final double totalFabricWithWastage;
  final String calculatedBy;
  final String calculatedByName;
  final String? approvedBy;
  final String? approvedByName;
  final DateTime? approvedAt;
  final FabricCalculationStatus status;
  final String? notes;

  const FabricCalculation({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.calculationNumber,
    required this.productId,
    required this.productName,
    required this.fabricType,
    required this.fabricWidth,
    required this.sizeQuantities,
    required this.totalFabricRequired,
    required this.wastagePercentage,
    required this.totalFabricWithWastage,
    required this.calculatedBy,
    required this.calculatedByName,
    this.approvedBy,
    this.approvedByName,
    this.approvedAt,
    required this.status,
    this.notes,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        calculationNumber,
        productId,
        productName,
        fabricType,
        fabricWidth,
        sizeQuantities,
        totalFabricRequired,
        wastagePercentage,
        totalFabricWithWastage,
        calculatedBy,
        calculatedByName,
        approvedBy,
        approvedByName,
        approvedAt,
        status,
        notes,
      ];

  /// Calculate total pieces
  int get totalPieces => sizeQuantities.values.fold(0, (sum, qty) => sum + qty);

  /// Check if calculation is approved
  bool get isApproved => status == FabricCalculationStatus.approved;
}

/// Quality check entity
class QualityCheck extends Equatable {
  final String checkId;
  final String checkType;
  final QualityCheckStatus status;
  final double score;
  final String checkedBy;
  final String checkedByName;
  final DateTime checkedAt;
  final String? notes;
  final List<String> defects;

  const QualityCheck({
    required this.checkId,
    required this.checkType,
    required this.status,
    required this.score,
    required this.checkedBy,
    required this.checkedByName,
    required this.checkedAt,
    this.notes,
    this.defects = const [],
  });

  @override
  List<Object?> get props => [
        checkId,
        checkType,
        status,
        score,
        checkedBy,
        checkedByName,
        checkedAt,
        notes,
        defects,
      ];

  /// Check if quality check passed
  bool get passed => status == QualityCheckStatus.passed;
}

/// Production log entry
class ProductionLogEntry extends BaseEntity {
  final String logNumber;
  final String taskId;
  final String taskType;
  final String operatorId;
  final String operatorName;
  final DateTime startTime;
  final DateTime? endTime;
  final int quantityProduced;
  final double? qualityScore;
  final String? notes;
  final Map<String, dynamic> metrics;

  const ProductionLogEntry({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.logNumber,
    required this.taskId,
    required this.taskType,
    required this.operatorId,
    required this.operatorName,
    required this.startTime,
    this.endTime,
    required this.quantityProduced,
    this.qualityScore,
    this.notes,
    this.metrics = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        logNumber,
        taskId,
        taskType,
        operatorId,
        operatorName,
        startTime,
        endTime,
        quantityProduced,
        qualityScore,
        notes,
        metrics,
      ];

  /// Calculate duration in hours
  double? get durationHours {
    if (endTime == null) return null;
    return endTime!.difference(startTime).inMinutes / 60.0;
  }

  /// Check if log entry is completed
  bool get isCompleted => endTime != null;
}

// Enums

/// Marker status
enum MarkerStatus {
  draft,
  pending,
  approved,
  rejected,
  archived,
}

/// Cutting task status
enum CuttingTaskStatus {
  pending,
  inProgress,
  completed,
  onHold,
  cancelled,
}

/// Cutting piece status
enum CuttingPieceStatus {
  pending,
  cut,
  approved,
  rejected,
  rework,
}

/// Fabric calculation status
enum FabricCalculationStatus {
  draft,
  pending,
  approved,
  rejected,
}

/// Quality check status
enum QualityCheckStatus {
  pending,
  passed,
  failed,
  rework,
}
