import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'order_details_page.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/client_entities.dart';
import '../../domain/entities/order_entities.dart';
import '../bloc/order_bloc.dart';
import '../widgets/order_app_bar.dart';
import '../widgets/order_filter_dialog.dart';
import '../widgets/order_list_view.dart';
import '../widgets/order_search_bar.dart';
import '../widgets/order_statistics_card.dart' show OrderStatisticsCard;
import '../../domain/entities/client_entities.dart' show OrderStatistics;

/// Orders page
class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  OrderFilterCriteria _currentFilter = OrderFilterCriteria();
  String? _searchQuery;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<OrderBloc>()
        ..add(const LoadOrdersRequested()),
      child: Scaffold(
        appBar: OrderAppBar(
          onSearch: _handleSearch,
          onFilter: _showFilterDialog,
          onAdd: _navigateToCreateOrder,
          onViewModeChanged: _handleViewModeChanged,
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAllOrdersTab(),
                  _buildPendingOrdersTab(),
                  _buildInProductionOrdersTab(),
                  _buildOverdueOrdersTab(),
                  _buildUrgentOrdersTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _navigateToCreateOrder,
          tooltip: 'Create Order',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(text: 'All Orders'),
          Tab(text: 'Pending'),
          Tab(text: 'In Production'),
          Tab(text: 'Overdue'),
          Tab(text: 'Urgent'),
        ],
        onTap: _handleTabChanged,
      ),
    );
  }

  Widget _buildAllOrdersTab() {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Column(
            children: [
              _buildSearchBar(),
              _buildStatisticsSection(),
              _buildOrdersList(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPendingOrdersTab() {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildOrdersList(state, status: OrderStatus.pending),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInProductionOrdersTab() {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildOrdersList(state, status: OrderStatus.inProduction),
            ),
          ],
        );
      },
    );
  }

  Widget _buildOverdueOrdersTab() {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildOverdueOrdersList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUrgentOrdersTab() {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildUrgentOrdersList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: OrderSearchBar(
        onSearch: _handleSearch,
        onClear: _clearSearch,
        initialQuery: _searchQuery,
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state is OrdersLoaded && state.orders.isNotEmpty) {
          // Calculate statistics
          final totalOrders = state.orders.length;
          final pendingOrders = state.orders.where((o) => o.status == OrderStatus.pending).length;
          final completedOrders = state.orders.where((o) => o.status == OrderStatus.completed).length;
          final totalRevenue = state.orders.fold(0.0, (sum, order) => sum + (order.pricing.totalAmount ?? 0));

          final statistics = OrderStatistics(
            totalOrders: totalOrders,
            completedOrders: completedOrders,
            inProgressOrders: state.orders.where((o) => o.status == OrderStatus.inProduction).length,
            overdueOrders: state.orders.where((o) => o.isOverdue).length,
            totalValue: totalRevenue,
            averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0.0,
            onTimeDeliveryRate: 1.0, // You might want to calculate this based on your business logic
          );

          return Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            child: OrderStatisticsCard(
              statistics: statistics,
              onTap: _handleStatisticTap,
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildOrdersList(OrderState state, {OrderStatus? status}) {
    if (state is OrderLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is OrderError) {
      return _buildErrorWidget(state.message);
    }

    if (state is OrdersLoaded) {
      var orders = state.orders;
      
      // Filter by status if specified
      if (status != null) {
        orders = orders.where((order) => order.status == status).toList();
      }

      if (orders.isEmpty) {
        return _buildEmptyState(status);
      }

      // Convert ManufacturingOrder list to OrderItem list
      return RefreshIndicator(
        onRefresh: () async => _refreshOrders(),
        child: OrderListView(
          orders: orders,
          viewMode: state.viewMode,
          selectedOrderIds: state.selectedOrderIds,
          onOrderTap: _navigateToOrderDetails,
          onOrderLongPress: (order) => _handleOrderLongPress(order.id),
          onSelectionChanged: _handleSelectionChanged,
          onStatusChanged: _handleStatusChanged,
          onLoadMore: _loadMoreOrders,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: state.isRefreshing,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildOverdueOrdersList(OrderState state) {
    if (state is OrderLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is OrderError) {
      return _buildErrorWidget(state.message);
    }

    if (state is OverdueOrdersLoaded) {
      if (state.orders.isEmpty) {
        return _buildEmptyState(null, isOverdue: true);
      }

      return RefreshIndicator(
        onRefresh: () async => _loadOverdueOrders(),
        child: OrderListView(
          orders: state.orders,
          viewMode: OrderViewMode.list,
          selectedOrderIds: const [],
          onOrderTap: _navigateToOrderDetails,
          onOrderLongPress: (order) => _handleOrderLongPress(order.id),
          onSelectionChanged: _handleSelectionChanged,
          onStatusChanged: _handleStatusChanged,
          onLoadMore: _loadMoreOrders,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildUrgentOrdersList(OrderState state) {
    if (state is OrderLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is OrderError) {
      return _buildErrorWidget(state.message);
    }

    if (state is UrgentOrdersLoaded) {
      if (state.orders.isEmpty) {
        return _buildEmptyState(null, isUrgent: true);
      }

      return RefreshIndicator(
        onRefresh: () async => _loadUrgentOrders(),
        child: OrderListView(
          orders: state.orders,
          viewMode: OrderViewMode.list,
          selectedOrderIds: const [],
          onOrderTap: _navigateToOrderDetails,
          onOrderLongPress: (order) => _handleOrderLongPress(order.id),
          onSelectionChanged: _handleSelectionChanged,
          onStatusChanged: _handleStatusChanged,
          onLoadMore: _loadMoreOrders,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading orders',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _refreshOrders,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(OrderStatus? status, {bool isOverdue = false, bool isUrgent = false}) {
    String title;
    String subtitle;
    IconData icon;

    if (isOverdue) {
      title = 'No Overdue Orders';
      subtitle = 'Great! All orders are on track.';
      icon = Icons.check_circle_outline;
    } else if (isUrgent) {
      title = 'No Urgent Orders';
      subtitle = 'No urgent orders at the moment.';
      icon = Icons.priority_high;
    } else if (status != null) {
      title = 'No ${status.displayName} Orders';
      subtitle = 'No orders found with ${status.displayName.toLowerCase()} status.';
      icon = Icons.inbox_outlined;
    } else {
      title = 'No Orders Found';
      subtitle = 'Create your first order to get started.';
      icon = Icons.shopping_bag_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (!isOverdue && !isUrgent) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _navigateToCreateOrder,
              child: const Text('Create Order'),
            ),
          ],
        ],
      ),
    );
  }

  void _handleTabChanged(int index) {
    switch (index) {
      case 0:
        context.read<OrderBloc>().add(const LoadOrdersRequested());
        break;
      case 1:
        context.read<OrderBloc>().add(FilterOrdersRequested(
          const OrderFilterCriteria(status: OrderStatus.pending),
        ));
        break;
      case 2:
        context.read<OrderBloc>().add(FilterOrdersRequested(
          const OrderFilterCriteria(status: OrderStatus.inProduction),
        ));
        break;
      case 3:
        _loadOverdueOrders();
        break;
      case 4:
        _loadUrgentOrders();
        break;
    }
  }

  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query.isEmpty ? null : query;
    });

    if (query.isEmpty) {
      context.read<OrderBloc>().add(LoadOrdersRequested(filter: _currentFilter));
    } else {
      context.read<OrderBloc>().add(SearchOrdersRequested(
        query,
        filter: _currentFilter,
      ));
    }
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = null;
    });
    context.read<OrderBloc>().add(LoadOrdersRequested(filter: _currentFilter));
  }

  void _showFilterDialog() async {
    // Convert OrderFilterCriteria to OrderFilter for the dialog
    final orderFilter = _currentFilter != null
        ? OrderFilter(
            status: _currentFilter.status,
            priority: _currentFilter.priority,
            startDate: _currentFilter.orderDateFrom,
            endDate: _currentFilter.orderDateTo,
            searchQuery: _searchQuery,
          )
        : null;

    final filter = await showDialog<OrderFilter>(
      context: context,
      builder: (context) => OrderFilterDialog(
        initialFilter: orderFilter,
        onApplyFilter: (newFilter) {
          Navigator.of(context).pop(newFilter);
        },
      ),
    );

    if (filter != null) {
      setState(() {
        // Convert OrderFilter back to OrderFilterCriteria
        _currentFilter = OrderFilterCriteria(
          status: filter.status,
          priority: filter.priority,
          orderDateFrom: filter.startDate,
          orderDateTo: filter.endDate,
          // Preserve other filter criteria
          clientId: _currentFilter?.clientId,
          type: _currentFilter?.type,
          department: _currentFilter?.department,
          assignedMerchandiser: _currentFilter?.assignedMerchandiser,
          requiredDateFrom: _currentFilter?.requiredDateFrom,
          requiredDateTo: _currentFilter?.requiredDateTo,
          minValue: _currentFilter?.minValue,
          maxValue: _currentFilter?.maxValue,
          isOverdue: _currentFilter?.isOverdue,
          isUrgent: _currentFilter?.isUrgent,
          tags: _currentFilter?.tags,
        );
        _searchQuery = filter.searchQuery;
      });
      context.read<OrderBloc>().add(FilterOrdersRequested(_currentFilter));
    }
  }

  void _handleViewModeChanged(OrderViewMode viewMode) {
    context.read<OrderBloc>().add(ChangeOrderViewModeRequested(viewMode));
  }

  void _handleStatisticTap(String statistic) {
    // Handle statistic tap for filtering or navigation
    switch (statistic) {
      case 'pending':
        _tabController.animateTo(1);
        break;
      case 'in_production':
        _tabController.animateTo(2);
        break;
      case 'overdue':
        _tabController.animateTo(3);
        break;
      case 'urgent':
        _tabController.animateTo(4);
        break;
    }
  }

  void _navigateToOrderDetails(ManufacturingOrder order) {
    // TODO: Navigate to order details page with full order object
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OrderDetailsPage(order: order),
      ),
    );
  }

  void _navigateToCreateOrder() {
    // TODO: Navigate to create order page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create Order feature coming soon')),
    );
  }

  void _handleOrderLongPress(String orderId) {
    context.read<OrderBloc>().add(ToggleOrderSelectionRequested(orderId));
  }

  void _handleSelectionChanged(List<String> selectedIds) {
    // Handle selection change for bulk operations
  }

  void _handleStatusChanged(String orderId, OrderStatus status) {
    context.read<OrderBloc>().add(UpdateOrderStatusRequested(orderId, status));
  }

  void _refreshOrders() {
    context.read<OrderBloc>().add(const RefreshOrdersRequested());
  }

  void _loadMoreOrders() {
    context.read<OrderBloc>().add(const LoadMoreOrdersRequested());
  }

  void _loadOverdueOrders() {
    context.read<OrderBloc>().add(const LoadOverdueOrdersRequested());
  }

  void _loadUrgentOrders() {
    context.read<OrderBloc>().add(const LoadUrgentOrdersRequested());
  }
}
