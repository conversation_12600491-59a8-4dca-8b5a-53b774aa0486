part of 'resource_bloc.dart';

/// Base resource event
abstract class ResourceEvent extends Equatable {
  const ResourceEvent();

  @override
  List<Object?> get props => [];
}

// Machine Events

/// Load machines
class LoadMachinesRequested extends ResourceEvent {
  final MachineFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadMachinesRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Refresh machines
class RefreshMachinesRequested extends ResourceEvent {
  const RefreshMachinesRequested();
}

/// Load machine details
class LoadMachineDetailsRequested extends ResourceEvent {
  final String machineId;

  const LoadMachineDetailsRequested(this.machineId);

  @override
  List<Object?> get props => [machineId];
}

/// Create machine
class CreateMachineRequested extends ResourceEvent {
  final CreateMachineRequest request;

  const CreateMachineRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update machine
class UpdateMachineRequested extends ResourceEvent {
  final UpdateMachineRequest request;

  const UpdateMachineRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update machine status
class UpdateMachineStatusRequested extends ResourceEvent {
  final String machineId;
  final MachineStatus status;
  final String? reason;

  const UpdateMachineStatusRequested(
    this.machineId,
    this.status,
    this.reason,
  );

  @override
  List<Object?> get props => [machineId, status, reason];
}

/// Load machine schedules
class LoadMachineSchedulesRequested extends ResourceEvent {
  final MachineScheduleFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadMachineSchedulesRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Create machine schedule
class CreateMachineScheduleRequested extends ResourceEvent {
  final CreateMachineScheduleRequest request;

  const CreateMachineScheduleRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load machine maintenance records
class LoadMachineMaintenanceRecordsRequested extends ResourceEvent {
  final MachineMaintenanceFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadMachineMaintenanceRecordsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Create maintenance record
class CreateMaintenanceRecordRequested extends ResourceEvent {
  final CreateMaintenanceRecordRequest request;

  const CreateMaintenanceRecordRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Complete maintenance
class CompleteMaintenanceRequested extends ResourceEvent {
  final String maintenanceId;
  final CompleteMaintenanceRequest request;

  const CompleteMaintenanceRequested(
    this.maintenanceId,
    this.request,
  );

  @override
  List<Object?> get props => [maintenanceId, request];
}

/// Load available machines
class LoadAvailableMachinesRequested extends ResourceEvent {
  final DateTime? startTime;
  final DateTime? endTime;
  final PaginationParams? pagination;

  const LoadAvailableMachinesRequested({
    this.startTime,
    this.endTime,
    this.pagination,
  });

  @override
  List<Object?> get props => [startTime, endTime, pagination];
}

/// Load machines due for maintenance
class LoadMachinesDueForMaintenanceRequested extends ResourceEvent {
  final int daysAhead;
  final PaginationParams? pagination;

  const LoadMachinesDueForMaintenanceRequested({
    this.daysAhead = 30,
    this.pagination,
  });

  @override
  List<Object?> get props => [daysAhead, pagination];
}

/// Load machine utilization report
class LoadMachineUtilizationReportRequested extends ResourceEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? departmentId;

  const LoadMachineUtilizationReportRequested({
    this.startDate,
    this.endDate,
    this.departmentId,
  });

  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

/// Search machines
class SearchMachinesRequested extends ResourceEvent {
  final String query;
  final MachineFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchMachinesRequested(
    this.query, {
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

// Worker Events

/// Load workers
class LoadWorkersRequested extends ResourceEvent {
  final WorkerFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadWorkersRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Refresh workers
class RefreshWorkersRequested extends ResourceEvent {
  const RefreshWorkersRequested();
}

/// Load worker details
class LoadWorkerDetailsRequested extends ResourceEvent {
  final String workerId;

  const LoadWorkerDetailsRequested(this.workerId);

  @override
  List<Object?> get props => [workerId];
}

/// Create worker
class CreateWorkerRequested extends ResourceEvent {
  final CreateWorkerRequest request;

  const CreateWorkerRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update worker
class UpdateWorkerRequested extends ResourceEvent {
  final UpdateWorkerRequest request;

  const UpdateWorkerRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Update worker status
class UpdateWorkerStatusRequested extends ResourceEvent {
  final String workerId;
  final WorkerStatus status;
  final String? reason;

  const UpdateWorkerStatusRequested(
    this.workerId,
    this.status,
    this.reason,
  );

  @override
  List<Object?> get props => [workerId, status, reason];
}

/// Load worker schedules
class LoadWorkerSchedulesRequested extends ResourceEvent {
  final WorkerScheduleFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadWorkerSchedulesRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Create worker schedule
class CreateWorkerScheduleRequested extends ResourceEvent {
  final CreateWorkerScheduleRequest request;

  const CreateWorkerScheduleRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load worker attendance
class LoadWorkerAttendanceRequested extends ResourceEvent {
  final WorkerAttendanceFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadWorkerAttendanceRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Record attendance
class RecordAttendanceRequested extends ResourceEvent {
  final RecordAttendanceRequest request;

  const RecordAttendanceRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load worker leave requests
class LoadWorkerLeaveRequestsRequested extends ResourceEvent {
  final WorkerLeaveFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadWorkerLeaveRequestsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Create leave request
class CreateLeaveRequestRequested extends ResourceEvent {
  final CreateLeaveRequestRequest request;

  const CreateLeaveRequestRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Approve leave request
class ApproveLeaveRequestRequested extends ResourceEvent {
  final String leaveRequestId;
  final String approverId;
  final String? notes;

  const ApproveLeaveRequestRequested(
    this.leaveRequestId,
    this.approverId,
    this.notes,
  );

  @override
  List<Object?> get props => [leaveRequestId, approverId, notes];
}

/// Load available workers
class LoadAvailableWorkersRequested extends ResourceEvent {
  final DateTime? startTime;
  final DateTime? endTime;
  final List<String>? skillsRequired;
  final PaginationParams? pagination;

  const LoadAvailableWorkersRequested({
    this.startTime,
    this.endTime,
    this.skillsRequired,
    this.pagination,
  });

  @override
  List<Object?> get props => [startTime, endTime, skillsRequired, pagination];
}

/// Load worker performance report
class LoadWorkerPerformanceReportRequested extends ResourceEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? departmentId;

  const LoadWorkerPerformanceReportRequested({
    this.startDate,
    this.endDate,
    this.departmentId,
  });

  @override
  List<Object?> get props => [startDate, endDate, departmentId];
}

/// Search workers
class SearchWorkersRequested extends ResourceEvent {
  final String query;
  final WorkerFilterCriteria? filter;
  final PaginationParams? pagination;

  const SearchWorkersRequested(
    this.query, {
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

// Facility Events

/// Load facilities
class LoadFacilitiesRequested extends ResourceEvent {
  final FacilityFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadFacilitiesRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Load facility details
class LoadFacilityDetailsRequested extends ResourceEvent {
  final String facilityId;

  const LoadFacilityDetailsRequested(this.facilityId);

  @override
  List<Object?> get props => [facilityId];
}

/// Create facility
class CreateFacilityRequested extends ResourceEvent {
  final CreateFacilityRequest request;

  const CreateFacilityRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load departments
class LoadDepartmentsRequested extends ResourceEvent {
  final DepartmentFilterCriteria? filter;
  final PaginationParams? pagination;

  const LoadDepartmentsRequested({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Create department
class CreateDepartmentRequested extends ResourceEvent {
  final CreateDepartmentRequest request;

  const CreateDepartmentRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Load facility capacity report
class LoadFacilityCapacityReportRequested extends ResourceEvent {
  final String? facilityId;
  final DateTime? asOfDate;

  const LoadFacilityCapacityReportRequested({
    this.facilityId,
    this.asOfDate,
  });

  @override
  List<Object?> get props => [facilityId, asOfDate];
}

/// Load facility utilization report
class LoadFacilityUtilizationReportRequested extends ResourceEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? facilityId;

  const LoadFacilityUtilizationReportRequested({
    this.startDate,
    this.endDate,
    this.facilityId,
  });

  @override
  List<Object?> get props => [startDate, endDate, facilityId];
}

// General Events

/// Filter resources
class FilterResourcesRequested extends ResourceEvent {
  final ResourceType resourceType;
  final Map<String, dynamic> filters;

  const FilterResourcesRequested(this.resourceType, this.filters);

  @override
  List<Object?> get props => [resourceType, filters];
}

/// Clear resource state
class ClearResourceState extends ResourceEvent {
  const ClearResourceState();
}

/// Load more resources (pagination)
class LoadMoreResourcesRequested extends ResourceEvent {
  final ResourceType resourceType;

  const LoadMoreResourcesRequested(this.resourceType);

  @override
  List<Object?> get props => [resourceType];
}

/// Sort resources
class SortResourcesRequested extends ResourceEvent {
  final ResourceType resourceType;
  final String sortBy;
  final bool ascending;

  const SortResourcesRequested(this.resourceType, this.sortBy, this.ascending);

  @override
  List<Object?> get props => [resourceType, sortBy, ascending];
}

/// Select resource
class SelectResourceRequested extends ResourceEvent {
  final ResourceType resourceType;
  final String resourceId;

  const SelectResourceRequested(this.resourceType, this.resourceId);

  @override
  List<Object?> get props => [resourceType, resourceId];
}

/// Change resource view mode
class ChangeResourceViewModeRequested extends ResourceEvent {
  final ResourceType resourceType;
  final ResourceViewMode viewMode;

  const ChangeResourceViewModeRequested(this.resourceType, this.viewMode);

  @override
  List<Object?> get props => [resourceType, viewMode];
}

// Enums

/// Resource type enum
enum ResourceType {
  machine,
  worker,
  facility,
  department,
}

/// Resource type extension
extension ResourceTypeExtension on ResourceType {
  String get displayName {
    switch (this) {
      case ResourceType.machine:
        return 'Machines';
      case ResourceType.worker:
        return 'Workers';
      case ResourceType.facility:
        return 'Facilities';
      case ResourceType.department:
        return 'Departments';
    }
  }

  String get value => name;
}

/// Resource view mode enum
enum ResourceViewMode {
  list,
  grid,
  kanban,
  calendar,
  dashboard,
}

/// Resource view mode extension
extension ResourceViewModeExtension on ResourceViewMode {
  String get displayName {
    switch (this) {
      case ResourceViewMode.list:
        return 'List';
      case ResourceViewMode.grid:
        return 'Grid';
      case ResourceViewMode.kanban:
        return 'Kanban';
      case ResourceViewMode.calendar:
        return 'Calendar';
      case ResourceViewMode.dashboard:
        return 'Dashboard';
    }
  }

  IconData get icon {
    switch (this) {
      case ResourceViewMode.list:
        return Icons.list;
      case ResourceViewMode.grid:
        return Icons.grid_view;
      case ResourceViewMode.kanban:
        return Icons.view_kanban;
      case ResourceViewMode.calendar:
        return Icons.calendar_view_month;
      case ResourceViewMode.dashboard:
        return Icons.dashboard;
    }
  }
}

class MachineFilterCriteria extends Equatable {
  const MachineFilterCriteria();
  @override
  List<Object?> get props => [];
}

class MachineScheduleFilterCriteria extends Equatable {
  const MachineScheduleFilterCriteria();
  @override
  List<Object?> get props => [];
}

class MachineMaintenanceFilterCriteria extends Equatable {
  const MachineMaintenanceFilterCriteria();
  @override
  List<Object?> get props => [];
}

class CompleteMaintenanceRequest extends Equatable {
  const CompleteMaintenanceRequest();
  @override
  List<Object?> get props => [];
}

class WorkerFilterCriteria extends Equatable {
  const WorkerFilterCriteria();
  @override
  List<Object?> get props => [];
}

class WorkerScheduleFilterCriteria extends Equatable {
  const WorkerScheduleFilterCriteria();
  @override
  List<Object?> get props => [];
}

class WorkerAttendanceFilterCriteria extends Equatable {
  const WorkerAttendanceFilterCriteria();
  @override
  List<Object?> get props => [];
}

class WorkerLeaveFilterCriteria extends Equatable {
  const WorkerLeaveFilterCriteria();
  @override
  List<Object?> get props => [];
}

class FacilityFilterCriteria extends Equatable {
  const FacilityFilterCriteria();
  @override
  List<Object?> get props => [];
}

class DepartmentFilterCriteria extends Equatable {
  const DepartmentFilterCriteria();
  @override
  List<Object?> get props => [];
}
