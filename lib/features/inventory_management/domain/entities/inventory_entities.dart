import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Inventory item entity
class InventoryItem extends BaseEntity {
  final String itemCode;
  final String itemName;
  final String description;
  final ItemCategory category;
  final ItemType type;
  final String unit;
  final double currentStock;
  final double reservedStock;
  final double availableStock;
  final double minimumStock;
  final double maximumStock;
  final double reorderPoint;
  final double reorderQuantity;
  final double unitCost;
  final double totalValue;
  final String? supplierId;
  final String? supplierName;
  final String? location;
  final String? binLocation;
  final ItemStatus status;
  final List<String> tags;
  final Map<String, dynamic> specifications;
  final DateTime? lastStockUpdate;
  final DateTime? lastOrderDate;
  final DateTime? expiryDate;

  const InventoryItem({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.itemCode,
    required this.itemName,
    required this.description,
    required this.category,
    required this.type,
    required this.unit,
    required this.currentStock,
    this.reservedStock = 0.0,
    required this.availableStock,
    required this.minimumStock,
    required this.maximumStock,
    required this.reorderPoint,
    required this.reorderQuantity,
    required this.unitCost,
    required this.totalValue,
    this.supplierId,
    this.supplierName,
    this.location,
    this.binLocation,
    required this.status,
    this.tags = const [],
    this.specifications = const {},
    this.lastStockUpdate,
    this.lastOrderDate,
    this.expiryDate,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        itemCode,
        itemName,
        description,
        category,
        type,
        unit,
        currentStock,
        reservedStock,
        availableStock,
        minimumStock,
        maximumStock,
        reorderPoint,
        reorderQuantity,
        unitCost,
        totalValue,
        supplierId,
        supplierName,
        location,
        binLocation,
        status,
        tags,
        specifications,
        lastStockUpdate,
        lastOrderDate,
        expiryDate,
      ];

  /// Check if item is low stock
  bool get isLowStock => currentStock <= reorderPoint;

  /// Check if item is out of stock
  bool get isOutOfStock => currentStock <= 0;

  /// Check if item is overstocked
  bool get isOverstocked => currentStock > maximumStock;

  /// Check if item is expired
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// Check if item is expiring soon (within 30 days)
  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  /// Get stock status
  StockStatus get stockStatus {
    if (isOutOfStock) return StockStatus.outOfStock;
    if (isLowStock) return StockStatus.lowStock;
    if (isOverstocked) return StockStatus.overstock;
    return StockStatus.inStock;
  }

  /// Get stock percentage
  double get stockPercentage {
    if (maximumStock == 0) return 0.0;
    return (currentStock / maximumStock) * 100;
  }

  /// Get days until reorder needed
  int? get daysUntilReorder {
    if (currentStock > reorderPoint) return null;
    // Simplified calculation - would need consumption rate in real implementation
    return 0;
  }
}

/// Stock movement entity
class StockMovement extends BaseEntity {
  final String itemId;
  final String itemCode;
  final String itemName;
  final MovementType movementType;
  final double quantity;
  final double unitCost;
  final double totalCost;
  final String reason;
  final String? referenceNumber;
  final String? orderId;
  final String? supplierId;
  final String? customerId;
  final String? departmentId;
  final String userId;
  final String userName;
  final DateTime movementDate;
  final double stockBefore;
  final double stockAfter;
  final Map<String, dynamic> metadata;

  const StockMovement({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.movementType,
    required this.quantity,
    required this.unitCost,
    required this.totalCost,
    required this.reason,
    this.referenceNumber,
    this.orderId,
    this.supplierId,
    this.customerId,
    this.departmentId,
    required this.userId,
    required this.userName,
    required this.movementDate,
    required this.stockBefore,
    required this.stockAfter,
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        itemId,
        itemCode,
        itemName,
        movementType,
        quantity,
        unitCost,
        totalCost,
        reason,
        referenceNumber,
        orderId,
        supplierId,
        customerId,
        departmentId,
        userId,
        userName,
        movementDate,
        stockBefore,
        stockAfter,
        metadata,
      ];

  /// Check if movement increases stock
  bool get isInbound => movementType.isInbound;

  /// Check if movement decreases stock
  bool get isOutbound => movementType.isOutbound;
}

/// Bill of Materials (BOM) entity
class BillOfMaterials extends BaseEntity {
  final String bomCode;
  final String productId;
  final String productName;
  final String productCode;
  final String version;
  final BOMStatus status;
  final List<BOMItem> items;
  final double totalCost;
  final String? description;
  final String? createdBy;
  final String? approvedBy;
  final DateTime? approvedAt;
  final DateTime? effectiveDate;
  final DateTime? expiryDate;

  const BillOfMaterials({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.bomCode,
    required this.productId,
    required this.productName,
    required this.productCode,
    required this.version,
    required this.status,
    required this.items,
    required this.totalCost,
    this.description,
    this.createdBy,
    this.approvedBy,
    this.approvedAt,
    this.effectiveDate,
    this.expiryDate,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        bomCode,
        productId,
        productName,
        productCode,
        version,
        status,
        items,
        totalCost,
        description,
        createdBy,
        approvedBy,
        approvedAt,
        effectiveDate,
        expiryDate,
      ];

  /// Check if BOM is active
  bool get isActive => status == BOMStatus.active;

  /// Check if BOM is approved
  bool get isApproved => approvedAt != null && approvedBy != null;

  /// Get total material cost
  double get totalMaterialCost => items.fold(0.0, (sum, item) => sum + item.totalCost);

  /// Get total quantity of all items
  double get totalQuantity => items.fold(0.0, (sum, item) => sum + item.quantity);
}

/// BOM item entity
class BOMItem extends Equatable {
  final String itemId;
  final String itemCode;
  final String itemName;
  final double quantity;
  final String unit;
  final double unitCost;
  final double totalCost;
  final bool isOptional;
  final String? notes;
  final Map<String, dynamic> specifications;

  const BOMItem({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.quantity,
    required this.unit,
    required this.unitCost,
    required this.totalCost,
    this.isOptional = false,
    this.notes,
    this.specifications = const {},
  });

  @override
  List<Object?> get props => [
        itemId,
        itemCode,
        itemName,
        quantity,
        unit,
        unitCost,
        totalCost,
        isOptional,
        notes,
        specifications,
      ];
}

/// Purchase order entity
class PurchaseOrder extends BaseEntity {
  final String poNumber;
  final String supplierId;
  final String supplierName;
  final PurchaseOrderStatus status;
  final DateTime orderDate;
  final DateTime? expectedDeliveryDate;
  final DateTime? actualDeliveryDate;
  final List<PurchaseOrderItem> items;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double shippingCost;
  final double totalAmount;
  final String currency;
  final String? notes;
  final String? terms;
  final String createdBy;
  final String? approvedBy;
  final DateTime? approvedAt;

  const PurchaseOrder({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.poNumber,
    required this.supplierId,
    required this.supplierName,
    required this.status,
    required this.orderDate,
    this.expectedDeliveryDate,
    this.actualDeliveryDate,
    required this.items,
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.shippingCost,
    required this.totalAmount,
    required this.currency,
    this.notes,
    this.terms,
    required this.createdBy,
    this.approvedBy,
    this.approvedAt,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        poNumber,
        supplierId,
        supplierName,
        status,
        orderDate,
        expectedDeliveryDate,
        actualDeliveryDate,
        items,
        subtotal,
        taxAmount,
        discountAmount,
        shippingCost,
        totalAmount,
        currency,
        notes,
        terms,
        createdBy,
        approvedBy,
        approvedAt,
      ];

  /// Check if purchase order is overdue
  bool get isOverdue {
    if (expectedDeliveryDate == null) return false;
    return DateTime.now().isAfter(expectedDeliveryDate!) && !status.isCompleted;
  }

  /// Get total quantity of all items
  double get totalQuantity => items.fold(0.0, (sum, item) => sum + item.quantity);

  /// Check if purchase order is approved
  bool get isApproved => approvedAt != null && approvedBy != null;
}

/// Purchase order item entity
class PurchaseOrderItem extends Equatable {
  final String itemId;
  final String itemCode;
  final String itemName;
  final double quantity;
  final String unit;
  final double unitPrice;
  final double totalPrice;
  final double receivedQuantity;
  final String? notes;

  const PurchaseOrderItem({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.quantity,
    required this.unit,
    required this.unitPrice,
    required this.totalPrice,
    this.receivedQuantity = 0.0,
    this.notes,
  });

  @override
  List<Object?> get props => [
        itemId,
        itemCode,
        itemName,
        quantity,
        unit,
        unitPrice,
        totalPrice,
        receivedQuantity,
        notes,
      ];

  /// Get remaining quantity to receive
  double get remainingQuantity => quantity - receivedQuantity;

  /// Check if item is fully received
  bool get isFullyReceived => receivedQuantity >= quantity;

  /// Get received percentage
  double get receivedPercentage {
    if (quantity == 0) return 0.0;
    return (receivedQuantity / quantity) * 100;
  }
}

// Enums

/// Item category enum
enum ItemCategory {
  rawMaterial,
  fabric,
  accessory,
  packaging,
  consumable,
  tool,
  equipment,
  spare,
}

/// Item category extension
extension ItemCategoryExtension on ItemCategory {
  String get displayName {
    switch (this) {
      case ItemCategory.rawMaterial:
        return 'Raw Material';
      case ItemCategory.fabric:
        return 'Fabric';
      case ItemCategory.accessory:
        return 'Accessory';
      case ItemCategory.packaging:
        return 'Packaging';
      case ItemCategory.consumable:
        return 'Consumable';
      case ItemCategory.tool:
        return 'Tool';
      case ItemCategory.equipment:
        return 'Equipment';
      case ItemCategory.spare:
        return 'Spare Part';
    }
  }

  String get value => name;
}

/// Item type enum
enum ItemType {
  cotton,
  polyester,
  silk,
  wool,
  button,
  zipper,
  thread,
  label,
  box,
  bag,
  chemical,
  dye,
  needle,
  scissors,
  machine,
  motor,
}

/// Item status enum
enum ItemStatus {
  active,
  inactive,
  discontinued,
  blocked,
}

/// Stock status enum
enum StockStatus {
  inStock,
  lowStock,
  outOfStock,
  overstock,
}

/// Stock status extension
extension StockStatusExtension on StockStatus {
  String get displayName {
    switch (this) {
      case StockStatus.inStock:
        return 'In Stock';
      case StockStatus.lowStock:
        return 'Low Stock';
      case StockStatus.outOfStock:
        return 'Out of Stock';
      case StockStatus.overstock:
        return 'Overstock';
    }
  }

  String get value => name;
}

/// Movement type enum
enum MovementType {
  purchase,
  sale,
  production,
  adjustment,
  transfer,
  returned,
  damage,
  expired,
}

/// Movement type extension
extension MovementTypeExtension on MovementType {
  String get displayName {
    switch (this) {
      case MovementType.purchase:
        return 'Purchase';
      case MovementType.sale:
        return 'Sale';
      case MovementType.production:
        return 'Production';
      case MovementType.adjustment:
        return 'Adjustment';
      case MovementType.transfer:
        return 'Transfer';
      case MovementType.returned:
        return 'Return';
      case MovementType.damage:
        return 'Damage';
      case MovementType.expired:
        return 'Expired';
    }
  }

  String get value => name;

  bool get isInbound {
    switch (this) {
      case MovementType.purchase:
      case MovementType.returned:
      case MovementType.adjustment:
        return true;
      default:
        return false;
    }
  }

  bool get isOutbound {
    switch (this) {
      case MovementType.sale:
      case MovementType.production:
      case MovementType.transfer:
      case MovementType.damage:
      case MovementType.expired:
        return true;
      default:
        return false;
    }
  }
}

/// BOM status enum
enum BOMStatus {
  draft,
  active,
  inactive,
  archived,
}

/// Purchase order status enum
enum PurchaseOrderStatus {
  draft,
  pending,
  approved,
  ordered,
  partiallyReceived,
  received,
  cancelled,
  closed,
}

/// Purchase order status extension
extension PurchaseOrderStatusExtension on PurchaseOrderStatus {
  String get displayName {
    switch (this) {
      case PurchaseOrderStatus.draft:
        return 'Draft';
      case PurchaseOrderStatus.pending:
        return 'Pending';
      case PurchaseOrderStatus.approved:
        return 'Approved';
      case PurchaseOrderStatus.ordered:
        return 'Ordered';
      case PurchaseOrderStatus.partiallyReceived:
        return 'Partially Received';
      case PurchaseOrderStatus.received:
        return 'Received';
      case PurchaseOrderStatus.cancelled:
        return 'Cancelled';
      case PurchaseOrderStatus.closed:
        return 'Closed';
    }
  }

  String get value => name;

  bool get isCompleted {
    return this == PurchaseOrderStatus.received ||
           this == PurchaseOrderStatus.closed;
  }

  bool get canEdit {
    return this == PurchaseOrderStatus.draft ||
           this == PurchaseOrderStatus.pending;
  }

  bool get canCancel {
    return this != PurchaseOrderStatus.cancelled &&
           this != PurchaseOrderStatus.received &&
           this != PurchaseOrderStatus.closed;
  }
}
