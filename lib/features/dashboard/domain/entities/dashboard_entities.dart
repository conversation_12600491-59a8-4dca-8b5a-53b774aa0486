import 'package:equatable/equatable.dart';

import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/base_entity.dart';

/// Dashboard widget configuration
class DashboardWidget extends Equatable {
  final String id;
  final String title;
  final DashboardWidgetType type;
  final Map<String, dynamic> configuration;
  final List<UserRole> allowedRoles;
  final List<Department> allowedDepartments;
  final int order;
  final bool isEnabled;
  final DashboardWidgetSize size;

  const DashboardWidget({
    required this.id,
    required this.title,
    required this.type,
    required this.configuration,
    required this.allowedRoles,
    required this.allowedDepartments,
    required this.order,
    this.isEnabled = true,
    this.size = DashboardWidgetSize.medium,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        type,
        configuration,
        allowedRoles,
        allowedDepartments,
        order,
        isEnabled,
        size,
      ];
}

/// Dashboard widget types
enum DashboardWidgetType {
  statistics,
  chart,
  recentActivity,
  quickActions,
  notifications,
  calendar,
  taskList,
  orderSummary,
  productionStatus,
  qualityMetrics,
  inventoryAlert,
  performanceKpi,
}

/// Dashboard widget sizes
enum DashboardWidgetSize {
  small,
  medium,
  large,
  extraLarge,
}

/// Extension for dashboard widget type
extension DashboardWidgetTypeExtension on DashboardWidgetType {
  String get displayName {
    switch (this) {
      case DashboardWidgetType.statistics:
        return 'Statistics';
      case DashboardWidgetType.chart:
        return 'Chart';
      case DashboardWidgetType.recentActivity:
        return 'Recent Activity';
      case DashboardWidgetType.quickActions:
        return 'Quick Actions';
      case DashboardWidgetType.notifications:
        return 'Notifications';
      case DashboardWidgetType.calendar:
        return 'Calendar';
      case DashboardWidgetType.taskList:
        return 'Task List';
      case DashboardWidgetType.orderSummary:
        return 'Order Summary';
      case DashboardWidgetType.productionStatus:
        return 'Production Status';
      case DashboardWidgetType.qualityMetrics:
        return 'Quality Metrics';
      case DashboardWidgetType.inventoryAlert:
        return 'Inventory Alert';
      case DashboardWidgetType.performanceKpi:
        return 'Performance KPI';
    }
  }

  String get description {
    switch (this) {
      case DashboardWidgetType.statistics:
        return 'Display key statistics and metrics';
      case DashboardWidgetType.chart:
        return 'Show data in chart format';
      case DashboardWidgetType.recentActivity:
        return 'Recent system activities';
      case DashboardWidgetType.quickActions:
        return 'Quick action buttons';
      case DashboardWidgetType.notifications:
        return 'System notifications';
      case DashboardWidgetType.calendar:
        return 'Calendar view';
      case DashboardWidgetType.taskList:
        return 'Task management';
      case DashboardWidgetType.orderSummary:
        return 'Order overview';
      case DashboardWidgetType.productionStatus:
        return 'Production line status';
      case DashboardWidgetType.qualityMetrics:
        return 'Quality control metrics';
      case DashboardWidgetType.inventoryAlert:
        return 'Inventory alerts';
      case DashboardWidgetType.performanceKpi:
        return 'Performance indicators';
    }
  }
}

/// Dashboard statistics
class DashboardStatistics extends Equatable {
  final String title;
  final String value;
  final String? subtitle;
  final String? trend;
  final double? trendPercentage;
  final StatisticTrend trendDirection;
  final String? icon;
  final String? color;

  const DashboardStatistics({
    required this.title,
    required this.value,
    this.subtitle,
    this.trend,
    this.trendPercentage,
    this.trendDirection = StatisticTrend.neutral,
    this.icon,
    this.color,
  });

  @override
  List<Object?> get props => [
        title,
        value,
        subtitle,
        trend,
        trendPercentage,
        trendDirection,
        icon,
        color,
      ];
}

/// Statistic trend direction
enum StatisticTrend {
  up,
  down,
  neutral,
}

/// Dashboard chart data
class DashboardChartData extends Equatable {
  final String title;
  final ChartType type;
  final List<ChartDataPoint> data;
  final Map<String, dynamic>? configuration;

  const DashboardChartData({
    required this.title,
    required this.type,
    required this.data,
    this.configuration,
  });

  @override
  List<Object?> get props => [title, type, data, configuration];
}

/// Chart types
enum ChartType {
  line,
  bar,
  pie,
  doughnut,
  area,
  scatter,
}

/// Chart data point
class ChartDataPoint extends Equatable {
  final String label;
  final double value;
  final String? color;
  final Map<String, dynamic>? metadata;

  const ChartDataPoint({
    required this.label,
    required this.value,
    this.color,
    this.metadata,
  });

  @override
  List<Object?> get props => [label, value, color, metadata];
}

/// Dashboard activity
class DashboardActivity extends BaseEntity {
  final String userId;
  final String userName;
  final String action;
  final String description;
  final ActivityType type;
  final String? entityId;
  final String? entityType;
  final Map<String, dynamic>? metadata;

  const DashboardActivity({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    required this.userId,
    required this.userName,
    required this.action,
    required this.description,
    required this.type,
    this.entityId,
    this.entityType,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        userId,
        userName,
        action,
        description,
        type,
        entityId,
        entityType,
        metadata,
      ];
}

/// Activity types
enum ActivityType {
  userLogin,
  userLogout,
  orderCreated,
  orderUpdated,
  productionStarted,
  productionCompleted,
  qualityCheck,
  inventoryUpdate,
  systemAlert,
  other,
}

/// Dashboard notification
class DashboardNotification extends BaseEntity {
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final String? actionUrl;
  final String? actionLabel;
  final bool isRead;
  final String? userId;
  final List<UserRole>? targetRoles;
  final List<Department>? targetDepartments;

  const DashboardNotification({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.deletedAt,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    this.actionUrl,
    this.actionLabel,
    this.isRead = false,
    this.userId,
    this.targetRoles,
    this.targetDepartments,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        title,
        message,
        type,
        priority,
        actionUrl,
        actionLabel,
        isRead,
        userId,
        targetRoles,
        targetDepartments,
      ];
}

/// Notification types
enum NotificationType {
  info,
  warning,
  error,
  success,
  system,
}

/// Notification priority
enum NotificationPriority {
  low,
  medium,
  high,
  urgent,
}

/// Quick action
class QuickAction extends Equatable {
  final String id;
  final String title;
  final String description;
  final String icon;
  final String route;
  final List<UserRole> allowedRoles;
  final List<Department> allowedDepartments;
  final String? color;
  final bool isEnabled;

  const QuickAction({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.route,
    required this.allowedRoles,
    required this.allowedDepartments,
    this.color,
    this.isEnabled = true,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        icon,
        route,
        allowedRoles,
        allowedDepartments,
        color,
        isEnabled,
      ];
}

/// Dashboard layout configuration
class DashboardLayout extends Equatable {
  final String userId;
  final UserRole role;
  final Department department;
  final List<DashboardWidget> widgets;
  final Map<String, dynamic> preferences;

  const DashboardLayout({
    required this.userId,
    required this.role,
    required this.department,
    required this.widgets,
    this.preferences = const {},
  });

  @override
  List<Object?> get props => [userId, role, department, widgets, preferences];
}
