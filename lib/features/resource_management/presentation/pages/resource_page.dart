import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../domain/entities/facility_entities.dart';
import '../../domain/entities/machine_entities.dart';
import '../../domain/entities/worker_entities.dart';
import '../bloc/resource_bloc.dart';
import '../widgets/resource_app_bar.dart';
import '../widgets/resource_statistics_card.dart';

/// Resource management page
class ResourcePage extends StatefulWidget {
  const ResourcePage({super.key});

  @override
  State<ResourcePage> createState() => _ResourcePageState();
}

class _ResourcePageState extends State<ResourcePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ResourceType _currentResourceType = ResourceType.machine;
  ResourceViewMode _viewMode = ResourceViewMode.list;
  String? _searchQuery;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChanged);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ResourceAppBar(
        title: 'Resource Management',
        onSearchPressed: _handleSearch,
        onFilterPressed: _showFilterDialog,
        onAdd: _navigateToCreateResource,
        onViewModeChanged: _handleViewModeChanged,
        currentViewMode: _viewMode,
        currentResourceType: _currentResourceType,
      ),
      body: Column(
        children: [
          _buildTabBar(),
          _buildStatisticsSection(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildMachinesTab(),
                _buildWorkersTab(),
                _buildFacilitiesTab(),
                _buildDepartmentsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreateResource,
        tooltip: 'Add ${_currentResourceType.displayName}',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: 'Machines'),
          Tab(text: 'Workers'),
          Tab(text: 'Facilities'),
          Tab(text: 'Departments'),
        ],
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return BlocBuilder<ResourceBloc, ResourceState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: ResourceStatisticsCard(
            statistics: _extractStatisticsFromState(state),
            onTap: () => _handleStatisticTap('statistics'),
          ),
        );
      },
    );
  }

  Widget _buildMachinesTab() {
    return BlocBuilder<ResourceBloc, ResourceState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildMachinesList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildWorkersTab() {
    return BlocBuilder<ResourceBloc, ResourceState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildWorkersList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFacilitiesTab() {
    return BlocBuilder<ResourceBloc, ResourceState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildFacilitiesList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDepartmentsTab() {
    return BlocBuilder<ResourceBloc, ResourceState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildDepartmentsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search ${_currentResourceType.displayName.toLowerCase()}...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery != null
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: _clearSearch,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onChanged: _handleSearch,
      ),
    );
  }

  Widget _buildMachinesList(ResourceState state) {
    if (state is ResourceLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is ResourceError) {
      return _buildErrorWidget(state.message);
    }

    if (state is MachinesLoaded) {
      if (state.machines.isEmpty) {
        return _buildEmptyState('No machines found');
      }

      return RefreshIndicator(
        onRefresh: () async => _refreshResources(),
        child: ListView.builder(
          itemCount: state.machines.length,
          itemBuilder: (context, index) {
            final machine = state.machines[index];
            return _buildMachineCard(machine);
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildWorkersList(ResourceState state) {
    if (state is ResourceLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is ResourceError) {
      return _buildErrorWidget(state.message);
    }

    if (state is WorkersLoaded) {
      if (state.workers.isEmpty) {
        return _buildEmptyState('No workers found');
      }

      return RefreshIndicator(
        onRefresh: () async => _refreshResources(),
        child: ListView.builder(
          itemCount: state.workers.length,
          itemBuilder: (context, index) {
            final worker = state.workers[index];
            return _buildWorkerCard(worker);
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildFacilitiesList(ResourceState state) {
    if (state is ResourceLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is ResourceError) {
      return _buildErrorWidget(state.message);
    }

    if (state is FacilitiesLoaded) {
      if (state.facilities.isEmpty) {
        return _buildEmptyState('No facilities found');
      }

      return RefreshIndicator(
        onRefresh: () async => _refreshResources(),
        child: ListView.builder(
          itemCount: state.facilities.length,
          itemBuilder: (context, index) {
            final facility = state.facilities[index];
            return _buildFacilityCard(facility);
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildDepartmentsList(ResourceState state) {
    if (state is ResourceLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is ResourceError) {
      return _buildErrorWidget(state.message);
    }

    if (state is DepartmentsLoaded) {
      if (state.departments.isEmpty) {
        return _buildEmptyState('No departments found');
      }

      return RefreshIndicator(
        onRefresh: () async => _refreshResources(),
        child: ListView.builder(
          itemCount: state.departments.length,
          itemBuilder: (context, index) {
            final department = state.departments[index];
            return _buildDepartmentCard(department as ResourceDepartment);
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildMachineCard(Machine machine) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: 4,
      ),
      child: ListTile(
        leading: Icon(
          _getMachineIcon(machine.type),
          color: _getMachineStatusColor(machine.status),
        ),
        title: Text(machine.machineName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${machine.machineCode} • ${machine.type.displayName}'),
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(machine.status.displayName),
                  backgroundColor: _getMachineStatusColor(machine.status).withAlpha(26),
                ),
                const SizedBox(width: 8),
                Text(
                  machine.departmentName,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${machine.utilizationRate.toStringAsFixed(1)}%',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              'Utilization',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        onTap: () => _navigateToResourceDetails(ResourceType.machine, machine.id),
      ),
    );
  }

  Widget _buildWorkerCard(Worker worker) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: 4,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getWorkerStatusColor(worker.status),
          child: Text(
            worker.firstName[0] + worker.lastName[0],
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(worker.fullName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${worker.employeeId} • ${worker.role.displayName}'),
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(worker.status.displayName),
                  backgroundColor: _getWorkerStatusColor(worker.status).withAlpha(26),
                ),
                const SizedBox(width: 8),
                Text(
                  worker.departmentName,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              worker.performance.overallRating.toStringAsFixed(1),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              'Rating',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        onTap: () => _navigateToResourceDetails(ResourceType.worker, worker.id),
      ),
    );
  }

  Widget _buildFacilityCard(Facility facility) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: 4,
      ),
      child: ListTile(
        leading: Icon(
          _getFacilityIcon(facility.type),
          color: _getFacilityStatusColor(facility.status),
        ),
        title: Text(facility.facilityName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${facility.facilityCode} • ${facility.type.displayName}'),
            const SizedBox(height: 4),
            Text(facility.city),
            const SizedBox(height: 4),
            Chip(
              label: Text(facility.status.displayName),
              backgroundColor: _getFacilityStatusColor(facility.status).withAlpha(26),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${facility.capacity.workerUtilization.toStringAsFixed(1)}%',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              'Capacity',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        onTap: () => _navigateToResourceDetails(ResourceType.facility, facility.id),
      ),
    );
  }

  Widget _buildDepartmentCard(ResourceDepartment department) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: 4,
      ),
      child: ListTile(
        leading: Icon(
          _getDepartmentIcon(department.type),
          color: department.isActive ? Colors.green : Colors.grey,
        ),
        title: Text(department.departmentName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${department.departmentCode} • ${department.type.displayName}'),
            const SizedBox(height: 4),
            Text(department.facilityName),
            const SizedBox(height: 4),
            Row(
              children: [
                Text('${department.workerCount} workers'),
                const SizedBox(width: 16),
                Text('${department.machineCount} machines'),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${department.budgetUtilization.toStringAsFixed(1)}%',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              'Budget',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        onTap: () => _navigateToResourceDetails(ResourceType.department, department.id),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading resources',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _refreshResources,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first ${_currentResourceType.displayName.toLowerCase()} to get started.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _navigateToCreateResource,
            child: Text('Add ${_currentResourceType.displayName}'),
          ),
        ],
      ),
    );
  }

  IconData _getMachineIcon(MachineType type) {
    switch (type) {
      case MachineType.cutting:
        return Icons.content_cut;
      case MachineType.sewing:
        return Icons.design_services;
      case MachineType.embroidery:
        return Icons.brush;
      case MachineType.pressing:
        return Icons.iron;
      case MachineType.finishing:
        return Icons.auto_fix_high;
      case MachineType.packaging:
        return Icons.inventory;
      case MachineType.inspection:
        return Icons.search;
      case MachineType.utility:
        return Icons.build;
    }
  }

  Color _getMachineStatusColor(MachineStatus status) {
    switch (status) {
      case MachineStatus.available:
        return Colors.green;
      case MachineStatus.inUse:
        return Colors.blue;
      case MachineStatus.maintenance:
        return Colors.orange;
      case MachineStatus.outOfOrder:
        return Colors.red;
      case MachineStatus.retired:
        return Colors.grey;
    }
  }

  Color _getWorkerStatusColor(WorkerStatus status) {
    switch (status) {
      case WorkerStatus.active:
        return Colors.green;
      case WorkerStatus.available:
        return Colors.blue;
      case WorkerStatus.busy:
        return Colors.orange;
      case WorkerStatus.onLeave:
        return Colors.purple;
      case WorkerStatus.inactive:
        return Colors.grey;
      case WorkerStatus.terminated:
        return Colors.red;
    }
  }

  IconData _getFacilityIcon(FacilityType type) {
    switch (type) {
      case FacilityType.manufacturing:
        return Icons.factory;
      case FacilityType.warehouse:
        return Icons.warehouse;
      case FacilityType.office:
        return Icons.business;
      case FacilityType.mixed:
        return Icons.domain;
    }
  }

  Color _getFacilityStatusColor(FacilityStatus status) {
    switch (status) {
      case FacilityStatus.operational:
        return Colors.green;
      case FacilityStatus.maintenance:
        return Colors.orange;
      case FacilityStatus.construction:
        return Colors.blue;
      case FacilityStatus.closed:
        return Colors.red;
      case FacilityStatus.decommissioned:
        return Colors.grey;
    }
  }

  IconData _getDepartmentIcon(DepartmentType type) {
    switch (type) {
      case DepartmentType.fabric:
        return Icons.texture;
      case DepartmentType.production:
        return Icons.precision_manufacturing;
      case DepartmentType.quality:
        return Icons.verified;
      case DepartmentType.maintenance:
        return Icons.build_circle;
      case DepartmentType.warehouse:
        return Icons.inventory_2;
      case DepartmentType.administration:
        return Icons.admin_panel_settings;
      case DepartmentType.research:
        return Icons.science;
      case DepartmentType.stitching:
        return Icons.construction;
      case DepartmentType.packing:
        return Icons.inventory_2;
      case DepartmentType.finishing:
        return Icons.auto_fix_high;
      case DepartmentType.cutting:
        return Icons.content_cut;
      case DepartmentType.other:
        return Icons.business;
    }
  }

  void _handleTabChanged() {
    final index = _tabController.index;
    setState(() {
      _currentResourceType = ResourceType.values[index];
    });

    switch (index) {
      case 0:
        context.read<ResourceBloc>().add(const LoadMachinesRequested());
        break;
      case 1:
        context.read<ResourceBloc>().add(const LoadWorkersRequested());
        break;
      case 2:
        context.read<ResourceBloc>().add(const LoadFacilitiesRequested());
        break;
      case 3:
        context.read<ResourceBloc>().add(const LoadDepartmentsRequested());
        break;
    }
  }

  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query.isEmpty ? null : query;
    });

    if (query.isEmpty) {
      _refreshResources();
    } else {
      switch (_currentResourceType) {
        case ResourceType.machine:
          context.read<ResourceBloc>().add(SearchMachinesRequested(query));
          break;
        case ResourceType.worker:
          context.read<ResourceBloc>().add(SearchWorkersRequested(query));
          break;
        case ResourceType.facility:
          // TODO: Implement facility search
          break;
        case ResourceType.department:
          // TODO: Implement department search
          break;
      }
    }
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = null;
    });
    _refreshResources();
  }

  void _showFilterDialog() async {
    // TODO: Implement filter dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Filter dialog coming soon')),
    );
  }

  void _handleViewModeChanged(ResourceViewMode viewMode) {
    setState(() {
      _viewMode = viewMode;
    });
    context.read<ResourceBloc>().add(ChangeResourceViewModeRequested(_currentResourceType, viewMode));
  }

  void _handleStatisticTap(String statistic) {
    // Handle statistic tap for filtering or navigation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Statistic tapped: $statistic')),
    );
  }

  void _navigateToResourceDetails(ResourceType type, String resourceId) {
    // TODO: Navigate to resource details page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Navigate to ${type.displayName} $resourceId details')),
    );
  }

  void _navigateToCreateResource() {
    // TODO: Navigate to create resource page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Create ${_currentResourceType.displayName} feature coming soon')),
    );
  }

  void _refreshResources() {
    switch (_currentResourceType) {
      case ResourceType.machine:
        context.read<ResourceBloc>().add(const RefreshMachinesRequested());
        break;
      case ResourceType.worker:
        context.read<ResourceBloc>().add(const RefreshWorkersRequested());
        break;
      case ResourceType.facility:
        context.read<ResourceBloc>().add(const LoadFacilitiesRequested());
        break;
      case ResourceType.department:
        context.read<ResourceBloc>().add(const LoadDepartmentsRequested());
        break;
    }
  }

  /// Extract statistics from the current resource state
  ResourceStatistics _extractStatisticsFromState(ResourceState state) {
    int totalWorkers = 0;
    int activeWorkers = 0;
    int totalFacilities = 0;
    int totalDepartments = 0;

    if (state is WorkersLoaded) {
      totalWorkers = state.workers.length;
      activeWorkers = state.activeWorkersCount;
    }

    if (state is FacilitiesLoaded) {
      totalFacilities = state.facilities.length;
    }

    if (state is DepartmentsLoaded) {
      totalDepartments = state.departments.length;
    }

    // For machines loaded state, we don't have direct worker/facility data
    // So we'll use default values or could make additional API calls
    if (state is MachinesLoaded) {
      // Use placeholder values or make additional calls to get complete statistics
      totalWorkers = 0;
      activeWorkers = 0;
      totalFacilities = 0;
      totalDepartments = 0;
    }

    return ResourceStatistics(
      totalWorkers: totalWorkers,
      activeWorkers: activeWorkers,
      totalFacilities: totalFacilities,
      totalDepartments: totalDepartments,
    );
  }
}
