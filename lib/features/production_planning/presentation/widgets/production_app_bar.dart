import 'package:flutter/material.dart';

/// Custom app bar for production planning pages
class ProductionAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onAddPressed;
  final bool showSearch;
  final bool showFilter;
  final bool showAdd;

  const ProductionAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.onSearchPressed,
    this.onFilterPressed,
    this.onAddPressed,
    this.showSearch = true,
    this.showFilter = true,
    this.showAdd = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (showSearch)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: onSearchPressed,
            tooltip: 'Search',
          ),
        if (showFilter)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: onFilterPressed,
            tooltip: 'Filter',
          ),
        if (showAdd)
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: onAddPressed,
            tooltip: 'Add New',
          ),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Production app bar with search functionality
class ProductionSearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchClear;
  final VoidCallback? onFilterPressed;
  final bool isSearching;
  final TextEditingController? searchController;

  const ProductionSearchAppBar({
    Key? key,
    required this.title,
    this.hintText = 'Search production orders...',
    this.onSearchChanged,
    this.onSearchClear,
    this.onFilterPressed,
    this.isSearching = false,
    this.searchController,
  }) : super(key: key);

  @override
  State<ProductionSearchAppBar> createState() => _ProductionSearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _ProductionSearchAppBarState extends State<ProductionSearchAppBar> {
  late bool _isSearching;
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _isSearching = widget.isSearching;
    _searchController = widget.searchController ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.searchController == null) {
      _searchController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: _isSearching
          ? TextField(
              controller: _searchController,
              onChanged: widget.onSearchChanged,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
                border: InputBorder.none,
              ),
              autofocus: true,
            )
          : Text(
              widget.title,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (_isSearching)
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              widget.onSearchClear?.call();
              setState(() => _isSearching = false);
            },
            tooltip: 'Clear Search',
          )
        else
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => setState(() => _isSearching = true),
            tooltip: 'Search',
          ),
        if (widget.onFilterPressed != null)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: widget.onFilterPressed,
            tooltip: 'Filter',
          ),
      ],
    );
  }
}

/// Production app bar with tabs
class ProductionTabAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Tab> tabs;
  final TabController? controller;
  final List<Widget>? actions;

  const ProductionTabAppBar({
    Key? key,
    required this.title,
    required this.tabs,
    this.controller,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: actions,
      bottom: TabBar(
        controller: controller,
        tabs: tabs,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withOpacity(0.7),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + kTextTabBarHeight,
  );
}