import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../../auth/domain/entities/user.dart';
import '../../domain/entities/dashboard_entities.dart';
import '../../domain/usecases/dashboard_usecases.dart';

part 'dashboard_event.dart';
part 'dashboard_state.dart';

/// Dashboard Bloc
@injectable
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetDashboardLayoutUseCase _getDashboardLayoutUseCase;
  final UpdateDashboardLayoutUseCase _updateDashboardLayoutUseCase;
  final GetDashboardStatisticsUseCase _getDashboardStatisticsUseCase;
  final GetDashboardChartsUseCase _getDashboardChartsUseCase;
  final GetRecentActivitiesUseCase _getRecentActivitiesUseCase;
  final GetNotificationsUseCase _getNotificationsUseCase;
  final MarkNotificationAsReadUseCase _markNotificationAsReadUseCase;
  final GetQuickActionsUseCase _getQuickActionsUseCase;
  final GetWidgetDataUseCase _getWidgetDataUseCase;
  final GetAnalyticsUseCase _getAnalyticsUseCase;

  DashboardBloc(
    this._getDashboardLayoutUseCase,
    this._updateDashboardLayoutUseCase,
    this._getDashboardStatisticsUseCase,
    this._getDashboardChartsUseCase,
    this._getRecentActivitiesUseCase,
    this._getNotificationsUseCase,
    this._markNotificationAsReadUseCase,
    this._getQuickActionsUseCase,
    this._getWidgetDataUseCase,
    this._getAnalyticsUseCase,
  ) : super(const DashboardInitial()) {
    on<LoadDashboardRequested>(_onLoadDashboardRequested);
    on<RefreshDashboardRequested>(_onRefreshDashboardRequested);
    on<UpdateDashboardLayoutRequested>(_onUpdateDashboardLayoutRequested);
    on<LoadDashboardStatisticsRequested>(_onLoadDashboardStatisticsRequested);
    on<LoadDashboardChartsRequested>(_onLoadDashboardChartsRequested);
    on<LoadRecentActivitiesRequested>(_onLoadRecentActivitiesRequested);
    on<LoadNotificationsRequested>(_onLoadNotificationsRequested);
    on<MarkNotificationAsReadRequested>(_onMarkNotificationAsReadRequested);
    on<LoadQuickActionsRequested>(_onLoadQuickActionsRequested);
    on<LoadWidgetDataRequested>(_onLoadWidgetDataRequested);
    on<LoadAnalyticsRequested>(_onLoadAnalyticsRequested);
    on<FilterDashboardDataRequested>(_onFilterDashboardDataRequested);
  }

  Future<void> _onLoadDashboardRequested(
    LoadDashboardRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    try {
      // Load dashboard layout
      final layoutResult = await _getDashboardLayoutUseCase(IdParams(event.user.id));
      
      if (layoutResult.isLeft()) {
        emit(DashboardError(layoutResult.fold((l) => l.message, (r) => '')));
        return;
      }

      final layout = layoutResult.getOrElse(() => _getDefaultLayout(event.user));

      // Load dashboard data in parallel
      final futures = await Future.wait([
        _getDashboardStatisticsUseCase(GetDashboardDataParams(event.user.role, event.user.department)),
        _getDashboardChartsUseCase(GetDashboardChartsParams(event.user.role, event.user.department)),
        _getRecentActivitiesUseCase(GetActivitiesParams(
          userId: event.user.id,
          role: event.user.role,
          department: event.user.department,
          pagination: const PaginationParams(page: 1, perPage: 10),
        )),
        _getNotificationsUseCase(GetNotificationsParams(
          userId: event.user.id,
          unreadOnly: true,
          pagination: const PaginationParams(page: 1, perPage: 5),
        )),
        _getQuickActionsUseCase(GetDashboardDataParams(event.user.role, event.user.department)),
      ]);

      final statistics = futures[0].fold((l) => <DashboardStatistics>[], (r) => r as List<DashboardStatistics>);
      final charts = futures[1].fold((l) => <DashboardChartData>[], (r) => r as List<DashboardChartData>);
      final activities = futures[2].fold((l) => <DashboardActivity>[], (r) => (r as dynamic).data ?? <DashboardActivity>[]);
      final notifications = futures[3].fold((l) => <DashboardNotification>[], (r) => (r as dynamic).data ?? <DashboardNotification>[]);
      final quickActions = futures[4].fold((l) => <QuickAction>[], (r) => r as List<QuickAction>);

      emit(DashboardLoaded(
        layout: layout,
        statistics: statistics,
        charts: charts,
        recentActivities: activities,
        notifications: notifications,
        quickActions: quickActions,
      ));
    } catch (e) {
      emit(DashboardError('Failed to load dashboard: $e'));
    }
  }

  Future<void> _onRefreshDashboardRequested(
    RefreshDashboardRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      emit(currentState.copyWith(isRefreshing: true));

      try {
        // Refresh dashboard data
        final user = event.user;
        final futures = await Future.wait([
          _getDashboardStatisticsUseCase(GetDashboardDataParams(user.role, user.department)),
          _getDashboardChartsUseCase(GetDashboardChartsParams(user.role, user.department)),
          _getRecentActivitiesUseCase(GetActivitiesParams(
            userId: user.id,
            role: user.role,
            department: user.department,
            pagination: const PaginationParams(page: 1, perPage: 10),
          )),
          _getNotificationsUseCase(GetNotificationsParams(
            userId: user.id,
            unreadOnly: true,
            pagination: const PaginationParams(page: 1, perPage: 5),
          )),
        ]);

        final statistics = futures[0].fold((l) => currentState.statistics, (r) => r as List<DashboardStatistics>);
        final charts = futures[1].fold((l) => currentState.charts, (r) => r as List<DashboardChartData>);
        final activities = futures[2].fold((l) => currentState.recentActivities, (r) => (r as dynamic).data ?? <DashboardActivity>[]);
        final notifications = futures[3].fold((l) => currentState.notifications, (r) => (r as dynamic).data ?? <DashboardNotification>[]);

        emit(currentState.copyWith(
          statistics: statistics,
          charts: charts,
          recentActivities: activities,
          notifications: notifications,
          isRefreshing: false,
        ));
      } catch (e) {
        emit(currentState.copyWith(isRefreshing: false));
        emit(DashboardError('Failed to refresh dashboard: $e'));
      }
    }
  }

  Future<void> _onUpdateDashboardLayoutRequested(
    UpdateDashboardLayoutRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _updateDashboardLayoutUseCase(UpdateDashboardLayoutParams(event.layout));

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (success) {
        final currentState = state;
        if (currentState is DashboardLoaded) {
          emit(currentState.copyWith(layout: event.layout));
        }
      },
    );
  }

  Future<void> _onLoadDashboardStatisticsRequested(
    LoadDashboardStatisticsRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _getDashboardStatisticsUseCase(
      GetDashboardDataParams(event.role, event.department),
    );

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (statistics) {
        final currentState = state;
        if (currentState is DashboardLoaded) {
          emit(currentState.copyWith(statistics: statistics));
        }
      },
    );
  }

  Future<void> _onLoadDashboardChartsRequested(
    LoadDashboardChartsRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _getDashboardChartsUseCase(
      GetDashboardChartsParams(
        event.role,
        event.department,
        startDate: event.startDate,
        endDate: event.endDate,
      ),
    );

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (charts) {
        final currentState = state;
        if (currentState is DashboardLoaded) {
          emit(currentState.copyWith(charts: charts));
        }
      },
    );
  }

  Future<void> _onLoadRecentActivitiesRequested(
    LoadRecentActivitiesRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _getRecentActivitiesUseCase(GetActivitiesParams(
      userId: event.userId,
      role: event.role,
      department: event.department,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (response) {
        final currentState = state;
        if (currentState is DashboardLoaded) {
          emit(currentState.copyWith(recentActivities: response.data ?? []));
        }
      },
    );
  }

  Future<void> _onLoadNotificationsRequested(
    LoadNotificationsRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _getNotificationsUseCase(GetNotificationsParams(
      userId: event.userId,
      unreadOnly: event.unreadOnly,
      pagination: event.pagination,
    ));

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (response) {
        final currentState = state;
        if (currentState is DashboardLoaded) {
          emit(currentState.copyWith(notifications: response.data ?? []));
        }
      },
    );
  }

  Future<void> _onMarkNotificationAsReadRequested(
    MarkNotificationAsReadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _markNotificationAsReadUseCase(IdParams(event.notificationId));

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (success) {
        final currentState = state;
        if (currentState is DashboardLoaded) {
          final updatedNotifications = currentState.notifications.map((notification) {
            if (notification.id == event.notificationId) {
              return DashboardNotification(
                id: notification.id,
                createdAt: notification.createdAt,
                updatedAt: notification.updatedAt,
                title: notification.title,
                message: notification.message,
                type: notification.type,
                priority: notification.priority,
                isRead: true,
                userId: notification.userId,
                actionUrl: notification.actionUrl,
                actionLabel: notification.actionLabel,
                targetRoles: notification.targetRoles,
                targetDepartments: notification.targetDepartments,
              );
            }
            return notification;
          }).toList();

          emit(currentState.copyWith(notifications: updatedNotifications));
        }
      },
    );
  }

  Future<void> _onLoadQuickActionsRequested(
    LoadQuickActionsRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _getQuickActionsUseCase(
      GetDashboardDataParams(event.role, event.department),
    );

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (quickActions) {
        final currentState = state;
        if (currentState is DashboardLoaded) {
          emit(currentState.copyWith(quickActions: quickActions));
        }
      },
    );
  }

  Future<void> _onLoadWidgetDataRequested(
    LoadWidgetDataRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _getWidgetDataUseCase(
      GetWidgetDataParams(event.widgetId, event.configuration),
    );

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (data) => emit(WidgetDataLoaded(event.widgetId, data)),
    );
  }

  Future<void> _onLoadAnalyticsRequested(
    LoadAnalyticsRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final result = await _getAnalyticsUseCase(GetAnalyticsParams(
      event.type,
      department: event.department,
      startDate: event.startDate,
      endDate: event.endDate,
    ));

    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (data) => emit(AnalyticsLoaded(event.type, data)),
    );
  }

  Future<void> _onFilterDashboardDataRequested(
    FilterDashboardDataRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      emit(currentState.copyWith(
        dateFilter: event.dateFilter,
        departmentFilter: event.departmentFilter,
      ));
    }
  }

  DashboardLayout _getDefaultLayout(User user) {
    // Create default layout based on user role
    final defaultWidgets = _getDefaultWidgetsForRole(user.role, user.department);
    
    return DashboardLayout(
      userId: user.id,
      role: user.role,
      department: user.department,
      widgets: defaultWidgets,
    );
  }

  List<DashboardWidget> _getDefaultWidgetsForRole(UserRole role, Department department) {
    // Return default widgets based on role and department
    final widgets = <DashboardWidget>[];

    // Common widgets for all roles
    widgets.addAll([
      const DashboardWidget(
        id: 'statistics',
        title: 'Key Statistics',
        type: DashboardWidgetType.statistics,
        configuration: {},
        allowedRoles: UserRole.values,
        allowedDepartments: Department.values,
        order: 1,
        size: DashboardWidgetSize.large,
      ),
      const DashboardWidget(
        id: 'recent_activity',
        title: 'Recent Activity',
        type: DashboardWidgetType.recentActivity,
        configuration: {},
        allowedRoles: UserRole.values,
        allowedDepartments: Department.values,
        order: 2,
        size: DashboardWidgetSize.medium,
      ),
      const DashboardWidget(
        id: 'notifications',
        title: 'Notifications',
        type: DashboardWidgetType.notifications,
        configuration: {},
        allowedRoles: UserRole.values,
        allowedDepartments: Department.values,
        order: 3,
        size: DashboardWidgetSize.medium,
      ),
    ]);

    // Role-specific widgets
    switch (role) {
      case UserRole.administrator:
        widgets.addAll([
          const DashboardWidget(
            id: 'system_health',
            title: 'System Health',
            type: DashboardWidgetType.performanceKpi,
            configuration: {'metric': 'system_health'},
            allowedRoles: [UserRole.administrator],
            allowedDepartments: Department.values,
            order: 4,
          ),
          const DashboardWidget(
            id: 'user_analytics',
            title: 'User Analytics',
            type: DashboardWidgetType.chart,
            configuration: {'chart_type': 'user_activity'},
            allowedRoles: [UserRole.administrator],
            allowedDepartments: Department.values,
            order: 5,
          ),
        ]);
        break;
      case UserRole.merchandiser:
        widgets.addAll([
          const DashboardWidget(
            id: 'order_summary',
            title: 'Order Summary',
            type: DashboardWidgetType.orderSummary,
            configuration: {},
            allowedRoles: [UserRole.merchandiser],
            allowedDepartments: [Department.merchandising],
            order: 4,
          ),
        ]);
        break;
      case UserRole.sewingHead:
      case UserRole.cuttingHead:
      case UserRole.finishingHead:
        widgets.addAll([
          const DashboardWidget(
            id: 'production_status',
            title: 'Production Status',
            type: DashboardWidgetType.productionStatus,
            configuration: {},
            allowedRoles: [UserRole.sewingHead, UserRole.cuttingHead, UserRole.finishingHead],
            allowedDepartments: [Department.sewing, Department.cutting, Department.finishing],
            order: 4,
          ),
        ]);
        break;
      case UserRole.qualityController:
        widgets.addAll([
          const DashboardWidget(
            id: 'quality_metrics',
            title: 'Quality Metrics',
            type: DashboardWidgetType.qualityMetrics,
            configuration: {},
            allowedRoles: [UserRole.qualityController],
            allowedDepartments: [Department.quality],
            order: 4,
          ),
        ]);
        break;
      default:
        break;
    }

    return widgets;
  }
}
