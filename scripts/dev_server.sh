#!/bin/bash

# HM Collection Development Server Script
# This script starts a development server for the Flutter web application

set -e

echo "🚀 Starting HM Collection Development Server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "pubspec.yaml not found. Please run this script from the project root."
    exit 1
fi

print_status "Flutter version:"
flutter --version
echo

# Get dependencies
print_status "Getting dependencies..."
flutter pub get

if [ $? -ne 0 ]; then
    print_error "Failed to get dependencies"
    exit 1
fi

# Check for web support
print_status "Checking web support..."
if ! flutter devices | grep -q "Chrome"; then
    print_warning "Chrome not detected. Installing Chrome or using another browser is recommended."
fi

# Set default port
PORT=${1:-3000}
HOST=${2:-localhost}

print_status "Starting development server..."
print_status "Host: $HOST"
print_status "Port: $PORT"
print_status "URL: http://$HOST:$PORT"
echo

# Start the development server
print_success "🌐 Development server starting..."
print_status "Press Ctrl+C to stop the server"
echo

# Run Flutter web server
flutter run -d chrome --web-hostname=$HOST --web-port=$PORT

# Cleanup on exit
trap 'print_status "Shutting down development server..."; exit 0' INT TERM

print_success "Development server stopped."
