import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/usecases/usecase.dart';
import 'package:hm_collection/features/department/domain/entities/department.dart';
import 'package:hm_collection/features/department/domain/repositories/department_repository.dart';

import '../../../../core/errors/failures.dart';

class GetDepartments implements UseCase<List<Department>, NoParams> {
  final DepartmentRepository repository;

  GetDepartments(this.repository);

  @override
  Future<Either<Failure, List<Department>>> call(NoParams params) async {
    return await repository.getDepartments();
  }
}
