import 'package:equatable/equatable.dart';

/// Represents the criteria for filtering quality standards
class QualityStandardFilterCriteria extends Equatable {
  final String? name;
  final String? status;
  final String? type;
  final String? category;
  final DateTime? effectiveFrom;
  final DateTime? effectiveTo;
  final String? searchQuery;

  const QualityStandardFilterCriteria({
    this.name,
    this.status,
    this.type,
    this.category,
    this.effectiveFrom,
    this.effectiveTo,
    this.searchQuery,
  });

  /// Creates a copy of this QualityStandardFilterCriteria with the given fields replaced
  QualityStandardFilterCriteria copyWith({
    String? name,
    String? status,
    String? type,
    String? category,
    DateTime? effectiveFrom,
    DateTime? effectiveTo,
    String? searchQuery,
  }) {
    return QualityStandardFilterCriteria(
      name: name ?? this.name,
      status: status ?? this.status,
      type: type ?? this.type,
      category: category ?? this.category,
      effectiveFrom: effectiveFrom ?? this.effectiveFrom,
      effectiveTo: effectiveTo ?? this.effectiveTo,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [
        name,
        status,
        type,
        category,
        effectiveFrom,
        effectiveTo,
        searchQuery,
      ];

  /// Returns true if no filters are applied
  bool get isEmpty => props.every((prop) => prop == null);

  /// Returns true if any filter is applied
  bool get isNotEmpty => !isEmpty;
}
