part of 'auth_bloc.dart';

/// Base authentication state
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial authentication state
class AuthInitial extends AuthState {
  const AuthInitial();
}

/// Authentication loading state
class AuthLoading extends AuthState {
  const AuthLoading();
}

/// User is authenticated
class AuthAuthenticated extends AuthState {
  final User user;
  final AuthTokens tokens;
  final bool isFirstLogin;
  final bool requiresPasswordChange;
  final bool requiresTwoFactorSetup;

  const AuthAuthenticated({
    required this.user,
    required this.tokens,
    this.isFirstLogin = false,
    this.requiresPasswordChange = false,
    this.requiresTwoFactorSetup = false,
  });

  /// Check if any additional setup is required
  bool get requiresAdditionalSetup {
    return requiresPasswordChange || requiresTwoFactorSetup;
  }

  /// Copy with new values
  AuthAuthenticated copyWith({
    User? user,
    AuthTokens? tokens,
    bool? isFirstLogin,
    bool? requiresPasswordChange,
    bool? requiresTwoFactorSetup,
  }) {
    return AuthAuthenticated(
      user: user ?? this.user,
      tokens: tokens ?? this.tokens,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      requiresPasswordChange: requiresPasswordChange ?? this.requiresPasswordChange,
      requiresTwoFactorSetup: requiresTwoFactorSetup ?? this.requiresTwoFactorSetup,
    );
  }

  @override
  List<Object?> get props => [
        user,
        tokens,
        isFirstLogin,
        requiresPasswordChange,
        requiresTwoFactorSetup,
      ];
}

/// User is not authenticated
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

/// Authentication error
class AuthError extends AuthState {
  final String message;
  final dynamic error;

  const AuthError(this.message, [this.error]);

  @override
  List<Object?> get props => [message, error];
}

/// Session expired state
class AuthSessionExpiredState extends AuthState {
  final String? reason;

  const AuthSessionExpiredState({this.reason});

  @override
  List<Object?> get props => [reason];
}

/// Password change required
class AuthPasswordChangeRequired extends AuthState {
  final User user;
  final AuthTokens tokens;
  final String? reason;

  const AuthPasswordChangeRequired({
    required this.user,
    required this.tokens,
    this.reason,
  });

  @override
  List<Object?> get props => [user, tokens, reason];
}

/// Two-factor authentication required
class AuthTwoFactorRequired extends AuthState {
  final User user;
  final String? method;
  final String? message;

  const AuthTwoFactorRequired({
    required this.user,
    this.method,
    this.message,
  });

  @override
  List<Object?> get props => [user, method, message];
}

/// Biometric authentication available
class AuthBiometricAvailable extends AuthState {
  final List<String> availableTypes;

  const AuthBiometricAvailable(this.availableTypes);

  @override
  List<Object?> get props => [availableTypes];
}

/// Email verification required
class AuthEmailVerificationRequired extends AuthState {
  final User user;
  final String? message;

  const AuthEmailVerificationRequired({
    required this.user,
    this.message,
  });

  @override
  List<Object?> get props => [user, message];
}

/// Account locked
class AuthAccountLocked extends AuthState {
  final String reason;
  final DateTime? unlockTime;

  const AuthAccountLocked({
    required this.reason,
    this.unlockTime,
  });

  @override
  List<Object?> get props => [reason, unlockTime];
}

/// Account suspended
class AuthAccountSuspended extends AuthState {
  final String reason;
  final DateTime? suspendedUntil;

  const AuthAccountSuspended({
    required this.reason,
    this.suspendedUntil,
  });

  @override
  List<Object?> get props => [reason, suspendedUntil];
}

/// Password reset success
class AuthPasswordResetSuccess extends AuthState {
  final String message;

  const AuthPasswordResetSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Email verification success
class AuthEmailVerificationSuccess extends AuthState {
  final String message;

  const AuthEmailVerificationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Two-factor setup success
class AuthTwoFactorSetupSuccess extends AuthState {
  final String qrCode;
  final List<String> backupCodes;

  const AuthTwoFactorSetupSuccess({
    required this.qrCode,
    required this.backupCodes,
  });

  @override
  List<Object?> get props => [qrCode, backupCodes];
}

/// Biometric setup success
class AuthBiometricSetupSuccess extends AuthState {
  final String biometricType;

  const AuthBiometricSetupSuccess(this.biometricType);

  @override
  List<Object?> get props => [biometricType];
}

/// Profile update success
class AuthProfileUpdateSuccess extends AuthState {
  final User user;
  final String message;

  const AuthProfileUpdateSuccess({
    required this.user,
    required this.message,
  });

  @override
  List<Object?> get props => [user, message];
}

/// Preferences update success
class AuthPreferencesUpdateSuccess extends AuthState {
  final UserPreferences preferences;
  final String message;

  const AuthPreferencesUpdateSuccess({
    required this.preferences,
    required this.message,
  });

  @override
  List<Object?> get props => [preferences, message];
}

/// Session revoked
class AuthSessionRevoked extends AuthState {
  final String sessionId;
  final String message;

  const AuthSessionRevoked({
    required this.sessionId,
    required this.message,
  });

  @override
  List<Object?> get props => [sessionId, message];
}

/// All sessions revoked
class AuthAllSessionsRevoked extends AuthState {
  final String message;

  const AuthAllSessionsRevoked(this.message);

  @override
  List<Object?> get props => [message];
}

/// Cache cleared
class AuthCacheCleared extends AuthState {
  final String message;

  const AuthCacheCleared(this.message);

  @override
  List<Object?> get props => [message];
}

/// Force logout
class AuthForceLogout extends AuthState {
  final String reason;

  const AuthForceLogout(this.reason);

  @override
  List<Object?> get props => [reason];
}

/// Token refresh success
class AuthTokenRefreshSuccess extends AuthState {
  final AuthTokens tokens;

  const AuthTokenRefreshSuccess(this.tokens);

  @override
  List<Object?> get props => [tokens];
}
