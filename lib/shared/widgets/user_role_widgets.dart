import 'package:flutter/material.dart';

import '../../core/auth/entities/user_entities.dart' hide UserRole, UserStatus;
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../enums/common_enums.dart';

/// Widget that displays a user role as a badge
class RoleBadge extends StatelessWidget {
  final UserRole role;
  final double? fontSize;

  const RoleBadge({
    super.key,
    required this.role,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getRoleColor(role).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getRoleColor(role).withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        role.displayName,
        style: AppTextStyles.bodySmall.copyWith(
          color: _getRoleColor(role),
          fontWeight: FontWeight.w600,
          fontSize: fontSize,
        ),
      ),
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return Colors.red;
      case UserRole.merchandiser:
        return Colors.purple;
      case UserRole.sewingSupervisor:
        return Colors.blue;
      case UserRole.sewingOperator:
        return Colors.green;
      case UserRole.qualityController:
        return Colors.orange;
      case UserRole.finishingOperator:
        return Colors.grey;
      case UserRole.cuttingHelper:
        return Colors.teal;
      case UserRole.warehouseManager:
        return Colors.brown;
      case UserRole.inventoryManager:
        return Colors.indigo;
      case UserRole.cuttingHead:
        return Colors.deepOrange;
      case UserRole.cuttingMaster:
        return Colors.amber;
      case UserRole.sewingHead:
        return Colors.cyan;
      case UserRole.finishingHead:
        return Colors.lime;
      case UserRole.viewer:
        return Colors.blueGrey;
      case UserRole.mundaOperator:
        return Colors.pink;
        throw UnimplementedError();
    }
  }
}

/// Widget that displays a user status as a chip
class UserStatusChip extends StatelessWidget {
  final CommonStatus status;
  final double? fontSize;

  const UserStatusChip({
    super.key,
    required this.status,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(status).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: _getStatusColor(status),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            status.displayName,
            style: AppTextStyles.bodySmall.copyWith(
              color: _getStatusColor(status),
              fontWeight: FontWeight.w600,
              fontSize: fontSize,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(CommonStatus status) {
    switch (status) {
      case CommonStatus.active:
        return Colors.green;
      case CommonStatus.inactive:
        return Colors.grey;
      case CommonStatus.suspended:
        return Colors.red;
      case CommonStatus.pending:
        return Colors.orange;
      case CommonStatus.archived:
        return Colors.grey.withValues(alpha: 0.5);
    }
  }
}

/// Widget that displays a permission as a chip
class PermissionChip extends StatelessWidget {
  final String permission;
  final bool granted;
  final VoidCallback? onTap;

  const PermissionChip({
    super.key,
    required this.permission,
    required this.granted,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final color = granted ? Colors.green : Colors.red;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              granted ? Icons.check : Icons.close,
              size: 12,
              color: color,
            ),
            const SizedBox(width: 4),
            Text(
              _formatPermissionName(permission),
              style: AppTextStyles.bodySmall.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatPermissionName(String permission) {
    // Convert permission like "users.read" to "Users Read"
    final parts = permission.split('.');
    if (parts.length == 2) {
      final resource = parts[0];
      final action = parts[1];
      return '${_capitalize(resource)} ${_capitalize(action)}';
    }
    return _capitalize(permission);
  }

  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}

/// Widget that displays user hierarchy level
class UserHierarchyWidget extends StatelessWidget {
  final UserRole role;
  final bool showLevel;

  const UserHierarchyWidget({
    super.key,
    required this.role,
    this.showLevel = true,
  });

  @override
  Widget build(BuildContext context) {
    final level = _getHierarchyLevel(role);
    final maxLevel = 6; // Total number of roles

    return Column(
      children: [
        if (showLevel)
          Text(
            'Level $level',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        const SizedBox(height: 4),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(maxLevel, (index) {
            final isActive = index < level;
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 1),
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: isActive ? _getRoleColor(role) : Colors.grey.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
            );
          }),
        ),
      ],
    );
  }

  int _getHierarchyLevel(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return 8;
      case UserRole.merchandiser:
        return 7;
      case UserRole.sewingSupervisor:
        return 6;
      case UserRole.qualityController:
        return 5;
      case UserRole.sewingOperator:
        return 4;
      case UserRole.cuttingHelper:
        return 3;
      case UserRole.finishingOperator:
        return 2;
      case UserRole.warehouseManager:
        return 1;
      case UserRole.inventoryManager:
        return 6;
      case UserRole.cuttingHead:
        return 7;
      case UserRole.cuttingMaster:
        return 6;
      case UserRole.sewingHead:
        return 7;
      case UserRole.finishingHead:
        return 7;
      case UserRole.viewer:
        return 1;
      case UserRole.mundaOperator:
        return 9;
        throw UnimplementedError();
    }
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return Colors.red;
      case UserRole.merchandiser:
        return Colors.purple;
      case UserRole.sewingSupervisor:
        return Colors.blue;
      case UserRole.sewingOperator:
        return Colors.green;
      case UserRole.qualityController:
        return Colors.orange;
      case UserRole.cuttingHelper:
        return Colors.teal;
      case UserRole.finishingOperator:
        return Colors.grey;
      case UserRole.warehouseManager:
        return Colors.brown;
      case UserRole.inventoryManager:
        return Colors.indigo;
      case UserRole.cuttingHead:
        return Colors.deepOrange;
      case UserRole.cuttingMaster:
        return Colors.amber;
      case UserRole.sewingHead:
        return Colors.cyan;
      case UserRole.finishingHead:
        return Colors.lime;
      case UserRole.viewer:
        return Colors.blueGrey;
      case UserRole.mundaOperator:
        return Colors.pink;
        throw UnimplementedError();
    }
  }
}

/// Widget that shows role comparison
class RoleComparisonWidget extends StatelessWidget {
  final UserRole currentRole;
  final UserRole targetRole;

  const RoleComparisonWidget({
    super.key,
    required this.currentRole,
    required this.targetRole,
  });

  @override
  Widget build(BuildContext context) {
    final currentLevel = _getHierarchyLevel(currentRole);
    final targetLevel = _getHierarchyLevel(targetRole);
    final isUpgrade = targetLevel > currentLevel;
    final isDowngrade = targetLevel < currentLevel;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isUpgrade 
            ? Colors.green.withValues(alpha: 0.1)
            : isDowngrade 
                ? Colors.orange.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUpgrade 
              ? Colors.green.withValues(alpha: 0.3)
              : isDowngrade 
                  ? Colors.orange.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          RoleBadge(role: currentRole),
          const SizedBox(width: 8),
          Icon(
            isUpgrade 
                ? Icons.arrow_upward
                : isDowngrade 
                    ? Icons.arrow_downward
                    : Icons.arrow_forward,
            color: isUpgrade 
                ? Colors.green
                : isDowngrade 
                    ? Colors.orange
                    : Colors.grey,
          ),
          const SizedBox(width: 8),
          RoleBadge(role: targetRole),
          const Spacer(),
          Text(
            isUpgrade 
                ? 'Promotion'
                : isDowngrade 
                    ? 'Demotion'
                    : 'No Change',
            style: AppTextStyles.bodySmall.copyWith(
              color: isUpgrade 
                  ? Colors.green
                  : isDowngrade 
                      ? Colors.orange
                      : Colors.grey,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  int _getHierarchyLevel(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return 8;
      case UserRole.merchandiser:
        return 7;
      case UserRole.sewingSupervisor:
        return 6;
      case UserRole.qualityController:
        return 5;
      case UserRole.sewingOperator:
        return 4;
      case UserRole.cuttingHelper:
        return 3;
      case UserRole.finishingOperator:
        return 2;
      case UserRole.warehouseManager:
        return 1;
      case UserRole.inventoryManager:
        return 6;
      case UserRole.cuttingHead:
        return 7;
      case UserRole.cuttingMaster:
        return 6;
      case UserRole.sewingHead:
        return 7;
      case UserRole.finishingHead:
        return 7;
      case UserRole.viewer:
        return 1;
      case UserRole.mundaOperator:
        return 9;
        throw UnimplementedError();
    }
  }
}
