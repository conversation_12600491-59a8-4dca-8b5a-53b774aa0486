import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:hm_collection/features/auth/domain/entities/login_credentials.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/validation_utils.dart';
import '../../domain/entities/auth_tokens.dart';
import '../../domain/repositories/auth_repository.dart';
import '../bloc/auth_bloc.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/loading_button.dart';
import 'firebase_signup_page.dart';

/// Login page
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _rememberMe = false;
  bool _obscurePassword = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<AuthBloc>(),
      child: Scaffold(
        body: SafeArea(
          child: BlocListener<AuthBloc, AuthState>(
            listener: _handleAuthStateChange,
            child: BlocBuilder<AuthBloc, AuthState>(
              builder: (context, state) {
                _isLoading = state is AuthLoading;

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 60),
                        _buildHeader(),
                        const SizedBox(height: 60),
                        _buildLoginForm(),
                        const SizedBox(height: 24),
                        _buildRememberMeCheckbox(),
                        const SizedBox(height: 32),
                        _buildLoginButton(context),
                        const SizedBox(height: 16),
                        _buildForgotPasswordButton(),
                        if (state is AuthError) ...[
                          const SizedBox(height: 24),
                          _buildErrorMessage(state.message),
                        ],
                        const SizedBox(height: 40),
                        _buildBiometricLogin(context),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        const Icon(
          Icons.factory,
          size: 80,
          color: AppColors.primary,
        ),
        const SizedBox(height: 16),
        Text(
          AppConstants.appName,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          'Cloth Manufacturing Management',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.textSecondary,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Column(
      children: [
        AuthTextField(
          controller: _usernameController,
          labelText: 'Username or Email',
          hintText: 'Enter your username or email',
          prefixIcon: Icons.person_outline,
          textInputAction: TextInputAction.next,
          validator: (value) =>
              ValidationUtils.validateRequired(value, 'Username'),
          enabled: !_isLoading,
        ),
        const SizedBox(height: 16),
        AuthTextField(
          controller: _passwordController,
          labelText: 'Password',
          hintText: 'Enter your password',
          prefixIcon: Icons.lock_outline,
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.done,
          validator: (value) =>
              ValidationUtils.validateRequired(value, 'Password'),
          enabled: !_isLoading,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility : Icons.visibility_off,
              color: AppColors.textSecondary,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
          onFieldSubmitted: (_) => _handleLogin(context),
        ),
      ],
    );
  }

  Widget _buildRememberMeCheckbox() {
    return Row(
      children: [
        Checkbox(
          value: _rememberMe,
          onChanged: _isLoading
              ? null
              : (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
          activeColor: AppColors.primary,
        ),
        Text(
          'Remember me',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildLoginButton(BuildContext context) {
    return LoadingButton(
      onPressed: () => _handleLogin(context),
      isLoading: _isLoading,
      child: const Text('Login'),
    );
  }

  Widget _buildForgotPasswordButton() {
    return TextButton(
      onPressed: _isLoading
          ? null
          : () {
              // TODO: Navigate to forgot password page
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Forgot password feature coming soon'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
      child: const Text(
        'Forgot Password?',
        style: TextStyle(color: AppColors.primary),
      ),
    );
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.errorLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                color: AppColors.error,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricLogin(BuildContext context) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 24),
        Text(
          'Or continue with',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      // TODO: Implement biometric login
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Biometric login coming soon'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
              icon: const Icon(Icons.fingerprint, size: 40),
              color: AppColors.primary,
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text("Don't have an account?"),
            TextButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const FirebaseSignUpPage(),
                        ),
                      );
                    },
              child: const Text('Sign Up'),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _handleLogin(BuildContext context) async {
    if ((_formKey.currentState?.validate() ?? false) && !_isLoading) {
      setState(() {
        _isLoading = true;
      });

      try {
        final credentials = LoginCredentials(
          email: _usernameController.text.trim(),
          password: _passwordController.text,
          rememberMe: _rememberMe,
        );

        // Dispatch login event
        context.read<AuthBloc>().add(
              AuthLoginRequested(
                credentials: credentials,
                // deviceId: await _getDeviceId(),
                // deviceName: await _getDeviceName(),
              ),
            );
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Login failed: ${e.toString()}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  void _handleBiometricLogin(BuildContext context) {
    context.read<AuthBloc>().add(const AuthBiometricLoginRequested());
  }

  void _handleAuthStateChange(BuildContext context, AuthState state) {
    if (state is AuthAuthenticated) {
      setState(() {
        _isLoading = false;
      });

      // Navigate to main app
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Welcome back, ${state.user.displayName ?? ''}!'),
          backgroundColor: AppColors.success,
        ),
      );

      // TODO: Navigate to dashboard
      // Navigator.of(context).pushReplacementNamed('/dashboard');
    } else if (state is AuthError) {
      setState(() {
        _isLoading = false;
      });

      // Show error message
      if (state.message.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(state.message),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } else if (state is AuthPasswordChangeRequired) {
      setState(() {
        _isLoading = false;
      });

      // TODO: Navigate to password change page
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Password change required'),
          backgroundColor: AppColors.warning,
        ),
      );
    } else if (state is AuthTwoFactorRequired) {
      setState(() {
        _isLoading = false;
      });

      // TODO: Navigate to 2FA page
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Two-factor authentication required'),
          backgroundColor: AppColors.info,
        ),
      );
    } else if (state is AuthLoading) {
      setState(() {
        _isLoading = true;
      });
    }
  }
}
