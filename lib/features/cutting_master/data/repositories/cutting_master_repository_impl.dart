import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/cutting_master_entities.dart';
import '../../domain/repositories/cutting_master_repository.dart';
import '../models/cutting_master_models.dart';

/// Firebase implementation of cutting master repository
class CuttingMasterRepositoryImpl implements CuttingMasterRepository {
  final FirebaseFirestore _firestore;

  // Collection names
  static const String _markersCollection = 'markers';
  static const String _cuttingTasksCollection = 'cutting_tasks';
  static const String _fabricCalculationsCollection = 'fabric_calculations';
  static const String _productionLogsCollection = 'production_logs';

  CuttingMasterRepositoryImpl(this._firestore);

  @override
  Future<Either<Failure, ApiResponse<Marker>>> createMarker({
    required String markerNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required double fabricWidth,
    required double markerLength,
    required int piecesPerMarker,
    required List<MarkerPiece> pieces,
    required String createdBy,
    required String createdByName,
    required double efficiency,
    String? notes,
    Map<String, dynamic> specifications = const {},
  }) async {
    try {
      final now = Timestamp.now();
      final docRef = _firestore.collection(_markersCollection).doc();

      final marker = Marker(
        id: docRef.id,
        markerNumber: markerNumber,
        productId: productId,
        productName: productName,
        fabricType: fabricType,
        fabricWidth: fabricWidth,
        markerLength: markerLength,
        piecesPerMarker: piecesPerMarker,
        pieces: pieces,
        status: MarkerStatus.draft,
        createdBy: createdBy,
        createdByName: createdByName,
        efficiency: efficiency,
        notes: notes,
        specifications: specifications,
        createdAt: now.toDate(),
        updatedAt: now.toDate(),
      );

      final markerModel = MarkerModel.fromEntity(marker);
      await docRef.set(markerModel.toFirestore());

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker created successfully',
        data: marker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to create marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<Marker>>> getMarkers({
    MarkerStatus? status,
    String? productId,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_markersCollection);

      // Apply filters
      if (status != null) {
        query = query.where('status', isEqualTo: status.name);
      }
      if (productId != null) {
        query = query.where('productId', isEqualTo: productId);
      }

      // Apply pagination
      if (lastDocumentId != null) {
        final lastDoc = await _firestore
            .collection(_markersCollection)
            .doc(lastDocumentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      // Order by creation date
      query = query.orderBy('createdAt', descending: true);

      final querySnapshot = await query.get();
      final markers = querySnapshot.docs
          .map((doc) => MarkerModel.fromFirestore(doc).toEntity())
          .toList();

      return Right(ApiListResponse<Marker>(
        success: true,
        message: 'Markers retrieved successfully',
        data: markers,
        pagination: Pagination(
          currentPage: 1,
          totalPages: 1,
          perPage: markers.length,
          hasNextPage: querySnapshot.docs.length == (limit ?? 20),
            hasPreviousPage: querySnapshot.docs.length == (limit ?? 20),
          total: markers.length,
        ),
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get markers: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> getMarkerById(String markerId) async {
    try {
      final doc = await _firestore.collection(_markersCollection).doc(markerId).get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Marker not found'));
      }

      final marker = MarkerModel.fromFirestore(doc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker retrieved successfully',
        data: marker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> updateMarker({
    required String markerId,
    String? markerNumber,
    String? productId,
    String? productName,
    String? fabricType,
    double? fabricWidth,
    double? markerLength,
    int? piecesPerMarker,
    List<MarkerPiece>? pieces,
    MarkerStatus? status,
    double? efficiency,
    String? notes,
    Map<String, dynamic>? specifications,
  }) async {
    try {
      final docRef = _firestore.collection(_markersCollection).doc(markerId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Marker not found'));
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.now(),
      };

      if (markerNumber != null) updateData['markerNumber'] = markerNumber;
      if (productId != null) updateData['productId'] = productId;
      if (productName != null) updateData['productName'] = productName;
      if (fabricType != null) updateData['fabricType'] = fabricType;
      if (fabricWidth != null) updateData['fabricWidth'] = fabricWidth;
      if (markerLength != null) updateData['markerLength'] = markerLength;
      if (piecesPerMarker != null) updateData['piecesPerMarker'] = piecesPerMarker;
      if (pieces != null) {
        updateData['pieces'] = pieces
            .map((piece) => MarkerPieceModel.fromEntity(piece).toMap())
            .toList();
      }
      if (status != null) updateData['status'] = status.name;
      if (efficiency != null) updateData['efficiency'] = efficiency;
      if (notes != null) updateData['notes'] = notes;
      if (specifications != null) updateData['specifications'] = specifications;

      await docRef.update(updateData);

      // Get updated marker
      final updatedDoc = await docRef.get();
      final updatedMarker = MarkerModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker updated successfully',
        data: updatedMarker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> approveMarker({
    required String markerId,
    required String approvedBy,
    required String approvedByName,
  }) async {
    try {
      final docRef = _firestore.collection(_markersCollection).doc(markerId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Marker not found'));
      }

      await docRef.update({
        'status': MarkerStatus.approved.name,
        'approvedBy': approvedBy,
        'approvedByName': approvedByName,
        'approvedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      // Get updated marker
      final updatedDoc = await docRef.get();
      final updatedMarker = MarkerModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker approved successfully',
        data: updatedMarker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to approve marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> rejectMarker({
    required String markerId,
    required String rejectedBy,
    required String rejectedByName,
    required String reason,
  }) async {
    try {
      final docRef = _firestore.collection(_markersCollection).doc(markerId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return const Left(NotFoundFailure('Marker not found'));
      }

      await docRef.update({
        'status': MarkerStatus.rejected.name,
        'rejectedBy': rejectedBy,
        'rejectedByName': rejectedByName,
        'rejectionReason': reason,
        'rejectedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      // Get updated marker
      final updatedDoc = await docRef.get();
      final updatedMarker = MarkerModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker rejected successfully',
        data: updatedMarker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to reject marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteMarker(String markerId) async {
    try {
      await _firestore.collection(_markersCollection).doc(markerId).update({
        'deletedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return const Right(ApiVoidResponse(
        success: true,
        message: 'Marker deleted successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to delete marker: ${e.toString()}'));
    }
  }

  // Fabric calculation operations

  @override
  Future<Either<Failure, ApiResponse<FabricCalculation>>> createFabricCalculation({
    required String calculationNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required double fabricWidth,
    required Map<String, int> sizeQuantities,
    required double totalFabricRequired,
    required double wastagePercentage,
    required double totalFabricWithWastage,
    required String calculatedBy,
    required String calculatedByName,
    String? notes,
  }) async {
    try {
      final now = Timestamp.now();
      final docRef = _firestore.collection(_fabricCalculationsCollection).doc();

      final fabricCalculation = FabricCalculation(
        id: docRef.id,
        calculationNumber: calculationNumber,
        productId: productId,
        productName: productName,
        fabricType: fabricType,
        fabricWidth: fabricWidth,
        sizeQuantities: sizeQuantities,
        totalFabricRequired: totalFabricRequired,
        wastagePercentage: wastagePercentage,
        totalFabricWithWastage: totalFabricWithWastage,
        calculatedBy: calculatedBy,
        calculatedByName: calculatedByName,
        status: FabricCalculationStatus.draft,
        notes: notes,
        createdAt: now.toDate(),
        updatedAt: now.toDate(),
      );

      final calculationModel = FabricCalculationModel.fromEntity(fabricCalculation);
      await docRef.set(calculationModel.toFirestore());

      return Right(ApiResponse<FabricCalculation>(
        success: true,
        message: 'Fabric calculation created successfully',
        data: fabricCalculation,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to create fabric calculation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<FabricCalculation>>> getFabricCalculations({
    FabricCalculationStatus? status,
    String? productId,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_fabricCalculationsCollection);

      // Apply filters
      if (status != null) {
        query = query.where('status', isEqualTo: status.name);
      }
      if (productId != null) {
        query = query.where('productId', isEqualTo: productId);
      }

      // Apply pagination
      if (lastDocumentId != null) {
        final lastDoc = await _firestore
            .collection(_fabricCalculationsCollection)
            .doc(lastDocumentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      // Order by creation date
      query = query.orderBy('createdAt', descending: true);

      final querySnapshot = await query.get();
      final calculations = querySnapshot.docs
          .map((doc) => FabricCalculationModel.fromFirestore(doc).toEntity())
          .toList();

      return Right(ApiListResponse<FabricCalculation>(
        success: true,
        message: 'Fabric calculations retrieved successfully',
        data: calculations,
        pagination: Pagination(
          currentPage: 1,
          totalPages: 1,
          perPage: calculations.length,
          hasNextPage: querySnapshot.docs.length == (limit ?? 20),
          hasPreviousPage: querySnapshot.docs.length == (limit ?? 20),
          total: calculations.length,
        ),
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get fabric calculations: ${e.toString()}'));
    }
  }

  // Production log operations

  @override
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> createProductionLogEntry({
    required String logNumber,
    required String taskId,
    required String taskType,
    required String operatorId,
    required String operatorName,
    required DateTime startTime,
    DateTime? endTime,
    required int quantityProduced,
    double? qualityScore,
    String? notes,
    Map<String, dynamic> metrics = const {},
  }) async {
    try {
      final now = Timestamp.now();
      final docRef = _firestore.collection(_productionLogsCollection).doc();

      final logEntry = ProductionLogEntry(
        id: docRef.id,
        logNumber: logNumber,
        taskId: taskId,
        taskType: taskType,
        operatorId: operatorId,
        operatorName: operatorName,
        startTime: startTime,
        endTime: endTime,
        quantityProduced: quantityProduced,
        qualityScore: qualityScore,
        notes: notes,
        metrics: metrics,
        createdAt: now.toDate(),
        updatedAt: now.toDate(),
      );

      final logModel = ProductionLogEntryModel.fromEntity(logEntry);
      await docRef.set(logModel.toFirestore());

      return Right(ApiResponse<ProductionLogEntry>(
        success: true,
        message: 'Production log entry created successfully',
        data: logEntry,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to create production log entry: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<ProductionLogEntry>>> getProductionLogEntries({
    String? taskId,
    String? operatorId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_productionLogsCollection);

      // Apply filters
      if (taskId != null) {
        query = query.where('taskId', isEqualTo: taskId);
      }
      if (operatorId != null) {
        query = query.where('operatorId', isEqualTo: operatorId);
      }
      if (startDate != null) {
        query = query.where('startTime', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        query = query.where('startTime', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      // Apply pagination
      if (lastDocumentId != null) {
        final lastDoc = await _firestore
            .collection(_productionLogsCollection)
            .doc(lastDocumentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      // Order by creation date
      query = query.orderBy('createdAt', descending: true);

      final querySnapshot = await query.get();
      final logEntries = querySnapshot.docs
          .map((doc) => ProductionLogEntryModel.fromFirestore(doc).toEntity())
          .toList();

      return Right(ApiListResponse<ProductionLogEntry>(
        success: true,
        message: 'Production log entries retrieved successfully',
        data: logEntries,
        pagination: Pagination(
          currentPage: 1,
          totalPages: 1,
          perPage: logEntries.length,
          hasNextPage: querySnapshot.docs.length == (limit ?? 20),
          hasPreviousPage: querySnapshot.docs.length == (limit ?? 20),
          total: logEntries.length,
        ),
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get production log entries: ${e.toString()}'));
    }
  }

  // Cutting task operations

  @override
  Future<Either<Failure, ApiResponse<CuttingTask>>> createCuttingTask({
    required String taskNumber,
    required String markerId,
    required String markerNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required int plannedQuantity,
    required String assignedTo,
    required String assignedToName,
    required String assignedBy,
    required String assignedByName,
    required DateTime plannedStartDate,
    required DateTime plannedEndDate,
    String? notes,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final now = Timestamp.now();
      final docRef = _firestore.collection(_cuttingTasksCollection).doc();

      final task = CuttingTask(
        id: docRef.id,
        taskNumber: taskNumber,
        markerId: markerId,
        markerNumber: markerNumber,
        productId: productId,
        productName: productName,
        fabricType: fabricType,
        plannedQuantity: plannedQuantity,
        actualQuantity: 0,
        status: CuttingTaskStatus.pending,
        assignedTo: assignedTo,
        assignedToName: assignedToName,
        assignedBy: assignedBy,
        assignedByName: assignedByName,
        plannedStartDate: plannedStartDate,
        plannedEndDate: plannedEndDate,
        actualStartDate: null,
        actualEndDate: null,
        cutPieces: const [],
        qualityChecks: const [],
        notes: notes,
        metadata: metadata,
        createdAt: now.toDate(),
        updatedAt: now.toDate(),
        deletedAt: null,
      );

      final model = CuttingTaskModel.fromEntity(task);
      await docRef.set(model.toFirestore());

      return Right(ApiResponse<CuttingTask>(
        success: true,
        message: 'Cutting task created successfully',
        data: task,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to create cutting task: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<CuttingTask>>> getCuttingTasks({
    CuttingTaskStatus? status,
    String? assignedTo,
    String? productId,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_cuttingTasksCollection);

      if (status != null) {
        query = query.where('status', isEqualTo: status.name);
      }
      if (assignedTo != null) {
        query = query.where('assignedTo', isEqualTo: assignedTo);
      }
      if (productId != null) {
        query = query.where('productId', isEqualTo: productId);
      }

      if (lastDocumentId != null) {
        final lastDoc = await _firestore.collection(_cuttingTasksCollection).doc(lastDocumentId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      query = query.orderBy('createdAt', descending: true);

      final qs = await query.get();
      final tasks = qs.docs.map((d) => CuttingTaskModel.fromFirestore(d).toEntity()).toList();

      return Right(ApiListResponse<CuttingTask>(
        success: true,
        message: 'Cutting tasks retrieved successfully',
        data: tasks,
        pagination: Pagination(
          currentPage: 1,
          totalPages: 1,
          perPage: tasks.length,
          hasNextPage: qs.docs.length == (limit ?? 20),
          hasPreviousPage: qs.docs.length == (limit ?? 20),
          total: tasks.length,
        ),
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get cutting tasks: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CuttingTask>>> getCuttingTaskById(String taskId) async {
    try {
      final doc = await _firestore.collection(_cuttingTasksCollection).doc(taskId).get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Cutting task not found'));
      }
      final task = CuttingTaskModel.fromFirestore(doc).toEntity();
      return Right(ApiResponse<CuttingTask>(
        success: true,
        message: 'Cutting task retrieved successfully',
        data: task,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get cutting task: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CuttingTask>>> updateCuttingTask({
    required String taskId,
    String? taskNumber,
    int? actualQuantity,
    CuttingTaskStatus? status,
    DateTime? actualStartDate,
    DateTime? actualEndDate,
    List<CuttingPiece>? cutPieces,
    List<QualityCheck>? qualityChecks,
    String? notes,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final docRef = _firestore.collection(_cuttingTasksCollection).doc(taskId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Cutting task not found'));
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.now(),
      };

      if (taskNumber != null) updateData['taskNumber'] = taskNumber;
      if (actualQuantity != null) updateData['actualQuantity'] = actualQuantity;
      if (status != null) updateData['status'] = status.name;
      if (actualStartDate != null) updateData['actualStartDate'] = Timestamp.fromDate(actualStartDate);
      if (actualEndDate != null) updateData['actualEndDate'] = Timestamp.fromDate(actualEndDate);
      if (cutPieces != null) {
        updateData['cutPieces'] = cutPieces
            .map((p) => CuttingPieceModel.fromEntity(p).toMap())
            .toList();
      }
      if (qualityChecks != null) {
        updateData['qualityChecks'] = qualityChecks
            .map((q) => QualityCheckModel.fromEntity(q).toMap())
            .toList();
      }
      if (notes != null) updateData['notes'] = notes;
      if (metadata != null) updateData['metadata'] = metadata;

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      final updatedTask = CuttingTaskModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<CuttingTask>(
        success: true,
        message: 'Cutting task updated successfully',
        data: updatedTask,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update cutting task: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CuttingTask>>> startCuttingTask(String taskId) async {
    try {
      final docRef = _firestore.collection(_cuttingTasksCollection).doc(taskId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Cutting task not found'));
      }

      await docRef.update({
        'status': CuttingTaskStatus.inProgress.name,
        'actualStartDate': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      final updatedDoc = await docRef.get();
      final task = CuttingTaskModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<CuttingTask>(
        success: true,
        message: 'Cutting task started successfully',
        data: task,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to start cutting task: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CuttingTask>>> completeCuttingTask({
    required String taskId,
    required int actualQuantity,
    required List<CuttingPiece> cutPieces,
    List<QualityCheck>? qualityChecks,
    String? notes,
  }) async {
    try {
      final docRef = _firestore.collection(_cuttingTasksCollection).doc(taskId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Cutting task not found'));
      }

      final updateData = <String, dynamic>{
        'status': CuttingTaskStatus.completed.name,
        'actualEndDate': Timestamp.now(),
        'actualQuantity': actualQuantity,
        'cutPieces': cutPieces.map((p) => CuttingPieceModel.fromEntity(p).toMap()).toList(),
        'updatedAt': Timestamp.now(),
      };

      if (qualityChecks != null) {
        updateData['qualityChecks'] = qualityChecks
            .map((q) => QualityCheckModel.fromEntity(q).toMap())
            .toList();
      }
      if (notes != null) updateData['notes'] = notes;

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      final task = CuttingTaskModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<CuttingTask>(
        success: true,
        message: 'Cutting task completed successfully',
        data: task,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to complete cutting task: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteCuttingTask(String taskId) async {
    try {
      await _firestore.collection(_cuttingTasksCollection).doc(taskId).update({
        'deletedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });
      return const Right(ApiVoidResponse(
        success: true,
        message: 'Cutting task deleted successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to delete cutting task: ${e.toString()}'));
    }
  }

  // Quality check operations

  @override
  Future<Either<Failure, ApiResponse<CuttingTask>>> addQualityCheck({
    required String taskId,
    required String checkType,
    required double score,
    required String checkedBy,
    required String checkedByName,
    String? notes,
    List<String> defects = const [],
  }) async {
    try {
      final docRef = _firestore.collection(_cuttingTasksCollection).doc(taskId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Cutting task not found'));
      }

      final newCheckId = _firestore.collection(_cuttingTasksCollection).doc().id;
      final qc = QualityCheck(
        checkId: newCheckId,
        checkType: checkType,
        status: QualityCheckStatus.pending,
        score: score,
        checkedBy: checkedBy,
        checkedByName: checkedByName,
        checkedAt: Timestamp.now().toDate(),
        notes: notes,
        defects: defects,
      );

      final existing = List<Map<String, dynamic>>.from(
        (doc.data() as Map<String, dynamic>)['qualityChecks'] ?? [],
      );
      existing.add(QualityCheckModel.fromEntity(qc).toMap());

      await docRef.update({
        'qualityChecks': existing,
        'updatedAt': Timestamp.now(),
      });

      final updatedDoc = await docRef.get();
      final updatedTask = CuttingTaskModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<CuttingTask>(
        success: true,
        message: 'Quality check added successfully',
        data: updatedTask,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to add quality check: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<CuttingTask>>> updateQualityCheck({
    required String taskId,
    required String checkId,
    String? checkType,
    double? score,
    QualityCheckStatus? status,
    String? notes,
    List<String>? defects,
  }) async {
    try {
      final docRef = _firestore.collection(_cuttingTasksCollection).doc(taskId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Cutting task not found'));
      }

      final data = (doc.data() as Map<String, dynamic>);
      final checks = List<Map<String, dynamic>>.from(data['qualityChecks'] ?? []);
      final idx = checks.indexWhere((c) => c['checkId'] == checkId);
      if (idx == -1) {
        return const Left(NotFoundFailure('Quality check not found'));
      }

      final updated = Map<String, dynamic>.from(checks[idx]);
      if (checkType != null) updated['checkType'] = checkType;
      if (score != null) updated['score'] = score;
      if (status != null) updated['status'] = status.name;
      if (notes != null) updated['notes'] = notes;
      if (defects != null) updated['defects'] = defects;

      checks[idx] = updated;

      await docRef.update({
        'qualityChecks': checks,
        'updatedAt': Timestamp.now(),
      });

      final updatedDoc = await docRef.get();
      final updatedTask = CuttingTaskModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<CuttingTask>(
        success: true,
        message: 'Quality check updated successfully',
        data: updatedTask,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update quality check: ${e.toString()}'));
    }
  }

  // Fabric calculation operations (additional)

  @override
  Future<Either<Failure, ApiResponse<FabricCalculation>>> getFabricCalculationById(String calculationId) async {
    try {
      final doc = await _firestore.collection(_fabricCalculationsCollection).doc(calculationId).get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Fabric calculation not found'));
      }
      final calc = FabricCalculationModel.fromFirestore(doc).toEntity();
      return Right(ApiResponse<FabricCalculation>(
        success: true,
        message: 'Fabric calculation retrieved successfully',
        data: calc,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get fabric calculation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<FabricCalculation>>> updateFabricCalculation({
    required String calculationId,
    String? calculationNumber,
    String? productId,
    String? productName,
    String? fabricType,
    double? fabricWidth,
    Map<String, int>? sizeQuantities,
    double? totalFabricRequired,
    double? wastagePercentage,
    double? totalFabricWithWastage,
    FabricCalculationStatus? status,
    String? notes,
  }) async {
    try {
      final docRef = _firestore.collection(_fabricCalculationsCollection).doc(calculationId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Fabric calculation not found'));
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.now(),
      };

      if (calculationNumber != null) updateData['calculationNumber'] = calculationNumber;
      if (productId != null) updateData['productId'] = productId;
      if (productName != null) updateData['productName'] = productName;
      if (fabricType != null) updateData['fabricType'] = fabricType;
      if (fabricWidth != null) updateData['fabricWidth'] = fabricWidth;
      if (sizeQuantities != null) updateData['sizeQuantities'] = sizeQuantities;
      if (totalFabricRequired != null) updateData['totalFabricRequired'] = totalFabricRequired;
      if (wastagePercentage != null) updateData['wastagePercentage'] = wastagePercentage;
      if (totalFabricWithWastage != null) updateData['totalFabricWithWastage'] = totalFabricWithWastage;
      if (status != null) updateData['status'] = status.name;
      if (notes != null) updateData['notes'] = notes;

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      final updatedCalc = FabricCalculationModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<FabricCalculation>(
        success: true,
        message: 'Fabric calculation updated successfully',
        data: updatedCalc,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update fabric calculation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<FabricCalculation>>> approveFabricCalculation({
    required String calculationId,
    required String approvedBy,
    required String approvedByName,
  }) async {
    try {
      final docRef = _firestore.collection(_fabricCalculationsCollection).doc(calculationId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Fabric calculation not found'));
      }

      await docRef.update({
        'status': FabricCalculationStatus.approved.name,
        'approvedBy': approvedBy,
        'approvedByName': approvedByName,
        'approvedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      final updatedDoc = await docRef.get();
      final updatedCalc = FabricCalculationModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<FabricCalculation>(
        success: true,
        message: 'Fabric calculation approved successfully',
        data: updatedCalc,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to approve fabric calculation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteFabricCalculation(String calculationId) async {
    try {
      await _firestore.collection(_fabricCalculationsCollection).doc(calculationId).update({
        'deletedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });
      return const Right(ApiVoidResponse(
        success: true,
        message: 'Fabric calculation deleted successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to delete fabric calculation: ${e.toString()}'));
    }
  }

  // Production log operations (completions and updates)

  @override
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> getProductionLogEntryById(String logId) async {
    try {
      final doc = await _firestore.collection(_productionLogsCollection).doc(logId).get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Production log entry not found'));
      }
      final entry = ProductionLogEntryModel.fromFirestore(doc).toEntity();
      return Right(ApiResponse<ProductionLogEntry>(
        success: true,
        message: 'Production log entry retrieved successfully',
        data: entry,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get production log entry: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> updateProductionLogEntry({
    required String logId,
    DateTime? endTime,
    int? quantityProduced,
    double? qualityScore,
    String? notes,
    Map<String, dynamic>? metrics,
  }) async {
    try {
      final docRef = _firestore.collection(_productionLogsCollection).doc(logId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Production log entry not found'));
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.now(),
      };

      if (endTime != null) updateData['endTime'] = Timestamp.fromDate(endTime);
      if (quantityProduced != null) updateData['quantityProduced'] = quantityProduced;
      if (qualityScore != null) updateData['qualityScore'] = qualityScore;
      if (notes != null) updateData['notes'] = notes;
      if (metrics != null) updateData['metrics'] = metrics;

      await docRef.update(updateData);

      final updatedDoc = await docRef.get();
      final updatedEntry = ProductionLogEntryModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<ProductionLogEntry>(
        success: true,
        message: 'Production log entry updated successfully',
        data: updatedEntry,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update production log entry: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> completeProductionLogEntry({
    required String logId,
    required DateTime endTime,
    required int quantityProduced,
    double? qualityScore,
    String? notes,
  }) async {
    try {
      final docRef = _firestore.collection(_productionLogsCollection).doc(logId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(NotFoundFailure('Production log entry not found'));
      }

      await docRef.update({
        'endTime': Timestamp.fromDate(endTime),
        'quantityProduced': quantityProduced,
        'qualityScore': qualityScore,
        'notes': notes,
        'updatedAt': Timestamp.now(),
      });

      final updatedDoc = await docRef.get();
      final updatedEntry = ProductionLogEntryModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<ProductionLogEntry>(
        success: true,
        message: 'Production log entry completed successfully',
        data: updatedEntry,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to complete production log entry: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteProductionLogEntry(String logId) async {
    try {
      await _firestore.collection(_productionLogsCollection).doc(logId).update({
        'deletedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });
      return const Right(ApiVoidResponse(
        success: true,
        message: 'Production log entry deleted successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to delete production log entry: ${e.toString()}'));
    }
  }

  // Analytics methods

  @override
  Future<Either<Failure, Map<String, dynamic>>> getCuttingTaskAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String? productId,
  }) async {
    try {
      Query baseQuery = _firestore.collection(_cuttingTasksCollection);

      if (productId != null) {
        baseQuery = baseQuery.where('productId', isEqualTo: productId);
      }
      if (startDate != null) {
        baseQuery = baseQuery.where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        baseQuery = baseQuery.where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final totalTasksQuery = baseQuery.count().get();
      final completedTasksQuery = baseQuery.where('status', isEqualTo: CuttingTaskStatus.completed.name).count().get();
      final inProgressTasksQuery = baseQuery.where('status', isEqualTo: CuttingTaskStatus.inProgress.name).count().get();
      final pendingTasksQuery = baseQuery.where('status', isEqualTo: CuttingTaskStatus.pending.name).count().get();

      final results = await Future.wait([
        totalTasksQuery,
        completedTasksQuery,
        inProgressTasksQuery,
        pendingTasksQuery,
      ]);

      return Right({
        'totalTasks': results[0].count,
        'completedTasks': results[1].count,
        'inProgressTasks': results[2].count,
        'pendingTasks': results[3].count,
      });
    } catch (e) {
      return Left(ServerFailure('Failed to get cutting task analytics: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getMarkerEfficiencyReport({
    DateTime? startDate,
    DateTime? endDate,
    String? productId,
  }) async {
    try {
      Query query = _firestore.collection(_markersCollection);

      if (productId != null) {
        query = query.where('productId', isEqualTo: productId);
      }
      if (startDate != null) {
        query = query.where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        query = query.where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final querySnapshot = await query.get();

      if (querySnapshot.docs.isEmpty) {
        return const Right({
          'averageEfficiency': 0.0,
          'highestEfficiency': 0.0,
          'lowestEfficiency': 0.0,
        });
      }

      double totalEfficiency = 0;
      double highestEfficiency = 0;
      double lowestEfficiency = double.maxFinite;

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final efficiency = (data['efficiency'] as num?)?.toDouble() ?? 0.0;
        totalEfficiency += efficiency;
        if (efficiency > highestEfficiency) {
          highestEfficiency = efficiency;
        }
        if (efficiency < lowestEfficiency) {
          lowestEfficiency = efficiency;
        }
      }

      return Right({
        'averageEfficiency': totalEfficiency / querySnapshot.docs.length,
        'highestEfficiency': highestEfficiency,
        'lowestEfficiency': lowestEfficiency,
      });
    } catch (e) {
      return Left(ServerFailure('Failed to get marker efficiency report: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getProductionSummary({
    DateTime? startDate,
    DateTime? endDate,
    String? operatorId,
  }) async {
    try {
      Query query = _firestore.collection(_productionLogsCollection);

      if (operatorId != null) {
        query = query.where('operatorId', isEqualTo: operatorId);
      }
      if (startDate != null) {
        query = query.where('startTime', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        query = query.where('startTime', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final querySnapshot = await query.get();

      if (querySnapshot.docs.isEmpty) {
        return const Right({
          'totalPiecesCut': 0,
          'totalReworked': 0, // Placeholder, as rework logic is not defined
          'overallQualityScore': 0.0,
        });
      }

      int totalPiecesCut = 0;
      double totalQualityScore = 0;
      int qualityScoreCount = 0;

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        totalPiecesCut += (data['quantityProduced'] as int?) ?? 0;
        final qualityScore = (data['qualityScore'] as num?)?.toDouble();
        if (qualityScore != null) {
          totalQualityScore += qualityScore;
          qualityScoreCount++;
        }
      }

      return Right({
        'totalPiecesCut': totalPiecesCut,
        'totalReworked': 0, // Placeholder, as rework logic is not defined
        'overallQualityScore': qualityScoreCount > 0 ? totalQualityScore / qualityScoreCount : 0.0,
      });
    } catch (e) {
      return Left(ServerFailure('Failed to get production summary: ${e.toString()}'));
    }
  }
}
